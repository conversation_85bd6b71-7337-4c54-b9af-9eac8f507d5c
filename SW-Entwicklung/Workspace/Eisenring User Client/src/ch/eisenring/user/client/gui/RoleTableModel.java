package ch.eisenring.user.client.gui;

import ch.eisenring.core.collections.List;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.gui.components.HEAGListTableModel;
import ch.eisenring.gui.components.HEAGTableLayout;
import ch.eisenring.gui.components.SortKey;
import ch.eisenring.model.TransactionContext;
import ch.eisenring.user.shared.model.RoleModel;
import ch.eisenring.user.shared.service.RoleFacade;

@SuppressWarnings("serial")
public final class RoleTableModel extends HEAGListTableModel<RoleModel> {

	private final static HEAGTableLayout LAYOUT; static {
		final HEAGTableLayout l = new HEAGTableLayout("RoleTableLayout", 2, new SortKey(0));
		l.addColumn("Rolle", 200, 32, 2048, RoleFacade.Order.DisplayName);
		l.addColumn("Mitglieder", 64, 32, 128);
		LAYOUT = l;
	}

	public RoleTableModel(final TransactionContext context) {
		super(LAYOUT);
		updateRoleList(context);
	}
	
	@Override
	public Object getColumnValue(final RoleModel role, final int columnIndex) {
		if (role == null)
			return "???";
		switch (columnIndex) {
			default: return "???";
			case 0: return role.getDisplayName();
			case 1: return Strings.toString(role.getUsers().size());
		}
	}

	@SuppressWarnings("unchecked")
	void updateRoleList(final TransactionContext context) {
		final List<RoleModel> list = context.getModels(RoleModel.class);
		setContent((List) list);
	}

}

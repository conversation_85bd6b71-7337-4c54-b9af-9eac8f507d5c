package ch.eisenring.user.client.network;

import ch.eisenring.core.logging.Logger;
import ch.eisenring.core.logging.Logger.LogLevel;
import ch.eisenring.network.PacketSink;
import ch.eisenring.network.implementation.PacketDispatchMode;
import ch.eisenring.network.packet.AbstractPacket;
import ch.eisenring.user.client.USRClient;
import ch.eisenring.user.shared.model.UserContext;
import ch.eisenring.user.shared.network.PacketRequest;
import ch.eisenring.user.shared.network.PacketUpdate;

final class UpdateHandler extends AbstractPacketHandler {

	UpdateHandler(final USRClient client) {
		super(client, PacketUpdate.class, PacketDispatchMode.SYNCHRONOUS);
	}
	
	@Override
	public void handle(final AbstractPacket abstractPacket, final PacketSink sink) {
		final PacketUpdate packet = (PacketUpdate) abstractPacket;
		final UserContext context = client.USER_CONTEXT.get();
		if (context == null) {
			Logger.log(LogLevel.WARN, "no user context available, ignored update");
		} else {
			try {
				packet.updateContext(context);
				context.setUnchanged(true);
				client.USER_CONTEXT.fireValueChange();
			} catch (final Exception e) {
				// throw away damaged context and request a new copy
				client.USER_CONTEXT.set((UserContext) null);
				sink.sendPacket(PacketRequest.create(), false);
				Logger.error(e);
			}
			client.updatePermissionProvider();
		}
	}

}

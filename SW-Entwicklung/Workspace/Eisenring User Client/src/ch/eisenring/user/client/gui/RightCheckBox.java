package ch.eisenring.user.client.gui;

import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.awt.event.MouseListener;

import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.gui.components.HEAGCheckBox;
import ch.eisenring.gui.menu.MenuBuilder;
import ch.eisenring.user.client.USRClient;
import ch.eisenring.user.client.action.ActionUserWhoHasRight;
import ch.eisenring.user.shared.Permission;
import ch.eisenring.user.shared.model.RightModel;

@SuppressWarnings("serial")
public final class RightCheckBox extends HEAGCheckBox {

	private final static MouseListener mouseListener = new MouseAdapter() {
		@Override
		public void mousePressed(final MouseEvent event) {
			showPopup(event);
		}
		
		@Override
		public void mouseReleased(final MouseEvent event) {
			showPopup(event);
		}
		
		private void showPopup(final MouseEvent event) {
			if (event.isConsumed() || !event.isPopupTrigger())
				return;
			final Object source = event.getSource();
			if (source instanceof RightCheckBox) {
				event.consume();
				final RightCheckBox checkBox = (RightCheckBox) source;
				checkBox.showPopup(event);
			}
		}
	};

	private final USRClient client;
	private final RightModel right;
	
	public RightCheckBox(final USRClient client, final RightModel right) {
		super(right.getDisplayname());
		this.client = client;
		this.right = right;
		addMouseListener(mouseListener);
		String tooltip = right.getDescription();
		if (!Strings.isEmpty(tooltip)) {
			tooltip = tooltip.replace("***", "<br>");
			tooltip = tooltip.replace("\n", "<br>");
			if (!Strings.startsWithIgnoreCase(tooltip, "<html>")) {
				tooltip = Strings.concat("<html>", tooltip);
			}
			setToolTipText(tooltip);
		}
	}
	
	public Permission getPermission() {
		return right;
	}

	protected void showPopup(final MouseEvent event) {
		final MenuBuilder b = new MenuBuilder();
		b.add(new ActionUserWhoHasRight(client, right));
		b.showPopupMenu(event);
	}

}

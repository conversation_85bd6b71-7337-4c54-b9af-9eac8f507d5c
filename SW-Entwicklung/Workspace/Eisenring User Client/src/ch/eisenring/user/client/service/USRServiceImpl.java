package ch.eisenring.user.client.service;

import ch.eisenring.app.shared.Service;
import ch.eisenring.app.shared.ServiceNotFoundException;
import ch.eisenring.core.observable.Observable;
import ch.eisenring.user.client.USRClient;
import ch.eisenring.user.shared.model.UserContext;
import ch.eisenring.user.shared.service.UserFacade;
import ch.eisenring.user.shared.service.impl.USRServiceBase;

public final class USRServiceImpl extends USRServiceBase {

	private final USRClient usrClient;

	@Override
	protected <S extends Service> S locateService(final Class<S> serviceClass) throws ServiceNotFoundException {
		return usrClient.locateService(serviceClass);
	}

	@Override
	public UserContext getUserContext() {
		return usrClient.getContext();
	}

	public USRServiceImpl(final USRClient client) {
		this.usrClient = client;
	}

	@Override
	public Observable<?> getPermissionObservable() {
		return usrClient.PERMISSIONS_CHANGED;
	}

	@Override
	public UserFacade getLoggedInUser() {
		return usrClient.LOGGED_IN_USER.get();
	}

}

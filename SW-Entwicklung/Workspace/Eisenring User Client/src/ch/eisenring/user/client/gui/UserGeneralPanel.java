package ch.eisenring.user.client.gui;

import static ch.eisenring.user.shared.metamodel.USRUserMeta.ATR_FIRSTNAME;
import static ch.eisenring.user.shared.metamodel.USRUserMeta.ATR_LASTNAME;
import static ch.eisenring.user.shared.metamodel.USRUserMeta.ATR_LOGINNAME;
import static ch.eisenring.user.shared.metamodel.USRUserMeta.ATR_STATUSCODE;

import java.awt.GridBagLayout;
import java.util.Arrays;
import java.util.Iterator;

import ch.eisenring.core.collections.List;
import ch.eisenring.core.crypt.CryptUtil;
import ch.eisenring.core.crypt.password.PasswordMethodCode;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.gui.GridBagConstraints;
import ch.eisenring.gui.ManualBinding;
import ch.eisenring.gui.ReflectiveBinding;
import ch.eisenring.gui.components.DecorationHeader;
import ch.eisenring.gui.components.HEAGCodeComboBox;
import ch.eisenring.gui.components.HEAGLabel;
import ch.eisenring.gui.components.HEAGPanel;
import ch.eisenring.gui.components.HEAGPasswordField;
import ch.eisenring.gui.components.HEAGTextArea;
import ch.eisenring.gui.components.HEAGTextField;
import ch.eisenring.gui.components.Separator;
import ch.eisenring.gui.model.ValidationResults;
import ch.eisenring.model.gui.ModelBinding;
import ch.eisenring.user.client.USRRightCode;
import ch.eisenring.user.client.resource.images.Images;
import ch.eisenring.user.shared.codetables.USRStatusCode;
import ch.eisenring.user.shared.model.UserModel;

@SuppressWarnings("serial")
public final class UserGeneralPanel extends HEAGPanel {

	private final static String PW_NOT_CHANGED_STRING  = "\tNot\tChanged";
	private final static char[] PW_NOT_CHANGED_CHARS = PW_NOT_CHANGED_STRING.toCharArray();
	
	private final HEAGTextField txtLoginname = new HEAGTextField(new ModelBinding(ATR_LOGINNAME));
	private final HEAGTextField txtFirstname = new HEAGTextField(new ModelBinding(ATR_FIRSTNAME));
	private final HEAGTextField txtLastname = new HEAGTextField(new ModelBinding(ATR_LASTNAME));
	private final HEAGTextField txtEMailAddress = new HEAGTextField(new ManualBinding(120) {
		@Override
		protected Object getModelValueImpl(Object model) {
			return ((UserModel) model).getEMailAddress();
		}
		@Override
		protected void setModelValueImpl(Object model, Object value) {
			((UserModel) model).setEMailAddress((String) value);		}
	});
	private final HEAGPasswordField txtPassword = new HEAGPasswordField(); //new ModelBinding(ATR_PASSWORDHASH));
	private final HEAGTextArea txtBemerkungen = new HEAGTextArea(new ManualBinding(250) {
		@Override
		protected Object getModelValueImpl(Object model) {
			return ((UserModel) model).getAdminComment();
		}
		@Override
		protected void setModelValueImpl(Object model, Object value) {
			((UserModel) model).setAdminComment((String) value);
		}
	});
	private final HEAGCodeComboBox<USRStatusCode> cmbStatus = new HEAGCodeComboBox<>(USRStatusCode.class, new ModelBinding(ATR_STATUSCODE));
	private final HEAGTextField txtPhoneMobileBusiness = new HEAGTextField(new ReflectiveBinding("PhoneMobileBusiness", 30));
	private final HEAGTextField txtPhoneFixnetBusiness = new HEAGTextField(new ReflectiveBinding("PhoneFixnetBusiness", 30));
	private final HEAGTextField txtPhoneFaxBusiness = new HEAGTextField(new ReflectiveBinding("PhoneFaxBusiness", 30));
	private final HEAGTextField txtPhoneReceptionBusiness = new HEAGTextField(new ReflectiveBinding("PhoneReceptionBusiness", 30));

	public UserGeneralPanel() {
		initComponents();
		initLayout();
	}

	private void initComponents() {
		setLayout(new GridBagLayout());
		txtLoginname.setToolTipText("Der Benutzername mit dem sich dieser Benutzer an seinem Arbeitsplatz-PC anmeldet");
		txtFirstname.setToolTipText("Vorname der Person. Wird zur Anzeige und für eMail verwendet");
		txtLastname.setToolTipText("Nachname der Person. Wird zur Anzeige und für eMail verwendet");
		txtPassword.setToolTipText("Hier neues Passwort eingeben um das Passwort zu ändern.\nDas Passwort wird nur zur Anmeldung über den Mobile-Client bzw. Montagerapport benötigt");
		txtBemerkungen.setToolTipText("Bemerkungsfeld für interne Notizen zum Benutzer.");
		txtEMailAddress.setToolTipText("eMail-Adresse, die verwendet wird um dem Benutzer automatisch generierte Nachrichten zuzustellen,\nwenn vom Benutzer eMail verschickt wird ist dies auch die Absender-Adresse");
		txtPhoneMobileBusiness.setToolTipText("Geschäftliche Natelnummer des Benutzers");
		txtPhoneFixnetBusiness.setToolTipText("Geschäftliche Festnetznummer des Benutzers");
		txtPhoneFaxBusiness.setToolTipText("Geschäftliche Faxnummer des Benutzers");
		txtPhoneReceptionBusiness.setToolTipText("Nummer der Geschäftsstelle (Empfang) des Benutzers");
		cmbStatus.setToolTipText("Status des Benutzerkontos");
		cmbStatus.setDisplayIcons(true);
	}

	private void initLayout() {
		removeAll();
		
		int y = 0;
		add(new DecorationHeader(DecorationHeader.ICON, Images.USER_FEMALE,
								 DecorationHeader.LABEL, "Angaben zur Person"),
				GridBagConstraints.field(0, y).gridWidthRemainder());
		
		++y;
		add(new HEAGLabel("Login-Name"), GridBagConstraints.label(0, y));
		add(txtLoginname, GridBagConstraints.field(1, y));
		add(new HEAGLabel(" Status"), GridBagConstraints.label(2, 1));
		add(cmbStatus, GridBagConstraints.field(3, y).gridWidthRemainder());
		++y;
		add(new HEAGLabel("Vor-/Nachname"), GridBagConstraints.label(0, y));
		add(txtFirstname, GridBagConstraints.field(1, y).gridWidth(2));
		add(txtLastname, GridBagConstraints.field(3, y).gridWidthRemainder());

		++y;
		add(new HEAGLabel("eMail"), GridBagConstraints.label(0, y));
		add(txtEMailAddress, GridBagConstraints.field(1, y).gridWidthRemainder());

		++y;
		add(new HEAGLabel("Passwort setzen"), GridBagConstraints.label(0, y));
		add(txtPassword, GridBagConstraints.field(1, y).gridWidthRemainder());

		++y;
		add(Separator.create("Telefon Geschäftlich"), GridBagConstraints.separator(0, y));
		
		++y;
		add(new HEAGLabel("Mobil"), GridBagConstraints.label(1, y));
		add(txtPhoneMobileBusiness, GridBagConstraints.field(2, y));
		add(new HEAGLabel("Festnetz"), GridBagConstraints.label(3, y));
		add(txtPhoneFixnetBusiness, GridBagConstraints.field(4, y).gridWidthRemainder());
		
		++y;
		add(new HEAGLabel("Fax"), GridBagConstraints.label(1, y));
		add(txtPhoneFaxBusiness, GridBagConstraints.field(2, y));
		add(new HEAGLabel("Empfang"), GridBagConstraints.label(3, y));
		add(txtPhoneReceptionBusiness, GridBagConstraints.field(4, y).gridWidthRemainder());

		++y;
		add(Separator.create(), GridBagConstraints.separator(0, y));

		++y;
		add(new HEAGLabel("Bemerkungen"), GridBagConstraints.label(0, y));
		add(txtBemerkungen, GridBagConstraints.area(1, y).gridWidthRemainder());
	}

	// --------------------------------------------------------------
	// ---
	// --- ModelViewContainer implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public void setEditable(final boolean editable) {
		final boolean e2 = editable && USRRightCode.EDIT_USERS.isPermitted();
		super.setEditable(e2);
		txtPassword.setEditable(true);
		txtEMailAddress.setEditable(e2);
		txtPhoneFixnetBusiness.setEditable(e2);
		txtPhoneMobileBusiness.setEditable(e2);
		txtPhoneFaxBusiness.setEditable(e2);
		txtPhoneReceptionBusiness.setEditable(e2);
		cmbStatus.setEditable(e2);
	}
	
	private UserModel user;

	@Override
	public void updateView(final Object model) {
		this.user = (UserModel) model;
		txtPassword.setText(PW_NOT_CHANGED_STRING);
		super.updateView(model);
	}

	@Override
	public void updateModel(final Object model) {
		super.updateModel(model);
		// update password
		if (model instanceof UserModel) {
			final UserModel user = (UserModel) model;
			final char[] password = txtPassword.getPassword();
			if (txtPassword.isEditable() && isPasswordModified(password)) {
				try {
					user.setPassword(password, PasswordMethodCode.SHA512S);
				} catch (final Exception e) {
					// could not set password, should not happen
					throw new RuntimeException(e.getMessage(), e);
				} finally {
					CryptUtil.destroy(password);
				}
			}
		}
	}

	@Override
	public void validateView(final ValidationResults results, final Object model) {
		super.validateView(results, model);
		if (user == null)
			results.add("Benutzer == null", null);
		String s;
		
		// windows name
		s = txtLoginname.getText();
		if (Strings.isEmpty(s)) {
			results.add("Login-Name darf nicht leer sein", txtLoginname);
		}

		// check for duplicate windows name
		@SuppressWarnings("unchecked")
		final List<UserModel> l = (List) user.getContext().getModels(UserModel.class);
		final Iterator<UserModel> i = l.iterator();
		while (i.hasNext()) {
			final UserModel b = i.next();
			if (b!=user && b.getLoginname().equalsIgnoreCase(s)) {
				results.add("Der Login-Name ist bereits vergeben", txtLoginname);
			}
		}		

		// name
		s = txtLastname.getText();
		if (Strings.isEmpty(s)) {
			results.add("Nachname darf nicht leer sein", txtLastname);
		}

		char[] password = null;
		try {
			password = txtPassword.getPassword();
			if (isPasswordModified(password)) {
				if (password != null && password.length > 0 && password.length < 8)
					results.add("Das Passwort muss mindestens 8 Zeichen lang sein", txtPassword);
			}
		} finally {
			CryptUtil.destroy(password);
		}
	}

	static boolean isPasswordModified(final char[] password) {
		return !Arrays.equals(password, PW_NOT_CHANGED_CHARS);
	}

}

package ch.eisenring.user.client.gui;

import javax.swing.JComponent;
import javax.swing.JScrollPane;
import javax.swing.ScrollPaneConstants;

import ch.eisenring.gui.BorderLayout;
import ch.eisenring.gui.components.HEAGPanel;
import ch.eisenring.gui.interfaces.ModelViewContainer;

@SuppressWarnings("serial")
public class ScrollPanel extends HEAGPanel implements ModelViewContainer {

	private final JScrollPane scrollPane = new JScrollPane();
	
	private ModelViewContainer panel;
	
	public ScrollPanel(final ModelViewContainer panel) {
		this.panel = panel;
		initComponents();
		initLayout();
	}

	private void initComponents() {
		setLayout(new BorderLayout(true));
		scrollPane.setHorizontalScrollBarPolicy(ScrollPaneConstants.HORIZONTAL_SCROLLBAR_AS_NEEDED);
		scrollPane.setVerticalScrollBarPolicy(ScrollPaneConstants.VERTICAL_SCROLLBAR_ALWAYS);
		scrollPane.getVerticalScrollBar().setBlockIncrement(128);
		scrollPane.getVerticalScrollBar().setUnitIncrement(32);
		scrollPane.setViewportView((JComponent) panel);
	}

	private void initLayout() {
		removeAll();
		add(scrollPane, BorderLayout.CENTER);
	}
	
	// --------------------------------------------------------------
	// ---
	// --- ModelViewContainer implementation
	// ---
	// --------------------------------------------------------------
	private boolean editable = true;
	
	@Override
	public void updateView(final Object model) {
		if (panel != null)
			panel.updateView(model);
	}
	
	@Override
	public void updateModel(final Object model) {
		if (panel != null)
			panel.updateModel(model);
	}

	@Override
	public boolean isEditable() {
		return editable;
	}
	
	@Override
	public void setEditable(final boolean editable) {
		if (panel != null)
			panel.setEditable(editable);
		this.editable = editable;
	}

}

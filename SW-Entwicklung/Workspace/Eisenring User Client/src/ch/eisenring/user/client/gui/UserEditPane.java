package ch.eisenring.user.client.gui;

import java.awt.Dimension;

import ch.eisenring.gui.components.HEAGTabbedPane;
import ch.eisenring.gui.util.GUIUtil;
import ch.eisenring.user.client.USRRightCode;

@SuppressWarnings("serial")
public final class UserEditPane extends HEAGTabbedPane {

	private final UserGeneralPanel panelGeneral = new UserGeneralPanel();
	private final UserSignaturePanel panelSignature = new UserSignaturePanel();
	private final UserRolePanel panelRole = new UserRolePanel();
	private final UserOptionPanel panelOption = new UserOptionPanel();
	private final UserExternalCodePanel panelCode = new UserExternalCodePanel(); 
	
	public UserEditPane() {
		initLayout();
	}
	
	private void initLayout() {
		final Dimension limit = new Dimension(600, 440);
		GUIUtil.limit(panelGeneral, limit);
		GUIUtil.limit(panelSignature, limit);
		GUIUtil.limit(panelRole, limit);
		GUIUtil.limit(panelOption, limit);
		GUIUtil.limit(panelCode, limit);
		removeAll();
		add(panelGeneral, "Person");
		add(panelSignature, "Signatur");
		add(panelOption, "Optionen");
		add(panelRole, "Gruppen");
		add(panelCode, "Codes");
	}

	@Override
	public void setEditable(final boolean editable) {
		super.setEditable(editable);
		// correct editable for role membership panel
		panelRole.setEditable(USRRightCode.EDIT_USERS.isPermitted());
	}

}

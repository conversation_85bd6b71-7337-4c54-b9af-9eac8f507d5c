package ch.eisenring.user.client.action;

import java.awt.Window;

import ch.eisenring.app.client.AppComponent;
import ch.eisenring.app.client.AppCore;
import ch.eisenring.app.shared.ServiceNotFoundException;
import ch.eisenring.gui.util.GUIUtil;
import ch.eisenring.user.client.gui.UserEditDialog;
import ch.eisenring.user.client.resource.images.Images;
import ch.eisenring.user.shared.service.USRService;
import ch.eisenring.user.shared.service.UserFacade;

public final class ActionUserMyAccount extends AbstractUSRAction {

	private final Window parentWindow;
	
	public ActionUserMyAccount(final AppComponent appComponent, final Window parentWindow) {
		this(appComponent.getCore(), parentWindow);
	}

	public ActionUserMyAccount(final AppCore appCore, final Window parentWindow) {
		super(appCore, Images.USER_FEMALE, "Mein <PERSON>...");
		this.parentWindow = parentWindow;
	}

	@Override
	protected boolean isEnabledImpl() {
		if (!super.isEnabledImpl())
			return false;
		return getUser() != null;
	}

	@Override
	protected void performAction() {
		GUIUtil.invokeLater(new Runnable() {
			@Override
			public void run() {
				final UserFacade user = getUser();
				if (user != null) {
					final UserEditDialog dialog = new UserEditDialog(client, parentWindow);
					dialog.doDialog(user, false);
				}
			}
		});
	}

	private final UserFacade getUser() {
		final USRService service;
		try {
			service = client.locateService(USRService.class);
		} catch (final ServiceNotFoundException e) {
			return null;
		}
		return service.getLoggedInUser();
	}

}

package ch.eisenring.user.client.gui;

import java.awt.Component;
import java.awt.Dimension;
import java.awt.GridBagLayout;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.awt.event.MouseListener;
import java.util.Collections;

import javax.swing.DefaultListModel;
import javax.swing.JButton;
import javax.swing.JList;
import javax.swing.JScrollPane;
import javax.swing.ListSelectionModel;
import javax.swing.ScrollPaneConstants;

import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.gui.GridBagConstraints;
import ch.eisenring.gui.components.HEAGLabel;
import ch.eisenring.gui.components.HEAGPanel;
import ch.eisenring.gui.util.ToStringWrapper;
import ch.eisenring.model.Model;
import ch.eisenring.user.client.resource.images.Images;

@SuppressWarnings("serial")
public abstract class MembershipPanel<T extends Model, M extends Model> extends HEAGPanel {

	protected final JScrollPane scrollM = new JScrollPane();
	protected final JScrollPane scrollU = new JScrollPane();
	protected final DefaultListModel<Object> modelM = new DefaultListModel<Object>();
	protected final DefaultListModel<Object> modelU = new DefaultListModel<Object>();
	protected final JList<Object> listM = new JList<Object>(modelM);
	protected final JList<Object> listU = new JList<Object>(modelU);
	protected final JButton btnAdd = new JButton(Images.ARROW_WEST.getIcon());
	protected final JButton btnRem = new JButton(Images.ARROW_EAST.getIcon());
	protected final HEAGLabel lblM;
	protected final HEAGLabel lblU;
	protected final Component decorationComponent;

	private final MouseListener clickAddListener = new MouseAdapter() {
		@Override
		public void mouseClicked(final MouseEvent event) {
			if (!event.isConsumed()
					&& event.getClickCount() == 2 
					&& event.getButton() == MouseEvent.BUTTON1
					&& isEditable()) {
			    final int index = listU.locationToIndex(event.getPoint());
			    final Object user = modelU.get(index);;
			    if (user != null) {
					event.consume();
					modelM.addElement(user);
					modelU.removeElement(user);
					sortList(modelM);
			    }
			}
		}
	};
	
	private final MouseListener clickRemListener = new MouseAdapter() {
		@Override
		public void mouseClicked(final MouseEvent event) {
			if (!event.isConsumed()
					&& event.getClickCount() == 2
					&& event.getButton() == MouseEvent.BUTTON1
					&& isEditable()) {
			    final int index = listM.locationToIndex(event.getPoint());
			    final Object user = modelM.get(index);;
			    if (user != null) {
					event.consume();
					modelU.addElement(user);
					modelM.removeElement(user);
					sortList(modelU);
			    }
			}
		}
	};

	private final ActionListener addListener = new ActionListener() {
		@Override
		public void actionPerformed(final ActionEvent event) {
			if (isEditable()) {
				for (final Object user : listU.getSelectedValuesList()) {
					modelM.addElement(user);
					modelU.removeElement(user);
					sortList(modelM);
				}
			}
		}
	};
	
	private final ActionListener remListener = new ActionListener() {
		@Override
		public void actionPerformed(final ActionEvent event) {
			if (isEditable()) {
				for (final Object user : listM.getSelectedValuesList()) {
					modelU.addElement(user);
					modelM.removeElement(user);
					sortList(modelU);
				}
			}
		}
	};

	protected MembershipPanel(final String leftLabel, final String rightLabel,
				final Component decorationComponent) {
		this.decorationComponent = decorationComponent;
		lblM = new HEAGLabel(leftLabel);
		lblU = new HEAGLabel(rightLabel);
		initComponents();
		initLayout();
	}

	private void initComponents() {
		setLayout(new GridBagLayout());
		btnAdd.setFocusable(false);
		btnRem.setFocusable(false);
		Dimension d = btnAdd.getPreferredSize();
		d.width = d.height;
		btnAdd.setPreferredSize(d);
		btnRem.setPreferredSize(d);
		btnAdd.addActionListener(addListener);
		btnRem.addActionListener(remListener);
		
		d = new Dimension(150, 150);
		scrollM.setPreferredSize(d);
		scrollU.setPreferredSize(d);
		scrollM.setMinimumSize(d);
		scrollU.setMinimumSize(d);
		scrollM.setHorizontalScrollBarPolicy(ScrollPaneConstants.HORIZONTAL_SCROLLBAR_NEVER);
		scrollU.setHorizontalScrollBarPolicy(ScrollPaneConstants.HORIZONTAL_SCROLLBAR_NEVER);
		scrollM.setVerticalScrollBarPolicy(ScrollPaneConstants.VERTICAL_SCROLLBAR_ALWAYS);
		scrollU.setVerticalScrollBarPolicy(ScrollPaneConstants.VERTICAL_SCROLLBAR_ALWAYS);
		scrollM.setViewportView(listM);
		scrollU.setViewportView(listU);
		listM.setSelectionMode(ListSelectionModel.MULTIPLE_INTERVAL_SELECTION);
		listU.setSelectionMode(ListSelectionModel.MULTIPLE_INTERVAL_SELECTION);
		listM.addMouseListener(clickRemListener);
		listU.addMouseListener(clickAddListener);
	}

	private void initLayout() {
		removeAll();
		int y = -1;
		
		if (decorationComponent != null) {
			++y;
			add(decorationComponent, GridBagConstraints.field(0, y).gridWidthRemainder());
		}
	
		++y;
		add(lblM, GridBagConstraints.field(0, y));
		add(btnRem, GridBagConstraints.button(1, y));
		add(btnAdd, GridBagConstraints.button(2, y));
		add(lblU, GridBagConstraints.field(3, y));

		++y;
		add(scrollM, GridBagConstraints.area(0, y).gridWidth(2));
		add(scrollU, GridBagConstraints.area(2, y).gridWidth(2));
	}

	protected void sortList(final DefaultListModel<Object> model) {
		final int count = model.getSize();
		final ArrayList<Object> users = new ArrayList<Object>(count);
		for (int i=0; i<count; ++i) {
			users.add(model.get(i));
		}
		Collections.sort(users, ToStringWrapper.COMPARATOR_ASC);
		model.removeAllElements();
		for (final Object user : users) {
			model.addElement(user);
		}
	}

	@Override
	public void setEditable(final boolean editable) {
		super.setEditable(editable);
		btnAdd.setEnabled(editable);
		btnRem.setEnabled(editable);
		listM.setEnabled(editable);
		listU.setEnabled(editable);
	}

}

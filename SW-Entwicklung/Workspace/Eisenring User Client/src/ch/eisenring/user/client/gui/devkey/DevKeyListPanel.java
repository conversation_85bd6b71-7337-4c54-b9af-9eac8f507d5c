package ch.eisenring.user.client.gui.devkey;

import java.awt.GridBagLayout;
import java.awt.event.MouseEvent;

import javax.swing.JComponent;
import javax.swing.JScrollPane;
import javax.swing.ScrollPaneConstants;
import javax.swing.event.ListSelectionEvent;
import javax.swing.event.ListSelectionListener;

import ch.eisenring.app.shared.network.RPCContext;
import ch.eisenring.app.shared.network.RPCHandlerEDT;
import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.gui.GridBagConstraints;
import ch.eisenring.gui.action.AbstractAction;
import ch.eisenring.gui.balloontip.BalloonTipManager;
import ch.eisenring.gui.components.DecorationHeader;
import ch.eisenring.gui.components.HEAGButton;
import ch.eisenring.gui.components.HEAGPanel;
import ch.eisenring.gui.components.HEAGTable;
import ch.eisenring.gui.components.Spacer;
import ch.eisenring.gui.dialogs.OkCancelDialog;
import ch.eisenring.gui.util.GUIUtil;
import ch.eisenring.user.client.USRClient;
import ch.eisenring.user.client.resource.images.Images;
import ch.eisenring.user.shared.api.USRDeviceKey;
import ch.eisenring.user.shared.codetables.DeviceKeyAction;
import ch.eisenring.user.shared.network.PacketDevKeyActionReply;
import ch.eisenring.user.shared.network.PacketDevKeyActionRequest;
import ch.eisenring.user.shared.network.PacketDevKeyAllReply;
import ch.eisenring.user.shared.network.PacketDevKeyAllRequest;

@SuppressWarnings("serial")
public class DevKeyListPanel extends HEAGPanel {

	private final JScrollPane scrollPane = new JScrollPane();
	private final HEAGTable tblDevKey = new HEAGTable() {
		@Override
		protected void rowDoubleClicked(final int rowIndex,
				                        final Object rowObject,
				                        final MouseEvent event) {
			final USRDeviceKey devKey = tableModel.getRow(rowIndex);
			setSelectedKey(devKey);
			actionEdit.fire(); 
		}
	};
	private final ListSelectionListener selectionListener = new ListSelectionListener() {
		@Override
		public void valueChanged(ListSelectionEvent e) {
			final int rowIndex = tblDevKey.getSelectedRow();
			setSelectedKey(tableModel.getRow(rowIndex));
		}
	};

	private final AbstractAction actionEdit = new AbstractAction(
			Images.EDIT, "Bearbeiten...",
			"Öffnet die ausgewählte Freigabe zum Bearbeiten") {
		@Override
		protected boolean isEnabledImpl() {
			return super.isEnabledImpl() && selectedKey != null;
		}
		@Override
		protected void performAction() {
			final USRDeviceKey devKey = selectedKey;
			if (devKey != null)
				doEditDialog(devKey);
		}
	};

	private final AbstractAction actionDelete = new AbstractAction(
			Images.DELETE, "Löschen",
			"Löscht die ausgewählte Freigabe") {
		@Override
		protected boolean isEnabledImpl() {
			return super.isEnabledImpl() && selectedKey != null;
		}
		@Override
		protected void performAction() {
			final USRDeviceKey deviceKey = selectedKey;
			if (deviceKey == null)
				return;
			boolean b = OkCancelDialog.doDialog(
					OkCancelDialog.ICON, ch.eisenring.gui.resource.images.Images.STOP,
					OkCancelDialog.MESSAGE, "Diese Freigabe wirklich löschen?",
					OkCancelDialog.TITLE, "Löschen bestätigen",
					OkCancelDialog.PARENT, btnDelete);
			if (b) {
				final PacketDevKeyActionRequest request = PacketDevKeyActionRequest.create(deviceKey, DeviceKeyAction.DELETE);
				final RPCHandlerEDT rpcHandler = new RPCHandlerEDT() {
					@Override
					public void timeoutOccured(final RPCContext rpcContext) {
						BalloonTipManager.show(DevKeyListPanel.this, ErrorMessage.TIMEOUT);
					}
					@Override
					public void replyReceived(final RPCContext rpcContext) {
						final PacketDevKeyActionReply reply = (PacketDevKeyActionReply) rpcContext.getReply();
						if (reply.isValid()) {
							updateContent();
						} else {
							BalloonTipManager.show(DevKeyListPanel.this, reply.getMessage());
						}
					}
				};
				client.sendPacket(rpcHandler, request);
			}
		}
	};

//	private final ActionListener deleteActionListener = new ActionListener() {
//		@Override
//		public void actionPerformed(final ActionEvent e) {
//			if (selectedUser != null) {
//				boolean b = OkCancelDialog.doDialog(
//						OkCancelDialog.ICON, ch.eisenring.gui.resource.images.Images.STOP,
//						OkCancelDialog.MESSAGE, "Diesen Benutzer wirklich löschen?",
//						OkCancelDialog.TITLE, "Löschen bestätigen",
//						OkCancelDialog.PARENT, btnDelete);
//				if (b) {
//					((UserModel) selectedUser).delete();
//					client.storeChanges();
//					setContext(context);
//				}
//			}
//		}
//	};
//btnDelete.setToolTipText("Löscht den gerade ausgwählten Benutzer");

	private final HEAGButton btnEdit = new HEAGButton(actionEdit);
	private final HEAGButton btnDelete = new HEAGButton(actionDelete);

	private USRClient client;
	private DevKeyListTableModel tableModel = new DevKeyListTableModel();
	private USRDeviceKey selectedKey;
	
	public DevKeyListPanel(final USRClient client) {
		this.client = client;
		initComponents();
		initLayout();
	}

	private void initComponents() {
		setLayout(new GridBagLayout());
		btnEdit.setToolTipText("Öffnet den gerade ausgewählten Benutzer zum Bearbeiten");
		GUIUtil.makeSameSize(btnEdit, btnDelete);
		tblDevKey.setModel(tableModel);
		tableModel.applyLayout(tblDevKey);
		scrollPane.setHorizontalScrollBarPolicy(ScrollPaneConstants.HORIZONTAL_SCROLLBAR_AS_NEEDED);
		scrollPane.setVerticalScrollBarPolicy(ScrollPaneConstants.VERTICAL_SCROLLBAR_ALWAYS);
		scrollPane.setViewportView(tblDevKey);
	}

	private void initLayout() {
		removeAll();

		add(new DecorationHeader(DecorationHeader.ICON, Images.DEVKEY,
								 DecorationHeader.LABEL, "Geräte Freigaben"),
				GridBagConstraints.field(0, 0).gridWidth(4));
		
		add(btnEdit, GridBagConstraints.button(0, 1));
		add(btnDelete, GridBagConstraints.button(1, 1));
		add(new Spacer(), GridBagConstraints.field(2, 1).gridWidthRemainder());
		
		add(scrollPane, GridBagConstraints.area(0, 2).gridWidth(4));
	}
	
	private RPCHandlerEDT populateHandler = new RPCHandlerEDT() {
		@Override
		public void timeoutOccured(final RPCContext rpcContext) {
			BalloonTipManager.show(DevKeyListPanel.this, ErrorMessage.TIMEOUT);
		}
		
		@Override
		public void replyReceived(final RPCContext rpcContext) {
			final PacketDevKeyAllReply reply = (PacketDevKeyAllReply) rpcContext.getReply();
			if (reply.isValid()) {
				setContent(reply.getAllKeys());
			} else {
				BalloonTipManager.show(DevKeyListPanel.this, reply.getMessage());
			}
		}
	};

	void updateContent() {
		final PacketDevKeyAllRequest request = PacketDevKeyAllRequest.create();
		client.sendPacket(populateHandler, request);
	}

	public void setContent(final java.util.Collection<USRDeviceKey> allKeys) {
		//final USRDeviceKey selectedKey = this.selectedKey;
		tblDevKey.getSelectionModel().removeListSelectionListener(selectionListener);
		setSelectedKey(null);
		tableModel.setContent(allKeys);
		tblDevKey.getSelectionModel().addListSelectionListener(selectionListener);
	}

	protected void setSelectedKey(final USRDeviceKey devKey) {
		this.selectedKey = devKey;
		actionEdit.isEnabled();
		actionDelete.isEnabled();
	}
	
	/**
	 * Open edit dialog for Benutzer
	 */
	protected void doEditDialog(final USRDeviceKey devKey) {
		final DevKeyEditDialog dialog = new DevKeyEditDialog(client, this, devKey);
		dialog.doDialog();
	}

}

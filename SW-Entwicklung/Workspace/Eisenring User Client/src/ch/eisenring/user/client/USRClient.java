package ch.eisenring.user.client;

import ch.eisenring.app.client.AbstractAppComponent;
import ch.eisenring.app.client.AppCore;
import ch.eisenring.core.collections.Collection;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.feature.Feature;
import ch.eisenring.core.iterators.Filter;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.core.observable.Observable;
import ch.eisenring.core.observable.Observer;
import ch.eisenring.core.observable.ObserverNotificationPolicy;
import ch.eisenring.core.platform.Platform;
import ch.eisenring.core.threading.ThreadCore;
import ch.eisenring.email.EMailAddressBook;
import ch.eisenring.email.EMailAddressBookRegistry;
import ch.eisenring.model.Model;
import ch.eisenring.model.TransactionContext;
import ch.eisenring.network.StreamableConnection;
import ch.eisenring.user.client.gui.menu.USRMainMenuFeatures;
import ch.eisenring.user.client.network.USRPacketDispatcher;
import ch.eisenring.user.client.resource.images.Images;
import ch.eisenring.user.client.service.USRServiceImpl;
import ch.eisenring.user.shared.AddressBookSource;
import ch.eisenring.user.shared.PermissionEvaluator;
import ch.eisenring.user.shared.model.UserContext;
import ch.eisenring.user.shared.model.UserModel;
import ch.eisenring.user.shared.network.PacketRequest;
import ch.eisenring.user.shared.network.PacketUpdate;
import ch.eisenring.user.shared.service.UserFacade;

/**
 * The main API for interacting with the user system
 */
public final class USRClient extends AbstractAppComponent {

	static {
		Images.INITIALIZER.init();
	}

	/**
	 * Holds the current user context (user system model)
	 */
	public final Observable<UserContext> USER_CONTEXT =
		Observable.create(true, "UserSystem Context", null);
	
	/**
	 * Holds the currently logged in user
	 */
	public final Observable<UserModel> LOGGED_IN_USER =
		Observable.create(true, "Logged in User", null);

	/**
	 * Holds no useful value, but fires a state change
	 * whenever any permissions may have changed.
	 */
	public final Observable<Object> PERMISSIONS_CHANGED =
		Observable.create(true, "PermissonsChanged", null);

	private final AddressBookSource ADDRESSBOOK_SOURCE = new AddressBookSource() {
		@Override
		public Collection<EMailAddressBook> getAddressBooks() {
			final List<EMailAddressBook> result = new ArrayList<EMailAddressBook>(1);
			final UserContext context = USER_CONTEXT.get();
			if (context != null) {
				final EMailAddressBook addressbook = new EMailAddressBook("HEAG Benutzer");
				getUserAddresses(context, addressbook);
				result.add(addressbook);
			}
			return result;
		}
	};

	// --------------------------------------------------------------
	// ---
	// --- Feature management
	// ---
	// --------------------------------------------------------------
	private final Feature[] features = {
			new USRPacketDispatcher(this),
			new USRServiceImpl(this),
			new USRMainMenuFeatures(this)
	};

	@Override
	public Feature[] getFeatures() {
		return features;
	}

	// --------------------------------------------------------------
	// ---
	// --- Client side permission control
	// ---
	// --------------------------------------------------------------
	private CachingPermissionEvaluator permissionEvaluator;
	
	private final Observer<UserContext> userContextObserver = new Observer<UserContext>() {
		@Override
		public void observableChanged(final Observable<UserContext> observeable) {
			updatePermissionProvider();
		}
	};

	// --------------------------------------------------------------
	// ---
	// --- AppComponent implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public void launch() throws Exception {
		super.launch();
		final AppCore core = getCore();
		core.addConnectionObserver(connectionObserver, ObserverNotificationPolicy.ASYNCHRONOUS);
		// configure RightCode to use the current user for permission evaluation
		permissionEvaluator = new CachingPermissionEvaluator();
		PermissionEvaluator.setEvaluator(permissionEvaluator);
		USER_CONTEXT.addObserver(userContextObserver, ObserverNotificationPolicy.SYNCHRONOUS);
		EMailAddressBookRegistry.addSource(ADDRESSBOOK_SOURCE);
	}
	
	@Override
	public void shutdown() throws Exception {
		EMailAddressBookRegistry.removeSource(ADDRESSBOOK_SOURCE);
		USER_CONTEXT.removeObserver(userContextObserver);
		permissionEvaluator.setUser(null);
		PermissionEvaluator.setEvaluator(null);
		super.shutdown();
	}

	private final Observer<Object> connectionObserver = new Observer<Object>() {
		public void observableChanged(final Observable<Object> observable) {
			final StreamableConnection connection = getConnection(false);
			if (connection == null || !connection.isAlive()) {
				// invalidate user data when connection is lost
				USER_CONTEXT.set((UserContext) null);
			} else {
				// request user data
				final PacketRequest packet = PacketRequest.create();
				sendPacket(packet, false);
				Logger.info("Requested user context from server");
			}
		}
	};

	public void updatePermissionProvider() {
		final UserModel user;
		final Platform platform = Platform.getPlatform();
		final String loginname = platform.getLoginName();
		final TransactionContext context = USER_CONTEXT.get();
		if (context == null) {
			user = null;
		} else {
			final List<?> userList = context.getModels(new Filter<Model>() {
				@Override
				public boolean accepts(final Model model) {
					if (!(model instanceof UserFacade))
						return false;
					final UserFacade user = (UserFacade) model;
					return Strings.equalsIgnoreCase(user.getLoginname(), loginname);
				}
			});
			if (userList.size() == 1) {
				user = (UserModel) userList.get(0);
			} else {
				user = null;
			}
		}
		final UserModel permEval = user != null && user.isLoginAllowed() ? user : null;
		permissionEvaluator.setUser(permEval);
		LOGGED_IN_USER.set(user);
		PERMISSIONS_CHANGED.fireValueChange();
		Logger.info("Set Permission Provider/Logged User to: {} / {}", permEval, user);
	}

	// --------------------------------------------------------------
	// ---
	// --- Private API
	// ---
	// --------------------------------------------------------------
	public UserContext getContext() {
		UserContext context = USER_CONTEXT.get();
		while (context == null) {
			ThreadCore.sleep(50);
			context = USER_CONTEXT.get();
		}
		return context;
	}

	public void storeChanges() {
		synchronized (getLock()) {
			final PacketUpdate packet;
			final UserContext context = USER_CONTEXT.get();
			try {
				packet = PacketUpdate.create(context);
				context.setUnchanged(true);
			} catch (final Exception e) {
				USER_CONTEXT.set((UserContext) null);
				error("Änderungen der Benutzerverwaltung konnten nicht gespeichert werden");
				return;
			}
			sendPacket(packet, false);
		}
	}

}

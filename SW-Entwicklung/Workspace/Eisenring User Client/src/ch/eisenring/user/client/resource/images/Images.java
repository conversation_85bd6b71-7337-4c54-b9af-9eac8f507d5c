package ch.eisenring.user.client.resource.images;

import static ch.eisenring.core.resource.ImageResource.FOUR_STANDARD_SIZES;
import static ch.eisenring.user.shared.resource.Images.*;
import static ch.eisenring.user.shared.resource.Images.USR_STATUS_INACTIVE;
import static ch.eisenring.user.shared.resource.Images.USR_STATUS_LOCKED;

import ch.eisenring.core.resource.ImageResource;
import ch.eisenring.core.resource.ResourceInitializer;
import ch.eisenring.core.resource.image.MIPImgResource;

public interface Images extends ch.eisenring.app.client.images.Images {

	public final static ImageResource USER_LIST = new MIPImgResource("users*.png", Images.class, 16, 24, 32, 48);
	public final static ImageResource USER_EDIT = new MIPImgResource("user_edit*.png", Images.class, 16, 32);
	public final static ImageResource USER_DELETE = new MIPImgResource("user_del*.png", Images.class, 16, 32);
	public final static ImageResource USER_ADD = new MIPImgResource("user_add*.png", Images.class, 16, 32);

	public final static ImageResource USER_MALE = new MIPImgResource("user_male*.png", Images.class, 16, 22, 32, 48, 128);
	public final static ImageResource USER_FEMALE = new MIPImgResource("user_female*.png", Images.class, 22, 32, 48, 64, 128);
		
	public final static ImageResource ROLE_LIST = new MIPImgResource("roles*.png", Images.class, 16, 22, 32);
	public final static ImageResource ROLE_EDIT = new MIPImgResource("role_edit*.png", Images.class, 16, 22, 32);
	public final static ImageResource ROLE_DELETE = new MIPImgResource("role_del*.png", Images.class, 16, 22, 32);
	public final static ImageResource ROLE_ADD = new MIPImgResource("role_add*.png", Images.class, 16, 22, 32);

	public final static ImageResource COMMON = new MIPImgResource("settings_common*.png", Images.class, 16, 24, 32, 48, 256);
	public final static ImageResource UNKNOWN = new MIPImgResource("settings_unknown*.png",Images.class, 16, 22, 32, 48, 128);
	public final static ImageResource OPTIONS = new MIPImgResource("options*.png", Images.class, FOUR_STANDARD_SIZES);
	public final static ImageResource DEVKEY = new MIPImgResource("devkey*.png", Images.class, FOUR_STANDARD_SIZES);
	
	public final static ResourceInitializer INITIALIZER = new ResourceInitializer(Images.class) {
		protected void initImpl() {
			USR_STATUS_ACTIVE.setDelegate(SPHERE_GREEN);
			USR_STATUS_INACTIVE.setDelegate(SPHERE_BLACK);
			USR_STATUS_LOCKED.setDelegate(SPHERE_RED);
			
			COLOR_GREEN.setDelegate(SPHERE_GREEN);
			COLOR_YELLOW.setDelegate(SPHERE_YELLOW);
			COLOR_RED.setDelegate(SPHERE_RED);
		}
	};

}

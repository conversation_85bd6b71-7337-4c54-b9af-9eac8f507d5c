package ch.eisenring.user.client.action;

import ch.eisenring.app.client.AppComponent;
import ch.eisenring.app.client.AppCore;
import ch.eisenring.user.client.gui.AbstractUSRDialog;
import ch.eisenring.user.client.gui.WhoHasRightDialog;
import ch.eisenring.user.shared.model.RightModel;

public final class ActionUserWhoHasRight extends AbstractUSRAction {

	private final RightModel right;
	
	public ActionUserWhoHasRight(final AppComponent appComponent,
			                     final RightModel right) {
		this(appComponent.getCore(), right);
	}

	public ActionUserWhoHasRight(final AppCore appCore,
			                     final RightModel right) {
		super(appCore, "Recht-Inhaber anzeigen...");
		this.right = right;
	}

	@Override
	protected void performAction() {
		final AbstractUSRDialog dialog = new WhoHasRightDialog(client, right);
		dialog.setVisible(true);
	}
	
}

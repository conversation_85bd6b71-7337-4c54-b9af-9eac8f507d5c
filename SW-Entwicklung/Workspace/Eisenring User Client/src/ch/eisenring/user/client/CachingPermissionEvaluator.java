package ch.eisenring.user.client;

import ch.eisenring.core.collections.Lookup;
import ch.eisenring.core.collections.impl.HashMap;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.user.shared.Permission;
import ch.eisenring.user.shared.PermissionEvaluator;

/**
 * Implementation of PermissionEvaluator intended for
 * client use.
 */
public class CachingPermissionEvaluator implements PermissionEvaluator {

	final class PermissionKey {
		int id;

		public PermissionKey(final int id) {
			this.id = id;
		}

		@Override
		public int hashCode() {
			return id;
		}

		@Override
		public boolean equals(final Object o) {
			return o instanceof PermissionKey && ((PermissionKey) o).id == id;
		}
		
		@Override
		public String toString() {
			return Strings.toString(id);
		}
	}

	private Lookup<PermissionKey, Boolean> permissionMap = new HashMap<>(32);
	private PermissionKey lookupKey = new PermissionKey(0);
	private PermissionEvaluator evaluator;

	/**
	 * Invalidates all cached permissions. Subsequent isPermitted()
	 * calls will re-evaluate permissions
	 */
	public void flushCache() {
		synchronized (permissionMap) {
			permissionMap.clear();
		}
	}

	/**
	 * Sets the user who will provide the permissions to
	 * be evaluated. As longs as the user is null, all
	 * permissions are assumed to be not granted (that is,
	 * isPermitted() will return false)
	 */
	public void setUser(final PermissionEvaluator user) {
		synchronized (permissionMap) {
			this.evaluator = user;
			flushCache();
		}
	}

	/**
	 * Implements permission evaluation
	 */
	@Override
	public boolean isPermitted(final Permission permission) {
		if (permission == null)
			return false;
		final int id = permission.getId();
		Boolean result;
		synchronized (permissionMap) {
			lookupKey.id = id;
			result = permissionMap.get(lookupKey);
			if (result != null)
				return result.booleanValue();
			// if evaluator not set, bypass cache allocation
			if (evaluator == null)
				return false;
			result = Boolean.valueOf(evaluator.isPermitted(permission));
			permissionMap.put(new PermissionKey(id), result);
			return result.booleanValue();
		}
	}

}

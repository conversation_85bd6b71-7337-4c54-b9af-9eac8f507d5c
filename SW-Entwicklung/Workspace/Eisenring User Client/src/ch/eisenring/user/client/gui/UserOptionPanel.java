package ch.eisenring.user.client.gui;

import java.awt.GridBagLayout;
import java.util.Iterator;
import java.util.Map.Entry;

import javax.swing.JScrollPane;
import javax.swing.ScrollPaneConstants;

import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.Map;
import ch.eisenring.core.collections.impl.HashMap;
import ch.eisenring.gui.GridBagConstraints;
import ch.eisenring.gui.components.DecorationHeader;
import ch.eisenring.gui.components.HEAGCheckBox;
import ch.eisenring.gui.components.HEAGPanel;
import ch.eisenring.gui.components.Separator;
import ch.eisenring.gui.util.ConversionUtil;
import ch.eisenring.gui.util.LayoutUtil;
import ch.eisenring.model.TransactionContext;
import ch.eisenring.user.client.resource.images.Images;
import ch.eisenring.user.shared.metamodel.USRUserMeta;
import ch.eisenring.user.shared.model.OptionModel;
import ch.eisenring.user.shared.model.UserModel;

@SuppressWarnings("serial")
public final class UserOptionPanel extends HEAGPanel {

	private final JScrollPane scrollPane = new JScrollPane();
	private final HEAGPanel optionPanel = new HEAGPanel();
	private final Map<OptionModel, HEAGCheckBox> checkMap = new HashMap<OptionModel, HEAGCheckBox>();
	
	public UserOptionPanel() {
		initComponents();
		initLayout();
	}

	private void initComponents() {
		optionPanel.setLayout(new GridBagLayout());
		scrollPane.setHorizontalScrollBarPolicy(ScrollPaneConstants.HORIZONTAL_SCROLLBAR_NEVER);
		scrollPane.setVerticalScrollBarPolicy(ScrollPaneConstants.VERTICAL_SCROLLBAR_ALWAYS);
		scrollPane.setViewportView(optionPanel);
	}

	private void initLayout() {
		removeAll();
		optionPanel.removeAll();

		setLayout(new GridBagLayout());
		add(new DecorationHeader(DecorationHeader.ICON, Images.OPTIONS,
				DecorationHeader.LABEL, "Optionen",
				DecorationHeader.SEPARATOR, false),
				GridBagConstraints.field(0, 0));
		add(scrollPane, GridBagConstraints.area(0, 1));
		
	}

	// --------------------------------------------------------------
	// ---
	// --- ModelViewContainer implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public void setEditable(final boolean editable) {
		super.setEditable(editable);
	}
	
	@SuppressWarnings("unchecked")
	@Override
	public void updateView(final Object model) {
		super.updateView(model);
		checkMap.clear();
		if (model != null) {
			final UserModel user = (UserModel) model;
			final TransactionContext context = user.getContext();
			final List<OptionModel> optionList = (List) context.getModels(OptionModel.class);
			final LayoutUtil l = new LayoutUtil(1);
			String lastApplication = "\u0000@@@\u0000";
			final Iterator<OptionModel> optionItr = optionList.iterator();
			while (optionItr.hasNext()) {
				final OptionModel option = optionItr.next();
				final String applicationname = option.getApplicationname();
				if (!applicationname.equals(lastApplication)) {
					l.nextLine();
					lastApplication = applicationname;
					optionPanel.add(Separator.create(applicationname), l.separator());
				}
				final HEAGCheckBox checkBox = new HEAGCheckBox(option.getDisplayname());
				final boolean checked = USRUserMeta.REL_OPTIONS.contains(user, option);
				checkBox.setValue(Boolean.valueOf(checked));
				checkBox.setEnabled(isEditable());
				checkMap.put(option, checkBox);
				optionPanel.add(checkBox, l.field(1));
			}
			l.nextLine();
			optionPanel.add(new HEAGPanel(), l.area());
		}
	}

	@Override
	public void updateModel(final Object model) {
		super.updateModel(model);
		if (model instanceof UserModel) {
			final UserModel user = (UserModel) model;
			final Iterator<Entry<OptionModel, HEAGCheckBox>> entryItr = checkMap.entrySet().iterator();
			while (entryItr.hasNext()) {
				final Entry<OptionModel, HEAGCheckBox> entry = entryItr.next();
				final OptionModel option = entry.getKey();
				final HEAGCheckBox checkBox = entry.getValue();
				final boolean checked = ConversionUtil.convert(checkBox, false);
				if (checked) {
					USRUserMeta.REL_OPTIONS.add(user, option);
				} else {
					USRUserMeta.REL_OPTIONS.remove(user, option);
				}
			}
		}
	}
}

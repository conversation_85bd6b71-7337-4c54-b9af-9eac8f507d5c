package ch.eisenring.user.client.action;

import ch.eisenring.app.client.AppCore;
import ch.eisenring.app.client.action.AbstractAPPAction;
import ch.eisenring.user.client.USRClient;

/**
 * Base class for actions
 */
abstract class AbstractUSRAction extends AbstractAPPAction<USRClient> {

	protected AbstractUSRAction(final AppCore appCore, final Object ... properties) {
		super((USRClient) appCore.getComponent(USRClient.class, true), properties);
		addPermissionObserver();
	}

}

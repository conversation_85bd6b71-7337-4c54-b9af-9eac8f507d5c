package ch.eisenring.user.client.gui.devkey;

import java.awt.Component;
import java.awt.Dimension;

import ch.eisenring.core.tag.TagSet;
import ch.eisenring.gui.BorderLayout;
import ch.eisenring.gui.balloontip.BalloonTipManager;
import ch.eisenring.gui.model.StoreResults;
import ch.eisenring.gui.window.DialogTags;
import ch.eisenring.gui.window.SACButtonPanel;
import ch.eisenring.gui.window.WindowTags;
import ch.eisenring.user.client.USRClient;
import ch.eisenring.user.client.gui.AbstractUSRDialog;
import ch.eisenring.user.client.resource.images.Images;
import ch.eisenring.user.shared.api.USRDeviceKey;
import ch.eisenring.user.shared.codetables.DeviceKeyAction;
import ch.eisenring.user.shared.network.PacketDevKeyActionReply;
import ch.eisenring.user.shared.network.PacketDevKeyActionRequest;

@SuppressWarnings("serial")
public final class DevKeyEditDialog extends AbstractUSRDialog {

	private final DevKeyEditPanel editPanel;
	private final SACButtonPanel buttonPanel = new SACButtonPanel(); 

	private final USRDeviceKey devKey;
	private final Component parent;
	
	public DevKeyEditDialog(final USRClient client, final Component parent, final USRDeviceKey devKey) {
		super(client, new TagSet( 
				WindowTags.MINIMUM_SIZE, new Dimension(512, 384),
				WindowTags.POSITION_SETTINGS_ID, "DevKeyEdit",
				WindowTags.ICON, Images.EDIT,
				WindowTags.LAYOUTMANAGER, new BorderLayout(),
				WindowTags.TITLE, "Geräte-Freigabe bearbeiten",
				DialogTags.MODALITY, DialogTags.MODALITY_MODAL,
				DialogTags.DIALOG_OWNER, parent));
		this.devKey = devKey;
		this.parent = parent;
		this.editPanel = new DevKeyEditPanel(devKey);
		initComponents();
		initLayout();
		pack();
	}
	
	private void initComponents() {
		buttonPanel.configureApplyButton(null, null);
	}

	private void initLayout() {
		getContentPane().removeAll();

		add(editPanel, BorderLayout.CENTER);
		add(buttonPanel, BorderLayout.SOUTH);
	}
	
	public void doDialog() {
		editPanel.updateView(devKey);
		editPanel.setEditable(true);
		setVisible(true);
	}

	@Override
	public void storeView(final StoreResults results, final Object model) {
		super.storeView(results, model);
		if (results.isSuccess()) {
			final PacketDevKeyActionRequest request = PacketDevKeyActionRequest.create(devKey, DeviceKeyAction.UPDATE);
			final PacketDevKeyActionReply reply = (PacketDevKeyActionReply) client.sendAndWait(request);
			if (reply.isValid()) {
				updateParent();
			} else {
				BalloonTipManager.show(editPanel, reply.getMessage());
			}
		}
	}

	@Override
	protected void onCancel() {
		super.onCancel();
		updateParent();
	}

	private void updateParent() {
		if (parent instanceof DevKeyListPanel) {
			((DevKeyListPanel) parent).updateContent();
		}
	}

	@Override
	public Object getViewModel() {
		return devKey;
	}

}

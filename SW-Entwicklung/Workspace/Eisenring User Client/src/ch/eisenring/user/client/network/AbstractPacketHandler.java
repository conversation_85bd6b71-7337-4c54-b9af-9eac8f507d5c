package ch.eisenring.user.client.network;

import ch.eisenring.network.implementation.PacketDispatchMode;
import ch.eisenring.network.packet.AbstractPacket;
import ch.eisenring.user.client.USRClient;

abstract class AbstractPacketHandler extends ch.eisenring.app.client.network.AbstractPacketHandler<USRClient, AbstractPacket> {

	protected AbstractPacketHandler(final USRClient client, 
			                        final Class<? extends AbstractPacket> packetClass,
			                        final PacketDispatchMode dispatchMode) {
		super(client, packetClass, dispatchMode);
	}

}

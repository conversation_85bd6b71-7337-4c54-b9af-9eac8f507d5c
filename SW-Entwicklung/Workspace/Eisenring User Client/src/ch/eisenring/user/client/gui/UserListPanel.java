package ch.eisenring.user.client.gui;

import java.awt.GridBagLayout;
import java.awt.event.MouseEvent;

import javax.swing.JComponent;
import javax.swing.JScrollPane;
import javax.swing.ScrollPaneConstants;
import javax.swing.event.DocumentEvent;
import javax.swing.event.DocumentListener;
import javax.swing.event.ListSelectionEvent;
import javax.swing.event.ListSelectionListener;

import ch.eisenring.core.collections.Collection;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.iterators.Filter;
import ch.eisenring.gui.GridBagConstraints;
import ch.eisenring.gui.action.AbstractAction;
import ch.eisenring.gui.components.*;
import ch.eisenring.gui.util.GUIUtil;
import ch.eisenring.model.TransactionContext;
import ch.eisenring.user.client.USRClient;
import ch.eisenring.user.client.resource.images.Images;
import ch.eisenring.user.shared.model.UserModel;
import ch.eisenring.user.shared.service.UserFacade;

@SuppressWarnings("serial")
public class UserListPanel extends HEAGPanel {

	private final JScrollPane scrollPane = new JScrollPane();
	private final HEAGTable tblUser = new HEAGTable() {
		@Override
		protected void rowDoubleClicked(final int rowIndex,
				                        final Object rowObject,
				                        final MouseEvent event) {
			final UserFacade benutzer = tableModel.getRow(rowIndex);
			setSelectedBenutzer(benutzer);
			actionEdit.fire(); 
		}
	};
	private final ListSelectionListener selectionListener = new ListSelectionListener() {
		@Override
		public void valueChanged(ListSelectionEvent e) {
			final int rowIndex = tblUser.getSelectedRow();
			setSelectedBenutzer(tableModel.getRow(rowIndex));
		}
	};
	private final AbstractAction actionEdit = new AbstractAction(
			Images.USER_EDIT, "Bearbeiten...",
			"Öffnet den gerade ausgewählten Benutzer zum Bearbeiten") {
		@Override
		protected boolean isEnabledImpl() {
			return super.isEnabledImpl() && selectedUser != null;
		}
		@Override
		protected void performAction() {
			final UserFacade user = selectedUser;
			if (user != null)
				doEditDialog((UserModel) user);
		}
	};
	private final AbstractAction actionAdd = new AbstractAction(
			Images.USER_ADD, "Neu...",
			"Öffnet den Dialog zur Eingabe eines neuen Benutzers") {
		@Override
		protected void performAction() {
			doEditDialog(null);
		}
	};

	private final DocumentListener searchListener = new DocumentListener() {
		@Override
		public void insertUpdate(final DocumentEvent e) { setSearchCriteria(txtSearch.getText()); }

		@Override
		public void removeUpdate(final DocumentEvent e) {
			setSearchCriteria(txtSearch.getText());
		}

		@Override
		public void changedUpdate(final DocumentEvent e) { setSearchCriteria(txtSearch.getText()); }
	};

	private final HEAGButton btnAdd = new HEAGButton(actionAdd);
	private final HEAGButton btnEdit = new HEAGButton(actionEdit);
	private final HEAGTextField txtSearch = new HEAGTextField(30);

	private USRClient client;
	private UserListTableModel tableModel = new UserListTableModel();
	private UserFacade selectedUser;
	private TransactionContext context;
	
	public UserListPanel(final USRClient client) {
		this.client = client;
		initComponents();
		initLayout();
	}

	private void initComponents() {
		setLayout(new GridBagLayout());
		btnAdd.setToolTipText("Öffnet den Dialog zur Eingabe eines neuen Benutzers");
		btnEdit.setToolTipText("Öffnet den gerade ausgewählten Benutzer zum Bearbeiten");
		GUIUtil.makeSameSize(btnAdd, btnEdit);
		tblUser.setModel(tableModel);
		tableModel.applyLayout(tblUser);
		scrollPane.setHorizontalScrollBarPolicy(ScrollPaneConstants.HORIZONTAL_SCROLLBAR_AS_NEEDED);
		scrollPane.setVerticalScrollBarPolicy(ScrollPaneConstants.VERTICAL_SCROLLBAR_ALWAYS);
		scrollPane.setViewportView(tblUser);
		txtSearch.getDocument().addDocumentListener(searchListener);
	}

	private void initLayout() {
		removeAll();

		add(new DecorationHeader(DecorationHeader.ICON, Images.USER_LIST,
								 DecorationHeader.LABEL, "Benutzerliste"),
				GridBagConstraints.field(0, 0).gridWidth(4));
		
		add(btnAdd, GridBagConstraints.button(0, 1));
		add(btnEdit, GridBagConstraints.button(1, 1));
		add(new HEAGLabel("Suchen"), GridBagConstraints.button(2, 1));
		add(txtSearch, GridBagConstraints.field(3, 1));
		add(new Spacer(), GridBagConstraints.field(2, 1).gridWidthRemainder());
		
		add(scrollPane, GridBagConstraints.area(0, 2).gridWidth(10));
	}
	
	public void setContext(final TransactionContext context) {
		tblUser.getSelectionModel().removeListSelectionListener(selectionListener);
		setSelectedBenutzer(null);
		this.context = context;
		tblUser.getSelectionModel().addListSelectionListener(selectionListener);
		refreshUserList();
	}

	protected void setSelectedBenutzer(final UserFacade user) {
		this.selectedUser = user;
		actionEdit.isEnabled();
	}
	
	/**
	 * Open edit dialog for Benutzer
	 */
	protected void doEditDialog(UserModel benutzer) {
		UserEditDialog dialog = new UserEditDialog(client, this);
		boolean isNew = false;
		if (benutzer == null) {
			benutzer = new UserModel(context, null);
			isNew = true;
		}
		dialog.doDialog(benutzer, isNew);
	}

	// --------------------------------------------------------------
	// ---
	// --- Live search
	// ---
	// --------------------------------------------------------------
	private String searchPattern;

	/**
	 * Sets new search criterion
	 */
	void setSearchCriteria(final String searchPattern) {
		final String sp = Strings.clean(searchPattern);
		if (Strings.equals(this.searchPattern, sp))
			return;
		this.searchPattern = sp;
		refreshUserList();
	}

	void refreshUserList() {
		final Filter<UserFacade> filter;
		if (searchPattern == null || searchPattern.length() < 3) {
			filter = (Filter<UserFacade>) Filter.ACCEPT;
		} else {
			final String lowerPattern = Strings.toLower(searchPattern);
			filter = new Filter<UserFacade>() {
				@Override
				public boolean accepts(final UserFacade user) {
					if (user == null)
						return false;
					return contains(user.getLoginname(), lowerPattern)
							|| contains(user.getFirstname(), lowerPattern)
							|| contains(user.getLastname(), lowerPattern)
							|| contains(user.getEMailAddress(), lowerPattern);
				}
			};
		}
		final TransactionContext context = this.context;
		final List<? extends UserFacade> allUsers = context != null
				? context.getModels(UserModel.class) : List.emptyReadOnly(UserFacade.class);
		final List<UserFacade> shownUsers = filter.filter(allUsers);
		tableModel.setContent(shownUsers);
	}

	static boolean contains(final CharSequence haystack, final CharSequence lowerPattern) {
		if (haystack == null || haystack.length() <= 0
			|| lowerPattern == null || lowerPattern.length() <= 0)
			return false;
		// using a StringMaker for the operation avoids creating a new String for every operation
		final StringMaker lowerHaystack = StringMaker.obtain(haystack.length());
		lowerHaystack.append(haystack);
		lowerHaystack.toLower();
		final boolean result = Strings.contains(lowerHaystack, lowerPattern);
		lowerHaystack.releaseSilent();
		return result;
	}

}

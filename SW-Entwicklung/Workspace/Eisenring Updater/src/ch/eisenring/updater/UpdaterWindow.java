package ch.eisenring.updater;

import static ch.eisenring.updater.UpdaterConstants.VERSION;

import java.awt.BorderLayout;
import java.awt.event.WindowAdapter;
import java.awt.event.WindowEvent;
import java.awt.event.WindowListener;

import javax.swing.JFrame;
import javax.swing.WindowConstants;

import ch.eisenring.core.observable.Observable;
import ch.eisenring.core.observable.Observer;
import ch.eisenring.core.observable.ObserverNotificationPolicy;
import ch.eisenring.gui.util.GUIUtil;
import ch.eisenring.updater.resources.Images;

@SuppressWarnings("serial")
public class UpdaterWindow extends JFrame {

	private final Updater UPDATER;
	private final UpdaterPanel PANEL;
	private boolean guiClosed;
	
	private final WindowListener windowListener = new WindowAdapter() {
		@Override
		public void windowClosed(WindowEvent e) {
			closeGUI();
		}
		@Override
		public void windowClosing(WindowEvent e) {
			closeGUI();
		}
	};

	private final Observer<Boolean> observer = new Observer<Boolean>() {
		@Override
		public void observableChanged(final Observable<Boolean> observeable) {
			if (guiClosed)
				closeGUI();
		}
	};
	
	public UpdaterWindow(Updater updater) {
		this.UPDATER = updater;
		this.PANEL = new UpdaterPanel(this.UPDATER);
		UPDATER.ENABLE_CLOSE.addObserver(observer, ObserverNotificationPolicy.EDT_ASYNCHRONOUS);
		addWindowListener(windowListener);
		setIconImages(Images.UPDATER.getImageList());
		initLayout();
		pack();
		setSize(584, 384);
		setResizable(false);
		setDefaultCloseOperation(WindowConstants.DO_NOTHING_ON_CLOSE);
		setTitle("Eisenring Updater " + VERSION);
		GUIUtil.centerOnScreen(this);
	}
	
	/**
	 * Checks if the GUI can be closed.
	 * If the close operation is valid, closes the GUI,
	 * otherwise it marks the GUI to be closed when
	 * the close is allowed.
	 */
	public void closeGUI() {
		guiClosed = true;
		if (UPDATER.ENABLE_CLOSE.get()) {
			setVisible(false);
			dispose();
			System.exit(0);
		}
	}

	private void initLayout() {
		getContentPane().removeAll();
		setLayout(new BorderLayout());
		add(PANEL, BorderLayout.CENTER);
	}
	
}

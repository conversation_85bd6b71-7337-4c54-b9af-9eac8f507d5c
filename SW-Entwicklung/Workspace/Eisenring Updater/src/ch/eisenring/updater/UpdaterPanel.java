package ch.eisenring.updater;

import java.awt.GridBagLayout;

import javax.swing.JProgressBar;
import javax.swing.ScrollPaneConstants;
import javax.swing.SwingConstants;

import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.observable.Observable;
import ch.eisenring.core.observable.Observer;
import ch.eisenring.core.observable.ObserverNotificationPolicy;
import ch.eisenring.gui.GridBagConstraints;
import ch.eisenring.gui.components.HEAGLabel;
import ch.eisenring.gui.components.HEAGPanel;
import ch.eisenring.gui.components.HEAGTextArea;
import ch.eisenring.gui.util.GUIUtil;

@SuppressWarnings("serial")
public class UpdaterPanel extends HEAGPanel {

	private final Updater updater;
	private final JProgressBar progressBar = new JProgressBar(SwingConstants.HORIZONTAL);
	private final HEAGLabel lblProduct = new HEAGLabel("", false);
	private final HEAGTextArea txtMessages = new HEAGTextArea(10000000);
	private final HEAGLabel lblProgress = new HEAGLabel("Fortschritt");

	private final Observer<Integer> progressObserver = new Observer<Integer>() {
		@Override
		public void observableChanged(final Observable<Integer> observeable) {
			updateProgressBar();
		}
	};

	private final Observer<String> messageObserver = new Observer<String>() {
		@Override
		public void observableChanged(final Observable<String> observeable) {
			updateMessages();
		}
	};

	public UpdaterPanel(final Updater updater) {
		this.updater = updater;
		updater.PROGRESS.addObserver(progressObserver, ObserverNotificationPolicy.EDT_ASYNCHRONOUS);
		updater.MESSAGES.addObserver(messageObserver, ObserverNotificationPolicy.EDT_ASYNCHRONOUS);
		initComponents();
		initLayout();
	}

	private void initComponents() {
		setLayout(new GridBagLayout());
		
		final String productName = Strings.clean(updater.PRODUCT_NAME.get());
		final String productText = Strings.concat("<html>Die Anwendung <b>", productName, "</b> wird jetzt aktualisiert.");
		lblProduct.setText(productText);
		
		txtMessages.setEditable(false);
		txtMessages.setHorizontalScrollBarPolicy(ScrollPaneConstants.HORIZONTAL_SCROLLBAR_ALWAYS);
		
		progressBar.setMinimum(0);
		progressBar.setMaximum(1000);
		progressBar.setValue(0);
	}
	
	private void initLayout() {
		removeAll();

		add(lblProduct, GridBagConstraints.field(0, 0).gridWidth(2));
	
		add(lblProgress, GridBagConstraints.label(0, 1));
		add(progressBar, GridBagConstraints.field(1, 1));

		add(txtMessages, GridBagConstraints.area(0, 2).gridWidth(2));
	}
	
	/**
	 * Updates the progress bar
	 */
	protected void updateProgressBar() {
		if (GUIUtil.isEventDispatchThread()) {
			final Object value = updater.PROGRESS.get();
			if (value instanceof Number) {
				int progress = ((Number) value).intValue();
				if (progress<0) {
					progress = 0;
				} else if (progress>1000) {
					progress = 1000;
				}
				if (progressBar.getValue() != progress) {
					progressBar.setValue(progress);
				}
			}
	 	} else {
	 		// delegate the update to the event dispatch thread
	 		GUIUtil.invokeLater(new Runnable() {
	 			@Override
	 			public void run() {
	 				updateProgressBar();
	 			}
	 		});
	 	}
	}
	
	/**
	 * Updates the message text area
	 */
	protected void updateMessages() {
		final String messages = updater.MESSAGES.get("");
		setMessages(messages);
	}
	
	public void setMessages(final String messages) {
		if (GUIUtil.isEventDispatchThread()) {
			String text = Strings.trim(messages);
			txtMessages.setText(text);
			final int position = text.lastIndexOf('\n')+1;
			if (position<=0) {
				txtMessages.setCaretPosition(0);
			} else if (position>=text.length()) {
				txtMessages.setCaretPosition(text.length());
			} else {
				txtMessages.setCaretPosition(position);
			}
			txtMessages.repaint();
	 	} else {
	 		GUIUtil.invokeLater(new Runnable() {
	 			@Override
	 			public void run() {
	 				setMessages(messages);
	 			}
	 		});
	 	}
	}

}

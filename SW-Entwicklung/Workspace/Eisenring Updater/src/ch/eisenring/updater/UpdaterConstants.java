package ch.eisenring.updater;

import ch.eisenring.core.datatypes.date.TimestampUtil;

public interface UpdaterConstants {

	// DONT MODFIY THE VERSION LINE, EXCEPT THE STRING CONTENTS!
	// BUILD SCRIPTS NEED THE LINE TO BE EXACTLY AS IT IS!!!
	// THE VERSION STRING MUST HAVE THREE DOT-SEPARATED PARTS AND
	// MUST CONTAIN ONLY DIGITS (it is used as .exe and installer
	// version by the build)
	String VERSION = "1.0.7";

	long VERSION_TIMESTAMP = TimestampUtil.toTimestamp("2013-11-18");

}

package ch.eisenring.tapi.client;

import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.gui.action.AbstractAction;
import ch.eisenring.tapi.client.resources.images.Images;

public class ActionPhoneDial extends AbstractAction {

	private String phoneNumber;
	
	public ActionPhoneDial(final String labelText,
			               final String phoneNumber) {
		super(Images.PHONE, Strings.concat(labelText, " ", TAPIStaticAPI.formatPhoneNumber(phoneNumber)));
		setPhoneNumber(phoneNumber);
	}

	@Override
	protected void performAction() {
		final String phoneNumber = getPhoneNumber();
		TAPIStaticAPI.dialNumber(phoneNumber);
	}

	@Override
	protected boolean isEnabledImpl() {
		final String phoneNumber = Strings.clean(getPhoneNumber());
		return TAPIStaticAPI.isAvailable() && TAPIStaticAPI.isValidPhoneNumber(phoneNumber);
	}
	
	protected String getPhoneNumber() {
		return phoneNumber;
	}
	
	public void setPhoneNumber(String phoneNumber) {
		this.phoneNumber = Strings.clean(phoneNumber);
	}

}

package ch.eisenring.tapi.client.gui;

import java.awt.event.FocusEvent;
import java.awt.event.FocusListener;
import java.awt.event.KeyAdapter;
import java.awt.event.KeyEvent;
import java.awt.event.KeyListener;

import ch.eisenring.core.datatypes.primitives.ConversionUtil;
import ch.eisenring.gui.ValueEditorBinding;
import ch.eisenring.gui.components.CompoundComponent;
import ch.eisenring.gui.components.HEAGTextField;
import ch.eisenring.gui.interfaces.ValueEditor;

@SuppressWarnings("serial")
public final class PhoneNumberField extends CompoundComponent implements ValueEditor {

	private final FocusListener focusListener = new FocusListener() {
		@Override
		public void focusGained(FocusEvent e) {
			btnDial.updateEnabled();
		}
		@Override
		public void focusLost(FocusEvent e) {
			btnDial.updateEnabled();
		}
	};

	private final KeyListener keyListener = new KeyAdapter() {
		@Override
		public void keyTyped(final KeyEvent event) {
		}
		@Override
		public void keyReleased(final KeyEvent event) {
			btnDial.updateEnabled();
		}
		@Override
		public void keyPressed(final KeyEvent event) {
		}
	};

	private final HEAGTextField txtPhone;
	private final PhoneDialButton btnDial;
	private ValueEditorBinding binding;
	
	public PhoneNumberField(final ValueEditorBinding binding) {
		this.binding = binding;
		this.txtPhone = new HEAGTextField(binding.getLength());
		this.btnDial = new PhoneDialButton(txtPhone);
		txtPhone.addFocusListener(focusListener);
		txtPhone.addKeyListener(keyListener);
		addField(txtPhone, 1);
		addButton(btnDial);
	}

	@Deprecated
	public PhoneNumberField(int limit) {
		txtPhone = new HEAGTextField(limit);
		btnDial = new PhoneDialButton(txtPhone);
		txtPhone.addFocusListener(focusListener);
		addField(txtPhone, 1);
		addButton(btnDial);
	}

	public String getText() {
		return ConversionUtil.convert(txtPhone.getValue(), (String) null);
	}

	public void setText(final String text) {
		txtPhone.setValue(text);
		btnDial.updateEnabled();
	}

	// --------------------------------------------------------------
	// ---
	// --- ModelView implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public void updateView(final Object model) {
		if (binding != null) {
			binding.updateView(txtPhone, model);
			btnDial.updateEnabled();
		}
	}

	@Override
	public void updateModel(final Object model) {
		if (binding != null) {
			binding.updateModel(txtPhone, model);
			btnDial.updateEnabled();
		}
	}
	
	// --------------------------------------------------------------
	// ---
	// --- ValueEditor implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public ValueEditorBinding getBinding() {
		return null;
	}

	/**
	 * Gets the value from this editor component
	 */
	public Object getValue() {
		return txtPhone.getValue();
	}
	
	/**
	 * Sets the value of this editor component
	 */
	public void setValue(final Object value) {
		txtPhone.setValue(value);
	}

	/**
	 * Gets the class of the value expected/returned by this editor
	 */
	public Class<?> getValueClass() {
		return txtPhone.getValueClass();
	}
	
	/**
	 * Returns true if the editor is disabled,
	 * not editable or otherwise view only.
	 */
	public boolean isViewOnly() {
		return txtPhone.isViewOnly();
	}

}

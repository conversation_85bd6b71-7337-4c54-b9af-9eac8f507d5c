package ch.eisenring.tapi.client;

import java.io.File;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;

import javax.telephony.Address;
import javax.telephony.JtapiPeer;
import javax.telephony.JtapiPeerFactory;
import javax.telephony.Provider;
import javax.telephony.Terminal;
import javax.telephony.callcontrol.CallControlCall;

import ch.eisenring.app.client.fileassoc.ProtocolURL;
import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.codetable.MessageClassCode;
import ch.eisenring.core.datatypes.strings.Msg;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.core.logging.Logger.LogLevel;
import ch.eisenring.core.platform.Platform;
import ch.eisenring.core.platform.windows.WinRegistry;
import ch.eisenring.core.platform.windows.WinRegistryCompat;
import ch.eisenring.core.threading.ThreadCore;
import ch.eisenring.gui.window.FeedbackHandler;

public final class TAPIStaticAPI {

	private final static int STACKSIZE_KB = 1024;

	private final static Object LOCK = new Object();
	private final static long INIT_TIMEOUT = 5000L;
	private final static String IGNORE = " \t\r\n\u00A0/-+";
	private final static String TAPIPEER_CLASS = "net.sourceforge.gjtapi.GenericJtapiPeer";
	private final static String PROVIDER_CLASS = "net.sourceforge.gjtapi.raw.tapi3.Tapi3Provider";

	private final static AtomicBoolean INITIALIZED = new AtomicBoolean(false);
	private final static AtomicBoolean AVAILABLE = new AtomicBoolean(false);
	
	private static JtapiPeer tapiPeer;
	private static Provider provider;
	private static Address address;
	
	private final static AtomicReference<TAPIServiceClient> CLIENT = new AtomicReference<TAPIServiceClient>();
	
	/**
	 * Returns true if the telephony service is available
	 */
	public static boolean isAvailable() {
		initialize();
		return AVAILABLE.get() || isFakeMode();
	}

	/**
	 * Calls the given phone number
	 */
	public static void dialNumber(final String rawNumber) {
		if (isFakeMode()) {
			// fake success (don't call anything
			getFeedbackHandler().message(new ErrorMessage(MessageClassCode.INFO,
					Strings.concat("Phone Call faked: ", rawNumber)));
		}
		initialize();
		
		final String phoneNumber = preProcessNumber(rawNumber, false);
		if (null!=phoneNumber) {
			final Runnable r = new Runnable() {
				public void run() {
					getFeedbackHandler().message(new ErrorMessage(MessageClassCode.INFO,
							Strings.concat("Telefonnummer ", formatPhoneNumber(rawNumber), " wird angerufen...")));
					if (isDebugMode()) {
						// just log the number
						Logger.log(LogLevel.INFO, Strings.concat("TAPI debug mode enabled, called number = ", phoneNumber));
					} else {
						if (isCallByUrl()) {
							// call the number the new way
							Logger.info("TAPI call URL to dial = {}", phoneNumber);
							final String url = Strings.concat("HEAGTapi://dial?", phoneNumber);
							final TAPIServiceClient client = getClient();
							if (client != null) {
								ProtocolURL.openURL(client, url);
							} else {
								ProtocolURL.openUrlWithoutFeedback(url);
							}
						} else {
							// call the number the old way
							try {
								Logger.info("TAPI dial number = {}", phoneNumber);
								final CallControlCall callControl = (CallControlCall) provider.createCall();
								final Terminal[] terminals = address.getTerminals();
								callControl.connect(terminals[0], address, phoneNumber);

							} catch (final Exception e) {
								Logger.error("TAPI dial number = {} failed:", phoneNumber);
								Logger.error(e);
							}
						}
					}
					getFeedbackHandler().message(new ErrorMessage(MessageClassCode.INFO,
							Strings.concat("Anruf ", formatPhoneNumber(rawNumber), " beendet")));
				}
			};
			final Thread t = ThreadCore.create(r, "Telephony", STACKSIZE_KB);
			t.setDaemon(true);
			t.start();
		} else {
			if (Logger.isWarnEnabled())
				Logger.warn("argument \"{}\" contains no valid phone number", rawNumber);
			getFeedbackHandler().warn(Msg.mk("\"{}\" ist keine Telefonnummer", rawNumber));
		}
	}

	/**
	 * Clean phone number string of garbage.
	 */
	protected static String cleanPhoneNumber(CharSequence rawNumber) {
		if (Strings.isEmpty(rawNumber))
			return null;
		final StringMaker b = StringMaker.obtain(32);

		// remove all white spaces and separators from the string
		for (int i=0; i<rawNumber.length(); ++i) {
			char c = rawNumber.charAt(i);
			if (IGNORE.indexOf(c) < 0) {
				b.append(c);
			}
		}
		rawNumber = b.toString();
		b.setLength(0);

		// now filter the string 
		char prev = 0;
		char curr = 0;
		char next = 0;
		for (int i=0; i<=rawNumber.length(); ++i) {
			prev = curr;
			curr = next;
			next = filterDigits(i>=0 && i<rawNumber.length() ? rawNumber.charAt(i) : 0);
			if (0!=curr && (0!=prev || 0!=next)) {
				b.append(curr);
			}
		}
		return b.release();
	}

	private static char filterDigits(final char c) {
		if (Strings.isASCIIDigit(c))
			return c;
		return 0;
	}

	/**
	 * Detects if the number is an internal or external call,
	 * prepends external call prefix, or cuts down to internal
	 * extension number as needed.
	 */
	protected static String preProcessNumber(final CharSequence rawNumber, boolean suppressPrefix) {
		if (Strings.isEmpty(rawNumber))
			return null;
		String number = cleanPhoneNumber(rawNumber);
		if (number == null)
			return null;
		if (number.length()<6 || number.charAt(0)!='0') {
			// to few digits, or doesn't start with a 0.
			number = null;
		} else if (number.startsWith(getIntCallPrefix())) {
			// it is an internal call
			number = Strings.subString(number, getIntCallPrefix().length());
		} else if (!suppressPrefix){
			// it must be an external call
			number = getExtCallPrefix()+number;
		}
//		if (Logger.isEnabled(LogLevel.TRACE)) {
//			Logger.trace(Strings.concat("cleaning phone number \"", rawNumber, "\" -> \"", number, "\""));
//		}
		return number;
	}

	/**
	 * Detects if the given parameter contains a valid phone number
	 */
	public static boolean isValidPhoneNumber(final CharSequence phoneNumber) {
		return preProcessNumber(phoneNumber, true) != null;
	}

	/**
	 * Format phone number
	 */
	public static String formatPhoneNumber(final CharSequence phoneNumber) {
		String number = preProcessNumber(phoneNumber, true);
		if (null==number) {
			number = "(Keine Nummer hinterlegt)";
		} else if (number.length()<4) {
			number = Strings.concat(number, " (Intern)");
		} else {
			final int[] groups = { 3, 3, 2 };
			if (number.startsWith("00")) {
				groups[0] = 4; 
				groups[1] = 2;
			}
			int groupIndex = 0;
			int groupLength = 0;
			final StringMaker b = StringMaker.obtain(32);
			for (int i=0; i<number.length(); ++i) {
				b.append(number.charAt(i));
				++groupLength;
				if (groupLength>=groups[groupIndex]) {
					groupLength = 0;
					b.append(' ');
					++groupIndex;
					if (groupIndex>=groups.length)
						groupIndex = groups.length-1;
				}
			}
			number = b.release();
		}
		return number;
	}

	@SuppressWarnings("deprecation")
	static void initialize() {
		if (INITIALIZED.get() || isFakeMode())
			return;
		if (isCallByUrl()) {
			AVAILABLE.set(Boolean.TRUE);
			INITIALIZED.set(Boolean.TRUE);
			return;
		}
		synchronized (getLock()) {
			// check again...
			if (INITIALIZED.get())
				return;
			// start initializer thread
			final Runnable r = new Runnable() {
				@Override
				public void run() {
					try {
						do {
							// Check for interruption before starting
							if (Thread.currentThread().isInterrupted()) {
								Logger.log(LogLevel.INFO, "TAPI initializer thread was interrupted before starting");
								break;
							}

							// don't attempt to load telephony, if no address is configured
							final TAPIServiceClient client = getClient();
							if (client == null)
								break;
							final String addressName = client.TAPI_ADDRESS.get();
							if (Strings.isEmpty(addressName))
								break;

							// Check for interruption before loading GJTAPI
							if (Thread.currentThread().isInterrupted()) {
								Logger.log(LogLevel.INFO, "TAPI initializer thread was interrupted before loading GJTAPI");
								break;
							}

							// load the gjtapi framework
							try {
								tapiPeer = JtapiPeerFactory.getJtapiPeer(TAPIPEER_CLASS);
							} catch (Exception e) {
								Logger.log(LogLevel.ERROR, "failed to load tapi peer: "+TAPIPEER_CLASS);
								Logger.log(LogLevel.ERROR, e);
								break;
							}

							// Check for interruption before loading TAPI driver
							if (Thread.currentThread().isInterrupted()) {
								Logger.log(LogLevel.INFO, "TAPI initializer thread was interrupted before loading TAPI driver");
								break;
							}

							// load the TAPI driver
							try {
								provider = tapiPeer.getProvider(PROVIDER_CLASS);
							} catch (Exception e) {
								Logger.log(LogLevel.ERROR, "failed to load tapi provider: "+PROVIDER_CLASS);
								Logger.log(LogLevel.ERROR, e);
								break;
							}

							// Check for interruption before getting address
							if (Thread.currentThread().isInterrupted()) {
								Logger.log(LogLevel.INFO, "TAPI initializer thread was interrupted before getting address");
								break;
							}

							// get the desired address (phone)
							try {
								address = provider.getAddress(addressName);
							} catch (Exception e) {
								address = null;
								Logger.log(LogLevel.ERROR, e);
							}
							if (address == null)
								Logger.log(LogLevel.ERROR, "TAPI address \""+addressName+"\" not found");

							AVAILABLE.set(address != null);
						} while (false);
					} catch (Exception e) {
						// Handle any unexpected exceptions
						Logger.log(LogLevel.ERROR, "TAPI initializer thread encountered unexpected error: " + e.getMessage());
						Logger.log(LogLevel.ERROR, e);
						AVAILABLE.set(false);
					} finally {
						// Always set initialized flag, even if interrupted
						INITIALIZED.set(true);
					}
				}
			};
			final Thread t = ThreadCore.create(r, "TAPI Service initializer", STACKSIZE_KB);
			t.setDaemon(true);
			t.setPriority(Thread.MIN_PRIORITY);
			t.start();
			// wait for initializer thread to finish or timeout
			try {
				t.join(INIT_TIMEOUT);
			} catch (InterruptedException e) {
				Thread.currentThread().interrupt();
				// not that we care
			}
			INITIALIZED.set(true);
			if (t.isAlive()) {
				// if initializer thread is still working, terminate it gracefully
				Logger.log(LogLevel.ERROR, "TAPI Service initialization exceeded time limit, terminating initializer");
				getFeedbackHandler().error("Die Telefonie konnte nicht initialisiert werden. Telefon-Unterstützung deaktiviert.");

				// First attempt: polite interruption
				t.interrupt();
				ThreadCore.sleep(100);

				// Second attempt: more insistent interruption
				if (t.isAlive()) {
					t.interrupt();
					ThreadCore.sleep(1000);
				}

				// Final attempt: extended wait for graceful shutdown
				if (t.isAlive()) {
					Logger.log(LogLevel.WARN, "TAPI initializer thread did not respond to interruption, waiting longer...");
					ThreadCore.sleep(5000); // Give more time for cleanup
				}

				// Java 21 compatible: Cannot force-kill threads
				if (t.isAlive()) {
					Logger.log(LogLevel.ERROR, "TAPI initializer thread is still running - it will continue in background");
					Logger.log(LogLevel.ERROR, "This may indicate a thread that doesn't properly handle interruption");
				}

				AVAILABLE.set(false);
			}
			
		}
	}

	public static Object getLock() {
		return LOCK;
	}

	public static boolean isDebugMode() {
		final TAPIServiceClient client = getClient();
		if (client == null)
			return false;
		final Object value = client.TAPI_DEBUG.get(); 
		return Boolean.TRUE.equals(value);
	}

	public static boolean isFakeMode() {
		final TAPIServiceClient client = getClient();
		if (client == null)
			return false;
		final Object value = client.TAPI_FAKEMODE.get(); 
		return Boolean.TRUE.equals(value);
	}

	public static String getIntCallPrefix() {
		final TAPIServiceClient client = getClient();
		if (client == null)
			return "";
		final String value = client.TAPI_INTCALLPREFIX.get();
		return Strings.trim(value);
	}
	
	public static String getExtCallPrefix() {
		final TAPIServiceClient client = getClient();
		if (client == null)
			return "";
		final String value = client.TAPI_EXTCALLPREFIX.get();
		return Strings.trim(value);
	}

	public static Boolean isCallByUrl() {
		final String path = "Software\\Classes\\HEAGTapi";
		String urlProtocolHLM = null;
		String urlProtocolHCU = null;

		try {
			urlProtocolHCU = WinRegistryCompat.readString(WinRegistry.HKEY_CURRENT_USER, path, "URL Protocol");
			urlProtocolHLM = WinRegistryCompat.readString(WinRegistry.HKEY_LOCAL_MACHINE, path, "URL Protocol");
		} catch (Exception e) {
			Logger.error(e);
		}
		return (urlProtocolHLM != null) || (urlProtocolHCU != null);
	}

	/**
	 * Gets the FeedbackHandler used for output
	 */
	public static FeedbackHandler getFeedbackHandler() {
		final TAPIServiceClient client = CLIENT.get();
		if (client != null) {
			FeedbackHandler h = client.getFeedbackHandler();
			if (h != null)
				return h;
		}
		return FeedbackHandler.NULL;
	}

	/**
	 * Gets the TAPI client instance. May return null.
	 */
	public static TAPIServiceClient getClient() {
		return CLIENT.get(); 
	}

	/**
	 * Sets the TAPI client instance.
	 */
	static void setClient(final TAPIServiceClient client) {
		CLIENT.set(client);
	}

}


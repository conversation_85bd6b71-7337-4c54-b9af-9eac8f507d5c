package heag;

import java.util.List;

final class CmdRegister {

    static void doCmd(final List<String> args, final Dialer dialer) {
        final boolean localMachine;
        switch (args.size()) {
            case 1:
                localMachine = false;
                break;
            case 2:
                final String arg2 = args.get(1).toUpperCase();
                switch (arg2) {
                    case Dialer.PARAM_CURRENTUSER:
                        localMachine = false;
                        break;
                    case Dialer.PARAM_LOCALMACHINE:
                        localMachine = true;
                        break;
                    default:
                        dialer.logger.error("Error: invalid parameter " + args.get(1));
                        dialer.terminate(10);
                        throw new IllegalStateException();
                }
                break;
            default:
                dialer.logger.error("too many arguments");
                dialer.terminate(10);
                throw new IllegalStateException();
        }
        final Configuration c = dialer.getConfiguration();
        try {
            final WindowsRegistration r = new WindowsRegistration();
            r.configureRegistry(c, localMachine);
            String who = localMachine ? "local machine" : "current user";
            dialer.logger.info("Dialer registered for protocol: " + c.getProtocolName() + " (" + who + ")");
            dialer.terminate(0);
        } catch (final Exception e) {
            dialer.logger.error("Error:");
            dialer.logger.error(e);
            dialer.terminate(20);
        }
    }

}

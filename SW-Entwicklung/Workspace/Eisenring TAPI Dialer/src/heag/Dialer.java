package heag;

import heag.log.Logger;

import java.util.List;

public class Dialer {

    final static String CMD_USAGE = "?";
    final static String CMD_DIAL = "DIAL";
    final static String CMD_REGISTER = "REGISTER";
    final static String PARAM_LOCALMACHINE = "LOCALMACHINE";
    final static String PARAM_CURRENTUSER = "CURRENTUSER";

    final List<String> args;
    final Logger logger;

    private Dialer(final List<String> args, final Logger logger) {
        this.logger = logger;
        this.args = args;
    }


    public static void start(final String[] argv, final Logger logger) {
        final Dialer dialer = new Dialer(Args.prepareArgs(argv), logger);
        try {
            dialer.dispatchCommand();
            dialer.terminate(0);
        } catch (final Throwable e) {
            logger.error("Error:");
            logger.error(e);
            dialer.terminate(20);
        }
    }

    // --------------------------------------------------------------
    // ---
    // --- Configuration handling
    // ---
    // --------------------------------------------------------------
    private Configuration configuration;

    public Configuration getConfiguration() {
        Configuration configuration = this.configuration;
        if (configuration == null) {
            configuration = new Configuration(logger);
            this.configuration = configuration;
        }
        return configuration;
    }

    void dispatchCommand() {
        final String arg1 = args.isEmpty() ? CMD_USAGE : args.get(0).toUpperCase();
        if (CMD_USAGE.equals(arg1)) {
            CmdUsage.doCmd(args, this);
            terminate(0);
        } else if (CMD_REGISTER.equals(arg1)) {
            CmdRegister.doCmd(args, this);
        } else if (CMD_DIAL.equals(arg1)) {
            CmdDial.doCmd(args, this);
        } else {
            logger.error("Unsupported argument: '" + args.get(0) + "'");
            terminate(10);
        }
    }

    public void terminate(final int exitCode) {
        logger.lastAction();
        System.exit(exitCode);
    }

}


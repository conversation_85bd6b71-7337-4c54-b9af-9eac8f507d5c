package heag;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

class Args {

    /**
     * Process command line args intp canonic form.
     * This will decompose URL arguments into simple arguments
     */
    static List<String> prepareArgs(final String[] args) {
        final List<String> result = new ArrayList<>();
        for (final String arg : args) {
            decodeURL(arg, result);
        }
        return result;
    }

    static void decodeURL(final String url, final Collection<String> target) throws IllegalArgumentException {
        if (url == null)
            throw new IllegalArgumentException("url must not be null");
        if (url.length() <= 0)
            return;
        int i = url.indexOf("://");
        if (i < 0) {
            target.add(url);
            return;
        }
        i += 3;
        final StringBuffer b = new StringBuffer();
        boolean inQuery = false;
        while (i < url.length()) {
            final char c = url.charAt(i++);
            switch (c) {
                case '/':
                    if (inQuery) {
                        b.append(c);
                    } else {
                        if (b.length() > 0) {
                            target.add(percentDecode(b));
                            b.setLength(0);
                        }
                    }
                    break;
                case '?':
                    if (inQuery) {
                        b.append(c);
                    } else {
                        if (b.length() > 0) {
                            target.add(percentDecode(b));
                            b.setLength(0);
                        }
                        inQuery = true;
                    }
                    break;
                case ';': {
                    if (inQuery) {
                        if (b.length() > 0) {
                            target.add(percentDecode(b));
                            b.setLength(0);
                        }
                    } else {
                        b.append(c);
                    }
                    break;
                }
                default:
                    b.append(c);
                    break;
            }
        }
        if (b.length() > 0)
            target.add(percentDecode(b));
    }

    static String percentDecode(final CharSequence s) {
        if (s == null || s.length() <= 0)
            return "";
        final StringBuffer b = new StringBuffer(s.length());
        int state = 0;
        int value = 0;
        for (int i=0; i<s.length(); ++i) {
            final char c = s.charAt(i);
            b.append(c);
            if (c == '%') {
                state = 1;
                value = 0;
            } else if (state == 1) {
                final int h = decodeHex(c);
                if (h < 0) {
                    state = 0;
                } else {
                    state = 2;
                    value = h;
                }
            } else if (state == 2) {
                final int h = decodeHex(c);
                if (h >= 0) {
                    value = (value << 4) | h;
                    b.setLength(b.length() - 3);
                    b.append((char) value);
                }
                state = 0;
            }
        }
        return b.toString();
    }

    static int decodeHex(final char c) {
        if (c < '0')
            return -1;
        if (c <= '9')
            return c - '0';
        if (c < 'A')
            return -1;
        if (c <= 'F')
            return c - ('A' + 10);
        if (c < 'a')
            return -1;
        if (c <= 'f')
            return c - ('a' + 10);
        return -1;
    }

}

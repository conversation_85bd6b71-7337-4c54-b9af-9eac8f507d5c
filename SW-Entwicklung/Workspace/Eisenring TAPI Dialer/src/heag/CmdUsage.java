package heag;

import java.util.List;

final class CmdUsage {

    static void doCmd(final List<String> args, final Dialer dialer) {
        final String fileName = dialer.getConfiguration().getExeNameCLI();
        dialer.logger.info("Usage:");
        dialer.logger.info(fileName + " " + Dialer.CMD_DIAL + " <number>");
        dialer.logger.info(fileName + " " + Dialer.CMD_REGISTER + " [" + Dialer.PARAM_CURRENTUSER + "|" + Dialer.PARAM_LOCALMACHINE + "]");
    }

}

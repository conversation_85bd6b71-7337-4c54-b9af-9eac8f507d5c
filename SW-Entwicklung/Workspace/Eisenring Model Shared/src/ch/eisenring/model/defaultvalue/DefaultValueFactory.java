package ch.eisenring.model.defaultvalue;
import static ch.eisenring.model.defaultvalue.DefaultValue.FALSE;
import static ch.eisenring.model.defaultvalue.DefaultValue.NAN_DOUBLE;
import static ch.eisenring.model.defaultvalue.DefaultValue.NAN_FLOAT;
import static ch.eisenring.model.defaultvalue.DefaultValue.NULL;
import static ch.eisenring.model.defaultvalue.DefaultValue.TRUE;
import static ch.eisenring.model.defaultvalue.DefaultValue.ZERO_BYTE;
import static ch.eisenring.model.defaultvalue.DefaultValue.ZERO_DOUBLE;
import static ch.eisenring.model.defaultvalue.DefaultValue.ZERO_FLOAT;
import static ch.eisenring.model.defaultvalue.DefaultValue.ZERO_INT;
import static ch.eisenring.model.defaultvalue.DefaultValue.ZERO_LONG;
import static ch.eisenring.model.defaultvalue.DefaultValue.ZERO_SHORT;

import java.util.Date;

import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.datatypes.date.DateUtil;
import ch.eisenring.core.datatypes.primitives.Primitives;
import ch.eisenring.model.Datatype;

public abstract class DefaultValueFactory {

	//---------------------------------------------------------------
	//---
	//--- Factory methods for default values of various types
	//---
	//---------------------------------------------------------------
	/**
	 * Creates an boolean default value
	 */
	public static DefaultValue create(final boolean defaultValue) {
		return defaultValue ? TRUE : FALSE;
	}

	/**
	 * Creates a byte default value
	 */
	public static DefaultValue create(final byte defaultValue) {
		return defaultValue == 0 ? ZERO_BYTE : new GenericDefaultValue<Byte>(Byte.valueOf(defaultValue), byte.class);
	}

	/**
	 * Creates a short default value
	 */
	public static DefaultValue create(final short defaultValue) {
		return defaultValue == 0 ? ZERO_SHORT : new GenericDefaultValue<Short>(Short.valueOf(defaultValue), short.class);
	}

	/**
	 * Creates an integer default value
	 */
	public static DefaultValue create(final int defaultValue) {
		return defaultValue == 0 ? ZERO_INT : new GenericDefaultValue<Integer>(Integer.valueOf(defaultValue), int.class);
	}

	/**
	 * Creates a long default value
	 */
	public static DefaultValue create(final long defaultValue) {
		return defaultValue == 0L ? ZERO_LONG : new GenericDefaultValue<Long>(Long.valueOf(defaultValue), long.class);
	}

	/**
	 * Creates a float default value
	 */
	public static DefaultValue create(final float defaultValue) {
		if (Float.isNaN(defaultValue)) {
			return NAN_FLOAT;
		} else if (defaultValue == 0F) {
			return ZERO_FLOAT;
		}
		return new GenericDefaultValue<Float>(Primitives.valueOf(defaultValue), float.class);
	}

	/**
	 * Creates a double default value
	 */
	public static DefaultValue create(final double defaultValue) {
		if (Double.isNaN(defaultValue)) {
			return NAN_DOUBLE;
		} else if (defaultValue == 0D) {
			return ZERO_DOUBLE;
		} 
		return new GenericDefaultValue<Double>(Primitives.valueOf(defaultValue), double.class);
	}

	/**
	 * Creates a string default value
	 */
	public static DefaultValue create(final String defaultValue) {
		return defaultValue == null || defaultValue.length() <= 0
			? NULL : new GenericDefaultValue<String>(defaultValue, String.class);
	}

	/**
	 * Creates a binary default value
	 */
	public static DefaultValue create(final byte[] defaultValue) {
		if (defaultValue == null)
			return NULL;
		return new GenericDefaultValue<byte[]>(defaultValue, byte[].class);
	}
	
	/**
	 * Creates a default value from object. The type is automatically
	 * determined by the object's class.
	 */
	public static DefaultValue create(final Object defaultValue) {
		if (defaultValue == null) {
			return NULL;
		} else if (defaultValue instanceof DefaultValue) {
			return (DefaultValue) defaultValue;
		} else if (defaultValue instanceof Byte) {
			return create(((Byte) defaultValue).byteValue());
		} else if (defaultValue instanceof Short) {
			return create(((Short) defaultValue).shortValue());
		} else if (defaultValue instanceof Integer) {
			return create(((Integer) defaultValue).intValue());
		} else if (defaultValue instanceof Long) {
			return create(((Long) defaultValue).longValue());
		} else if (defaultValue instanceof Float) {
			return create(((Float) defaultValue).floatValue());
		} else if (defaultValue instanceof Double) {
			return create(((Double) defaultValue).doubleValue());
		} else if (defaultValue instanceof String) {
			return create(defaultValue.toString());
		} else if (defaultValue instanceof byte[]) {
			return create((byte[]) defaultValue);
		} else if (defaultValue instanceof Date) {
			return new GenericDefaultValue<Date>(DateUtil.copy((Date) defaultValue), Date.class);
		}
		return new GenericDefaultValue<Object>(defaultValue, defaultValue.getClass());
	}

	public static DefaultValue create(final Class<? extends AbstractCode> codeClass) {
		return new DefaultValue() {
			@Override
			public Object getValue(final Datatype datatype) {
				return AbstractCode.getNull(codeClass);
			}
			@Override
			public boolean isAssignableTo(final Class<?> valueClass) {
				return codeClass.equals(valueClass);
			}
		};
	}

}

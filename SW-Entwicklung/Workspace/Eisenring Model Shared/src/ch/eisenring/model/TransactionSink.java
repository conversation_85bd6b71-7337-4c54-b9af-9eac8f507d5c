package ch.eisenring.model;

import java.io.IOException;
import java.util.Iterator;

/**
 * Interface to be implemented by model sink/source engines
 */
public interface TransactionSink {

	/**
	 * Writes all models to the sink.
	 * 
	 * The sink implementation decides how and which models
	 * it needs to process (it may process only touched models,
	 * a subset, or all models).
	 */
	void store(final Iterator<Model> modelItr,
			   final TransactionContext context) throws IOException;
	
}

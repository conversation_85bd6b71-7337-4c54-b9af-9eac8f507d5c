package ch.eisenring.model;

public interface AbstractProperty {

	/**
	 * Returns an identifier that identifies the type
	 * of this property.
	 * 
	 * Best practice is to use the class of the implementing
	 * class as property identified. If the implementation
	 * of the property is a class hierarchy, use the base class's
	 * class (or the common interface's class).
	 */	
	public abstract Class<?> getPropertyType();

}

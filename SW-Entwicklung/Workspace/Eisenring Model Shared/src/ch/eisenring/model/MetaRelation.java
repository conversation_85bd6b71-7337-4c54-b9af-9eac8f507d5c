package ch.eisenring.model;

import java.util.Iterator;

import ch.eisenring.core.collections.List;
import ch.eisenring.core.datatypes.primitives.Primitives;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.iterators.EmptyIterator;
import ch.eisenring.model.defaultvalue.DefaultValue;
import ch.eisenring.model.exceptions.ModelException;

public abstract class MetaRelation extends MetaClassMember {

	public final static Model[] EMPTY_MODEL_ARRAY = Primitives.getEmptyArray(Model.class);  
	
	@SuppressWarnings("unchecked")
	public final static Iterator<Model> EMPTY_ITERATOR = (Iterator<Model>) EmptyIterator.INSTANCE;

	/**
	 * The opposite end of the relation.
	 */
	private MetaRelation otherEnd;
	
	protected MetaRelation(final String name,
			               final DefaultValue defaultValue,
			               final Datatype datatype,
			               final MetaRelation otherEnd,
			               final MetaProperty ... properties) throws ModelException {
		super(name, defaultValue, datatype, properties);
		// This is a very, very, very evil hack to initialize
		// a circular dependency between two relation instances.
		//
		// Since relation ends are declared statically somewhere,
		// the one initialized first will not properly supply
		// the reference (see JLS on static circular dependencies),
		// to have this work regardless, the second one hacks the
		// correct reference into the other.
		if (otherEnd != null) {
			this.otherEnd = otherEnd;
			otherEnd.otherEnd = this;
		}
	}

	@Override
	public final Class<?> getPropertyType() {
		return MetaRelation.class;
	}

	/**
	 * Gets the opposite end of the relation
	 */
	public final MetaRelation getOtherEnd() throws ModelException {
		return otherEnd;
	}

	/**
	 * Gets the first object in the relation.
	 */
	public abstract Model getFirst(final Model model) throws ModelException;

	/**
	 * Gets an iterator of the relations contents
	 */
	public abstract Iterator<Model> iterator(final Model model) throws ModelException;
	
	/**
	 * Gets a list of the relations contents
	 */
	public abstract List<Model> list(final Model model) throws ModelException;
	
	/**
	 * Gets an array of the relations contents
	 */
	public abstract Model[] toArray(final Model model) throws ModelException;
	
	/**
	 * Checks if the relation is empty
	 */
	public final boolean isEmpty(final Model model) throws ModelException {
		return size(model) <= 0;
	}

	/**
	 * Gets the number of objects in this relation
	 */
	public abstract int size(final Model model) throws ModelException;

	/**
	 * Gets the other end of this relation
	 */
	public final MetaRelation otherEnd(final Model model) throws ModelException {
		return null;
	}
	
	/**
	 * Clears the contents of this relation.
	 */
	public abstract boolean clear(final Model model) throws ModelException;

	/**
	 * Adds the given model to this relation.
	 * For to-one relations, this is effectively a set operation. 
	 */
	public abstract boolean add(final Model model, final Model related) throws ModelException;
	
	/**
	 * Removes the given model from this relation.
	 */
	public abstract boolean remove(final Model model, final Model related) throws ModelException;

	/**
	 * Returns true if the relation on the given model
	 * contains the related object.
	 */
	public abstract boolean contains(final Model model, final Model related) throws ModelException;

	@Override
	public void toStringMaker(final StringMaker target) {
		final MetaRelation otherEnd = getOtherEnd();
		target.append(Primitives.getSimpleName(getClass()));
		target.append("[Role=");
		target.append(getMetaClass().getName());
		target.append('.');
		target.append(getName());
		target.append(";OtherEnd=");
		if (otherEnd == null) {
			target.append("null");
		} else {
			target.append(otherEnd.getMetaClass().getName());
			target.append('.');
			target.append(otherEnd.getName());
		}
		target.append(']');
	}
	
}

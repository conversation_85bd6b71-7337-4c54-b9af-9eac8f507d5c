package ch.eisenring.model.datatypes.lw;

import ch.eisenring.core.datatypes.date.TimestampUtil;

/**
 * Conversion utilities for the more odd date fields in Logiware
 */
public final class LWDateUtil {

	public final static long NULL_CANONIC = TimestampUtil.toTimestamp("1900-01-01");
	public final static long NULL_BOUNDARY = TimestampUtil.toTimestamp("1900-01-02");
	public final static java.sql.Date NULL_SQLDATE = new java.sql.Date(NULL_CANONIC);

	private LWDateUtil() {
	}

	public static long toTimestamp(final java.util.Date date) {
		if (date == null)
			return TimestampUtil.NULL_TIMESTAMP;
		final long timestamp = date.getTime();
		return timestamp < NULL_BOUNDARY ? TimestampUtil.NULL_TIMESTAMP : timestamp;
	}

//	public static java.util.Date toDate(final long timestamp) {
//		return TimestampUtil.toDate();
//	}

	public static <U extends java.util.Date> U toDate(final Long timestamp, final Class<U> dateClass) {
		final long t = (timestamp == null || TimestampUtil.isNull(timestamp)) ? NULL_CANONIC : timestamp;
		return TimestampUtil.toDate(t, dateClass);
	}

	public static <U extends java.util.Date> U toDate(final long timestamp, final Class<U> dateClass) {
		final long t = TimestampUtil.isNull(timestamp) ? NULL_CANONIC : timestamp;
		return TimestampUtil.toDate(t, dateClass);
	}
	
}

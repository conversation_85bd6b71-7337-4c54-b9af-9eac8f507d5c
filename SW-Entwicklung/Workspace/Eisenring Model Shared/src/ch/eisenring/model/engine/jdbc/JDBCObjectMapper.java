package ch.eisenring.model.engine.jdbc;

import java.sql.SQLException;
import java.util.Collections;
import java.util.Iterator;

import ch.eisenring.core.collections.Collection;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.datatypes.primitives.Primitives;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.jdbc.Column;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.core.logging.Logger.LogLevel;
import ch.eisenring.core.mapping.ToOneMapping;
import ch.eisenring.core.sort.Comparator;
import ch.eisenring.jdbc.JDBCBase;
import ch.eisenring.jdbc.JDBCConnectionMetaData;
import ch.eisenring.jdbc.JDBCResultSet;
import ch.eisenring.jdbc.StatementWrapper;
import ch.eisenring.model.AttributeAccessor;
import ch.eisenring.model.AttributeProcessor;
import ch.eisenring.model.MetaAttribute;
import ch.eisenring.model.MetaClassMember;
import ch.eisenring.model.ModelPrimaryKeyAccessor;
import ch.eisenring.model.TransactionContext;
import ch.eisenring.model.mapping.ModelMapping;
import ch.eisenring.model.mapping.ObjectMapping;
import ch.eisenring.model.util.ModelUtil;

/**
 * Utility class to work with objects mapped to a table.
 * 
 * Provides methods to load, insert and update such objects.
 */
public final class JDBCObjectMapper<T> extends JDBCObjectMapperFactory {

	private final static ColumnHandler COLUMNHANLDER_DUMMY = new ColumnHandler() {
		@Override
		public Object getColumn(JDBCResultSet resultSet, MetaClassMember member, int columnIndex) throws SQLException {
			return resultSet.getObject(columnIndex);
		}
		
		@Override
		public Object getColumn(JDBCResultSet resultSet, MetaClassMember member, String columnName) throws SQLException {
			return resultSet.getObject(columnName);
		}
	};

	protected final SingleTableMapping tableMapping;
	protected final ObjectMapping<T> objectMapping;
	
	/**
	 * Class used internally to bind columns from the table mapping
	 * to a field in the mapped object. 
	 */
	static class ColumnMapping {
		public final AttributeAccessor accessor;
		public final MetaAttribute attribute;
		public final ColumnHandler columnHandler;
		public final ColumnSpecifier column;

		ColumnMapping(final MetaAttribute attribute, 
				      final ColumnSpecifier column,
				      final AttributeAccessor accessor) {
			this.accessor = accessor;
			this.attribute = attribute;
			this.column = column;
			if (attribute != null) {
				this.columnHandler = ColumnHandler.get(attribute.getDatatype());
			} else {
				this.columnHandler = COLUMNHANLDER_DUMMY;
			}
		}
	
		public void setAttribute(final Object object, final Object value) {
			final Object valueForSet; 
			if (attribute == null) {
				valueForSet = value;
			} else {
				final AttributeProcessor processor = attribute.getSetterProcessor();
				valueForSet = (processor == null) ? value : processor.process(value);
			}
			accessor.setMember(object, valueForSet);
		}

		public Object getAttribute(final Object object) {
			final Object result = accessor.getMember(object);
			if (attribute == null)
				return result;
			final AttributeProcessor processor = attribute.getGetterProcessor();
			if (processor == null)
				return result;
			return processor.process(result);
		}
	}
	
	/**
	 * Primary key columns mapping (excludes non-key columns)
	 */
	protected final ColumnMapping[] pkMappings;

	/**
	 * Normal columns mapping (excludes primary key columns)
	 */
	protected final ColumnMapping[] columnMappings;

	/**
	 * All columns (key and normal columns all together)
	 */
	protected final ColumnMapping[] allMappings;
	
	JDBCObjectMapper(final SingleTableMapping tableMapping, final ObjectMapping<T> objectMapping) {
		this.tableMapping = tableMapping;
		this.objectMapping = objectMapping;
		this.pkMappings = buildColumnMapping(tableMapping, objectMapping, true);
		this.columnMappings = buildColumnMapping(tableMapping, objectMapping, false);
		this.allMappings = Primitives.join(pkMappings, columnMappings);
		this.insertSQL = buildInsertSQL(allMappings);
		this.updateSQL = buildUpdateSQL(pkMappings, columnMappings);
	}

	// --------------------------------------------------------------
	// ---
	// --- Mapping initialization
	// ---
	// --------------------------------------------------------------
	/**
	 * Builds an array that maps columns from the table to the object
	 */
	static <T> ColumnMapping[] buildColumnMapping(final TableMapping tableMapping,
			                                      final ObjectMapping<T> objectMapping,
			                                      final boolean primaryKeys) {
		final List<ColumnMapping> result = new ArrayList<ColumnMapping>();
		if (objectMapping instanceof ModelMapping && primaryKeys) {
			// model does not have an explicit primary key member.
			// thus the pkColumns must be emulated as if they were normal members.
			final ColumnSpecifier[] pkColumns = tableMapping.getPrimaryKeyColumns();
			final ColumnMapping columnMapping = new ColumnMapping((MetaAttribute) null, pkColumns[0],
					ModelPrimaryKeyAccessor.INSTANCE);
			result.add(columnMapping);
		} else {
			final Iterator<MetaAttribute> attrItr = tableMapping.getAttributeIterator();
			while (attrItr.hasNext()) {
				final MetaAttribute attribute = attrItr.next();
				final AttributeAccessor accessor = objectMapping.getAccessor(attribute);
				if (accessor == null) {
					// skip the column if not mapped in target object
					continue;
				}
				final ColumnSpecifier column = tableMapping.getColumn(attribute);
				// determine if it is a primary key column
				boolean isPKColumn = ModelUtil.getPrimaryKeyOrdinal(attribute) != null;
				if (isPKColumn == primaryKeys) {
					final ColumnMapping columnMapping = new ColumnMapping(attribute, column, accessor);
					result.add(columnMapping);
				}
			}
			if (primaryKeys) {
				Collections.sort(result, new Comparator<ColumnMapping>() {
					@Override
					public int compare(final ColumnMapping o1, final ColumnMapping o2) {
						final Integer i1 = ModelUtil.getPrimaryKeyOrdinal(o1.attribute);
						final Integer i2 = ModelUtil.getPrimaryKeyOrdinal(o2.attribute);
						return compareSigned(i1,  i2);
					}
				});
			}
		}
		return Collection.toArray(result, ColumnMapping.class);
	}
	
	// --------------------------------------------------------------
	// ---
	// --- Simple Getters
	// ---
	// --------------------------------------------------------------
	public SingleTableMapping getTableMapping() {
		return tableMapping;
	}

	public ObjectMapping<T> getObjectmapping() {
		return objectMapping;
	}

	// --------------------------------------------------------------
	// ---
	// --- Object select methods
	// ---
	// --------------------------------------------------------------
	public final void appendSelectSQL(final StringMaker target, final JDBCConnectionMetaData metaData) throws SQLException {
		tableMapping.appendSelectSQL(target, metaData, TableMapping.FULL_TABLE_ALIAS);
	}

	public final void appendSelectSQL(final StringMaker target, final JDBCConnectionMetaData metaData, final String tableAlias) throws SQLException { 
		tableMapping.appendSelectSQL(target, metaData, tableAlias);
	}
	
	// --------------------------------------------------------------
	// ---
	// --- Object loading methods
	// ---
	// --------------------------------------------------------------
//	/**
//	 * Fills object attributes from ResultSet columns.
//	 */
//	public final void loadRowColumns(final T object, final JDBCResultSet resultSet) throws SQLException {
//		try {
//			for (final ColumnMapping column : allMappings) {
//				final Object value = column.columnHandler.getColumn(resultSet, column.attribute, column.columnName);
//				column.accessor.setMember(object, value);
//			}
//		} catch (final SQLException e) {
//			throw e;
//		} catch (final RuntimeException e) {
//			throw new SQLException(e.getMessage(), e);
//		}
//	}

	/**
	 * Loads data from result set into existing object
	 */
	public void loadRow(final JDBCResultSet resultSet, final Object object) throws SQLException {
		loadRow(resultSet, object, (ToOneMapping<ColumnSpecifier, ColumnAlias>) null);
	}

	/**
	 * Loads data from result set into existing object
	 */
	public void loadRow(final JDBCResultSet resultSet, final Object object,
			final ToOneMapping<ColumnSpecifier, ColumnAlias> columnAliaser) throws SQLException {
		try {
			if (columnAliaser == null) {
				for (final ColumnMapping column : columnMappings) {
					final Object value = column.columnHandler.getColumn(resultSet, column.attribute, column.column.getColumnName());
					column.setAttribute(object, value);
				}
			} else {
				for (final ColumnMapping column : columnMappings) {
					final Column columnAlias = columnAliaser.get(column.column);
					final Object value = column.columnHandler.getColumn(resultSet, column.attribute, columnAlias.toString());
					column.setAttribute(object, value);
				}
			}
		} catch (final SQLException e) {
			throw e;
		} catch (final RuntimeException e) {
			throw new SQLException(e.getMessage(), e);
		}
	}	
	
	/**
	 * Creates and loads object from ResultSet
	 */
	public T loadRow(final TransactionContext context, final JDBCResultSet resultSet) throws SQLException {
		return loadRow(context, resultSet, false);
	}

	/**
	 * Creates and loads object from ResultSet
	 */
	public T loadRow(final TransactionContext context, final JDBCResultSet resultSet, final boolean ignoreMissingColumns) throws SQLException {
		try {
			// get primary key(s) from ResultSet
			final Object[] primaryKeyValues;
			if (pkMappings.length <= 0) {
				primaryKeyValues = Primitives.EMPTY_OBJECT_ARRAY;
			} else {
				primaryKeyValues = Primitives.newArray(Object.class, pkMappings.length);
				for (int i=0; i<pkMappings.length; ++i) {
					final ColumnMapping pkColumn = pkMappings[i];
					final Object value = pkColumn.columnHandler.getColumn(resultSet, pkColumn.attribute, pkColumn.column.getColumnName());
					if (value == null) {
						throw new SQLException(Strings.concat("primary key column ", pkColumn.column, " is null!"));
					}
					primaryKeyValues[i] = value;
				}
			}
			final T object = objectMapping.createObject(context, primaryKeyValues);
			for (final ColumnMapping column : columnMappings) {
				if (ignoreMissingColumns) {
					try {
						final Object value = column.columnHandler.getColumn(resultSet, column.attribute, column.column.getColumnName());
						column.setAttribute(object, value);
					} catch (final Exception e) {}
				} else {
					final Object value = column.columnHandler.getColumn(resultSet, column.attribute, column.column.getColumnName());
					column.setAttribute(object, value);
				}
			}
			return object;
		} catch (final SQLException e) {
			throw e;
		} catch (final RuntimeException e) {
			throw new SQLException(e.getMessage(), e);
		}
	}

	/**
	 * Loads objects from result set
	 */
	public final List<T> loadRows(final TransactionContext context, final JDBCResultSet resultSet) throws SQLException {
		return loadRows(context, resultSet, 64);
	}
	
	/**
	 * Loads objects from result set
	 */
	public final List<T> loadRows(final TransactionContext context, final JDBCResultSet resultSet,
			                      final int sizeHint) throws SQLException {
		final List<T> result = new ArrayList<T>(sizeHint);
		while (resultSet.next()) {
			final T object = loadRow(context, resultSet);
			result.add(object);
		}
		result.trimToSize();
		if (Logger.isEnabled(LogLevel.DEBUG)) {
			Logger.debug("loaded {} '{}' from result set", result.size(),
					Primitives.getSimpleName(objectMapping.getObjectClass()));
		}
		return result;
	}

	public final T loadFirst(final TransactionContext context, final JDBCResultSet resultSet,
						     final boolean throwIfNone, final boolean throwIfMultiple) throws SQLException {
		final List<T> list = loadRows(context, resultSet, 1);
		switch(list.size()) {
			case 0:
				if (throwIfNone)
					throw new SQLException("Leere Ergebnismenge");
				return null;
			case 1:
				return list.get(0);
			default:
				if (throwIfMultiple)
					throw new SQLException("Unerwartet viele Ergebnisse");
				return list.get(0);
		}
	}
	
	// --------------------------------------------------------------
	// ---
	// --- Object insertion
	// ---
	// --------------------------------------------------------------
	/**
	 * Pre-built part of insert statement. 
	 */
	private final String insertSQL;
	
	/**
	 * Prepares the more complicated part of the insert statement.
	 * This is done only once. 
	 */
	private static String buildInsertSQL(final ColumnMapping[] columnMappings) {
		final StringMaker sql = StringMaker.obtain(256);
		sql.append(" (");
		for (int i=0; i<columnMappings.length; ++i) {
			if (i > 0)
				sql.append(", ");
			sql.append(columnMappings[i].column);
		}
		sql.append(") VALUES (");
		for (int i=0; i<columnMappings.length; ++i) {
			sql.append(i > 0 ? ", ?" : "?");
		}
		sql.append(')');
		return sql.release();
	}

	/**
	 * Inserts a single object. If possible avoid calling this multiple times
	 * and use insert(Collection<Object>) to take advantage of batch inserts.
	 */
	public final void insert(final JDBCBase transaction, final T object) throws SQLException {
		insert(transaction, List.asList(object));
	}

	/**
	 * Batch insert of objects.
	 */
	public final void insert(final JDBCBase transaction, final Collection<T> objects) throws SQLException {
		if (objects == null || objects.isEmpty())
			return;
		// prepare insert statement
		final StringMaker sql = StringMaker.obtain(512);
		sql.append("INSERT INTO ");
		transaction.qualifyTableName(sql, tableMapping.getTableSpecifier());
		sql.append(insertSQL);
		final StatementWrapper insert = transaction.prepareStatement(sql.release());
		try {
			for (final T object : objects) {
				try {
					objectMapping.autoAssignPrimaryKey(object);
				} catch (final Exception e) {
					throw new SQLException(e.getMessage(), e);
				}
				for (int i=0; i<allMappings.length; ++i) {
					final ColumnMapping mapping = allMappings[i];
					Object value = mapping.getAttribute(object);
					value = mapping.columnHandler.getValue(value, mapping.attribute);
					insert.setObject(i + 1, value);
				}
				insert.addBatch();
			}
			// no need to check return of call, all error conditions should 
			// lead to SQLException being thrown.
			insert.executeBatch();
		} finally {
			JDBCBase.closeSilent(insert);
		}
	}

	// --------------------------------------------------------------
	// ---
	// --- Delete methods
	// ---
	// --------------------------------------------------------------
	public int delete(final JDBCBase transaction, final T object) throws SQLException {
		// not implemented
		throw new UnsupportedOperationException();
	}

	public int delete(final JDBCBase transaction, final java.util.Collection<T> objects) throws SQLException {
		if (objects == null || objects.isEmpty())
			return 0;
		// not implemented
		throw new UnsupportedOperationException();
	}

	// --------------------------------------------------------------
	// ---
	// --- Update methods
	// ---
	// --------------------------------------------------------------
	protected final String updateSQL;

	private static String buildUpdateSQL(final ColumnMapping[] pkMappings, final ColumnMapping[] columnMappings) {
		final StringMaker sql = StringMaker.obtain(256);
		sql.append(" SET ");
		for (int i=0; i<columnMappings.length; ++i) {
			final ColumnMapping column = columnMappings[i];
			if (i > 0)
				sql.append(", ");
			sql.append(column.column);
			sql.append(" = ?");
		}
		if (pkMappings.length > 0) {
			sql.append(" WHERE (");
			for (int i=0; i<pkMappings.length; ++i) {
				final ColumnMapping column = pkMappings[i];
				if (i > 0)
					sql.append(" AND ");
				sql.append(column.column);
				sql.append(" = ?");
			}
			sql.append(')');
		}
		return sql.release();
	}

	/**
	 * Updates a single object. If possible avoid calling this multiple times
	 * and use update(Collection<Object>) to take advantage of batch inserts.
	 * 
	 * Returns the number of records updated.
	 */
	public int update(final JDBCBase transaction, final T object) throws SQLException {
		final List<T> objectList = List.asList(object);
		final int[] result = update(transaction, objectList);
		if (result == null || result.length != 1)
			throw new SQLException("Single object update of " + object.getClass() + " invalid update result");
		return result[0];
	}

	/**
	 * Batch update of objects.
	 */
	public final int[] update(final JDBCBase transaction, final Collection<T> objects) throws SQLException {
		if (objects == null || objects.isEmpty())
			return Primitives.EMPTY_INT_ARRAY;
		// prepare update statement
		final StringMaker sql = StringMaker.obtain(512);
		sql.append("UPDATE ");
		transaction.qualifyTableName(sql, tableMapping.getTableSpecifier());
		sql.append(updateSQL);
		final StatementWrapper update = transaction.prepareStatement(sql.release());
		update.setLogLevel(LogLevel.DEBUG);
		try {
			for (final Object object : objects) {
				int i = 0;
				for (int j=0; j<columnMappings.length; ++j) {
					final ColumnMapping mapping = columnMappings[j];
					Object value = mapping.getAttribute(object);
					value = mapping.columnHandler.getValue(value, mapping.attribute);
					update.setObject(++i, value);
				}
				for (int j=0; j<pkMappings.length; ++j) {
					final ColumnMapping mapping = pkMappings[j];
					Object value = mapping.getAttribute(object);
					value = mapping.columnHandler.getValue(value, mapping.attribute);
					update.setObject(++i, value);
				}
				update.addBatch();
			}
			// no need to check return of call, all error conditions should 
			// lead to SQLException being thrown.
			return update.executeBatch();
		} finally {
			JDBCBase.closeSilent(update);
		}
	}
	
	/**
	 * Upsert of objects (UPDATE or INSERT depending on if PK already exists in table)

 MERGE LWX_PAUFTRAG AS T 
 USING (VALUES('106052', '2000')) AS S(PAuftrNr, Id_GSE_PA)
 ON (T.PAuftrNr = S.PAuftrNr AND T.Id_GSE_PA = S.Id_GSE_PA)
   WHEN MATCHED THEN
     UPDATE SET T.Haus_BemerkungUebersicht = '123'
   WHEN NOT MATCHED THEN
     INSERT (PAuftrNr, Id_GSE_PA, Haus_BemerkungUebersicht) VALUES ('106052', '2000', '345');

	 */
	public final int[] upsert(final JDBCBase transaction, final Collection<T> objects) throws SQLException {
		if (objects == null || objects.isEmpty())
			return Primitives.EMPTY_INT_ARRAY;
		// prepare merge statement
		final StringMaker sql = StringMaker.obtain(512);
		sql.append("MERGE ");
		transaction.qualifyTableName(sql, tableMapping.getTableSpecifier());
		sql.append(" AS T USING (VALUES(");
		for (int i=0; i<pkMappings.length; ++i)
			sql.append(i > 0 ? ", ?" : "?");
		sql.append(")) AS S(");
		for (int i=0; i<pkMappings.length; ++i) {
			if (i > 0)
				sql.append(", ");
			sql.append(pkMappings[i].column);
		}
		sql.append(") ON (");
		for (int i=0; i<pkMappings.length; ++i) {
			if (i > 0)
				sql.append(" AND ");
			sql.append("T.");
			sql.append(pkMappings[i].column);
			sql.append(" = S.");
			sql.append(pkMappings[i].column);
		}
		sql.append(") WHEN MATCHED THEN UPDATE SET ");
		for (int i=0; i<columnMappings.length; ++i) {
			if (i > 0)
				sql.append(", ");
			sql.append(columnMappings[i].column);
			sql.append(" = ?");
		}
		sql.append(" WHEN NOT MATCHED THEN INSERT (");
		for (int i=0; i<allMappings.length; ++i) {
			if (i > 0)
				sql.append(", ");
			sql.append(allMappings[i].column);
		}
		sql.append(") VALUES (");
		for (int i=0; i<allMappings.length; ++i) {
			sql.append(i > 0 ? ", ?" : "?");
		}
		sql.append(");");
		final StatementWrapper upsert = transaction.prepareStatement(sql.release());
		upsert.setLogLevel(LogLevel.DEBUG);
		try {
			for (final Object object : objects) {
				int i = 0;
				// primary key(s), using clause
				for (int j=0; j<pkMappings.length; ++j) {
					final ColumnMapping mapping = pkMappings[j];
					Object value = mapping.getAttribute(object);
					value = mapping.columnHandler.getValue(value, mapping.attribute);
					upsert.setObject(++i, value);
				}
				// values, update clause
				for (int j=0; j<columnMappings.length; ++j) {
					final ColumnMapping mapping = columnMappings[j];
					Object value = mapping.getAttribute(object);
					value = mapping.columnHandler.getValue(value, mapping.attribute);
					upsert.setObject(++i, value);
				}
				// primary keys and values, insert clause
				for (int j=0; j<allMappings.length; ++j) {
					final ColumnMapping mapping = allMappings[j];
					Object value = mapping.getAttribute(object);
					value = mapping.columnHandler.getValue(value, mapping.attribute);
					upsert.setObject(++i, value);
				}
				// do it
				upsert.addBatch();
			}
			// no need to check return of call, all error conditions should 
			// lead to SQLException being thrown.
			return upsert.executeBatch();
		} finally {
			JDBCBase.closeSilent(upsert);
		}
	}

	// --------------------------------------------------------------
	// ---
	// --- Object overrides
	// ---
	// --------------------------------------------------------------
	@Override
	public int hashCode() {
		return (tableMapping.hashCode() * 37) ^ objectMapping.hashCode();
	}

	@Override
	public boolean equals(final Object o) {
		if (o instanceof JDBCObjectMapper) {
			final JDBCObjectMapper<?> l = (JDBCObjectMapper<?>) o;
			return l.tableMapping.equals(tableMapping) &&
				   l.objectMapping.equals(objectMapping);
		}
		return false;
	}

}

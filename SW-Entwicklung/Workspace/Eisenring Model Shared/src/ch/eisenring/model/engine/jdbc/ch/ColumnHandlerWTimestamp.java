package ch.eisenring.model.engine.jdbc.ch;

import java.sql.SQLException;
import java.util.Date;

import ch.eisenring.jdbc.JDBCResultSet;
import ch.eisenring.model.MetaClassMember;
import ch.eisenring.model.engine.jdbc.ColumnHandler;

public final class ColumnHandlerWTimestamp extends ColumnHandler {

	@Override
	public Object getColumn(final JDBCResultSet resultSet,
			                final MetaClassMember member,
			                final String columnName) throws SQLException {
		final java.sql.Timestamp t = resultSet.getTimestamp(columnName);
		if (resultSet.wasNull() || t == null)
			return null;
		return new Date(t.getTime());
	}

	@Override
	public Object getColumn(final JDBCResultSet resultSet,
			                final MetaClassMember member,
			                final int columnIndex) throws SQLException {
		final java.sql.Timestamp t = resultSet.getTimestamp(columnIndex);
		if (resultSet.wasNull() || t == null)
			return null;
		return new Date(t.getTime());
	}

	@Override
	public Object getValue(final Object memberValue,
			               final MetaClassMember member) {
		if (memberValue == null)
			return null;
		return new java.sql.Timestamp(((Date) memberValue).getTime());
	}

}

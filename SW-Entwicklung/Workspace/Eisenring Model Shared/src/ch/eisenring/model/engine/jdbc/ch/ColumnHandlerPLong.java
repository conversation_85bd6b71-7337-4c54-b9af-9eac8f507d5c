package ch.eisenring.model.engine.jdbc.ch;

import java.sql.SQLException;

import ch.eisenring.jdbc.JDBCResultSet;
import ch.eisenring.model.MetaClassMember;
import ch.eisenring.model.engine.jdbc.ColumnHandler;

public final class ColumnHandlerPLong extends ColumnHandler {

	@Override
	public Object getColumn(final JDBCResultSet resultSet,
			                final MetaClassMember member,
			                final String columnName) throws SQLException {
		final long value = resultSet.getLong(columnName);
		return Long.valueOf(resultSet.wasNull() ? 0L : value);
	}
	@Override
	public Object getColumn(final JDBCResultSet resultSet,
							final MetaClassMember member,
							final int columnIndex) throws SQLException {
		final long value = resultSet.getLong(columnIndex);
		return Long.valueOf(resultSet.wasNull() ? 0L : value);
	}

}

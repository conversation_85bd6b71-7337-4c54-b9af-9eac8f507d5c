package ch.eisenring.model.engine.jdbc.ch;

import java.sql.SQLException;

import ch.eisenring.core.datatypes.primitives.Primitives;
import ch.eisenring.jdbc.JDBCResultSet;
import ch.eisenring.model.MetaClassMember;
import ch.eisenring.model.engine.jdbc.ColumnHandler;

public final class ColumnHandlerPFloat extends ColumnHandler {

	@Override
	public Object getColumn(final JDBCResultSet resultSet,
			                final MetaClassMember member,
			                final String columnName) throws SQLException {
		final float value = resultSet.getFloat(columnName);
		return resultSet.wasNull() ? Primitives.FLOAT_ZERO : Primitives.valueOf(value);
	}

	@Override
	public Object getColumn(final JDBCResultSet resultSet,
			                final MetaClassMember member,
			                final int columnIndex) throws SQLException {
		final float value = resultSet.getFloat(columnIndex);
		return resultSet.wasNull() ? Primitives.FLOAT_ZERO : Primitives.valueOf(value);
	}


}

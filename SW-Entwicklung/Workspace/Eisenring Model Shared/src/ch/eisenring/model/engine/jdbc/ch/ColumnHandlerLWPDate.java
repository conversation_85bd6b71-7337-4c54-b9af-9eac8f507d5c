package ch.eisenring.model.engine.jdbc.ch;

import java.sql.SQLException;

import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.jdbc.JDBCResultSet;
import ch.eisenring.model.MetaClassMember;
import ch.eisenring.model.datatypes.lw.LWDateUtil;

public final class ColumnHandlerLWPDate extends ColumnHandlerLWDate {

	@Override
	protected Object getColumn(final JDBCResultSet resultSet,
						  	 final MetaClassMember member,
                             final java.sql.Date value) throws SQLException {
		if (resultSet.wasNull())
			return TimestampUtil.NULL_TIMESTAMP_LONG;
		return LWDateUtil.toTimestamp(value);
	}

	@Override
	public Object getValue(final Object memberValue, 
			               final MetaClassMember member) {
		final long millis = ((Number) memberValue).longValue();
		return LWDateUtil.toDate(millis, java.sql.Date.class);
	}

}

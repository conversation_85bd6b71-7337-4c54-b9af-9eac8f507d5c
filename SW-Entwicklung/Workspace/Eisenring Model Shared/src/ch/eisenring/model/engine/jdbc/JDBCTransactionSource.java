package ch.eisenring.model.engine.jdbc;

import java.io.IOException;
import java.sql.Connection;
import java.sql.SQLException;

import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.jdbc.ConnectionConfigurator;
import ch.eisenring.jdbc.ConnectionInfo;
import ch.eisenring.jdbc.JDBCBase;
import ch.eisenring.model.TransactionContext;
import ch.eisenring.model.TransactionSource;

/**
 * Class for loading model objects from database
 */
public abstract class JDBCTransactionSource extends JDBCBase implements TransactionSource {

	protected JDBCTransactionSource(final DatabaseSpecifier database) {
		super(database.getConnectionInfo());
	}

	@Deprecated
	public JDBCTransactionSource(final ConnectionInfo connectionInfo) {
		super(connectionInfo);
	}

	@Override
	public void configure(final Connection connection) throws SQLException {
		ConnectionConfigurator.READONLY.configure(connection);
	}

	@Override
	public void load(final TransactionContext context) throws IOException {
		final Connection connection = openSilent();
		if (connection == null)
			throw new IOException("connection failed"); 
		try {
			// Locking the context is not strictly required since
			// no other thread has access to the instance at this
			// point. Since many of the calls we need to make
			// are synchronizing on the context by themselves, this
			// should still be good for performance.
			synchronized (context.getLock()) {
				try {
					prepare(context);
					loadModels(context);
					loadRelations(context);
					if (!connection.getAutoCommit())
						connection.commit();
					loadComplete(context);
				} finally {
					cleanup(context);
				}
			}
		} catch (final Exception e) {
			try {
				connection.rollback();
			} catch (final SQLException e2) {
				Logger.error(Strings.concat("rollback failed: ", e.getMessage()));
				Logger.error(e);
			}
			Logger.error(Strings.concat("context load failed: ", e.getMessage()));
			Logger.error(e);
			if (e instanceof IOException) {
				throw (IOException) e;
			} else {
				throw new IOException(e.getMessage(), e);
			}
		} finally {
			// close connection
			close();
		}
	}

	/**
	 * Called before loading of the objects begins.
	 * Can be used to allocate prepared statements etc.
	 */
	protected void prepare(final TransactionContext context) throws SQLException {
	}

	/**
	 * Called to load all models into the context.
	 */
	protected void loadModels(final TransactionContext context) throws SQLException {
	}

	/**
	 * Called to load all relations not loaded by loadModels().
	 */
	protected void loadRelations(final TransactionContext context) throws SQLException {
	}

	/**
	 * Called after all load methods. Only in case no errors occurred.
	 */
	protected void loadComplete(final TransactionContext context) throws SQLException {
	}

	/**
	 * Called to release all resources that have been allocated,
	 * regardless of error conditions.
	 */
	protected void cleanup(final TransactionContext context) throws SQLException {
	}

	/**
	 * Loads all objects using the provided table mapping.
	 * This call will use an unrestricted select (no WHERE clause)
	 * of all columns in the mapping to load all rows from
	 * the database.
	 */
	protected final void load(final TransactionContext context,
			                  final SingleTableMapping mapping) throws SQLException {
		final StringMaker sql = StringMaker.obtain(256);
		mapping.appendSelectSQL(sql, this, TableMapping.NO_TABLE_ALIAS);
		doQuery(sql.release(), (resultSet) ->  { mapping.loadRow(context, resultSet); });
	}
	
}

package ch.eisenring.model.engine.jdbc.ch;

import java.sql.SQLException;

import ch.eisenring.jdbc.JDBCResultSet;
import ch.eisenring.model.MetaClassMember;
import ch.eisenring.model.datatypes.lw.LWDateUtil;

public final class ColumnHandlerLWWDate extends ColumnHandlerLWDate {

	@Override
	protected Object getColumn(final JDBCResultSet resultSet,
						  	   final MetaClassMember member,
                               final java.sql.Date value) throws SQLException {
		if (resultSet.wasNull()) {
			return null;
		} else if (value.getTime() < LWDateUtil.NULL_BOUNDARY) {
			return null;
		}
		return new java.util.Date(value.getTime());
	}

	@Override
	public Object getValue(final Object memberValue, 
			               final MetaClassMember member) {
		if (memberValue == null)
			return LWDateUtil.NULL_SQLDATE;
		final long millis = ((java.util.Date) memberValue).getTime();
		return new java.sql.Date(millis);
	}

}

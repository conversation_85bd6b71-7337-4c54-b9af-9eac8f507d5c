package ch.eisenring.model.engine.jdbc;

import java.sql.SQLException;
import java.util.Iterator;
import java.util.LinkedHashMap;

import ch.eisenring.core.collections.Collection;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.iterators.ArrayIterator;
import ch.eisenring.core.mapping.ToOneMapping;
import ch.eisenring.jdbc.JDBCConnectionMetaData;
import ch.eisenring.model.MetaAttribute;
import ch.eisenring.model.MetaClass;
import ch.eisenring.model.MetaClassMember;
import ch.eisenring.model.MetaRelation;
import ch.eisenring.model.MetaRelationMany;

/**
 * Implementation of TableMapping for a single table
 */
public final class SingleTableMapping extends TableMapping implements ToOneMapping<MetaClassMember, ColumnSpecifier> {

	private final LinkedHashMap<MetaClassMember, ColumnSpecifier> columnMap = new LinkedHashMap<MetaClassMember, ColumnSpecifier>(32, 1.25F);
	private final LinkedHashMap<ColumnSpecifier, MetaClassMember> memberMap = new LinkedHashMap<ColumnSpecifier, MetaClassMember>(32, 1.25F);
	private final RelationMappingM2M[] relationsM2M;
	
	private final TableSpecifier table;
	
	/**
	 * The columns that act as primary key.
	 * (usually just one, but there are exceptions:
	 * zero = no primary key, multiple = composite primary key).
	 */
	private final ColumnSpecifier[] primaryKeyColumns;

	/**
	 * Creates table mapping for class -> table
	 */
	SingleTableMapping(final MetaClass metaClass, final TableSpecifier table) {
		super(metaClass);
		this.table = table;

		// filter the meta class for attributes mapped to table
		final Iterator<MetaAttribute> attrItr = metaClass.getAttributeIterator(true);
		while (attrItr.hasNext()) {
			final MetaAttribute attribute = attrItr.next();
			final ColumnSpecifier[] columns = table.getColumnSpecifiers(attribute);
			switch (columns.length) {
				case 0:
				default:
					break;
				case 1: {
					columnMap.put(attribute, columns[0]);
					memberMap.put(columns[0], attribute);
				}
			}
		}
		
		// build array of primary key columns
		primaryKeyColumns = table.getColumnSpecifiers(metaClass);
		
		// find all ManyToMany relation mappings originating from mapped class
		final ArrayList<RelationMappingM2M> relationList = new ArrayList<RelationMappingM2M>();
		final Iterator<MetaRelation> relationItr = metaClass.getRelationIterator(true);
		while (relationItr.hasNext()) {
			MetaRelation relation = relationItr.next();
			if (!(relation instanceof MetaRelationMany))
				continue;
			final MetaRelationMany fromEnd = (MetaRelationMany) relation;
			relation = fromEnd.getOtherEnd();
			if (!(relation instanceof MetaRelationMany))
				continue;
			final RelationMappingM2M mapping = RelationMappingM2M.get(fromEnd, table.getDatabase());
			relationList.add(mapping);
		}
		if (relationList.isEmpty()) {
			this.relationsM2M = null;
		} else {
			this.relationsM2M = Collection.toArray(relationList, RelationMappingM2M.class);
		}
	}

	/**
	 * Gets the table this mapping is for
	 */
	public TableSpecifier getTableSpecifier() {
		return table;
	}

	/**
	 * Gets iterator of all ManyToMany relation mappings
	 * originating from the mapped class.
	 */
	@SuppressWarnings("unchecked")
	public Iterator<RelationMappingM2M> getRelationMappingIterator() {
		return ArrayIterator.get(relationsM2M);
	}
	
	@Override
	public Iterator<MetaClassMember> getMemberIterator() {
		return memberMap.values().iterator();
	}

	@Override
	public Iterator<ColumnSpecifier> getColumnIterator() {
		return columnMap.values().iterator();
	}
	
	@Override
	public MetaClassMember getMember(final ColumnSpecifier column) {
		return memberMap.get(column);
	}
	
	@Override
	public ColumnSpecifier get(final MetaClassMember member) {
		return columnMap.get(member);
	}

	/**
	 * Gets the column for attribute. Returns NULL if the member is not mapped.
	 */
	@Override
	public ColumnSpecifier getColumn(final MetaClassMember member) {
		return columnMap.get(member);
	}

	@Override
	public ColumnSpecifier getPrimaryKeyColumn() {
		return primaryKeyColumns.length > 0 ? primaryKeyColumns[0] : null;
	}

	@Override
	public ColumnSpecifier[] getPrimaryKeyColumns() {
		return primaryKeyColumns;
	}

	/**
	 * Gets a select statement for this mapping.
	 * This will have the form "SELECT <COLUMNS> FROM <TABLE> [AS Alias]" without where clause.
	 */
	public void appendSelectSQL(
			final StringMaker target,
			final String database,
			final String schema,
			final String tableAlias) {
		target.append("SELECT ");
		appendColumns(target, tableAlias);
		target.append(" FROM ");
		if (!Strings.isEmpty(database)) {
			target.append(database);
			target.append('.');
		}
		if (!Strings.isEmpty(schema)) {
			target.append(schema);
			target.append('.');
		}
		target.append(getTableSpecifier().getTableName());
		if (tableAlias != null && !FULL_TABLE_ALIAS.equals(tableAlias)) {
			target.append(" AS ");
			target.append(tableAlias);
		}
	}

	/**
	 * Gets a select statement for this mapping.
	 * This will have the form "SELECT <COLUMNS> FROM <TABLE>" without where clause.
	 */
	public void appendSelectSQL(final StringMaker target, final String database, final String schema) {
		appendSelectSQL(target, database, schema, FULL_TABLE_ALIAS);
	}
	
	/**
	 * Gets a select statement for this mapping.
	 * This will have the form "SELECT <COLUMNS> FROM <TABLE>" without where clause.
	 */
	public void appendSelectSQL(final StringMaker target, final JDBCConnectionMetaData metaData) throws SQLException {
		appendSelectSQL(target, metaData.getDatabase(), metaData.getSchema(), FULL_TABLE_ALIAS);
	}

	/**
	 * Gets a select statement for this mapping.
	 * This will have the form "SELECT <COLUMNS> FROM <TABLE> [AS Alias]" with no where clause.
	 */
	public void appendSelectSQL(final StringMaker target, final JDBCConnectionMetaData metaData, final String tableAlias) throws SQLException {
		appendSelectSQL(target, metaData.getDatabase(), metaData.getSchema(), tableAlias);
	}

	/**
	 * Replaces all column variables with column names
	 */
	public String replaceColumnSymbols(final String symbolicSql) {
		if (Strings.isEmpty(symbolicSql) || Strings.indexOf(symbolicSql, '$') < 0)
			return symbolicSql;
		final TableSpecifier table = getTableSpecifier();
		String sql = symbolicSql.replace("${" + metaClass.getName() + "}", table.getTableName());
		final ColumnSpecifier pkColumn = getPrimaryKeyColumn();
		if (pkColumn != null)
			sql = sql.replace("${PK}", pkColumn.getColumnName());
		final Iterator<ColumnSpecifier> i = getColumnIterator();
		while (i.hasNext()) {
			final ColumnSpecifier column = i.next();
			final MetaClassMember member = getMember(column);
			sql = sql.replace("${" + member.getName() + "}", column.getColumnName());
		}
		return sql;
	}

	// --------------------------------------------------------------
	// ---
	// --- Object overrides
	// ---
	// --------------------------------------------------------------
	@Override
	public int hashCode() {
		return (metaClass.hashCode() * 37) ^ table.hashCode();
	}
	
	@Override
	public boolean equals(final Object o) {
		if (o instanceof SingleTableMapping) {
			final SingleTableMapping m = (SingleTableMapping) o;
			return m.metaClass.equals(metaClass) &&
			       m.table.equals(table);
		}
		return false;
	}

	@Override
	public String toString() {
		return "SingleTableMapping(" + metaClass.getName() + " -> " + table.getTableName() + ")";
	}

}

package ch.eisenring.model.engine.jdbc.ch;

import java.sql.SQLException;

import ch.eisenring.jdbc.JDBCResultSet;
import ch.eisenring.model.MetaClassMember;
import ch.eisenring.model.engine.jdbc.ColumnHandler;

abstract class <PERSON>umnHandlerLWDate extends ColumnHandler {

	@Override
	public Object getColumn(final JDBCResultSet resultSet,
			                final MetaClassMember member,
			                final String columnName) throws SQLException {
		final java.sql.Date value = resultSet.getDate(columnName);
		return getColumn(resultSet, member, value);
	}

	@Override
	public Object getColumn(final JDBCResultSet resultSet,
			                final MetaClassMember member,
			                final int columnIndex) throws SQLException {
		final java.sql.Date value = resultSet.getDate(columnIndex);
		return getColumn(resultSet, member, value);
	}

	/**
	 * Gets value from result set and convert to data type representation
	 */
	protected abstract Object getColumn(final JDBCResultSet resultSet,
										final MetaClassMember member,
										final java.sql.Date value) throws SQLException;

}

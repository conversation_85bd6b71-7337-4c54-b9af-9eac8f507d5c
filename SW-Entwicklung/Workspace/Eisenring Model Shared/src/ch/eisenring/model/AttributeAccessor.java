package ch.eisenring.model;

/**
 * Combined getter/setter interface for attributes
 */
public interface AttributeAccessor {

	/**
	 * Gets the current value of the member from materializing object.
	 */
	public abstract Object getMember(final Object object);

	/**
	 * Sets the current value of the member on the materializing object.
	 */
	public abstract void setMember(final Object object, final Object value);
	
}

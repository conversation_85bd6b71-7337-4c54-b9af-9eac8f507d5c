package ch.eisenring.model.attribute.validation;

import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.model.validation.ValidationContext;
import ch.eisenring.model.validation.ValidationMessageType;

public class StringLengthValidator extends AbstractAttributeValidator {

	public final static StringLengthValidator LENGTH_30 = new StringLengthValidator(30, ValidationMessageType.WARN);
	public final static StringLengthValidator LENGTH_60 = new StringLengthValidator(60, ValidationMessageType.WARN);
	
	protected final int maxLength;
	
	public StringLengthValidator(final int maxLength, final ValidationMessageType severity) {
		this.maxLength = maxLength;
	}

	@Override
	public void validate(final ValidationContext context,
						 final Object value) {
		if (value instanceof String) {
			final String strValue = value.toString();
			if (strValue.length() > maxLength) {
				context.post(severity, "too long", strValue.length(), maxLength);
			}
		}
	}

	@Override
	public Object makeValid(final ValidationContext context,
			                final Object value) {
		if (value instanceof CharSequence) {
			final CharSequence charSeq = (CharSequence) value;
			if (Strings.length(charSeq) > maxLength) {
				return Strings.subString(charSeq, 0, maxLength);
			}
		}
		return value;
	}

}

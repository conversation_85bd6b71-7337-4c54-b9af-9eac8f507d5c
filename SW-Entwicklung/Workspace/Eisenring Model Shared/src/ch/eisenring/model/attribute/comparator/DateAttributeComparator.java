package ch.eisenring.model.attribute.comparator;

import java.util.Date;

import ch.eisenring.model.MetaAttribute;

final class DateAttributeComparator extends AbstractAttributeComparator {

	DateAttributeComparator(final MetaAttribute attribute) {
		super(attribute);
	}
	
	@Override
	protected int compareImpl(final Object value1, final Object value2) {
		return ((Date) value1).compareTo((Date) value2);
	}

}

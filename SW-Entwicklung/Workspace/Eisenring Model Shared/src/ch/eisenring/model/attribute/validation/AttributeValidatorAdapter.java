package ch.eisenring.model.attribute.validation;

import ch.eisenring.model.validation.AttributeValidator;
import ch.eisenring.model.validation.ValidationContext;

/**
 * A do-nothing default implementation of AttributeValidator.
 */
public final class AttributeValidatorAdapter implements AttributeValidator {

	@Override
	public void validate(final ValidationContext context, final Object value) {
		// do nothing
	}
	@Override
	public Object makeValid(final ValidationContext context, final Object value) {
		return value;
	}

	@Override
	public final Class<?> getPropertyType() {
		return AttributeValidator.class;
	}

}

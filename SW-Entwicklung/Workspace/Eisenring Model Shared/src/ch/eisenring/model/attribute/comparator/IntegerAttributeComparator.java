package ch.eisenring.model.attribute.comparator;

import ch.eisenring.model.MetaAttribute;

public final class IntegerAttributeComparator extends AbstractAttributeComparator {

	public IntegerAttributeComparator(final MetaAttribute attribute) {
		super(attribute);
	}
	
	@Override
	protected int compareImpl(final Object value1, final Object value2) {
		return ((Integer) value1).compareTo((Integer) value2);
	}

}

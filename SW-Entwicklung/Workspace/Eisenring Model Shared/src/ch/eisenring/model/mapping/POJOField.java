package ch.eisenring.model.mapping;

import java.lang.reflect.Field;

import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.reflect.AccessorFactory;
import ch.eisenring.core.reflect.FieldAccessor;
import ch.eisenring.model.AttributeAccessor;
import ch.eisenring.model.MetaProperty;

public final class POJOField implements MetaProperty, AttributeAccessor {

	protected final Class<?> pojoClass;
	protected final FieldAccessor accessor;

	POJO<PERSON>ield(final Class<?> pojoClass, final Field pojoField) throws IllegalArgumentException {
		this.pojoClass = pojoClass;
		this.accessor = AccessorFactory.getFieldAccessor(pojoField);
	}

	public Class<?> getPOJOClass() {
		return pojoClass;
	}

	public Field getPOJOField() {
		return accessor.getField();
	}

	public FieldAccessor getAccessor() {
		return accessor;
	}

	// --------------------------------------------------------------
	// ---
	// --- Factory methods
	// ---
	// --------------------------------------------------------------
	/**
	 * Cache for POJOField's
	 */
	private final static List<POJOField> POJO_FIELDS = new ArrayList<>();
	
	/**
	 * Creates a POJOField MetaProperty for given class/field
	 */
	public static POJOField get(final Class<?> pojoClass,
			                    final String fieldName) throws IllegalArgumentException {
		POJOField result = null;
		final Field pojoField = getField(pojoClass, fieldName);
		synchronized (POJO_FIELDS) {
			for (POJOField cached : POJO_FIELDS) {
				if (cached.getPOJOField().equals(pojoField) &&
					cached.pojoClass.equals(pojoClass)) {
					result = cached;
					break;
				}
			}
			if (result == null) {
				result = new POJOField(pojoClass, pojoField);
				POJO_FIELDS.add(result);
				POJO_FIELDS.trimToSize();
			}
		}
		return result;
	}

	private static Field getField(final Class<?> holderClass, final String fieldName) throws IllegalArgumentException {
		Class<?> fieldHolder = holderClass;
		Field result = null;
		while (fieldHolder != null && !Object.class.equals(fieldHolder)) {
			try {
				final Field field = fieldHolder.getDeclaredField(fieldName);
				if (field != null) {
					result = field;
					break;
				}
			} catch (final Exception e) {
				// ignore
			}
			fieldHolder = fieldHolder.getSuperclass();
		}
		if (result == null)
			throw new IllegalArgumentException("No field \"" + fieldName + "\" not found in " + holderClass);
		if (!isFieldAccessible(result)) {
			try {
				result.setAccessible(true);
			} catch (final SecurityException e) {
				throw new IllegalArgumentException("Field " + fieldName + " in " + fieldHolder + " is not accessible", e);
			}
		}
		return result;
	}
	
	// --------------------------------------------------------------
	// ---
	// --- MetaProperty implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public Class<?> getPropertyType() {
		return POJOField.class;
	}

	// --------------------------------------------------------------
	// ---
	// --- AttributeGetter & Setter implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public Object getMember(final Object object) {
		return accessor.getAutoBoxing(object);
	}

	@Override
	public void setMember(final Object object, final Object value) {
		accessor.setAutoBoxing(object, value);
	}

	// --------------------------------------------------------------
	// ---
	// --- Utility methods
	// ---
	// --------------------------------------------------------------
	/**
	 * Returns true if this field is present in class
	 */
	public boolean isFromClass(final Class<?> someClass) {
		if (someClass == null)
			return false;
		return pojoClass.isAssignableFrom(someClass);
	}

	// --------------------------------------------------------------
	// ---
	// --- Object overrides
	// ---
	// --------------------------------------------------------------
	@Override
	public int hashCode() {
		return accessor.hashCode();
	}

	@Override
	public boolean equals(final Object o) {
		if (o instanceof POJOField && o.getClass().equals(getClass())) {
			final POJOField f = (POJOField) o;
			return f.getAccessor().equals(accessor) && f.pojoClass.equals(pojoClass);
		}
		return false;
	}

	/**
	 * Checks if a field is accessible, compatible with both Java 8 and Java 21.
	 * Uses canAccess() for Java 9+ and falls back to isAccessible() for Java 8.
	 */
	private static boolean isFieldAccessible(java.lang.reflect.Field field) {
		try {
			// Try Java 9+ canAccess() method first
			if (java.lang.reflect.Modifier.isStatic(field.getModifiers())) {
				return field.canAccess(null);
			} else {
				// For instance fields, check if the field is public
				return java.lang.reflect.Modifier.isPublic(field.getModifiers()) &&
				       java.lang.reflect.Modifier.isPublic(field.getDeclaringClass().getModifiers());
			}
		} catch (NoSuchMethodError e) {
			// Fall back to deprecated isAccessible() for Java 8
			try {
				java.lang.reflect.Method isAccessibleMethod = java.lang.reflect.Field.class.getMethod("isAccessible");
				return (Boolean) isAccessibleMethod.invoke(field);
			} catch (Exception ex) {
				// If both fail, assume not accessible
				return false;
			}
		} catch (Exception e) {
			// If canAccess() throws an exception, assume not accessible
			return false;
		}
	}

}

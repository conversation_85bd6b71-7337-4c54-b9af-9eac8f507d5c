package ch.eisenring.model.mapping;

import java.io.IOException;

import ch.eisenring.model.AttributeAccessor;
import ch.eisenring.model.MetaClass;
import ch.eisenring.model.MetaClassMember;
import ch.eisenring.model.Model;
import ch.eisenring.model.TransactionContext;
import ch.eisenring.model.exceptions.ModelException;

/**
 * Describes how MetaClass maps to ModelClass.
 * 
 * (Technically this is a thin facade, because MetaClass
 * for historical reasons *implements* the mapping to model
 * already).
 */
public final class ModelMapping<T extends Model> extends ObjectMappingBase<T> {

	@SuppressWarnings("unchecked")
	ModelMapping(final MetaClass metaClass, final Class<T> targetClass) {
		super(metaClass, targetClass);
	}

	// --------------------------------------------------------------
	// ---
	// --- Factory methods
	// ---
	// --------------------------------------------------------------
	public static <T extends Model> ModelMapping<T> get(final Class<T> modelClass) {
		final MetaClass metaClass = Model.getMetaClass(modelClass);
		return get(metaClass, modelClass);
	}
	
	public static <T extends Model> ModelMapping<T> get(final MetaClass metaClass, final Class<T> modelClass) {
		ModelMapping<T> result = (ModelMapping<T>) ObjectMappingCache.INSTANCE.get(metaClass, modelClass);
		if (result == null) {
			result = new ModelMapping<T>(metaClass, modelClass);
			ObjectMappingCache.INSTANCE.add(result);
		}
		return result;
	}
	
	// --------------------------------------------------------------
	// ---
	// --- ObjectMapping implementation
	// ---
	// --------------------------------------------------------------
	@SuppressWarnings("unchecked")
	@Override
	public T createObject(final Object context, final Object[] primaryKeyValues) throws ModelException {
		final Model model = ((TransactionContext) context).createModel(metaClass, primaryKeyValues); 
		return (T) model;
	}

	@Override
	public AttributeAccessor getAccessor(final MetaClassMember member) throws ModelException {
		return member;
	}

	@Override
	public void autoAssignPrimaryKey(final T object) throws IOException {
		// not necessary for this type
	}

}

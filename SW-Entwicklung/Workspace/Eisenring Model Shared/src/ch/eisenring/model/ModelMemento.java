package ch.eisenring.model;

/**
 * Helper class to copy information from one context
 * to another without needing to lock both at the same time.
 */
public final class ModelMemento {

	private final MetaClass metaClass;
	private final PrimaryKey primaryKey;
	private final ModelStatus status;
	private final Object[] values;
	
	public ModelMemento(final Model source) {
		synchronized(source.getContext().getLock()) {
			this.metaClass = source.getMetaClass();
			this.status = source.getStatus();
			this.primaryKey = source.getPrimaryKey();
			final MetaAttribute[] attributes = metaClass.getAttributes(true);
			values = new Object[attributes.length];
			for (int i=0; i<attributes.length; ++i) {
				values[i] = attributes[i].getMember(source);
			}
		}
	}

	/**
	 * Re-creates the remembered model in target context, replicating
	 * the same status the model had orginally.
	 */
	public Model createModel(final TransactionContext context) {
		return createModel(context, status);
	}

	/**
	 * Re-creates the remembered model in target context, using the provided
	 * status instead of the remembered status.
	 */
	public Model createModel(final TransactionContext context, final ModelStatus status) {
		final Model model;
		synchronized (context.getLock()) {
			model = context.createModel(metaClass, primaryKey);
			final MetaAttribute[] attributes = metaClass.getAttributes(true);
			for (int i=0; i<attributes.length; ++i) {
				attributes[i].setMember(model, values[i]);
			}
			model.setStatus(status);
		}
		return model;
	}
	
}

package ch.eisenring.lw.api;

import ch.eisenring.logiware.LWSubjektKey;
import ch.eisenring.logiware.code.hard.LWPaKommArtCode;
import ch.eisenring.logiware.code.soft.GSECode;

/**
 * Subjekt kontakt-partner communication address
 */
public interface LWSubKontaktPartnerKommData extends LWObjectBaseData {

	public int getSubjektId();
	
	public GSECode getGSE();

	public int getLaufNummer();

	public String getRolleGBZ();

	/**
	 * Get the subjekt key this data belongs to
	 */
	public default LWSubjektKey getSubjektKey() {
		return LWSubjektKey.get(getSubjektId());
	}

	/**
	 * Type of communication address
	 */
	public LWPaKommArtCode getPaKommArt();

	/**
	 * Gets the timestamp from when this address is valid.
	 */
	public long getValidFrom();
	
	/**
	 * Gets the actual address data (interpretation depends on LWPaKommArtCode).
	 */
	public String getKommAdresse();

	// --------------------------------------------------------------
	// ---
	// --- Utility methods
	// ---
	// --------------------------------------------------------------
	public default boolean matches(final LWSubKontaktPartnerData partner) {
		return partner != null
				&& partner.getSubjektId() == this.getSubjektId()
				&& partner.getRolleGBZ() == this.getRolleGBZ()
				&& partner.getGSE() == this.getGSE();
	}

}

package ch.eisenring.lw.api;

import ch.eisenring.logiware.LWAuftragKey;
import ch.eisenring.logiware.LWDokumentKey;
import ch.eisenring.logiware.LWFormularKey;
import ch.eisenring.logiware.code.hard.LWFormularArt;
import ch.eisenring.logiware.code.hard.LWMontagescheinSubTyp;
import ch.eisenring.logiware.code.soft.GSECode;

public interface LWDokumentData extends LWObjectData {

	public int getDokumentId();

	public GSECode getGSE();

	public LWFormularArt getFormularArt();
	
	public int getFormularNummer();
	
	/**
	 * Montageschein sub type
	 * 
	 */
	public LWMontagescheinSubTyp getMSSubTyp();
	
	public long getFormularDatum();

	public int getAuftragNummer();
	
	public LWDokumentKey getPK();

	public String getAusgabeOrt1();

	/**
	 * Gets the key of auftrag this form belongs to
	 */
	public default LWAuftragKey getAuftragKey() {
		return LWAuftragKey.get(getAuftragNummer(), getGSE());
	}
	
	/**
	 * Gets the key of the from in form table
	 */
	public default LWFormularKey getFormularKey() {
		return LWFormularKey.get(getFormularNummer(), getFormularArt(), getGSE());
	}

}

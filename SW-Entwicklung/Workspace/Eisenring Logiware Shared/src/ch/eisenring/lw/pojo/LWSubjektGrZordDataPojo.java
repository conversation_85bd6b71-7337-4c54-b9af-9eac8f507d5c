package ch.eisenring.lw.pojo;

import ch.eisenring.core.io.StreamReader;
import ch.eisenring.core.io.StreamWriter;
import ch.eisenring.logiware.LWObjectKey;
import ch.eisenring.logiware.LWSubjektKey;
import ch.eisenring.logiware.code.pseudo.LWGBZGruppeCode;
import ch.eisenring.logiware.code.soft.LWGBZGrArtCode;
import ch.eisenring.lw.api.LWSubjektGrZordData;

import java.io.IOException;

public class LWSubjektGrZordDataPojo extends LWDataPojo implements LWSubjektGrZordData {

    private int subjektId;
    private LWGBZGruppeCode gruppe;
    private LWGBZGrArtCode gruppenArt;

    private LWSubjektGrZordDataPojo() {
    }

    // --------------------------------------------------------------
    // ---
    // --- Factory
    // ---
    // --------------------------------------------------------------
    public static LWSubjektGrZordDataPojo create(final LWSubjektGrZordData source) {
        if (source == null || source instanceof LWSubjektGrZordDataPojo)
            return (LWSubjektGrZordDataPojo) source;
        final LWSubjektGrZordDataPojo result = new LWSubjektGrZordDataPojo();
        result.subjektId = source.getSubjektKey().getSubjektId();
        result.gruppe = source.getGruppe();
        result.gruppenArt = source.getGruppenArt();
        return result;
    }

    // --------------------------------------------------------------
    // ---
    // --- LWSubjektGrZordData implementation
    // ---
    // --------------------------------------------------------------
    @Override
    public LWGBZGruppeCode getGruppe() {
        return gruppe;
    }

    @Override
    public LWGBZGrArtCode getGruppenArt() {
        return gruppenArt;
    }

    @Override
    public LWSubjektKey getSubjektKey() {
        return LWSubjektKey.get(subjektId);
    }

    @Override
    public boolean isStorniert() {
        return false;
    }

    @Override
    public LWObjectKey getPK() {
        return null;
    }

    // --------------------------------------------------------------
    // ---
    // --- Streamable implementation
    // ---
    // --------------------------------------------------------------
    @Override
    public int getStreamVersion() {
        return 1;
    }

    @Override
    public void read(final StreamReader reader) throws IOException {
        super.read(reader);
        subjektId = reader.readInt();
        gruppe = reader.readCode(LWGBZGruppeCode.class);
        gruppenArt = reader.readCode(LWGBZGrArtCode.class);
    }

    @Override
    public void write(final StreamWriter writer) throws IOException {
        super.write(writer);
        writer.writeInt(subjektId);
        writer.writeCode(gruppe);
        writer.writeCode(gruppenArt);
    }

}

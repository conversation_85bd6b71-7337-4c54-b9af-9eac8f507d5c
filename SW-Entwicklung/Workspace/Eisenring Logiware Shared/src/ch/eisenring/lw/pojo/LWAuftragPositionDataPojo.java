package ch.eisenring.lw.pojo;

import ch.eisenring.logiware.code.soft.LWKOKCode;
import ch.eisenring.logiware.code.soft.LWTerminCD;
import java.io.IOException;

import ch.eisenring.core.io.StreamReader;
import ch.eisenring.core.io.StreamWriter;
import ch.eisenring.core.io.Streamable;
import ch.eisenring.logiware.LWPositionKey;
import ch.eisenring.logiware.LWProduktKey;
import ch.eisenring.logiware.code.soft.LWLTolerCode;
import ch.eisenring.logiware.code.soft.LWPositionStatusCode;
import ch.eisenring.lw.api.LWAuftragPositionData;
import ch.eisenring.lw.stringcache.LWStringCaches;

public final class LWAuftragPositionDataPojo extends LWDataPojo implements LWAuftragPositionData, Streamable {

	private LWPositionKey positionKey;
	private LWPositionKey positionKeyAB;
	private LWProduktKey produktKey;
	private String gseLager;
	private String produktBezeichnung;
	private String textVor;
	private String textNach;
	private long wunschDatum;
	private LWLTolerCode ltolerCode;
	private LWPositionStatusCode statusCode;
	private double totBestellMenge;
	private double totGelieferteMenge;
	private double faktMenge;
	private double nettoWert;
	private LWKOKCode kokCode;
	private LWTerminCD terminCD;

	private LWAuftragPositionDataPojo() {
	}

	// --------------------------------------------------------------
	// ---
	// --- Factory methods
	// ---
	// --------------------------------------------------------------
	public static LWAuftragPositionData create(final LWAuftragPositionData source) {
		if (source == null || source instanceof LWAuftragPositionDataPojo)
			return source;
		final LWAuftragPositionDataPojo result = new LWAuftragPositionDataPojo();
		result.initFrom(source);
		result.positionKey = source.getPositionKey();
		result.positionKeyAB = source.getPositionKeyAB();
		result.produktKey = source.getProduktKey();
		result.gseLager = source.getGSELager();
		result.produktBezeichnung = source.getProduktBezeichnung();
		result.wunschDatum = source.getWunschDatum();
		result.ltolerCode = source.getLTolerCode();
		result.statusCode = source.getStatusCode();
		result.totBestellMenge = source.getBestellMenge();
		result.totGelieferteMenge = source.getGelieferteMenge();
		result.faktMenge = source.getFaktMenge();
		result.nettoWert = source.getNettoWert();
		result.textVor = source.getTextVor();
		result.textNach = source.getTextNach();
		result.kokCode = source.getKokCode();
		result.terminCD = source.getTerminCd();
		return result;
	}

	// --------------------------------------------------------------
	// ---
	// --- LWAuftragPositionData implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public LWPositionKey getPK() {
		return positionKey;
	}

	@Override
	public LWProduktKey getProduktKey() {
		return produktKey;
	}

	@Override
	public String getGSELager() {
		return gseLager;
	}

	@Override
	public long getWunschDatum() {
		return wunschDatum;
	}

	@Override
	public String getProduktBezeichnung() {
		return produktBezeichnung;
	}

	@Override
	public LWLTolerCode getLTolerCode() {
		return ltolerCode;
	}

	@Override
	public LWPositionStatusCode getStatusCode() {
		return statusCode;
	}

	@Override
	public double getBestellMenge() {
		return totBestellMenge;
	}

	@Override
	public double getGelieferteMenge() {
		return totGelieferteMenge;
	}

	@Override
	public double getFaktMenge() {
		return faktMenge;
	}

	@Override
	public double getNettoWert() {
		return nettoWert;
	}

	@Override
	public LWPositionKey getPositionKeyAB() {
		return positionKeyAB;
	}

	@Override
	public String getTextVor() {
		return textVor;
	}

	@Override
	public String getTextNach() {
		return textNach;
	}

	@Override
	public LWKOKCode getKokCode() { return kokCode; }

	@Override
	public LWTerminCD getTerminCd() { return terminCD; }

	// --------------------------------------------------------------
	// ---
	// --- Streamable
	// ---
	// --------------------------------------------------------------
	@Override
	public int getStreamVersion() {
		return 1;
	}

	@Override
	public void read(final StreamReader reader) throws IOException {
		super.read(reader);
		positionKey = (LWPositionKey) reader.readObject();
		positionKeyAB = (LWPositionKey) reader.readObject();
		produktKey = (LWProduktKey) reader.readObject();
		gseLager = reader.readString(LWStringCaches.GSE_LAGER);
		produktBezeichnung = reader.readString(LWStringCaches.PRODUKTBEZEICHNUNG);
		textVor = reader.readString();
		textNach = reader.readString();
		wunschDatum = reader.readLong();
		totBestellMenge = reader.readDouble();
		totGelieferteMenge = reader.readDouble();
		faktMenge = reader.readDouble();
		nettoWert = reader.readDouble();
		ltolerCode = reader.readCode(LWLTolerCode.class);
		statusCode = reader.readCode(LWPositionStatusCode.class);
		kokCode = reader.readCode(LWKOKCode.class);
		terminCD = reader.readCode(LWTerminCD.class);
	}

	@Override
	public void write(final StreamWriter writer) throws IOException {
		super.write(writer);
		writer.writeObject(positionKey);
		writer.writeObject(positionKeyAB);
		writer.writeObject(produktKey);
		writer.writeString(gseLager, LWStringCaches.GSE_LAGER);
		writer.writeString(produktBezeichnung, LWStringCaches.PRODUKTBEZEICHNUNG);
		writer.writeString(textVor);
		writer.writeString(textNach);
		writer.writeLong(wunschDatum);
		writer.writeDouble(totBestellMenge);
		writer.writeDouble(totGelieferteMenge);
		writer.writeDouble(faktMenge);
		writer.writeDouble(nettoWert);
		writer.writeCode(ltolerCode);
		writer.writeCode(statusCode);
		writer.writeCode(kokCode);
		writer.writeCode(terminCD);
	}

}

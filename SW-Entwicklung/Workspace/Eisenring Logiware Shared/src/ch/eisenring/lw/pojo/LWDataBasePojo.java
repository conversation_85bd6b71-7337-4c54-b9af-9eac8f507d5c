package ch.eisenring.lw.pojo;

import java.io.IOException;

import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.core.io.StreamReader;
import ch.eisenring.core.io.StreamWriter;
import ch.eisenring.core.io.Streamable;
import ch.eisenring.lw.api.LWObjectBaseData;

/**
 * Base class for Logiware data Pojo's
 */
public abstract class LWDataBasePojo implements Streamable, LWObjectBaseData {

	private transient long loadTimestamp;
	private transient long addDate;
	private transient long updDate;
	private transient String addSaBe;
	private transient String updSaBe;

	protected LWDataBasePojo() {
	}

	// --------------------------------------------------------------
	// ---
	// --- Factory methods
	// ---
	// --------------------------------------------------------------
	protected void initFrom(final LWObjectBaseData source) {
		if (source == null) {
			loadTimestamp = TimestampUtil.NULL_TIMESTAMP;
			addDate = TimestampUtil.NULL_TIMESTAMP;
			updDate = TimestampUtil.NULL_TIMESTAMP;
			addSaBe = null;
			updSaBe = null;
		} else {
			loadTimestamp = source.getLoadTimestamp();
			addDate = source.getAddDate();
			updDate = source.getUpdDate();
			addSaBe = source.getAddSaBe();
			updSaBe = source.getUpdSaBe();
		}
	}

	// --------------------------------------------------------------
	// ---
	// --- API
	// ---
	// --------------------------------------------------------------
	/**
	 * Gets the user who created this record. If the record type
	 * does not contain this info, returns NULL.
	 */
	@Override
	public final String getAddSaBe() {
		return addSaBe;
	}
	
	/**
	 * Gets the user who last updated this record. If the record type
	 * does not contain this info, returns NULL.
	 */
	@Override
	public final String getUpdSaBe() {
		return updSaBe;
	}
	
	/**
	 * Gets the timestamp when this record was created. If the record type
	 * does not contain this info, returns NULL_TIMESTAMP.
	 */
	@Override
	public final long getAddDate() {
		return addDate;
	}
	
	/**
	 * Gets the timestamp when this record last updated created. If the record type
	 * does not contain this info, returns NULL_TIMESTAMP.
	 */
	@Override
	public final long getUpdDate() {
		return updDate;
	}

	@Override
	public final long getLoadTimestamp() {
		return loadTimestamp;
	}

	// --------------------------------------------------------------
	// ---
	// --- Streamable
	// ---
	// --------------------------------------------------------------
	@Override
	public abstract int getStreamVersion();

	@Override
	public void read(final StreamReader reader) throws IOException {
		readVersion(reader);
		loadTimestamp = reader.readLong();
		addDate = reader.readLong();
		updDate = reader.readLong();
		addSaBe = reader.readString();
		updSaBe = reader.readString();
	}

	@Override
	public void write(final StreamWriter writer) throws IOException {
		writeVersion(writer);
		writer.writeLong(loadTimestamp);
		writer.writeLong(addDate);
		writer.writeLong(updDate);
		writer.writeString(addSaBe);
		writer.writeString(updSaBe);
	}

	// --------------------------------------------------------------
	// ---
	// --- Object overrides
	// ---
	// --------------------------------------------------------------

	// none

}

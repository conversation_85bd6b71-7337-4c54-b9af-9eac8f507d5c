package ch.eisenring.lw.meta;

import static ch.eisenring.lw.LWConstants.BASIS;
import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.core.util.VarArgs;
import ch.eisenring.logiware.LWSubjektKey;
import ch.eisenring.lw.model.navigable.LWSubjekt;
import ch.eisenring.model.MetaAttribute;
import ch.eisenring.model.MetaClass;
import ch.eisenring.model.MetaClassMember;
import ch.eisenring.model.MetaProperty;
import ch.eisenring.model.PrimaryKeyOrdinal;
import ch.eisenring.model.engine.jdbc.ColumnSpecifier;
import ch.eisenring.model.engine.jdbc.TableSpecifier;
import ch.eisenring.model.factory.AttributeFactory;
import ch.eisenring.model.mapping.POJOField;

public interface LWSubjektMeta {

	TableSpecifier TABLE = TableSpecifier.get(BASIS, "SUBJEKT");
	
	MetaAttribute ATR_SUBJEKTID = AttributeFactory.pInt(
			"SubjektId", 0, 0, Integer.MAX_VALUE,
			ColumnSpecifier.get(TABLE, "Id"),
			PrimaryKeyOrdinal.get(1)); 

	MetaAttribute ATR_ADD_SABE = AttributeFactory.cleanString(
			"addSaBe", null, 10,
			ColumnSpecifier.get(TABLE, "ADD_SABE"),
			POJOField.get(LWSubjekt.class, "addSaBe"));

	MetaAttribute ATR_ADD_DATE = AttributeFactory.datetime(
			"AddDate", TimestampUtil.NULL_TIMESTAMP,
			ColumnSpecifier.get(TABLE, "ADD_DATE"),
			POJOField.get(LWSubjekt.class, "addDate"));
	
	MetaAttribute ATR_UPD_SABE = AttributeFactory.cleanString(
			"updSaBe", null, 10,
			ColumnSpecifier.get(TABLE, "UPD_SABE"),
			POJOField.get(LWSubjekt.class, "updSaBe"));

	MetaAttribute ATR_UPD_DATE = AttributeFactory.datetime(
			"UpdDate", TimestampUtil.NULL_TIMESTAMP,
			ColumnSpecifier.get(TABLE, "UPD_DATE"),
			POJOField.get(LWSubjekt.class, "updDate"));

	MetaAttribute ATR_NAME = AttributeFactory.string(
			"Name", null, 50,
			ColumnSpecifier.get(TABLE, "Name"),
			POJOField.get(LWSubjekt.class, "name"));

	MetaAttribute ATR_ZUSATZNAME = AttributeFactory.string(
			"ZusatzName", null, 35,
			ColumnSpecifier.get(TABLE, "ZusatzName"),
			POJOField.get(LWSubjekt.class, "zusatzName"));

	MetaAttribute ATR_ZUSATZBEZEICHNUNG = AttributeFactory.string(
			"ZusatzBezeichnung", null, 35,
			ColumnSpecifier.get(TABLE, "ZusatzBez"),
			POJOField.get(LWSubjekt.class, "zusatzBezeichnung"));

	MetaAttribute ATR_KUERZEL = AttributeFactory.string(
			"Kuerzel", null, 8,
			ColumnSpecifier.get(TABLE, "Kuerzel"),
			POJOField.get(LWSubjekt.class, "kuerzel"));

	MetaAttribute ATR_KURZBEZEICHNUNG = AttributeFactory.string(
			"Kurzbezeichnung", null, 20,
			ColumnSpecifier.get(TABLE, "KurzBez"),
			POJOField.get(LWSubjekt.class, "kurzBezeichnung"));


	MetaClass METACLASS = new MetaClass("Logiware Subjekt", null,
						LWSubjektKey.class, (MetaClass) null,
						// Attributes
						new VarArgs<MetaProperty>(MetaProperty.class).flat(
								ColumnSpecifier.get(TABLE, "Id"),
								TABLE,
								MetaClassMember.discoverMetaClassMembers(LWSubjektMeta.class)
						));

}

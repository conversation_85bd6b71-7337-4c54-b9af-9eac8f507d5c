package ch.eisenring.lw.meta;

import static ch.eisenring.lw.LWConstants.VERTRIEB;
import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.core.util.VarArgs;
import ch.eisenring.logiware.LWProduktKey;
import ch.eisenring.logiware.code.soft.GSECode;
import ch.eisenring.logiware.code.soft.LWProduktGruppenArtCode;
import ch.eisenring.lw.model.navigable.LWProduktZuordnung;
import ch.eisenring.model.MetaAttribute;
import ch.eisenring.model.MetaClass;
import ch.eisenring.model.MetaClassMember;
import ch.eisenring.model.MetaProperty;
import ch.eisenring.model.PrimaryKeyOrdinal;
import ch.eisenring.model.engine.jdbc.ColumnSpecifier;
import ch.eisenring.model.engine.jdbc.TableSpecifier;
import ch.eisenring.model.factory.AttributeFactory;
import ch.eisenring.model.mapping.POJOField;

public interface LWProduktZuordnungMeta {

	TableSpecifier TABLE = TableSpecifier.get(VERTRIEB, "PRODZORD");
	
	MetaAttribute ATR_PRODUKTNUMMER = AttributeFactory.string(
			"Produktnummer", null, 25,
			ColumnSpecifier.get(TABLE, "ProdNr"),
			PrimaryKeyOrdinal.get(1)); 

	MetaAttribute ATR_GSE = AttributeFactory.code(
			"Id_GSE", GSECode.class, null, 
			ColumnSpecifier.get(TABLE, "Id_GSE"),
			PrimaryKeyOrdinal.get(2));

	MetaAttribute ATR_PRODUKTGRUPPENART = AttributeFactory.code(
			"ProduktGruppenArt", LWProduktGruppenArtCode.class, LWProduktGruppenArtCode.NULL, 
			ColumnSpecifier.get(TABLE, "PRODGrArt"),
			PrimaryKeyOrdinal.get(3));

	MetaAttribute ATR_PRODUKTGRUPPE = AttributeFactory.string(
			"ProduktGruppe", null, 8, 
			ColumnSpecifier.get(TABLE, "Gruppe"),
			PrimaryKeyOrdinal.get(4));
	
	MetaAttribute ATR_ADD_SABE = AttributeFactory.cleanString(
			"AddSaBe", null, 10,
			ColumnSpecifier.get(TABLE, "Add_Sabe"),
			POJOField.get(LWProduktZuordnung.class, "addSaBe"));

	MetaAttribute ATR_ADD_DATE = AttributeFactory.datetime(
			"AddDate", TimestampUtil.NULL_TIMESTAMP,
			ColumnSpecifier.get(TABLE, "Add_Date"),
			POJOField.get(LWProduktZuordnung.class, "addDate"));
	
	MetaAttribute ATR_UPD_SABE = AttributeFactory.cleanString(
			"UpdSaBe", null, 10,
			ColumnSpecifier.get(TABLE, "Upd_Sabe"),
			POJOField.get(LWProduktZuordnung.class, "updSaBe"));

	MetaAttribute ATR_UPD_DATE = AttributeFactory.datetime(
			"UpdDate", TimestampUtil.NULL_TIMESTAMP,
			ColumnSpecifier.get(TABLE, "Upd_Date"),
			POJOField.get(LWProduktZuordnung.class, "updDate"));

	MetaClass METACLASS = new MetaClass("Logiware ProduktZuordnung", null,
			LWProduktKey.class, (MetaClass) null,
			// Attributes
			new VarArgs<MetaProperty>(MetaProperty.class).flat(
					ColumnSpecifier.get(TABLE, "ProdNr"),
					ColumnSpecifier.get(TABLE, "Id_GSE"),
					ColumnSpecifier.get(TABLE, "PRODGrArt"),
					ColumnSpecifier.get(TABLE, "Gruppe"),
					TABLE,
					MetaClassMember.discoverMetaClassMembers(LWProduktZuordnungMeta.class)
			));

}

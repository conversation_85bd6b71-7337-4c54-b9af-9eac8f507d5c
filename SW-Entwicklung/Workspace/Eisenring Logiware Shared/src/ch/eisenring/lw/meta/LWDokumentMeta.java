package ch.eisenring.lw.meta;

import static ch.eisenring.lw.LWConstants.BASIS;
import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.core.util.VarArgs;
import ch.eisenring.logiware.LWDokumentKey;
import ch.eisenring.logiware.code.hard.LWFormularArt;
import ch.eisenring.logiware.code.soft.GSECode;
import ch.eisenring.lw.model.navigable.LWDokument;
import ch.eisenring.model.MetaAttribute;
import ch.eisenring.model.MetaClass;
import ch.eisenring.model.MetaClassMember;
import ch.eisenring.model.MetaProperty;
import ch.eisenring.model.PrimaryKeyOrdinal;
import ch.eisenring.model.engine.jdbc.ColumnSpecifier;
import ch.eisenring.model.engine.jdbc.TableSpecifier;
import ch.eisenring.model.factory.AttributeFactory;
import ch.eisenring.model.mapping.POJOField;

public interface LWDokumentMeta {

	TableSpecifier TABLE = TableSpecifier.get(BASIS, "DOKUMENT");
	
	ColumnSpecifier PKCOL = ColumnSpecifier.get(TABLE, "DokId"); 

	MetaAttribute ATR_DOKUMENTID = AttributeFactory.pInt(
			"DokumentId", 0, Integer.MIN_VALUE, Integer.MAX_VALUE,
			PKCOL, PrimaryKeyOrdinal.O1); 

	MetaAttribute ATR_GSE = AttributeFactory.code(
			"GSE", GSECode.class, GSECode.NULL,
			ColumnSpecifier.get(TABLE, "Id_GSE"),
			POJOField.get(LWDokument.class, "gse"));

	MetaAttribute ATR_FOMULARART = AttributeFactory.code(
			"FomularArt", LWFormularArt.class, LWFormularArt.NULL,
			ColumnSpecifier.get(TABLE, "FormArt"),
			POJOField.get(LWDokument.class, "formularArt"));

	MetaAttribute ATR_FOMULARNR = AttributeFactory.pInt(
			"FomularNr", 0, Integer.MIN_VALUE, Integer.MAX_VALUE,
			ColumnSpecifier.get(TABLE, "FormNr"),
			POJOField.get(LWDokument.class, "formularNr"));

	MetaAttribute ATR_AUSGABEORT1 = AttributeFactory.cleanString(
			"AusgabeOrt1", null, 3,
			ColumnSpecifier.get(TABLE, "AusgabeOrt1"),
			POJOField.get(LWDokument.class, "ausgabeOrt1"));

	// NOTE: this is an Integer in the database, but it is *meant* to be a code key
	//       the ORM can't handle *that* properly, so the attribute must be declared
	//       as an Integer and conversion handled explicitly in the getter for the
	//       attribute in the POJO.
	MetaAttribute ATR_MSSUBTYP = AttributeFactory.wInteger(
			"MontagescheinSubTyp", null, Integer.MIN_VALUE, Integer.MAX_VALUE,
			ColumnSpecifier.get(TABLE, "Anzahl_Rektifikat"),
			POJOField.get(LWDokument.class, "msSubTyp"));

	MetaAttribute ATR_FORMULARDATUM = AttributeFactory.datetime(
			"FormDatum", TimestampUtil.NULL_TIMESTAMP,
			ColumnSpecifier.get(TABLE, "FormDatum"),
			POJOField.get(LWDokument.class, "formularDatum"));

	MetaAttribute ATR_AUFTRAGNUMMER = AttributeFactory.pInt(
			"AuftragNummer", 0,
			ColumnSpecifier.get(TABLE, "BelegNr"),
			POJOField.get(LWDokument.class, "belegNr"));
			
	MetaClass METACLASS = new MetaClass("Logiware Dokument", null,
						LWDokumentKey.class, (MetaClass) null,
						// Attributes
						new VarArgs<MetaProperty>(MetaProperty.class).flat(
								TABLE, PKCOL,
								MetaClassMember.discoverMetaClassMembers(LWDokumentMeta.class)
						));

}

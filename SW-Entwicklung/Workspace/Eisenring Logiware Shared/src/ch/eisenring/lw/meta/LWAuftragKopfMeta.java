package ch.eisenring.lw.meta;

import static ch.eisenring.lw.LWConstants.VERTRIEB;
import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.core.util.VarArgs;
import ch.eisenring.logiware.LWAuftragKey;
import ch.eisenring.logiware.code.hard.LWFixterminCode;
import ch.eisenring.logiware.code.hard.LWPlanungStatusCode;
import ch.eisenring.logiware.code.soft.*;
import ch.eisenring.lw.model.navigable.LWAuftragKopf;
import ch.eisenring.lw.stringcache.LWStringCaches;
import ch.eisenring.model.MetaAttribute;
import ch.eisenring.model.MetaClass;
import ch.eisenring.model.MetaClassMember;
import ch.eisenring.model.MetaProperty;
import ch.eisenring.model.PrimaryKeyOrdinal;
import ch.eisenring.model.engine.jdbc.ColumnSpecifier;
import ch.eisenring.model.engine.jdbc.TableSpecifier;
import ch.eisenring.model.factory.AttributeFactory;
import ch.eisenring.model.mapping.POJOField;

public interface LWAuftragKopfMeta {

	TableSpecifier TABLE = TableSpecifier.get(VERTRIEB, "AuftragsKopfZusatz");

	ColumnSpecifier PKCOL1 = ColumnSpecifier.get(TABLE, "AuftrNr");
	ColumnSpecifier PKCOL2 = ColumnSpecifier.get(TABLE, "Id_GSE");
	
	MetaAttribute ATR_AUFTRAGNUMMER = AttributeFactory.pInt(
			"Auftragnummer", 0, Integer.MIN_VALUE, Integer.MAX_VALUE,
			PKCOL1, PrimaryKeyOrdinal.O1);

	MetaAttribute ATR_GSE_AUFTRAG = AttributeFactory.code(
			"GSEAuftrag", GSECode.class, null,
			PKCOL2, PrimaryKeyOrdinal.O2);
	
	MetaAttribute ATR_ABSCHLUSSDATUM = AttributeFactory.date(
			"Küchen-Ende", TimestampUtil.NULL_TIMESTAMP,
			ColumnSpecifier.get(TABLE, "MDatum1"),
			POJOField.get(LWAuftragKopf.class, "abschlussDatum"));

	MetaAttribute ATR_GRANIT1CODE = AttributeFactory.code(
			"Granitschlüssel Block I", LWGranit1Code.class, null,
			ColumnSpecifier.get(TABLE, "SCH_BLOCK1"),
			POJOField.get(LWAuftragKopf.class, "granit1"));
	
	MetaAttribute ATR_GRANIT2CODE = AttributeFactory.code(
			"Granitschlüssel Block II", LWGranit2Code.class, null,
			ColumnSpecifier.get(TABLE, "SCH_BLOCK2"),
			POJOField.get(LWAuftragKopf.class, "granit2"));
	
	MetaAttribute ATR_GRANIT3CODE = AttributeFactory.code(
			"Granitschlüssel Block III", LWGranit3Code.class, null,
			ColumnSpecifier.get(TABLE, "SCH_BLOCK3"),
			POJOField.get(LWAuftragKopf.class, "granit3"));
	
	MetaAttribute ATR_GRANIT4CODE = AttributeFactory.code(
			"Granitschlüssel Block IV", LWGranit4Code.class, null,
			ColumnSpecifier.get(TABLE, "SCH_BLOCK4"),
			POJOField.get(LWAuftragKopf.class, "granit4"));
	
	MetaAttribute ATR_GRANIT5CODE = AttributeFactory.code(
			"Granitschlüssel Block V", LWGranit5Code.class, null,
			ColumnSpecifier.get(TABLE, "SCH_BLOCK5"),
			POJOField.get(LWAuftragKopf.class, "granit5"));
	
	MetaAttribute ATR_GRANIT6CODE = AttributeFactory.code(
			"Granitschlüssel Block VI", LWGranit6Code.class, null,
			ColumnSpecifier.get(TABLE, "SCH_BLOCK6"),
			POJOField.get(LWAuftragKopf.class, "granit6"));
		
	MetaAttribute ATR_GRANIT7CODE = AttributeFactory.code(
			"Granitschlüssel Block VII", LWGranit7Code.class, null,
			ColumnSpecifier.get(TABLE, "SCH_BLOCK7"),
			POJOField.get(LWAuftragKopf.class, "granit7"));
	
	MetaAttribute ATR_GRANIT8CODE = AttributeFactory.code(
			"Granitschlüssel Block VIII", LWGranit8Code.class, null,
			ColumnSpecifier.get(TABLE, "SCH_BLOCK8"),
			POJOField.get(LWAuftragKopf.class, "granit8"));
	
	MetaAttribute ATR_GRANIT9CODE = AttributeFactory.code(
			"Granitschlüssel Block IX", LWGranit9Code.class, null,
			ColumnSpecifier.get(TABLE, "SCH_BLOCK9"),
			POJOField.get(LWAuftragKopf.class, "granit9"));
	
	MetaAttribute ATR_GRANITACODE = AttributeFactory.code(
			"Granitschlüssel Block X", LWGranitACode.class, null,
			ColumnSpecifier.get(TABLE, "SCH_BLOCKA"),
			POJOField.get(LWAuftragKopf.class, "granitA"));

	MetaAttribute ATR_GRANITBCODE = AttributeFactory.code(
			"Granitschlüssel Block XI", LWGranitBCode.class, null,
			ColumnSpecifier.get(TABLE, "SCH_BLOCKB"),
			POJOField.get(LWAuftragKopf.class, "granitB"));

	MetaAttribute ATR_GRANITCCODE = AttributeFactory.code(
			"Granitschlüssel Block XII", LWGranitCCode.class, null,
			ColumnSpecifier.get(TABLE, "SCH_BLOCKC"),
			POJOField.get(LWAuftragKopf.class, "granitC"));
	
	MetaAttribute ATR_DISPONENTSIR = AttributeFactory.code(
			"DisponentSIR", LWDisponentSirCode.class, null,
			ColumnSpecifier.get(TABLE, "DISPONENTSIR"),
			POJOField.get(LWAuftragKopf.class, "disponentSIR"));
	
	MetaAttribute ATR_ABBRUCHDATUM = AttributeFactory.logiwareDate(
			"Abbruchdatum", TimestampUtil.NULL_TIMESTAMP,
			ColumnSpecifier.get(TABLE, "AZusatzSDatum1"),
			POJOField.get(LWAuftragKopf.class, "abbruchDatum"));
	
	MetaAttribute ATR_SPEZIALDATUM = AttributeFactory.logiwareDate(
			"Spezialdatum", TimestampUtil.NULL_TIMESTAMP,
			ColumnSpecifier.get(TABLE, "AZusatzSDatum2"),
			POJOField.get(LWAuftragKopf.class, "spezialDatum"));
	
	MetaAttribute ATR_GRANITSTARTDATUM = AttributeFactory.date(
			"GranitmontageAnfang", TimestampUtil.NULL_TIMESTAMP,
			ColumnSpecifier.get(TABLE, "MDatum2"),
			POJOField.get(LWAuftragKopf.class, "granitStart"));
	
	MetaAttribute ATR_GRANITENDEDATUM = AttributeFactory.date(
			"GranitmontageEnde", TimestampUtil.NULL_TIMESTAMP,
			ColumnSpecifier.get(TABLE, "MDatum3"),
			POJOField.get(LWAuftragKopf.class, "granitEnde"));

	MetaAttribute ATR_BEZUGSDATUM = AttributeFactory.date(
			"Bezugsdatum", TimestampUtil.NULL_TIMESTAMP,
			ColumnSpecifier.get(TABLE, "MDatum4"),
			POJOField.get(LWAuftragKopf.class, "bezugsDatum"));

	MetaAttribute ATR_DIENSTSIR = AttributeFactory.code(
			"Dientleistung Sirnach", LWDienstSirCode.class, null,
			ColumnSpecifier.get(TABLE, "DienstSir"),
			POJOField.get(LWAuftragKopf.class, "dienstSIR"));

	MetaAttribute ATR_KUECHENTYP = AttributeFactory.code(
			"Küchentyp", LWKuechenTypCode.class, null,
			ColumnSpecifier.get(TABLE, "KUECHEN"),
			POJOField.get(LWAuftragKopf.class, "kuechenTyp"));
	
	MetaAttribute ATR_MONTEURSIR = AttributeFactory.code(
			"Monteur Sirnach", LWMonteurSirCode.class, null,
			ColumnSpecifier.get(TABLE, "MonteurSir"),
			POJOField.get(LWAuftragKopf.class, "monteurSIR"));
	
	MetaAttribute ATR_MONTEURPFYN = AttributeFactory.code(
			"Monteur Pfyn", LWMonteurPfyCode.class, null,
			ColumnSpecifier.get(TABLE, "MonteurPfy"),
			POJOField.get(LWAuftragKopf.class, "monteurPFY"));

	MetaAttribute ATR_PLANUNGSTATUS = AttributeFactory.code(
			"Planungs-Status", LWPlanungStatusCode.class, null,
			ColumnSpecifier.get(TABLE, "Planung"),
			POJOField.get(LWAuftragKopf.class, "planungStatus"));

	MetaAttribute ATR_DIENST1PFY = AttributeFactory.code(
			"Dienstleistung 1 Pfyn", LWDienst1PfyCode.class, null,
			ColumnSpecifier.get(TABLE, "Dienst1PFY"),
			POJOField.get(LWAuftragKopf.class, "dienstPFY1"));
	
	MetaAttribute ATR_DIENST2PFY = AttributeFactory.code(
			"Dienstleistung 2 Pfyn", LWDienst2PfyCode.class, null,
			ColumnSpecifier.get(TABLE, "Dienst2PFY"),
			POJOField.get(LWAuftragKopf.class, "dienstPFY2"));

	MetaAttribute ATR_DIENST3PFY = AttributeFactory.code(
			"Dienstleistung 3 Pfyn", LWDienst3PfyCode.class, null,
			ColumnSpecifier.get(TABLE, "Dienst3PFY"),
			POJOField.get(LWAuftragKopf.class, "dienstPFY3"));

	MetaAttribute ATR_DIENST4PFY = AttributeFactory.code(
			"Dienstleistung 4 Pfyn", LWDienst4PfyCode.class, null,
			ColumnSpecifier.get(TABLE, "Dienst4PFY"),
			POJOField.get(LWAuftragKopf.class, "dienstPFY4"));
	
	MetaAttribute ATR_DIENST5PFY = AttributeFactory.code(
			"Dienstleistung 5 Pfyn", LWDienst5PfyCode.class, null,
			ColumnSpecifier.get(TABLE, "Dienst5PFY"),
			POJOField.get(LWAuftragKopf.class, "dienstPFY5"));

	MetaAttribute ATR_BEMERKUNGEN = AttributeFactory.cleanString(
			"Bemerkungen", null, 255,
			ColumnSpecifier.get(TABLE, "AZusatzSInfo"),
			POJOField.get(LWAuftragKopf.class, "bemerkungen"));
	
	MetaAttribute ATR_AUSFUEHRUNG = AttributeFactory.cleanString(
			"Ausführung", null, 40,
			ColumnSpecifier.get(TABLE, "AZusatzSAusf"),
			POJOField.get(LWAuftragKopf.class, "ausfuehrung"));
	
	MetaAttribute ATR_ABDECKUNG = AttributeFactory.cleanString(
			"Abdeckung", null, 40,
			ColumnSpecifier.get(TABLE, "AZusatzSAbd"),
			POJOField.get(LWAuftragKopf.class, "abdeckung"));

	MetaAttribute ATR_INFOMONTEUR = AttributeFactory.cleanString(
			"Info Monteur", null, 255,
			ColumnSpecifier.get(TABLE, "Bemerk1"),
			POJOField.get(LWAuftragKopf.class, "infoMonteur"));
	
	MetaAttribute ATR_BEMERKUNGENMONTEUR = AttributeFactory.cleanString(
			"Bemerkungen Monteur", null, 255,
			ColumnSpecifier.get(TABLE, "Bemerk2"),
			POJOField.get(LWAuftragKopf.class, "bemerkungenMonteur"));
	
	MetaAttribute ATR_INFOMASSE = AttributeFactory.cleanString(
			"Info Masse", null, 40,
			ColumnSpecifier.get(TABLE, "InfoMasse"),
			POJOField.get(LWAuftragKopf.class, "infoMasse"));
	
	MetaAttribute ATR_TERMINZEIT = AttributeFactory.cleanString(
			"Termin Zeit", null, 5,
			ColumnSpecifier.get(TABLE, "ZeitA"),
			POJOField.get(LWAuftragKopf.class, "terminZeit"));
	
	MetaAttribute ATR_KONTAKTPERSON = AttributeFactory.cleanString(
			"Kontaktperson", null, 80,
			ColumnSpecifier.get(TABLE, "KontaktP"),
			POJOField.get(LWAuftragKopf.class, "kontaktPerson"));
	
	MetaAttribute ATR_FIXTERMIN = AttributeFactory.code(
			"Fixtermin", LWFixterminCode.class, LWFixterminCode.NULL,
			ColumnSpecifier.get(TABLE, "AZusatzSCode"),
			POJOField.get(LWAuftragKopf.class, "fixTermin"));

	MetaAttribute ATR_GRANITMONTAGE = AttributeFactory.code(
			"Granitmontage", LWGranitMontageCode.class, null,
			ColumnSpecifier.get(TABLE, "GranitMontage"),
			POJOField.get(LWAuftragKopf.class, "granitMontage"));

	MetaAttribute ATR_MASSKONTROLLE = AttributeFactory.code(
			"Masskontrolle", LWMassKontrolleCode.class, null,
			ColumnSpecifier.get(TABLE, "MassKontID"),
			POJOField.get(LWAuftragKopf.class, "massKontrolleCode"));

	MetaAttribute ATR_ZAHLUNGSBEDINGUNGEN = AttributeFactory.cleanString(
			"Zahlungsbedingungen", null, 250, 
			ColumnSpecifier.get(TABLE, "Tx1401_4"),
			POJOField.get(LWAuftragKopf.class, "zahlungsBedingungen"));

	MetaAttribute ATR_DB_RECHWERT = AttributeFactory.wDouble(
			"DB_Rechwert", (Double) null,
			ColumnSpecifier.get(TABLE, "DB_Rechwert"),
			POJOField.get(LWAuftragKopf.class, "dbRechwert"));

	MetaAttribute ATR_DB_EKSIR = AttributeFactory.wDouble(
			"DB_EKSIR", (Double) null,
			ColumnSpecifier.get(TABLE, "DB_EKSIR"),
			POJOField.get(LWAuftragKopf.class, "dbEKSIR"));

	MetaAttribute ATR_DB_KOMONTAGE = AttributeFactory.wDouble(
			"DB_KoMontage", (Double) null,
			ColumnSpecifier.get(TABLE, "DB_KoMontage"),
			POJOField.get(LWAuftragKopf.class, "dbKoMontage"));

	MetaAttribute ATR_DB_KOMONTAGEAWA = AttributeFactory.wDouble(
			"DB_KoMontageAWA", (Double) null,
			ColumnSpecifier.get(TABLE, "DB_SummeMoAuftraege"),
			POJOField.get(LWAuftragKopf.class, "dbKoMontageAWA"));
	
	MetaAttribute ATR_DB_PROVRECHWERT = AttributeFactory.wDouble(
			"ProvRechWert", (Double) null,
			ColumnSpecifier.get(TABLE, "Prov_Rechwert"),
			POJOField.get(LWAuftragKopf.class, "provRechWert"));

	MetaAttribute ATR_KUECHENBESTELLUNG_DATUM = AttributeFactory.date(
			"KuechenbestellungDatum", TimestampUtil.NULL_TIMESTAMP, 
			ColumnSpecifier.get(TABLE, "AZusatzPDatum1"),
			POJOField.get(LWAuftragKopf.class, "kuechenBestellungDatum"));

	MetaAttribute ATR_KUECHENBESTELLUNG_BELEGNR = AttributeFactory.wInteger(
			"KuechenbestellungBelegnummer", null, 
			ColumnSpecifier.get(TABLE, "AZusatzPStk"),
			POJOField.get(LWAuftragKopf.class, "kuechenBestellungBelegNr"));

	MetaAttribute ATR_TOTBETRAG = AttributeFactory.wDouble(
			"TOTBETRAG", null,
			ColumnSpecifier.get(TABLE, "TOTBETRAG"),
			POJOField.get(LWAuftragKopf.class, "totBetrag"));

	MetaAttribute ATR_BPREIS = AttributeFactory.wDouble(
			"BPreis", null,
			ColumnSpecifier.get(TABLE, "BPreis"),
			POJOField.get(LWAuftragKopf.class, "bPreis"));

	MetaAttribute ATR_MPREIS = AttributeFactory.wDouble(
			"MPreis", null,
			ColumnSpecifier.get(TABLE, "MPreis"),
			POJOField.get(LWAuftragKopf.class, "mPreis"));

	MetaAttribute ATR_B2BCode = AttributeFactory.code(
			"B2BCode", LWB2BCode.class, null,
			ColumnSpecifier.get(TABLE, "B2BCode"),
			POJOField.get(LWAuftragKopf.class, "b2bCode"));

	MetaAttribute ATR_AVERSDATUM = AttributeFactory.datetime(
			"AversDatum", TimestampUtil.NULL_TIMESTAMP,
			ColumnSpecifier.get(TABLE, "AVers_Datum"),
			POJOField.get(LWAuftragKopf.class, "aversDatum"));

	MetaAttribute ATR_ABESTDATUM = AttributeFactory.datetime(
			"AbestDatum", TimestampUtil.NULL_TIMESTAMP,
			ColumnSpecifier.get(TABLE, "ABest_Datum"),
			POJOField.get(LWAuftragKopf.class, "abestDatum"));

	MetaAttribute ATR_EINGANGAB = AttributeFactory.datetime(
			"eingangAB", TimestampUtil.NULL_TIMESTAMP,
			ColumnSpecifier.get(TABLE, "BE_Bestellnr_Timestamp"),
			POJOField.get(LWAuftragKopf.class, "eingangAB"));

	MetaAttribute ATR_DECKBLATT01 = AttributeFactory.cleanString("Deckblatt01", null, 50, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt01"), ColumnSpecifier.get(TABLE, "Tx1423_1"));
	MetaAttribute ATR_DECKBLATT02 = AttributeFactory.cleanString("Deckblatt02", null, 50, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt02"), ColumnSpecifier.get(TABLE, "Tx1423_2"));
	MetaAttribute ATR_DECKBLATT03 = AttributeFactory.cleanString("Deckblatt03", null, 50, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt03"), ColumnSpecifier.get(TABLE, "Tx1423_3"));
	MetaAttribute ATR_DECKBLATT04 = AttributeFactory.cleanString("Deckblatt04", null, 50, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt04"), ColumnSpecifier.get(TABLE, "Tx1423_4"));
	MetaAttribute ATR_DECKBLATT05 = AttributeFactory.cleanString("Deckblatt05", null, 50, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt05"), ColumnSpecifier.get(TABLE, "Tx1423_5"));
	MetaAttribute ATR_DECKBLATT06 = AttributeFactory.cleanString("Deckblatt06", null, 50, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt06"), ColumnSpecifier.get(TABLE, "Tx1423_6"));
	MetaAttribute ATR_DECKBLATT07 = AttributeFactory.cleanString("Deckblatt07", null, 50, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt07"), ColumnSpecifier.get(TABLE, "Tx1423_7"));
	MetaAttribute ATR_DECKBLATT08 = AttributeFactory.cleanString("Deckblatt08", null, 50, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt08"), ColumnSpecifier.get(TABLE, "Tx1423_8"));
	MetaAttribute ATR_DECKBLATT09 = AttributeFactory.cleanString("Deckblatt09", null, 50, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt09"), ColumnSpecifier.get(TABLE, "Tx1423_9"));
	MetaAttribute ATR_DECKBLATT10 = AttributeFactory.cleanString("Deckblatt10", null, 50, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt10"), ColumnSpecifier.get(TABLE, "Tx1423_10"));
	MetaAttribute ATR_DECKBLATT11 = AttributeFactory.cleanString("Deckblatt11", null, 50, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt11"), ColumnSpecifier.get(TABLE, "Tx1423_11"));
	MetaAttribute ATR_DECKBLATT12 = AttributeFactory.cleanString("Deckblatt12", null, 50, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt12"), ColumnSpecifier.get(TABLE, "Tx1423_12"));
	MetaAttribute ATR_DECKBLATT13 = AttributeFactory.cleanString("Deckblatt13", null, 50, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt13"), ColumnSpecifier.get(TABLE, "Tx1423_13"));
	MetaAttribute ATR_DECKBLATT14 = AttributeFactory.cleanString("Deckblatt14", null, 50, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt14"), ColumnSpecifier.get(TABLE, "Tx1423_14"));
	MetaAttribute ATR_DECKBLATT15 = AttributeFactory.cleanString("Deckblatt15", null, 50, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt15"), ColumnSpecifier.get(TABLE, "Tx1423_15"));
	MetaAttribute ATR_DECKBLATT16 = AttributeFactory.cleanString("Deckblatt16", null, 50, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt16"), ColumnSpecifier.get(TABLE, "Tx1423_16"));
	MetaAttribute ATR_DECKBLATT17 = AttributeFactory.cleanString("Deckblatt17", null, 50, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt17"), ColumnSpecifier.get(TABLE, "Tx1423_17"));
	MetaAttribute ATR_DECKBLATT18 = AttributeFactory.cleanString("Deckblatt18", null, 50, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt18"), ColumnSpecifier.get(TABLE, "Tx1423_18"));
	MetaAttribute ATR_DECKBLATT19 = AttributeFactory.cleanString("Deckblatt19", null, 50, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt19"), ColumnSpecifier.get(TABLE, "Tx1423_19"));
	MetaAttribute ATR_DECKBLATT20 = AttributeFactory.cleanString("Deckblatt20", null, 50, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt20"), ColumnSpecifier.get(TABLE, "Tx1423_20"));
	MetaAttribute ATR_DECKBLATT21 = AttributeFactory.cleanString("Deckblatt21", null, 40, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt21"), ColumnSpecifier.get(TABLE, "Tx1423_21"));
	MetaAttribute ATR_DECKBLATT22 = AttributeFactory.cleanString("Deckblatt22", null, 40, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt22"), ColumnSpecifier.get(TABLE, "Tx1423_22"));
	MetaAttribute ATR_DECKBLATT23 = AttributeFactory.cleanString("Deckblatt23", null, 40, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt23"), ColumnSpecifier.get(TABLE, "Tx1423_23"));
	MetaAttribute ATR_DECKBLATT24 = AttributeFactory.cleanString("Deckblatt24", null, 40, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt24"), ColumnSpecifier.get(TABLE, "Tx1423_24"));
	MetaAttribute ATR_DECKBLATT25 = AttributeFactory.cleanString("Deckblatt25", null, 40, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt25"), ColumnSpecifier.get(TABLE, "Tx1423_25"));
	MetaAttribute ATR_DECKBLATT26 = AttributeFactory.cleanString("Deckblatt26", null, 40, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt26"), ColumnSpecifier.get(TABLE, "Tx1423_26"));
	MetaAttribute ATR_DECKBLATT27 = AttributeFactory.cleanString("Deckblatt27", null, 40, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt27"), ColumnSpecifier.get(TABLE, "Tx1423_27"));
	MetaAttribute ATR_DECKBLATT28 = AttributeFactory.cleanString("Deckblatt28", null, 40, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt28"), ColumnSpecifier.get(TABLE, "Tx1423_28"));
	MetaAttribute ATR_DECKBLATT29 = AttributeFactory.cleanString("Deckblatt29", null, 40, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt29"), ColumnSpecifier.get(TABLE, "Tx1423_29"));
	MetaAttribute ATR_DECKBLATT30 = AttributeFactory.cleanString("Deckblatt30", null, 40, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt30"), ColumnSpecifier.get(TABLE, "Tx1423_30"));
	MetaAttribute ATR_DECKBLATT31 = AttributeFactory.cleanString("Deckblatt31", null, 40, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt31"), ColumnSpecifier.get(TABLE, "Tx1423_31"));
	MetaAttribute ATR_DECKBLATT32 = AttributeFactory.cleanString("Deckblatt32", null, 40, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt32"), ColumnSpecifier.get(TABLE, "Tx1423_32"));
	MetaAttribute ATR_DECKBLATT33 = AttributeFactory.cleanString("Deckblatt33", null, 40, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt33"), ColumnSpecifier.get(TABLE, "Tx1423_33"));
	MetaAttribute ATR_DECKBLATT34 = AttributeFactory.cleanString("Deckblatt34", null, 40, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt34"), ColumnSpecifier.get(TABLE, "Tx1423_34"));
	MetaAttribute ATR_DECKBLATT35 = AttributeFactory.cleanString("Deckblatt35", null, 40, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt35"), ColumnSpecifier.get(TABLE, "Tx1423_35"));
	MetaAttribute ATR_DECKBLATT36 = AttributeFactory.cleanString("Deckblatt36", null, 40, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt36"), ColumnSpecifier.get(TABLE, "Tx1423_36"));
	MetaAttribute ATR_DECKBLATT37 = AttributeFactory.cleanString("Deckblatt37", null, 40, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt37"), ColumnSpecifier.get(TABLE, "Tx1423_37"));
	MetaAttribute ATR_DECKBLATT38 = AttributeFactory.cleanString("Deckblatt38", null, 40, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt38"), ColumnSpecifier.get(TABLE, "Tx1423_38"));
	MetaAttribute ATR_DECKBLATT39 = AttributeFactory.cleanString("Deckblatt39", null, 40, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt39"), ColumnSpecifier.get(TABLE, "Tx1423_39"));
	MetaAttribute ATR_DECKBLATT40 = AttributeFactory.cleanString("Deckblatt40", null, 40, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt40"), ColumnSpecifier.get(TABLE, "Tx1423_40"));
	MetaAttribute ATR_DECKBLATT41 = AttributeFactory.cleanString("Deckblatt41", null, 40, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt41"), ColumnSpecifier.get(TABLE, "Tx1423_41"));
	MetaAttribute ATR_DECKBLATT42 = AttributeFactory.cleanString("Deckblatt42", null, 40, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt42"), ColumnSpecifier.get(TABLE, "Tx1423_42"));
	MetaAttribute ATR_DECKBLATT43 = AttributeFactory.cleanString("Deckblatt43", null, 40, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt43"), ColumnSpecifier.get(TABLE, "Tx1423_43"));
	MetaAttribute ATR_DECKBLATT44 = AttributeFactory.cleanString("Deckblatt44", null, 40, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt44"), ColumnSpecifier.get(TABLE, "Tx1423_44"));
	MetaAttribute ATR_DECKBLATT45 = AttributeFactory.cleanString("Deckblatt45", null, 40, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt45"), ColumnSpecifier.get(TABLE, "Tx1423_45"));
	MetaAttribute ATR_DECKBLATT46 = AttributeFactory.cleanString("Deckblatt46", null, 40, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt46"), ColumnSpecifier.get(TABLE, "Tx1423_46"));
	MetaAttribute ATR_DECKBLATT47 = AttributeFactory.cleanString("Deckblatt47", null, 40, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt47"), ColumnSpecifier.get(TABLE, "Tx1423_47"));
	MetaAttribute ATR_DECKBLATT48 = AttributeFactory.cleanString("Deckblatt48", null, 40, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt48"), ColumnSpecifier.get(TABLE, "Tx1423_48"));
	MetaAttribute ATR_DECKBLATT49 = AttributeFactory.cleanString("Deckblatt49", null, 40, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt49"), ColumnSpecifier.get(TABLE, "Tx1423_49"));
	MetaAttribute ATR_DECKBLATT50 = AttributeFactory.cleanString("Deckblatt50", null, 40, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt50"), ColumnSpecifier.get(TABLE, "Tx1423_50"));
	MetaAttribute ATR_DECKBLATT51 = AttributeFactory.cleanString("Deckblatt51", null, 40, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt51"), ColumnSpecifier.get(TABLE, "Tx1423_51"));
	MetaAttribute ATR_DECKBLATT52 = AttributeFactory.cleanString("Deckblatt52", null, 40, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt52"), ColumnSpecifier.get(TABLE, "Tx1423_52"));
	MetaAttribute ATR_DECKBLATT53 = AttributeFactory.cleanString("Deckblatt53", null, 40, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt53"), ColumnSpecifier.get(TABLE, "Tx1423_53"));
	MetaAttribute ATR_DECKBLATT54 = AttributeFactory.cleanString("Deckblatt54", null, 40, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt54"), ColumnSpecifier.get(TABLE, "Tx1423_54"));
	MetaAttribute ATR_DECKBLATT55 = AttributeFactory.cleanString("Deckblatt55", null, 40, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt55"), ColumnSpecifier.get(TABLE, "Tx1423_55"));
	MetaAttribute ATR_DECKBLATT56 = AttributeFactory.cleanString("Deckblatt56", null, 40, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt56"), ColumnSpecifier.get(TABLE, "Tx1423_56"));
	MetaAttribute ATR_DECKBLATT57 = AttributeFactory.cleanString("Deckblatt57", null, 40, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt57"), ColumnSpecifier.get(TABLE, "Tx1423_57"));
	MetaAttribute ATR_DECKBLATT58 = AttributeFactory.cleanString("Deckblatt58", null, 40, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt58"), ColumnSpecifier.get(TABLE, "Tx1423_58"));
	MetaAttribute ATR_DECKBLATT59 = AttributeFactory.cleanString("Deckblatt59", null, 40, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt59"), ColumnSpecifier.get(TABLE, "Tx1423_59"));
	MetaAttribute ATR_DECKBLATT60 = AttributeFactory.cleanString("Deckblatt60", null, 40, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt60"), ColumnSpecifier.get(TABLE, "Tx1423_60"));
	MetaAttribute ATR_DECKBLATT61 = AttributeFactory.cleanString("Deckblatt61", null, 40, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt61"), ColumnSpecifier.get(TABLE, "Tx1423_61"));
	MetaAttribute ATR_DECKBLATT62 = AttributeFactory.cleanString("Deckblatt62", null, 40, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt62"), ColumnSpecifier.get(TABLE, "Tx1423_62"));
	MetaAttribute ATR_DECKBLATT63 = AttributeFactory.cleanString("Deckblatt63", null, 40, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt63"), ColumnSpecifier.get(TABLE, "Tx1423_63"));
	MetaAttribute ATR_DECKBLATT64 = AttributeFactory.cleanString("Deckblatt64", null, 40, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt64"), ColumnSpecifier.get(TABLE, "Tx1423_64"));
	MetaAttribute ATR_DECKBLATT65 = AttributeFactory.cleanString("Deckblatt65", null, 40, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt65"), ColumnSpecifier.get(TABLE, "Tx1423_65"));
	MetaAttribute ATR_DECKBLATT66 = AttributeFactory.cleanString("Deckblatt66", null, 40, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt66"), ColumnSpecifier.get(TABLE, "Tx1423_66"));
	MetaAttribute ATR_DECKBLATT67 = AttributeFactory.cleanString("Deckblatt67", null, 40, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt67"), ColumnSpecifier.get(TABLE, "Tx1423_67"));
	MetaAttribute ATR_DECKBLATT68 = AttributeFactory.cleanString("Deckblatt68", null, 40, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt68"), ColumnSpecifier.get(TABLE, "Tx1423_68"));
	MetaAttribute ATR_DECKBLATT69 = AttributeFactory.cleanString("Deckblatt69", null, 40, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt69"), ColumnSpecifier.get(TABLE, "Tx1423_69"));
	MetaAttribute ATR_DECKBLATT70 = AttributeFactory.cleanString("Deckblatt70", null, 40, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt70"), ColumnSpecifier.get(TABLE, "Tx1423_70"));
	MetaAttribute ATR_DECKBLATT71 = AttributeFactory.cleanString("Deckblatt71", null, 40, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt71"), ColumnSpecifier.get(TABLE, "Tx1423_71"));
	MetaAttribute ATR_DECKBLATT72 = AttributeFactory.cleanString("Deckblatt72", null, 40, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt72"), ColumnSpecifier.get(TABLE, "Tx1423_72"));
	MetaAttribute ATR_DECKBLATT73 = AttributeFactory.cleanString("Deckblatt73", null, 40, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt73"), ColumnSpecifier.get(TABLE, "Tx1423_73"));
	MetaAttribute ATR_DECKBLATT74 = AttributeFactory.cleanString("Deckblatt74", null, 40, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt74"), ColumnSpecifier.get(TABLE, "Tx1423_74"));
	MetaAttribute ATR_DECKBLATT75 = AttributeFactory.cleanString("Deckblatt75", null, 40, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt75"), ColumnSpecifier.get(TABLE, "Tx1423_75"));
	MetaAttribute ATR_DECKBLATT76 = AttributeFactory.cleanString("Deckblatt76", null, 40, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt76"), ColumnSpecifier.get(TABLE, "Tx1423_76"));
	MetaAttribute ATR_DECKBLATT77 = AttributeFactory.cleanString("Deckblatt77", null, 40, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt77"), ColumnSpecifier.get(TABLE, "Tx1423_77"));
	MetaAttribute ATR_DECKBLATT78 = AttributeFactory.cleanString("Deckblatt78", null, 40, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt78"), ColumnSpecifier.get(TABLE, "Tx1423_78"));
	MetaAttribute ATR_DECKBLATT79 = AttributeFactory.cleanString("Deckblatt79", null, 40, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt79"), ColumnSpecifier.get(TABLE, "Tx1423_79"));
	MetaAttribute ATR_DECKBLATT80 = AttributeFactory.cleanString("Deckblatt80", null, 40, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt80"), ColumnSpecifier.get(TABLE, "Tx1423_80"));
	MetaAttribute ATR_DECKBLATT81 = AttributeFactory.cleanString("Deckblatt81", null, 40, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt81"), ColumnSpecifier.get(TABLE, "Tx1423_81"));
	MetaAttribute ATR_DECKBLATT82 = AttributeFactory.cleanString("Deckblatt82", null, 40, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt82"), ColumnSpecifier.get(TABLE, "Tx1423_82"));
	MetaAttribute ATR_DECKBLATT83 = AttributeFactory.cleanString("Deckblatt83", null, 40, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt83"), ColumnSpecifier.get(TABLE, "Tx1423_83"));
	MetaAttribute ATR_DECKBLATT84 = AttributeFactory.cleanString("Deckblatt84", null, 40, LWStringCaches.DECKBLATT, POJOField.get(LWAuftragKopf.class, "deckblatt84"), ColumnSpecifier.get(TABLE, "Tx1423_84"));

	MetaClass METACLASS = new MetaClass("LWAuftragsKopfZusatz", null,
			LWAuftragKey.class, (MetaClass) null,
			// Attributes
			new VarArgs<MetaProperty>(MetaProperty.class).flat(
					TABLE, PKCOL1, PKCOL2,
					MetaClassMember.discoverMetaClassMembers(LWAuftragKopfMeta.class)
			));

}

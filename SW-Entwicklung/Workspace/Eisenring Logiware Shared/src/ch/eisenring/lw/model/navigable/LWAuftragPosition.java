package ch.eisenring.lw.model.navigable;

import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.sort.Comparator;
import ch.eisenring.logiware.LWAuftragKey;
import ch.eisenring.logiware.LWObjectKey;
import ch.eisenring.logiware.LWPositionKey;
import ch.eisenring.logiware.LWProduktKey;
import ch.eisenring.logiware.code.soft.GSECode;
import ch.eisenring.logiware.code.soft.LWKOKCode;
import ch.eisenring.logiware.code.soft.LWLTolerCode;
import ch.eisenring.logiware.code.soft.LWPositionStatusCode;
import ch.eisenring.logiware.code.soft.LWTerminCD;
import ch.eisenring.lw.LWException;
import ch.eisenring.lw.api.LWAuftragPositionData;

public final class LWAuftragPosition extends LWObject implements LWAuftragPositionData {

	// --------------------------------------------------------------
	// ---
	// --- Directly mapped attributes
	// ---
	// --------------------------------------------------------------
	protected String produktNummer;
	protected GSECode gseProdukt;
	protected GSECode gseAB;
	protected String produktBezeichnung;
	protected LWPositionStatusCode statusCode;
	protected LWLTolerCode ltolerCode;
	protected String addSaBe;
	protected String updSaBe;
	protected String gseLager;
	protected String textVor;
	protected String textNach;
	protected Integer auftrNrAB;
	protected Integer idSubjektL;
	protected long wunschDatum;
	protected long addDate;
	protected long updDate;
	protected double totBestellMenge;
	protected double totGelieferteMenge;
	protected double faktMenge;
	protected double nettoWert;
	protected Double posNrAB; 
	protected Double einstandsPreis;
	protected Double einstandsMenge;
	protected Double zusatzKosten;
	protected LWKOKCode kokCode;
	protected LWTerminCD terminCd;

	// --------------------------------------------------------------
	// ---
	// --- Constructor
	// ---
	// --------------------------------------------------------------
	public LWAuftragPosition(final LWObjectCache cache, final LWObjectKey pk) {
		super(cache, pk);
	}	

	// --------------------------------------------------------------
	// ---
	// --- Load/Derived attribute management
	// ---
	// --------------------------------------------------------------
	private transient LWProduktKey produktKey;
	private transient LWPositionKey positionKeyAB;

	@Override
	protected void postLoadHook() {
		super.postLoadHook();
		produktKey = LWProduktKey.get(produktNummer, gseProdukt);
		positionKeyAB = (auftrNrAB == null || AbstractCode.isNull(gseAB) || posNrAB == null)
				? null : LWPositionKey.get(posNrAB, LWAuftragKey.get(auftrNrAB, gseAB));
	}

	@Override
	protected void preLoadHook() {
		super.preLoadHook();
		produktKey = null;
		positionKeyAB = null;
	}

	// --------------------------------------------------------------
	// ---
	// --- Derived getters
	// ---
	// --------------------------------------------------------------
	@Override
	public final LWPositionKey getPK() {
		return (LWPositionKey) super.getPK();
	}
	
	@Override
	public String getAddSaBe() {
		ensureLoaded(); return addSaBe;
	}

	@Override
	public long getAddDate() {
		ensureLoaded(); return addDate;
	}

	@Override
	public String getUpdSaBe() {
		ensureLoaded(); return updSaBe;
	}

	@Override
	public long getUpdDate() {
		ensureLoaded(); return updDate;
	}

	@Override
	public boolean isStorniert() {
		ensureLoaded();
		if (LWPositionStatusCode.STORNIERT.equals(statusCode))
			return true;
		try {
			final LWAuftrag auftrag = getAuftrag();
			return auftrag == null || auftrag.isStorniert();
		} catch (final Exception e) {
			// assume storno if problem fetching order
			return true;
		}
	}

	// --------------------------------------------------------------
	// ---
	// --- Relation getters
	// ---
	// --------------------------------------------------------------
	/**
	 * Gets Auftrag of this AufragPosition
	 */
	public LWAuftrag getAuftrag() throws LWException {
		final LWPositionKey pk = (LWPositionKey) getPK();
		return getCache().getObject(pk.getAuftragKey(), LWAuftrag.class);
	}

	/**
	 * Gets AuftragKopf of this AuftragPosition
	 */
	public LWAuftragKopf getAuftragKopf() throws LWException {
		final LWPositionKey pk = (LWPositionKey) getPK();
		return getCache().getObject(pk.getAuftragKey(), LWAuftragKopf.class);
	}

	/**
	 * Gets Produkt of this AuftragPosition
	 */
	public LWProdukt getProdukt() throws LWException {
		ensureLoaded();
		return getCache().getObject(produktKey, LWProdukt.class);
	}

	/**
	 * Gets Liefersubjekt
	 */
	public LWSubjekt getSubjektL() throws LWException {
		ensureLoaded();
		LWSubjekt result = getSubjektImpl(idSubjektL);
		if (result == null)
			result = getAuftrag().getSubjektL();
		return result;
	}

	/**
	 * Gets AB position key (or null)
	 */
	@Override
	public LWPositionKey getPositionKeyAB() throws LWException {
		ensureLoaded(); return positionKeyAB;
	}

	/**
	 * Gets the AB position (or null)
	 */
	public LWAuftragPosition getABPosition() throws LWException {
		final LWPositionKey key = getPositionKeyAB();
		return key == null ? null : getCache().getObject(key, LWAuftragPosition.class);
	}

	// --------------------------------------------------------------
	// ---
	// --- Attribute getters
	// ---
	// --------------------------------------------------------------
	@Override
	public LWProduktKey getProduktKey() {
		ensureLoaded();	return produktKey;
	}


	@Override
	public String getProduktBezeichnung() {
		ensureLoaded(); return produktBezeichnung;
	}

	@Override
	public LWLTolerCode getLTolerCode() {
		ensureLoaded(); return ltolerCode;
	}

	@Override
	public long getWunschDatum() {
		ensureLoaded(); return wunschDatum;
	}

	@Override
	public LWPositionStatusCode getStatusCode() {
		ensureLoaded(); return statusCode;
	}

	@Override
	public String getGSELager() {
		ensureLoaded(); return gseLager;
	}

	@Override
	public double getBestellMenge() {
		ensureLoaded(); return totBestellMenge;
	}

	@Override
	public double getGelieferteMenge() {
		ensureLoaded(); return totGelieferteMenge;
	}

	@Override
	public double getFaktMenge() {
		ensureLoaded(); return faktMenge;
	}

	public double getNettoWert() {
		ensureLoaded(); return nettoWert;
	}

	public Double getEinstandsPreis() {
		ensureLoaded();
		final Double result = einstandsPreis;
		if (result == null)
			return result;
		if (result.doubleValue() == 0D)
			return null;
		return result;
	}

	public Double getEinstandsMenge() {
		ensureLoaded(); return einstandsMenge;
	}

	@Override
	public String getTextVor() {
		ensureLoaded();
		return textVor;
	}

	@Override
	public String getTextNach() {
		ensureLoaded();
		return textNach;
	}

	@Override
	public LWKOKCode getKokCode(){
		ensureLoaded(); return kokCode;
	}

	@Override
	public LWTerminCD getTerminCd() {
		ensureLoaded(); return terminCd;
	}

	public Double getZusatzKosten() {
		ensureLoaded(); return zusatzKosten;
	}

	// --------------------------------------------------------------
	// ---
	// --- Sorting
	// ---
	// --------------------------------------------------------------
	public static class Order extends LWObject.Order {
		public final static Comparator<LWAuftragPosition> PositionsNummer = Comparator.wrapNullSafe(
				new Comparator<LWAuftragPosition>() {
					@Override
					public int compare(final LWAuftragPosition p1, final LWAuftragPosition p2) {
						return compareSigned(p1.getPositionsNummer(), p2.getPositionsNummer());
					}
				});
	}

}

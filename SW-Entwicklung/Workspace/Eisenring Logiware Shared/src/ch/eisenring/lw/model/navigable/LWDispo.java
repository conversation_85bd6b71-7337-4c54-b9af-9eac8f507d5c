package ch.eisenring.lw.model.navigable;

import ch.eisenring.core.sort.Comparator;
import ch.eisenring.logiware.LWAuftragKey;
import ch.eisenring.logiware.LWDispoKey;
import ch.eisenring.logiware.LWObjectKey;
import ch.eisenring.logiware.LWPositionKey;
import ch.eisenring.logiware.code.soft.GSECode;

public final class LWDispo extends LWObject {

    // --------------------------------------------------------------
    // ---
    // --- Directly mapped attributes
    // ---
    // --------------------------------------------------------------

    protected long updDate;
    protected String updSaBe;
    protected long termin;
    protected String prodNr;

    // --------------------------------------------------------------
    // ---
    // --- Derived / Cached attributes
    // ---
    // --------------------------------------------------------------

    @Override
    public String getUpdSaBe() {
        // not present
        return null;
    }

    @Override
    public long getUpdDate() {
        ensureLoaded();
        return updDate;
    }

    @Override
    public boolean isStorniert() {
        // always ok
        return false;
    }

    // --------------------------------------------------------------
    // ---
    // --- Constructor
    // ---
    // --------------------------------------------------------------
    public LWDispo(final LWObjectCache cache, final LWObjectKey pk) {
        super(cache, pk);
    }

    // --------------------------------------------------------------
    // ---
    // --- Cached attributes
    // ---
    // --------------------------------------------------------------


    @Override
    protected void preLoadHook() {
        super.preLoadHook();
    }

    @Override
    protected void postLoadHook() {
        super.postLoadHook();

    }

    // --------------------------------------------------------------
    // ---
    // --- Attribute getters
    // ---
    // --------------------------------------------------------------

    public int getAuftrNr() {
        final LWDispoKey key = (LWDispoKey) getPK();
        return key.getAuftragNr();
    }

    public double getPosNr() {
        final LWDispoKey key = (LWDispoKey) getPK();
        return key.getPosNr();
    }

    public GSECode getGSEAuftrag() {
        final LWDispoKey key = (LWDispoKey) getPK();
        return key.getIdGseAuftrag();
    }

    public long getTermin() {
        ensureLoaded();
        return termin;
    }

    public String getProdNr() {
        ensureLoaded();
        return prodNr;
    }

    // --------------------------------------------------------------
    // ---
    // --- Relation getters
    // ---
    // --------------------------------------------------------------

    public LWAuftrag getAuftrag() {
        ensureLoaded();
        final LWAuftragKey key = LWAuftragKey.get(getAuftrNr(), getGSEAuftrag());
        return getCache().getObject(key, LWAuftrag.class);
    }

    public LWAuftragPosition getAuftragPosition() {
        ensureLoaded();
        final LWPositionKey key = LWPositionKey.get(getPosNr(), LWAuftragKey.get(getAuftrNr(), getGSEAuftrag()));
        return getCache().getObject(key, LWAuftragPosition.class);
    }

    // --------------------------------------------------------------
    // ---
    // --- Sorting
    // ---
    // --------------------------------------------------------------
    public static class Order extends LWObject.Order {
        public final static Comparator<LWDispo> PosNr = Comparator.wrapNullSafe(
                new Comparator<LWDispo>() {
                    public int compare(final LWDispo f1, final LWDispo f2) {
                        return compareSigned(f1.getPosNr(), f2.getPosNr());
                    }
                });
    }

}
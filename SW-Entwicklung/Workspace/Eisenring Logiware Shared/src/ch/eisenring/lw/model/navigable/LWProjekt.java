package ch.eisenring.lw.model.navigable;

import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.collections.Collection;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.Map;
import ch.eisenring.core.collections.Set;
import ch.eisenring.core.collections.impl.ArraySet;
import ch.eisenring.core.collections.impl.HashMap;
import ch.eisenring.core.collections.impl.HashSet;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.logiware.LWObjectKey;
import ch.eisenring.logiware.LWProjektKey;
import ch.eisenring.logiware.code.hard.LWAdressArtCode;
import ch.eisenring.logiware.code.pseudo.AbwicklungsartCode;
import ch.eisenring.logiware.code.soft.*;
import ch.eisenring.logiware.util.LogiwareUtil;
import ch.eisenring.lw.LWException;
import ch.eisenring.lw.api.LWProjektData;
import ch.eisenring.lw.condition.Factory;
import ch.eisenring.lw.meta.LWAuftragKopfMeta;
import ch.eisenring.lw.meta.LWAuftragMeta;
import ch.eisenring.lw.meta.LWProjektMeta;
import ch.eisenring.lw.meta.LWXEtappeMeta;
import ch.eisenring.lw.model.special.LWKontaktDaten;

public final class LWProjekt extends LWObject implements LWProjektData {

	// --------------------------------------------------------------
	// ---
	// --- Directly mapped attributes
	// ---
	// --------------------------------------------------------------

	protected String basisnummer;
	protected GSECode gseBasis;
	protected LWProjektStatusCode statusCode;
	protected LWBasisobjektCode basisobjektCode;
	protected LWBasisobjektStatus basisobjektStatus;
	protected LWObjektBetreuerCode objektbetreuerCode;
	protected LWObjektBetreuerCode kundenPLCode;
	protected LWMarketingCode marketingCode;
	protected LWKundenberaterCode kundenberater;
	protected LWObjektStatusCode objektstatus;
	protected LWZahlungsmodalitaetCode modalitaet;
	protected LWProduktKategorieCode produktKategorie;
	protected LWGeschossCode geschoss;
	protected LWLageCode lage;
	protected LWProjektAuftragsartCode auftragsArt;
	protected String hausSpiegel;
	protected String objektkuechenAnzahl;
	protected String projektBezeichnung;

	protected String nameBauherr;
	protected String phoneBauherr;
	protected String mobileBauherr;
	protected String emailBauherr;

	protected String nameBaufuehrer;
	protected String phoneBaufuehrer;
	protected String mobileBaufuehrer;
	protected String emailBaufuehrer;
	protected String phoneKunde;
	protected String gpsKoordinaten;
	protected String updSaBe;
	protected String addSaBe;
	protected Double auftragsSumme;
	protected Double subjektWert;
	protected Double basisWert;
	protected Integer vertreterId;

	protected Integer idSubjekt;
	protected Integer idSubjektA;
	protected Integer idSubjektB;
	protected Integer idSubjektI;
	protected Integer idSubjektK;
	protected Integer idSubjektAG;

	protected LWAdressArtCode subAdrArtA;
	protected LWAdressArtCode subAdrArtB;
	protected LWAdressArtCode subAdrArtI;
	protected LWAdressArtCode subAdrArtK;

	protected long addDate;
	protected long updDate;
	protected long bemusterung1;
	protected long bemusterung2;
	protected long bemusterung3;

	protected String mBauherrAdr;

	// ProjektBezeichnung detail
	protected String prjBezPLZ;
	protected String prjBezOrt;
	protected String prjBezStrasse;
	protected String prjBezProjektBez;
	protected String prjBezWohnungBez;
	protected String prjBezWohnungTyp;
	protected String prjBezFamilienBez;

	protected LWProjektBasisInfoCode prjBasisInfoCode;

	// --------------------------------------------------------------
	// ---
	// --- Derived / Cached attributes
	// ---
	// --------------------------------------------------------------

	private LWProjektKey parentKey;
	private boolean basisKeyValid;
	private LWProjektKey basisKey;
	private Set<LWAuftrag> auftraege;
	private Set<LWProjekt> childProjects;
	private Set<LWXEtappe> etappen;

	// --------------------------------------------------------------
	// ---
	// --- Constructor
	// ---
	// --------------------------------------------------------------

	public LWProjekt(final LWObjectCache cache, final LWObjectKey pk) {
		super(cache, pk);
	}

	// --------------------------------------------------------------
	// ---
	// --- Getter
	// ---
	// --------------------------------------------------------------

	@Override
	public final LWProjektKey getPK() {
		return (LWProjektKey) super.getPK();
	}

	@Override
	public String getUpdSaBe() {
		ensureLoaded();
		return updSaBe;
	}

	@Override
	public long getUpdDate() {
		ensureLoaded();
		return updDate;
	}

	@Override
	public String getAddSaBe() {
		ensureLoaded();
		return addSaBe;
	}

	@Override
	public long getAddDate() {
		ensureLoaded();
		return addDate;
	}

	@Override
	public boolean isStorniert() {
		ensureLoaded();
		return LWProjektStatusCode.STORNIERT.equals(getStatusCode());
	}

	@Override
	public LWProjektKey getProjektKey() {
		return getPK();
	}

	@Override
	protected void preLoadHook() {
		super.preLoadHook();
		parentKey = null;
		basisKeyValid = false;
		basisKey = null;
		auftraege = null;
		childProjects = null;
		etappen = null;
	}

	@Override
	protected void postLoadHook() {
		super.postLoadHook();
		parentKey = Strings.isEmpty(basisnummer) ? null : LWProjektKey.get(basisnummer, gseBasis);
	}

	// --------------------------------------------------------------
	// ---
	// --- Attribute getters
	// ---
	// --------------------------------------------------------------

	@Override
	public String getBasisnummer() {
		ensureLoaded();
		return basisnummer;
	}

	@Override
	public GSECode getGSEBasis() {
		ensureLoaded();
		return gseBasis;
	}

	@Override
	public LWProjektStatusCode getStatusCode() {
		ensureLoaded();
		return statusCode;
	}

	@Override
	public LWBasisobjektCode getBasisobjektCode() {
		ensureLoaded();
		return basisobjektCode;
	}

	@Override
	public LWObjektBetreuerCode getObjektbetreuerCode() {
		ensureLoaded();
		return objektbetreuerCode;
	}

	@Override
	public LWObjektBetreuerCode getKundenPLCode() {
		ensureLoaded();
		return kundenPLCode;
	}

	@Override
	public LWMarketingCode getMarketingCode() {
		ensureLoaded();
		return marketingCode;
	}

	@Override
	public String getObjektkuechenAnzahl() {
		ensureLoaded();
		return objektkuechenAnzahl;
	}

	@Override
	public String getProjektbezeichnung() {
		ensureLoaded();
		return projektBezeichnung;
	}

	@Override
	public String getManuelleBauherrAdresse() {
		ensureLoaded();
		return mBauherrAdr;
	}

	public String getPhoneBauherr() {
		ensureLoaded();
		return phoneBauherr;
	}

	public String getMobileBauherr() {
		ensureLoaded();
		return mobileBauherr;
	}

	public String getEmailBauherr() {
		ensureLoaded();
		return emailBauherr;
	}

	public String getNameBaufuehrer() {
		ensureLoaded();
		return nameBaufuehrer;
	}

	public String getNameBauherr() {
		ensureLoaded();
		return nameBauherr;
	}

	public String getPhoneBaufuehrer() {
		ensureLoaded();
		return phoneBaufuehrer;
	}

	public String getMobileBaufuehrer() {
		ensureLoaded();
		return mobileBaufuehrer;
	}

	public String getEmailBaufuehrer() {
		ensureLoaded();
		return emailBaufuehrer;
	}

	@Override
	public String getPhoneKunde() {
		ensureLoaded();
		return phoneKunde;
	}

	@Override
	public String getGPSKoordinaten() {
		ensureLoaded();
		return gpsKoordinaten;
	}

	@Override
	public Integer getVerkaueferSubjektId() {
		ensureLoaded();
		return vertreterId;
	}

	public LWSubjekt getVerkaueferSubjekt() {
		ensureLoaded();
		return getSubjektImpl(vertreterId);
	}

	@Override
	public LWKundenberaterCode getKundenberater() {
		ensureLoaded();
		return kundenberater;
	}

	@Override
	public LWObjektStatusCode getObjektStatus() {
		ensureLoaded();
		return objektstatus;
	}

	@Override
	public long getBemusterung1() {
		ensureLoaded();
		return bemusterung1;
	}

	@Override
	public long getBemusterung2() {
		ensureLoaded();
		return bemusterung2;
	}

	@Override
	public long getBemusterung3() {
		ensureLoaded();
		return bemusterung3;
	}

	@Override
	public Double getAuftragsSumme() {
		ensureLoaded();
		return auftragsSumme;
	}

	@Override
	public Double getSubjektWert() {
		ensureLoaded();
		return subjektWert;
	}

	@Override
	public Double getBasisWert() {
		ensureLoaded();
		return basisWert;
	}

	@Override
	public String getHausSpiegel() {
		ensureLoaded();
		return hausSpiegel;
	}

	@Override
	public LWGeschossCode getGeschoss() {
		ensureLoaded();
		return geschoss;
	}

	@Override
	public LWLageCode getLage() {
		ensureLoaded();
		return lage;
	}

	// --------------------------------------------------------------
	// ---
	// --- Projektbezeichnung
	// ---
	// --------------------------------------------------------------

	/**
	 * Detail Bezeichnung PLZ, or if null, derived from PBez
	 */
	@Override
	public String getProjektPLZ() {
		ensureLoaded();
		if (prjBezPLZ != null)
			return prjBezPLZ;
		// try to split out PLZ from ProjektBezeichnung
		return LogiwareUtil.getPLZFromBauherr(getProjektbezeichnung(), null);
	}

	/**
	 * Detail Bezeichnung Ort, or if null, derived from PBez
	 */
	@Override
	public String getProjektOrt() {
		ensureLoaded();
		if (prjBezOrt != null)
			return prjBezOrt;
		// try to split city from ProjektBezeichnung
		return LogiwareUtil.getOrtFromBauherr(getProjektbezeichnung(), null);
	}

	@Override
	public String getProjektStrasse() {
		ensureLoaded();
		return prjBezStrasse;
	}

	@Override
	public String getProjektWohnungBez() {
		ensureLoaded();
		return prjBezWohnungBez;
	}

	@Override
	public String getProjektWohnungTyp() {
		ensureLoaded();
		return prjBezWohnungTyp;
	}

	@Override
	public String getProjektFamilienBez() {
		ensureLoaded();
		return prjBezFamilienBez;
	}

	@Override
	public String getProjektDetailbezeichnung() {
		ensureLoaded();
		return prjBezProjektBez;
	}

	@Override
	public LWZahlungsmodalitaetCode getModalitaet() {
		ensureLoaded();
		return modalitaet;
	}

	@Override
	public LWProduktKategorieCode getProduktKategorie() {
		ensureLoaded();
		return produktKategorie;
	}

	@Override
	public Long getEtappeRowId() {
		final LWXPAuftrag lwxPAuftrag = getLWXPAuftrag();
		return lwxPAuftrag == null ? null : lwxPAuftrag.getEtappeRowId();
	}

	public LWProjektAuftragsartCode getAuftragsart() {
		ensureLoaded();
		return auftragsArt;
	}

	// --------------------------------------------------------------
	// ---
	// --- Subjekte
	// ---
	// --------------------------------------------------------------

	/**
	 * Gets Subjekt (Objekt)
	 */
	public LWSubjekt getSubjekt() {
		ensureLoaded();
		return getSubjektImpl(idSubjekt);
	}

	/**
	 * Gets SubjektA (Architekt)
	 */
	public LWSubjekt getSubjektA() {
		ensureLoaded();
		return getSubjektImpl(idSubjektA);
	}

	public LWAdressArtCode getSubjektAArt() {
		ensureLoaded();
		return subAdrArtA;
	}

	/**
	 * Gets SubjektB (Bauherr)
	 */
	public LWSubjekt getSubjektB() {
		ensureLoaded();
		return getSubjektImpl(idSubjektB);
	}

	public LWAdressArtCode getSubjektBArt() {
		ensureLoaded();
		return subAdrArtB;
	}

	/**
	 * SubjektI (usually specifies the GU)
	 */
	public LWSubjekt getSubjektI() {
		ensureLoaded();
		return getSubjektImpl(idSubjektI);
	}

	public LWAdressArtCode getSubjektIArt() {
		ensureLoaded();
		return subAdrArtI;
	}

	/**
	 * Gets SubjektK (Käufer)
	 */
	public LWSubjekt getSubjektK() {
		ensureLoaded();
		return getSubjektImpl(idSubjektK);
	}

	public LWAdressArtCode getSubjektKArt() {
		ensureLoaded();
		return subAdrArtK;
	}

	/**
	 * Gets SubjektAG (?)
	 */
	public LWSubjekt getSubjektAG() {
		ensureLoaded();
		return getSubjektImpl(idSubjektAG);
	}

	// --------------------------------------------------------------
	// ---
	// --- Special access helpers
	// ---
	// --------------------------------------------------------------

	@Override
	public LWKontaktDaten getKontaktDatenBauherr() {
		return new LWKontaktDaten() {
			@Override
			public String getEMail() {
				return getEmailBauherr();
			}

			@Override
			public String getName() {
				return getNameBauherr();
			}

			@Override
			public String getMobile() {
				return getMobileBauherr();
			}

			@Override
			public String getPhone() {
				return getPhoneBauherr();
			}
		};
	}

	@Override
	public LWKontaktDaten getKontaktDatenBaufuehrer() {
		return new LWKontaktDaten() {
			@Override
			public String getEMail() {
				return getEmailBaufuehrer();
			}

			@Override
			public String getName() {
				return getNameBaufuehrer();
			}

			@Override
			public String getMobile() {
				return getMobileBaufuehrer();
			}

			@Override
			public String getPhone() {
				return getPhoneBaufuehrer();
			}
		};
	}

	// --------------------------------------------------------------
	// ---
	// --- Relation getters
	// ---
	// --------------------------------------------------------------

	/**
	 * Gets the extension record for this projekt. Returns null if no extension record exists.
	 */
	public LWXPAuftrag getLWXPAuftrag() throws LWException {
		LWXPAuftrag lwxPAuftrag = getCache().getObject(getPK(), LWXPAuftrag.class);
		lwxPAuftrag = validateExists(lwxPAuftrag);
		return lwxPAuftrag;
	}

	@Override
	public LWProjektKey getParentKey() {
		ensureLoaded();
		return parentKey;
	}

	/**
	 * Gets Etappen for this projekt.
	 */
	public Set<LWXEtappe> getEtappen() throws LWException {
		if (etappen == null) {
			final LWProjektKey key = getPK();
			etappen = getCache().load(LWXEtappe.class,
				Factory.eq(LWXEtappeMeta.ATR_BASISNUMMER, key.getProjektnummer()),
				Factory.eq(LWXEtappeMeta.ATR_BASISGSE, key.getGSE()));
		}
		return etappen;
	}

	/**
	 * Gets the parent project, that is the project directly referenced by the key in "Basis-Objektnummer". Resolution is done by canonicalizing the BasisobjektNummer-Key and getting that project.
	 * Returns NULL if the relation points to the project itself or is unresolvable.
	 */
	public LWProjekt getParentProjekt() throws LWException {
		ensureLoaded();
		if (parentKey == null)
			return null;
		try {
			final LWProjektKey canonicalKey = parentKey.getCanonicalBasisobjektKey();
			if (getPK().equals(canonicalKey))
				return null;
			final LWProjekt parent = getCache().getObject(canonicalKey, LWProjekt.class);
			return LWObject.validateExists(parent);
//			if (parent != null) {
//				parent.ensureLoaded();
//			}
//			return parent;
		} catch (final Exception e) {
			return null;
		}
	}

	@Override
	public LWProjektKey getBasisKey() {
		if (!basisKeyValid) {
			// look up the base project
			final Set<LWObjectKey> traversedKeys = new HashSet<>();
			LWProjekt child = this;
			while (true) {
				final LWProjekt parent = child.getParentProjekt();
				if (parent == null || parent == child) {
					basisKey = child.getPK();
					basisKeyValid = true;
					break;
				}
				if (!traversedKeys.add(child.getPK())) {
					// circular graph detected, invalid data model
					basisKeyValid = true;
					basisKey = null;
					break;
				}
				child = parent;
			}
		}
		return basisKey;
	}

	/**
	 * Gets the Base project, that is the root found by walking up the parent project relation until the root is found. Returns NULL if a circular graph is encountered.
	 */
	public LWProjekt getBasisProjekt() throws LWException {
		final LWProjektKey basisKey = getBasisKey();
		if (basisKey == null)
			return null;
		final LWProjekt basis = getCache().getObject(basisKey, LWProjekt.class);
		return LWObject.validateExists(basis);
	}

	/**
	 * Returns true if this project is the "Basis" of a project hierarchy
	 */
	public boolean isBasisprojekt() {
		return equals(getBasisProjekt());
	}

	/**
	 * Gets all Auftrag that are referencing this Projekt.
	 */
	public Set<LWAuftrag> getAuftraege() throws LWException {
		if (auftraege == null) {
			final LWProjektKey pk = getPK();
			final List<LWAuftrag> list = getCache().load(
				LWAuftrag.class, LWAuftragKopf.class,
				Factory.eq(LWAuftragMeta.ATR_PROJEKTNUMMER, pk.getProjektnummer()),
				Factory.eq(LWAuftragMeta.ATR_GSE_PROJEKT, pk.getGSE()),
				Factory.eq(LWAuftragMeta.ATR_AUFTRAGNUMMER, LWAuftragKopfMeta.ATR_AUFTRAGNUMMER),
				Factory.eq(LWAuftragMeta.ATR_GSE_AUFTRAG, LWAuftragKopfMeta.ATR_GSE_AUFTRAG));
			this.auftraege = new ArraySet<>(list);
		}
		return new ArraySet<>(auftraege);
	}

	// --------------------------------------------------------------
	// ---
	// --- Aktiver / Küchenauftrag
	// ---
	// --------------------------------------------------------------

	private final static Map<AbwicklungsartCode, Integer> AWA_KUECHE_SCORE;
	static {
		final Map<AbwicklungsartCode, Integer> m = new HashMap<>();
		m.put(AbwicklungsartCode.AWA001, 3);
		m.put(AbwicklungsartCode.AWA135, 5);
		AWA_KUECHE_SCORE = m;
	}

	/**
	 * Gets "Aktiven" Auftrag (001 or 135). Returns NULL if none is found.
	 */
	public LWAuftrag getKuechenAuftrag() throws LWException {
		return getKuechenAuftrag(false);
	}

	/**
	 * Gets "Aktiven" Auftrag (001 or 135). Returns NULL if none is found.
	 */
	public LWAuftrag getKuechenAuftrag(final boolean includeStorniert) throws LWException {
		final Set<LWAuftrag> auftraege = getAuftraege();
		int bestScore = Integer.MIN_VALUE;
		LWAuftrag result = null;
		for (final LWAuftrag auftrag : auftraege) {
			final Integer scoreVal = AWA_KUECHE_SCORE.get(auftrag.getAbwicklungsart());
			if (scoreVal == null)
				continue;
			int score = scoreVal.intValue();
			if (auftrag.isStorniert()) {
				if (!includeStorniert)
					continue;
				score /= 2;
			}
			if (score > bestScore) {
				bestScore = score;
				result = auftrag;
			} else if (score == bestScore) {
				if (result == null || result.getAuftragsDatum() < auftrag.getAuftragsDatum())
					result = auftrag;
			}
		}
		return result;
	}

	/**
	 * Gets Auftrag by AbwicklungsArt, throws if not found or multiple results.
	 */
	public LWAuftrag getAuftrag(final AbwicklungsartCode abwicklungsArt) throws LWException {
		final Set<LWAuftrag> auftraege = getAuftraege();
		LWAuftrag result = null;
		boolean storno = false;
		for (final LWAuftrag auftrag : auftraege) {
			if (auftrag.isStorniert()) {
				storno = true;
				continue;
			}
			final AbwicklungsartCode awa = auftrag.getAbwicklungsart();
			if (!AbstractCode.equals(abwicklungsArt, awa))
				continue;
			if (result != null)
				throw new LWException(Strings.concat("Mehrere (aktive) Aufträge mit Abwicklungsart ", abwicklungsArt));
			result = auftrag;
		}
		if (result == null) {
			if (storno) {
				throw new LWException(Strings.concat("Auftrag Abwicklungsart ", abwicklungsArt, " ist storniert"));
			} else {
				throw new LWException(Strings.concat("Kein (aktiver) Auftrag Abwicklungsart ", abwicklungsArt));
			}
		}
		return result;
	}

	/**
	 * Gets all child projects of this project (projects referencing this project with their BasisObjektNummer field)
	 */
	public Set<LWProjekt> getChildProjekte() throws LWException {
		if (childProjects == null) {
			final LWProjektKey rawKey, canKey;
			try {
				rawKey = getPK();
				canKey = rawKey.getCanonicalBasisobjektKey();
				if (!rawKey.equals(canKey)) {
					// non-canonical projects can't be the base
					childProjects = Set.emptyReadonly(LWProjekt.class);
				} else {
					final LWProjektKey pk = getPK().getCanonicalBasisobjektKey();
					childProjects = getCache().load(LWProjekt.class,
						Factory.and(
							Factory.or(
								Factory.eq(LWProjektMeta.ATR_BASISPROJEKTNUMMER, pk.getProjektnummer()),
								Factory.like(LWProjektMeta.ATR_BASISPROJEKTNUMMER, Strings.concat(pk.getProjektnummer(), "-%"))),
							Factory.eq(LWProjektMeta.ATR_GSE_BASISPROJEKT, pk.getGSE())));
					// a project is not its own child, although the relationship can be set this way in logiware.
					childProjects.remove(this);
					childProjects.trimToSize();
				}
			} catch (final Exception e) {
				childProjects = Set.emptyReadonly(LWProjekt.class);
			}
		}
		return new ArraySet<>(childProjects);
	}

	/**
	 * Gets all subordinated projects of this project. This includes children of children and so on.
	 */
	public Set<LWProjekt> getChildProjekteRecursive() throws LWException {
		final Set<LWProjekt> result = new ArraySet<>(getChildProjekte());
		final LWProjekt[] childProjekte = Collection.toArray(result, LWProjekt.class);
		for (final LWProjekt childProjekt : childProjekte) {
			final Collection<LWProjekt> subProjekte = childProjekt.getChildProjekteRecursive();
			result.addAll(subProjekte);
		}
		result.add(this);
		result.trimToSize();
		return result;
	}

	public LWBasisobjektStatus getBasisObjektStatus() {
		return basisobjektStatus;
	}

	@Override
	public LWProjektBasisInfoCode getBasisInfoCode() {
		return prjBasisInfoCode;
	}

}

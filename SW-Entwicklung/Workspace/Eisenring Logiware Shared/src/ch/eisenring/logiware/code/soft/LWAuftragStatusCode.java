package ch.eisenring.logiware.code.soft;

import ch.eisenring.core.codetype.LoadQuery;
import ch.eisenring.core.collections.Set;
import ch.eisenring.logiware.code.LWAbstractStatusCode;

@LoadQuery("SELECT " +
	         "C.CD_Hard AS ${Key}, " +
	         "C.CD_Soft AS ${ShortText}, " +
	         "C.Bezeichnung AS ${LongText} " +
	       "FROM " +
	         "${DB}${Schema}APPCODE AS C " +
		   "WHERE " +
		     "C.CD_Sprache = '001' AND " +
		     "C.Tab_Prfx = 'LAUK' AND " +
		     "C.Col_Name = 'Status'")
public final class LWAuftragStatusCode extends LWAbstractStatusCode {

	public final static LWAuftragStatusCode NULL = new LWAuftragStatusCode(0, null, "", "");
	public final static LWAuftragStatusCode GESPERRT080 = new LWAuftragStatusCode(1, "080", "Gsp", "Gesperrt/In Bearb.");
	public final static LWAuftragStatusCode GESPERRT081 = new LWAuftragStatusCode(2, "081", "Gsp/Dup", "Gesperrt/Duplizieren");
	public final static LWAuftragStatusCode GESPERRT085 = new LWAuftragStatusCode(3, "085", "Gsp/WBez", "Gesperrt/Interner Warenbezug");
	public final static LWAuftragStatusCode PENDENT_SABE = new LWAuftragStatusCode(4, "090", "PndSB", "Pendent (Sachb.)");
	public final static LWAuftragStatusCode PENDENT_BONI = new LWAuftragStatusCode(5, "091", "PndBo", "Pendent (Bonität)");
	public final static LWAuftragStatusCode STORNIERT = new LWAuftragStatusCode(6, "099", "Sto", "Storniert");

	private final static Set<LWAuftragStatusCode> CODES_PENDENT = Set.asReadonlySet(
			PENDENT_SABE, PENDENT_BONI);

	LWAuftragStatusCode(final Object key, final String shortText, final String longText) {
		super(key, shortText, longText);
	}
	
	LWAuftragStatusCode(final int id, final Object key,
				      final String shortText, final String longText) {
		super(id, key, shortText, longText);
	}

	public boolean isStorniert() {
		return "099".equals(getKey());
	}

	public boolean isPendent() {
		return CODES_PENDENT.contains(this);
	}

}

package ch.eisenring.logiware.code.soft;

import ch.eisenring.core.codetype.LoadQuery;
import ch.eisenring.logiware.code.LWAbstractStatusCode;

@LoadQuery("SELECT " +
	         "C.CD_Hard AS ${Key}, " +
	         "C.CD_Soft AS ${ShortText}, " +
	         "C.Bezeichnung AS ${LongText} " +
	       "FROM " +
	         "${DB}${Schema}APPCODE AS C " +
		   "WHERE " +
		     "C.CD_Sprache = '001' AND " +
		     "C.Tab_Prfx = 'LPRO' AND " +
		     "C.Col_Name = 'STATUS'")
public final class LWProduktStatusCode extends LWAbstractStatusCode {

	public final static LWProduktStatusCode NULL = new LWProduktStatusCode(0, null, "", "");

	public final static LWProduktStatusCode AKTIV = new LWProduktStatusCode(1, "001", "", "Aktiv");
	public final static LWProduktStatusCode INAKTIV = new LWProduktStatusCode(2, "002", "", "Inaktiv");
	public final static LWProduktStatusCode LIQUIDATION = new LWProduktStatusCode(3, "003", "", "Liquidation");
	public final static LWProduktStatusCode GESPERRT = new LWProduktStatusCode(4, "080", "", "Gesperrt");

	LWProduktStatusCode(final Object key, final String shortText, final String longText) {
		super(key, shortText, longText);
	}
	
	LWProduktStatusCode(final int id, final Object key,
				      final String shortText, final String longText) {
		super(id, key, shortText, longText);
	}

	@Override
	public boolean isStorniert() {
		return equals(GESPERRT) || equals(INAKTIV);
	}

}

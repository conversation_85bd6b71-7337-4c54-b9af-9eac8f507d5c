package ch.eisenring.logiware.code.pseudo;

import ch.eisenring.core.codetype.DynamicCode;
import ch.eisenring.core.codetype.LoadQuery;

@LoadQuery("SELECT " +
             "Gruppe AS ${Key}, " +
		     "Gruppe AS ${ShortText}, " +
		     "Bezeichnung AS ${LongText} " +
		   "FROM " +
		     "${DB}${Schema}GBZGRUPPE " +
		   "WHERE " +
		     "GBZGrArt = 900 AND Id_GSE = '2000'")
public final class AVORTeamCode extends DynamicCode {

	public final static AVORTeamCode NULL = new AVORTeamCode(0, null, "", "");

	public final static AVORTeamCode Z100 = new AVORTeamCode(1, "z-100", "", "");
	
	AVORTeamCode(final Object key, final String shortText, final String longText) {
		super(key, shortText, longText);
	}
	
	AVORTeamCode(final int id, final Object key,
				 final String shortText, final String longText) {
		super(id, key, shortText, longText);
	}
	
}

package ch.eisenring.logiware.code.soft;

import ch.eisenring.core.codetype.LoadQuery;
import ch.eisenring.logiware.code.LogiwareAppCode;

@LoadQuery("SELECT " +
		     "C.CD_Hard AS ${Key}, " +
		     "C.CD_Soft AS ${ShortText}, " +
		     "C<PERSON>Bezeichnung AS ${LongText} " +
		   "FROM " +
		     "${DB}${Schema}APPCODE AS C " +
		   "WHERE " +
		     "C.CD_Sprache = '001' AND " +
		     "C.Tab_Prfx = 'LPAU' AND " +
		     "C.Col_Name = 'Marketing'")
public final class LWMarketingCode extends LogiwareAppCode {

	public final static LWMarketingCode NULL = new LWMarketingCode(0, null, "", "");

	public final static LWMarketingCode VIP = new LWMarketingCode(1, "090", "90", "90 VIP");

	LWMarketingCode(final Object key, final String shortText, final String longText) {
		super(key, shortText, longText);
	}
	
	LWMarketingCode(final int id, final Object key,
			      final String shorText, final String longText) {
		super(id, key, shorText, longText);
	}

}

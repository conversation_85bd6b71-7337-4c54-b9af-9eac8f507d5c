package ch.eisenring.logiware.code.soft;

import ch.eisenring.core.codetype.LoadQuery;
import ch.eisenring.logiware.code.LogiwareAppCode;

@LoadQuery("SELECT " +
		     "C.CD_Hard AS ${Key}, " +
		     "C.CD_Soft AS ${ShortText}, " +
		     "<PERSON><PERSON>Bezeichnung AS ${LongText} " +
		   "FROM " +
		     "${DB}${Schema}APPCODE AS C " +
		   "WHERE " +
		     "C.CD_Sprache = '001' AND " +
		     "C.Tab_Prfx = 'LAKZ' AND " +
		     "C.Col_Name = 'MONTEURPFY' ")
public final class LWMonteurPfyCode extends LogiwareAppCode {

	public final static LWMonteurPfyCode NULL = new LWMonteurPfyCode(0, null, "", "");
	
	LWMonteurPfyCode(final Object key, final String shortText, final String longText) {
		super(key, shortText, longText);
	}
	
	LWMonteurPfyCode(final int id, final Object key,
			        final String shortText, final String longText) {
		super(id, key, shortText, longText);
	}

}

package ch.eisenring.logiware.code;

import ch.eisenring.core.codetype.DynamicCode;

/**
 * Base class for codes loaded dynamically from LOGIWARE table APPCODE. All non-abstract children need to declare a static String LOAD_QUERY that is used to load the code instances from the DB. The
 * field does not need to be public. Also, all constructors provided by this base class must be present in the child class. Constructors must not necessarily be public, they will be used via
 * reflection.
 */
public abstract class LogiwareAppCode extends DynamicCode {

	/**
	 * This key value is generally considered "NULL" in Logiware. Note that there is no clear definition if NULL or 000 is the canonic form.
	 */
	protected final static String APPCODE_NULL_KEY = "000";

	/**
	 * Constructor
	 * 
	 * @param id
	 * @param key
	 * @param shortText
	 * @param longText
	 */
	protected LogiwareAppCode(final int id, final Object key, final String shortText, final String longText) {
		super(id, key, shortText, longText);
	}

	/**
	 * Constructor
	 * 
	 * @param key
	 * @param shortText
	 * @param longText
	 */
	protected LogiwareAppCode(final Object key, final String shortText, final String longText) {
		super(key, shortText, longText);
	}

	@Override
	public final boolean isNull() {
		final Object key;
		return getId() == 0 || ((key = getKey()) == null || APPCODE_NULL_KEY.equals(key));
	}

	/**
	 * Detects if text starts with "zz".
	 */
	public static boolean isRetired(final CharSequence charSeq) {
		return charSeq != null && charSeq.length() > 2 && charSeq.charAt(0) == 'z' && charSeq.charAt(1) == 'z';
	}

	/**
	 * Defines retired as "long text starts with zz" for logiware codes. Can still be overwritten by child classes.
	 */
	@Override
	public boolean isRetired() {
		return isRetired(getLongText());
	}

}

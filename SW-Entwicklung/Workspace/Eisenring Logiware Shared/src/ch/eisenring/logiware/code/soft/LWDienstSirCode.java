package ch.eisenring.logiware.code.soft;

import ch.eisenring.core.codetype.LoadQuery;
import ch.eisenring.logiware.code.LogiwareAppCode;

@LoadQuery("SELECT " +
		     "C.CD_Hard AS ${Key}, " +
		     "C.CD_Soft AS ${ShortText}, " +
		     "C<PERSON>Bezeichnung AS ${LongText} " +
		   "FROM " +
		     "${DB}${Schema}APPCODE AS C " +
		   "WHERE " +
		     "C.CD_Sprache = '001' AND " +
		     "C.Tab_Prfx = 'LAKZ' AND " +
		     "C.Col_Name = 'DIENSTSIR'")
public final class LWDienstSirCode extends LogiwareAppCode {
	
	public final static LWDienstSirCode NULL = new LWDienstSirCode(0, null, "", "");

	LWDienstSirCode(final Object key, final String shortText, final String longText) {
		super(key, shortText, longText);
	}

	LWDienstSirCode(final int id, final Object key,
				  final String shortText, final String longText) {
		super(id, key, shortText, longText);
	}

}

package ch.eisenring.logiware.code.soft;

import ch.eisenring.core.codetype.LoadQuery;
import ch.eisenring.logiware.code.LogiwareAppCode;

@LoadQuery("SELECT " +
		     "C.CD_Hard AS ${Key}, " +
		     "C.CD_Soft AS ${ShortText}, " +
		     "C<PERSON>Bezeichnung AS ${LongText} " +
		   "FROM " +
		     "${DB}${Schema}APPCODE AS C " +
		   "WHERE " +
		     "C.CD_Sprache = '001' AND " +
		     "C.Tab_Prfx = 'LAKZ' AND " +
		     "C.Col_Name = 'B2BCODE'")
public final class LWB2BCode extends LogiwareAppCode {

	public final static LWB2BCode NULL = new LWB2BCode(0, null, "", "");

	public final static LWB2BCode C001 = new LWB2BCode(1, "001", "001Ges", "Gesendet an Magic Eddy");


	LWB2BCode(final Object key, final String shortText, final String longText) {
		super(key, shortText, longText);
	}

	LWB2BCode(final int id, final Object key,
              final String shortText, final String longText) {
		super(id, key, shortText, longText);
	}

}

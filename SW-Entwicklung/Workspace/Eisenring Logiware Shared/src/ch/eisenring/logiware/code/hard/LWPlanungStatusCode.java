package ch.eisenring.logiware.code.hard;

import ch.eisenring.core.codetable.RelationalOperator;
import ch.eisenring.core.codetype.StaticCode;

public final class LWPlanungStatusCode extends StaticCode {

	public final static LWPlanungStatusCode UNGEPLANT = new LWPlanungStatusCode(0, null, "U", "Ungeplant");
	// Entfällt ab 20.04.2016
	//public final static LWPlanungStatusCode GROBGEPLANT = new LWPlanungStatusCode(1, "PLG", "G", "Grobplanung");
	public final static LWPlanungStatusCode GEPLANT = new LWPlanungStatusCode(2, "PLF", "F", "Geplant");
	public final static LWPlanungStatusCode MONTAGE = new LWPlanungStatusCode(3, "PLM", "M", "Montage");
	public final static LWPlanungStatusCode ABGESCHLOSSEN = new LWPlanungStatusCode(4, "<PERSON>L<PERSON>", "<PERSON>", "<PERSON>ü<PERSON> beendet");
	
	LWPlanungStatusCode(final int id, final Object key,
					  final String shortText, final String longText) {
		super(id, key, shortText, longText);
	}

	/**
	 * Checks if this status is >= the given status
	 */
	public boolean isGreaterOrEqual(final LWPlanungStatusCode otherStatus) {
		if (otherStatus == null) {
			return UNGEPLANT.equals(this);
		} else {
			return getId() >= otherStatus.getId();
		}
	}

	/**
	 * Status comparison: [this comparison operator argument]
	 */
	public boolean evaluate(final RelationalOperator operator, final LWPlanungStatusCode otherStatus) {
		final int a = getId();
		final int b = otherStatus == null ? 0 : otherStatus.getId();
		return operator.evaluate(a, b);
	}

}

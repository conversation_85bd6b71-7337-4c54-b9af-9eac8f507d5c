package ch.eisenring.logiware.code.soft;

import ch.eisenring.core.codetype.LoadQuery;
import ch.eisenring.core.collections.Lookup;
import ch.eisenring.core.collections.impl.HashMap;
import ch.eisenring.logiware.code.LWGranitCode;
import ch.eisenring.logiware.code.hard.LWObjektTyp;

/** <ul><li>aaa = a Objekt</li><li>bbb = b <PERSON>zel</li><li>ccc = c Einzel (Laufkundschaft)</li><li>ddd = d Mietobjekt</li></ul> **/
@LoadQuery("SELECT " +
	         "C.CD_Hard AS ${Key}, " +
	         "C.CD_Soft AS ${ShortText}, " +
	         "C.Bezeichnung AS ${LongText} " +
	       "FROM " +
	         "${DB}${Schema}APPCODE AS C " +
	       "WHERE " +
	         "C.CD_Sprache = '001' AND " +
	         "C.Tab_Prfx = 'LAKZ' AND " +
	         "C.Col_Name = 'SCH_BLOCK2'")
public final class LWGranit2Code extends LWGranitCode {

	public final static LWGranit2Code NULL = new LWGranit2Code(
			0, null, "", "");

	public final static LWGranit2Code EIGENTUM = new LWGranit2Code(
			1, "aaa", "a", "");

	public final static LWGranit2Code EINZEL_B = new LWGranit2Code(
			2, "bbb", "b", "");

	public final static LWGranit2Code EINZEL_C = new LWGranit2Code(
			3, "ccc", "c", "");

	public final static LWGranit2Code MIET = new LWGranit2Code(
			4, "ddd", "d", "");

	private final static Lookup<Object, LWObjektTyp> OBJEKTTYP_LOOKUP;
	static {
		final Lookup<Object, LWObjektTyp> l = new HashMap<>();
		l.put(EIGENTUM.getKey(), LWObjektTyp.EIGENTUM);
		l.put(EINZEL_B.getKey(), LWObjektTyp.EINZEL);
		l.put(EINZEL_C.getKey(), LWObjektTyp.EINZEL);
		l.put(MIET.getKey(), LWObjektTyp.MIET);
		OBJEKTTYP_LOOKUP = l;
	}

	LWGranit2Code(final int id, final Object key,
		    final String shortText, final String longText) {
		super(id, key, shortText, longText);
	}
	
	LWGranit2Code(final Object key, final String shortText, final String longText) {
		super(key, shortText, longText);
	}

	// --------------------------------------------------------------
	// ---
	// --- Business meanings (hard coded, can't be derived from anywhere)
	// ---
	// --------------------------------------------------------------
	public LWObjektTyp getObjektTyp() {
		final LWObjektTyp result = OBJEKTTYP_LOOKUP.get(getKey());
		return result == null ? LWObjektTyp.NULL : result;
	}

	/**
	 * Returns true if: Code is for "Einzelküche"
	 */
	public boolean isEinzelkueche() {
		final LWObjektTyp objektTyp = getObjektTyp();
		return LWObjektTyp.EINZEL.equals(objektTyp);
	}

	/**
	 * Returns true if: Code is for "Eigentumsobjekt"
	 */
	public boolean isEigentumsobjekt() {
		final LWObjektTyp objektTyp = getObjektTyp();
		return LWObjektTyp.EIGENTUM.equals(objektTyp);
	}

	/**
	 * Returns true if: Code is for "Mietobjekt"
	 */
	public boolean isMietobjekt() {
		final LWObjektTyp objektTyp = getObjektTyp();
		return LWObjektTyp.MIET.equals(objektTyp);
	}

	/**
	 * Returns true if: Code is for "Mietobjekt" or "Eigentumsobjekt"
	 */
	public boolean isObjekt() {
		final LWObjektTyp objektTyp = getObjektTyp();
		return LWObjektTyp.EIGENTUM.equals(objektTyp) || LWObjektTyp.MIET.equals(objektTyp);
	}

}

package ch.eisenring.logiware.code.soft;

import ch.eisenring.core.codetype.LoadQuery;
import ch.eisenring.core.collections.Lookup;
import ch.eisenring.core.collections.impl.HashMap;
import ch.eisenring.logiware.code.LWGranitCode;

@LoadQuery("SELECT " +
	"C.CD_Hard AS ${Key}, " +
	"C.CD_Soft AS ${ShortText}, " +
	"C.Bezeichnung AS ${LongText} " +
	"FROM " +
	"${DB}${Schema}APPCODE AS C " +
	"WHERE " +
	"C.CD_Sprache = '001' AND " +
	"C.Tab_Prfx = 'LAKZ' AND " +
	"C.Col_Name = 'SCH_BLOCK8'")
public final class LWGranit8Code extends LWGranitCode {

	// TODO Prod-Schlüssel #589: neu sind noch 003, 006, 015 vorhanden. Die anderen Schlüssel sollen entfernt werden - diese sollten (auch in der DB) nicht mehr vorhanden sein.

	public final static LWGranit8Code NULL = new LWGranit8Code(0, null, "", "");
	public final static LWGranit8Code C03 = new LWGranit8Code(8, "003", "", "");
	public final static LWGranit8Code C06 = new LWGranit8Code(9, "006", "", "");
	// public final static LWGranit8Code C10 = new LWGranit8Code(1, "010", "", "");
	public final static LWGranit8Code C15 = new LWGranit8Code(2, "015", "", "");
	// public final static LWGranit8Code C20 = new LWGranit8Code(3, "020", "", "");
	// public final static LWGranit8Code C25 = new LWGranit8Code(4, "025", "", "");
	// public final static LWGranit8Code C30 = new LWGranit8Code(5, "030", "", "");
	// public final static LWGranit8Code C40 = new LWGranit8Code(6, "040", "", "");
	// public final static LWGranit8Code C50 = new LWGranit8Code(7, "050", "", "");

	private LWGranit8Code(int id, Object key, String shortText, String longText) {
		super(id, key, shortText, longText);
	}

	private LWGranit8Code(Object key, String shortText, String longText) {
		super(key, shortText, longText);
	}

	// --------------------------------------------------------------
	// ---
	// --- Business meanings (hard coded, can't be derived from anywhere)
	// ---
	// --------------------------------------------------------------

	private final static Lookup<LWGranit8Code, Integer> DELAY_DAYS_2501 = new HashMap<LWGranit8Code, Integer>()
		.add(C03, 3)
		.add(C06, 8)
		// .add(C10, 0)
		.add(C15, 0)
	// .add(C20, 1)
	// .add(C30, 5)
	// .add(C40, 7)
	// .add(C50, 9)
	;
	// static {
	// final Lookup<Object, Integer> map = new HashMap<>();
	// map.put("010", Integer.valueOf(0));
	// map.put("015", Integer.valueOf(0));
	// map.put("020", Integer.valueOf(1));
	// map.put("020", Integer.valueOf(1));
	// map.put("030", Integer.valueOf(5));
	// map.put("040", Integer.valueOf(7));
	// map.put("050", Integer.valueOf(9));
	// DELAY_DAYS_2501 = map;
	// }

	public int getDelayWorkDays2501() {
		Integer result = DELAY_DAYS_2501.get(this); // (getKey());
		return result == null ? -1 : result;
	}

	private final static Lookup<LWGranit8Code, Integer> DELAY_DAYS_2503 = new HashMap<LWGranit8Code, Integer>()
		.add(C03, 3)
		.add(C06, 8)
		// .add(C10, 1)
		.add(C15, 1)
	// .add(C20, 2)
	// .add(C30, 5)
	// .add(C40, 7)
	// .add(C50, 9)
	;
	// static {
	// final Lookup<Object, Integer> map = new HashMap<>();
	// map.put("010", Integer.valueOf(1));
	// map.put("015", Integer.valueOf(1));
	// map.put("020", Integer.valueOf(2));
	// map.put("025", Integer.valueOf(2));
	// map.put("030", Integer.valueOf(5));
	// map.put("040", Integer.valueOf(7));
	// map.put("050", Integer.valueOf(9));
	// DELAY_DAYS_2503 = map;
	// }

	/**
	 * Determines the allowed delay for granite in days
	 * after kitchen furniture has been mounted.
	 * This is hard coded against Logiware code keys.
	 * @return -1 if code is unknown.
	 */
	public int getDelayWorkDaysGranite() {
		Integer result = DELAY_DAYS_2503.get(this); // (getKey());
		return result == null ? -1 : result;
	}

}

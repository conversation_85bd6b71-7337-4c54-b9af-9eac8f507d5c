package ch.eisenring.logiware.code.hard;

import ch.eisenring.core.codetype.StaticCode;

/**
 * Code indicating if product is on stock
 */
public final class LWLagerArtCode extends StaticCode {

	public final static LWLagerArtCode KEIN_LAGERARTIKEL = new LWLagerArtCode(
			0, "000", "N", "Kein Lagerartikel");

	public final static LWLagerArtCode LAGERARTIKEL = new LWLagerArtCode(
			1, "001", "J", "Lagerartikel");

	private LWLagerArtCode(final int id, final String key,
			final String shortText, final String longText) {
		super(id, key, shortText, longText);
	}

	// --------------------------------------------------------------
	// ---
	// --- API
	// ---
	// --------------------------------------------------------------
	public boolean isLagerArtikel() {
		return getId() == LAGERARTIKEL.getId();
	}

}

package ch.eisenring.logiware.code.soft;

import ch.eisenring.core.codetype.DynamicCode;
import ch.eisenring.core.codetype.LoadQuery;
import ch.eisenring.logiware.code.LogiwareAppCode;

@LoadQuery("SELECT " +
		     "G1.Gruppe AS ${Key}, " +
			 "G1.Gruppe AS ${ShortText}, " +
			 "G1.Bezeichnung AS ${LongText}, " +
			 "G2.Bezeichnung AS ${eMail} " +
		   "FROM " +
			 "${DB}${Schema}GBZGRUPPE G1 " +
		   "LEFT JOIN " +
			 "${DB}${Schema}GBZGRUPPE G2 " +
		   "ON " +
			 "G1.Gruppe = G2.Gruppe AND G1.Id_GSE = G2.Id_GSE AND G2.GBZGrArt = '940' " +
		   "WHERE " +
			 "G1.GBZGrArt = '900' AND G1.Id_GSE = '2000'")
public final class LWKundenberaterCode extends DynamicCode {

	public final static LWKundenberaterCode NULL = new LWKundenberaterCode(0, null, "", "");

	public final static LWKundenberaterCode Z100 = new LWKundenberaterCode(1, "z-100", "", "");
	
	LWKundenberaterCode(final Object key, final String shortText, final String longText) {
		super(key, shortText, longText);
	}

	LWKundenberaterCode(final int id, final Object key,
			          final String shortText, final String longText) {
		super(id, key, shortText, longText);
	}

	/**
	 * Gets the eMail address of Kundenberater. Null if no mail address was found.
	 */
	public String getEMail() {
		return (String) getData("eMail", null);
	}

	@Override
	public boolean isRetired() {
		return LogiwareAppCode.isRetired(getLongText());
	}

}

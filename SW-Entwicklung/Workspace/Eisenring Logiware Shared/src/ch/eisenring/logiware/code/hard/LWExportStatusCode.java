package ch.eisenring.logiware.code.hard;

import ch.eisenring.core.codetype.StaticCode;

public final class LWExportStatusCode extends StaticCode {

	public final static LWExportStatusCode NULL = new LWExportStatusCode(0, null, "", "");

	public final static LWExportStatusCode S001 = new LWExportStatusCode(1, "001", "Ins", "Inserted");

	public final static LWExportStatusCode S002 = new LWExportStatusCode(2, "002", "Pro", "Processed");

	private LWExportStatusCode(final int id, final Object key,
			final String shortText, final String longText) {
		super(id, key, shortText, longText);
	}

}

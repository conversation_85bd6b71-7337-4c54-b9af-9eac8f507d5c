package ch.eisenring.logiware;

import java.io.IOException;

import ch.eisenring.core.datatypes.primitives.Primitives;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.StringMakerFriendly;
import ch.eisenring.core.io.StreamReader;
import ch.eisenring.core.io.StreamWriter;
import ch.eisenring.core.io.Streamable;
import ch.eisenring.model.PrimaryKey;

/**
 * Common base class for logiware primary keys
 */
public abstract class LWObjectKey implements Streamable, PrimaryKey, StringMakerFriendly {

	// --------------------------------------------------------------
	// ---
	// --- Array-like key element access
	// ---
	// --------------------------------------------------------------
	/**
	 * Returns the number of elements in this key
	 */
	public abstract int getKeyValueCount();

	/**
	 * Gets element N of this key.
	 */
	public abstract Object getKeyValue(final int elementIndex) throws IndexOutOfBoundsException;
	
	// --------------------------------------------------------------
	// ---
	// --- PrimaryKey implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public Object getKeyValue() {
		final Object[] result = new Object[getKeyValueCount()];
		for (int i=0; i<result.length; ++i)
			result[i] = getKeyValue(i);
		return result;
	}

	@Override
	public final Class<?> getValueClass() {
		return Object[].class;
	}
	
	@Override
	public boolean isValueEqual(final Object value) {
		if (!(value instanceof Object[]))
			return false;
		final Object[] otherValues = (Object[]) value;
		final int keyCount = getKeyValueCount();
		if (otherValues.length != keyCount)
			return false;
		for (int i=0; i<keyCount; ++i) {
			if (!Primitives.equals(otherValues[i], getKeyValue(i)))
				return false;
		}
		return true;
	}

	// --------------------------------------------------------------
	// ---
	// --- Streaming implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public abstract void read(StreamReader reader) throws IOException;

	@Override
	public abstract void write(StreamWriter writer) throws IOException;

	// --------------------------------------------------------------
	// ---
	// --- Object overrides
	// ---
	// --------------------------------------------------------------
	/**
	 * The hash code is is type specific.
	 * 
	 * Child classes may want to override this with their own implementation,
	 * especially if they have primitive types as key components.
	 */
	public int hashCode() {
		int h = 0;
		for (int i=getKeyValueCount(); i>=0; --i) {
			h = (h * 37) ^ Primitives.hashCode(getKeyValue(i));
		}
		return h;
	}

	/**
	 * Checks if this key equals another key. Two keys are equal
	 * if they are of the same class and their field values match.
	 * 
	 * Child classes may want to override this with their own implementation,
	 * especially if they have primitive types as key components.
	 */
	public boolean equals(final Object o) {
		if (!(o instanceof LWObjectKey))
			return false;
		if (!o.getClass().equals(getClass()))
			return false;
		final LWObjectKey key = (LWObjectKey) o;
		for (int i=getKeyValueCount()-1; i>=0; --i) {
			final Object k0 = getKeyValue(i);
			final Object k1 = key.getKeyValue(i);
			if (!Primitives.equals(k0, k1))
				return false;
		}
		return true;
	}

	@Override
	public final void toStringMaker(final StringMaker target) {
		target.append(Primitives.getSimpleName(getClass()));
		target.append('[');
		final int keyCount = getKeyValueCount();
		for (int i=0; i<keyCount; ++i) {
			if (i > 0)
				target.append('|');
			target.append(getKeyValue(i));
		}
		target.append(']');
	}

	@Override
	public final String toString() {
		return StringMakerFriendly.toString(this);
	}

}

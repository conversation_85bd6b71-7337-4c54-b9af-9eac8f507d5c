package ch.eisenring.logiware.util;

import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.format.api.FloatFormat;

public abstract class LWUtil {

	public final static FloatFormat POSITIONSNR = new FloatFormat() {
		@Override
		public void appendTo(final StringMaker target, final double posNr) {
			if (Double.isNaN(posNr) || posNr <= 0F)
				return;
			final int i = (int) Math.round(posNr * 100D);
			target.append((i / 100));
			target.append('.');
			target.append((char) (((i / 10) % 10) + 48));
			target.append((char) ( (i       % 10) + 48));			
		}

		@Override
		public void appendTo(final StringMaker target, final float posNr) {
			appendTo(target, (double) posNr);
		}
	};

	protected LWUtil() {
	}

	// --------------------------------------------------------------
	// ---
	// --- Position related helper methods
	// ---
	// --------------------------------------------------------------
	/**
	 * Formats Position number for display. Invalid numbers
	 * (negative, NaN) convert to empty string.
	 */
	public static String formatPosNr(final double posNr) {
		if (Double.isNaN(posNr) || posNr <= 0D)
			return "";
		final StringMaker b = StringMaker.obtain();
		b.append(posNr, POSITIONSNR);
		return b.release();
	}

	/**
	 * Appends Position number to maker. Invalid numbers
	 * (negative, NaN) convert to empty string.
	 */
	public static void appendPosNr(final StringMaker target, final double posNr) {
		target.append(posNr, POSITIONSNR);
	}

}

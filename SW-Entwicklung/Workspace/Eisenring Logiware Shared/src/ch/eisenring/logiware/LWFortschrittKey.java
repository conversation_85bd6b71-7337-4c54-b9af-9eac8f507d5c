package ch.eisenring.logiware;

import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.datatypes.primitives.Primitives;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.io.StreamReader;
import ch.eisenring.core.io.StreamWriter;
import ch.eisenring.core.sort.Comparator;
import ch.eisenring.logiware.code.soft.GSECode;

import java.io.IOException;

/**
 * Identifies a Logiware Fortschritt
 */
public final class LWFortschrittKey extends LWObjectKey implements Comparable<LWFortschrittKey> {

	private int auftragNr;
	private double posNr;
	private int formularNummer;
	private GSECode gse;

	// --------------------------------------------------------------
	// ---
	// --- Factory methods & Constructors
	// ---
	// --------------------------------------------------------------
	LWFortschrittKey() {
	}

	public static LWFortschrittKey get(final int auftragNr, final double posNr, final int formularNummer, final GSECode gse) {
		final LWFortschrittKey key = new LWFortschrittKey();
		key.auftragNr = auftragNr;
		key.posNr = posNr;
		key.formularNummer = formularNummer;
		key.gse = gse;
		return key;
	}

	// --------------------------------------------------------------
	// ---
	// --- Key specific API
	// ---
	// --------------------------------------------------------------

	public int getAuftragNr() { return auftragNr; }

	public double getPosNr() { return posNr; }

	public int getFormularNummer() {
		return formularNummer;
	}

	public GSECode getGSE() {
		return gse;
	}

	// --------------------------------------------------------------
	// ---
	// --- Generic Key API
	// ---
	// --------------------------------------------------------------
	@Override
	public int getKeyValueCount() {
		return 4;
	}

	@Override
	public Object getKeyValue(final int elementIndex) {
		switch (elementIndex) {
			case 0: return Integer.valueOf(auftragNr);
			case 1: return Double.valueOf(posNr);
			case 2: return Integer.valueOf(formularNummer);
			case 3: return gse;
			default: throw new IndexOutOfBoundsException();
		}
	}	
	// --------------------------------------------------------------
	// ---
	// --- Comparable implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public int compareTo(final LWFortschrittKey key) {
		final String key1 = Strings.toString(AbstractCode.getKey(getGSE(), "???")); 
		final String key2 = Strings.toString(AbstractCode.getKey(key.getGSE(), "???")); 
		int result = Strings.compare(key1, key2);
		if (result != 0)
			return result;

		result = Comparator.compareUnsigned(getFormularNummer(), key.getFormularNummer());
		if (result != 0)
			return result;

		result = Comparator.compareUnsigned(getAuftragNr(), key.getAuftragNr());
		if (result != 0)
			return result;

		result = Comparator.compareUnsigned(getPosNr(), key.getPosNr());

		return result;
	}
	
	// --------------------------------------------------------------
	// ---
	// --- Streamable implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public void read(final StreamReader reader) throws IOException {
		auftragNr = reader.readInt();
		posNr = reader.readDouble();
		formularNummer = reader.readInt();
		gse = reader.readCode(GSECode.class);
	}

	@Override
	public void write(final StreamWriter writer) throws IOException {
		writer.writeInt(auftragNr);
		writer.writeDouble(posNr);
		writer.writeInt(formularNummer);
		writer.writeCode(gse);
	}

	// --------------------------------------------------------------
	// ---
	// --- Object overrides
	// ---
	// --------------------------------------------------------------
	@Override
	public int hashCode() {
		return (auftragNr * 131) ^ ((int)posNr * 71) ^ (formularNummer* 31) ^ AbstractCode.hashCode(gse);
	}

	@Override
	public boolean equals(final Object o) {
		if (!(o instanceof LWFortschrittKey))
			return false;
		final LWFortschrittKey k = (LWFortschrittKey) o;
		return k.auftragNr == auftragNr
				&& k.posNr == posNr
				&& k.formularNummer == formularNummer
				&& Primitives.equals(k.gse, gse);
	}	

}

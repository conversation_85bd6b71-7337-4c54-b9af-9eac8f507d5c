package ch.eisenring.print.shared.model;

import net.sf.jasperreports.engine.JRException;
import net.sf.jasperreports.engine.JRField;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.Map;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.collections.impl.HashMap;
import ch.eisenring.core.datatypes.primitives.ConversionUtil;
import ch.eisenring.core.util.ReflectiveAttributeGetter;

/**
 * Predefined generic report data source completely based on parameters.
 * 
 * This source is streamable, if all parameters are streamable types.
 */
public class ParameterReportSource extends PRTReportDataSource {

	protected final Map<String, Object> parameterMap = new HashMap<>();
	protected int recordCount;
	protected int recordIndex = -1;
	protected List<?> records;

	/**
	 * Creates a source with one record
	 */
	public ParameterReportSource() {
		this(1);
	}

	/**
	 * Creates a source with the given number of records
	 */
	public ParameterReportSource(final int recordCount) {
		this.recordCount = recordCount;
	}

	/**
	 * Creates a source with the given number of records and specified parameters
	 */
	public ParameterReportSource(final int recordCount, final Map<String, Object> parameterMap) {
		if (parameterMap != null) {
			this.parameterMap.putAll(parameterMap);
		}
		this.recordCount = recordCount;
	}

	/**
	 * Sets records for this data source.
	 * 
	 * Setting the records will override the default number of (empty) records
	 * for this data source to the size of the collection provided.
	 */
	public void setRecords(final java.util.Collection<?> records) {
		this.recordCount = records.size();
		this.records = new ArrayList<>(records);
	}

	// --------------------------------------------------------------
	// ---
	// --- PRTReportDataSource implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public void moveFirst() throws JRException {
		recordIndex = -1;
	}

	@Override
	public boolean next() throws JRException {
		final int nextIndex = recordIndex + 1;
		if (nextIndex >= recordCount)
			return false;
		recordIndex = nextIndex;
		return true;
	}

	/**
	 * Gets the value for a report field for current iteration state.
	 */
	@Override
	public Object getFieldValue(final JRField field) throws JRException {
		final Object record = getCurrent();
		if (record == null)
			return null;
		final String name = field.getName();
		try {
			Object result;
			result = ReflectiveAttributeGetter.getAttributeValue(record, name);
			return ConversionUtil.convert(result, (String) null);
		} catch (final Exception e) {
			// field / getter not found
		}
		Object o = super.getFieldValue(field);
		return ConversionUtil.convert(o, (String) null);		
	}
	
	@Override
	public Object getCurrent() {
		final List<?> records = this.records;
		if (records == null)
			return null;
		if (recordIndex < 0 || recordIndex >= records.size())
			return null;
		return records.get(recordIndex);
	}

}

package ch.eisenring.print.shared.model;

import java.util.Iterator;

import net.sf.jasperreports.engine.JRException;
import net.sf.jasperreports.engine.JRField;
import ch.eisenring.core.collections.Collection;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.Map;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.util.ReflectiveAttributeGetter;

/**
 * Implements a custom data source based on a collection of objects
 * for use with JasperReports.
 */
public class AbstractReportSource extends PRTReportDataSource {

	private final ArrayList<Object> modelList = new ArrayList<Object>();

	// iteration state
	private int index;

	// --------------------------------------------------------------
	// ---
	// --- Constructors
	// ---
	// --------------------------------------------------------------
	public AbstractReportSource() {
		setModelList(null);
	}

	public AbstractReportSource(final java.util.Collection<?> collection) {
		setModelList(collection);
	}

	public AbstractReportSource(final Object ... printModels) {
		setModelList(List.asList(printModels));
	}

	// --------------------------------------------------------------
	// ---
	// --- Additional API
	// ---
	// --------------------------------------------------------------
	public void setModelList(final java.util.Collection<?> collection) {
		this.modelList.clear();
		if (collection != null) {
			this.modelList.addAll(collection);
		}
		try {
			moveFirst();
		} catch (final JRException e) {
			// can't happen...
			throw new RuntimeException(e.getMessage(), e);
		}
	}

	/**
	 * Gets all models in this source. For internal use, and use with care.
	 */
	protected List<Object> getModelList() {
		return new ArrayList<>(modelList);
	}

	/**
	 * Gets the current record
	 */
	public Object getCurrent() {
		if (index < 0 || index >= modelList.size())
			return null;
		return modelList.get(index);
	}

	/**
	 * Gets the previous record
	 */
	public Object getPrevious() {
		final int j = index - 1;
		if (j < 0 || j >= modelList.size())
			return null;
		return modelList.get(j);
	}

	private boolean modelsPopulated;

	/**
	 * Triggers filling of the model list.
	 */
	@Override
	public final void populateModels() {
		if (!modelsPopulated) {
			modelsPopulated = true;
			populateModelsImpl();
		}
	}
	
	protected void populateModelsImpl() {
		// nothing by default
	}

	/**
	 * This method is called to populate the given map with
	 * the parameters for the report.
	 * Which parameters are used is report specific.
	 */
	public void populateParameters(final Map<String, Object> map) {
		final long now = System.currentTimeMillis();
		map.put("DRUCKDATUM", TIMESTAMP_FORMAT.format(now));
	}

	/**
	 * Get the number of "items" in this data source.
	 */
	public int getSize() {
		return modelList.size();
	}

	public boolean isEmpty() {
		return modelList.isEmpty();
	}

	// --------------------------------------------------------------
	// ---
	// --- Utility methods
	// ---
	// --------------------------------------------------------------
	/**
	 * Truncates a collection to the given maximum number of records
	 */
	public static ArrayList<?> truncate(final Collection<?> collection, final int maxRecords) {
		final ArrayList<Object> list = new ArrayList<Object>(maxRecords);
		if (collection != null) {
			final Iterator<?> i = collection.iterator();
			while (i.hasNext() && list.size() < maxRecords) {
				final Object record = i.next();
				list.add(record);
			}
		}
		return list;
	}

	// --------------------------------------------------------------
	// ---
	// --- JRRewindableDataSource implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public void moveFirst() throws JRException {
		index = -1;
	}

	@Override
	public boolean next() throws JRException {
		if (index < modelList.size())
			++index;
		return index < modelList.size();
	}

	@Override
	public Object getFieldValue(final JRField field) throws JRException {
		Object result = null;
		final Object model = getCurrent();
		if (model != null && field != null) {
			final String name = field.getName();
			try {
				result = ReflectiveAttributeGetter.getAttributeValue(model, name);
			} catch (final Exception e) {
				result = null;
			}
			if (result == null) {
				try {
					result = ReflectiveAttributeGetter.getAttributeValue(this, name);
				} catch (final Exception e) {
					result = null;
				}
			}
		}
		return result;
	}
	
}

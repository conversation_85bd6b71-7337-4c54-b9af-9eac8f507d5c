package ch.eisenring.print.shared.network;

import ch.eisenring.core.io.AutoStreamed;

import javax.print.DocFlavor;
import javax.print.attribute.Attribute;
import javax.print.attribute.HashPrintServiceAttributeSet;
import javax.print.attribute.PrintServiceAttributeSet;

/**
 * Packet used to query for the printers that exist on the remote side.
 */
public final class PacketPrinterServiceGetRequest extends AbstractPRTPacket {

	@AutoStreamed
	private DocFlavor docFlavor;

	@AutoStreamed
	private Attribute[] attributes;

	private PacketPrinterServiceGetRequest() {
		super(CAPS_SEQUENCE_REQUEST, PRIORITY_REQUEST);
	}

	// --------------------------------------------------------------
	// ---
	// --- Factory methods
	// ---
	// --------------------------------------------------------------

	public static PacketPrinterServiceGetRequest create() {
		return create(null, null);
	}

	public static PacketPrinterServiceGetRequest create(final DocFlavor docFlavor, final PrintServiceAttributeSet attributeSet) {
		final PacketPrinterServiceGetRequest packet = new PacketPrinterServiceGetRequest();
		packet.docFlavor = docFlavor;
		packet.attributes = attributeSet == null ? null : attributeSet.toArray();
		return packet;
	}

	// --------------------------------------------------------------
	// ---
	// --- Packet API
	// ---
	// --------------------------------------------------------------

	public DocFlavor getDocFlavor() {
		return docFlavor;
	}

	public PrintServiceAttributeSet getAttributeSet() {
		PrintServiceAttributeSet result = null;
		if (attributes != null) {
			result = new HashPrintServiceAttributeSet();
			for (Attribute attribute : attributes)
				result.add(attribute);
		}
		return result;
	}

}

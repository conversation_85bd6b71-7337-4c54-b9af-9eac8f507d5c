package ch.eisenring.print.shared.network;

import ch.eisenring.network.packet.AbstractPacket;
import ch.eisenring.network.security.SecurityDomain;

public abstract class AbstractPRTPacket extends AbstractPacket {

	protected AbstractPRTPacket(final int caps, final int priority) {
		super(caps, priority);
	}

	public final static int COMPATIBILITY_MASK = allocateCompatibilityMaskBit();
	
	public final static SecurityDomain PRT_DOMAIN = SecurityDomain.get("PRT");

	@Override
	public int getCompatibilityMask() {
		return COMPATIBILITY_MASK;
	}
	
	@Override
	public SecurityDomain getSecurityDomain() {
		return PRT_DOMAIN;
	}

}

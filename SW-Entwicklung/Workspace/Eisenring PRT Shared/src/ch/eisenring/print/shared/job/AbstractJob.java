package ch.eisenring.print.shared.job;

import ch.eisenring.core.logging.Logger;
import ch.eisenring.core.tag.Tag;
import ch.eisenring.core.tag.TagSet;

/**
 * Base class for print jobs
 */
public abstract class AbstractJob {

    protected JobProgressListener progressListener;
    protected final TagSet tagSet;
	
	protected AbstractJob(
			final JobProgressListener progressListener,
			final Object ... tagValues) {
		this.progressListener = progressListener;
		this.tagSet = new TagSet(tagValues);
	}

    public final <U> void addTag(final Tag<U> tag, final U value) {
    	if (tag != null) {
    		tagSet.add(tag, value);
    	}
    }
   
	/**
	 * Sets the progress in fractions of 1.0
	 */
	protected void setProgress(final double progress) {
		if (tagSet != null && tagSet.get(JobTags.SUPPRESS_PROGRESS, false)) {
			return;
		}
		final JobProgressListener listener = progressListener;
		if (listener != null) {
			try {
				listener.setProgress(progress);
			} catch (final Exception e) {
				Logger.warn("JobProgressListener Error:");
				Logger.warn(e);
			}
		}
	}

	/**
	 * Called in its own thread. The job terminates when this method returns.
	 * (Should be protected!)
	 */
	protected abstract void performJob();

	/**
	 * Executes another job as sub job
	 */
	public final void performSubJob(final AbstractJob subJob) {
		subJob.addTag(JobTags.SUPPRESS_PROGRESS, Boolean.TRUE);
		subJob.performJob();
	}

	/**
	 * Called when the job has ended (regardless on how it ended)
	 */
	protected void finished() {
	}

}

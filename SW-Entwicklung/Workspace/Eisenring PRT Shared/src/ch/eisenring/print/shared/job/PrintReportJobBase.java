package ch.eisenring.print.shared.job;

import java.awt.print.PrinterJob;

import javax.print.attribute.HashPrintRequestAttributeSet;
import javax.print.attribute.PrintRequestAttributeSet;
import javax.print.attribute.standard.Chromaticity;
import javax.print.attribute.standard.Copies;
import javax.print.attribute.standard.MediaSizeName;
import javax.print.attribute.standard.OrientationRequested;

import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.Map;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.collections.impl.HashMap;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.io.binary.BinaryHolder;
import ch.eisenring.core.io.binary.BinaryHolderUtil;
import ch.eisenring.core.io.memory.MemoryOutputStream;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.core.threading.ThreadCore;
import ch.eisenring.core.util.OperationResult;
import ch.eisenring.print.shared.PrintServiceManager;
import ch.eisenring.print.shared.model.PRTReportDataSource;
import ch.eisenring.print.shared.model.PrintServiceIdentifier;
import ch.eisenring.print.shared.resource.ReportResource;
import net.sf.jasperreports.engine.JasperFillManager;
import net.sf.jasperreports.engine.JasperPrint;
import net.sf.jasperreports.engine.JasperReport;
import net.sf.jasperreports.export.Exporter;
import net.sf.jasperreports.export.ExporterInput;
import net.sf.jasperreports.export.OutputStreamExporterOutput;
import net.sf.jasperreports.export.PdfExporter;
import net.sf.jasperreports.export.PrintServiceExporter;
import net.sf.jasperreports.export.SimpleExporterInput;
import net.sf.jasperreports.export.SimpleOutputStreamExporterOutput;
import net.sf.jasperreports.export.SimplePrintServiceExporterConfiguration;
import net.sf.jasperreports.export.SimpleExporterInput;

public abstract class PrintReportJobBase extends AbstractJob {

	static class ReportEntry {
		public final ReportResource reportResource;
		public final PRTReportDataSource dataSource;
		
		public ReportEntry(final ReportResource reportResource, final PRTReportDataSource dataSource) {
			this.reportResource = reportResource;
			this.dataSource = dataSource;
		}
	}

	protected final List<ReportEntry> entries = new ArrayList<>();

    protected PrintReportJobBase(
			  final JobProgressListener progressListener,
			  final Object ... tagValues) {
    	super(progressListener == null ? JobProgressListener.NULL : progressListener, tagValues);
    }

    protected PrintReportJobBase(
    					  final ReportResource reportResource,
    					  final PRTReportDataSource dataSource,
    					  final JobProgressListener progressListener,
    					  final Object ... tagValues) {
    	super(progressListener == null ? JobProgressListener.NULL : progressListener, tagValues);
    	addReport(reportResource, dataSource);
	}

    public void addReport(final ReportResource reportResource, final PRTReportDataSource dataSource) {
    	final ReportEntry entry = new ReportEntry(reportResource, dataSource);
    	entries.add(entry);
    }
   
    protected List<JasperReport> getReports() {
    	final List<JasperReport> result = new ArrayList<>();
    	for (final ReportEntry entry : entries) {
    		final JasperReport report = getReport(entry.reportResource);
    		result.add(report);
    	}
    	return result;
    }
   
    protected JasperReport getReport(final ReportResource reportResource) {
    	if (reportResource == null) {
    		showError("Schwerer Fehler: Report-Ressource fehlt");
    		return null;
    	}
    	final JasperReport report = reportResource.getReport();
    	if (report == null) {
    		showError(Strings.concat("Schwerer Fehler: Report \"", reportResource.getName(), "\" kann nicht geladen werden"));
    		return null;
    	}
    	return report;
    }
   
    protected PrintServiceIdentifier getPrinter() {
    	Object printer = tagSet.get(PrintTags.PRINTER);
    	if (printer instanceof PrintServiceIdentifier) {
    		return (PrintServiceIdentifier) printer;
    	}
   		return PrintServiceManager.lookupDefaultPrintService();
    }

    protected Copies getNumCopies() {
    	final int numCopies = tagSet.get(PrintTags.NUM_COPIES, Integer.valueOf(1));
    	return new Copies(numCopies);
    }

	@Override
	public void performJob() {
		if (entries.isEmpty())
			return;
		setProgress(0.1);

		final PrintServiceIdentifier printer = getPrinter();
		if (printer == null)
			return;
		setProgress(0.2);
	
		// create print object from report
		final List<JasperPrint> prints = new ArrayList<>();
		ReportResource reportResource = null;
		try {
			for (final ReportEntry entry : entries) {
				reportResource = entry.reportResource;
				final PRTReportDataSource dataSource = entry.dataSource;
				JasperPrint jrPrint = null;
				final Map<String, Object> parameters = new HashMap<String, Object>();
				dataSource.populateModels();
				dataSource.populateParameters(parameters);
				final JasperReport report = getReport(reportResource);
				jrPrint = JasperFillManager.fillReport(report, parameters, dataSource);
				prints.add(jrPrint);
			}
		} catch (Exception e) {
			Logger.error(Strings.concat("exception while preparing to print: ", reportResource.getName()));
			Logger.error(e);
			Logger.error(e.getCause());
			showError(Strings.concat("Fehler bei der Aufbereitung der Daten für: ", reportResource.getName()));
			return;
		}
		setProgress(0.3);
	
		// set the selected printer
		try {
			final PrinterJob job = PrinterJob.getPrinterJob();
			job.setPrintService(printer.getPrintService());
		} catch (final Exception e1) {
			ThreadCore.sleep(1250);
			try { // WTF we KNOW the printer exists, try again!
				final PrinterJob job = PrinterJob.getPrinterJob();
				job.setPrintService(printer.getPrintService());
			} catch (final Exception e) {
				Logger.error("error selecting printer: " + e.getMessage());
				showError("Fehler beim Setzen des Druckers \"" + printer + "\": " + e.getMessage());
				return;
			}
		}
		setProgress(0.4);
		
		// print the report to printer
		try {
			Object attr;
			final PrintRequestAttributeSet printRequestAttributeSet = new HashPrintRequestAttributeSet();
			printRequestAttributeSet.add(getNumCopies());
			attr = tagSet.get(PrintTags.PAPER_FORMAT);
			if (attr != null)
				printRequestAttributeSet.add((MediaSizeName) attr);
			attr = tagSet.get(PrintTags.ORIENTATION);
			if (attr != null)
				printRequestAttributeSet.add((OrientationRequested) attr);
			attr = tagSet.get(PrintTags.CHROMATICITY);
			if (attr != null)
				printRequestAttributeSet.add((Chromaticity) attr);
			
			final JRExporter exporter = new JRPrintServiceExporter();
			//JRPdfExporter 
			if (prints.size() == 1) {
				exporter.setParameter(JRExporterParameter.JASPER_PRINT, prints.get(0));
			} else {
				exporter.setParameter(JRExporterParameter.JASPER_PRINT_LIST, prints);
			}
			exporter.setParameter(JRPrintServiceExporterParameter.PRINT_SERVICE, printer.getPrintService());
			exporter.setParameter(JRPrintServiceExporterParameter.PRINT_SERVICE_ATTRIBUTE_SET, printer.getPrintService().getAttributes());
			exporter.setParameter(JRPrintServiceExporterParameter.PRINT_REQUEST_ATTRIBUTE_SET, printRequestAttributeSet);
			exporter.setParameter(JRPrintServiceExporterParameter.DISPLAY_PAGE_DIALOG, Boolean.FALSE);
			exporter.setParameter(JRPrintServiceExporterParameter.DISPLAY_PRINT_DIALOG, Boolean.FALSE);
			setProgress(0.6);
			exporter.exportReport();
		} catch (final Exception e) {
			Logger.error(Strings.concat("exception while attempting to print: ", reportResource.getName()));
			Logger.error(e);
			showError(Strings.concat("Fehler beim Drucken von: ", reportResource.getName(), " (", e.getMessage(), ")"));
		} catch (final OutOfMemoryError e) {
			Logger.error(Strings.concat("exception while attempting to print: ", reportResource.getName()));
			Logger.error(e);
			showError(Strings.concat("Fehler beim Drucken von: ", reportResource.getName(), " (", e.getMessage(), ")"));
		}
	}

	/**
	 * Export to binary data in PDF format
	 */
	public OperationResult<BinaryHolder> exportPDF() {
		return exportDocument(new JRPdfExporter());
	}

	/**
	 * Export to binary data in selected format
	 */
	public OperationResult<BinaryHolder> exportDocument(final JRAbstractExporter exporter) {
		ReportResource reportResource = null;
		try {
			final List<JasperPrint> prints = new ArrayList<>();
			for (final ReportEntry entry : entries) {
				reportResource = entry.reportResource;
				final JasperReport report = getReport(reportResource);
				final PRTReportDataSource dataSource = entry.dataSource;
				JasperPrint jrPrint = null;
				try {
					final Map<String, Object> parameters = new HashMap<String, Object>();
					dataSource.populateModels();
					dataSource.populateParameters(parameters);
					jrPrint = JasperFillManager.fillReport(report, parameters, dataSource);
					prints.add(jrPrint);
				} catch (final Exception e) {
					Logger.error(Strings.concat("exception while preparing to print: ", reportResource.getName()));
					Logger.error(e);
					return new OperationResult<BinaryHolder>(new ErrorMessage(Strings.concat("Fehler: ", e.getMessage())), e, null);
				}
			}
			final MemoryOutputStream memOut = new MemoryOutputStream();
			if (prints.size() == 1) {
				exporter.setParameter(JRExporterParameter.OUTPUT_STREAM, memOut);
				exporter.setParameter(JRExporterParameter.JASPER_PRINT, prints.get(0));
				exporter.exportReport();
				memOut.close();
				return new OperationResult<BinaryHolder>(ErrorMessage.OK, null, BinaryHolderUtil.create(memOut, true));
			} else {
				exporter.setParameter(JRExporterParameter.OUTPUT_STREAM, memOut);
				exporter.setParameter(JRExporterParameter.JASPER_PRINT_LIST, prints);
				exporter.exportReport();
				memOut.close();
				return new OperationResult<BinaryHolder>(ErrorMessage.OK, null, BinaryHolderUtil.create(memOut, true));
			}
		} catch (final Exception e) {
			Logger.error(Strings.concat("exception while attempting to export: ", reportResource.getName()));
			Logger.error(e);
			return new OperationResult<BinaryHolder>(new ErrorMessage(Strings.concat("Fehler: ", e.getMessage())), e, null);
		}
	}

	/**
	 * Called when an error occured and should be implemented by child classes
	 */
	protected abstract void showError(final String message);

}

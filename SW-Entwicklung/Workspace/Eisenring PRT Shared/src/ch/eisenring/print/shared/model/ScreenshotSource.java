package ch.eisenring.print.shared.model;

import java.awt.image.BufferedImage;

import ch.eisenring.core.collections.Collection;
import ch.eisenring.core.collections.impl.ArrayList;

public class ScreenshotSource extends AbstractReportSource {

	protected ScreenshotSource(final Collection<ScreenshotPrintModel> collection) {
		super(collection);
	}

	public static ScreenshotSource create(final BufferedImage ... images) {
		final Collection<ScreenshotPrintModel> collection = new ArrayList<ScreenshotPrintModel>();
		for (final BufferedImage image : images) {
			final ScreenshotPrintModel model = new ScreenshotPrintModel(image);
			collection.add(model);
		}
		return new ScreenshotSource(collection);
	}

}

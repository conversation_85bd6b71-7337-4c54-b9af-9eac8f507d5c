package ch.eisenring.fx;

import java.awt.AWTException;
import java.awt.AlphaComposite;
import java.awt.Color;
import java.awt.Composite;
import java.awt.Graphics;
import java.awt.Graphics2D;
import java.awt.GraphicsConfiguration;
import java.awt.GraphicsDevice;
import java.awt.GraphicsEnvironment;
import java.awt.Rectangle;
import java.awt.Robot;
import java.awt.event.KeyEvent;
import java.awt.event.KeyListener;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.awt.event.MouseMotionListener;
import java.awt.image.BufferedImage;
import java.awt.image.ConvolveOp;
import java.awt.image.Kernel;

import javax.swing.JFrame;

@SuppressWarnings("serial")
public class FullScreenFX extends JFrame {

	{
		addKeyListener(new KeyListener() {
			@Override
			public void keyReleased(final KeyEvent event) {
				inputDetected();
			}
			@Override
			public void keyPressed(KeyEvent e) {
				inputDetected();
			}
			@Override
			public void keyTyped(KeyEvent e) {
				inputDetected();
			}
		});
		addMouseListener(new MouseAdapter() {
			@Override
			public void mousePressed(final MouseEvent event) {
				inputDetected();
			}
			@Override
			public void mouseReleased(final MouseEvent event) {
				inputDetected();
			}
		});
		addMouseMotionListener(new MouseMotionListener() {
			@Override
			public void mouseMoved(final MouseEvent event) {
				inputDetected();
			}
			@Override
			public void mouseDragged(final MouseEvent event) {
				inputDetected();
			}
		});
	}

	long graceUntil;
	boolean inputDetected;
	BufferedImage screenshot;
	BufferedImage backbuffer;
	
	public FullScreenFX() {
		setUndecorated(true);
		setResizable(false);
	}

	public void enterFullscreenMode() {
		GraphicsDevice gd = GraphicsEnvironment.getLocalGraphicsEnvironment().getDefaultScreenDevice();
		GraphicsConfiguration gc = gd.getDefaultConfiguration();
		Rectangle bounds = gc.getBounds();
		setBounds(bounds);
		BufferedImage screenshot = null;
		try {
			Robot robot = new Robot(gd);
			screenshot = robot.createScreenCapture(bounds);
			BufferedImage i2 = new BufferedImage(screenshot.getWidth(), screenshot.getHeight(), BufferedImage.TYPE_INT_ARGB);
			Graphics2D g2d = i2.createGraphics();
			g2d.drawImage(screenshot, 0, 0, null);
			g2d.dispose();
			screenshot = i2;
		} catch (final AWTException e) {
			screenshot = new BufferedImage(bounds.width, bounds.height, BufferedImage.TYPE_INT_ARGB);
		}
		backbuffer = new BufferedImage(screenshot.getWidth(), screenshot.getHeight(), BufferedImage.TYPE_INT_ARGB);
		
		this.screenshot = screenshot;
		graceUntil = System.currentTimeMillis() + 125;
		gd.setFullScreenWindow(this);
	}

	@Override
	public void update(final Graphics g) {
		paint(g);
	}

	int line;
	int[] sine = new int[8192]; 
	{
		double freq = (Math.PI + Math.PI) / 4096;
		freq *= 72;
		double amp = 5.3D;
		for (int i=0; i<sine.length; ++i) {
			sine[i] = (int) Math.round(Math.sin(i * freq) * amp);
			
		}
	}
	
	private final Composite composite = AlphaComposite.getInstance(AlphaComposite.SRC_OVER, 0.175F);

	@Override
	public void paint(Graphics g) {
		final Graphics2D g2d = (Graphics2D) g;
//		Color c = new Color((int) System.currentTimeMillis(), true);
		line = (line + 1) & 1023;
		g2d.setPaintMode();
		g2d.setColor(Color.BLACK);
//		Color c = new Color(0x7F000000, true);
//		g.setColor(c);
//		g.drawLine(0, line, 32767, line);
		if (true) {
			int dx = ((int) (Math.random() * 3)) - 1;
			int dy = ((int) (Math.random() * 3)) - 1;
			//float alpha = (float) (Math.random() * 0.15);
			dx = 0; dy = 0;
			float alpha = 0.08F;
			g2d.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER, alpha));
			g2d.drawImage(screenshot, dx, dy, null);

			// blur screenshot
			Kernel k = new Kernel(3, 3, new float[] {
					1F/20, 1F/10, 1F/20, 
                    1F/10, 4F/10, 1F/10, 
                    1F/20, 1F/10, 1F/20 });
			ConvolveOp op = new ConvolveOp(k);
			screenshot = op.filter(screenshot, null);
			
			if (false) {
				g2d.setColor(Color.WHITE);
				//g2d.setColor(new Color((int) (Math.random() * 0x1000000)));
				for (int i=8192; i>=0; --i) {
					int x0 = (int) (Math.random() * screenshot.getWidth());
					int y0 = (int) (Math.random() * screenshot.getHeight());
					g2d.fillRect(x0, y0, 3, 3);
				}
				//g2d.setColor(Color.BLACK);
				for (int i=8192; i>=0; --i) {
					int x0 = (int) (Math.random() * screenshot.getWidth());
					int y0 = (int) (Math.random() * screenshot.getHeight());
					g2d.fillRect(x0, y0, 3, 3);
				}
			}
		} else {
			g2d.setComposite(composite);
			int block = 1;
			double d = (Math.random() * 2D - 1D);
			for (int line = 0; line < 1024; line += block) {
				d += (Math.random() - 0.5D) * 0.233D;
				int shft = (int) d; //sine[line + this.line];
				int h = block;
				g2d.drawImage(screenshot, shft, line, shft+screenshot.getWidth(), line+h,
						0, line, screenshot.getWidth(), line+h, null);
				//g2d.fillRect(0, line, shft-1, line+h);
				//g2d.fillRect(screenshot.getWidth()+shft, line, 32767, line+h);
			}
		}
	}

	public void inputDetected() {
		if (System.currentTimeMillis() < graceUntil)
			return;
		System.out.println("inputDetected!");
		System.exit(0);
	}

	public static void main(String[] argv) {
		FullScreenFX fx = new FullScreenFX();
		try {
			Thread.sleep(1525);
		} catch (InterruptedException e) {
		}
		fx.enterFullscreenMode();
		while (!fx.inputDetected) {
			try {
				Thread.sleep(20);
			} catch (InterruptedException e) {
			}
			fx.repaint();
		}
		System.exit(0);
	}


}

package ch.eisenring.app.shared.timing;

import java.util.concurrent.atomic.AtomicLong;

/**
 * A Timer that becomes due periodically.
 * 
 * If this timer is set due manually, the next periodic due time is
 * moved ahead to new + interval.
 */
public final class PeriodicTimer implements Timer {

	private final long intervalMillis;
	private final AtomicLong nextDue = new AtomicLong(Long.MIN_VALUE);
	
	/**
	 * Constructs a periodic timer that is due every
	 * n milliseconds.
	 */
	public PeriodicTimer(final long intervalMillis) {
		this.intervalMillis = intervalMillis;
		this.nextDue.set(System.currentTimeMillis() + intervalMillis);
	}
	
	/**
	 * Constructs a periodic timer that is due every
	 * x seconds.
	 */
	public PeriodicTimer(final int seconds) {
		this(seconds * 1000L);
	}

	// --------------------------------------------------------------
	// ---
	// --- Timer interface implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public boolean isDue() {
		final long nextDue = this.nextDue.get();
		final boolean due = System.currentTimeMillis() >= nextDue;
		if (due) {
			this.nextDue.compareAndSet(nextDue, System.currentTimeMillis() + intervalMillis);
		}
		return due;
	}

	@Override
	public void markAsDue() {
		nextDue.set(Long.MIN_VALUE);
	}
	
}

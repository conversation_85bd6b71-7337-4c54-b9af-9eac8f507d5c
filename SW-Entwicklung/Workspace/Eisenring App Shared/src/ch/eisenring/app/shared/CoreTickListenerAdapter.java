package ch.eisenring.app.shared;

import java.util.Calendar;
import java.util.concurrent.atomic.AtomicBoolean;

import ch.eisenring.core.datatypes.date.HEAGCalendar;
import ch.eisenring.core.datatypes.date.TimeOfDay;
import ch.eisenring.core.datatypes.primitives.Primitives;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.core.logging.Logger.LogLevel;

/**
 * Base class for implementing a core tick listener
 */
public abstract class CoreTickListenerAdapter implements CoreTickListener {

	/**
	 * Constant for synchronous tick mode
	 * (tick runs on core thread)
	 */
	public final static boolean SYNCHRONOUS = false;

	/**
	 * Constant for asynchronous tick mode
	 * (tick runs in its own thread)
	 */
	public final static boolean ASYNCHRONOUS = true;
	
	private final CoreProvider coreProvider;
	private final String name;
	private final boolean mode;
	private final AtomicBoolean active = new AtomicBoolean();

	protected CoreTickListenerAdapter(final CoreProvider coreProvider) {
		this(coreProvider, null, SYNCHRONOUS);
	}

	protected CoreTickListenerAdapter(final CoreProvider coreProvider, final String name) {
		this(coreProvider, name, SYNCHRONOUS);
	}

	protected CoreTickListenerAdapter(final CoreProvider coreProvider, final String name, final boolean mode) {
		this.coreProvider = coreProvider;
		this.name = name == null ? Primitives.getSimpleName(getClass()) : name;
		this.mode = mode;
	}

	@Override
	public final String getFeatureName() {
		return name;
	}

	public final SoftwareCore<?> getCore() {
		return coreProvider.getCore();
	}

	@Override
	public final String getName() {
		return name;
	}

	@Override
	public final void tick(final long now) {
		// ensure the core is in state RUNNING and not currently
		// starting up or shutting down.
		final SoftwareCore<?> core = getCore();
		final CoreStatus status = core.getStatus();
		if (status != CoreStatus.RUNNING)
			return;
		// ensure only one thread runs tick
		if (!active.compareAndSet(false, true))
			return;
		// provides a simple hook for action timing.
		if (!isDue(now)) {
			active.set(false);
			return;
		}
		if (mode == SYNCHRONOUS) {
			// execute the tick synchronously
			final LogLevel defaultLevel = Logger.setLogLevel(getLogLevel(), Logger.SCOPE_THREAD);
			try {
				tickImpl(now);
			} finally {
				active.set(false);
				Logger.setLogLevel(defaultLevel, Logger.SCOPE_THREAD);
			}
		} else {
			// execute the tick asynchronously
			core.getThreadPool().start(() -> { tickAsync(now); }, name);
		}
	}

	void tickAsync(final long now) {
		try {
			Logger.setLogLevel(getLogLevel(), Logger.SCOPE_THREAD);
			tickImpl(now);
		} finally {
			active.set(false);
		}
	}

	// --------------------------------------------------------------
	// ---
	// --- Control
	// ---
	// --------------------------------------------------------------
	/**
	 * Controls if this listener is due.
	 * The default implementation simply returns true.
	 */
	protected boolean isDue(final long now) {
		return true;
	}

	/**
	 * Controls the LogLevel that is active while the tick implementation executes.
	 * By default this will return NULL, specifying the default log level.
	 */
	protected LogLevel getLogLevel() {
		return null;
	}

	/**
	 * Utility method: select the next closest time if the future from
	 * an ordered list of times.
	 */
	protected long selectNextDueTimestamp(final long now, final TimeOfDay[] times, long next) {
		if (times == null || times.length <= 0)
			return next;
		// check if any extra time is due today
		for (final TimeOfDay time : times) {
			final HEAGCalendar c = HEAGCalendar.obtain(now);
			time.setTo(c);
			long l = c.getTimeInMillis();
			if (l > now && l < next) {
				next = l;
			}
			c.release();
		}
		// extra check for the first extra time on next day
		{
			final HEAGCalendar c = HEAGCalendar.obtain(now);
			c.add(Calendar.DAY_OF_YEAR, 1);
			times[0].setTo(c);
			long l = c.getTimeInMillis();
			if (l > now && l < next) {
				next = l;
			}
		}
		return next;
	}

	// --------------------------------------------------------------
	// ---
	// --- Tick implementation
	// ---
	// --------------------------------------------------------------
	/**
	 * Implements the tick action(s)
	 */
	protected abstract void tickImpl(final long now);

}

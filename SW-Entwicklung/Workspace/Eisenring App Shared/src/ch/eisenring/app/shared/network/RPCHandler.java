package ch.eisenring.app.shared.network;


public interface RPCHandler {

	/**
	 * Provides the RPC timeout to be used for this RPC (in milliseconds)
	 */
	public default int getRPCTimeout() {
		return RPCContext.DEFAULT_TIMEOUT;
	}

	/**
	 * Called as soon as the request packet has been sent.
	 */
	public default void requestSent(final RPCContext rpcContext) {
		// NO-OP by default
	}

	/**
	 * Called by the ReplyActionHandler when reply is received.
	 */
	public void replyReceived(final RPCContext rpcContext);
	
	/**
	 * Called if the action timed out (no reply was received within
	 * the time limit).
	 */
	public void timeoutOccured(final RPCContext rpcContext);
		
	/**
	 * Indicates if this handler must be notified on the EDT
	 * or if it may be invoked from any thread. If this method returns
	 * true, all calls to this handler will be made on <PERSON>'s EDT.
	 */
	public default boolean requiresEDTNotification() {
		return false;
	}

}

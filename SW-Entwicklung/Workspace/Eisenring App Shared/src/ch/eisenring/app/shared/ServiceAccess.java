package ch.eisenring.app.shared;

import ch.eisenring.core.feature.FeatureLookup;

/**
 * Provides service access with lazy lookup.
 */
public interface ServiceAccess<S extends Service> {

	/**
	 * Gets the service.
	 *  
	 * If the service can't be located, RuntimeException is thrown.
	 */
	public abstract S get() throws RuntimeException;

	// --------------------------------------------------------------
	// ---
	// --- Factories
	// ---
	// --------------------------------------------------------------
	public static <U extends Service> ServiceAccess<U> create(final FeatureLookup locator, final Class<U> serviceType) {
		return new ServiceAccessImpl<U, FeatureLookup>(locator, serviceType) {
			@Override
			U lookup() throws ServiceNotFoundException {
				return locator.getFeature(serviceType);
			}
		};
	}

	public static <U extends Service> ServiceAccess<U> create(final SoftwareComponent locator, final Class<U> serviceType) {
		return new ServiceAccessImpl<U, SoftwareComponent>(locator, serviceType) {
			@Override
			U lookup() throws ServiceNotFoundException {
				return locator.getCore().getFeatureLookup().getFeature(serviceType);
			}
		};
	}

	public static <U extends Service> ServiceAccess<U> create(final SoftwareCore<?> locator, final Class<U> serviceType) {
		return new ServiceAccessImpl<U, SoftwareCore<?>>(locator, serviceType) {
			@Override
			U lookup() throws ServiceNotFoundException {
				return locator.getFeatureLookup().getFeature(serviceType);
			}
		};
	}

	public static <U extends Service> ServiceAccess<U> create(final ServiceLocator locator, final Class<U> serviceType) {
		return new ServiceAccessImpl<U, ServiceLocator>(locator, serviceType) {
			@Override
			U lookup() throws ServiceNotFoundException {
				return locator.locateService(serviceType);
			}
		};
	}
}

abstract class ServiceAccessImpl<S extends Service, L> implements ServiceAccess<S> {

	final L locator;
	final Class<S> serviceType;
	volatile S service;

	ServiceAccessImpl(final L locator, final Class<S> serviceType) {
		this.locator = locator;
		this.serviceType = serviceType;
	}

	@Override
	public S get() throws RuntimeException {
		S s = service;
		if (s == null) {
			try {
				s = lookup();
				if (s == null)
					throw new ServiceNotFoundException(serviceType);
			} catch (final ServiceNotFoundException e) {
				throw new RuntimeException(e.getMessage(), e);
			}
			service = s;
		}
		return s;
	}

	abstract S lookup() throws ServiceNotFoundException;

}

package ch.eisenring.app.shared;

import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.core.usersettings.GenericUserSettings;

final class SettingsAutoSaveTickListener extends CoreTickListenerAdapter {

	SettingsAutoSaveTickListener(final CoreProvider coreProvider) {
		super(core<PERSON>rovider, "SettingsAutoSaver");
	}

	@Override
	protected void tickImpl(final long now) {
		final GenericUserSettings settings;
		try {
			final SoftwareCore<?> core = getCore();
			settings = (GenericUserSettings) core.getUserSettings();
			if (settings == null)
				return;
			if (settings.saveSettings(false)) 
				Logger.debug("UserSettings autosaved");
		} catch (final Exception e) {
			Logger.error(Strings.concat("Failed to autosave settings: ", e.getMessage()));
			Logger.error(e);
		}
	}

}

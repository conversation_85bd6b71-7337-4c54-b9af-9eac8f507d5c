package ch.eisenring.app.shared.timing;

public final class AlwaysDueTimer implements Timer {

	@Override
	public boolean isDue() {
		// this timer is always due
		return true;
	}

	@Override
	public void markAsDue() {
		// nothing here
	}

	// --------------------------------------------------------------
	// ---
	// --- Object overrides
	// ---
	// --------------------------------------------------------------
	@Override
	public int hashCode() {
		return 42;
	}

	@Override
	public boolean equals(final Object o) {
		return o instanceof AlwaysDueTimer;
	}

}

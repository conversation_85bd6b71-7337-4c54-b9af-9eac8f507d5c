package ch.eisenring.app.shared;

import ch.eisenring.core.datatypes.date.TimeClassifier;
import ch.eisenring.core.datatypes.primitives.Primitives;
import ch.eisenring.core.feature.Feature;
import ch.eisenring.core.platform.Platform;
import ch.eisenring.core.threading.ThreadPool;
import ch.eisenring.network.MasterPacketDispatcher;
import ch.eisenring.network.PacketSink;
import ch.eisenring.network.packet.AbstractPacket;

/**
 * Software component interface.
 *
 * Implementing class may decide to implement Configurable too
 * to get free configuration services.
 */
public interface SoftwareComponent extends Feature, CoreProvider,
	ServiceLocator, ComponentLocator {

	/**
	 * Sets the core of this component.
	 * This method is called from the core itself as part of the core startup procedure.
	 */
	public void setCore(final SoftwareCore<?> core);

	/**
	 * Gets the core of this component
	 */
	public SoftwareCore<?> getCore();

	/**
	 * Returns true if this component is currently running
	 */
	public boolean isRunning();

	/**
	 * Gets the name of the computer
	 */
	public default String getHostname(){
		final SoftwareCore<?> core = getCore();
		if (core == null) {
			final Platform platform = Platform.getPlatform();
			return platform.getLocalHostName();
		} else {
			return core.getHostname();
		}		
	}

	/**
	 * Dispatches a network packet.
	 * By default dispatching is delegated to the software core
	 * that launched the component. The component may supply its
	 * own dispatcher if it has special requirements. 
	 */
	public void dispatch(final AbstractPacket packet, final PacketSink sink);

	//@Deprecated
	@Override
	public default <C extends SoftwareComponent> C getComponent(final Class<C> componentClass)
			throws ComponentNotFoundException {
		final SoftwareCore<?> core = getCore();
		if (core == null)
			throw new ComponentNotFoundException(this + " not initialized");
		return core.getComponent(componentClass);
	}

	/**
	 * Gets the features this component provides, may return null.
	 * Beware that this may be called before the component has been configure()'d.
	 */
	public default Feature[] getFeatures() {
		return null;
	}

	/**
	 * Handles the argument packet locally, this consists of locating
	 * the packet handler for the packet, calling the optional
	 * handle(packet) method and returning its result packet.
	 * 
	 * The packet is not actually sent over the network! 
	 * Optional operation, may throw if the handler does not implement
	 * the handle(packet) method.
	 */
	public default AbstractPacket handle(final AbstractPacket packet) throws UnsupportedOperationException {
		final MasterPacketDispatcher dispatcher = getCore().getMasterPacketDispatcher();
		return dispatcher.dispatch(packet);
	}

	public default TimeClassifier getCurrentTimeClassifier() {
		return getCore().getCurrentTimeClassifier();
	}

	public ThreadPool getThreadPool();

	// --------------------------------------------------------------
	// ---
	// --- Feature implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public default String getFeatureName() {
		return Primitives.getSimpleName(getClass());
	}

}

package ch.eisenring.app.shared;

import ch.eisenring.core.application.Configuration;
import ch.eisenring.core.application.ConfigurationException;
import ch.eisenring.core.collections.Collection;

/**
 * Interface configurable components implement.
 * The core recognizes if a component implements Configurable
 * and will call the methods accordingly.
 */
public interface Configurable {

	/**
	 * Called with a map of all available configuration
	 * properties (from configuration files and command
	 * line arguments).
	 * 
	 * The configurable picks up values from whatever
	 * properties it likes/supports.
	 */
	public void configure(final Configuration configuration) throws ConfigurationException;

	/**
	 * Queries this configurable for configuration file names
	 */
	public default void getConfigurationFileNames(final Collection<String> fileNames) {
		// nothing by default
	}

}

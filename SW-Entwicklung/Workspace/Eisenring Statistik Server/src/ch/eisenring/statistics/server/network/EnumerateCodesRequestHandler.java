package ch.eisenring.statistics.server.network;

import ch.eisenring.network.PacketSink;
import ch.eisenring.network.implementation.PacketDispatchMode;
import ch.eisenring.network.packet.AbstractPacket;
import ch.eisenring.statistics.server.StatisticServer;
import ch.eisenring.statistics.shared.network.PacketEnumerateCodesRequest;
import ch.eisenring.statistics.shared.network.PacketEnumerateCodesResponse;

final class EnumerateCodesRequestHandler extends AbstractPacketHandler {

	EnumerateCodesRequestHandler(final StatisticServer server) {
		super(server, PacketEnumerateCodesRequest.class, PacketDispatchMode.ASYNCHRONOUS);
	}
	
	@Override
	public void handle(final AbstractPacket abstractPacket, final PacketSink sink) {
		final PacketEnumerateCodesRequest request = (PacketEnumerateCodesRequest) abstractPacket;
		final AbstractPacket reply = PacketEnumerateCodesResponse.create(request);
		sink.sendPacket(reply, false);
	}

}

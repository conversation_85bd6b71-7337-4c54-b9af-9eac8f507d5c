package ch.eisenring.statistics.server.network;

import java.util.Collection;

import ch.eisenring.core.codetable.MessageClassCode;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.core.logging.Logger.LogLevel;
import ch.eisenring.network.PacketSink;
import ch.eisenring.network.implementation.PacketDispatchMode;
import ch.eisenring.network.packet.AbstractPacket;
import ch.eisenring.network.packet.PacketErrorMessage;
import ch.eisenring.statistics.server.StatisticServer;
import ch.eisenring.statistics.server.StatisticService;
import ch.eisenring.statistics.shared.network.PacketStatisticRequest;
import ch.eisenring.statistics.shared.network.PacketStatisticResponse;

final class StatisticRequestHandler extends AbstractPacketHandler {

	StatisticRequestHandler(final StatisticServer server) {
		super(server, PacketStatisticRequest.class, PacketDispatchMode.ASYNCHRONOUS);
	}
	
	@Override
	public void handle(final AbstractPacket abstractPacket, final PacketSink sink) {
		final PacketStatisticRequest packet = (PacketStatisticRequest) abstractPacket;
		final StatisticService service = server.getService(packet);
		if (service == null) {
			// return error message: no service
			Logger.log(LogLevel.ERROR, "No StatisticService for \""+packet.getType()+"\" found");
			final PacketErrorMessage reply = PacketErrorMessage.create(
					"Die angeforderte Auswertung ist nicht bekannt und kann nicht erstellt werden", MessageClassCode.ERROR);
			sink.sendPacket(reply, false);
			return;
		}

		// run the service
		Collection<PacketStatisticResponse> result = null;
		try {
			result = service.createResponses();
		} catch (final Throwable e) {
			// return error message: service failed
			Logger.log(LogLevel.ERROR, "StatisticService \""+service+"\" failed: "+e.getMessage());
			Logger.log(LogLevel.ERROR, e);
			final PacketErrorMessage reply = PacketErrorMessage.create(
					"Die Auswertung ist fehlgeschlagen: "+e.getMessage(), MessageClassCode.ERROR);
			sink.sendPacket(reply, false);
			return;
		}
		
		// process service result
		if (result == null || result.isEmpty()) {
			final PacketErrorMessage reply = PacketErrorMessage.create(
					"Die angeforderte Auswertung lieferte keine Daten", MessageClassCode.ERROR);
			sink.sendPacket(reply, false);
		} else {
			// send the results
			for (final PacketStatisticResponse response : result) {
				sink.sendPacket(response, false);
			}
		}
	}

}

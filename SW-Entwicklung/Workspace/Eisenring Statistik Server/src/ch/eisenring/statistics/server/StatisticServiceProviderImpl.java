package ch.eisenring.statistics.server;

import ch.eisenring.core.collections.Lookup;
import ch.eisenring.core.collections.impl.AtomicArrayLookup;
import ch.eisenring.statistics.shared.codetables.StatisticTypeCode;

public abstract class StatisticServiceProviderImpl implements StatisticServiceProvider {

	private final Lookup<StatisticTypeCode, Class<? extends StatisticService>> serviceLookup = new AtomicArrayLookup<>();

	protected void register(final StatisticTypeCode typeCode, final Class<? extends StatisticService> serviceClass) {
		serviceLookup.put(typeCode, serviceClass);
	}

	protected Class<? extends StatisticService> getServiceClass(final StatisticTypeCode typeCode) {
		return serviceLookup.get(typeCode);
	}

}

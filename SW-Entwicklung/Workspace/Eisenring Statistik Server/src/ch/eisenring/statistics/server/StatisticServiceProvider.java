package ch.eisenring.statistics.server;

import ch.eisenring.core.feature.Feature;
import ch.eisenring.statistics.shared.network.PacketStatisticRequest;

/**
 * Interface for statistic service providers.
 */
public interface StatisticServiceProvider extends Feature {

	/**
	 * Creates an instance of the statistic service to
	 * handle the given code. Returns null if the
	 * provider can't handle the request.
	 */
	public StatisticService getService(final StatisticServer statisticServer,
			                           final PacketStatisticRequest request);

	@Override
	public default Class<?> getFeatureType() {
		return StatisticServiceProvider.class;
	}

	@Override
	public default String getFeatureName() {
		return "StatisticServiceProvider";
	}

}

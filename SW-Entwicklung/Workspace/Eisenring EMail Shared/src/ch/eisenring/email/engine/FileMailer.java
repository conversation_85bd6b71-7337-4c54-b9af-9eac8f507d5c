package ch.eisenring.email.engine;

import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.codetable.MessageClassCode;
import ch.eisenring.email.EMail;
import ch.eisenring.email.EMailer;
import ch.eisenring.email.MailerConfiguration;


final class FileMailer implements EMailer {

	private MailerConfiguration config;
	private MailLogger mailLogger;

	public FileMailer(final MailerConfiguration config) {
		this.config = config;
		this.mailLogger = new MailLogger(config);
	}

	@Override
	public ErrorMessage sendEMail(final EMail eMail) {
		mailLogger.log(eMail);
		return new ErrorMessage(MessageClassCode.OK, "FileMailer: EMail saved at: " + config.LOG_DIR.get().getAbsolutePath());
	}

	@Override
	public void dispose() {
		// nothing to do here
	}
}

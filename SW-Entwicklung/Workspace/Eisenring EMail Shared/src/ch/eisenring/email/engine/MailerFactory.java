package ch.eisenring.email.engine;

import ch.eisenring.core.datatypes.primitives.Primitives;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.email.EMailer;
import ch.eisenring.email.MailerConfiguration;
import ch.eisenring.email.engine.smtp.SMTPMailer;

/**
 * Factory for mailer engine implementations
 */
public abstract class MailerFactory {

	protected final static Object LOCK = new Object();

	protected static EMailer defaultEngine = new NullMailer(); 

	protected MailerFactory() {
	}

	/**
	 * Creates a mail engine instance using the given parameters 
	 */
	public static EMailer createEngine(final MailerConfiguration config) throws Exception {
		final String engine = config.ENGINE.get();
		if (Strings.equalsIgnoreCase(engine, "SMTP")) {
			return new SMTPMailer(config, false);
		} else if (Strings.equalsIgnoreCase(engine, "ESMTP")) {
			return new SMTPMailer(config, true);
		} else if (Strings.equalsIgnoreCase(engine, "FILE")) {
			return new FileMailer(config);
		} else if (Strings.isEmpty(engine)
				|| Strings.equalsIgnoreCase(engine, "NULL")
				|| Strings.equalsIgnoreCase(engine, "NONE")) {
			return new NullMailer();
		} else {
			throw new IllegalArgumentException("unknown/unsupported mailer engine: " + engine);
		}
	}
	
	/**
	 * Gets the default eMail sender
	 */
	public static EMailer getDefaultEngine() {
		synchronized (LOCK) {
			return defaultEngine;
		}
	}

	/**
	 * Sets the default mailer
	 */
	public static void setDefaultEngine(final EMailer newMailer) {
		if (newMailer == null)
			throw new NullPointerException("can't set default mailer to NULL");
		synchronized (LOCK) {
			defaultEngine = newMailer;
		}
	}

}

package ch.eisenring.email;

import java.io.IOException;

import ch.eisenring.core.io.StreamReader;
import ch.eisenring.core.io.StreamWriter;
import ch.eisenring.core.io.Streamable;

public final class EMailSignatureSimple implements EMailSignature, Streamable {

	private String html;
	private String plainText;

	public EMailSignatureSimple(final String html, final String plainText) {
		this.html = html;
		this.plainText = plainText;
	}

	@Override
	public String getHTML() {
		return html;
	}

	@Override
	public String getPlainText() {
		return plainText;
	}

	// --------------------------------------------------------------
	// ---
	// --- Streamable implementation
	// ---
	// --------------------------------------------------------------
	EMailSignatureSimple() {
	}

	@Override
	public void read(final StreamReader reader) throws IOException {
		html = reader.readString();
		plainText = reader.readString();
	}

	@Override
	public void write(final StreamWriter writer) throws IOException {
		writer.writeString(html);
		writer.writeString(plainText);
	}

}

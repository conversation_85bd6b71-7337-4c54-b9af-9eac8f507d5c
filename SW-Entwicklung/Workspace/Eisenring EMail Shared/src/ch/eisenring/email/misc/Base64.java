package ch.eisenring.email.misc;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;

import ch.eisenring.core.datatypes.Linefeed;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.io.Streams;
import ch.eisenring.core.io.binary.BinaryHolder;
import ch.eisenring.core.io.binary.BinaryHolderUtil;
import ch.eisenring.core.io.memory.MemoryOutputStream;
import static ch.eisenring.core.io.base64.Base64Constants.*;

/**
 * Utility class for Base64 encoding
 */
public class Base64 {

    /**
     * Encodes binary in base64 form into a new binary holder.
     */
    public static BinaryHolder encode(final BinaryHolder binary,
    		                          final Linefeed linefeed,
    		                          final int maxLineLength) throws IOException {
    	final MemoryOutputStream memOut = new MemoryOutputStream();
    	try {
    		encode(binary, linefeed, maxLineLength, memOut);
    	} finally {
    		Streams.closeSilent(memOut);
    	}
		return BinaryHolderUtil.create(memOut, true);
    }
    
    /**
     * Encodes binary in base64 form into the given Output stream.
     */
	@SuppressWarnings("deprecation")
    public static void encode(final BinaryHolder binary,
            				  final Linefeed linefeed,
            				  final int maxLineLength,
            				  final OutputStream output) throws IOException {
    	final byte[] lf = Strings.getBytes(linefeed, StandardCharsets.US_ASCII);
		final byte[] bytes = new byte[3];
    	final int charsPerLine = maxLineLength - linefeed.length();
    	InputStream binIn = null;
    	int lineLength = 0;
    	try {
    		binIn = binary.getInputStream();
    		int count;
    		do {
    			count = Streams.readFully(binIn, bytes, 0, 3);
    			if (count <= 0)
    				break;
    			// concatenate 24 bits from byte buffer
    			final int bits = (((bytes[0]       ) << 16) |
    							  ((bytes[1] & 0xFF) <<  8) |
    					          ((bytes[2] & 0xFF)      ));
    			// encode 1st character of group
    			output.write(BASE_64_CHARS[(bits >> 18) & 0x3F]);
    			if (++lineLength >= charsPerLine) {
    				output.write(lf);
    				lineLength = 0;
    			}
    			// encode 2nd character of group
    			output.write(BASE_64_CHARS[(bits >> 12) & 0x3F]);
    			if (++lineLength >= charsPerLine) {
    				output.write(lf);
    				lineLength = 0;
    			}
    			// encode 3rd character of group
    			output.write(count >= 2 ? BASE_64_CHARS[(bits >> 6) & 0x3F] : PADDING_CHAR);
    			if (++lineLength >= charsPerLine) {
    				output.write(lf);
    				lineLength = 0;
    			}
    			// encode 4th character of group
    			output.write(count >= 3 ? BASE_64_CHARS[bits & 0x3F] : PADDING_CHAR);
    			if (++lineLength >= charsPerLine) {
    				output.write(lf);
    				lineLength = 0;
    			}
    		} while (count == 3);
    		if (lineLength > 0)
				output.write(lf);
    	} finally {
    		Streams.closeSilent(binIn);
    	}
    }

 
}

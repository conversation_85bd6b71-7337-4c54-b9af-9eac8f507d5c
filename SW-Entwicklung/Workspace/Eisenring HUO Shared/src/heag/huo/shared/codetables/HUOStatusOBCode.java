package heag.huo.shared.codetables;

import java.awt.Color;

import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.codetype.StaticCode;
import ch.eisenring.core.sort.Comparator;

public abstract class HUOStatusOBCode extends StaticCode {

	private final int precedence;
	private final Color color;

	protected HUOStatusOBCode(final int id, final String text, 
			final int precendence, final Color color) {
		super(id, id == 0 ? null : Integer.valueOf(id), text, text);
		this.precedence = precendence;
		this.color = color;
	}

	// --------------------------------------------------------------
	// ---
	// --- Extra API
	// ---
	// --------------------------------------------------------------
	public Color getColor() {
		return color;
	}

	public int getPrecendence() {
		return precedence;
	}

	// --------------------------------------------------------------
	// ---
	// --- Sorting
	// ---
	// --------------------------------------------------------------
	public static interface Order extends AbstractCode.Order {
		public final static Comparator<HUOStatusOBCode> Precendence = Comparator.wrapNullSafe(
				new Comparator<HUOStatusOBCode>() {
					@Override
					public int compare(final HUOStatusOBCode c1, final HUOStatusOBCode c2) {
						return compareSigned(c1.getPrecendence(), c2.getPrecendence());
					}
				});
	}

}

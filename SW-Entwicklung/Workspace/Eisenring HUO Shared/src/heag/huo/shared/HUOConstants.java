package heag.huo.shared;

import ch.eisenring.core.application.SoftwareDescriptor;
import ch.eisenring.core.codetable.SoftwareType;
import ch.eisenring.core.datatypes.date.TimestampUtil;

public interface HUOConstants {

	// DONT MODFIY THE VERSION LINE, EXCEPT THE STRING CONTENTS!
	// BUILD SCRIPTS NEED THE LINE TO BE EXACTLY AS IT IS!!!
	// THE VERSION STRING MUST HAVE THREE DOT-SEPARATED PARTS AND
	// MUST CONTAIN ONLY DIGITS (it is used as .exe and installer
	// version by the build)
	String VERSION = "0.5.0";
	long VERSION_TIMESTAMP = TimestampUtil.toTimestamp("2015-12-08");

	String APPNAME = "HEAG Haus- und Objektübersicht";

	SoftwareDescriptor DESCRIPTOR = new SoftwareDescriptor(
			APPNAME, VERSION, VERSION_TIMESTAMP, SoftwareType.APPLICATION);

	//String URL_PROTOCOL = "HEAG???://";

	//DatabaseSpecifier HUO_DATABASE = DatabaseSpecifier.get("HUO");

}

package heag.huo.shared.pojo;

import ch.eisenring.core.collections.Set;
import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.io.StreamReader;
import ch.eisenring.core.io.StreamWriter;
import ch.eisenring.logiware.LWAuftragKey;
import ch.eisenring.logiware.LWPositionKey;
import ch.eisenring.logiware.LWProjektKey;
import ch.eisenring.logiware.code.hard.LWFormularArt;
import ch.eisenring.logiware.code.hard.LWLagerArtCode;
import ch.eisenring.logiware.code.hard.LWPlanungStatusCode;
import ch.eisenring.lw.api.LWAuftragData;
import ch.eisenring.lw.api.LWAuftragPositionData;
import ch.eisenring.lw.pojo.LWAuftragPositionDataPojo;
import heag.huo.shared.codetables.AUSJNStatusCode;

import java.io.IOException;

/**
 * Represents AuftragPosition based on Logiware position
 */
public final class AUSPositionDataRepair extends AUSPositionData {

    private LWAuftragPositionData position;
    private LWProjektKey projektKey;

    // --------------------------------------------------------------
    // ---
    // --- Factory methods
    // ---
    // --------------------------------------------------------------
    public static AUSPositionDataRepair create(final LWProjektKey projektKey, final LWAuftragPositionData position) {
        final AUSPositionDataRepair result = new AUSPositionDataRepair();
        result.projektKey = projektKey;
        result.position = LWAuftragPositionDataPojo.create(position);
        return result;
    }

    // --------------------------------------------------------------
    // ---
    // --- API
    // ---
    // --------------------------------------------------------------
    @Override
    public boolean isStorniert() {
        return position.isStorniert();
    }

    @Override
    public String getPositionsNummer() {
        return position.getPositionKey().getPositionsNummerText();
    }

    @Override
    public String getPositionsNummerToolTip() { return "Positionsnummer im Geräte-Reparaturauftrag Logiware"; }

    @Override
    public String getProduktNummer() {
        return position.getProduktNummer();
    }

    @Override
    public String getProduktNummerToolTip() { return "Artikelnr. Logiware"; }

    @Override
    public String getBeschreibung() { return getBeschreibung(position); }

    @Override
    public String getBeschreibungToolTip() {
        return "Beschreibung der Position\nZusammenzug aus Text Vor, Produktbezeichnung und Text nach\n(Logiware)";
    }

    @Override
    public String getBestellMenge() {
        return position.getBestellMengeText();
    }

    @Override
    public String getBestellMengeToolTip() {
        return "Bestellmenge dieser Position";
    }

    @Override
    public String getBestellungAuftragNummer() {
        final LWPositionKey keyAB = position.getPositionKeyAB();
        return keyAB == null ? "" : Strings.toString(keyAB.getAuftragnummer());
    }

    @Override
    public long getAufnahmeDatum() {
        return position.getAddDate();
    }

    @Override
    public String getAufnahmeDatumToolTip() { return "Erfassungsdatum der Position"; }

    @Override
    public AUSJNStatusCode getBestellStatus() { return AUSJNStatusCode.NA; }

    @Override
    public String getBestellStatusToolTip() {
        return "Bestellstatus der Position";
    }

    @Override
    public AUSJNStatusCode getEinlagerStatus() { return AUSJNStatusCode.NA; }

    @Override
    public String getEinlagerStatusToolTip() { return "Lagerartikel/Artikel ohne Bestellung"; }

    @Override
    public String getLieferDatum() { return ""; }

    @Override
    public long getWunschDatum() {
        return 0;
    }

    @Override
    public String getWunschDatumToolTip() { return "Wunschdatum der Auftragsposition in Logiware"; }


    @Override
    public long getKOK() { return position.getWunschDatum(); }

    @Override
    public String getKOKToolTip() { return "KOK der Auftragsposition in Logiware"; }



    @Override
    public String getPlanungStatus(final AUSMainData mainData) { return "-"; }

    @Override
    public AUSFormularStatus getStatusLS() {
        return AUSFormularStatus.create(LWFormularArt.F204, TimestampUtil.NULL_TIMESTAMP, AUSFormularStatus.STATUS_KEIN_FORMULAR);
    }

    @Override
    public String getStatusLSToolTip() {
        return "N = Nicht ausgegeben\nA = Ausgegeben\nG = Gutgemeldet";
    }

    public LWAuftragPositionData getAuftragPosition() {
        return position;
    }

    @Override
    public LWAuftragKey getAuftragKey() { return position.getAuftragKey(); }

    @Override
    public String getAuftragKeyToolTip() { return "Auftragsnummer die diese Position enthält"; }

    @Override
    public LWProjektKey getProjektKey() { return projektKey; }

    @Override
    public String getProjektKeyToolTip() { return "Projektnummer dieses Auftrags/Position"; }

    // --------------------------------------------------------------
    // ---
    // --- Streamable
    // ---
    // --------------------------------------------------------------
    @Override
    public void read(final StreamReader reader) throws IOException {
        super.read(reader);
        projektKey = (LWProjektKey) reader.readObject();
        position = (LWAuftragPositionData) reader.readObject();
    }

    @Override
    public void write(final StreamWriter writer) throws IOException {
        super.write(writer);
        writer.writeObject(projektKey);
        writer.writeObject(position);
    }

}

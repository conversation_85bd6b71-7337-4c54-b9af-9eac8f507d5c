package heag.huo.shared.pojo;

import java.io.IOException;

import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.core.datatypes.date.format.HEAGDateFormat;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.StringMakerFriendly;
import ch.eisenring.core.io.StreamReader;
import ch.eisenring.core.io.StreamWriter;
import ch.eisenring.core.io.Streamable;
import ch.eisenring.logiware.code.hard.LWFormularArt;
import ch.eisenring.logiware.code.hard.LWPlanungStatusCode;

public final class AUSFormularStatus implements Streamable, StringMakerFriendly {

	public final static int STATUS_NICHT_ERSTELLT = 0;
	public final static int STATUS_AUSGEGEBEN = 1;
	public final static int STATUS_GUTGEMELDET = 2;
	public final static int STATUS_KEIN_FORMULAR = 3;
	
	private LWFormularArt formularArt;
	private long timestamp;
	private int status;

	AUSFormularStatus() {
	}

	// --------------------------------------------------------------
	// ---
	// --- Factory methods
	// ---
	// --------------------------------------------------------------
	public static AUSFormularStatus create(final LWFormularArt formularArt, final long timestamp, final int status) {
		final AUSFormularStatus result = new AUSFormularStatus();
		result.formularArt = formularArt;
		result.timestamp = timestamp;
		result.status = status;
		return result;
	}

	// --------------------------------------------------------------
	// ---
	// --- API
	// ---
	// --------------------------------------------------------------
	public String getStatusString() {
		switch (status) {
			default: return "?";
			case STATUS_NICHT_ERSTELLT: return "N";
			case STATUS_AUSGEGEBEN: return "A";
			case STATUS_GUTGEMELDET: return "G";
			case STATUS_KEIN_FORMULAR: return "-";
		}
	}

	public String getPlanungStatusString(final LWPlanungStatusCode planungStatus) {
		switch (status) {
			default:
				return AbstractCode.getLongText(planungStatus, "???");
			case STATUS_GUTGEMELDET:
				return "Erledigt";
		}
	}

	// --------------------------------------------------------------
	// ---
	// --- Streamable support
	// ---
	// --------------------------------------------------------------
	@Override
	public void read(final StreamReader reader) throws IOException {
		formularArt = reader.readCode(LWFormularArt.class);
		timestamp = reader.readLong();
		status = reader.readInt();
	}

	@Override
	public void write(final StreamWriter writer) throws IOException {
		writer.writeCode(formularArt);
		writer.writeLong(timestamp);
		writer.writeInt(status);
	}

	// --------------------------------------------------------------
	// ---
	// --- Object overrides
	// ---
	// --------------------------------------------------------------
	private final static HEAGDateFormat TIMESTAMP_FORMAT = HEAGDateFormat.get(" (dd.MM.yyyy)");
	
	@Override
	public void toStringMaker(final StringMaker target) {
		char c;
		switch (status) {
			default: c = '?'; break;
			case STATUS_NICHT_ERSTELLT: c = 'N'; break;
			case STATUS_AUSGEGEBEN: c = 'A'; break;
			case STATUS_GUTGEMELDET: c = 'G'; break;
		}
		target.append(c);
		final long t = this.timestamp;
		if (!TimestampUtil.isNull(t))
			target.append(t, TIMESTAMP_FORMAT);
	}

	@Override
	public String toString() {
		return StringMakerFriendly.toString(this);
	}

}

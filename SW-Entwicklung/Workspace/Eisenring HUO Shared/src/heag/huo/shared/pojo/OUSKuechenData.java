package heag.huo.shared.pojo;

import java.io.IOException;
import ch.eisenring.core.collections.Collection;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.core.io.StreamReader;
import ch.eisenring.core.io.StreamUtil;
import ch.eisenring.core.io.StreamWriter;
import ch.eisenring.core.io.Streamable;
import ch.eisenring.lw.api.LWAuftragData;
import ch.eisenring.lw.api.LWProjektData;
import ch.eisenring.lw.api.LWSubKontaktPartnerData;
import ch.eisenring.lw.api.LWSubjektData;
import ch.eisenring.lw.model.navigable.LWAuftrag;
import ch.eisenring.lw.model.navigable.LWProjekt;
import ch.eisenring.lw.pojo.LWAuftragDataPojo;
import ch.eisenring.lw.pojo.LWSubjektDataPojo;

public final class OUSKuechenData implements Streamable {

	private LWAuftragData auftrag135;
	private LWAuftragData auftrag132Garderobe220;
	private LWAuftragData auftrag132Garderobe221;
	private LWAuftragData auftrag132Garderobe222;
	private LWAuftragData auftrag132Garderobe295;
	private LWAuftragData auftrag137Waschturm290;
	private LWAuftragData auftrag137Waschturm320;
	private boolean isVerrechnet;
	private long maxIPChangeDate = TimestampUtil.NULL_TIMESTAMP;
	private long maxMassaufnahmeDate = TimestampUtil.NULL_TIMESTAMP;
	private LWSubjektData kaeufer;
	private Integer subjektId;
	private final List<LWSubKontaktPartnerData> kontaktDaten = new ArrayList<>();
	private LWProjektData projekt;
	private boolean isBauseits = false;
	private String etappe;

	// Initialization

	private OUSKuechenData() {
	}

	public static OUSKuechenData create(LWAuftragData auftrag135) {
		if (auftrag135 == null)
			throw new NullPointerException();
		OUSKuechenData kueche = new OUSKuechenData();
		kueche.setAuftrag135(auftrag135);
		return kueche;
	}

	public static OUSKuechenData createDummy(LWProjektData projekt) {
		OUSKuechenData kueche = new OUSKuechenData();
		kueche.setProjekt(projekt);
		return kueche;
	}


	// Kuechen Auftrag

	public void setAuftrag135(LWAuftragData auftrag) {
		this.auftrag135 = LWAuftragDataPojo.create(auftrag);
	}

	public LWAuftragData getAuftrag135() {
		return auftrag135;
	}

	// Garderoben Aufträge

	public void setAuftrag132Garderobe220(LWAuftragData auftrag) {
		this.auftrag132Garderobe220 = LWAuftragDataPojo.create(auftrag);
	}

	public LWAuftragData getAuftrag132Garderobe220() {
		return auftrag132Garderobe220;
	}

	public void setAuftrag132Garderobe221(LWAuftragData auftrag) {
		this.auftrag132Garderobe221 = LWAuftragDataPojo.create(auftrag);
	}

	public LWAuftragData getAuftrag132Garderobe221() {
		return auftrag132Garderobe221;
	}

	public void setAuftrag132Garderobe222(LWAuftragData auftrag) {
		this.auftrag132Garderobe222 = LWAuftragDataPojo.create(auftrag);
	}

	public LWAuftragData getAuftrag132Garderobe222() {
		return auftrag132Garderobe222;
	}

	public void setAuftrag132Garderobe295(LWAuftragData auftrag) {
		this.auftrag132Garderobe295 = LWAuftragDataPojo.create(auftrag);
	}

	public LWAuftragData getAuftrag132Garderobe295() {
		return auftrag132Garderobe295;
	}

	// Waschturm Aufträge

	public void setAuftrag137Waschturm290(final LWAuftragData auftrag137Waschturm290) {
		this.auftrag137Waschturm290 = LWAuftragDataPojo.create(auftrag137Waschturm290);
	}

	public LWAuftragData getAuftrag137Waschturm290() {
		return auftrag137Waschturm290;
	}

	public void setAuftrag137Waschturm320(LWAuftragData auftrag137Waschturm320) {
		this.auftrag137Waschturm320 = LWAuftragDataPojo.create(auftrag137Waschturm320);
	}

	public LWAuftragData getAuftrag137Waschturm320() {
		return auftrag137Waschturm320;
	}

	// verrechnet

	public void setVerrechnet(final boolean isVerrechnet) {
		this.isVerrechnet = isVerrechnet;
	}

	public boolean isVerrechnet() {
		return isVerrechnet;
	}

	// bauseits

	public void setBauseits(final boolean isBauseits) {
		this.isBauseits = isBauseits;
	}

	public boolean isBauseits() {
		return isBauseits;
	}

	// Anderes

	public void setMaxIPChangeDate(final long timestamp) {
		this.maxIPChangeDate = timestamp;
	}

	public long getMaxIPChangeDate() {
		return maxIPChangeDate;
	}

	public void setMaxMassaufnahmeDate(final long timestamp) {
		this.maxMassaufnahmeDate = timestamp;
	}

	public long getMaxMassaufnahmeDate() {
		return maxMassaufnahmeDate;
	}

	public void setKaeufer(final LWSubjektData kaeufer) {
		this.kaeufer = LWSubjektDataPojo.create(kaeufer);
	}

	public LWSubjektData getKaeufer() {
		return kaeufer;
	}

	public void setSubjektId(final Integer subjektId) {
		this.subjektId = subjektId;
	}

	public Integer getSubjektId() {
		return subjektId;
	}

	public void setKontaktDaten(final java.util.Collection<LWSubKontaktPartnerData> kontaktDaten) {
		this.kontaktDaten.addAll(kontaktDaten);
	}

	public void setProjekt(LWProjektData projekt) {
		this.projekt = projekt;
	}

	public LWProjektData getProjekt() {
		if (auftrag135 != null) {
			return auftrag135.getProjektData();
		}
		return projekt;
	}

	public Collection<LWSubKontaktPartnerData> getKontaktDaten() {
		return new ArrayList<>(kontaktDaten);
	}

	public void setEtappe(String etappe) {
		this.etappe = etappe;
	}

	public String getEtappe() {
		return this.etappe;
	}

	// --------------------------------------------------------------
	// ---
	// --- Streamable
	// ---
	// --------------------------------------------------------------

	@Override
	public int getStreamVersion() {
		return 5;
	}

	@Override
	public void read(final StreamReader reader) throws IOException {
		readVersion(reader);
		setAuftrag135((LWAuftragData) reader.readObject());
		setAuftrag137Waschturm290((LWAuftragData) reader.readObject());
		setAuftrag137Waschturm320((LWAuftragData) reader.readObject());
		setAuftrag132Garderobe220((LWAuftragData) reader.readObject());
		setAuftrag132Garderobe221((LWAuftragData) reader.readObject());
		setAuftrag132Garderobe222((LWAuftragData) reader.readObject());
		setAuftrag132Garderobe295((LWAuftragData) reader.readObject());
		setVerrechnet(reader.readBoolean());
		setMaxIPChangeDate(reader.readLong());
		setMaxMassaufnahmeDate(reader.readLong());
		setKaeufer((LWSubjektData) reader.readObject());
		setSubjektId((Integer) reader.readObject());
		setBauseits(reader.readBoolean());
		setProjekt((LWProjektData) reader.readObject());
		StreamUtil.readCollection(reader, kontaktDaten);
		setEtappe(reader.readString());
	}

	@Override
	public void write(final StreamWriter writer) throws IOException {
		writeVersion(writer);
		writer.writeObject(getAuftrag135());
		writer.writeObject(getAuftrag137Waschturm290());
		writer.writeObject(getAuftrag137Waschturm320());
		writer.writeObject(getAuftrag132Garderobe220());
		writer.writeObject(getAuftrag132Garderobe221());
		writer.writeObject(getAuftrag132Garderobe222());
		writer.writeObject(getAuftrag132Garderobe295());
		writer.writeBoolean(isVerrechnet());
		writer.writeLong(getMaxIPChangeDate());
		writer.writeLong(getMaxMassaufnahmeDate());
		writer.writeObject(getKaeufer());
		writer.writeObject(getSubjektId());
		writer.writeBoolean(isBauseits());
		writer.writeObject(getProjekt());
		StreamUtil.writeCollection(writer, kontaktDaten);
		writer.writeString(getEtappe());
	}

}

package heag.huo.shared.network;

import java.io.IOException;

import ch.eisenring.core.collections.Set;
import ch.eisenring.core.collections.impl.HashSet;
import ch.eisenring.core.datatypes.date.Period;
import ch.eisenring.core.io.StreamReader;
import ch.eisenring.core.io.StreamUtil;
import ch.eisenring.core.io.StreamWriter;
import ch.eisenring.logiware.code.soft.LWKundenberaterCode;

abstract class PacketAuslastungSCBase extends AbstractHUOPacket {

	protected Set<LWKundenberaterCode> kundenBerater = new HashSet<>();
	protected Set<Integer> verkauefer = new HashSet<>();
	protected long begin;
	protected long end;
	
	protected PacketAuslastungSCBase(final int caps, final int priority) {
		super(caps, priority);
	}

	// --------------------------------------------------------------
	// ---
	// --- Factory methods
	// ---
	// --------------------------------------------------------------
	
	// --------------------------------------------------------------
	// ---
	// --- Packet API
	// ---
	// --------------------------------------------------------------
	public final Set<LWKundenberaterCode> getKundenberaterCodes() {
		return new HashSet<>(kundenBerater);
	}

	public final Set<Integer> getVerkaeufer() {
		return new HashSet<>(verkauefer);
	}

	public final Period getPeriod() {
		return Period.create(begin, end);
	}

	public long getBegin() {
		return begin;
	}
	
	public long getEnd() {
		return end;
	}

	// --------------------------------------------------------------
	// ---
	// --- Streamable
	// ---
	// --------------------------------------------------------------
	@Override
	protected void readImpl(final StreamReader reader) throws IOException {
		super.readImpl(reader);
		begin = reader.readLong();
		end = reader.readLong();
		StreamUtil.readCollection(reader, kundenBerater);
		StreamUtil.readCollection(reader, verkauefer);
	}

	@Override
	protected void writeImpl(final StreamWriter writer) throws IOException {
		super.writeImpl(writer);
		writer.writeLong(begin);
		writer.writeLong(end);
		StreamUtil.writeCollection(writer, kundenBerater);
		StreamUtil.writeCollection(writer, verkauefer);
	}

}

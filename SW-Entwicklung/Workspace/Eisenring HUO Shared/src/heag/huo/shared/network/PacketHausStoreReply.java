package heag.huo.shared.network;

import java.io.IOException;

import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.io.StreamReader;
import ch.eisenring.core.io.StreamWriter;

public final class PacketHausStoreReply extends PacketHausStoreBase {

	private PacketHausStoreReply() {
		super(CAPS_SEQUENCE_REPLY | CAPS_ERRORMESSAGE, PRIORITY_REPLY);
	}

	// --------------------------------------------------------------
	// ---
	// --- Factory methods
	// ---
	// --------------------------------------------------------------
	public static PacketHausStoreReply create(final PacketHausStoreRequest request, final ErrorMessage message) {
		final PacketHausStoreReply packet = new PacketHausStoreReply();
		packet.setReplyId(request, message);
		return packet;
	}

	// --------------------------------------------------------------
	// ---
	// --- API
	// ---
	// --------------------------------------------------------------
	
	// none

	// --------------------------------------------------------------
	// ---
	// --- Streamable
	// ---
	// --------------------------------------------------------------
	@Override
	protected void readImpl(final StreamReader reader) throws IOException {
		super.readImpl(reader);
	}

	@Override
	protected void writeImpl(final StreamWriter writer) throws IOException {
		super.writeImpl(writer);
	}

}

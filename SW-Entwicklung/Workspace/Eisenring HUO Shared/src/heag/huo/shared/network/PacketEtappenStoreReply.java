package heag.huo.shared.network;

import ch.eisenring.core.codetable.ErrorMessage;

public final class PacketEtappenStoreReply extends PacketEtappenStoreBase {

	private PacketEtappenStoreReply() {
		super(CAPS_SEQUENCE_REPLY | CAPS_ERRORMESSAGE, PRIORITY_REPLY);
	}

	// --------------------------------------------------------------
	// ---
	// --- Factory methods
	// ---
	// --------------------------------------------------------------
	public static PacketEtappenStoreReply create(final PacketEtappenStoreRequest request, final ErrorMessage error) {
		final PacketEtappenStoreReply packet = new PacketEtappenStoreReply();
		packet.setReplyId(request, error);
		packet.basisKey = request.getBasisKey();
		return packet;
	}
	
	// --------------------------------------------------------------
	// ---
	// --- API
	// ---
	// --------------------------------------------------------------

	// none

	// --------------------------------------------------------------
	// ---
	// --- Streamable
	// ---
	// --------------------------------------------------------------

	// none
	
}

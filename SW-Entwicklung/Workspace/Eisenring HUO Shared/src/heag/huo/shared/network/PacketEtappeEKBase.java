package heag.huo.shared.network;

import java.io.IOException;

import ch.eisenring.core.io.StreamReader;
import ch.eisenring.core.io.StreamWriter;

abstract class PacketEtappeEKBase extends AbstractHUOPacket {
		
	protected String projectNumber;

	protected PacketEtappeEKBase(final int capabilities,int priority) {
		super(capabilities, priority);
	}

	// --------------------------------------------------------------
	// ---
	// --- Packet API
	// ---
	// --------------------------------------------------------------
	public final String getProjectNumber() {
		return projectNumber;
	}

	// --------------------------------------------------------------
	// ---
	// --- Streamable
	// ---
	// --------------------------------------------------------------
	@Override
	protected void readImpl(final StreamReader reader) throws IOException {
		super.readImpl(reader);
		projectNumber = reader.readString();
	}

	@Override
	protected void writeImpl(final StreamWriter writer) throws IOException {
		super.writeImpl(writer);
		writer.writeString(projectNumber);
	}

}

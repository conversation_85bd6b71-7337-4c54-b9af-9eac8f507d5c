package heag.huo.shared.network;

import ch.eisenring.core.codetable.ErrorMessage;

/**
 * Reply to data fattening request from server
 */
public final class PacketOUSFattenReply extends PacketOUSFattenBase {

    private PacketOUSFattenReply() {
        super(CAPS_SEQUENCE_REPLY | CAPS_ERRORMESSAGE, PRIORITY_REPLY);
    }

    // --------------------------------------------------------------
    // ---
    // --- Factory methods
    // ---
    // --------------------------------------------------------------
    public static PacketOUSFattenReply create(final PacketOUSFattenRequest request, final ErrorMessage error) {
        final PacketOUSFattenReply packet = new PacketOUSFattenReply();
        packet.setReplyId(request, error);
        packet.mainData = request.getMainData();
        return packet;
    }
    
}

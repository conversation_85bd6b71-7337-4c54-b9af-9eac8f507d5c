package heag.huo.shared.network;

import java.io.IOException;

import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.io.StreamReader;
import ch.eisenring.core.io.StreamWriter;
import heag.huo.shared.pojo.AUSMainData;

public final class PacketAUSGetReply extends PacketAUSBase {

	private AUSMainData data;
	
	private PacketAUSGetReply() {
		super(CAPS_SEQUENCE_REPLY | CAPS_ERRORMESSAGE, PRIORITY_REPLY);
	}

	// --------------------------------------------------------------
	// ---
	// --- Streamable
	// ---
	// --------------------------------------------------------------
	public static PacketAUSGetReply create(final PacketAUSGetRequest request, final ErrorMessage message) {
		final PacketAUSGetReply packet = new PacketAUSGetReply();
		packet.setReplyId(request, message);
		packet.projektKey = request.projektKey;
		return packet;
	}

	public static PacketAUSGetReply create(final PacketAUSGetRequest request, final AUSMainData data) {
		final PacketAUSGetReply packet = create(request, ErrorMessage.OK);
		packet.data = data;
		return packet;
	}

	// --------------------------------------------------------------
	// ---
	// --- API
	// ---
	// --------------------------------------------------------------
	public AUSMainData getData() {
		return data;
	}

	// --------------------------------------------------------------
	// ---
	// --- Streamable
	// ---
	// --------------------------------------------------------------
	@Override
	protected void readImpl(final StreamReader reader) throws IOException {
		super.readImpl(reader);
		data = (AUSMainData) reader.readObject();
	}

	@Override
	protected void writeImpl(final StreamWriter writer) throws IOException {
		super.writeImpl(writer);
		writer.writeObject(data);
	}

}

package heag.huo.shared.resources.reports;

import ch.eisenring.print.shared.resource.ReportResource;

public interface Reports extends ch.eisenring.print.shared.resource.reports.Reports {

	ReportResource A4P = new ReportResource(
			Reports.class, "Uebesicht-A4-Hoch", "A4P");

	ReportResource A4L = new ReportResource(
			Reports.class, "Uebesicht-A4-Quer", "A4L");

	ReportResource A3P = new ReportResource(
			Reports.class, "Uebesicht-A3-Hoch", "A3P");

	ReportResource A3L = new ReportResource(
			Reports.class, "Uebesicht-A3-Quer", "A3L");

}

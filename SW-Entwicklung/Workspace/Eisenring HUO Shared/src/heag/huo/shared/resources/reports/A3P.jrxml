<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="A3P" pageWidth="842" pageHeight="1190" columnWidth="786" leftMargin="28" rightMargin="28" topMargin="25" bottomMargin="25" uuid="291ee9d2-4456-418f-890c-4a4c1e93342d">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="0.75"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="58"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<parameter name="TITLE" class="java.lang.String" isForPrompting="false"/>
	<parameter name="DRUCKDATUM" class="java.lang.String" isForPrompting="false"/>
	<parameter name="USER" class="java.lang.String" isForPrompting="false"/>
	<field name="documentImage" class="java.awt.Image"/>
	<detail>
		<band height="1140" splitType="Prevent">
			<image scaleImage="RetainShape" hAlign="Left" vAlign="Top">
				<reportElement uuid="25e3dcfd-7019-4232-ad3a-3b9fc2ac2c25" key="DocumentImage" mode="Transparent" x="0" y="0" width="786" height="1140"/>
				<imageExpression><![CDATA[$F{documentImage}]]></imageExpression>
			</image>
		</band>
	</detail>
</jasperReport>

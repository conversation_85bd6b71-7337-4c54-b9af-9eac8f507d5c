package heag.huo.server.network;

import heag.huo.server.HUOServer;
import ch.eisenring.app.server.network.AbstractPacketHandler;
import ch.eisenring.network.PacketSink;
import ch.eisenring.network.implementation.PacketDispatchMode;
import ch.eisenring.network.packet.AbstractPacket;

abstract class AbstractH<PERSON><PERSON><PERSON>etHandler extends AbstractPacketHandler<HUOServer, AbstractPacket> {

	protected AbstractHUOPacketHandler(final HUOServer server, final Class<? extends AbstractPacket> packetClass) {
		this(server, packetClass, PacketDispatchMode.ASYNCHRONOUS);
	}

	@SuppressWarnings("unchecked")
	protected AbstractHUOPacketHandler(final HUOServer server,
			                           final Class<? extends AbstractPacket> packetClass,
			                           final PacketDispatchMode dispatchMode) {
		super(server, (Class<AbstractPacket>) packetClass, dispatchMode);
	}

	@Override
	public void handle(final AbstractPacket packet, final PacketSink sink) {
		final AbstractPacket reply = handle(packet);
		sink.sendPacket(reply);
	}

}

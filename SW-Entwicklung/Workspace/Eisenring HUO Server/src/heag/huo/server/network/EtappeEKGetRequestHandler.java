package heag.huo.server.network;

import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.collections.Collection;
import ch.eisenring.core.datatypes.strings.Msg;
import ch.eisenring.logiware.LWEtappeKey;
import ch.eisenring.logiware.code.service.LogiwareService;
import ch.eisenring.lw.api.LWEtappeData;
import ch.eisenring.lw.condition.Factory;
import ch.eisenring.lw.meta.LWProjektMeta;
import ch.eisenring.lw.model.navigable.LWObject;
import ch.eisenring.lw.model.navigable.LWObjectCache;
import ch.eisenring.lw.model.navigable.LWProjekt;
import ch.eisenring.lw.model.navigable.LWXEtappe;
import ch.eisenring.lw.model.navigable.LWXPAuftrag;
import ch.eisenring.lw.pojo.LWXPAuftragPojo;
import ch.eisenring.network.PacketSink;
import ch.eisenring.network.packet.AbstractPacket;
import heag.huo.server.HUOServer;
import heag.huo.shared.network.PacketEtappeEKGetReply;
import heag.huo.shared.network.PacketEtappeEKGetRequest;

final class EtappeEKGetRequestHandler extends AbstractHUOPacketHandler {
	
	EtappeEKGetRequestHandler(final HUOServer server) {
		super(server, PacketEtappeEKGetRequest.class);
	}

	@Override
	public PacketEtappeEKGetReply handle(final AbstractPacket packet) {
		final PacketEtappeEKGetRequest request = (PacketEtappeEKGetRequest) packet;
		final PacketEtappeEKGetReply reply = handle(server, request);
		return reply;
	}

	@Override
	public void handle(final AbstractPacket packet, final PacketSink sink) {
		final PacketEtappeEKGetRequest request = (PacketEtappeEKGetRequest) packet;
		final PacketEtappeEKGetReply reply = handle(server, request);
		sink.sendPacket(reply);
	}

	private static PacketEtappeEKGetReply handle(final HUOServer server, final PacketEtappeEKGetRequest request) {
		try {
			final PacketEtappeEKGetReply reply = handleImpl(server, request);
			if (reply != null)
				return reply;
			return PacketEtappeEKGetReply.create(request, ErrorMessage.ERROR);
		} catch (final Exception e) {
			return PacketEtappeEKGetReply.create(request, new ErrorMessage(e));
		}
	}
	
	private static PacketEtappeEKGetReply handleImpl(final HUOServer server, final PacketEtappeEKGetRequest request) throws Exception {
		final LogiwareService service = server.locateService(LogiwareService.class);
		final LWObjectCache cache = service.createObjectCache();

		final Collection<LWProjekt> projekte = cache.load(LWProjekt.class,
				Factory.eq(LWProjektMeta.ATR_PROJEKTNUMMER, request.getProjectNumber()));
		if (projekte.isEmpty())
			return PacketEtappeEKGetReply.create(request, new ErrorMessage(
					Msg.mk("Kein Projektbild {} gefunden", request.getProjectNumber())));
		if (projekte.size() > 1)
			return PacketEtappeEKGetReply.create(request, new ErrorMessage(
					Msg.mk("Projektnummer {} nicht eindeutig", request.getProjectNumber())));
		
		final LWProjekt projekt = projekte.iterator().next();
		final LWXPAuftragPojo exPojo = LWXPAuftragPojo.create(projekt.getPK());
		{ // load or create project extension
			final LWXPAuftrag exProjekt = projekt.getLWXPAuftrag();
			if (exProjekt == null) {
				// initialize to default values
				exPojo.setHausBemerkungenUebersicht(Msg.mk("Einzelküche {}", request.getProjectNumber()));
			} else {
				exProjekt.updatePojo(exPojo);
			}
		}

		// load etappe (if exists)
		final LWEtappeData etappe;
		if (exPojo.getEtappeRowId() != null) {
			final LWEtappeKey key = LWEtappeKey.get(exPojo.getEtappeRowId());
			etappe = LWObject.validateExists(cache.getObject(key, LWXEtappe.class));
		} else {
			etappe = null;
		}
		
		return PacketEtappeEKGetReply.create(request, exPojo, etappe);
	}

}

package heag.huo.server.network;

import java.sql.SQLException;
import java.util.concurrent.atomic.AtomicReference;

import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.jdbc.StatementParameters;
import ch.eisenring.logiware.LWContextSink;
import ch.eisenring.logiware.LWProjektKey;
import ch.eisenring.lw.LWConstants;
import ch.eisenring.lw.meta.LWXPAuftragMeta;
import ch.eisenring.lw.pojo.LWXPAuftragPojo;
import ch.eisenring.model.TransactionContext;
import ch.eisenring.model.engine.jdbc.JDBCObjectMapper;
import ch.eisenring.model.engine.jdbc.SingleTableMapping;
import ch.eisenring.model.engine.jdbc.TableMapping;
import ch.eisenring.model.mapping.POJOMapping;
import ch.eisenring.network.PacketSink;
import ch.eisenring.network.packet.AbstractPacket;
import heag.huo.server.HUOServer;
import heag.huo.shared.network.PacketOUSCommentStoreReply;
import heag.huo.shared.network.PacketOUSCommentStoreRequest;

final class OUSCommentStoreRequestHandler extends AbstractHUOPacketHandler {

	private OUSCommentStoreRequestHandler(final HUOServer server) {
		super(server, PacketOUSCommentStoreRequest.class);
	}

	@Override
	public PacketOUSCommentStoreReply handle(final AbstractPacket packet) {
		final PacketOUSCommentStoreRequest request = (PacketOUSCommentStoreRequest) packet;
		final PacketOUSCommentStoreReply reply = handle(server, request);
		return reply;
	}

	@Override
	public void handle(final AbstractPacket packet, final PacketSink sink) {
		final PacketOUSCommentStoreRequest request = (PacketOUSCommentStoreRequest) packet;
		final PacketOUSCommentStoreReply reply = handle(server, request);
		sink.sendPacket(reply);
	}

	static PacketOUSCommentStoreReply handle(final HUOServer server, final PacketOUSCommentStoreRequest request) {
		try {
			final PacketOUSCommentStoreReply reply = handleImpl(server, request);
			return reply == null ? PacketOUSCommentStoreReply.create(request, ErrorMessage.ERROR) : reply;
		} catch (final Exception e) {
			return PacketOUSCommentStoreReply.create(request, new ErrorMessage(e));
		}
	}

	final static SingleTableMapping TM = TableMapping.get(LWXPAuftragMeta.METACLASS, LWXPAuftragMeta.TABLE);
	final static POJOMapping<LWXPAuftragPojo> PM = POJOMapping.get(LWXPAuftragMeta.METACLASS, LWXPAuftragPojo.class);
	final static JDBCObjectMapper<LWXPAuftragPojo> OM = JDBCObjectMapper.get(TM, PM);

	private static PacketOUSCommentStoreReply handleImpl(
			final HUOServer server, final PacketOUSCommentStoreRequest request) throws Exception {
		final AtomicReference<LWXPAuftragPojo> result = new AtomicReference<>();
		final LWContextSink sink = new LWContextSink(LWConstants.LWX) {
			@Override
			protected void storeImpl(final TransactionContext context) throws SQLException {
				final LWProjektKey projektKey = request.getProjektKey();
				// fetch existing row
				final StringMaker sql = StringMaker.obtain();
				final StatementParameters params = new StatementParameters();
				OM.appendSelectSQL(sql, this);
				sql.append(" WHERE ");
				sql.append(TM.getColumn(LWXPAuftragMeta.ATR_PROJEKTNUMMER));
				sql.append(" = ? AND ");
				sql.append(TM.getColumn(LWXPAuftragMeta.ATR_GSE_PROJEKT));
				sql.append(" = ?");
				params.add(projektKey.getProjektnummer());
				params.add(projektKey.getGSE());
				doQuery(sql.release(), params, (resultSet) -> {
					final LWXPAuftragPojo pojo = OM.loadRow(context, resultSet);
					if (!result.compareAndSet(null, pojo))
						throw new SQLException("Unexpected multiple rows");
				});
				// create or update row
				LWXPAuftragPojo pojo = result.get();
				if (pojo == null) {
					pojo = LWXPAuftragPojo.create(projektKey);
					pojo.setOUSBemerkung(request.getComment());
					OM.insert(this, pojo);
					result.set(pojo);
				} else {
					pojo.setOUSBemerkung(request.getComment());
					OM.update(this, pojo);
				}
			}
		};
		final TransactionContext context = new TransactionContext();
		context.store(sink);
		return PacketOUSCommentStoreReply.create(request, result.get());		
	}

}

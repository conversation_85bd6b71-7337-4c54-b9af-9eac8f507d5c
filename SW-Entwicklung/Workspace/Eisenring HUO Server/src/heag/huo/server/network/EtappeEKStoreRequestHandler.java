package heag.huo.server.network;

import static ch.eisenring.lw.LWMapping.OM_LWXETAPPE;
import static ch.eisenring.lw.LWMapping.OM_LWXPAUFTRAG;
import static ch.eisenring.lw.LWMapping.TM_LWXETAPPE;

import java.sql.SQLException;

import ch.eisenring.app.server.model.ContextSink;
import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.lw.export.LWExportAuftrag;
import ch.eisenring.lw.export.LWExporter;
import ch.eisenring.lw.export.etappen.LWExportPAuftragEtappe;
import ch.eisenring.lw.model.navigable.LWXPAuftrag;
import ch.eisenring.lw.pojo.LWEtappePojo;
import ch.eisenring.lw.pojo.LWXPAuftragPojo;
import ch.eisenring.model.TransactionContext;
import ch.eisenring.model.TransactionSink;
import ch.eisenring.network.PacketSink;
import ch.eisenring.network.packet.AbstractPacket;
import heag.huo.server.HUOServer;
import heag.huo.shared.network.PacketEtappeEKStoreReply;
import heag.huo.shared.network.PacketEtappeEKStoreRequest;

final class EtappeEKStoreRequestHandler extends AbstractHUOPacketHandler {
	
	EtappeEKStoreRequestHandler(final HUOServer server) {
		super(server, PacketEtappeEKStoreRequest.class);
	}

	@Override
	public PacketEtappeEKStoreReply handle(final AbstractPacket packet) {
		final PacketEtappeEKStoreRequest request = (PacketEtappeEKStoreRequest) packet;
		final PacketEtappeEKStoreReply reply = handle(server, request);
		return reply;
	}

	@Override
	public void handle(final AbstractPacket packet, final PacketSink sink) {
		final PacketEtappeEKStoreRequest request = (PacketEtappeEKStoreRequest) packet;
		final PacketEtappeEKStoreReply reply = handle(server, request);
		sink.sendPacket(reply);
	}

	private static PacketEtappeEKStoreReply handle(final HUOServer server, final PacketEtappeEKStoreRequest request) {
		try {
			final PacketEtappeEKStoreReply reply = handleImpl(server, request);
			if (reply != null)
				return reply;
			return PacketEtappeEKStoreReply.create(request, ErrorMessage.ERROR);
		} catch (final Exception e) {
			return PacketEtappeEKStoreReply.create(request, new ErrorMessage(e));
		}
	}
	
	private static PacketEtappeEKStoreReply handleImpl(final HUOServer server, final PacketEtappeEKStoreRequest request) throws Exception {
		final TransactionSink sink = new ContextSink(TM_LWXETAPPE) {
			@Override
			protected void storeImpl(final TransactionContext context) throws SQLException {
				OM_LWXETAPPE.upsert(this, List.asList(request.getEtappe()));
				OM_LWXPAUFTRAG.upsert(this, List.asList(request.getProjekt()));
			}
		};
		final TransactionContext context = new TransactionContext();
		context.store(sink);

		Logger.debug("Exporting EK Etappen to PAuftrag from HUO now");
		final List<LWExportPAuftragEtappe> exportPAuftragEtappeList = new ArrayList<>();
		LWXPAuftragPojo projekt = request.getProjekt();
		LWEtappePojo etappe = request.getEtappe();
		LWExportPAuftragEtappe lwExportPAuftragEtappe = LWExportPAuftragEtappe.create(projekt.getProjektKey());
		lwExportPAuftragEtappe.setEtappeRowId(etappe == null ? null : etappe.getRowId());
		lwExportPAuftragEtappe.setEtappeStart(etappe == null ? TimestampUtil.NULL_TIMESTAMP : etappe.getBegin());
		lwExportPAuftragEtappe.setEtappeEnde(etappe == null ? TimestampUtil.NULL_TIMESTAMP : etappe.getEnd());
		exportPAuftragEtappeList.add(lwExportPAuftragEtappe);
		final LWExporter exporter = LWExporter.create(server);
		exporter.exportPAuftrag(exportPAuftragEtappeList);

		return PacketEtappeEKStoreReply.create(request, ErrorMessage.OK);
	}

}

package ch.eisenring.reporting.report.offenepositionen;

import ch.eisenring.jdbc.JDBCResultSet;
import ch.eisenring.logiware.LWPositionKey;
import ch.eisenring.logiware.code.hard.LWLagerArtCode;

import java.util.Date;

public class PositionsInfo {
    public LWPositionKey positionKey;
    public String produktNr;
    public String produktBezeichnung;
    public String produktText;
    public String aufwand = "";
    public String bestellMenge;
    public Date wunschDatum;
    public String lagerPosition;
    public String lagerPlatz;
    public Date lieferTermin;
    public LWLagerArtCode lagerArtikel;
    public String Lieferant;
    public Integer bestellNr;
    public String alleBestellNr;
    public Date erfassungsDatumBestellung;

    public String lieferant;
    public String lieferantPlz;
    public String lieferantOrt;
    public String lieferantCode;
    public Date wunschDatumBestellung;
    public String grpTxtBez;
    public Date erfassungDatumPosition;
}

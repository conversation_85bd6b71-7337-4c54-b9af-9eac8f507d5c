package ch.eisenring.reporting.report.kmem;

import ch.eisenring.logiware.LWAuftragKey;
import ch.eisenring.logiware.LWProjektKey;
import ch.eisenring.logiware.code.soft.GSECode;

import java.util.Date;

public class KMEMFormular {
    public final LWProjektKey projektKey;
    public Date formDate;
    public LWAuftragKey auftragKey;
    public String monteurCodeSoft;
    public String monteurCodeHard;

    public KMEMFormular(final String projektnummer, final GSECode gse) {
        this.projektKey = LWProjektKey.get(projektnummer, gse);
    }

}


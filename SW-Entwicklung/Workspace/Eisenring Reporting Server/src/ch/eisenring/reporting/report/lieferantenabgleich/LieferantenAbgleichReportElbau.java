package ch.eisenring.reporting.report.lieferantenabgleich;

import ch.eisenring.reporting.report.Column;
import ch.eisenring.reporting.util.CellStyleManager;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;

import java.lang.reflect.Field;
import java.time.LocalDate;
import java.time.temporal.WeekFields;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static ch.eisenring.reporting.report.lieferantenabgleich.LieferantenAbgleichReportElbau.Columns.*;
import static ch.eisenring.reporting.util.CellStyleManager.CellStyleName.*;

public class LieferantenAbgleichReportElbau {
    public interface Columns {
        // Define columns with dynamically calculated positions
        AtomicInteger positionIndex = new AtomicInteger();
        Column LIEFER_DATUM = new Column(positionIndex.getAndIncrement(), "Lieferdatum", CellType.NUMERIC);
        Column KOMMISSION = new Column(positionIndex.getAndIncrement(), "Kom. Nr.", 20);
        Column ADRESSE = new Column(positionIndex.getAndIncrement(), "Projekt Bezeichnung/Adresse", 20);
        Column AB = new Column(positionIndex.getAndIncrement(), "AB Nr.", 20);

        Column VERLADEREIHENFOLGE = new Column(positionIndex.getAndIncrement(), "Verladereihenfolge", 20);
        Column LIEFER_ZEIT = new Column(positionIndex.getAndIncrement(), "Lieferzeit", 20);
        Column BEMERKUNG_HEAG = new Column(positionIndex.getAndIncrement(), "Bemerkung HEAG", 20);
        Column ANSPRECHPERSON = new Column(positionIndex.getAndIncrement(), "Ansprechsperson vor Ort", 20);
        Column LIEFERUNG_BESTAETIGT = new Column(positionIndex.getAndIncrement(), "Lieferung Bestätigt", 20);
        Column BEMERKUNG_LIEFERANT = new Column(positionIndex.getAndIncrement(), "Bemerkung Lieferant", 20);

    }

    public static Workbook addTab(Workbook workbook, List<BestellInfo> bestellInfoList, LocalDate startDate) {
        CellStyleManager cellStyleManager = new CellStyleManager(workbook);

        int sheetCount = workbook.getNumberOfSheets();
        Sheet sht1;
        try {
            sht1 = workbook.createSheet(createSheetName(startDate));
        } catch (IllegalArgumentException e) {
            sht1 = workbook.createSheet(createSheetName(startDate) + "_" + sheetCount);
        }

        // Freeze first row
        sht1.createFreezePane(0, 1);

        AtomicInteger rowIndex = new AtomicInteger();
        // ColumnHeaderRow
        Row row = sht1.createRow(rowIndex.getAndIncrement());
        final int columnCount = LieferantenAbgleichReportElbau.Columns.positionIndex.get();
        for (int i = 0; i < columnCount; i++) {
            Cell cell = row.createCell(i);
            cell.setCellStyle(cellStyleManager.getStyle(HEADER));
            setupHeaderCell(cell, i);
        }

        // Sort list
        bestellInfoList.sort(Comparator.comparing(BestellInfo::getWunschDatum)
                .thenComparing(BestellInfo::getBasisNr)
                .thenComparing(BestellInfo::getKommissionsNr));

        Map<Date, Map<String, List<BestellInfo>>> grouped =
                bestellInfoList.stream()
                        .collect(Collectors.groupingBy(
                                BestellInfo::getWunschDatum,
                                LinkedHashMap::new, // Preserve order
                                Collectors.groupingBy(BestellInfo::getBasisNr, LinkedHashMap::new, Collectors.toList())
                        ));

        // Data
        int line = 0;
        int lineOffset = 1;
        int group = 0;
        Map<CellStyle, CellStyle> cellStyleWithBottomBorder = new HashMap<>();
        for (Date deliveryDate : grouped.keySet()) {
            Map<String, List<BestellInfo>> bestellInfoPerDate = grouped.get(deliveryDate);
            for (String basis : bestellInfoPerDate.keySet()) {

                group++;
                List<BestellInfo> bestellInfoPerProject = bestellInfoPerDate.get(basis);
                for (BestellInfo bestellInfo : bestellInfoPerProject) {
                    line++;
                    bestellInfo.nr = group;
                    Row dataRow = sht1.createRow(rowIndex.getAndIncrement());
                    for (int i = 0; i < columnCount; i++) {
                        setupDataCell(dataRow, cellStyleManager, i, bestellInfo);
                    }
                }
                if (line > 0) {
                    if (lineOffset != sht1.getLastRowNum()) {
                        sht1.addMergedRegion(new CellRangeAddress(lineOffset, sht1.getLastRowNum(), 5, 5));
                    }
                    Row lastRow = sht1.getRow(sht1.getLastRowNum());
                    for (int i = 0; i < columnCount; i++) {
                        Cell cell = lastRow.getCell(i);
                        if (cellStyleWithBottomBorder.containsKey(cell.getCellStyle())) {
                            CellStyle newCellStyle = cellStyleWithBottomBorder.get(cell.getCellStyle());
                            cell.setCellStyle(newCellStyle);
                        } else {
                            CellStyle sourceCellStyle = cell.getCellStyle();
                            CellStyle newCellStyle = workbook.createCellStyle();
                            newCellStyle.cloneStyleFrom(sourceCellStyle);
                            newCellStyle.setBorderBottom(BorderStyle.MEDIUM);
                            cell.setCellStyle(newCellStyle);
                            cellStyleWithBottomBorder.put(sourceCellStyle, newCellStyle);
                        }
                    }
                    lineOffset = sht1.getLastRowNum() + 1;
                    line = 0;
                }

            }

        }

        // set Column width
        for (int columnIndex = 0; columnIndex < columnCount; columnIndex++) {
            sht1.autoSizeColumn(columnIndex);
        }

        sht1.setColumnWidth(0, 28 * 256);
        sht1.setColumnWidth(5, 15 * 256);
        sht1.setAutoFilter(new CellRangeAddress(0, rowIndex.get() - 1, 0, columnCount - 1));
        workbook.setActiveSheet(sheetCount);

        return workbook;
    }

    /**
     * Pattern KW_13_25
     *
     * @param startDate
     * @return
     */
    private static String createSheetName(LocalDate startDate) {
        int year = startDate.getYear() % 100;
        int weekOfYear = startDate.get(WeekFields.of(Locale.getDefault()).weekOfWeekBasedYear());
        return "KW" + "_" + weekOfYear + "_" + year;
    }

    private static void setupHeaderCell(Cell cell, int columnIndex) {
        try {
            for (Field field : LieferantenAbgleichReportElbau.Columns.class.getFields()) {
                if (Column.class.isAssignableFrom(field.getType())) {
                    Column column = (Column) field.get(null);
                    if (columnIndex == column.getPosition()) {
                        cell.setCellValue(column.getHeader() != null ? column.getHeader() : "NULL");
                        return;
                    }
                }
            }
        } catch (IllegalAccessException e) {
            // e.printStackTrace();
        }
        cell.setCellValue("Unknown Column");
    }

    private static void setupDataCell(Row row, CellStyleManager cellStyleManager, int columnIndex, BestellInfo bestellInfo) {
        // LIEFER_DATUM
        if (columnIndex == LIEFER_DATUM.getPosition()) {
            Cell cell = row.createCell(columnIndex, LIEFER_DATUM.getCellType());
            cell.setCellStyle(cellStyleManager.getStyle(DATE_EXTENDED));
            Date value = bestellInfo.wunschDatum;
            if (value == null) {
                cell.setCellValue("");
            } else {
                cell.setCellValue(value);
            }
        }
        // KOMMISSION
        else if (columnIndex == KOMMISSION.getPosition()) {
            Cell cell = row.createCell(columnIndex, KOMMISSION.getCellType());
            cell.setCellStyle(cellStyleManager.getStyle(TEXT));
            String value = String.valueOf(bestellInfo.kommissionsNr);
            cell.setCellValue(value != null ? value : "");
        }
        // ADRESSE
        else if (columnIndex == ADRESSE.getPosition()) {
            Cell cell = row.createCell(columnIndex, ADRESSE.getCellType());
            cell.setCellStyle(cellStyleManager.getStyle(TEXT));
            String value = bestellInfo.bezeichnung;
            if (value == null) {
                cell.setCellValue("");
            } else {
                cell.setCellValue(value.replaceAll("\\r?\\n", " "));
            }
        }
        // AB
        else if (columnIndex == AB.getPosition()) {
            Cell cell = row.createCell(columnIndex, AB.getCellType());
            cell.setCellStyle(cellStyleManager.getStyle(TEXT));
            String value = bestellInfo.bestellNr;
            cell.setCellValue(value != null ? value : "");
        }
        // VERLADEREIHENFOLGE
        else if (columnIndex == VERLADEREIHENFOLGE.getPosition()) {
            Cell cell = row.createCell(columnIndex, VERLADEREIHENFOLGE.getCellType());
            cell.setCellStyle(cellStyleManager.getStyle(TEXT));
            cell.setCellValue("");
        }
        // LIEFER_ZEIT
        else if (columnIndex == LIEFER_ZEIT.getPosition()) {
            Cell cell = row.createCell(columnIndex, LIEFER_ZEIT.getCellType());
            cell.setCellStyle(cellStyleManager.getStyle(DATA_WITH_BORDER));
            cell.setCellValue("");
        }
        // BEMERKUNG_HEAG
        else if (columnIndex == BEMERKUNG_HEAG.getPosition()) {
            Cell cell = row.createCell(columnIndex, BEMERKUNG_HEAG.getCellType());
            cell.setCellStyle(cellStyleManager.getStyle(TEXT));
            cell.setCellValue("");
        }
        // ANSPRECHPERSON
        else if (columnIndex == ANSPRECHPERSON.getPosition()) {
            Cell cell = row.createCell(columnIndex, ANSPRECHPERSON.getCellType());
            cell.setCellStyle(cellStyleManager.getStyle(TEXT));
            cell.setCellValue("");
        }
        // LIEFERUNG_BESTAETIGT
        else if (columnIndex == LIEFERUNG_BESTAETIGT.getPosition()) {
            Cell cell = row.createCell(columnIndex, LIEFERUNG_BESTAETIGT.getCellType());
            cell.setCellStyle(cellStyleManager.getStyle(TEXT));
            cell.setCellValue("");
        }
        // BEMERKUNG_LIEFERANT
        else if (columnIndex == BEMERKUNG_LIEFERANT.getPosition()) {
            Cell cell = row.createCell(columnIndex, BEMERKUNG_LIEFERANT.getCellType());
            cell.setCellStyle(cellStyleManager.getStyle(TEXT_WITH_OBRDER_RIGHT));
            cell.setCellValue("");
        }
    }

}

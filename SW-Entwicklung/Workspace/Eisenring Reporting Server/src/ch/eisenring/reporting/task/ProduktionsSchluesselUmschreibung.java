package ch.eisenring.reporting.task;

import ch.eisenring.app.shared.AbstractSoftwareComponent;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.io.file.FileItem;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.core.poi.POIUtil;
import ch.eisenring.email.EMail;
import ch.eisenring.email.EMailAttachment;
import ch.eisenring.jdbc.JDBCResultSet;
import ch.eisenring.logiware.LWContextSource;
import ch.eisenring.model.TransactionContext;
import ch.eisenring.model.engine.jdbc.DatabaseSpecifier;
import ch.eisenring.reporting.server.ReportingServerConfig;
import ch.eisenring.reporting.util.DatabaseConstants.BasisDboSchema.AppcodeTable;
import ch.eisenring.reporting.util.DatabaseConstants.BasisDboSchema.GbzGruppeTable;
import ch.eisenring.reporting.util.DatabaseConstants.VertriebDboSchema.AuftragTable;
import ch.eisenring.reporting.util.DatabaseConstants.VertriebDboSchema.AuftragsKopfZusatzTable;
import ch.eisenring.reporting.util.DatabaseConstants.VertriebDboSchema.PAuftragTable;
import ch.eisenring.reporting.util.ExcelHelper.ExcelCellStyle;
import ch.eisenring.reporting.util.ExcelHelper.ExcelCellStyle.BorderLine;
import ch.eisenring.reporting.util.ExcelHelper.ExcelCellStyle.HorizAlign;
import org.apache.poi.ss.usermodel.*;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.sql.SQLException;
import java.util.Calendar;
import java.util.Collection;
import java.util.concurrent.atomic.AtomicInteger;

public class ProduktionsSchluesselUmschreibung implements ReportingTask {

	public interface ProdSchlCol {
		AtomicInteger COUNT = new AtomicInteger();
		int Basisnummer = COUNT.getAndIncrement();
		int Kommissionsnummer = COUNT.getAndIncrement();
		int Objektbezeichnung = COUNT.getAndIncrement();
		int Belegnummer = COUNT.getAndIncrement();
		int Wunschdatum = COUNT.getAndIncrement();
		int Kalenderwoche = COUNT.getAndIncrement();
		int AWA = COUNT.getAndIncrement();
		int Produktionsschluessel = COUNT.getAndIncrement();
		int Objektbetreuer = COUNT.getAndIncrement();
		int SachbearbeiterKundenberater = COUNT.getAndIncrement();
	}

	private String createQuery(StringBuilder body) {

		body.append("<p>Filterkriterien:<ul>");
		body.append("<li>Projekt/Kom: nicht stornierte</li>");
		body.append("<li>Auftrag: nicht stornierte</li>");
		body.append("<li>AWA: 001, 129, 135</li>");
		body.append("<li>Aufträge nur mit Projektbild</li>");
		body.append("<li>Aufträge mit und ohne Produktionsschlüssel</li>");
		body.append("<li>Aufträge mit und ohne OB</li>");
		body.append("<li>Aufträge mit und ohne SaBe</li>");
		body.append("</ul></p>");

		body.append("<p>Sortierung:<ol>");
		body.append("<li>Produktionsschlüssel</li>");
		body.append("<li>Wunschdatum (neuste zuerst)</li>");
		body.append("<li>Belegnummer</li>");
		body.append("</ol></p>");

		return new StringBuilder() //
			.append("SELECT p.").append(PAuftragTable.P_AUFTRNR_B).append(" Basisnummer") //
			.append(", p.").append(PAuftragTable.P_AUFTR_NR).append(" Kommissionsnummer") //
			.append(", p.").append(PAuftragTable.P_BEZ).append(" Objektbezeichung") //
			.append(", a.").append(AuftragTable.AUFTR_NR).append(" Belegnummer") //
			.append(", a.").append(AuftragTable.ABW_ART).append(" AWA") //
			.append(", a.").append(AuftragTable.DAT_WU).append(" Wunschdatum") //
			.append(", sch.").append(AppcodeTable.BEZEICHNUNG).append(" Produktionsschluessel") //
			.append(", ob.").append(AppcodeTable.BEZEICHNUNG).append(" ObName") //
			.append(", sb.").append(GbzGruppeTable.BEZEICHNUNG).append(" SaBeName") //

			.append(" FROM ").append(AuftragTable._TN).append(" a") //

			.append(" JOIN ").append(PAuftragTable._TN).append(" p") //
			.append(" ON p.").append(PAuftragTable.P_AUFTR_NR).append(" = a.").append(AuftragTable.P_AUFTR_NR) //
			.append(" AND p.").append(PAuftragTable.ID_GSE_PA).append(" = a.").append(AuftragTable.ID_GSE_PA) //

			.append(" LEFT JOIN ").append(AuftragsKopfZusatzTable._TN).append(" akz") //
			.append(" ON akz.").append(AuftragsKopfZusatzTable.AUFTR_NR).append(" = a.").append(AuftragTable.AUFTR_NR) //
			.append(" AND akz.").append(AuftragsKopfZusatzTable.ID_GSE).append(" = a.").append(AuftragTable.ID_GSE) //

			.append(" LEFT JOIN ").append(AppcodeTable._TN).append(" sch") //
			.append(" ON sch.").append(AppcodeTable.CD_HARD).append(" = akz.").append(AuftragsKopfZusatzTable.SCH_BLOCK8) //
			.append(" AND sch.").append(AppcodeTable.TAB_PRFX).append(" = 'LAKZ'") //
			.append(" AND sch.").append(AppcodeTable.COL_NAME).append(" = 'SCH_BLOCK8'") //
			.append(" AND sch.").append(AppcodeTable.CD_SPRACHE).append(" = '001'") //

			.append(" LEFT JOIN ").append(AppcodeTable._TN).append(" ob") //
			.append(" ON ob.").append(AppcodeTable.CD_HARD).append(" = p.").append(PAuftragTable.P_FAKT_PER_CD) //
			.append(" AND ob.").append(AppcodeTable.COL_NAME).append(" = 'PFAKTPERCD'") //
			.append(" AND ob.").append(AppcodeTable.CD_SPRACHE).append(" = '001'") //

			.append(" LEFT JOIN ").append(GbzGruppeTable._TN).append(" sb") //
			.append(" ON sb.").append(GbzGruppeTable.GRUPPE).append(" = p.").append(PAuftragTable.VERT1) //
			.append(" AND sb.").append(GbzGruppeTable.GBZ_GR_ART).append(" = '900'") //
			.append(" AND sb.").append(GbzGruppeTable.ID_GSE).append(" = '2000'") //

			.append(" WHERE a.").append(AuftragTable.ABW_ART).append(" in ('001','129','135')") //
			.append(" AND a.").append(AuftragTable.STATUS).append(" != '099'") //
			.append(" AND p.").append(PAuftragTable.STATUS).append(" != '099'") //

			.append(" ORDER BY iif(akz.").append(AuftragsKopfZusatzTable.SCH_BLOCK8).append(" is null OR akz.").append(AuftragsKopfZusatzTable.SCH_BLOCK8).append(" = '000', '999', akz.")
			.append(AuftragsKopfZusatzTable.SCH_BLOCK8).append("), a.").append(AuftragTable.DAT_WU).append(" desc, a.").append(AuftragTable.AUFTR_NR).append(" desc") //
			.toString();
	}

	private void setupDataCell(int columnIndex, JDBCResultSet rst, Cell cell, CellStyle datumCellStyle, CellStyle nullCellStyle, CellStyle multilineCellStyle) throws SQLException {
		if (columnIndex == ProdSchlCol.Basisnummer) {
			String value = rst.getString("Basisnummer");
			if (value == null) {
				cell.setCellValue("-");
				cell.setCellStyle(nullCellStyle);
			} else if (value.matches("\\d+")) {
				cell.setCellValue(Integer.parseInt(value));
			} else {
				cell.setCellValue(value);
			}
		} else if (columnIndex == ProdSchlCol.Kommissionsnummer) {
			String value = rst.getString("Kommissionsnummer");
			if (value == null) {
				cell.setCellValue("-");
				cell.setCellStyle(nullCellStyle);
			} else if (value.matches("\\d+")) {
				cell.setCellValue(Integer.parseInt(value));
			} else {
				cell.setCellValue(value);
			}
		} else if (columnIndex == ProdSchlCol.Objektbezeichnung) {
			String value = rst.getString("Objektbezeichung");
			if (value == null) {
				cell.setCellValue("-");
				cell.setCellStyle(nullCellStyle);
			} else {
				cell.setCellValue(value);
				cell.setCellStyle(multilineCellStyle);
			}
		} else if (columnIndex == ProdSchlCol.Wunschdatum) {
			java.util.Date value = rst.getDate("Wunschdatum");
			if (value == null) {
				cell.setCellValue(0);
				cell.setCellStyle(nullCellStyle);
			} else {
				cell.setCellValue(value);
				cell.setCellStyle(datumCellStyle);
			}
		} else if (columnIndex == ProdSchlCol.Kalenderwoche) {
			java.util.Date value = rst.getDate("Wunschdatum");
			if (value == null) {
				cell.setCellValue(0);
				cell.setCellStyle(nullCellStyle);
			} else {
				Calendar cal = Calendar.getInstance();
				cal.setTime(value);
				cell.setCellValue(cal.get(Calendar.WEEK_OF_YEAR));
			}
		} else if (columnIndex == ProdSchlCol.Produktionsschluessel) {
			String value = rst.getString("Produktionsschluessel");
			if (value == null) {
				cell.setCellValue(0);
				cell.setCellStyle(nullCellStyle);
			} else {
				cell.setCellValue(value);
			}
		} else if (columnIndex == ProdSchlCol.Objektbetreuer) {
			String value = rst.getString("ObName");
			if (value == null) {
				cell.setCellValue(0);
				cell.setCellStyle(nullCellStyle);
			} else {
				cell.setCellValue(value);
			}
		} else if (columnIndex == ProdSchlCol.SachbearbeiterKundenberater) {
			String value = rst.getString("SaBeName");
			if (value == null) {
				cell.setCellValue(0);
				cell.setCellStyle(nullCellStyle);
			} else {
				cell.setCellValue(value);
			}
		} else if (columnIndex == ProdSchlCol.Belegnummer) {
			Integer value = rst.getInteger("Belegnummer");
			if (value == null) {
				cell.setCellValue(0);
				cell.setCellStyle(nullCellStyle);
			} else {
				cell.setCellValue(value);
			}
		} else if (columnIndex == ProdSchlCol.AWA) {
			String value = rst.getString("AWA");
			if (value == null) {
				cell.setCellValue(0);
				cell.setCellStyle(nullCellStyle);
				// } else if (value.matches("\\d+")) {
				// cell.setCellValue(Integer.parseInt(value));
			} else {
				cell.setCellValue("AWA" + value);
			}
		}
	}

	private void setupHeaderCell(int columnIndex, Cell cell, CellStyle headerCellStyle) {
		if (columnIndex == ProdSchlCol.Basisnummer) {
			cell.setCellValue("Basiskommission");
			cell.setCellStyle(headerCellStyle);
		} else if (columnIndex == ProdSchlCol.Kommissionsnummer) {
			cell.setCellValue("Kommissionsnummer");
			cell.setCellStyle(headerCellStyle);
		} else if (columnIndex == ProdSchlCol.Objektbezeichnung) {
			cell.setCellValue("Objektbezeichnung");
			cell.setCellStyle(headerCellStyle);
		} else if (columnIndex == ProdSchlCol.Wunschdatum) {
			cell.setCellValue("Wunschdatum");
			cell.setCellStyle(headerCellStyle);
		} else if (columnIndex == ProdSchlCol.Kalenderwoche) {
			cell.setCellValue("Kalenderwoche");
			cell.setCellStyle(headerCellStyle);
		} else if (columnIndex == ProdSchlCol.Produktionsschluessel) {
			cell.setCellValue("Produktionsschlüssel");
			cell.setCellStyle(headerCellStyle);
		} else if (columnIndex == ProdSchlCol.Objektbetreuer) {
			cell.setCellValue("Objektbetreuer");
			cell.setCellStyle(headerCellStyle);
		} else if (columnIndex == ProdSchlCol.SachbearbeiterKundenberater) {
			cell.setCellValue("Sachbearbeiter oder Kundenberater");
			cell.setCellStyle(headerCellStyle);
		} else if (columnIndex == ProdSchlCol.Belegnummer) {
			cell.setCellValue("Belegnummer");
			cell.setCellStyle(headerCellStyle);
		} else if (columnIndex == ProdSchlCol.AWA) {
			cell.setCellValue("AWA");
			cell.setCellStyle(headerCellStyle);
		}
	}

	@Override
	public Collection<EMail> execute(ReportingServerConfig config, AbstractSoftwareComponent server) throws IOException {

		Workbook workbook = POIUtil.createWorkbook();
		StringBuilder body = new StringBuilder();
		body.append("<html><body style=\"font-family: Verdana;\">");
		body.append("<p>Sehr geehrte Damen und Herren</p>");
		body.append("<p>Anbei die Auswertung Umschreiben Produktionsschlüssel Abdeckung</p>");

		CellStyle headerCellStyle = new ExcelCellStyle(workbook) //
				.setBackgroundColor(IndexedColors.LIGHT_YELLOW) //
				.setBorderLine(BorderLine.NORMAL) //
			.setWordWrap(true) //
			.getStyle();

		CellStyle datumCellStyle = new ExcelCellStyle(workbook) //
			.setDateFormat(workbook, "dd.MM.yyyy") //
			.getStyle();

		CellStyle nullCellStyle = new ExcelCellStyle(workbook) //
			.setHorizAlign(HorizAlign.CENTER) //
			.setFontColor(IndexedColors.GREY_50_PERCENT) //
			.getStyle();

		CellStyle multilineCellStyle = new ExcelCellStyle(workbook) //
			.setWordWrap(true) //
			.getStyle();

		Sheet sht = workbook.createSheet("Auswertung");
		sht.createFreezePane(0, 1);
		AtomicInteger rowIndex = new AtomicInteger();

		// ColumnHeaderRow
		Row row = sht.createRow(rowIndex.getAndIncrement());
		for (int columnIndex = 0; columnIndex < ProdSchlCol.COUNT.get(); columnIndex++) {
			setupHeaderCell(columnIndex, row.createCell(columnIndex), headerCellStyle);
		}

		// Data access
		String sql = createQuery(body);
		new TransactionContext().load(new LWContextSource(DatabaseSpecifier.get("VERTRIEB")) {
			@Override
			protected void loadImpl(TransactionContext context) throws SQLException {
				doQuery(sql, null, rst -> {
					// TableDataRow
					Row row = sht.createRow(rowIndex.getAndIncrement());
					for (int columnIndex = 0; columnIndex < ProdSchlCol.COUNT.get(); columnIndex++) {
						setupDataCell(columnIndex, rst, row.createCell(columnIndex), datumCellStyle, nullCellStyle, multilineCellStyle);
					}
				});
			}
		});

		sht.setColumnWidth(ProdSchlCol.SachbearbeiterKundenberater, 36 * 256);
		sht.setColumnWidth(ProdSchlCol.Wunschdatum, 10 * 256);
		sht.setColumnWidth(ProdSchlCol.Objektbetreuer, 28 * 256);
		sht.setColumnWidth(ProdSchlCol.Produktionsschluessel, 27 * 256);
		// sht.setColumnWidth(ProdSchlCol.Objektbezeichnung, 87 * 256);
		sht.setColumnWidth(ProdSchlCol.Objektbezeichnung, 60 * 256);

		// Save Excel-File
		File folder = new File(config.getExportPath());
		File file = new File(folder, new StringBuilder().append("Auswertung_Produktionsschluessel_").append(System.currentTimeMillis()).append(".xlsx").toString());
		body.append("<p>Im Anhang ").append(file.getName()).append("</p>");
		try (OutputStream out = new FileOutputStream(file)) {
			workbook.write(out);
		} catch (Exception e) {
			Logger.error(e);
		}

		body.append("</body></html>");

		// Email
		Collection<EMail> emailList = new ArrayList<>();
		Collection<String> emailAddresses = config.getEmailAddresses();
		if (emailAddresses != null && file.isFile()) {
			for (String email : emailAddresses) {
				try {
					EMail mail = new EMail();
					mail.setFromName("HEAG Reporting");
					mail.setFromAddress("<EMAIL>");
					mail.setReplyTo("<EMAIL>");
					mail.setSubject("Auswertung Produktionsschlüssel");
					mail.setPlainText(body.toString());
					mail.addTo(email);
					mail.addAttachment(new EMailAttachment(FileItem.create(file)));
					emailList.add(mail);
				} catch (Exception e) {
					Logger.error(e);
				}
			}
		}
		return emailList;
	}

}

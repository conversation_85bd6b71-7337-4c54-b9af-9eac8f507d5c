package ch.eisenring.reporting.task;

import ch.eisenring.app.shared.AbstractSoftwareComponent;
import ch.eisenring.core.io.file.FileItem;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.email.EMail;
import ch.eisenring.email.EMailAttachment;
import ch.eisenring.logiware.LWContextSource;
import ch.eisenring.model.TransactionContext;
import ch.eisenring.model.engine.jdbc.DatabaseSpecifier;
import ch.eisenring.reporting.server.ReportingServerConfig;
import ch.eisenring.reporting.util.CsvHelper.Csv;
import ch.eisenring.reporting.util.DatabaseConstants.BasisDboSchema.GbzGruppeTable;
import ch.eisenring.reporting.util.DatabaseConstants.HeagDmsProduktivDboSchema.DmsAuditTrailTable;
import ch.eisenring.reporting.util.DatabaseConstants.HeagDmsProduktivDboSchema.DmsObjectTable;
import ch.eisenring.reporting.util.DatabaseConstants.HeagDmsProduktivDboSchema.DmsPropertyTable;
import ch.eisenring.reporting.util.DatabaseConstants.VertriebDboSchema.PAuftragTable;
import ch.eisenring.reporting.util.QueryObjects.DbField;
import ch.eisenring.reporting.util.QueryObjects.DbTable;

import java.io.*;
import java.sql.SQLException;
import java.util.*;

public class Auftragsbestaetigung implements ReportingTask {

	private final DbTable auf = new DbTable(PAuftragTable._TN, "a");
	private final DbField aufBasis = new DbField(PAuftragTable.P_AUFTR_NR, auf);
	private final DbField auf_codeId = new DbField(PAuftragTable.VERT1, auf);

	private final DbTable grp = new DbTable(GbzGruppeTable._TN, "g");
	private final DbField grp_codeId = new DbField(GbzGruppeTable.GRUPPE, grp);
	private final DbField grpBezeichnung = new DbField(GbzGruppeTable.BEZEICHNUNG, grp);
	private final DbField grpArt = new DbField(GbzGruppeTable.GBZ_GR_ART, grp);
	private final DbField grpGse = new DbField(GbzGruppeTable.ID_GSE, grp);

	private final DbTable bas = new DbTable(DmsObjectTable._TN, "bas");
	private final DbField basId = new DbField(DmsObjectTable.ROW_ID, bas);
	private final DbField basName = new DbField(DmsObjectTable.OBJECTNAME, bas);

	private final DbTable kom = new DbTable(DmsObjectTable._TN, "kom");
	private final DbField komId = new DbField(DmsObjectTable.ROW_ID, kom);
	private final DbField kom_basId = new DbField(DmsObjectTable.PARENT_ROW_ID, kom);
	private final DbField komName = new DbField(DmsObjectTable.OBJECTNAME, kom);

	private final DbTable fol = new DbTable(DmsObjectTable._TN, "fol");
	private final DbField folId = new DbField(DmsObjectTable.ROW_ID, fol);
	private final DbField fol_komId = new DbField(DmsObjectTable.PARENT_ROW_ID, fol);
	private final DbField folName = new DbField(DmsObjectTable.OBJECTNAME, fol);

	private final DbTable dok = new DbTable(DmsObjectTable._TN, "dok");
	private final DbField dokId = new DbField(DmsObjectTable.ROW_ID, dok);
	private final DbField dokDate = new DbField(DmsObjectTable.LAST_CHANGED, dok);
	private final DbField dok_folId = new DbField(DmsObjectTable.PARENT_ROW_ID, dok);
	private final DbField dokName = new DbField(DmsObjectTable.OBJECTNAME, dok);

	private final DbTable prp = new DbTable(DmsPropertyTable._TN, "p");
	private final DbField prpValue = new DbField(DmsPropertyTable.PROPERTYVALUE, prp);
	private final DbField prpCode = new DbField(DmsPropertyTable.PROPERTYCODE, prp);
	private final DbField prp_objId = new DbField(DmsPropertyTable.OBJECT_ROW_ID, prp);

	private final DbTable trl = new DbTable(DmsAuditTrailTable._TN, "t");
	private final DbField trlMadeBy = new DbField(DmsAuditTrailTable.MADE_BY, trl);
	private final DbField trlMadeOn = new DbField(DmsAuditTrailTable.MADE_ON, trl);
	private final DbField trl_dokId = new DbField(DmsAuditTrailTable.OBJECT_ROW_ID, trl);

	private final String BGZG_GBZGRART_Verkaeufer_Kundenberater_Conseiller = "'900'";
	private final String GSE_HEAG_COMPANY = "'2000'";
	private final String EINZELKUECHEN = "'Einzelküchen'";

	@Override
	public Collection<EMail> execute(ReportingServerConfig config, AbstractSoftwareComponent server) throws IOException {
		final Map<String, String> mapKundenberaterForBasisnummer = new HashMap<>();
		new TransactionContext().load(new LWContextSource(DatabaseSpecifier.get("VERTRIEB")) {
			@Override
			protected void loadImpl(TransactionContext context) throws SQLException {
				doQuery(createMappingQuery(), null, resultSet -> mapKundenberaterForBasisnummer.put(resultSet.getString(aufBasis.alias), resultSet.getString(grpBezeichnung.alias)));
			}
		});
		Collection<EMail> list = new ArrayList<>();
		new TransactionContext().load(new LWContextSource(DatabaseSpecifier.get("DMS")) {
			@Override
			protected void loadImpl(TransactionContext context) throws SQLException {
				File folder = new File(config.getExportPath() != null ? config.getExportPath() : System.getProperty("user.home"));
				if (folder.exists() || folder.mkdirs()) {
					int days = 7;
					Calendar cal = Calendar.getInstance();
					cal.add(Calendar.DATE, -days);
					for (int i = 0; i < days; i++) {
						int y = cal.get(Calendar.YEAR);
						int m = cal.get(Calendar.MONTH) + 1;
						int d = cal.get(Calendar.DAY_OF_MONTH);
						String filename = String.format("Auftragsbestaetigung-%04d-%02d-%02d.csv", y, m, d);
						File file = new File(folder, filename);
						if (!file.exists()) {
							try {
								try (PrintWriter out = new PrintWriter(new OutputStreamWriter(new FileOutputStream(file), Csv.charset()))) {
									out.print("Datum");
									out.print(Csv.delim());
									out.print("Basis-Nr");
									out.print(Csv.delim());
									out.print("Basis");
									out.print(Csv.delim());
									out.print("Kommission-Nr");
									out.print(Csv.delim());
									out.print("Kommission");
									out.print(Csv.delim());
									out.print("Dokument");
									out.print(Csv.delim());
									out.print("abgel./bearb./mut.");
									out.print(Csv.delim());
									out.print("LW-Basis Kundenbetreuer");
									out.println();
									doQuery(createOutputQuery(y, m, d), null, rst -> {
										String bpnr = rst.getString("BASISPROJEKTNR");
										String pnr = rst.getString("PROJEKTNR");
										if (bpnr != null && pnr == null)
											pnr = bpnr;
										out.print(Csv.datetime(rst.getDate(dokDate.alias)));
										out.print(Csv.delim());
										out.print(bpnr);
										out.print(Csv.delim());
										out.print(Csv.doublequote(dmsLink(rst.getLong(basId.alias), rst.getString(basName.alias))));
										out.print(Csv.delim());
										out.print(pnr);
										out.print(Csv.delim());
										out.print(Csv.doublequote(dmsLink(rst.getLong(komId.alias), rst.getString(komName.alias))));
										out.print(Csv.delim());
										out.print(Csv.doublequote(dmsLink(rst.getLong(dokId.alias), rst.getString(dokName.alias))));
										out.print(Csv.delim());
										out.print(Csv.doublequote(rst.getString("LASTMUTUSER")));
										out.print(Csv.delim());
										String basisprojektnr = mapKundenberaterForBasisnummer.get(bpnr);
										if (basisprojektnr != null)
											out.print(Csv.doublequote(basisprojektnr));
										out.println();
									});
								}
								list.addAll(createEmail(config.getEmailAddresses(), file));
							} catch (Exception e) {
								Logger.error(e);
							}
						}
						cal.add(Calendar.DATE, 1);
					}
				}
			}
		});
		return list;
	}

	private String createMappingQuery() {
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT ").append(aufBasis.def());
		sql.append(", ").append(grpBezeichnung.def());
		sql.append(" FROM ").append(auf.def());
		sql.append(" JOIN ").append(grp.def()).append(" ON ").append(grp_codeId).append("=").append(auf_codeId);
		sql.append(" WHERE ").append(aufBasis).append(" is not null");
		sql.append(" AND ").append(grpBezeichnung).append(" is not null");
		sql.append(" AND ").append(grpArt).append("=").append(BGZG_GBZGRART_Verkaeufer_Kundenberater_Conseiller);
		sql.append(" AND ").append(grpGse).append("=").append(GSE_HEAG_COMPANY);
		return sql.toString();
	}

	private String createOutputQuery(int y, int m, int d) {
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT ").append(dokDate.def());
		// Basis
		sql.append(", IIF(").append(basName.field).append("=").append(EINZELKUECHEN)
			.append(", (SELECT ").append(prpValue.def())
			.append(" FROM ").append(prp.def())
			.append(" WHERE ").append(prp_objId).append("=").append(komId)
			.append(" AND ").append(prpCode).append("=1)")
			.append(", (SELECT ").append(prpValue.def())
			.append(" FROM ").append(prp.def())
			.append(" WHERE ").append(prp_objId).append("=").append(basId)
			.append(" AND ").append(prpCode).append("=2)")
			.append(") as BASISPROJEKTNR");
		sql.append(", IIF(").append(basName).append("=").append(EINZELKUECHEN)
			.append(",").append(komId)
			.append(",").append(basId)
			.append(") as ").append(basId.alias);
		sql.append(", IIF(").append(basName).append("=").append(EINZELKUECHEN)
			.append(",").append(komName)
			.append(",").append(basName)
			.append(") as ").append(basName.alias);
		// Projekt/Kom
		sql.append(", (SELECT ").append(prpValue.def())
			.append(" FROM ").append(prp.def())
			.append(" WHERE ").append(prp_objId).append("=").append(komId)
			.append(" AND ").append(prpCode).append("=1)")
			.append(" as PROJEKTNR");
		sql.append(", ").append(komId.def());
		sql.append(", ").append(komName.def());
		// Dokument
		sql.append(", ").append(dokId.def());
		sql.append(", ").append(dokName.def());
		// Mutation
		sql.append(", (SELECT TOP (1) ").append(trlMadeBy.def())
			.append(" FROM ").append(trl.def())
			.append(" WHERE ").append(trl_dokId).append("=").append(dokId)
			.append(" ORDER BY ").append(trlMadeOn).append(" desc)")
			.append(" as LASTMUTUSER");
		// Tables
		sql.append(" FROM ").append(dok.def());
		sql.append(" JOIN ").append(fol.def()).append(" ON ").append(folId).append("=").append(dok_folId);
		sql.append(" JOIN ").append(kom.def()).append(" ON ").append(komId).append("=").append(fol_komId);
		sql.append(" JOIN ").append(bas.def()).append(" ON ").append(basId).append("=").append(kom_basId);
		// Filter
		sql.append(" WHERE ").append(folName).append(" in ('6 Einkauf AB','Sammelauftrag AB','Sammeleinkauf AB')");
		sql.append(" AND year(").append(dokDate).append(")=").append(y);
		sql.append(" AND month(").append(dokDate).append(")=").append(m);
		sql.append(" AND day(").append(dokDate).append(")=").append(d);
		// Sorting
		sql.append(" ORDER BY ").append(basId).append(", ").append(komId).append(", ").append(folId).append(", ").append(dokId);
		return sql.toString();
	}

	private String dmsLink(Long objectId, String objectName) {
		return new StringBuilder().append("=HYPERLINK(").append(Csv.doublequote("EisenringDMS://Goto/ObjectId/" + objectId))
			.append(";").append(Csv.doublequote(objectName)).append(")").toString();
	}

	private Collection<EMail> createEmail(Collection<String> emailAddresses, File file) throws IOException {
		Collection<EMail> list = new ArrayList<>();
		if (file.isFile()) {
			long len = file.length();
			if (0 < len && len < 15_000_000) {
				if (emailAddresses != null) {
					for (String email : emailAddresses) {
						EMail mail = new EMail();
						mail.addTo(email);
						mail.setFromName("HEAG Reporting");
						mail.setFromAddress("<EMAIL>");
						mail.setReplyTo("<EMAIL>");
						mail.setSubject(file.getName());
						mail.addAttachment(new EMailAttachment(FileItem.create(file)));
						mail.setPlainText("Guten Morgen\n\nAnbei die neue Auswertung der gestrig geänderten DMS Dokumente.\n\nEinen schönen Tag noch");
						list.add(mail);
					}
				}
			}
		}
		return list;
	}

}

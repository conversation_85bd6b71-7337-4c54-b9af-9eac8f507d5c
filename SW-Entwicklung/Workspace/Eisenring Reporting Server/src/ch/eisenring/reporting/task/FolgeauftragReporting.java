package ch.eisenring.reporting.task;

import ch.eisenring.app.shared.AbstractSoftwareComponent;
import ch.eisenring.core.io.file.FileItem;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.core.poi.POIUtil;
import ch.eisenring.email.EMail;
import ch.eisenring.email.EMailAttachment;
import ch.eisenring.logiware.LWContextSource;
import ch.eisenring.model.TransactionContext;
import ch.eisenring.model.engine.jdbc.DatabaseSpecifier;
import ch.eisenring.reporting.server.ReportingServerConfig;
import ch.eisenring.reporting.util.DatabaseConstants.BasisDboSchema.AppcodeTable;
import ch.eisenring.reporting.util.DatabaseConstants.BasisDboSchema.GbzGruppeTable;
import ch.eisenring.reporting.util.DatabaseConstants.VertriebDboSchema.AufposTable;
import ch.eisenring.reporting.util.DatabaseConstants.VertriebDboSchema.AuftragTable;
import ch.eisenring.reporting.util.DatabaseConstants.VertriebDboSchema.PAuftragTable;
import ch.eisenring.reporting.util.ExcelHelper.ExcelCellStyle;
import ch.eisenring.reporting.util.ExcelHelper.ExcelCellStyle.BorderLine;
import ch.eisenring.reporting.util.ExcelHelper.ExcelCellStyle.VertAlign;
import ch.eisenring.reporting.util.QueryObjects.DbField;
import ch.eisenring.reporting.util.QueryObjects.DbTable;
import ch.eisenring.reporting.util.QueryObjects.Query;
import org.apache.poi.ss.usermodel.*;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.sql.Date;
import java.sql.SQLException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collection;
import java.util.concurrent.atomic.AtomicInteger;

public class FolgeauftragReporting implements ReportingTask {

	public interface CtrlCol {
		AtomicInteger COUNT = new AtomicInteger();
		int BASIS = COUNT.getAndIncrement(); // opt
		int KOMM = COUNT.getAndIncrement();
		int DATUM = COUNT.getAndIncrement(); // opt
		int BAUSTELLENBEZEICHUNG = COUNT.getAndIncrement();
		int ID = COUNT.getAndIncrement();
		int OB = COUNT.getAndIncrement();
		int ANZAHL = COUNT.getAndIncrement();
	}

	private final DbTable prj = new DbTable(PAuftragTable._TN, "prj");
	private final DbField prjBasis = new DbField(PAuftragTable.P_AUFTRNR_B, prj);
	private final DbField prjKomm = new DbField(PAuftragTable.P_AUFTR_NR, prj);
	private final DbField prjBez = new DbField(PAuftragTable.P_BEZ, prj);
	private final DbField prjID = new DbField(PAuftragTable.VERT1, prj);
	private final DbField prjOB = new DbField(PAuftragTable.P_FAKT_PER_CD, prj);

	private final DbTable a = new DbTable(AuftragTable._TN, "a");
	private final DbField aId = new DbField(AuftragTable.AUFTR_NR, a);
	private final DbField a_prjKomm = new DbField(AuftragTable.P_AUFTR_NR, a);
	private final DbField aAWA = new DbField(AuftragTable.ABW_ART, a);
	private final DbField aGse = new DbField(AuftragTable.ID_GSE, a);
	private final DbField aAufDat = new DbField(AuftragTable.AUF_DAT, a);

	private final DbTable pos = new DbTable(AufposTable._TN, "pos");
	private final DbField pos_aId = new DbField(AufposTable.AUFTR_NR, pos);
	private final DbField posStatus = new DbField(AufposTable.STATUS, pos);
	private final DbField posProdNr = new DbField(AufposTable.PROD_NR, pos);

	private final DbTable gbz = new DbTable(GbzGruppeTable._TN, "gbz");
	private final DbField gbzKey = new DbField(GbzGruppeTable.GRUPPE, gbz);
	private final DbField gbzArt = new DbField(GbzGruppeTable.GBZ_GR_ART, gbz);
	private final DbField gbzGse = new DbField(GbzGruppeTable.ID_GSE, gbz);
	private final DbField gbzName = new DbField(GbzGruppeTable.BEZEICHNUNG, gbz);

	private final DbTable ac = new DbTable(AppcodeTable._TN, "ac");
	private final DbField acTbl = new DbField(AppcodeTable.TAB_PRFX, ac);
	private final DbField acCol = new DbField(AppcodeTable.COL_NAME, ac);
	private final DbField acLng = new DbField(AppcodeTable.CD_SPRACHE, ac);
	private final DbField acKey = new DbField(AppcodeTable.CD_HARD, ac);
	private final DbField acKuerzel = new DbField(AppcodeTable.CD_SOFT, ac);
	private final DbField acName = new DbField(AppcodeTable.BEZEICHNUNG, ac);

	private String createQuery() {
		DateFormat fmt = new SimpleDateFormat("''yyyy-MM-dd''");
		Calendar cal = Calendar.getInstance();
		cal.add(Calendar.DATE, -7 - (cal.get(Calendar.DAY_OF_WEEK) + 5) % 7);
		String d1 = fmt.format(cal.getTime());
		cal.add(Calendar.DATE, 7);
		String d2 = fmt.format(cal.getTime());

		StringBuilder sql = new StringBuilder();
		sql.append(Query.select(prjBasis, prjKomm, prjBez, aAufDat, aId, gbzKey, gbzName, acKuerzel, acName)).append(", count(*) as AnzPos");

		sql.append(" FROM ").append(prj.def());
		sql.append(" JOIN ").append(a.def()) //
			.append(" ON ").append(a_prjKomm).append("=").append(prjKomm);
		sql.append(" JOIN ").append(pos.def()) //
			.append(" ON ").append(pos_aId).append("=").append(aId);
		sql.append(" LEFT JOIN ").append(gbz.def()) //
			.append(" ON ").append(gbzKey).append("=").append(prjID) //
			.append(" AND ").append(gbzArt).append("='900'") //
			.append(" AND ").append(gbzGse).append("='2000'");
		sql.append(" LEFT JOIN ").append(ac.def()) //
			.append(" ON ").append(acKey).append("=").append(prjOB) //
			.append(" AND ").append(acTbl).append("='LPAU'") //
			.append(" AND ").append(acCol).append("='PFAKTPERCD'") //
			.append(" AND ").append(acLng).append("='001'");

		sql.append(" WHERE ").append(aAWA).append("='115'");
		sql.append(" AND ").append(aGse).append("='2501'");
		sql.append(" AND ").append(aAufDat).append(" between ").append(d1).append(" AND ").append(d2);
		sql.append(" AND ").append(posStatus).append(" != '099'");
		sql.append(" AND ").append(posProdNr).append(" not in ('la-mo','la-fa1','la-fa2','la-fa3')");

		sql.append(Query.group(prjBasis, prjKomm, prjBez, aAufDat, aId, gbzKey, gbzName, acKuerzel, acName));
		sql.append(" HAVING count(*) >= 5");
		return sql.toString();
	}

	@Override
	public Collection<EMail> execute(ReportingServerConfig config, AbstractSoftwareComponent server) throws IOException {
		Collection<EMail> emaillist = new ArrayList<>();
		new TransactionContext().load(new LWContextSource(DatabaseSpecifier.get("VERTRIEB")) {
			@Override
			protected void loadImpl(TransactionContext context) throws SQLException {
				StringBuilder msg = new StringBuilder();
				msg.append("<html><body style=\"font-family: Verdana; \">");
				msg.append("Guten Tag") //
						.append("<br><br>Anbei die Folgeaufträge-Liste vom ").append(DateFormat.getDateInstance().format(new java.util.Date())) //
						.append("<br><br>Freundliche Grüsse");

				Workbook workbook = POIUtil.createWorkbook();
				Sheet sht = workbook.createSheet();
				AtomicInteger rowIndex = new AtomicInteger();

				CellStyle headerCellStyle = new ExcelCellStyle(workbook) //
					.setBackgroundColor(IndexedColors.GREY_25_PERCENT) //
					.setBorderLine(BorderLine.NORMAL) //
					.getStyle();
				CellStyle dataCellStyle = new ExcelCellStyle(workbook) //
					.setVertAlign(VertAlign.TOP) //
					.getStyle();
				CellStyle dateCellStyle = new ExcelCellStyle(workbook) //
					.setVertAlign(VertAlign.TOP) //
					.setDateFormat(workbook, "dd.MM.yyyy") //
					.getStyle();
				CellStyle numberCellStyle = dataCellStyle;

				{
					Row row = sht.createRow(rowIndex.getAndIncrement());
					for (int columnIndex = 0; columnIndex < CtrlCol.COUNT.get(); columnIndex++) {
						Cell cell = row.createCell(columnIndex);
						if (columnIndex == CtrlCol.BASIS) {
							String value = "Basis";
							cell.setCellValue(value);
							cell.setCellStyle(headerCellStyle);
						} else if (columnIndex == CtrlCol.KOMM) {
							String value = "Kommission";
							cell.setCellValue(value);
							cell.setCellStyle(headerCellStyle);
						} else if (columnIndex == CtrlCol.BAUSTELLENBEZEICHUNG) {
							String value = "Baustellenbezeichnung";
							cell.setCellValue(value);
							cell.setCellStyle(headerCellStyle);
						} else if (columnIndex == CtrlCol.DATUM) {
							String value = "Auftragsdatum";
							cell.setCellValue(value);
							cell.setCellStyle(headerCellStyle);
						} else if (columnIndex == CtrlCol.ID) {
							String value = "ID";
							cell.setCellValue(value);
							cell.setCellStyle(headerCellStyle);
						} else if (columnIndex == CtrlCol.OB) {
							String value = "OB";
							cell.setCellValue(value);
							cell.setCellStyle(headerCellStyle);
						} else if (columnIndex == CtrlCol.ANZAHL) {
							String value = "Anzahl";
							cell.setCellValue(value);
							cell.setCellStyle(headerCellStyle);
						}
					}
				}
				doQuery(createQuery(), null, rst -> {
					Row row = sht.createRow(rowIndex.getAndIncrement());
					for (int columnIndex = 0; columnIndex < CtrlCol.COUNT.get(); columnIndex++) {
						Cell cell = row.createCell(columnIndex);
						if (columnIndex == CtrlCol.BASIS) {
							String value = rst.getString(prjBasis.alias);
							cell.setCellValue(value);
							cell.setCellStyle(dataCellStyle);
						} else if (columnIndex == CtrlCol.KOMM) {
							String value = rst.getString(prjKomm.alias);
							cell.setCellValue(value);
							cell.setCellStyle(dataCellStyle);
						} else if (columnIndex == CtrlCol.BAUSTELLENBEZEICHUNG) {
							String value = rst.getString(prjBez.alias);
							cell.setCellValue(value);
							cell.setCellStyle(dataCellStyle);
						} else if (columnIndex == CtrlCol.DATUM) {
							Date value = rst.getDate(aAufDat.alias);
							cell.setCellValue(value);
							cell.setCellStyle(dateCellStyle);
						} else if (columnIndex == CtrlCol.ID) {
							String value = rst.getString(gbzName.alias) + " (" + rst.getString(gbzKey.alias) + ")";
							cell.setCellValue(value);
							cell.setCellStyle(dataCellStyle);
						} else if (columnIndex == CtrlCol.OB) {
							String value = rst.getString(acName.alias) + " (" + rst.getString(acKuerzel.alias) + ")";
							cell.setCellValue(value);
							cell.setCellStyle(dataCellStyle);
						} else if (columnIndex == CtrlCol.ANZAHL) {
							Integer value = rst.getInteger("AnzPos");
							cell.setCellValue(value);
							cell.setCellStyle(numberCellStyle);
						}

					}
				});

				// Column Width
				for (int columnIndex = 0; columnIndex < CtrlCol.COUNT.get(); columnIndex++) {
					int columnWidthChars = 0;
					if (columnIndex == CtrlCol.BASIS) {
						columnWidthChars = 9;
					} else if (columnIndex == CtrlCol.KOMM) {
						columnWidthChars = 12;
					} else if (columnIndex == CtrlCol.BAUSTELLENBEZEICHUNG) {
						columnWidthChars = 77;
					} else if (columnIndex == CtrlCol.DATUM) {
						columnWidthChars = 14;
					} else if (columnIndex == CtrlCol.ID) {
						columnWidthChars = 40;
					} else if (columnIndex == CtrlCol.OB) {
						columnWidthChars = 40;
					} else if (columnIndex == CtrlCol.ANZAHL) {
						columnWidthChars = 7;
					}
					sht.setColumnWidth(columnIndex, 256 * columnWidthChars);
				}

				// Save Excel-File
				File folder = new File(config.getExportPath());
				File file = new File(folder, String.format("Folgeauftrag vom %s.xlsx", DateFormat.getDateInstance().format(new java.util.Date())));
				try (OutputStream out = new FileOutputStream(file)) {
					workbook.write(out);
				} catch (IOException e) {
					Logger.error(e);
				}

				// Email
				emaillist.addAll(createEmail(config.getEmailAddresses(), msg.toString(), file));
			}
		});
		return emaillist;
	}

	private Collection<EMail> createEmail(Collection<String> emails, String text, File file) {
		Collection<EMail> list = new ArrayList<>();
		if (file.isFile() && emails != null) {
			for (String email : emails) {
				EMail mail = new EMail();
				mail.addTo(email);
				mail.setFromName("HEAG Reporting");
				mail.setFromAddress("<EMAIL>");
				mail.setReplyTo("<EMAIL>");
				mail.setSubject("Controlling Münchwilen Bereich Ausführung");
				mail.setPlainText(text);
				try {
					mail.addAttachment(new EMailAttachment(FileItem.create(file)));
				} catch (Exception e) {
					Logger.error(e);
				}
				list.add(mail);
			}
		}
		return list;
	}

	public static void main(String[] args) {
		System.out.println(new FolgeauftragReporting().createQuery());
	}

}

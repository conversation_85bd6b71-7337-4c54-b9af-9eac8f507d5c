package ch.eisenring.reporting.task;

import ch.eisenring.app.shared.AbstractSoftwareComponent;
import ch.eisenring.app.shared.ServiceNotFoundException;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.dms.service.DMSServiceException;
import ch.eisenring.dms.service.proxy.DMSDocumentHandle;
import ch.eisenring.email.EMail;
import ch.eisenring.reporting.server.ReportingServerConfig;
import ch.eisenring.reporting.util.DMSHelper;
import org.apache.poi.ss.usermodel.Workbook;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.Collection;
import java.util.Date;

/**
 * Ist eine Zusammenfassung mehrer Abfragen in einer Liste.
 * Basis ist der Report Endmontage pro Küche in den Dispo-Client Werkzeugen @ReportEndmontagenRequestHandler.java
 */
public class ProjektleiterUebersicht implements ReportingTask {

    private static String REPORT_NAME = "KM_EM_Liste";

    private AbstractSoftwareComponent server;
    private ReportingServerConfig config;
    private Date dateFrom = null;
    private Date dateUpto = null;

    @Override
    public Collection<EMail> execute(ReportingServerConfig config, AbstractSoftwareComponent server) throws IOException, DMSServiceException, ServiceNotFoundException {
        this.config = config;
        this.server = server;

        try {


            return createEmail(null);
        } catch (Exception e) {
            Logger.error("Die KM_EM_Liste konnte nicht erstellt werden: " + e.getMessage());
            Logger.error(e);
            e.printStackTrace();
            throw e;
        }
    }


    private DMSDocumentHandle saveReport(Workbook workbook) throws IOException, DMSServiceException, ServiceNotFoundException {
        Integer folderId = config.getInteger("Folder", Integer.MIN_VALUE, Integer.MAX_VALUE, 0);
        File folder = new File(config.getExportPath());
        File file = new File(folder, new StringBuilder().append(REPORT_NAME).append(".xlsx").toString());

        try (OutputStream out = new FileOutputStream(file)) {
            workbook.write(out);
        } catch (IOException e) {
            Logger.error("Die KM_EM_Liste konnte nicht erstellt werden: " + e.getMessage());
            Logger.error(e);
            throw e;
        }

        try {
            DMSDocumentHandle documentHandle = DMSHelper.saveReport(server, file, folderId);
            return documentHandle;
        } catch (ServiceNotFoundException | DMSServiceException | IOException e) {
            Logger.error(e);
            Logger.error("Die KM_EM_Liste konnte nicht im DMS gespeichert werden: " + e.getMessage());
            throw e;
        } finally {
            // file.delete(); // Aktuell nicht gelöscht, falls es Probleme mit dem File gibt.
        }
    }

    private Collection<EMail> createEmail(DMSDocumentHandle documentHandle) {
        StringBuilder body = new StringBuilder();
        body.append("<html><body>");
        body.append("<p>Sehr geehrte Damen und Herren</p>");
        body.append("<p>Die Auswertung <a href=\"EisenringDMS://Goto/ObjectId/" + documentHandle.getPKValue() + "\"> KM_EM_Liste</a> wurde erstellt und im DMS aktualisiert</p>");
        body.append("</body></html>");
        // Email
        Collection<EMail> emailList = new ch.eisenring.core.collections.impl.ArrayList<>();
        Collection<String> emailAddresses = config.getEmailAddresses();
        if (emailAddresses != null) {
            for (String email : emailAddresses) {
                try {
                    EMail mail = new EMail();
                    mail.setFromName("HEAG Reporting");
                    mail.setFromAddress("<EMAIL>");
                    mail.setReplyTo("<EMAIL>");
                    mail.setSubject("Auswertung KM_EM_Liste");
                    mail.setPlainText(body.toString());
                    mail.addTo(email);
                    emailList.add(mail);
                } catch (Exception e) {
                    Logger.error(e);
                }
            }
        }
        return emailList;
    }


}
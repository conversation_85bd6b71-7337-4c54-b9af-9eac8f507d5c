package ch.eisenring.reporting.task;

import ch.eisenring.app.shared.AbstractSoftwareComponent;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.io.file.FileItem;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.core.poi.POIUtil;
import ch.eisenring.email.EMail;
import ch.eisenring.email.EMailAttachment;
import ch.eisenring.jdbc.JDBCResultSet;
import ch.eisenring.logiware.LWContextSource;
import ch.eisenring.model.TransactionContext;
import ch.eisenring.model.engine.jdbc.DatabaseSpecifier;
import ch.eisenring.reporting.server.ReportingServerConfig;
import ch.eisenring.reporting.util.AppenderMap;
import ch.eisenring.reporting.util.DatabaseConstants;
import ch.eisenring.reporting.util.DatabaseConstants.BasisDboSchema.GbzGruppeTable;
import ch.eisenring.reporting.util.DatabaseConstants.VertriebDboSchema.AuftragTable;
import ch.eisenring.reporting.util.DatabaseConstants.VertriebDboSchema.AuftragsKopfZusatzTable;
import ch.eisenring.reporting.util.DatabaseConstants.VertriebDboSchema.PAuftragTable;
import ch.eisenring.reporting.util.ExcelHelper.ExcelCellStyle;
import ch.eisenring.reporting.util.ExcelHelper.ExcelCellStyle.BorderLine;
import ch.eisenring.reporting.util.ExcelHelper.ExcelCellStyle.HorizAlign;
import ch.eisenring.reporting.util.QueryObjects.DbField;
import ch.eisenring.reporting.util.QueryObjects.DbTable;
import ch.eisenring.reporting.util.QueryObjects.Query;
import org.apache.poi.ss.usermodel.*;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.sql.SQLException;
import java.text.DateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

import static ch.eisenring.reporting.util.CollectionHelper.concat;
import static ch.eisenring.reporting.util.DMSHelper.hasMassaufnahme;
import static ch.eisenring.reporting.util.FrontFarbenHelper.getFrontAusfuehrungen;

public class ProduktionsplanungReporting implements ReportingTask {

    AbstractSoftwareComponent server;

    public class Result {

        private final Map<String, Lieferant> lieferanten = new TreeMap<>();

        public Lieferant getLieferant(String kuechenLieferant) {
            Lieferant lieferant = lieferanten.get(kuechenLieferant);
            if (lieferant == null) {
                lieferant = new Lieferant(kuechenLieferant);
                lieferanten.put(kuechenLieferant, lieferant);
            }
            return lieferant;
        }

        public Collection<Lieferant> getLieferanten() {
            return new ArrayList<>(lieferanten.values());
        }
    }

    private final Map<String, String> lieferantenMap = new AppenderMap<String, String>() //
            .add("010", "Veriset") //
            .add("020", "Sitag") //
            .add("021", "Kuhlmann") //
            .add("030", "Pronorm") //
            .add("040", "Poggenpohl") //
            .add("050", "Schreinerküchen") //
            .add("060", "Waschtürme") //
            .add("070", "Waschküchen") //
            .add("019", "Elbau")
            ;

    private Collection<String> activeLieferant;

    private final Map<String, String> objektArtMap = new AppenderMap<String, String>() //
            .add("aaa", "Eigentumsobjekt") //
            .add("bbb", "Einzelobjekt") //
            .add("ccc", "Einzel (Laufkundschaft)") //
            .add("ddd", "Mietobjekt") //
            ;
    private final Map<String, String> bauArtenMap = new AppenderMap<String, String>() //
            .add("AAA", "A Neubau") //
            .add("BBB", "B Umbau bewohnt") //
            .add("CCC", "C Umbau unbewohnt") //
            ;

    public class Lieferant {

        private final String lieferantenNummer;

        public Lieferant(String lieferantenCode) {
            this.lieferantenNummer = lieferantenCode;
        }

        public String getLieferantenNummer() {
            return lieferantenNummer;
        }

        public String getLieferantenName() {
            return lieferantenMap.get(lieferantenNummer);
        }

        private final Comparator<Kommission> kommissionComp = (k1, k2) -> {
            int pos;
            String v1 = k1.getProjektBezeichnung("-");
            pos = v1.indexOf(' ');
            if (pos > 0) {
                v1 = v1.substring(0, pos);
            }
            String v2 = k2.getProjektBezeichnung("-");
            pos = v2.indexOf(' ');
            if (pos > 0) {
                v2 = v2.substring(0, pos);
            }
            int cmp = v1.compareTo(v2);
            if (cmp != 0) {
                return cmp;
            }
            cmp = k1.getProjektNummer().compareTo(k2.getProjektNummer());
            if (cmp != 0) {
                return cmp;
            }
            return 0;
        };

        private final Map<String, Kommission> kommissions = new HashMap<>();

        public Kommission getKommission(String pAuftrNr) {
            Kommission kommission = kommissions.get(pAuftrNr);
            if (kommission == null) {
                kommission = new Kommission(pAuftrNr);
                kommissions.put(pAuftrNr, kommission);
            }
            return kommission;
        }

        public Collection<Kommission> getKommissionen() {
            List<Kommission> list = new ArrayList<>(kommissions.values());
            list.sort(kommissionComp);
            return list;
        }
    }

    public class Kommission {

        private final String projektNummer;
        private String projektObjektStatus;
        private String bemusterer;
        private String statusMassaufnahme;
        private String awa;

        /**
         * Constructor
         *
         * @param projektNummer aka Kommission
         */
        public Kommission(String projektNummer) {
            this.projektNummer = projektNummer;
        }

        public String getProjektNummer() {
            return projektNummer;
        }

        public void setProjektObjektStatus(String projektObjektStatus) {
            this.projektObjektStatus = projektObjektStatus;
        }

        public String getProjektObjektStatus() {
            return projektObjektStatus;
        }

        public void setAwa(String awa) {
            this.awa = awa;
        }

        public String getAwa() {
            return awa;
        }

        public void setBemusterer(String bemusterer) {
            this.bemusterer = bemusterer;
        }

        public String getBemusterer() {
            return bemusterer;
        }

        public void setStatusMassaufnahme(String statusMassaufnahme) {
            this.statusMassaufnahme = statusMassaufnahme;
        }

        public String getStatusMassaufnahme() {
            return statusMassaufnahme;
        }

        private final Collection<String> basisProjektNummers = new HashSet<>();

        public void addBasisProjektNummer(String basisNr) {
            if (basisNr != null) {
                this.basisProjektNummers.add(basisNr);
            }
        }

        public String getBasisProjektNummer(String delim) {
            return concat(basisProjektNummers, e -> e, delim);
        }

        private final Collection<String> projektBezeichnungen = new HashSet<>();

        public void addProjektBezeichnung(String pbez) {
            if (pbez != null) {
                this.projektBezeichnungen.add(pbez);
            }
        }

        public String getProjektBezeichnung(String delim) {
            return concat(projektBezeichnungen, e -> e, delim);
        }

        private final Collection<String> frontFarben = new HashSet<>();

        public void addFrontFarbe(String farbe) {
            if (farbe != null) {
                frontFarben.add(farbe);
            }
        }

        public String getFrontFarbe(String delim) {
            return concat(frontFarben, e -> e, delim);
        }

        private final Collection<java.util.Date> wunschdatum = new HashSet<>();

        public void addWunschDatum(java.util.Date lieTag) {
            if (lieTag != null) {
                wunschdatum.add(lieTag);
            }
        }

        public String getWunschDatum(String delim) {
            return concat(wunschdatum, e -> DateFormat.getDateInstance().format(e), delim);
        }

        public Integer getKW() {
            if (wunschdatum.size() == 1) {
                Calendar cal = Calendar.getInstance();
                cal.setTime(wunschdatum.iterator().next());
                return cal.get(Calendar.WEEK_OF_YEAR);
            }
            return null;
        }

        private final Collection<Boolean> bestellt = new HashSet<>();

        public void addBestellt(boolean b) {
            bestellt.add(b);
        }

        public String getBestellt(String delim) {
            return concat(bestellt, b -> b ? "Ja" : "Nein", delim);
        }

        private final Collection<String> objektArten = new HashSet<>();

        private final Collection<String> bauArten = new HashSet<>();

        public void addObjektArt(String code) {
            if (code != null) {
                objektArten.add(code);
            }
        }

        public void addBauArt(String code) {
            if (code != null) {
                bauArten.add(code);
            }
        }

        public String getObjektArt(String delim) {
            return concat(objektArten, e -> objektArtMap.get(e), delim);
        }

        public String getBauArt(String delim) {
            return concat(bauArten, e -> bauArtenMap.get(e), delim);
        }

        private final Collection<String> idNames = new HashSet<>();

        public void addInnendienst(String idName) {
            if (idName != null) {
                idNames.add(idName);
            }
        }

        public Object getInnendienst(String delim) {
            return concat(idNames, e -> e, delim);
        }

    }

    private final DbTable P = new DbTable(PAuftragTable._TN, "P");
    private final DbField pBasisNr = new DbField(PAuftragTable.P_AUFTRNR_B, P);
    private final DbField pPrjNr = new DbField(PAuftragTable.P_AUFTR_NR, P);
    private final DbField pObjektStatus = new DbField(PAuftragTable.OBJEKT_STATUS, P);
    private final DbField pGsePA = new DbField(PAuftragTable.ID_GSE_PA, P);
    private final DbField pBez = new DbField(PAuftragTable.P_BEZ, P);
    private final DbField pStatus = new DbField(PAuftragTable.STATUS, P);
    private final DbField pInnendienst = new DbField(PAuftragTable.VERT1, P);

    private final DbTable A = new DbTable(AuftragTable._TN, "A");
    private final DbField aPrjNr = new DbField(AuftragTable.P_AUFTR_NR, A);
    private final DbField aGsePA = new DbField(AuftragTable.ID_GSE_PA, A);
    private final DbField aAuftrNr = new DbField(AuftragTable.AUFTR_NR, A);
    private final DbField aGseK = new DbField(AuftragTable.ID_GSE, A);
    private final DbField aAwa = new DbField(AuftragTable.ABW_ART, A);
    private final DbField aWunschDatum = new DbField(AuftragTable.DAT_WU, A);
    private final DbField aStatus = new DbField(AuftragTable.STATUS, A);
    private final DbField aBesteller = new DbField(AuftragTable.VERT1, A);
    private final DbField aBemusterer = new DbField(AuftragTable.VERT2, A);

    private final DbTable K = new DbTable(AuftragsKopfZusatzTable._TN, "K");
    private final DbField kAuftrNr = new DbField(AuftragsKopfZusatzTable.AUFTR_NR, K);
    private final DbField kGseK = new DbField(AuftragsKopfZusatzTable.ID_GSE, K);
    private final DbField kKuechenLieferant = new DbField(AuftragsKopfZusatzTable.KUECHEN, K);
    private final DbField kBestellung = new DbField(AuftragsKopfZusatzTable.A_ZUSATZ_P_STK, K);
    private final DbField kObjektArt = new DbField(AuftragsKopfZusatzTable.SCH_BLOCK2, K);
    private final DbField kBauArt = new DbField(AuftragsKopfZusatzTable.SCH_BLOCK1, K);

    private final DbTable IP = new DbTable(GbzGruppeTable._TN, "IP");
    private final DbField ipGrArt = new DbField(GbzGruppeTable.GBZ_GR_ART, IP);
    private final DbField ipGse = new DbField(GbzGruppeTable.ID_GSE, IP);
    private final DbField ipGruppe = new DbField(GbzGruppeTable.GRUPPE, IP);
    private final DbField ipBezeichnung = new DbField(GbzGruppeTable.BEZEICHNUNG, IP);

    private final DbTable IA = new DbTable(GbzGruppeTable._TN, "IA");
    private final DbField iaGrArt = new DbField(GbzGruppeTable.GBZ_GR_ART, IA);
    private final DbField iaGse = new DbField(GbzGruppeTable.ID_GSE, IA);
    private final DbField iaGruppe = new DbField(GbzGruppeTable.GRUPPE, IA);

    private final DbTable BEMA = new DbTable(GbzGruppeTable._TN, "BEMA");
    private final DbField bemaGrArt = new DbField(GbzGruppeTable.GBZ_GR_ART, BEMA);
    private final DbField bemaGse = new DbField(GbzGruppeTable.ID_GSE, BEMA);
    private final DbField bemaGruppe = new DbField(GbzGruppeTable.GRUPPE, BEMA);
    private final DbField bestellerBezeichnung = new DbField(GbzGruppeTable.BEZEICHNUNG, IA);
    private final DbField bemusterBezeichnung = new DbField(GbzGruppeTable.BEZEICHNUNG, BEMA);

    private final DbTable AC = new DbTable(DatabaseConstants.BasisDboSchema.AppcodeTable._TN, "AC");
    private final DbField acCode = new DbField(DatabaseConstants.BasisDboSchema.AppcodeTable.CD_HARD, AC);
    private final DbField acSprache = new DbField(DatabaseConstants.BasisDboSchema.AppcodeTable.CD_SPRACHE, AC);
    private final DbField acTabPrfx = new DbField(DatabaseConstants.BasisDboSchema.AppcodeTable.TAB_PRFX, AC);
    private final DbField acColName = new DbField(DatabaseConstants.BasisDboSchema.AppcodeTable.COL_NAME, AC);
    private final DbField acBezeichnung = new DbField(DatabaseConstants.BasisDboSchema.AppcodeTable.BEZEICHNUNG, AC);


    private String createQuery(Integer durationWeeks) {

        String timeFrameCondition = getTimeFrameCondition(durationWeeks);


        return Query.select(pPrjNr, pBasisNr, pBez, pStatus, pObjektStatus, acBezeichnung, ipBezeichnung, aWunschDatum, aAwa, bestellerBezeichnung, bemusterBezeichnung, kKuechenLieferant, kBestellung, kObjektArt, kBauArt).append(", K.*") //
                .append(" FROM ").append(P.def()) //

                .append(" JOIN ").append(A.def())
                .append(" ON ").append(aPrjNr).append("=").append(pPrjNr)
                .append(" AND ").append(aGsePA).append("=").append(pGsePA) //

                .append(" JOIN ").append(K.def())
                .append(" ON ").append(kAuftrNr).append("=").append(aAuftrNr)
                .append(" AND ").append(kGseK).append("=").append(aGseK) //

                .append(" LEFT JOIN ").append(IP.def())
                .append(" ON ").append(ipGruppe).append("=").append(pInnendienst) //
                .append(" AND ").append(ipGrArt).append("='900'") //
                .append(" AND ").append(ipGse).append("='2000'") //

                .append(" LEFT JOIN ").append(IA.def())
                .append(" ON ").append(iaGruppe).append("=").append(aBesteller) //
                .append(" AND ").append(iaGrArt).append("='900'") //
                .append(" AND ").append(iaGse).append("='2000'") //

                .append(" LEFT JOIN ").append(BEMA.def())
                .append(" ON ").append(bemaGruppe).append("=").append(aBemusterer) //
                .append(" AND ").append(bemaGrArt).append("='900'") //
                .append(" AND ").append(bemaGse).append("='2000'") //

                .append(" LEFT JOIN ").append(AC.def())
                .append(" ON ").append(pObjektStatus).append("=").append(acCode) //
                .append(" AND ").append(acSprache).append("='001'") //
                .append(" AND ").append(acTabPrfx).append("='LPAU'") //
                .append(" AND ").append(acColName).append("='OBJEKTSTATUS'") //

                .append(" WHERE ").append(aAwa).append(" in ('001', '135')") //
                .append(" AND ").append(pStatus).append(" != '099'") //
                .append(" AND ").append(aStatus).append(" != '099'") //
                .append(timeFrameCondition) //
                .append(" AND ").append(kKuechenLieferant).append(" in (").append(concat(activeLieferant, e -> "'" + e + "'", ", ")).append(")") //
                .toString();
    }

    private String getTimeFrameCondition(Integer durationWeeks) {
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DATE, 14 - (cal.get(Calendar.DAY_OF_WEEK) + 5) % 7);
        String d1 = String.format("'%tF'", cal.getTime());
        StringMaker stringMaker = StringMaker.obtain();
        stringMaker.append(" AND ");
        stringMaker.append(d1);
        stringMaker.append(" <= ");
        stringMaker.append(aWunschDatum);
        if (durationWeeks != -1) {
            cal.add(Calendar.DATE, durationWeeks * 7);
            String d2 = String.format("'%tF'", cal.getTime());
            stringMaker.append(" AND ");
            stringMaker.append(aWunschDatum);
            stringMaker.append(" < ");
            stringMaker.append(d2);
        }
        return stringMaker.release();
    }

    @Override
    public Collection<EMail> execute(ReportingServerConfig config, AbstractSoftwareComponent server) throws IOException {
        this.server = server;
        Result result = new Result();
        activeLieferant = config.getStrings("Lieferanten");
        new TransactionContext().load(new LWContextSource(DatabaseSpecifier.get("VERTRIEB")) {
            @Override
            protected void loadImpl(TransactionContext context) throws SQLException {
                doQuery(createQuery(config.getInteger("Weeks", 1, 99, -1)), null, rst -> {
                    String kom = rst.getString(pPrjNr.alias);
                    Lieferant lieferant = result.getLieferant(rst.getString(kKuechenLieferant.alias));
                    Kommission kommission = lieferant.getKommission(kom);
                    kommission.setAwa(rst.getString(aAwa.alias));
                    kommission.addBasisProjektNummer(rst.getString(pBasisNr.alias));
                    kommission.addProjektBezeichnung(rst.getString(pBez.alias));
                    kommission.setProjektObjektStatus(rst.getString(acBezeichnung.alias));
                    kommission.addWunschDatum(rst.getDate(aWunschDatum.alias));
                    kommission.addBestellt(rst.getInteger(kBestellung.alias) != null);
                    kommission.addObjektArt(rst.getString(kObjektArt.alias));
                    kommission.addBauArt(rst.getString(kBauArt.alias));
                    kommission.addInnendienst(rst.getString(ipBezeichnung.alias));
                    kommission.addInnendienst(rst.getString(bestellerBezeichnung.alias));
                    kommission.setBemusterer(rst.getString(bemusterBezeichnung.alias));
                    addFrontFarben(kommission, rst);
                });
            }
        });

        // Check Massaufnahme
        addStatusMassaufnahme(result);

        // Save Excel-File
        File folder = new File(config.getExportPath());
        File file = new File(folder, String.format("%1$s_%2$tF-%2$tH%2$tM.xlsx", getClass().getSimpleName(), System.currentTimeMillis()));
        try (OutputStream out = new FileOutputStream(file)) {
            createExcel(result).write(out);
        } catch (Exception e) {
            Logger.error(e);
        }

        // Email
        Collection<EMail> emailList = new ArrayList<>();
        if (file.isFile() && config.getEmailAddresses() != null) {
            for (String email : config.getEmailAddresses()) {
                try {
                    EMail mail = new EMail();
                    mail.addTo(email);
                    mail.setFromName("HEAG Reporting");
                    mail.setFromAddress("<EMAIL>");
                    mail.setReplyTo("<EMAIL>");
                    mail.setSubject("Produktionsplanung mit Lieferanten - Auswertung aus LogiWare");
                    mail.setPlainText(createHtmlBody());
                    mail.addAttachment(new EMailAttachment(FileItem.create(file)));
                    emailList.add(mail);
                } catch (Exception e) {
                    Logger.error(e);
                }
            }
        }
        return emailList;
    }

    private void addStatusMassaufnahme(Result result) {
        for (Lieferant lieferant : result.getLieferanten()) {
            for (Kommission kommission : lieferant.getKommissionen()) {
                kommission.setStatusMassaufnahme(hasMassaufnahme(kommission.getProjektNummer(), server) ? "Ja" : "Nein");
            }
        }
    }

    /**
     * Extracts the Front-colors from the Deckblatt properties using the same logic as in OUSDetailPanel.java
     *
     * @param kommission
     * @param rst
     * @throws SQLException
     */
    public static void addFrontFarben(ProduktionsplanungReporting.Kommission kommission, JDBCResultSet rst) throws SQLException {
        Set<String> frontAusfuehrungenDistinct = getFrontAusfuehrungen(rst);
        for (String frontAusfuehrung : frontAusfuehrungenDistinct) {
            kommission.addFrontFarbe(frontAusfuehrung);
        }
    }

    public interface ProdCol {
        AtomicInteger COUNT = new AtomicInteger();
        int projektNummer = COUNT.getAndIncrement();
        int abwicklungsArt = COUNT.getAndIncrement();
        int basisBrojektNummer = COUNT.getAndIncrement();
        int projektBezeichnung = COUNT.getAndIncrement();
        int projektObjektStatus = COUNT.getAndIncrement();
        int besteller = COUNT.getAndIncrement();
        int bemusterer = COUNT.getAndIncrement();
        int frontFarbe = COUNT.getAndIncrement();
        int objektArt = COUNT.getAndIncrement();
        int bauArt = COUNT.getAndIncrement();
        int wunschDatum = COUNT.getAndIncrement();
        int wunschWoche = COUNT.getAndIncrement();
        int bestellt = COUNT.getAndIncrement();
        int statusMassaufnahme = COUNT.getAndIncrement();
    }

    public String getColumnName(int columnIndex) {
        if (columnIndex == ProdCol.projektNummer) {
            return "Kommission";
        } else if (columnIndex == ProdCol.abwicklungsArt) {
            return "AWA";
        } else if (columnIndex == ProdCol.basisBrojektNummer) {
            return "Basis";
        } else if (columnIndex == ProdCol.projektBezeichnung) {
            return "Projekt-Bez.";
        } else if (columnIndex == ProdCol.projektObjektStatus) {
            return "Objekt-Status";
        } else if (columnIndex == ProdCol.frontFarbe) {
            return "Frontfarbe";
        } else if (columnIndex == ProdCol.wunschDatum) {
            return "Wunschdatum";
        } else if (columnIndex == ProdCol.bestellt) {
            return "Bestellt";
        } else if (columnIndex == ProdCol.wunschWoche) {
            return "WuKW";
        } else if (columnIndex == ProdCol.objektArt) {
            return "Objekt/Einzelküche";
        } else if (columnIndex == ProdCol.bauArt) {
            return "Bauart";
        } else if (columnIndex == ProdCol.besteller) {
            return "Besteller";
        } else if (columnIndex == ProdCol.bemusterer) {
            return "Bemusterer";
        } else if (columnIndex == ProdCol.statusMassaufnahme) {
            return "Massaufnahme";
        } else {
            return null;
        }
    }

    public Object getCellValue(Kommission kommission, int columnIndex, String delim) {
        if (columnIndex == ProdCol.projektNummer) {
            return kommission.getProjektNummer();
        } else if (columnIndex == ProdCol.abwicklungsArt) {
            return kommission.getAwa();
        } else if (columnIndex == ProdCol.basisBrojektNummer) {
            return kommission.getBasisProjektNummer(delim);
        } else if (columnIndex == ProdCol.projektBezeichnung) {
            return kommission.getProjektBezeichnung(delim).replaceAll("[\r\n]+", " ");
        } else if (columnIndex == ProdCol.projektObjektStatus) {
            return kommission.getProjektObjektStatus();
        } else if (columnIndex == ProdCol.frontFarbe) {
            return kommission.getFrontFarbe(delim).replace("ß", "ss");
        } else if (columnIndex == ProdCol.wunschDatum) {
            return kommission.getWunschDatum(delim);
        } else if (columnIndex == ProdCol.bestellt) {
            return kommission.getBestellt(delim);
        } else if (columnIndex == ProdCol.wunschWoche) {
            return kommission.getKW();
        } else if (columnIndex == ProdCol.objektArt) {
            return kommission.getObjektArt(delim);
        } else if (columnIndex == ProdCol.bauArt) {
            return kommission.getBauArt(delim);
        } else if (columnIndex == ProdCol.besteller) {
            return kommission.getInnendienst(delim);
        } else if (columnIndex == ProdCol.bemusterer) {
            return kommission.getBemusterer();
        } else if (columnIndex == ProdCol.statusMassaufnahme) {
            return kommission.getStatusMassaufnahme();
        } else {
            return null;
        }
    }

    public int getColumnCharWidth(int columnIndex) {
        if (columnIndex == ProdCol.projektNummer) {
            return 12;
        } else if (columnIndex == ProdCol.abwicklungsArt) {
            return 5;
        } else if (columnIndex == ProdCol.basisBrojektNummer) {
            return 7;
        } else if (columnIndex == ProdCol.projektBezeichnung) {
            return 90;
        } else if (columnIndex == ProdCol.projektObjektStatus) {
            return 24;
        } else if (columnIndex == ProdCol.frontFarbe) {
            return 40;
        } else if (columnIndex == ProdCol.bestellt) {
            return 10;
        } else if (columnIndex == ProdCol.objektArt) {
            return 23;
        } else if (columnIndex == ProdCol.bauArt) {
            return 20;
        } else if (columnIndex == ProdCol.wunschDatum) {
            return 18;
        } else if (columnIndex == ProdCol.wunschWoche) {
            return 10;
        } else if (columnIndex == ProdCol.besteller) {
            return 35;
        } else if (columnIndex == ProdCol.bemusterer) {
            return 35;
        } else if (columnIndex == ProdCol.statusMassaufnahme) {
            return 15;
        } else {
            return 0;
        }
    }

    private String createHtmlBody() {
        return "<html><body style=\"font-family: Verdana; \">" +
                "<p>Sehr geehrte Damen und Herren<p>" +
                "<p>Anbei die Produktionsplanung mit Lieferanten - Auswertung aus LogiWare." +
                "<br>Siehe Anhang</p>" +
                "<h1>" + getClass().getSimpleName() + "</h1>" +
                "</body></html>";
    }

    private Workbook createExcel(Result result) {
        Workbook workbook = POIUtil.createWorkbook();

        CellStyle headerStyle = new ExcelCellStyle(workbook) //
                .setBackgroundColor(IndexedColors.YELLOW) //
                .setBorderLine(BorderLine.NORMAL) //
                .setHorizAlign(HorizAlign.CENTER) //
                .setFontBold(true) //
                .getStyle();

        CellStyle dataStyle = workbook.createCellStyle();
        dataStyle.setWrapText(true);

        for (Lieferant lieferant : result.getLieferanten()) {
            Sheet sht = workbook.createSheet(lieferant.getLieferantenName());
            int rowIndex = 0;
            Row row = sht.createRow(rowIndex++);
            for (int columnIndex = 0; columnIndex < ProdCol.COUNT.get(); columnIndex++) {
                Cell cell = row.createCell(columnIndex);
                cell.setCellValue(getColumnName(columnIndex));
                cell.setCellStyle(headerStyle);
            }
            for (Kommission kommission : lieferant.getKommissionen()) {
                row = sht.createRow(rowIndex++);
                row.setHeightInPoints(4 * sht.getDefaultRowHeightInPoints());
                for (int columnIndex = 0; columnIndex < ProdCol.COUNT.get(); columnIndex++) {
                    Cell cell = row.createCell(columnIndex);
                    cell.setCellStyle(dataStyle);
                    Object value = getCellValue(kommission, columnIndex, "\n");
                    if (value instanceof String) {
                        cell.setCellValue((String) value);
                    } else if (value instanceof Integer) {
                        cell.setCellValue((Integer) value);
                    } else if (value instanceof java.util.Date) {
                        cell.setCellValue((java.util.Date) value);
                    } else if (value != null) {
                        throw new IllegalArgumentException("wrong type");
                    }
                }
            }
            for (int columnIndex = 0; columnIndex < ProdCol.COUNT.get(); columnIndex++) {
                sht.setColumnWidth(columnIndex, getColumnCharWidth(columnIndex) * 256);
            }
            sht.createFreezePane(0, 1);
        }
        return workbook;
    }

    public static void main(String[] args) {
        System.out.println(new ProduktionsplanungReporting().createQuery(10));
    }

}

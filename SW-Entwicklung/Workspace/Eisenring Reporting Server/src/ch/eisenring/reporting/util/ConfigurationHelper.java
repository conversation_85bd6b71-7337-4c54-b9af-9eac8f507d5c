package ch.eisenring.reporting.util;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;

public class ConfigurationHelper {

    /**
     * @param separatedString String with values that are separated by blanks, commas, semicolons
     * @return <var>separatedString</var> as Array without blanks, commas, semicolons, empty- or null-strings
     */
    public static Collection<String> splitString(String separatedString) {
        if (separatedString != null && separatedString.length() > 0) {
            Collection<String> list = new ArrayList<>();
            for (String part : separatedString.split("[ ,;]+")) {
                if (part != null) {
                    part = part.trim();
                    if (part.length() > 0) {
                        list.add(part);
                    }
                }
            }
            return list;
        } else {
            return Collections.emptyList();
        }
    }
}

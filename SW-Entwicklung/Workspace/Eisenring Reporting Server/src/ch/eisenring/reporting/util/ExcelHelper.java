/* This Java-Class is generated by {@link ExcelHelperCreator<PERSON>rin<PERSON>} (c) <PERSON> */
package ch.eisenring.reporting.util;

import org.apache.poi.ss.usermodel.*;

public interface ExcelHelper {
	
	// --------------------------------------------------------------------------------------------------------------------------------------------
	// -- CellStyle
	// --------------------------------------------------------------------------------------------------------------------------------------------

	/**
	 * Wrapper-Class for a {@link CellStyle CellStyle}. Handles differences between POI-Versions.
	 */
	public class ExcelCellStyle {
		private final CellStyle style;
		private final Font font;

		public ExcelCellStyle(Workbook workbook) {
			this.style = workbook.createCellStyle();
			this.font = workbook.createFont();
		}

		public CellStyle getStyle() {
			return style;
		}

		public ExcelCellStyle setBackgroundColor(IndexedColors indexedColors) {
			style.setFillForegroundColor(indexedColors.getIndex());
			style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
			return this;
		}

		public Font getFont() {
			return font;
		}

		public enum BorderLine {
			NORMAL, THICK, HAIR, DOUBLE
		}

		public ExcelCellStyle setBorderLine(BorderLine borderLine) {
			if (borderLine == BorderLine.NORMAL) {
				style.setBorderTop(BorderStyle.THIN);
				style.setBorderBottom(BorderStyle.THIN);
				style.setBorderLeft(BorderStyle.THIN);
				style.setBorderRight(BorderStyle.THIN);
			} else if (borderLine == BorderLine.THICK) {
				style.setBorderTop(BorderStyle.THICK);
				style.setBorderBottom(BorderStyle.THICK);
				style.setBorderLeft(BorderStyle.THICK);
				style.setBorderRight(BorderStyle.THICK);
			} else if (borderLine == BorderLine.HAIR) {
				style.setBorderTop(BorderStyle.HAIR);
				style.setBorderBottom(BorderStyle.HAIR);
				style.setBorderLeft(BorderStyle.HAIR);
				style.setBorderRight(BorderStyle.HAIR);
			} else if (borderLine == BorderLine.DOUBLE) {
				style.setBorderTop(BorderStyle.DOUBLE);
				style.setBorderBottom(BorderStyle.DOUBLE);
				style.setBorderLeft(BorderStyle.DOUBLE);
				style.setBorderRight(BorderStyle.DOUBLE);
			}
			return this;
		}

		public ExcelCellStyle setResultBorder() {
			style.setBorderTop(BorderStyle.THIN);
			style.setBorderBottom(BorderStyle.DOUBLE);
			return this;
		}

		public ExcelCellStyle setBorderColor(IndexedColors indexedColors) {
			style.setTopBorderColor(indexedColors.getIndex());
			style.setBottomBorderColor(indexedColors.getIndex());
			style.setLeftBorderColor(indexedColors.getIndex());
			style.setRightBorderColor(indexedColors.getIndex());
			return this;
		}

		public enum HorizAlign {
			LEFT, CENTER, RIGHT
		}

		public ExcelCellStyle setHorizAlign(HorizAlign horizAlign) {
			if (horizAlign == HorizAlign.LEFT) {
				style.setAlignment(HorizontalAlignment.LEFT);
			} else if (horizAlign == HorizAlign.CENTER) {
				style.setAlignment(HorizontalAlignment.CENTER);
			} else if (horizAlign == HorizAlign.RIGHT) {
				style.setAlignment(HorizontalAlignment.RIGHT);
			}
			return this;
		}

		public enum VertAlign {
			TOP, CENTER, BOTTOM
		}

		public ExcelCellStyle setVertAlign(VertAlign vertAlign) {
			if (vertAlign == VertAlign.TOP) {
				style.setVerticalAlignment(VerticalAlignment.TOP);
			} else if (vertAlign == VertAlign.CENTER) {
				style.setVerticalAlignment(VerticalAlignment.CENTER);
			} else if (vertAlign == VertAlign.BOTTOM) {
				style.setVerticalAlignment(VerticalAlignment.BOTTOM);
			}
			return this;
		}

		/**
		 * Each Excel-Cell has a its Value and can have a Format for rendering the Value.
		 * @param workbook the {@link Workbook} same instance as in the Constructor}
		 * @param format Excel-Format: <table border="1" cellspacing="0" cellpadding="5">
		 * <tr><td>yy, yyyy</td><td>Year</td>
		 * <tr><td>M, MM</td><td>Month</td></tr>
		 * <tr><td>ddd, dddd</td><td>DayOfWeek</td></tr>
		 * <tr><td>d, dd</td><td>Day</td></tr>
		 * <tr><td>h, hh</td><td>Hour</td></tr>
		 * <tr><td>m, mm</td><td>Minute</td></tr>
		 * <tr><td>s, ss, ss.0</td><td>Second</td></tr>
		 * <tr><td>0, 0.0</td><td>Numeric Format</td></tr>
		 * <tr><td>0%, 0.0%</td><td>Percentage</td></tr>
		 * </table>
		 * @return this {@link ExcelCellStyle}
		 */
		public ExcelCellStyle setFormat(Workbook workbook, String format) {
			style.setDataFormat(workbook.getCreationHelper().createDataFormat().getFormat(format));
			return this;
		}

		/**
		 * @see #setFormat(Workbook, String)
		 */
		public ExcelCellStyle setDateFormat(Workbook workbook, String format) {
			return setFormat(workbook, format);
		}

		public ExcelCellStyle setWordWrap(boolean wordWrap) {
			style.setWrapText(wordWrap);
			return this;
		}

		public ExcelCellStyle setFontName(String fontName) {
			font.setFontName(fontName);
			style.setFont(font);
			return this;
		}

		public ExcelCellStyle setFontSize(double size) {
			font.setFontHeight((short) (20 * size + 0.5));
			style.setFont(font);
			return this;
		}

		public ExcelCellStyle setFontColor(IndexedColors indexedColors) {
			font.setColor(indexedColors.getIndex());
			style.setFont(font);
			return this;
		}

		public ExcelCellStyle setFontBold(boolean bold) {
			font.setBold(bold);
			style.setFont(font);
			return this;
		}

		public ExcelCellStyle setFontItalic(boolean italic) {
			font.setItalic(italic);
			style.setFont(font);
			return this;
		}
	}

	// --------------------------------------------------------------------------------------------------------------------------------------------
	// -- Columns
	// --------------------------------------------------------------------------------------------------------------------------------------------

	/**
	 * Calculates the Name of a Excel-Column e.g. 0 &rarr; "A", 26 &rarr; "AA"
	 * @param columnIndex Column-Index beginning with 0
	 * @return Excel-Column-Name
	 */
	public static String getColumnName(int columnIndex) {
		StringBuilder msg = new StringBuilder();
		while (columnIndex >= 0) {
			int d = columnIndex % 26;
			msg.insert(0, (char) (d + 'A'));
			columnIndex = (columnIndex - d) / 26 - 1; // or: columnIndex / 26 - 1
		}
		return msg.toString();
	}

	/**
	 * Calculates the Column-Index of an Excel-Cell e.g. "A" &rarr; 0, "AA" &rarr; 26
	 * @param cellSpec Cell-Name e.g. "AA1" or "AB"
	 * @return Excel-Column-Index
	 */
	public static int getColumnIndex(String cellSpec) {
		int c = -1;
		for (char ch : cellSpec.toUpperCase().toCharArray()) {
			if ('A' <= ch && ch <= 'Z') {
				c = (c + 1) * 26 + ch - 'A';
			}
		}
		return c;
	}

	/**
	 * Calculates the Column-Index-Array of an Excel-Cell-Range e.g. "B1:C5" &rarr; int[ 1, 2 ]
	 * @param range Cell-Range e.g. "B1:C5" or "D5:B1"
	 * @return Column-Index-Array
	 */
	public static int[] getColumnIndexes(String range) {
		int pos = range.indexOf(':');
		if (pos < 0) {
			return new int[] { getColumnIndex(range) };
		} else {
			int a = getColumnIndex(range.substring(0, pos));
			int b = getColumnIndex(range.substring(pos + 1));
			int[] array = new int[Math.abs(b - a) + 1];
			for (int i = 0; i < array.length; i++) {
				array[i] = i + Math.min(a, b);
			}
			return array;
		}
	}

	// --------------------------------------------------------------------------------------------------------------------------------------------
	// -- Rows
	// --------------------------------------------------------------------------------------------------------------------------------------------

	/**
	 * Calculates the Name of a Excel-Row e.g. 0 &rarr; "1", 26 &rarr; "27"
	 * @param rowIndex Row-Index beginning with 0
	 * @return Excel-Row-Name
	 */
	public static String getRowName(int rowIndex) {
		return Integer.toString(rowIndex + 1);
	}

	/**
	 * Calculates the Row-Index of an Excel-Cell e.g. "1" &rarr; 0, "27" &rarr; 26
	 * @param cellSpec Cell-Name e.g. "A27" or "5"
	 * @return Excel-Row-Index
	 */
	public static int getRowIndex(String cellSpec) {
		int r = 0;
		for (char ch : cellSpec.toCharArray()) {
			if ('0' <= ch && ch <= '9') {
				r = 10 * r + ch - '0';
			}
		}
		return r - 1;
	}

	/**
	 * Calculates the Row-Index-Array of an Excel-Cell-Range e.g. "B1:C5" &rarr; int[ 0, 1, 2, 3, 4 ]
	 * @param range Cell-Range e.g. "B1:C5" or "D5:B1"
	 * @return Row-Index-Array
	 */
	public static int[] getRowIndexes(String range) {
		int pos = range.indexOf(':');
		if (pos < 0) {
			return new int[] { getRowIndex(range) };
		} else {
			int a = getRowIndex(range.substring(0, pos));
			int b = getRowIndex(range.substring(pos + 1));
			int[] array = new int[Math.abs(b - a) + 1];
			for (int i = 0; i < array.length; i++) {
				array[i] = i + Math.min(a, b);
			}
			return array;
		}
	}

}
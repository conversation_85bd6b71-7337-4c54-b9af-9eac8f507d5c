/* This Java-Class is generated by {@link ReportingServerDatabaseConstantCreator} (c) <PERSON> */
package ch.eisenring.reporting.util;

public interface DatabaseConstants {

	public interface BasisDboSchema {

		/** BASIS.dbo.<b>appcode</b>, 8 Columns **/
		public interface AppcodeTable {
			/** BASIS.dbo.<b>appcode</b> **/
			String _TN = "BASIS.dbo.appcode";
			/** BASIS.dbo.appcode.<b>Id_GSE_Code</b>, varchar(4), mandatory **/
			String ID_GSE_CODE = "Id_GSE_Code";
			/** BASIS.dbo.appcode.<b>CD_Sprache</b>, char(3), mandatory **/
			String CD_SPRACHE = "CD_Sprache";
			/** BASIS.dbo.appcode.<b>Tab_Prfx</b>, varchar(4), mandatory **/
			String TAB_PRFX = "Tab_Prfx";
			/** BASIS.dbo.appcode.<b>Col_Name</b>, varchar(30), mandatory **/
			String COL_NAME = "Col_Name";
			/** BASIS.dbo.appcode.<b>CD_Typ</b>, char(3), mandatory **/
			String CD_TYP = "CD_Typ";
			/** BASIS.dbo.appcode.<b>CD_Hard</b>, char(3), mandatory **/
			String CD_HARD = "CD_Hard";
			/** BASIS.dbo.appcode.<b>CD_Soft</b>, varchar(8), mandatory **/
			String CD_SOFT = "CD_Soft";
			/** BASIS.dbo.appcode.<b>Bezeichnung</b>, varchar(30), mandatory **/
			String BEZEICHNUNG = "Bezeichnung";
		}

		/** BASIS.dbo.<b>GBZGRUPPE</b>, 6 Columns **/
		public interface GbzGruppeTable {
			/** BASIS.dbo.<b>GBZGRUPPE</b> **/
			String _TN = "BASIS.dbo.GBZGRUPPE";
			/** BASIS.dbo.GBZGRUPPE.<b>Id_GSE</b>, varchar(4), mandatory **/
			String ID_GSE = "Id_GSE";
			/** BASIS.dbo.GBZGRUPPE.<b>GBZGrArt</b>, char(3), mandatory **/
			String GBZ_GR_ART = "GBZGrArt";
			/** BASIS.dbo.GBZGRUPPE.<b>Gruppe</b>, varchar(8), mandatory **/
			String GRUPPE = "Gruppe";
			/** BASIS.dbo.GBZGRUPPE.<b>Bezeichnung</b>, varchar(40), mandatory **/
			String BEZEICHNUNG = "Bezeichnung";
			/** BASIS.dbo.GBZGRUPPE.<b>Rolle</b>, char(3) **/
			String ROLLE = "Rolle";
			/** BASIS.dbo.GBZGRUPPE.<b>Id_SUBJEKT</b>, int(4) **/
			String ID_SUBJEKT = "Id_SUBJEKT";
		}

		/** BASIS.dbo.<b>SUBJEKT</b>, 50 Columns **/
		public interface SubjektTable {
			/** BASIS.dbo.<b>SUBJEKT</b> **/
			String _TN = "BASIS.dbo.SUBJEKT";
			/** BASIS.dbo.SUBJEKT.<b>Id</b>, int(4), mandatory **/
			String ID = "Id";
			/** BASIS.dbo.SUBJEKT.<b>Name</b>, varchar(50), mandatory **/
			String NAME = "Name";
			/** BASIS.dbo.SUBJEKT.<b>Adresskette_BSUA</b>, char(3), mandatory **/
			String ADRESSKETTE_BSUA = "Adresskette_BSUA";
			/** BASIS.dbo.SUBJEKT.<b>ZusatzName</b>, varchar(35) **/
			String ZUSATZ_NAME = "ZusatzName";
			/** BASIS.dbo.SUBJEKT.<b>Anrede</b>, char(3) **/
			String ANREDE = "Anrede";
			/** BASIS.dbo.SUBJEKT.<b>ZusatzBez</b>, varchar(35) **/
			String ZUSATZ_BEZ = "ZusatzBez";
			/** BASIS.dbo.SUBJEKT.<b>BerufBranche</b>, char(3) **/
			String BERUF_BRANCHE = "BerufBranche";
			/** BASIS.dbo.SUBJEKT.<b>PrivPersonCd</b>, char(3), mandatory **/
			String PRIV_PERSON_CD = "PrivPersonCd";
			/** BASIS.dbo.SUBJEKT.<b>DruckRhflgCd</b>, char(3), mandatory **/
			String DRUCK_RHFLG_CD = "DruckRhflgCd";
			/** BASIS.dbo.SUBJEKT.<b>KorrSprach</b>, char(3), mandatory **/
			String KORR_SPRACH = "KorrSprach";
			/** BASIS.dbo.SUBJEKT.<b>KurzBez</b>, varchar(20), mandatory **/
			String KURZ_BEZ = "KurzBez";
			/** BASIS.dbo.SUBJEKT.<b>Kuerzel</b>, varchar(8) **/
			String KUERZEL = "Kuerzel";
			/** BASIS.dbo.SUBJEKT.<b>Titel</b>, char(3) **/
			String TITEL = "Titel";
			/** BASIS.dbo.SUBJEKT.<b>TitelSteCd</b>, char(3) **/
			String TITEL_STE_CD = "TitelSteCd";
			/** BASIS.dbo.SUBJEKT.<b>SuchBegriff</b>, varchar(15) **/
			String SUCH_BEGRIFF = "SuchBegriff";
			/** BASIS.dbo.SUBJEKT.<b>SuchBegriff2</b>, varchar(15) **/
			String SUCH_BEGRIFF2 = "SuchBegriff2";
			/** BASIS.dbo.SUBJEKT.<b>GesForm</b>, char(3) **/
			String GES_FORM = "GesForm";
			/** BASIS.dbo.SUBJEKT.<b>GrossistenNr</b>, varchar(12) **/
			String GROSSISTEN_NR = "GrossistenNr";
			/** BASIS.dbo.SUBJEKT.<b>GrossistAb</b>, datetime(8) **/
			String GROSSIST_AB = "GrossistAb";
			/** BASIS.dbo.SUBJEKT.<b>GrossistBis</b>, datetime(8) **/
			String GROSSIST_BIS = "GrossistBis";
			/** BASIS.dbo.SUBJEKT.<b>DauerGross</b>, char(3) **/
			String DAUER_GROSS = "DauerGross";
			/** BASIS.dbo.SUBJEKT.<b>GebGruendDat</b>, datetime(8) **/
			String GEB_GRUEND_DAT = "GebGruendDat";
			/** BASIS.dbo.SUBJEKT.<b>BenutzerDat</b>, datetime(8) **/
			String BENUTZER_DAT = "BenutzerDat";
			/** BASIS.dbo.SUBJEKT.<b>GueltAbDat</b>, datetime(8), mandatory **/
			String GUELT_AB_DAT = "GueltAbDat";
			/** BASIS.dbo.SUBJEKT.<b>Id_GSE</b>, varchar(4) **/
			String ID_GSE = "Id_GSE";
			/** BASIS.dbo.SUBJEKT.<b>LaufNr_SUBADRESSE</b>, smallint(2), mandatory **/
			String LAUF_NR_SUBADRESSE = "LaufNr_SUBADRESSE";
			/** BASIS.dbo.SUBJEKT.<b>Briefanrede</b>, varchar(40) **/
			String BRIEFANREDE = "Briefanrede";
			/** BASIS.dbo.SUBJEKT.<b>Status</b>, char(3) **/
			String STATUS = "Status";
			/** BASIS.dbo.SUBJEKT.<b>SubResDatum</b>, datetime(8) **/
			String SUB_RES_DATUM = "SubResDatum";
			/** BASIS.dbo.SUBJEKT.<b>SubResCd1</b>, char(3) **/
			String SUB_RES_CD1 = "SubResCd1";
			/** BASIS.dbo.SUBJEKT.<b>SubResCd2</b>, char(3) **/
			String SUB_RES_CD2 = "SubResCd2";
			/** BASIS.dbo.SUBJEKT.<b>SubResCd3</b>, char(3) **/
			String SUB_RES_CD3 = "SubResCd3";
			/** BASIS.dbo.SUBJEKT.<b>SubResCd4</b>, varchar(5) **/
			String SUB_RES_CD4 = "SubResCd4";
			/** BASIS.dbo.SUBJEKT.<b>SubResCd5</b>, varchar(5) **/
			String SUB_RES_CD5 = "SubResCd5";
			/** BASIS.dbo.SUBJEKT.<b>SubResCd6</b>, varchar(5) **/
			String SUB_RES_CD6 = "SubResCd6";
			/** BASIS.dbo.SUBJEKT.<b>AHVNr</b>, char(11) **/
			String AHV_NR = "AHVNr";
			/** BASIS.dbo.SUBJEKT.<b>BurNr</b>, int(4) **/
			String BUR_NR = "BurNr";
			/** BASIS.dbo.SUBJEKT.<b>Gesperrt</b>, char(3) **/
			String GESPERRT = "Gesperrt";
			/** BASIS.dbo.SUBJEKT.<b>Geschlecht</b>, char(3) **/
			String GESCHLECHT = "Geschlecht";
			/** BASIS.dbo.SUBJEKT.<b>Heimatort</b>, varchar(35) **/
			String HEIMATORT = "Heimatort";
			/** BASIS.dbo.SUBJEKT.<b>Konfession</b>, char(3) **/
			String KONFESSION = "Konfession";
			/** BASIS.dbo.SUBJEKT.<b>SprechSpra</b>, char(3) **/
			String SPRECH_SPRA = "SprechSpra";
			/** BASIS.dbo.SUBJEKT.<b>UstIdNr</b>, varchar(15) **/
			String UST_ID_NR = "UstIdNr";
			/** BASIS.dbo.SUBJEKT.<b>Zivilstand</b>, char(3) **/
			String ZIVILSTAND = "Zivilstand";
			/** BASIS.dbo.SUBJEKT.<b>UPDLOCK</b>, int(4) **/
			String UPDLOCK = "UPDLOCK";
			/** BASIS.dbo.SUBJEKT.<b>TIMESTAMP</b>, timestamp(8), mandatory **/
			String TIMESTAMP = "TIMESTAMP";
			/** BASIS.dbo.SUBJEKT.<b>ADD_DATE</b>, datetime(8), mandatory **/
			String ADD_DATE = "ADD_DATE";
			/** BASIS.dbo.SUBJEKT.<b>ADD_SABE</b>, varchar(10), mandatory **/
			String ADD_SABE = "ADD_SABE";
			/** BASIS.dbo.SUBJEKT.<b>UPD_DATE</b>, datetime(8) **/
			String UPD_DATE = "UPD_DATE";
			/** BASIS.dbo.SUBJEKT.<b>UPD_SABE</b>, varchar(10) **/
			String UPD_SABE = "UPD_SABE";
		}
	}

	public interface HeagDmsProduktivDboSchema {

		/** HEAG_DMS_Produktiv.dbo.<b>DMSAuditTrail</b>, 8 Columns, PK: rowId(ROW_ID) **/
		public interface DmsAuditTrailTable {
			/** HEAG_DMS_Produktiv.dbo.<b>DMSAuditTrail</b> **/
			String _TN = "HEAG_DMS_Produktiv.dbo.DMSAuditTrail";
			/** HEAG_DMS_Produktiv.dbo.DMSAuditTrail.<b>rowId</b>, bigint(8), PrimaryKey **/
			String ROW_ID = "rowId";
			/** HEAG_DMS_Produktiv.dbo.DMSAuditTrail.<b>Object_rowId</b>, bigint(8), mandatory, &rArr; DMSObject.rowId **/
			String OBJECT_ROW_ID = "Object_rowId";
			/** HEAG_DMS_Produktiv.dbo.DMSAuditTrail.<b>Binary_rowId</b>, bigint(8) **/
			String BINARY_ROW_ID = "Binary_rowId";
			/** HEAG_DMS_Produktiv.dbo.DMSAuditTrail.<b>Auditcode</b>, smallint(2), mandatory **/
			String AUDITCODE = "Auditcode";
			/** HEAG_DMS_Produktiv.dbo.DMSAuditTrail.<b>MadeOn</b>, datetime(8), mandatory **/
			String MADE_ON = "MadeOn";
			/** HEAG_DMS_Produktiv.dbo.DMSAuditTrail.<b>MadeBy</b>, varchar(60), mandatory **/
			String MADE_BY = "MadeBy";
			/** HEAG_DMS_Produktiv.dbo.DMSAuditTrail.<b>ValueOld</b>, varchar(250) **/
			String VALUE_OLD = "ValueOld";
			/** HEAG_DMS_Produktiv.dbo.DMSAuditTrail.<b>ValueNew</b>, varchar(250) **/
			String VALUE_NEW = "ValueNew";
		}

		/** HEAG_DMS_Produktiv.dbo.<b>DMSObject</b>, 16 Columns, PK: rowId(ROW_ID) **/
		public interface DmsObjectTable {
			/** HEAG_DMS_Produktiv.dbo.<b>DMSObject</b> **/
			String _TN = "HEAG_DMS_Produktiv.dbo.DMSObject";
			/** HEAG_DMS_Produktiv.dbo.DMSObject.<b>rowId</b>, bigint(8), PrimaryKey **/
			String ROW_ID = "rowId";
			/** HEAG_DMS_Produktiv.dbo.DMSObject.<b>Parent_rowId</b>, bigint(8), &rArr; DMSObject.rowId **/
			String PARENT_ROW_ID = "Parent_rowId";
			/** HEAG_DMS_Produktiv.dbo.DMSObject.<b>Permission_rowId</b>, bigint(8), &rArr; DMSPermission.rowId **/
			String PERMISSION_ROW_ID = "Permission_rowId";
			/** HEAG_DMS_Produktiv.dbo.DMSObject.<b>Typecode</b>, smallint(2), mandatory **/
			String TYPECODE = "Typecode";
			/** HEAG_DMS_Produktiv.dbo.DMSObject.<b>Statuscode</b>, smallint(2), mandatory **/
			String STATUSCODE = "Statuscode";
			/** HEAG_DMS_Produktiv.dbo.DMSObject.<b>Dirtycode</b>, smallint(2), mandatory **/
			String DIRTYCODE = "Dirtycode";
			/** HEAG_DMS_Produktiv.dbo.DMSObject.<b>Iconcode</b>, smallint(2), mandatory **/
			String ICONCODE = "Iconcode";
			/** HEAG_DMS_Produktiv.dbo.DMSObject.<b>LastChanged</b>, datetime(8), mandatory **/
			String LAST_CHANGED = "LastChanged";
			/** HEAG_DMS_Produktiv.dbo.DMSObject.<b>FileExtension</b>, varchar(16) **/
			String FILE_EXTENSION = "FileExtension";
			/** HEAG_DMS_Produktiv.dbo.DMSObject.<b>Objectname</b>, varchar(250), mandatory **/
			String OBJECTNAME = "Objectname";
			/** HEAG_DMS_Produktiv.dbo.DMSObject.<b>Participants</b>, varchar(250) **/
			String PARTICIPANTS = "Participants";
			/** HEAG_DMS_Produktiv.dbo.DMSObject.<b>Keywords</b>, varchar(2004) **/
			String KEYWORDS = "Keywords";
			/** HEAG_DMS_Produktiv.dbo.DMSObject.<b>Searchwords</b>, varchar **/
			String SEARCHWORDS = "Searchwords";
			/** HEAG_DMS_Produktiv.dbo.DMSObject.<b>Thumbnail</b>, varbinary **/
			String THUMBNAIL = "Thumbnail";
			/** HEAG_DMS_Produktiv.dbo.DMSObject.<b>Bytesize</b>, bigint(8) **/
			String BYTESIZE = "Bytesize";
			/** HEAG_DMS_Produktiv.dbo.DMSObject.<b>tru_typeclass</b>, smallint(2) **/
			String TRU_TYPECLASS = "tru_typeclass";
		}

		/** HEAG_DMS_Produktiv.dbo.<b>DMSPermission</b>, 4 Columns, PK: rowId(ROW_ID) **/
		public interface DmsPermissionTable {
			/** HEAG_DMS_Produktiv.dbo.<b>DMSPermission</b> **/
			String _TN = "HEAG_DMS_Produktiv.dbo.DMSPermission";
			/** HEAG_DMS_Produktiv.dbo.DMSPermission.<b>rowId</b>, bigint(8), PrimaryKey **/
			String ROW_ID = "rowId";
			/** HEAG_DMS_Produktiv.dbo.DMSPermission.<b>name</b>, varchar(30), mandatory **/
			String NAME = "name";
			/** HEAG_DMS_Produktiv.dbo.DMSPermission.<b>permissions</b>, varchar **/
			String PERMISSIONS = "permissions";
			/** HEAG_DMS_Produktiv.dbo.DMSPermission.<b>privateFlag</b>, int(4), mandatory **/
			String PRIVATE_FLAG = "privateFlag";
		}

		/** HEAG_DMS_Produktiv.dbo.<b>DMSProperty</b>, 4 Columns, PK: rowId(ROW_ID) **/
		public interface DmsPropertyTable {
			/** HEAG_DMS_Produktiv.dbo.<b>DMSProperty</b> **/
			String _TN = "HEAG_DMS_Produktiv.dbo.DMSProperty";
			/** HEAG_DMS_Produktiv.dbo.DMSProperty.<b>rowId</b>, bigint(8), PrimaryKey **/
			String ROW_ID = "rowId";
			/** HEAG_DMS_Produktiv.dbo.DMSProperty.<b>Object_rowId</b>, bigint(8), mandatory, &rArr; DMSObject.rowId **/
			String OBJECT_ROW_ID = "Object_rowId";
			/** HEAG_DMS_Produktiv.dbo.DMSProperty.<b>Propertycode</b>, smallint(2), mandatory **/
			String PROPERTYCODE = "Propertycode";
			/** HEAG_DMS_Produktiv.dbo.DMSProperty.<b>Propertyvalue</b>, varchar(250) **/
			String PROPERTYVALUE = "Propertyvalue";
		}
	}

	public interface HeagDspProduktivDboSchema {

		/** HEAG_DSP_Produktiv.dbo.<b>LWX_PROJEKTKONDITIONEN</b>, 3 Columns, PK: PAuftrNr(P_AUFTR_NR) PKondId(P_KOND_ID) **/
		public interface LwxProjektkonditionenTable {
			/** HEAG_DSP_Produktiv.dbo.<b>LWX_PROJEKTKONDITIONEN</b> **/
			String _TN = "HEAG_DSP_Produktiv.dbo.LWX_PROJEKTKONDITIONEN";
			/** HEAG_DSP_Produktiv.dbo.LWX_PROJEKTKONDITIONEN.<b>PAuftrNr</b>, varchar(16), PrimaryKey **/
			String P_AUFTR_NR = "PAuftrNr";
			/** HEAG_DSP_Produktiv.dbo.LWX_PROJEKTKONDITIONEN.<b>PKondId</b>, int(4), PrimaryKey **/
			String P_KOND_ID = "PKondId";
			/** HEAG_DSP_Produktiv.dbo.LWX_PROJEKTKONDITIONEN.<b>Value</b>, varchar(60), mandatory **/
			String VALUE = "Value";
		}
	}

	public interface VertriebDboSchema {

		/** VERTRIEB.dbo.<b>AddendumZahlung</b>, 41 Columns **/
		public interface AddendumZahlungTable {
			/** VERTRIEB.dbo.<b>AddendumZahlung</b> **/
			String _TN = "VERTRIEB.dbo.AddendumZahlung";
			/** VERTRIEB.dbo.AddendumZahlung.<b>Id_GSE</b>, char(4), mandatory **/
			String ID_GSE = "Id_GSE";
			/** VERTRIEB.dbo.AddendumZahlung.<b>Auftrnr</b>, int(4), mandatory **/
			String AUFTRNR = "Auftrnr";
			/** VERTRIEB.dbo.AddendumZahlung.<b>Id_GSE_ZAuftrag</b>, char(4) **/
			String ID_GSE_Z_AUFTRAG = "Id_GSE_ZAuftrag";
			/** VERTRIEB.dbo.AddendumZahlung.<b>Auftrnr_ZAuftrag</b>, int(4) **/
			String AUFTRNR_Z_AUFTRAG = "Auftrnr_ZAuftrag";
			/** VERTRIEB.dbo.AddendumZahlung.<b>Verrechnet</b>, char(3), mandatory **/
			String VERRECHNET = "Verrechnet";
			/** VERTRIEB.dbo.AddendumZahlung.<b>Modalitaet</b>, char(3), mandatory **/
			String MODALITAET = "Modalitaet";
			/** VERTRIEB.dbo.AddendumZahlung.<b>Stufe</b>, char(3), mandatory **/
			String STUFE = "Stufe";
			/** VERTRIEB.dbo.AddendumZahlung.<b>Id_GSE_PA</b>, char(4), mandatory **/
			String ID_GSE_PA = "Id_GSE_PA";
			/** VERTRIEB.dbo.AddendumZahlung.<b>Pauftrnr</b>, varchar(16), mandatory **/
			String PAUFTRNR = "Pauftrnr";
			/** VERTRIEB.dbo.AddendumZahlung.<b>Id_GSE_PA_B</b>, char(4), mandatory **/
			String ID_GSE_PA_B = "Id_GSE_PA_B";
			/** VERTRIEB.dbo.AddendumZahlung.<b>Pauftrnr_B</b>, varchar(16), mandatory **/
			String PAUFTRNR_B = "Pauftrnr_B";
			/** VERTRIEB.dbo.AddendumZahlung.<b>Obj_LaufNr</b>, int(4), mandatory **/
			String OBJ_LAUF_NR = "Obj_LaufNr";
			/** VERTRIEB.dbo.AddendumZahlung.<b>Add_date</b>, datetime(8), mandatory **/
			String ADD_DATE = "Add_date";
			/** VERTRIEB.dbo.AddendumZahlung.<b>Add_sabe</b>, varchar(10), mandatory **/
			String ADD_SABE = "Add_sabe";
			/** VERTRIEB.dbo.AddendumZahlung.<b>Upd_date</b>, datetime(8), mandatory **/
			String UPD_DATE = "Upd_date";
			/** VERTRIEB.dbo.AddendumZahlung.<b>Upd_sabe</b>, varchar(10), mandatory **/
			String UPD_SABE = "Upd_sabe";
			/** VERTRIEB.dbo.AddendumZahlung.<b>Id_GSE_GBZ</b>, char(4) **/
			String ID_GSE_GBZ = "Id_GSE_GBZ";
			/** VERTRIEB.dbo.AddendumZahlung.<b>Id_Subjekt</b>, int(4) **/
			String ID_SUBJEKT = "Id_Subjekt";
			/** VERTRIEB.dbo.AddendumZahlung.<b>SubjektTyp</b>, char(3) **/
			String SUBJEKT_TYP = "SubjektTyp";
			/** VERTRIEB.dbo.AddendumZahlung.<b>PosNr</b>, decimal(5) **/
			String POS_NR = "PosNr";
			/** VERTRIEB.dbo.AddendumZahlung.<b>Preistyp</b>, char(3) **/
			String PREISTYP = "Preistyp";
			/** VERTRIEB.dbo.AddendumZahlung.<b>Id_Subjekt_RA</b>, int(4) **/
			String ID_SUBJEKT_RA = "Id_Subjekt_RA";
			/** VERTRIEB.dbo.AddendumZahlung.<b>Modalitaet_M</b>, char(3) **/
			String MODALITAET_M = "Modalitaet_M";
			/** VERTRIEB.dbo.AddendumZahlung.<b>Stufe_M</b>, char(3) **/
			String STUFE_M = "Stufe_M";
			/** VERTRIEB.dbo.AddendumZahlung.<b>Id_Subjekt_F</b>, int(4) **/
			String ID_SUBJEKT_F = "Id_Subjekt_F";
			/** VERTRIEB.dbo.AddendumZahlung.<b>AbwArt</b>, char(3) **/
			String ABW_ART = "AbwArt";
			/** VERTRIEB.dbo.AddendumZahlung.<b>AbwArtNew</b>, char(3) **/
			String ABW_ART_NEW = "AbwArtNew";
			/** VERTRIEB.dbo.AddendumZahlung.<b>Betrag_BP</b>, decimal(9) **/
			String BETRAG_BP = "Betrag_BP";
			/** VERTRIEB.dbo.AddendumZahlung.<b>Betrag_MP</b>, decimal(9) **/
			String BETRAG_MP = "Betrag_MP";
			/** VERTRIEB.dbo.AddendumZahlung.<b>Reserve01</b>, float(8) **/
			String RESERVE01 = "Reserve01";
			/** VERTRIEB.dbo.AddendumZahlung.<b>Reserve02</b>, float(8) **/
			String RESERVE02 = "Reserve02";
			/** VERTRIEB.dbo.AddendumZahlung.<b>Reserve03</b>, float(8) **/
			String RESERVE03 = "Reserve03";
			/** VERTRIEB.dbo.AddendumZahlung.<b>Reserve04</b>, varchar(50) **/
			String RESERVE04 = "Reserve04";
			/** VERTRIEB.dbo.AddendumZahlung.<b>Reserve05</b>, varchar(50) **/
			String RESERVE05 = "Reserve05";
			/** VERTRIEB.dbo.AddendumZahlung.<b>Reserve06</b>, varchar(50) **/
			String RESERVE06 = "Reserve06";
			/** VERTRIEB.dbo.AddendumZahlung.<b>Reserve07</b>, int(4) **/
			String RESERVE07 = "Reserve07";
			/** VERTRIEB.dbo.AddendumZahlung.<b>Reserve08</b>, int(4) **/
			String RESERVE08 = "Reserve08";
			/** VERTRIEB.dbo.AddendumZahlung.<b>Reserve09</b>, int(4) **/
			String RESERVE09 = "Reserve09";
			/** VERTRIEB.dbo.AddendumZahlung.<b>Reserve10</b>, char(3) **/
			String RESERVE10 = "Reserve10";
			/** VERTRIEB.dbo.AddendumZahlung.<b>Reserve11</b>, char(3) **/
			String RESERVE11 = "Reserve11";
			/** VERTRIEB.dbo.AddendumZahlung.<b>Reserve12</b>, char(3) **/
			String RESERVE12 = "Reserve12";
		}

		/** VERTRIEB.dbo.<b>AUFPOS</b>, 234 Columns **/
		public interface AufposTable {
			/** VERTRIEB.dbo.<b>AUFPOS</b> **/
			String _TN = "VERTRIEB.dbo.AUFPOS";
			/** VERTRIEB.dbo.AUFPOS.<b>Id_GSE</b>, char(4), mandatory **/
			String ID_GSE = "Id_GSE";
			/** VERTRIEB.dbo.AUFPOS.<b>AuftrNr</b>, int(4), mandatory **/
			String AUFTR_NR = "AuftrNr";
			/** VERTRIEB.dbo.AUFPOS.<b>PosNr</b>, float(8), mandatory **/
			String POS_NR = "PosNr";
			/** VERTRIEB.dbo.AUFPOS.<b>DatPrErm</b>, datetime(8) **/
			String DAT_PR_ERM = "DatPrErm";
			/** VERTRIEB.dbo.AUFPOS.<b>DatKondErm</b>, datetime(8) **/
			String DAT_KOND_ERM = "DatKondErm";
			/** VERTRIEB.dbo.AUFPOS.<b>ZirkaCd</b>, char(3), mandatory **/
			String ZIRKA_CD = "ZirkaCd";
			/** VERTRIEB.dbo.AUFPOS.<b>SktoBer</b>, char(3), mandatory **/
			String SKTO_BER = "SktoBer";
			/** VERTRIEB.dbo.AUFPOS.<b>PrErmCd</b>, char(3), mandatory **/
			String PR_ERM_CD = "PrErmCd";
			/** VERTRIEB.dbo.AUFPOS.<b>KondErCd</b>, char(3), mandatory **/
			String KOND_ER_CD = "KondErCd";
			/** VERTRIEB.dbo.AUFPOS.<b>NettoCd</b>, char(3), mandatory **/
			String NETTO_CD = "NettoCd";
			/** VERTRIEB.dbo.AUFPOS.<b>GarantCd</b>, char(3), mandatory **/
			String GARANT_CD = "GarantCd";
			/** VERTRIEB.dbo.AUFPOS.<b>PSplitt</b>, char(3), mandatory **/
			String P_SPLITT = "PSplitt";
			/** VERTRIEB.dbo.AUFPOS.<b>StlWert</b>, char(3), mandatory **/
			String STL_WERT = "StlWert";
			/** VERTRIEB.dbo.AUFPOS.<b>GrundPr</b>, float(8) **/
			String GRUND_PR = "GrundPr";
			/** VERTRIEB.dbo.AUFPOS.<b>PE</b>, float(8) **/
			String PE = "PE";
			/** VERTRIEB.dbo.AUFPOS.<b>PrHerku</b>, char(3), mandatory **/
			String PR_HERKU = "PrHerku";
			/** VERTRIEB.dbo.AUFPOS.<b>GratisCd</b>, char(3) **/
			String GRATIS_CD = "GratisCd";
			/** VERTRIEB.dbo.AUFPOS.<b>FortschrSper</b>, char(3) **/
			String FORTSCHR_SPER = "FortschrSper";
			/** VERTRIEB.dbo.AUFPOS.<b>ChBezugsArt</b>, char(3), mandatory **/
			String CH_BEZUGS_ART = "ChBezugsArt";
			/** VERTRIEB.dbo.AUFPOS.<b>AvisDat</b>, datetime(8) **/
			String AVIS_DAT = "AvisDat";
			/** VERTRIEB.dbo.AUFPOS.<b>GewBrut</b>, float(8) **/
			String GEW_BRUT = "GewBrut";
			/** VERTRIEB.dbo.AUFPOS.<b>Durchmesser</b>, float(8) **/
			String DURCHMESSER = "Durchmesser";
			/** VERTRIEB.dbo.AUFPOS.<b>Hoehe</b>, float(8) **/
			String HOEHE = "Hoehe";
			/** VERTRIEB.dbo.AUFPOS.<b>Breite</b>, float(8) **/
			String BREITE = "Breite";
			/** VERTRIEB.dbo.AUFPOS.<b>Laenge</b>, float(8) **/
			String LAENGE = "Laenge";
			/** VERTRIEB.dbo.AUFPOS.<b>VerwendArt</b>, char(3), mandatory **/
			String VERWEND_ART = "VerwendArt";
			/** VERTRIEB.dbo.AUFPOS.<b>KommRef</b>, varchar(50) **/
			String KOMM_REF = "KommRef";
			/** VERTRIEB.dbo.AUFPOS.<b>Status</b>, char(3), mandatory **/
			String STATUS = "Status";
			/** VERTRIEB.dbo.AUFPOS.<b>StornoGr</b>, char(3) **/
			String STORNO_GR = "StornoGr";
			/** VERTRIEB.dbo.AUFPOS.<b>UmFaktBL</b>, float(8) **/
			String UM_FAKT_BL = "UmFaktBL";
			/** VERTRIEB.dbo.AUFPOS.<b>UmrDivBL</b>, float(8) **/
			String UMR_DIV_BL = "UmrDivBL";
			/** VERTRIEB.dbo.AUFPOS.<b>UmFaktVL</b>, float(8) **/
			String UM_FAKT_VL = "UmFaktVL";
			/** VERTRIEB.dbo.AUFPOS.<b>UmrDivVL</b>, float(8) **/
			String UMR_DIV_VL = "UmrDivVL";
			/** VERTRIEB.dbo.AUFPOS.<b>TotBMG</b>, float(8), mandatory **/
			String TOT_BMG = "TotBMG";
			/** VERTRIEB.dbo.AUFPOS.<b>TotGMG</b>, float(8), mandatory **/
			String TOT_GMG = "TotGMG";
			/** VERTRIEB.dbo.AUFPOS.<b>TotFMG</b>, float(8), mandatory **/
			String TOT_FMG = "TotFMG";
			/** VERTRIEB.dbo.AUFPOS.<b>AbwME</b>, char(3), mandatory **/
			String ABW_ME = "AbwME";
			/** VERTRIEB.dbo.AUFPOS.<b>MEL</b>, char(3), mandatory **/
			String MEL = "MEL";
			/** VERTRIEB.dbo.AUFPOS.<b>MEV</b>, char(3), mandatory **/
			String MEV = "MEV";
			/** VERTRIEB.dbo.AUFPOS.<b>FixMass</b>, float(8) **/
			String FIX_MASS = "FixMass";
			/** VERTRIEB.dbo.AUFPOS.<b>PendGebiCd</b>, char(3), mandatory **/
			String PEND_GEBI_CD = "PendGebiCd";
			/** VERTRIEB.dbo.AUFPOS.<b>PosTextV</b>, text(16) **/
			String POS_TEXT_V = "PosTextV";
			/** VERTRIEB.dbo.AUFPOS.<b>ProduktBez</b>, varchar(200), mandatory **/
			String PRODUKT_BEZ = "ProduktBez";
			/** VERTRIEB.dbo.AUFPOS.<b>ProdBezMan</b>, char(3), mandatory **/
			String PROD_BEZ_MAN = "ProdBezMan";
			/** VERTRIEB.dbo.AUFPOS.<b>PosTextN</b>, text(16) **/
			String POS_TEXT_N = "PosTextN";
			/** VERTRIEB.dbo.AUFPOS.<b>GrpTxtCd</b>, varchar(8) **/
			String GRP_TXT_CD = "GrpTxtCd";
			/** VERTRIEB.dbo.AUFPOS.<b>GrpTxtBez</b>, varchar(40) **/
			String GRP_TXT_BEZ = "GrpTxtBez";
			/** VERTRIEB.dbo.AUFPOS.<b>GrpTxtMan</b>, char(3), mandatory **/
			String GRP_TXT_MAN = "GrpTxtMan";
			/** VERTRIEB.dbo.AUFPOS.<b>UrsprCd</b>, char(4) **/
			String URSPR_CD = "UrsprCd";
			/** VERTRIEB.dbo.AUFPOS.<b>Nettowert</b>, float(8), mandatory **/
			String NETTOWERT = "Nettowert";
			/** VERTRIEB.dbo.AUFPOS.<b>Pauschal</b>, char(3), mandatory **/
			String PAUSCHAL = "Pauschal";
			/** VERTRIEB.dbo.AUFPOS.<b>Inkl</b>, char(3), mandatory **/
			String INKL = "Inkl";
			/** VERTRIEB.dbo.AUFPOS.<b>PreisMEB</b>, char(3), mandatory **/
			String PREIS_MEB = "PreisMEB";
			/** VERTRIEB.dbo.AUFPOS.<b>EP</b>, float(8) **/
			String EP = "EP";
			/** VERTRIEB.dbo.AUFPOS.<b>EP_PE</b>, float(8) **/
			String EP_PE = "EP_PE";
			/** VERTRIEB.dbo.AUFPOS.<b>DatWu</b>, datetime(8), mandatory **/
			String DAT_WU = "DatWu";
			/** VERTRIEB.dbo.AUFPOS.<b>AbschlCd</b>, char(3), mandatory **/
			String ABSCHL_CD = "AbschlCd";
			/** VERTRIEB.dbo.AUFPOS.<b>AttestCd</b>, char(3), mandatory **/
			String ATTEST_CD = "AttestCd";
			/** VERTRIEB.dbo.AUFPOS.<b>VersandArt</b>, char(3), mandatory **/
			String VERSAND_ART = "VersandArt";
			/** VERTRIEB.dbo.AUFPOS.<b>AvisFrist</b>, smallint(2) **/
			String AVIS_FRIST = "AvisFrist";
			/** VERTRIEB.dbo.AUFPOS.<b>AbwArt</b>, char(3), mandatory **/
			String ABW_ART = "AbwArt";
			/** VERTRIEB.dbo.AUFPOS.<b>AbwTyp</b>, char(3), mandatory **/
			String ABW_TYP = "AbwTyp";
			/** VERTRIEB.dbo.AUFPOS.<b>GSE_Lager</b>, char(4), mandatory **/
			String GSE_LAGER = "GSE_Lager";
			/** VERTRIEB.dbo.AUFPOS.<b>Vertreter</b>, int(4) **/
			String VERTRETER = "Vertreter";
			/** VERTRIEB.dbo.AUFPOS.<b>TourNr</b>, varchar(8) **/
			String TOUR_NR = "TourNr";
			/** VERTRIEB.dbo.AUFPOS.<b>TourDatum</b>, datetime(8) **/
			String TOUR_DATUM = "TourDatum";
			/** VERTRIEB.dbo.AUFPOS.<b>LiefMahnCd</b>, char(3), mandatory **/
			String LIEF_MAHN_CD = "LiefMahnCd";
			/** VERTRIEB.dbo.AUFPOS.<b>LiefMahnDat</b>, datetime(8) **/
			String LIEF_MAHN_DAT = "LiefMahnDat";
			/** VERTRIEB.dbo.AUFPOS.<b>TerminCd</b>, char(3) **/
			String TERMIN_CD = "TerminCd";
			/** VERTRIEB.dbo.AUFPOS.<b>StArtikel</b>, char(3) **/
			String ST_ARTIKEL = "StArtikel";
			/** VERTRIEB.dbo.AUFPOS.<b>Montage</b>, char(3) **/
			String MONTAGE = "Montage";
			/** VERTRIEB.dbo.AUFPOS.<b>Variante</b>, char(3) **/
			String VARIANTE = "Variante";
			/** VERTRIEB.dbo.AUFPOS.<b>StStufe</b>, smallint(2) **/
			String ST_STUFE = "StStufe";
			/** VERTRIEB.dbo.AUFPOS.<b>StPosNr</b>, smallint(2) **/
			String ST_POS_NR = "StPosNr";
			/** VERTRIEB.dbo.AUFPOS.<b>KOK</b>, char(3) **/
			String KOK = "KOK";
			/** VERTRIEB.dbo.AUFPOS.<b>BMHRescd1</b>, char(3) **/
			String BMH_RESCD1 = "BMHRescd1";
			/** VERTRIEB.dbo.AUFPOS.<b>BMHRescd2</b>, char(3) **/
			String BMH_RESCD2 = "BMHRescd2";
			/** VERTRIEB.dbo.AUFPOS.<b>ResDatum</b>, datetime(8) **/
			String RES_DATUM = "ResDatum";
			/** VERTRIEB.dbo.AUFPOS.<b>ResCd1</b>, char(3), mandatory **/
			String RES_CD1 = "ResCd1";
			/** VERTRIEB.dbo.AUFPOS.<b>ResCd2</b>, char(3), mandatory **/
			String RES_CD2 = "ResCd2";
			/** VERTRIEB.dbo.AUFPOS.<b>ResCd3</b>, char(3) **/
			String RES_CD3 = "ResCd3";
			/** VERTRIEB.dbo.AUFPOS.<b>ResCd4</b>, varchar(5) **/
			String RES_CD4 = "ResCd4";
			/** VERTRIEB.dbo.AUFPOS.<b>ResCd5</b>, varchar(5) **/
			String RES_CD5 = "ResCd5";
			/** VERTRIEB.dbo.AUFPOS.<b>ResCd6</b>, varchar(5) **/
			String RES_CD6 = "ResCd6";
			/** VERTRIEB.dbo.AUFPOS.<b>ResCd7</b>, char(3), mandatory **/
			String RES_CD7 = "ResCd7";
			/** VERTRIEB.dbo.AUFPOS.<b>ResCd8</b>, char(3), mandatory **/
			String RES_CD8 = "ResCd8";
			/** VERTRIEB.dbo.AUFPOS.<b>ResCd9</b>, char(3), mandatory **/
			String RES_CD9 = "ResCd9";
			/** VERTRIEB.dbo.AUFPOS.<b>Upd_Sabe</b>, varchar(10), mandatory **/
			String UPD_SABE = "Upd_Sabe";
			/** VERTRIEB.dbo.AUFPOS.<b>Add_Sabe</b>, varchar(10), mandatory **/
			String ADD_SABE = "Add_Sabe";
			/** VERTRIEB.dbo.AUFPOS.<b>Add_Date</b>, datetime(8), mandatory **/
			String ADD_DATE = "Add_Date";
			/** VERTRIEB.dbo.AUFPOS.<b>Upd_Date</b>, datetime(8), mandatory **/
			String UPD_DATE = "Upd_Date";
			/** VERTRIEB.dbo.AUFPOS.<b>Id_GSE_GBZ</b>, char(4), mandatory **/
			String ID_GSE_GBZ = "Id_GSE_GBZ";
			/** VERTRIEB.dbo.AUFPOS.<b>Rolle</b>, char(3), mandatory **/
			String ROLLE = "Rolle";
			/** VERTRIEB.dbo.AUFPOS.<b>Id_SUBJEKT</b>, int(4), mandatory **/
			String ID_SUBJEKT = "Id_SUBJEKT";
			/** VERTRIEB.dbo.AUFPOS.<b>Id_GSE_PRODUKT</b>, char(4), mandatory **/
			String ID_GSE_PRODUKT = "Id_GSE_PRODUKT";
			/** VERTRIEB.dbo.AUFPOS.<b>ProdNr</b>, varchar(25), mandatory **/
			String PROD_NR = "ProdNr";
			/** VERTRIEB.dbo.AUFPOS.<b>SteuerCd</b>, char(3), mandatory **/
			String STEUER_CD = "SteuerCd";
			/** VERTRIEB.dbo.AUFPOS.<b>Id_SUBJEKT_L</b>, int(4) **/
			String ID_SUBJEKT_L = "Id_SUBJEKT_L";
			/** VERTRIEB.dbo.AUFPOS.<b>LaufNr</b>, int(4) **/
			String LAUF_NR = "LaufNr";
			/** VERTRIEB.dbo.AUFPOS.<b>SerieNummer</b>, varchar(40) **/
			String SERIE_NUMMER = "SerieNummer";
			/** VERTRIEB.dbo.AUFPOS.<b>PosNr_T</b>, float(8) **/
			String POS_NR_T = "PosNr_T";
			/** VERTRIEB.dbo.AUFPOS.<b>Id_GSE_AB</b>, char(4) **/
			String ID_GSE_AB = "Id_GSE_AB";
			/** VERTRIEB.dbo.AUFPOS.<b>AuftrNr_AB</b>, int(4) **/
			String AUFTR_NR_AB = "AuftrNr_AB";
			/** VERTRIEB.dbo.AUFPOS.<b>PosNr_AB</b>, float(8) **/
			String POS_NR_AB = "PosNr_AB";
			/** VERTRIEB.dbo.AUFPOS.<b>Id_GSE_ABSCHL</b>, char(4) **/
			String ID_GSE_ABSCHL = "Id_GSE_ABSCHL";
			/** VERTRIEB.dbo.AUFPOS.<b>VertragNr</b>, int(4) **/
			String VERTRAG_NR = "VertragNr";
			/** VERTRIEB.dbo.AUFPOS.<b>Id_SUBJEKT_ABSCHL</b>, int(4) **/
			String ID_SUBJEKT_ABSCHL = "Id_SUBJEKT_ABSCHL";
			/** VERTRIEB.dbo.AUFPOS.<b>PosNr_ABSCHL</b>, smallint(2) **/
			String POS_NR_ABSCHL = "PosNr_ABSCHL";
			/** VERTRIEB.dbo.AUFPOS.<b>NSeite</b>, char(3) **/
			String N_SEITE = "NSeite";
			/** VERTRIEB.dbo.AUFPOS.<b>ZTVonPos</b>, float(8) **/
			String ZT_VON_POS = "ZTVonPos";
			/** VERTRIEB.dbo.AUFPOS.<b>ZTBisPos</b>, float(8) **/
			String ZT_BIS_POS = "ZTBisPos";
			/** VERTRIEB.dbo.AUFPOS.<b>TextVonPos</b>, float(8) **/
			String TEXT_VON_POS = "TextVonPos";
			/** VERTRIEB.dbo.AUFPOS.<b>TextBisPos</b>, float(8) **/
			String TEXT_BIS_POS = "TextBisPos";
			/** VERTRIEB.dbo.AUFPOS.<b>Add_Id_GSE</b>, varchar(4) **/
			String ADD_ID_GSE = "Add_Id_GSE";
			/** VERTRIEB.dbo.AUFPOS.<b>Add_FormNr</b>, int(4) **/
			String ADD_FORM_NR = "Add_FormNr";
			/** VERTRIEB.dbo.AUFPOS.<b>Add_Stufe</b>, char(3) **/
			String ADD_STUFE = "Add_Stufe";
			/** VERTRIEB.dbo.AUFPOS.<b>Id_GSE_DupVon</b>, varchar(4) **/
			String ID_GSE_DUP_VON = "Id_GSE_DupVon";
			/** VERTRIEB.dbo.AUFPOS.<b>AuftrNr_DupVon</b>, int(4) **/
			String AUFTR_NR_DUP_VON = "AuftrNr_DupVon";
			/** VERTRIEB.dbo.AUFPOS.<b>PosNr_DupVon</b>, float(8) **/
			String POS_NR_DUP_VON = "PosNr_DupVon";
			/** VERTRIEB.dbo.AUFPOS.<b>Id_GSE_ZUT</b>, varchar(4) **/
			String ID_GSE_ZUT = "Id_GSE_ZUT";
			/** VERTRIEB.dbo.AUFPOS.<b>Id_SUBJEKT_ZUT</b>, int(4) **/
			String ID_SUBJEKT_ZUT = "Id_SUBJEKT_ZUT";
			/** VERTRIEB.dbo.AUFPOS.<b>Rolle_ZUT</b>, char(3) **/
			String ROLLE_ZUT = "Rolle_ZUT";
			/** VERTRIEB.dbo.AUFPOS.<b>CdTyp_ZUT</b>, char(3) **/
			String CD_TYP_ZUT = "CdTyp_ZUT";
			/** VERTRIEB.dbo.AUFPOS.<b>OrderNR</b>, int(4) **/
			String ORDER_NR = "OrderNR";
			/** VERTRIEB.dbo.AUFPOS.<b>Gewicht</b>, float(8) **/
			String GEWICHT = "Gewicht";
			/** VERTRIEB.dbo.AUFPOS.<b>Volumen</b>, float(8) **/
			String VOLUMEN = "Volumen";
			/** VERTRIEB.dbo.AUFPOS.<b>DrMess</b>, float(8) **/
			String DR_MESS = "DrMess";
			/** VERTRIEB.dbo.AUFPOS.<b>DimCd</b>, char(3) **/
			String DIM_CD = "DimCd";
			/** VERTRIEB.dbo.AUFPOS.<b>Origlaenge</b>, float(8) **/
			String ORIGLAENGE = "Origlaenge";
			/** VERTRIEB.dbo.AUFPOS.<b>Origbreite</b>, float(8) **/
			String ORIGBREITE = "Origbreite";
			/** VERTRIEB.dbo.AUFPOS.<b>Orighoehe</b>, float(8) **/
			String ORIGHOEHE = "Orighoehe";
			/** VERTRIEB.dbo.AUFPOS.<b>Origtiefe</b>, float(8) **/
			String ORIGTIEFE = "Origtiefe";
			/** VERTRIEB.dbo.AUFPOS.<b>Produkt_Typ</b>, char(3) **/
			String PRODUKT_TYP = "Produkt_Typ";
			/** VERTRIEB.dbo.AUFPOS.<b>Norm_def</b>, char(3) **/
			String NORM_DEF = "Norm_def";
			/** VERTRIEB.dbo.AUFPOS.<b>Prod_Dichte</b>, float(8) **/
			String PROD_DICHTE = "Prod_Dichte";
			/** VERTRIEB.dbo.AUFPOS.<b>WaermeDW</b>, float(8) **/
			String WAERME_DW = "WaermeDW";
			/** VERTRIEB.dbo.AUFPOS.<b>Micronwert</b>, float(8) **/
			String MICRONWERT = "Micronwert";
			/** VERTRIEB.dbo.AUFPOS.<b>Form_Typ</b>, char(3) **/
			String FORM_TYP = "Form_Typ";
			/** VERTRIEB.dbo.AUFPOS.<b>Laenge_Tol</b>, char(3) **/
			String LAENGE_TOL = "Laenge_Tol";
			/** VERTRIEB.dbo.AUFPOS.<b>Breite_Tol</b>, char(3) **/
			String BREITE_TOL = "Breite_Tol";
			/** VERTRIEB.dbo.AUFPOS.<b>Hoehe_Tol</b>, char(3) **/
			String HOEHE_TOL = "Hoehe_Tol";
			/** VERTRIEB.dbo.AUFPOS.<b>Prod_Aufb_Bez_01</b>, char(3) **/
			String PROD_AUFB_BEZ_01 = "Prod_Aufb_Bez_01";
			/** VERTRIEB.dbo.AUFPOS.<b>Prod_Aufb_Wert_01</b>, float(8) **/
			String PROD_AUFB_WERT_01 = "Prod_Aufb_Wert_01";
			/** VERTRIEB.dbo.AUFPOS.<b>Prod_Aufb_Bez_02</b>, char(3) **/
			String PROD_AUFB_BEZ_02 = "Prod_Aufb_Bez_02";
			/** VERTRIEB.dbo.AUFPOS.<b>Prod_Aufb_Wert_02</b>, float(8) **/
			String PROD_AUFB_WERT_02 = "Prod_Aufb_Wert_02";
			/** VERTRIEB.dbo.AUFPOS.<b>Prod_Aufb_Bez_03</b>, char(3) **/
			String PROD_AUFB_BEZ_03 = "Prod_Aufb_Bez_03";
			/** VERTRIEB.dbo.AUFPOS.<b>Prod_Aufb_Wert_03</b>, float(8) **/
			String PROD_AUFB_WERT_03 = "Prod_Aufb_Wert_03";
			/** VERTRIEB.dbo.AUFPOS.<b>Prod_Aufb_Bez_04</b>, char(3) **/
			String PROD_AUFB_BEZ_04 = "Prod_Aufb_Bez_04";
			/** VERTRIEB.dbo.AUFPOS.<b>Prod_Aufb_Wert_04</b>, float(8) **/
			String PROD_AUFB_WERT_04 = "Prod_Aufb_Wert_04";
			/** VERTRIEB.dbo.AUFPOS.<b>Prod_Aufb_Bez_05</b>, char(3) **/
			String PROD_AUFB_BEZ_05 = "Prod_Aufb_Bez_05";
			/** VERTRIEB.dbo.AUFPOS.<b>Prod_Aufb_Wert_05</b>, float(8) **/
			String PROD_AUFB_WERT_05 = "Prod_Aufb_Wert_05";
			/** VERTRIEB.dbo.AUFPOS.<b>Prod_Aufb_Bez_06</b>, char(3) **/
			String PROD_AUFB_BEZ_06 = "Prod_Aufb_Bez_06";
			/** VERTRIEB.dbo.AUFPOS.<b>Prod_Aufb_Wert_06</b>, float(8) **/
			String PROD_AUFB_WERT_06 = "Prod_Aufb_Wert_06";
			/** VERTRIEB.dbo.AUFPOS.<b>Prod_Farbe_01</b>, char(3) **/
			String PROD_FARBE_01 = "Prod_Farbe_01";
			/** VERTRIEB.dbo.AUFPOS.<b>Oberflaeche</b>, char(3) **/
			String OBERFLAECHE = "Oberflaeche";
			/** VERTRIEB.dbo.AUFPOS.<b>Unterflaeche</b>, char(3) **/
			String UNTERFLAECHE = "Unterflaeche";
			/** VERTRIEB.dbo.AUFPOS.<b>Spez_Bearb_01</b>, varchar(40) **/
			String SPEZ_BEARB_01 = "Spez_Bearb_01";
			/** VERTRIEB.dbo.AUFPOS.<b>Spez_Bearb_02</b>, varchar(40) **/
			String SPEZ_BEARB_02 = "Spez_Bearb_02";
			/** VERTRIEB.dbo.AUFPOS.<b>Spez_Bearb_03</b>, varchar(40) **/
			String SPEZ_BEARB_03 = "Spez_Bearb_03";
			/** VERTRIEB.dbo.AUFPOS.<b>TRA_Mittel</b>, char(3) **/
			String TRA_MITTEL = "TRA_Mittel";
			/** VERTRIEB.dbo.AUFPOS.<b>Verpack_art</b>, char(3) **/
			String VERPACK_ART = "Verpack_art";
			/** VERTRIEB.dbo.AUFPOS.<b>Verpack_Stuktur</b>, varchar(40) **/
			String VERPACK_STUKTUR = "Verpack_Stuktur";
			/** VERTRIEB.dbo.AUFPOS.<b>Auszeichn_Etikett</b>, char(3) **/
			String AUSZEICHN_ETIKETT = "Auszeichn_Etikett";
			/** VERTRIEB.dbo.AUFPOS.<b>Mat_Kosten</b>, int(4) **/
			String MAT_KOSTEN = "Mat_Kosten";
			/** VERTRIEB.dbo.AUFPOS.<b>Energie</b>, int(4) **/
			String ENERGIE = "Energie";
			/** VERTRIEB.dbo.AUFPOS.<b>Oekologie</b>, int(4) **/
			String OEKOLOGIE = "Oekologie";
			/** VERTRIEB.dbo.AUFPOS.<b>Fert_Kosten</b>, int(4) **/
			String FERT_KOSTEN = "Fert_Kosten";
			/** VERTRIEB.dbo.AUFPOS.<b>Verp_Kosten</b>, int(4) **/
			String VERP_KOSTEN = "Verp_Kosten";
			/** VERTRIEB.dbo.AUFPOS.<b>Vers_Kosten</b>, int(4) **/
			String VERS_KOSTEN = "Vers_Kosten";
			/** VERTRIEB.dbo.AUFPOS.<b>Prod_Laenge</b>, int(4) **/
			String PROD_LAENGE = "Prod_Laenge";
			/** VERTRIEB.dbo.AUFPOS.<b>Prod_Breite</b>, int(4) **/
			String PROD_BREITE = "Prod_Breite";
			/** VERTRIEB.dbo.AUFPOS.<b>Anz_Bahnen</b>, int(4) **/
			String ANZ_BAHNEN = "Anz_Bahnen";
			/** VERTRIEB.dbo.AUFPOS.<b>Prod_Hoehe</b>, int(4) **/
			String PROD_HOEHE = "Prod_Hoehe";
			/** VERTRIEB.dbo.AUFPOS.<b>Prod_Dichte_P</b>, int(4) **/
			String PROD_DICHTE_P = "Prod_Dichte_P";
			/** VERTRIEB.dbo.AUFPOS.<b>Max_GlasMG</b>, int(4) **/
			String MAX_GLAS_MG = "Max_GlasMG";
			/** VERTRIEB.dbo.AUFPOS.<b>Anz_Spinner</b>, int(4) **/
			String ANZ_SPINNER = "Anz_Spinner";
			/** VERTRIEB.dbo.AUFPOS.<b>Max_AbGeschw</b>, int(4) **/
			String MAX_AB_GESCHW = "Max_AbGeschw";
			/** VERTRIEB.dbo.AUFPOS.<b>AbGeschw_Vent1</b>, int(4) **/
			String AB_GESCHW_VENT1 = "AbGeschw_Vent1";
			/** VERTRIEB.dbo.AUFPOS.<b>AbGeschw_Vent2</b>, int(4) **/
			String AB_GESCHW_VENT2 = "AbGeschw_Vent2";
			/** VERTRIEB.dbo.AUFPOS.<b>AbGeschw_Vent3</b>, int(4) **/
			String AB_GESCHW_VENT3 = "AbGeschw_Vent3";
			/** VERTRIEB.dbo.AUFPOS.<b>Temp_Bre1</b>, int(4) **/
			String TEMP_BRE1 = "Temp_Bre1";
			/** VERTRIEB.dbo.AUFPOS.<b>Temp_Bre2</b>, int(4) **/
			String TEMP_BRE2 = "Temp_Bre2";
			/** VERTRIEB.dbo.AUFPOS.<b>Temp_Bre3</b>, int(4) **/
			String TEMP_BRE3 = "Temp_Bre3";
			/** VERTRIEB.dbo.AUFPOS.<b>Ofen_Vent1</b>, int(4) **/
			String OFEN_VENT1 = "Ofen_Vent1";
			/** VERTRIEB.dbo.AUFPOS.<b>Ofen_Vent2</b>, int(4) **/
			String OFEN_VENT2 = "Ofen_Vent2";
			/** VERTRIEB.dbo.AUFPOS.<b>Ofen_Vent3</b>, int(4) **/
			String OFEN_VENT3 = "Ofen_Vent3";
			/** VERTRIEB.dbo.AUFPOS.<b>Komp_RollenW</b>, int(4) **/
			String KOMP_ROLLEN_W = "Komp_RollenW";
			/** VERTRIEB.dbo.AUFPOS.<b>Gewicht_Aufl</b>, int(4) **/
			String GEWICHT_AUFL = "Gewicht_Aufl";
			/** VERTRIEB.dbo.AUFPOS.<b>GlasMg</b>, int(4) **/
			String GLAS_MG = "GlasMg";
			/** VERTRIEB.dbo.AUFPOS.<b>BandGeschw</b>, int(4) **/
			String BAND_GESCHW = "BandGeschw";
			/** VERTRIEB.dbo.AUFPOS.<b>Besch_1_Mat</b>, int(4) **/
			String BESCH_1_MAT = "Besch_1_Mat";
			/** VERTRIEB.dbo.AUFPOS.<b>Besch_1_Menge</b>, float(8) **/
			String BESCH_1_MENGE = "Besch_1_Menge";
			/** VERTRIEB.dbo.AUFPOS.<b>Besch_2_Mat</b>, int(4) **/
			String BESCH_2_MAT = "Besch_2_Mat";
			/** VERTRIEB.dbo.AUFPOS.<b>Besch_2_Menge</b>, float(8) **/
			String BESCH_2_MENGE = "Besch_2_Menge";
			/** VERTRIEB.dbo.AUFPOS.<b>Besch_3_Mat</b>, int(4) **/
			String BESCH_3_MAT = "Besch_3_Mat";
			/** VERTRIEB.dbo.AUFPOS.<b>Besch_3_Menge</b>, float(8) **/
			String BESCH_3_MENGE = "Besch_3_Menge";
			/** VERTRIEB.dbo.AUFPOS.<b>Werk</b>, char(3) **/
			String WERK = "Werk";
			/** VERTRIEB.dbo.AUFPOS.<b>WerkBez</b>, char(3) **/
			String WERK_BEZ = "WerkBez";
			/** VERTRIEB.dbo.AUFPOS.<b>Produkthierarchie</b>, char(3) **/
			String PRODUKTHIERARCHIE = "Produkthierarchie";
			/** VERTRIEB.dbo.AUFPOS.<b>WarenGrp</b>, char(3) **/
			String WAREN_GRP = "WarenGrp";
			/** VERTRIEB.dbo.AUFPOS.<b>Reserve_01</b>, char(3) **/
			String RESERVE_01 = "Reserve_01";
			/** VERTRIEB.dbo.AUFPOS.<b>Reserve_02</b>, char(3) **/
			String RESERVE_02 = "Reserve_02";
			/** VERTRIEB.dbo.AUFPOS.<b>Reserve_03</b>, char(3) **/
			String RESERVE_03 = "Reserve_03";
			/** VERTRIEB.dbo.AUFPOS.<b>Reserve_04</b>, char(3) **/
			String RESERVE_04 = "Reserve_04";
			/** VERTRIEB.dbo.AUFPOS.<b>Reserve_05</b>, char(3) **/
			String RESERVE_05 = "Reserve_05";
			/** VERTRIEB.dbo.AUFPOS.<b>Reserve_06</b>, varchar(40) **/
			String RESERVE_06 = "Reserve_06";
			/** VERTRIEB.dbo.AUFPOS.<b>Reserve_07</b>, varchar(40) **/
			String RESERVE_07 = "Reserve_07";
			/** VERTRIEB.dbo.AUFPOS.<b>Reserve_08</b>, varchar(40) **/
			String RESERVE_08 = "Reserve_08";
			/** VERTRIEB.dbo.AUFPOS.<b>Reserve_09</b>, varchar(40) **/
			String RESERVE_09 = "Reserve_09";
			/** VERTRIEB.dbo.AUFPOS.<b>Reserve_10</b>, varchar(40) **/
			String RESERVE_10 = "Reserve_10";
			/** VERTRIEB.dbo.AUFPOS.<b>Reserve_11</b>, datetime(8) **/
			String RESERVE_11 = "Reserve_11";
			/** VERTRIEB.dbo.AUFPOS.<b>Reserve_12</b>, datetime(8) **/
			String RESERVE_12 = "Reserve_12";
			/** VERTRIEB.dbo.AUFPOS.<b>KONF_ANZ</b>, float(8) **/
			String KONF_ANZ = "KONF_ANZ";
			/** VERTRIEB.dbo.AUFPOS.<b>ATTCHANUA</b>, char(3) **/
			String ATTCHANUA = "ATTCHANUA";
			/** VERTRIEB.dbo.AUFPOS.<b>Rohstoff_Kosten</b>, float(8) **/
			String ROHSTOFF_KOSTEN = "Rohstoff_Kosten";
			/** VERTRIEB.dbo.AUFPOS.<b>Bindemittel_Kosten</b>, float(8) **/
			String BINDEMITTEL_KOSTEN = "Bindemittel_Kosten";
			/** VERTRIEB.dbo.AUFPOS.<b>Energie_Kosten</b>, float(8) **/
			String ENERGIE_KOSTEN = "Energie_Kosten";
			/** VERTRIEB.dbo.AUFPOS.<b>Fert_EinzelKosten</b>, float(8) **/
			String FERT_EINZEL_KOSTEN = "Fert_EinzelKosten";
			/** VERTRIEB.dbo.AUFPOS.<b>Fert_Gemeinkosten</b>, float(8) **/
			String FERT_GEMEINKOSTEN = "Fert_Gemeinkosten";
			/** VERTRIEB.dbo.AUFPOS.<b>Ausschuss_Kosten</b>, float(8) **/
			String AUSSCHUSS_KOSTEN = "Ausschuss_Kosten";
			/** VERTRIEB.dbo.AUFPOS.<b>Zusatzkosten</b>, float(8) **/
			String ZUSATZKOSTEN = "Zusatzkosten";
			/** VERTRIEB.dbo.AUFPOS.<b>Beschichtung_Kosten1</b>, float(8) **/
			String BESCHICHTUNG_KOSTEN1 = "Beschichtung_Kosten1";
			/** VERTRIEB.dbo.AUFPOS.<b>Beschichtung_Kosten2</b>, float(8) **/
			String BESCHICHTUNG_KOSTEN2 = "Beschichtung_Kosten2";
			/** VERTRIEB.dbo.AUFPOS.<b>Verpackung_Kosten1</b>, float(8) **/
			String VERPACKUNG_KOSTEN1 = "Verpackung_Kosten1";
			/** VERTRIEB.dbo.AUFPOS.<b>Palettierung_Kosten</b>, float(8) **/
			String PALETTIERUNG_KOSTEN = "Palettierung_Kosten";
			/** VERTRIEB.dbo.AUFPOS.<b>Reservere_13</b>, float(8) **/
			String RESERVERE_13 = "Reservere_13";
			/** VERTRIEB.dbo.AUFPOS.<b>Reservere_14</b>, float(8) **/
			String RESERVERE_14 = "Reservere_14";
			/** VERTRIEB.dbo.AUFPOS.<b>Reservere_15</b>, float(8) **/
			String RESERVERE_15 = "Reservere_15";
			/** VERTRIEB.dbo.AUFPOS.<b>Reservere_16</b>, float(8) **/
			String RESERVERE_16 = "Reservere_16";
			/** VERTRIEB.dbo.AUFPOS.<b>Reservere_17</b>, float(8) **/
			String RESERVERE_17 = "Reservere_17";
			/** VERTRIEB.dbo.AUFPOS.<b>HK_Saegefigur</b>, char(3) **/
			String HK_SAEGEFIGUR = "HK_Saegefigur";
			/** VERTRIEB.dbo.AUFPOS.<b>HK_Winkel_A</b>, int(4) **/
			String HK_WINKEL_A = "HK_Winkel_A";
			/** VERTRIEB.dbo.AUFPOS.<b>HK_Winkel_B</b>, int(4) **/
			String HK_WINKEL_B = "HK_Winkel_B";
			/** VERTRIEB.dbo.AUFPOS.<b>HK_Toleranz</b>, char(3) **/
			String HK_TOLERANZ = "HK_Toleranz";
		}

		/** VERTRIEB.dbo.<b>AUFTRAG</b>, 99 Columns **/
		public interface AuftragTable {
			/** VERTRIEB.dbo.<b>AUFTRAG</b> **/
			String _TN = "VERTRIEB.dbo.AUFTRAG";
			/** VERTRIEB.dbo.AUFTRAG.<b>Id_GSE</b>, char(4), mandatory **/
			String ID_GSE = "Id_GSE";
			/** VERTRIEB.dbo.AUFTRAG.<b>AuftrNr</b>, int(4), mandatory **/
			String AUFTR_NR = "AuftrNr";
			/** VERTRIEB.dbo.AUFTRAG.<b>AufDat</b>, datetime(8), mandatory **/
			String AUF_DAT = "AufDat";
			/** VERTRIEB.dbo.AUFTRAG.<b>KondDat</b>, datetime(8), mandatory **/
			String KOND_DAT = "KondDat";
			/** VERTRIEB.dbo.AUFTRAG.<b>DatBewer</b>, datetime(8) **/
			String DAT_BEWER = "DatBewer";
			/** VERTRIEB.dbo.AUFTRAG.<b>AufTot</b>, float(8), mandatory **/
			String AUF_TOT = "AufTot";
			/** VERTRIEB.dbo.AUFTRAG.<b>GewTot</b>, float(8), mandatory **/
			String GEW_TOT = "GewTot";
			/** VERTRIEB.dbo.AUFTRAG.<b>KredLim</b>, float(8) **/
			String KRED_LIM = "KredLim";
			/** VERTRIEB.dbo.AUFTRAG.<b>BestellArt</b>, char(3), mandatory **/
			String BESTELL_ART = "BestellArt";
			/** VERTRIEB.dbo.AUFTRAG.<b>NeuBewert</b>, char(3), mandatory **/
			String NEU_BEWERT = "NeuBewert";
			/** VERTRIEB.dbo.AUFTRAG.<b>KursArt</b>, char(3) **/
			String KURS_ART = "KursArt";
			/** VERTRIEB.dbo.AUFTRAG.<b>Kurs</b>, float(8) **/
			String KURS = "Kurs";
			/** VERTRIEB.dbo.AUFTRAG.<b>ASplitt</b>, char(3), mandatory **/
			String A_SPLITT = "ASplitt";
			/** VERTRIEB.dbo.AUFTRAG.<b>BewertCd</b>, char(3), mandatory **/
			String BEWERT_CD = "BewertCd";
			/** VERTRIEB.dbo.AUFTRAG.<b>KdlUeb</b>, char(3), mandatory **/
			String KDL_UEB = "KdlUeb";
			/** VERTRIEB.dbo.AUFTRAG.<b>Referenz</b>, varchar(50) **/
			String REFERENZ = "Referenz";
			/** VERTRIEB.dbo.AUFTRAG.<b>Status</b>, char(3), mandatory **/
			String STATUS = "Status";
			/** VERTRIEB.dbo.AUFTRAG.<b>Kopftext</b>, text(16) **/
			String KOPFTEXT = "Kopftext";
			/** VERTRIEB.dbo.AUFTRAG.<b>Fusstext</b>, text(16) **/
			String FUSSTEXT = "Fusstext";
			/** VERTRIEB.dbo.AUFTRAG.<b>Sabe</b>, varchar(10) **/
			String SABE = "Sabe";
			/** VERTRIEB.dbo.AUFTRAG.<b>BelegArt</b>, char(3), mandatory **/
			String BELEG_ART = "BelegArt";
			/** VERTRIEB.dbo.AUFTRAG.<b>MLiefAdr</b>, varchar(255) **/
			String M_LIEF_ADR = "MLiefAdr";
			/** VERTRIEB.dbo.AUFTRAG.<b>MRechAdr</b>, varchar(255) **/
			String M_RECH_ADR = "MRechAdr";
			/** VERTRIEB.dbo.AUFTRAG.<b>BestellNr</b>, varchar(50) **/
			String BESTELL_NR = "BestellNr";
			/** VERTRIEB.dbo.AUFTRAG.<b>StornoGr</b>, char(3) **/
			String STORNO_GR = "StornoGr";
			/** VERTRIEB.dbo.AUFTRAG.<b>ErsatzProdCd</b>, char(3), mandatory **/
			String ERSATZ_PROD_CD = "ErsatzProdCd";
			/** VERTRIEB.dbo.AUFTRAG.<b>FaktPerCd</b>, char(3), mandatory **/
			String FAKT_PER_CD = "FaktPerCd";
			/** VERTRIEB.dbo.AUFTRAG.<b>AnzZusFKop</b>, smallint(2) **/
			String ANZ_ZUS_F_KOP = "AnzZusFKop";
			/** VERTRIEB.dbo.AUFTRAG.<b>AnzZusLKop</b>, smallint(2) **/
			String ANZ_ZUS_L_KOP = "AnzZusLKop";
			/** VERTRIEB.dbo.AUFTRAG.<b>AnzZusAKop</b>, smallint(2) **/
			String ANZ_ZUS_A_KOP = "AnzZusAKop";
			/** VERTRIEB.dbo.AUFTRAG.<b>SpedVorsch</b>, char(3) **/
			String SPED_VORSCH = "SpedVorsch";
			/** VERTRIEB.dbo.AUFTRAG.<b>SteuerCd</b>, char(3), mandatory **/
			String STEUER_CD = "SteuerCd";
			/** VERTRIEB.dbo.AUFTRAG.<b>LsTermPos</b>, char(3), mandatory **/
			String LS_TERM_POS = "LsTermPos";
			/** VERTRIEB.dbo.AUFTRAG.<b>AufBestCd</b>, char(3) **/
			String AUF_BEST_CD = "AufBestCd";
			/** VERTRIEB.dbo.AUFTRAG.<b>AufBestDat</b>, datetime(8) **/
			String AUF_BEST_DAT = "AufBestDat";
			/** VERTRIEB.dbo.AUFTRAG.<b>Vert1</b>, varchar(8) **/
			String VERT1 = "Vert1";
			/** VERTRIEB.dbo.AUFTRAG.<b>Vert2</b>, varchar(8) **/
			String VERT2 = "Vert2";
			/** VERTRIEB.dbo.AUFTRAG.<b>DatWu</b>, datetime(8), mandatory **/
			String DAT_WU = "DatWu";
			/** VERTRIEB.dbo.AUFTRAG.<b>AbschlCd</b>, char(3), mandatory **/
			String ABSCHL_CD = "AbschlCd";
			/** VERTRIEB.dbo.AUFTRAG.<b>AttestCd</b>, char(3), mandatory **/
			String ATTEST_CD = "AttestCd";
			/** VERTRIEB.dbo.AUFTRAG.<b>VersandArt</b>, char(3), mandatory **/
			String VERSAND_ART = "VersandArt";
			/** VERTRIEB.dbo.AUFTRAG.<b>AvisFrist</b>, smallint(2) **/
			String AVIS_FRIST = "AvisFrist";
			/** VERTRIEB.dbo.AUFTRAG.<b>AbwArt</b>, char(3), mandatory **/
			String ABW_ART = "AbwArt";
			/** VERTRIEB.dbo.AUFTRAG.<b>AbwTyp</b>, char(3), mandatory **/
			String ABW_TYP = "AbwTyp";
			/** VERTRIEB.dbo.AUFTRAG.<b>GSE_Lager</b>, char(4), mandatory **/
			String GSE_LAGER = "GSE_Lager";
			/** VERTRIEB.dbo.AUFTRAG.<b>Vertreter</b>, int(4) **/
			String VERTRETER = "Vertreter";
			/** VERTRIEB.dbo.AUFTRAG.<b>TourNr</b>, varchar(8) **/
			String TOUR_NR = "TourNr";
			/** VERTRIEB.dbo.AUFTRAG.<b>TourDatum</b>, datetime(8) **/
			String TOUR_DATUM = "TourDatum";
			/** VERTRIEB.dbo.AUFTRAG.<b>LiefMahnCd</b>, char(3), mandatory **/
			String LIEF_MAHN_CD = "LiefMahnCd";
			/** VERTRIEB.dbo.AUFTRAG.<b>Id_SUBJEKT_L</b>, int(4) **/
			String ID_SUBJEKT_L = "Id_SUBJEKT_L";
			/** VERTRIEB.dbo.AUFTRAG.<b>LaufNr</b>, int(4) **/
			String LAUF_NR = "LaufNr";
			/** VERTRIEB.dbo.AUFTRAG.<b>GBZResDatum</b>, datetime(8) **/
			String GBZ_RES_DATUM = "GBZResDatum";
			/** VERTRIEB.dbo.AUFTRAG.<b>GBZResCd1</b>, char(3), mandatory **/
			String GBZ_RES_CD1 = "GBZResCd1";
			/** VERTRIEB.dbo.AUFTRAG.<b>GBZResCd2</b>, char(3), mandatory **/
			String GBZ_RES_CD2 = "GBZResCd2";
			/** VERTRIEB.dbo.AUFTRAG.<b>GBZResCd3</b>, char(3) **/
			String GBZ_RES_CD3 = "GBZResCd3";
			/** VERTRIEB.dbo.AUFTRAG.<b>GBZResCd4</b>, char(5) **/
			String GBZ_RES_CD4 = "GBZResCd4";
			/** VERTRIEB.dbo.AUFTRAG.<b>GBZResCd5</b>, char(5) **/
			String GBZ_RES_CD5 = "GBZResCd5";
			/** VERTRIEB.dbo.AUFTRAG.<b>GBZResCd6</b>, varchar(20) **/
			String GBZ_RES_CD6 = "GBZResCd6";
			/** VERTRIEB.dbo.AUFTRAG.<b>Upd_Sabe</b>, varchar(10), mandatory **/
			String UPD_SABE = "Upd_Sabe";
			/** VERTRIEB.dbo.AUFTRAG.<b>Add_Sabe</b>, varchar(10), mandatory **/
			String ADD_SABE = "Add_Sabe";
			/** VERTRIEB.dbo.AUFTRAG.<b>Add_Date</b>, datetime(8), mandatory **/
			String ADD_DATE = "Add_Date";
			/** VERTRIEB.dbo.AUFTRAG.<b>Upd_Date</b>, datetime(8), mandatory **/
			String UPD_DATE = "Upd_Date";
			/** VERTRIEB.dbo.AUFTRAG.<b>Prog</b>, varchar(8) **/
			String PROG = "Prog";
			/** VERTRIEB.dbo.AUFTRAG.<b>Herkunft</b>, varchar(10) **/
			String HERKUNFT = "Herkunft";
			/** VERTRIEB.dbo.AUFTRAG.<b>Reserve1</b>, varchar(10) **/
			String RESERVE1 = "Reserve1";
			/** VERTRIEB.dbo.AUFTRAG.<b>Reserve2</b>, char(3) **/
			String RESERVE2 = "Reserve2";
			/** VERTRIEB.dbo.AUFTRAG.<b>Reserve3</b>, datetime(8) **/
			String RESERVE3 = "Reserve3";
			/** VERTRIEB.dbo.AUFTRAG.<b>Reserve4</b>, varchar(6) **/
			String RESERVE4 = "Reserve4";
			/** VERTRIEB.dbo.AUFTRAG.<b>Reserve5</b>, int(4) **/
			String RESERVE5 = "Reserve5";
			/** VERTRIEB.dbo.AUFTRAG.<b>Id_GSE_GBZ</b>, char(4), mandatory **/
			String ID_GSE_GBZ = "Id_GSE_GBZ";
			/** VERTRIEB.dbo.AUFTRAG.<b>Rolle</b>, char(3), mandatory **/
			String ROLLE = "Rolle";
			/** VERTRIEB.dbo.AUFTRAG.<b>Id_SUBJEKT</b>, int(4), mandatory **/
			String ID_SUBJEKT = "Id_SUBJEKT";
			/** VERTRIEB.dbo.AUFTRAG.<b>Name_PLZ_Ort</b>, varchar(78) **/
			String NAME_PLZ_ORT = "Name_PLZ_Ort";
			/** VERTRIEB.dbo.AUFTRAG.<b>Id_SUBJEKT_K</b>, int(4) **/
			String ID_SUBJEKT_K = "Id_SUBJEKT_K";
			/** VERTRIEB.dbo.AUFTRAG.<b>Rolle_F</b>, char(3) **/
			String ROLLE_F = "Rolle_F";
			/** VERTRIEB.dbo.AUFTRAG.<b>Id_SUBJEKT_F</b>, int(4) **/
			String ID_SUBJEKT_F = "Id_SUBJEKT_F";
			/** VERTRIEB.dbo.AUFTRAG.<b>Id_SUBJEKT_KA</b>, int(4), mandatory **/
			String ID_SUBJEKT_KA = "Id_SUBJEKT_KA";
			/** VERTRIEB.dbo.AUFTRAG.<b>LaufNr_KA</b>, smallint(2), mandatory **/
			String LAUF_NR_KA = "LaufNr_KA";
			/** VERTRIEB.dbo.AUFTRAG.<b>Id_SUBJEKT_RA</b>, int(4), mandatory **/
			String ID_SUBJEKT_RA = "Id_SUBJEKT_RA";
			/** VERTRIEB.dbo.AUFTRAG.<b>LaufNr_RA</b>, smallint(2), mandatory **/
			String LAUF_NR_RA = "LaufNr_RA";
			/** VERTRIEB.dbo.AUFTRAG.<b>Id_WAEHRUNG</b>, varchar(4), mandatory **/
			String ID_WAEHRUNG = "Id_WAEHRUNG";
			/** VERTRIEB.dbo.AUFTRAG.<b>ZahlkondId</b>, int(4), mandatory **/
			String ZAHLKOND_ID = "ZahlkondId";
			/** VERTRIEB.dbo.AUFTRAG.<b>Id_GSE_PA</b>, char(4) **/
			String ID_GSE_PA = "Id_GSE_PA";
			/** VERTRIEB.dbo.AUFTRAG.<b>PAuftrNr</b>, varchar(16) **/
			String P_AUFTR_NR = "PAuftrNr";
			/** VERTRIEB.dbo.AUFTRAG.<b>Id_GSE_DupVon</b>, char(4) **/
			String ID_GSE_DUP_VON = "Id_GSE_DupVon";
			/** VERTRIEB.dbo.AUFTRAG.<b>AuftrNr_DupVon</b>, int(4) **/
			String AUFTR_NR_DUP_VON = "AuftrNr_DupVon";
			/** VERTRIEB.dbo.AUFTRAG.<b>AufHerkunft</b>, char(3) **/
			String AUF_HERKUNFT = "AufHerkunft";
			/** VERTRIEB.dbo.AUFTRAG.<b>Bsv_Id_GSE</b>, char(4) **/
			String BSV_ID_GSE = "Bsv_Id_GSE";
			/** VERTRIEB.dbo.AUFTRAG.<b>Bsv_Id_BSV</b>, int(4) **/
			String BSV_ID_BSV = "Bsv_Id_BSV";
			/** VERTRIEB.dbo.AUFTRAG.<b>Ausfuhr</b>, char(3) **/
			String AUSFUHR = "Ausfuhr";
			/** VERTRIEB.dbo.AUFTRAG.<b>RueckgabeGrund</b>, char(3) **/
			String RUECKGABE_GRUND = "RueckgabeGrund";
			/** VERTRIEB.dbo.AUFTRAG.<b>FormNr_DupVon</b>, int(4) **/
			String FORM_NR_DUP_VON = "FormNr_DupVon";
			/** VERTRIEB.dbo.AUFTRAG.<b>Stufe_DupVon</b>, char(3) **/
			String STUFE_DUP_VON = "Stufe_DupVon";
			/** VERTRIEB.dbo.AUFTRAG.<b>LastBewertMod</b>, char(3) **/
			String LAST_BEWERT_MOD = "LastBewertMod";
			/** VERTRIEB.dbo.AUFTRAG.<b>DatVerbuch</b>, datetime(8) **/
			String DAT_VERBUCH = "DatVerbuch";
			/** VERTRIEB.dbo.AUFTRAG.<b>DatSteuer</b>, datetime(8) **/
			String DAT_STEUER = "DatSteuer";
			/** VERTRIEB.dbo.AUFTRAG.<b>KontaktPerson</b>, int(4) **/
			String KONTAKT_PERSON = "KontaktPerson";
			/** VERTRIEB.dbo.AUFTRAG.<b>Add_Monat</b>, int(4) **/
			String ADD_MONAT = "Add_Monat";
			/** VERTRIEB.dbo.AUFTRAG.<b>Add_Jahr</b>, int(4) **/
			String ADD_JAHR = "Add_Jahr";
		}

		/** VERTRIEB.dbo.<b>AuftragsKopfZusatz</b>, 225 Columns **/
		public interface AuftragsKopfZusatzTable {
			/** VERTRIEB.dbo.<b>AuftragsKopfZusatz</b> **/
			String _TN = "VERTRIEB.dbo.AuftragsKopfZusatz";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>ID_GSE</b>, varchar(4), mandatory **/
			String ID_GSE = "ID_GSE";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>AuftrNr</b>, int(4), mandatory **/
			String AUFTR_NR = "AuftrNr";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>AZusatzSDatum1</b>, datetime(8) **/
			String A_ZUSATZ_S_DATUM1 = "AZusatzSDatum1";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>AZusatzSDatum2</b>, datetime(8) **/
			String A_ZUSATZ_S_DATUM2 = "AZusatzSDatum2";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>AZusatzSAusf</b>, varchar(40) **/
			String A_ZUSATZ_S_AUSF = "AZusatzSAusf";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>AZusatzSAbd</b>, varchar(40) **/
			String A_ZUSATZ_S_ABD = "AZusatzSAbd";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>AZusatzSStk</b>, int(4) **/
			String A_ZUSATZ_S_STK = "AZusatzSStk";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>AZusatzSInfo</b>, varchar(255) **/
			String A_ZUSATZ_S_INFO = "AZusatzSInfo";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>AZusatzSBestellt</b>, varchar(8) **/
			String A_ZUSATZ_S_BESTELLT = "AZusatzSBestellt";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>AZusatzSBestaetigt</b>, varchar(8) **/
			String A_ZUSATZ_S_BESTAETIGT = "AZusatzSBestaetigt";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>AZusatzSReserviert</b>, varchar(8) **/
			String A_ZUSATZ_S_RESERVIERT = "AZusatzSReserviert";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>AZusatzSCode</b>, char(3) **/
			String A_ZUSATZ_S_CODE = "AZusatzSCode";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>AZusatzPDatum1</b>, datetime(8) **/
			String A_ZUSATZ_P_DATUM1 = "AZusatzPDatum1";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>AZusatzPDatum2</b>, datetime(8) **/
			String A_ZUSATZ_P_DATUM2 = "AZusatzPDatum2";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>AZusatzPAuftgeber</b>, varchar(40) **/
			String A_ZUSATZ_P_AUFTGEBER = "AZusatzPAuftgeber";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>AZusatzPAbd</b>, char(1) **/
			String A_ZUSATZ_P_ABD = "AZusatzPAbd";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>AZusatzPRueckw</b>, char(1) **/
			String A_ZUSATZ_P_RUECKW = "AZusatzPRueckw";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>AZusatzPStk</b>, int(4) **/
			String A_ZUSATZ_P_STK = "AZusatzPStk";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>AZusatzPInfo1</b>, varchar(255) **/
			String A_ZUSATZ_P_INFO1 = "AZusatzPInfo1";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>AZusatzPInfo2</b>, varchar(255) **/
			String A_ZUSATZ_P_INFO2 = "AZusatzPInfo2";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>AZusatzPBeendet</b>, char(1) **/
			String A_ZUSATZ_P_BEENDET = "AZusatzPBeendet";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>AZusatzPOMontage</b>, char(1) **/
			String A_ZUSATZ_PO_MONTAGE = "AZusatzPOMontage";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>AZusatzPMMontage</b>, char(1) **/
			String A_ZUSATZ_PM_MONTAGE = "AZusatzPMMontage";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>AZusatzPNLieferung</b>, char(1) **/
			String A_ZUSATZ_PN_LIEFERUNG = "AZusatzPNLieferung";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Sch_Block1</b>, char(3) **/
			String SCH_BLOCK1 = "Sch_Block1";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Sch_Block2</b>, char(3) **/
			String SCH_BLOCK2 = "Sch_Block2";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Sch_Block3</b>, char(3) **/
			String SCH_BLOCK3 = "Sch_Block3";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Sch_Block4</b>, char(3) **/
			String SCH_BLOCK4 = "Sch_Block4";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Sch_Block5</b>, char(3) **/
			String SCH_BLOCK5 = "Sch_Block5";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Sch_Block6</b>, char(3) **/
			String SCH_BLOCK6 = "Sch_Block6";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Sch_Block7</b>, char(3) **/
			String SCH_BLOCK7 = "Sch_Block7";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Sch_Block8</b>, char(3) **/
			String SCH_BLOCK8 = "Sch_Block8";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Sch_Block9</b>, char(3) **/
			String SCH_BLOCK9 = "Sch_Block9";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Sch_BlockA</b>, char(3) **/
			String SCH_BLOCK_A = "Sch_BlockA";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>AZusatzSInfo1</b>, varchar(255) **/
			String A_ZUSATZ_S_INFO1 = "AZusatzSInfo1";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>AZusatzSCode1</b>, char(3) **/
			String A_ZUSATZ_S_CODE1 = "AZusatzSCode1";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>AZusatzSWoche</b>, int(4) **/
			String A_ZUSATZ_S_WOCHE = "AZusatzSWoche";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>AZusatzSAbwArt</b>, char(3) **/
			String A_ZUSATZ_S_ABW_ART = "AZusatzSAbwArt";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>AZusatzSPreis</b>, int(4) **/
			String A_ZUSATZ_S_PREIS = "AZusatzSPreis";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>AZusatzSCode2</b>, char(3) **/
			String A_ZUSATZ_S_CODE2 = "AZusatzSCode2";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1401_1</b>, varchar(255) **/
			String TX1401_1 = "Tx1401_1";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1401_2</b>, varchar(50) **/
			String TX1401_2 = "Tx1401_2";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Nr2420</b>, varchar(30) **/
			String NR2420 = "Nr2420";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>AZusatzSCode3</b>, char(3) **/
			String A_ZUSATZ_S_CODE3 = "AZusatzSCode3";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>AZusatzSCode4</b>, char(3) **/
			String A_ZUSATZ_S_CODE4 = "AZusatzSCode4";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>nr3420</b>, varchar(30) **/
			String NR3420 = "nr3420";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>tx1401_3</b>, varchar(80) **/
			String TX1401_3 = "tx1401_3";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>nr4420</b>, varchar(30) **/
			String NR4420 = "nr4420";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>nr5420</b>, varchar(30) **/
			String NR5420 = "nr5420";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>nr6420</b>, varchar(30) **/
			String NR6420 = "nr6420";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>nr7420</b>, varchar(30) **/
			String NR7420 = "nr7420";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>nr8420</b>, varchar(30) **/
			String NR8420 = "nr8420";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>nr9420</b>, varchar(30) **/
			String NR9420 = "nr9420";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_21</b>, varchar(40) **/
			String TX1423_21 = "Tx1423_21";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_22</b>, varchar(40) **/
			String TX1423_22 = "Tx1423_22";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_23</b>, varchar(40) **/
			String TX1423_23 = "Tx1423_23";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_24</b>, varchar(40) **/
			String TX1423_24 = "Tx1423_24";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_25</b>, varchar(40) **/
			String TX1423_25 = "Tx1423_25";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_26</b>, varchar(40) **/
			String TX1423_26 = "Tx1423_26";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_27</b>, varchar(40) **/
			String TX1423_27 = "Tx1423_27";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_28</b>, varchar(40) **/
			String TX1423_28 = "Tx1423_28";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_29</b>, varchar(40) **/
			String TX1423_29 = "Tx1423_29";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_30</b>, varchar(40) **/
			String TX1423_30 = "Tx1423_30";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_31</b>, varchar(40) **/
			String TX1423_31 = "Tx1423_31";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_32</b>, varchar(40) **/
			String TX1423_32 = "Tx1423_32";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_33</b>, varchar(40) **/
			String TX1423_33 = "Tx1423_33";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_34</b>, varchar(40) **/
			String TX1423_34 = "Tx1423_34";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_35</b>, varchar(40) **/
			String TX1423_35 = "Tx1423_35";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_36</b>, varchar(40) **/
			String TX1423_36 = "Tx1423_36";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_37</b>, varchar(40) **/
			String TX1423_37 = "Tx1423_37";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_38</b>, varchar(40) **/
			String TX1423_38 = "Tx1423_38";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_39</b>, varchar(40) **/
			String TX1423_39 = "Tx1423_39";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_40</b>, varchar(40) **/
			String TX1423_40 = "Tx1423_40";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_41</b>, varchar(40) **/
			String TX1423_41 = "Tx1423_41";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_42</b>, varchar(40) **/
			String TX1423_42 = "Tx1423_42";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_43</b>, varchar(40) **/
			String TX1423_43 = "Tx1423_43";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_44</b>, varchar(40) **/
			String TX1423_44 = "Tx1423_44";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_45</b>, varchar(40) **/
			String TX1423_45 = "Tx1423_45";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_46</b>, varchar(40) **/
			String TX1423_46 = "Tx1423_46";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_47</b>, varchar(40) **/
			String TX1423_47 = "Tx1423_47";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_48</b>, varchar(40) **/
			String TX1423_48 = "Tx1423_48";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_49</b>, varchar(40) **/
			String TX1423_49 = "Tx1423_49";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_50</b>, varchar(40) **/
			String TX1423_50 = "Tx1423_50";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_51</b>, varchar(40) **/
			String TX1423_51 = "Tx1423_51";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_52</b>, varchar(40) **/
			String TX1423_52 = "Tx1423_52";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_53</b>, varchar(40) **/
			String TX1423_53 = "Tx1423_53";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_54</b>, varchar(40) **/
			String TX1423_54 = "Tx1423_54";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_55</b>, varchar(40) **/
			String TX1423_55 = "Tx1423_55";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_56</b>, varchar(40) **/
			String TX1423_56 = "Tx1423_56";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_57</b>, varchar(40) **/
			String TX1423_57 = "Tx1423_57";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_58</b>, varchar(40) **/
			String TX1423_58 = "Tx1423_58";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_59</b>, varchar(40) **/
			String TX1423_59 = "Tx1423_59";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_60</b>, varchar(40) **/
			String TX1423_60 = "Tx1423_60";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_61</b>, varchar(40) **/
			String TX1423_61 = "Tx1423_61";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_62</b>, varchar(40) **/
			String TX1423_62 = "Tx1423_62";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_63</b>, varchar(40) **/
			String TX1423_63 = "Tx1423_63";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_64</b>, varchar(40) **/
			String TX1423_64 = "Tx1423_64";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_65</b>, varchar(40) **/
			String TX1423_65 = "Tx1423_65";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_66</b>, varchar(40) **/
			String TX1423_66 = "Tx1423_66";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_67</b>, varchar(40) **/
			String TX1423_67 = "Tx1423_67";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_68</b>, varchar(40) **/
			String TX1423_68 = "Tx1423_68";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_69</b>, varchar(40) **/
			String TX1423_69 = "Tx1423_69";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_70</b>, varchar(40) **/
			String TX1423_70 = "Tx1423_70";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_71</b>, varchar(40) **/
			String TX1423_71 = "Tx1423_71";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_72</b>, varchar(40) **/
			String TX1423_72 = "Tx1423_72";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_73</b>, varchar(40) **/
			String TX1423_73 = "Tx1423_73";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_74</b>, varchar(40) **/
			String TX1423_74 = "Tx1423_74";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_75</b>, varchar(40) **/
			String TX1423_75 = "Tx1423_75";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_76</b>, varchar(40) **/
			String TX1423_76 = "Tx1423_76";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_77</b>, varchar(40) **/
			String TX1423_77 = "Tx1423_77";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_78</b>, varchar(40) **/
			String TX1423_78 = "Tx1423_78";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_79</b>, varchar(40) **/
			String TX1423_79 = "Tx1423_79";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_80</b>, varchar(40) **/
			String TX1423_80 = "Tx1423_80";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_81</b>, varchar(40) **/
			String TX1423_81 = "Tx1423_81";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_82</b>, varchar(40) **/
			String TX1423_82 = "Tx1423_82";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_83</b>, varchar(40) **/
			String TX1423_83 = "Tx1423_83";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_84</b>, varchar(40) **/
			String TX1423_84 = "Tx1423_84";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1401_6</b>, varchar(255) **/
			String TX1401_6 = "Tx1401_6";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_1</b>, varchar(50) **/
			String TX1423_1 = "Tx1423_1";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_2</b>, varchar(50) **/
			String TX1423_2 = "Tx1423_2";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_3</b>, varchar(50) **/
			String TX1423_3 = "Tx1423_3";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_4</b>, varchar(50) **/
			String TX1423_4 = "Tx1423_4";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_5</b>, varchar(50) **/
			String TX1423_5 = "Tx1423_5";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_6</b>, varchar(50) **/
			String TX1423_6 = "Tx1423_6";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_7</b>, varchar(50) **/
			String TX1423_7 = "Tx1423_7";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_8</b>, varchar(50) **/
			String TX1423_8 = "Tx1423_8";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_9</b>, varchar(50) **/
			String TX1423_9 = "Tx1423_9";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_10</b>, varchar(50) **/
			String TX1423_10 = "Tx1423_10";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_11</b>, varchar(50) **/
			String TX1423_11 = "Tx1423_11";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_12</b>, varchar(50) **/
			String TX1423_12 = "Tx1423_12";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_13</b>, varchar(50) **/
			String TX1423_13 = "Tx1423_13";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_14</b>, varchar(50) **/
			String TX1423_14 = "Tx1423_14";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_15</b>, varchar(50) **/
			String TX1423_15 = "Tx1423_15";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_16</b>, varchar(50) **/
			String TX1423_16 = "Tx1423_16";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_17</b>, varchar(50) **/
			String TX1423_17 = "Tx1423_17";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_18</b>, varchar(50) **/
			String TX1423_18 = "Tx1423_18";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_19</b>, varchar(50) **/
			String TX1423_19 = "Tx1423_19";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1423_20</b>, varchar(50) **/
			String TX1423_20 = "Tx1423_20";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1401_5</b>, varchar(250) **/
			String TX1401_5 = "Tx1401_5";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Anrede</b>, varchar(150) **/
			String ANREDE = "Anrede";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>MonteurSir</b>, char(3) **/
			String MONTEUR_SIR = "MonteurSir";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>MonteurPfy</b>, char(3) **/
			String MONTEUR_PFY = "MonteurPfy";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>MDatum1</b>, datetime(8) **/
			String M_DATUM1 = "MDatum1";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>MDatum2</b>, datetime(8) **/
			String M_DATUM2 = "MDatum2";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>MDatum3</b>, datetime(8) **/
			String M_DATUM3 = "MDatum3";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>MDatum4</b>, datetime(8) **/
			String M_DATUM4 = "MDatum4";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Bemerk1</b>, varchar(255) **/
			String BEMERK1 = "Bemerk1";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Bemerk2</b>, varchar(255) **/
			String BEMERK2 = "Bemerk2";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>KontaktP</b>, varchar(80) **/
			String KONTAKT_P = "KontaktP";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>DienstSir</b>, char(3) **/
			String DIENST_SIR = "DienstSir";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>ZeitA</b>, char(5) **/
			String ZEIT_A = "ZeitA";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Planung</b>, char(3) **/
			String PLANUNG = "Planung";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Dienst1PFY</b>, char(3) **/
			String DIENST1_PFY = "Dienst1PFY";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Dienst2PFY</b>, char(3) **/
			String DIENST2_PFY = "Dienst2PFY";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Dienst3PFY</b>, char(3) **/
			String DIENST3_PFY = "Dienst3PFY";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Dienst4PFY</b>, char(3) **/
			String DIENST4_PFY = "Dienst4PFY";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Dienst5PFY</b>, char(3) **/
			String DIENST5_PFY = "Dienst5PFY";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Tx1401_4</b>, varchar(250) **/
			String TX1401_4 = "Tx1401_4";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>KUECHEN</b>, char(3) **/
			String KUECHEN = "KUECHEN";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>InfoMasse</b>, varchar(40) **/
			String INFO_MASSE = "InfoMasse";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>WP_Objekt</b>, varchar(20) **/
			String WP_OBJEKT = "WP_Objekt";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>TOTBETRAG</b>, float(8) **/
			String TOTBETRAG = "TOTBETRAG";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>BPreis</b>, float(8) **/
			String B_PREIS = "BPreis";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>MPreis</b>, float(8) **/
			String M_PREIS = "MPreis";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>AUSSEN_BEZ</b>, varchar(30) **/
			String AUSSEN_BEZ = "AUSSEN_BEZ";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>TEAM_BEZ</b>, varchar(30) **/
			String TEAM_BEZ = "TEAM_BEZ";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>KDBerat_1</b>, varchar(30) **/
			String KD_BERAT_1 = "KDBerat_1";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>KDBerat_2</b>, varchar(30) **/
			String KD_BERAT_2 = "KDBerat_2";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>AUSSEN_Betrag</b>, float(8) **/
			String AUSSEN_BETRAG = "AUSSEN_Betrag";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>TEAM_Betrag</b>, float(8) **/
			String TEAM_BETRAG = "TEAM_Betrag";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>KDBerat_1Betrag</b>, float(8) **/
			String KD_BERAT_1_BETRAG = "KDBerat_1Betrag";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>KDBerat_2Betrag</b>, float(8) **/
			String KD_BERAT_2_BETRAG = "KDBerat_2Betrag";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>DISPONENTSIR</b>, char(3) **/
			String DISPONENTSIR = "DISPONENTSIR";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>SHTHA</b>, char(3) **/
			String SHTHA = "SHTHA";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>MassKontID</b>, char(3) **/
			String MASS_KONT_ID = "MassKontID";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>GranitMontage</b>, char(3) **/
			String GRANIT_MONTAGE = "GranitMontage";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Bezug</b>, char(3) **/
			String BEZUG = "Bezug";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Sch_BlockB</b>, char(3) **/
			String SCH_BLOCK_B = "Sch_BlockB";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Sch_BlockC</b>, char(3) **/
			String SCH_BLOCK_C = "Sch_BlockC";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>DB_KoMontage</b>, float(8) **/
			String DB_KO_MONTAGE = "DB_KoMontage";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>DB_Rechwert</b>, float(8) **/
			String DB_RECHWERT = "DB_Rechwert";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>DB_EKSIR</b>, float(8) **/
			String DB_EKSIR = "DB_EKSIR";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>DB_TPPFY</b>, float(8) **/
			String DB_TPPFY = "DB_TPPFY";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>DB_Total</b>, float(8) **/
			String DB_TOTAL = "DB_Total";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>DB_KoPersonal</b>, float(8) **/
			String DB_KO_PERSONAL = "DB_KoPersonal";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>DB_KoBetrieb</b>, float(8) **/
			String DB_KO_BETRIEB = "DB_KoBetrieb";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>DB_ErgebnisI</b>, float(8) **/
			String DB_ERGEBNIS_I = "DB_ErgebnisI";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>DB_ErgebnisII</b>, float(8) **/
			String DB_ERGEBNIS_II = "DB_ErgebnisII";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>DB_TypProz</b>, float(8) **/
			String DB_TYP_PROZ = "DB_TypProz";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>DB_Objektbez</b>, varchar(30) **/
			String DB_OBJEKTBEZ = "DB_Objektbez";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>DB_KoFolgeauftrag</b>, float(8) **/
			String DB_KO_FOLGEAUFTRAG = "DB_KoFolgeauftrag";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>DB_KoBetriebProz</b>, float(8) **/
			String DB_KO_BETRIEB_PROZ = "DB_KoBetriebProz";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>DB_ErgebProz0</b>, float(8) **/
			String DB_ERGEB_PROZ0 = "DB_ErgebProz0";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>DB_ErgebProz1</b>, float(8) **/
			String DB_ERGEB_PROZ1 = "DB_ErgebProz1";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>DB_ErgebProz2</b>, float(8) **/
			String DB_ERGEB_PROZ2 = "DB_ErgebProz2";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Prov_Rechwert</b>, float(8) **/
			String PROV_RECHWERT = "Prov_Rechwert";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Kundenpreis</b>, float(8) **/
			String KUNDENPREIS = "Kundenpreis";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>GU_Honorar</b>, float(8) **/
			String GU_HONORAR = "GU_Honorar";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>DB_SummeRPArtikel</b>, float(8) **/
			String DB_SUMME_RP_ARTIKEL = "DB_SummeRPArtikel";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>DB_SummeMoAuftraege</b>, float(8) **/
			String DB_SUMME_MO_AUFTRAEGE = "DB_SummeMoAuftraege";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>B2BCode</b>, char(3) **/
			String B2_B_CODE = "B2BCode";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>AVers_Datum</b>, datetime(8) **/
			String A_VERS_DATUM = "AVers_Datum";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>ABest_Datum</b>, datetime(8) **/
			String A_BEST_DATUM = "ABest_Datum";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>OffGueltigCd</b>, char(3) **/
			String OFF_GUELTIG_CD = "OffGueltigCd";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>DBRECH</b>, char(3) **/
			String DBRECH = "DBRECH";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>DB_Abz_BP</b>, float(8) **/
			String DB_ABZ_BP = "DB_Abz_BP";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>DB_Abz_MP</b>, float(8) **/
			String DB_ABZ_MP = "DB_Abz_MP";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>DB_EKSIR_II</b>, float(8) **/
			String DB_EKSIR_II = "DB_EKSIR_II";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>DB_ErgebProz0_II</b>, float(8) **/
			String DB_ERGEB_PROZ0_II = "DB_ErgebProz0_II";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>DB_Total_II</b>, float(8) **/
			String DB_TOTAL_II = "DB_Total_II";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>DB_ErgebProz1_II</b>, float(8) **/
			String DB_ERGEB_PROZ1_II = "DB_ErgebProz1_II";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>DB_ErgebnisI_II</b>, float(8) **/
			String DB_ERGEBNIS_I_II = "DB_ErgebnisI_II";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>DB_ErgebProz2_II</b>, float(8) **/
			String DB_ERGEB_PROZ2_II = "DB_ErgebProz2_II";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>DB_ErgebnisII_II</b>, float(8) **/
			String DB_ERGEBNIS_II_II = "DB_ErgebnisII_II";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>DB_WV_SCHLABR</b>, char(3) **/
			String DB_WV_SCHLABR = "DB_WV_SCHLABR";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>DB_OBJ_Schlussabr</b>, datetime(8) **/
			String DB_OBJ_SCHLUSSABR = "DB_OBJ_Schlussabr";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Jou_Mann2</b>, int(4) **/
			String JOU_MANN2 = "Jou_Mann2";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Jou_AbschlussEndmontage</b>, datetime(8) **/
			String JOU_ABSCHLUSS_ENDMONTAGE = "Jou_AbschlussEndmontage";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Jou_Maengeltag</b>, datetime(8) **/
			String JOU_MAENGELTAG = "Jou_Maengeltag";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Jou_TeilEndMontage</b>, int(4) **/
			String JOU_TEIL_END_MONTAGE = "Jou_TeilEndMontage";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Jou_WunschdatumKW</b>, varchar(10) **/
			String JOU_WUNSCHDATUM_KW = "Jou_WunschdatumKW";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Jou_AufwandEndMontage</b>, float(8) **/
			String JOU_AUFWAND_END_MONTAGE = "Jou_AufwandEndMontage";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Jou_OriginalMasse</b>, int(4) **/
			String JOU_ORIGINAL_MASSE = "Jou_OriginalMasse";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Jou_Breite</b>, float(8) **/
			String JOU_BREITE = "Jou_Breite";
			/** VERTRIEB.dbo.AuftragsKopfZusatz.<b>Jou_Hoehe</b>, float(8) **/
			String JOU_HOEHE = "Jou_Hoehe";
		}

		/** VERTRIEB.dbo.<b>PAUFTRAG</b>, 150 Columns **/
		public interface PAuftragTable {
			/** VERTRIEB.dbo.<b>PAUFTRAG</b> **/
			String _TN = "VERTRIEB.dbo.PAUFTRAG";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Id_GSE_PA</b>, char(4), mandatory **/
			String ID_GSE_PA = "Id_GSE_PA";
			/** VERTRIEB.dbo.PAUFTRAG.<b>PAuftrNr</b>, varchar(16), mandatory **/
			String P_AUFTR_NR = "PAuftrNr";
			/** VERTRIEB.dbo.PAUFTRAG.<b>PBez</b>, varchar(250), mandatory **/
			String P_BEZ = "PBez";
			/** VERTRIEB.dbo.PAUFTRAG.<b>PDatVon</b>, datetime(8) **/
			String P_DAT_VON = "PDatVon";
			/** VERTRIEB.dbo.PAUFTRAG.<b>PDatBis</b>, datetime(8) **/
			String P_DAT_BIS = "PDatBis";
			/** VERTRIEB.dbo.PAUFTRAG.<b>PBudget</b>, float(8) **/
			String P_BUDGET = "PBudget";
			/** VERTRIEB.dbo.PAUFTRAG.<b>PAufSumme</b>, float(8) **/
			String P_AUF_SUMME = "PAufSumme";
			/** VERTRIEB.dbo.PAUFTRAG.<b>PAufDat</b>, datetime(8) **/
			String P_AUF_DAT = "PAufDat";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Status</b>, char(3), mandatory **/
			String STATUS = "Status";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Notiz</b>, text(16) **/
			String NOTIZ = "Notiz";
			/** VERTRIEB.dbo.PAUFTRAG.<b>PLeiter</b>, varchar(40) **/
			String P_LEITER = "PLeiter";
			/** VERTRIEB.dbo.PAUFTRAG.<b>PAuftrArt</b>, char(3), mandatory **/
			String P_AUFTR_ART = "PAuftrArt";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Id_GSE_KLR</b>, char(4) **/
			String ID_GSE_KLR = "Id_GSE_KLR";
			/** VERTRIEB.dbo.PAUFTRAG.<b>KstKT</b>, varchar(16) **/
			String KST_KT = "KstKT";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Id_GSE_GBZ</b>, char(4) **/
			String ID_GSE_GBZ = "Id_GSE_GBZ";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Rolle</b>, char(3) **/
			String ROLLE = "Rolle";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Id_SUBJEKT</b>, int(4) **/
			String ID_SUBJEKT = "Id_SUBJEKT";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Id_GSE_GBZ_AG</b>, char(4) **/
			String ID_GSE_GBZ_AG = "Id_GSE_GBZ_AG";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Rolle_AG</b>, char(3) **/
			String ROLLE_AG = "Rolle_AG";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Id_SUBJEKT_AG</b>, int(4) **/
			String ID_SUBJEKT_AG = "Id_SUBJEKT_AG";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Id_GSE_GBZ_B</b>, char(4) **/
			String ID_GSE_GBZ_B = "Id_GSE_GBZ_B";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Rolle_B</b>, char(3) **/
			String ROLLE_B = "Rolle_B";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Id_SUBJEKT_B</b>, int(4) **/
			String ID_SUBJEKT_B = "Id_SUBJEKT_B";
			/** VERTRIEB.dbo.PAUFTRAG.<b>MBauherrAdr</b>, varchar(255) **/
			String M_BAUHERR_ADR = "MBauherrAdr";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Id_GSE_GBZ_A</b>, char(4) **/
			String ID_GSE_GBZ_A = "Id_GSE_GBZ_A";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Rolle_A</b>, char(3) **/
			String ROLLE_A = "Rolle_A";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Id_SUBJEKT_A</b>, int(4) **/
			String ID_SUBJEKT_A = "Id_SUBJEKT_A";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Id_GSE_GBZ_I</b>, char(4) **/
			String ID_GSE_GBZ_I = "Id_GSE_GBZ_I";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Rolle_I</b>, char(3) **/
			String ROLLE_I = "Rolle_I";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Id_SUBJEKT_I</b>, int(4) **/
			String ID_SUBJEKT_I = "Id_SUBJEKT_I";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Reserve1</b>, varchar(50) **/
			String RESERVE1 = "Reserve1";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Reserve2</b>, varchar(50) **/
			String RESERVE2 = "Reserve2";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Reserve3</b>, int(4) **/
			String RESERVE3 = "Reserve3";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Reserve4</b>, int(4) **/
			String RESERVE4 = "Reserve4";
			/** VERTRIEB.dbo.PAUFTRAG.<b>ResDatum</b>, datetime(8) **/
			String RES_DATUM = "ResDatum";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Upd_Sabe</b>, varchar(10), mandatory **/
			String UPD_SABE = "Upd_Sabe";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Add_Sabe</b>, varchar(10), mandatory **/
			String ADD_SABE = "Add_Sabe";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Add_Date</b>, datetime(8), mandatory **/
			String ADD_DATE = "Add_Date";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Upd_Date</b>, datetime(8), mandatory **/
			String UPD_DATE = "Upd_Date";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Adressart_B</b>, char(3) **/
			String ADRESSART_B = "Adressart_B";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Adressart_A</b>, char(3) **/
			String ADRESSART_A = "Adressart_A";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Adressart_I</b>, char(3) **/
			String ADRESSART_I = "Adressart_I";
			/** VERTRIEB.dbo.PAUFTRAG.<b>PreisBasis</b>, datetime(8) **/
			String PREIS_BASIS = "PreisBasis";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Vertreter</b>, int(4) **/
			String VERTRETER = "Vertreter";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Vert1</b>, varchar(8) **/
			String VERT1 = "Vert1";
			/** VERTRIEB.dbo.PAUFTRAG.<b>ObjektTermin</b>, datetime(8) **/
			String OBJEKT_TERMIN = "ObjektTermin";
			/** VERTRIEB.dbo.PAUFTRAG.<b>ObjektStatus</b>, char(3) **/
			String OBJEKT_STATUS = "ObjektStatus";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Reserve5</b>, varchar(50) **/
			String RESERVE5 = "Reserve5";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Reserve6</b>, varchar(50) **/
			String RESERVE6 = "Reserve6";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Reserve7</b>, varchar(50) **/
			String RESERVE7 = "Reserve7";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Reserve8</b>, varchar(50) **/
			String RESERVE8 = "Reserve8";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Reserve9</b>, varchar(50) **/
			String RESERVE9 = "Reserve9";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Reserve10</b>, varchar(50) **/
			String RESERVE10 = "Reserve10";
			/** VERTRIEB.dbo.PAUFTRAG.<b>PBasisWert</b>, float(8) **/
			String P_BASIS_WERT = "PBasisWert";
			/** VERTRIEB.dbo.PAUFTRAG.<b>PSubjektWert</b>, float(8) **/
			String P_SUBJEKT_WERT = "PSubjektWert";
			/** VERTRIEB.dbo.PAUFTRAG.<b>PFaktPerCd</b>, char(3) **/
			String P_FAKT_PER_CD = "PFaktPerCd";
			/** VERTRIEB.dbo.PAUFTRAG.<b>PSubjektCd</b>, char(3) **/
			String P_SUBJEKT_CD = "PSubjektCd";
			/** VERTRIEB.dbo.PAUFTRAG.<b>PGruppe</b>, char(3) **/
			String P_GRUPPE = "PGruppe";
			/** VERTRIEB.dbo.PAUFTRAG.<b>PProdGruppe</b>, char(3) **/
			String P_PROD_GRUPPE = "PProdGruppe";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Reserve11</b>, datetime(8) **/
			String RESERVE11 = "Reserve11";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Reserve12</b>, datetime(8) **/
			String RESERVE12 = "Reserve12";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Id_GSE_GBZ_K</b>, char(4) **/
			String ID_GSE_GBZ_K = "Id_GSE_GBZ_K";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Rolle_K</b>, char(3) **/
			String ROLLE_K = "Rolle_K";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Id_SUBJEKT_K</b>, int(4) **/
			String ID_SUBJEKT_K = "Id_SUBJEKT_K";
			/** VERTRIEB.dbo.PAUFTRAG.<b>MKaeuferAdr</b>, varchar(255) **/
			String M_KAEUFER_ADR = "MKaeuferAdr";
			/** VERTRIEB.dbo.PAUFTRAG.<b>AdressArt_K</b>, char(3) **/
			String ADRESS_ART_K = "AdressArt_K";
			/** VERTRIEB.dbo.PAUFTRAG.<b>LaufNr_KontPer_AG</b>, int(4) **/
			String LAUF_NR_KONT_PER_AG = "LaufNr_KontPer_AG";
			/** VERTRIEB.dbo.PAUFTRAG.<b>LaufNr_KontPer_B</b>, int(4) **/
			String LAUF_NR_KONT_PER_B = "LaufNr_KontPer_B";
			/** VERTRIEB.dbo.PAUFTRAG.<b>LaufNr_KontPer_A</b>, int(4) **/
			String LAUF_NR_KONT_PER_A = "LaufNr_KontPer_A";
			/** VERTRIEB.dbo.PAUFTRAG.<b>LaufNr_KontPer_I</b>, int(4) **/
			String LAUF_NR_KONT_PER_I = "LaufNr_KontPer_I";
			/** VERTRIEB.dbo.PAUFTRAG.<b>LaufNr_KontPer_K</b>, int(4) **/
			String LAUF_NR_KONT_PER_K = "LaufNr_KontPer_K";
			/** VERTRIEB.dbo.PAUFTRAG.<b>LaufNrKPers_AG</b>, int(4) **/
			String LAUF_NR_K_PERS_AG = "LaufNrKPers_AG";
			/** VERTRIEB.dbo.PAUFTRAG.<b>LaufNrKPers_B</b>, int(4) **/
			String LAUF_NR_K_PERS_B = "LaufNrKPers_B";
			/** VERTRIEB.dbo.PAUFTRAG.<b>LaufNrKPers_K</b>, int(4) **/
			String LAUF_NR_K_PERS_K = "LaufNrKPers_K";
			/** VERTRIEB.dbo.PAUFTRAG.<b>LaufNrKPers_A</b>, int(4) **/
			String LAUF_NR_K_PERS_A = "LaufNrKPers_A";
			/** VERTRIEB.dbo.PAUFTRAG.<b>LaufNrKPers_I</b>, int(4) **/
			String LAUF_NR_K_PERS_I = "LaufNrKPers_I";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Basisobjekt</b>, char(3) **/
			String BASISOBJEKT = "Basisobjekt";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Basisobjektstatus</b>, char(3) **/
			String BASISOBJEKTSTATUS = "Basisobjektstatus";
			/** VERTRIEB.dbo.PAUFTRAG.<b>ArchivNrBez</b>, varchar(50) **/
			String ARCHIV_NR_BEZ = "ArchivNrBez";
			/** VERTRIEB.dbo.PAUFTRAG.<b>AnzKuechen_B</b>, int(4) **/
			String ANZ_KUECHEN_B = "AnzKuechen_B";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Id_GSE_PA_B</b>, char(4) **/
			String ID_GSE_PA_B = "Id_GSE_PA_B";
			/** VERTRIEB.dbo.PAUFTRAG.<b>PAuftrnr_B</b>, varchar(16) **/
			String P_AUFTRNR_B = "PAuftrnr_B";
			/** VERTRIEB.dbo.PAUFTRAG.<b>GU_Honorar_Summe</b>, float(8) **/
			String GU_HONORAR_SUMME = "GU_Honorar_Summe";
			/** VERTRIEB.dbo.PAUFTRAG.<b>GU_Honorar_Proz</b>, float(8) **/
			String GU_HONORAR_PROZ = "GU_Honorar_Proz";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Basisinfo</b>, char(3) **/
			String BASISINFO = "Basisinfo";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Moebel_B</b>, float(8) **/
			String MOEBEL_B = "Moebel_B";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Granit_B</b>, float(8) **/
			String GRANIT_B = "Granit_B";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Marketing</b>, char(3) **/
			String MARKETING = "Marketing";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Off_an_ID</b>, datetime(8) **/
			String OFF_AN_ID = "Off_an_ID";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Off_Ausgang_Wunsch</b>, datetime(8) **/
			String OFF_AUSGANG_WUNSCH = "Off_Ausgang_Wunsch";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Off_erstellt</b>, datetime(8) **/
			String OFF_ERSTELLT = "Off_erstellt";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Auftr_an_ID</b>, datetime(8) **/
			String AUFTR_AN_ID = "Auftr_an_ID";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Auftr_Ausgang_Wunsch</b>, datetime(8) **/
			String AUFTR_AUSGANG_WUNSCH = "Auftr_Ausgang_Wunsch";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Auftr_erstellt</b>, datetime(8) **/
			String AUFTR_ERSTELLT = "Auftr_erstellt";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Modalitaet</b>, char(3) **/
			String MODALITAET = "Modalitaet";
			/** VERTRIEB.dbo.PAUFTRAG.<b>EZ_BP_Subjekt</b>, int(4) **/
			String EZ_BP_SUBJEKT = "EZ_BP_Subjekt";
			/** VERTRIEB.dbo.PAUFTRAG.<b>EZ_BP_Subjekt_GSE</b>, char(4) **/
			String EZ_BP_SUBJEKT_GSE = "EZ_BP_Subjekt_GSE";
			/** VERTRIEB.dbo.PAUFTRAG.<b>EZ_MP_Subjekt</b>, int(4) **/
			String EZ_MP_SUBJEKT = "EZ_MP_Subjekt";
			/** VERTRIEB.dbo.PAUFTRAG.<b>EZ_MP_Subjekt_GSE</b>, char(4) **/
			String EZ_MP_SUBJEKT_GSE = "EZ_MP_Subjekt_GSE";
			/** VERTRIEB.dbo.PAUFTRAG.<b>EZ_B_StartAkontoDat</b>, datetime(8) **/
			String EZ_B_START_AKONTO_DAT = "EZ_B_StartAkontoDat";
			/** VERTRIEB.dbo.PAUFTRAG.<b>EZ_M_StartAkontoDat</b>, datetime(8) **/
			String EZ_M_START_AKONTO_DAT = "EZ_M_StartAkontoDat";
			/** VERTRIEB.dbo.PAUFTRAG.<b>EZ_Schlussabr</b>, datetime(8) **/
			String EZ_SCHLUSSABR = "EZ_Schlussabr";
			/** VERTRIEB.dbo.PAUFTRAG.<b>SchlRechStatus</b>, char(3) **/
			String SCHL_RECH_STATUS = "SchlRechStatus";
			/** VERTRIEB.dbo.PAUFTRAG.<b>EZ_BP_Subjekt_RAdr</b>, int(4) **/
			String EZ_BP_SUBJEKT_R_ADR = "EZ_BP_Subjekt_RAdr";
			/** VERTRIEB.dbo.PAUFTRAG.<b>EZ_BP_SubjektRolle_RAdr</b>, char(3) **/
			String EZ_BP_SUBJEKT_ROLLE_R_ADR = "EZ_BP_SubjektRolle_RAdr";
			/** VERTRIEB.dbo.PAUFTRAG.<b>EZ_BP_Subjekt_GSE_RAdr</b>, char(4) **/
			String EZ_BP_SUBJEKT_GSE_R_ADR = "EZ_BP_Subjekt_GSE_RAdr";
			/** VERTRIEB.dbo.PAUFTRAG.<b>EZ_MP_Subjekt_RAdr</b>, int(4) **/
			String EZ_MP_SUBJEKT_R_ADR = "EZ_MP_Subjekt_RAdr";
			/** VERTRIEB.dbo.PAUFTRAG.<b>EZ_MP_SubjektRolle_RAdr</b>, char(3) **/
			String EZ_MP_SUBJEKT_ROLLE_R_ADR = "EZ_MP_SubjektRolle_RAdr";
			/** VERTRIEB.dbo.PAUFTRAG.<b>EZ_MP_Subjekt_GSE_RAdr</b>, char(4) **/
			String EZ_MP_SUBJEKT_GSE_R_ADR = "EZ_MP_Subjekt_GSE_RAdr";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Haustyp</b>, varchar(25) **/
			String HAUSTYP = "Haustyp";
			/** VERTRIEB.dbo.PAUFTRAG.<b>HausBezeichnung</b>, varchar(200) **/
			String HAUS_BEZEICHNUNG = "HausBezeichnung";
			/** VERTRIEB.dbo.PAUFTRAG.<b>PLZ</b>, varchar(8) **/
			String PLZ = "PLZ";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Ort</b>, varchar(80) **/
			String ORT = "Ort";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Laufnr</b>, int(4) **/
			String LAUFNR = "Laufnr";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Hausname</b>, varchar(80) **/
			String HAUSNAME = "Hausname";
			/** VERTRIEB.dbo.PAUFTRAG.<b>HausVorname</b>, varchar(80) **/
			String HAUS_VORNAME = "HausVorname";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Reserve50</b>, varchar(50) **/
			String RESERVE50 = "Reserve50";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Reserve51</b>, varchar(50) **/
			String RESERVE51 = "Reserve51";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Reserve52</b>, varchar(50) **/
			String RESERVE52 = "Reserve52";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Reserve53</b>, datetime(8) **/
			String RESERVE53 = "Reserve53";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Reserve54</b>, datetime(8) **/
			String RESERVE54 = "Reserve54";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Reserve55</b>, datetime(8) **/
			String RESERVE55 = "Reserve55";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Reserve56</b>, float(8) **/
			String RESERVE56 = "Reserve56";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Reserve57</b>, float(8) **/
			String RESERVE57 = "Reserve57";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Reserve58</b>, float(8) **/
			String RESERVE58 = "Reserve58";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Reserve59</b>, int(4) **/
			String RESERVE59 = "Reserve59";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Reserve60</b>, int(4) **/
			String RESERVE60 = "Reserve60";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Reserve61</b>, int(4) **/
			String RESERVE61 = "Reserve61";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Reserve62</b>, char(3) **/
			String RESERVE62 = "Reserve62";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Reserve63</b>, char(3) **/
			String RESERVE63 = "Reserve63";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Reserve64</b>, char(3) **/
			String RESERVE64 = "Reserve64";
			/** VERTRIEB.dbo.PAUFTRAG.<b>StrassenBez</b>, varchar(200) **/
			String STRASSEN_BEZ = "StrassenBez";
			/** VERTRIEB.dbo.PAUFTRAG.<b>ProjektBez</b>, varchar(255) **/
			String PROJEKT_BEZ = "ProjektBez";
			/** VERTRIEB.dbo.PAUFTRAG.<b>ProjektArt</b>, char(3) **/
			String PROJEKT_ART = "ProjektArt";
			/** VERTRIEB.dbo.PAUFTRAG.<b>WohnungBez</b>, varchar(128) **/
			String WOHNUNG_BEZ = "WohnungBez";
			/** VERTRIEB.dbo.PAUFTRAG.<b>WohnungTyp</b>, varchar(64) **/
			String WOHNUNG_TYP = "WohnungTyp";
			/** VERTRIEB.dbo.PAUFTRAG.<b>FamilienBez</b>, varchar(128) **/
			String FAMILIEN_BEZ = "FamilienBez";
			/** VERTRIEB.dbo.PAUFTRAG.<b>ProdKategorie</b>, char(3) **/
			String PROD_KATEGORIE = "ProdKategorie";
			/** VERTRIEB.dbo.PAUFTRAG.<b>ProjektStorno</b>, char(3) **/
			String PROJEKT_STORNO = "ProjektStorno";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Etappe_B</b>, char(3) **/
			String ETAPPE_B = "Etappe_B";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Etappe_M</b>, char(3) **/
			String ETAPPE_M = "Etappe_M";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Geschoss</b>, char(3) **/
			String GESCHOSS = "Geschoss";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Lage</b>, char(3) **/
			String LAGE = "Lage";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Hausspiegel</b>, varchar(200) **/
			String HAUSSPIEGEL = "Hausspiegel";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Wohntyp</b>, char(3) **/
			String WOHNTYP = "Wohntyp";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Garderobe</b>, char(3) **/
			String GARDEROBE = "Garderobe";
			/** VERTRIEB.dbo.PAUFTRAG.<b>ChckboxReserver01</b>, char(3) **/
			String CHCKBOX_RESERVER01 = "ChckboxReserver01";
			/** VERTRIEB.dbo.PAUFTRAG.<b>SchlRechStatusMP</b>, char(3) **/
			String SCHL_RECH_STATUS_MP = "SchlRechStatusMP";
			/** VERTRIEB.dbo.PAUFTRAG.<b>MODALITAET_B</b>, char(3) **/
			String MODALITAET_B = "MODALITAET_B";
			/** VERTRIEB.dbo.PAUFTRAG.<b>Miet_Eigentum</b>, char(3) **/
			String MIET_EIGENTUM = "Miet_Eigentum";
		}
	}
}
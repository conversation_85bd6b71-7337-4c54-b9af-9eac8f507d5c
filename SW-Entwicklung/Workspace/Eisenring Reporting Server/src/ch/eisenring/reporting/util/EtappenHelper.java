package ch.eisenring.reporting.util;

import ch.eisenring.app.shared.AbstractSoftwareComponent;
import ch.eisenring.logiware.code.pseudo.AbwicklungsartCode;
import ch.eisenring.logiware.code.service.LogiwareService;
import ch.eisenring.lw.condition.Factory;
import ch.eisenring.lw.meta.LWXEtappeMeta;
import ch.eisenring.lw.meta.LWXPAuftragMeta;
import ch.eisenring.lw.model.navigable.LWObjectCache;
import ch.eisenring.lw.model.navigable.LWXEtappe;
import ch.eisenring.lw.model.navigable.LWXPAuftrag;

import java.util.Arrays;

import static ch.eisenring.logiware.code.pseudo.AbwicklungsartCode.*;


public class EtappenHelper {

    private static AbwicklungsartCode[] etappierteAbwicklungsarten = { AWA001, AWA002, AWA132, AWA134, AWA135, AWA137 };

    public static LWXEtappe getEtappen(String projektNummer, String ab, AbstractSoftwareComponent server) {
        boolean isEtappiert = Arrays.asList(etappierteAbwicklungsarten).stream().anyMatch(a -> a.getShortText().equals(ab));
        if (isEtappiert) {
            try {
                final LogiwareService service = (LogiwareService) server.locateService(LogiwareService.class);
                final LWObjectCache cache = service.createObjectCache();
                LWXPAuftrag projekt = cache.loadSingle(LWXPAuftrag.class,
                        Factory.eq(LWXPAuftragMeta.ATR_PROJEKTNUMMER, projektNummer)
                );
                if (projekt != null) {
                    return cache.loadSingle(LWXEtappe.class,
                            Factory.eq(LWXEtappeMeta.ATR_ROWID, projekt.getEtappeRowId()));
                }
            } catch (Exception e) {
                System.out.println(projektNummer);
                e.printStackTrace();
                return null;
            }
        }
        return null;
    }
}

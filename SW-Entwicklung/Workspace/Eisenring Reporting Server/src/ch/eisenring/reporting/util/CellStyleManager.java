package ch.eisenring.reporting.util;

import org.apache.poi.ss.usermodel.*;

import java.util.HashMap;
import java.util.Map;

import static ch.eisenring.reporting.util.CellStyleManager.CellStyleName.*;

public class CellStyleManager {

    private final Workbook workbook;
    private final Map<CellStyleName, CellStyle> styles = new HashMap<>();
    Font defaultFont;
    Font defaultFontBold;

    public CellStyleManager(Workbook workbook) {
        this.workbook = workbook;
        createDefaults();
    }

    public enum CellStyleName {
        HEADER, DATA, TEXT, DATE, DATE_EXTENDED, DATA_WITH_BORDER, TEXT_WITH_OBRDER_RIGHT
    }

    private void createDefaults() {
        createFont();
        createStyles();
    }

    private void createFont() {
        defaultFont = workbook.createFont();
        defaultFont.setFontName("Calibri");
        int i = defaultFont.getIndex();
        defaultFontBold = workbook.createFont();
        int j = defaultFontBold.getIndex();

        defaultFontBold.setFontName("Calibri");
        defaultFontBold.setBold(true);
        int z = defaultFontBold.getIndex();
    }

    private void createStyles() {
        styles.put(HEADER, getHeaderStyle());
        styles.put(DATA, getDataCellStyle());
        styles.put(TEXT, getForceTextCellStyle());
        styles.put(DATE, getDateCellStyle());
        styles.put(DATE_EXTENDED, getExtendedDateCellStyle());
        styles.put(DATA_WITH_BORDER, getTextWithBorder());
        styles.put(TEXT_WITH_OBRDER_RIGHT, getTextWithBorderRight());
    }

    private CellStyle getTextWithBorder() {
        CellStyle style = getBaseStyle();
        style.setBorderBottom(BorderStyle.MEDIUM);
        style.setBorderLeft(BorderStyle.MEDIUM);
        style.setBorderRight(BorderStyle.MEDIUM);
        style.setBorderTop(BorderStyle.MEDIUM);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setFont(defaultFontBold);
        return style;
    }

    private CellStyle getTextWithBorderRight() {
        CellStyle style = getBaseStyle();
        style.setBorderRight(BorderStyle.MEDIUM);
        return style;
    }

    private CellStyle getDateCellStyle() {
        CellStyle dateStyle = getBaseStyle();
        DataFormat format = workbook.createDataFormat();
        dateStyle.setDataFormat(format.getFormat("dd.MM.yyyy"));
        return dateStyle;
    }

    private CellStyle getExtendedDateCellStyle() {
        CellStyle dateStyle = getBaseStyle();
        DataFormat format = workbook.createDataFormat();
        dateStyle.setDataFormat(format.getFormat("[$-de-DE]dddd, d. MMMM yyyy")); //[$-de-DE]dddd, d. MMMM yyyy --> "Mittwoch, 10. Dezember 2025
        return dateStyle;
    }

    public CellStyle getStyle(CellStyleName key) {
        return styles.get(key);
    }

    private CellStyle getDataCellStyle() {
        return getBaseStyle();
    }

    private CellStyle getForceTextCellStyle() {
        CellStyle textStyle = getBaseStyle();
        DataFormat format = workbook.createDataFormat();
        textStyle.setDataFormat(format.getFormat("@"));
        return textStyle;
    }

    private CellStyle getHeaderStyle() {
        CellStyle headerStyle = new ExcelHelper.ExcelCellStyle(workbook) //
                .setBackgroundColor(IndexedColors.GREY_25_PERCENT) //
                .setBorderLine(ExcelHelper.ExcelCellStyle.BorderLine.NORMAL) //
                .setHorizAlign(ExcelHelper.ExcelCellStyle.HorizAlign.LEFT) //
                .setFontBold(true) //
                .setFontName("Calibri")
                .getStyle();
        headerStyle.setBorderBottom(BorderStyle.MEDIUM);
        return headerStyle;
    }

    private CellStyle getBaseStyle() {
        CellStyle baseStyle = workbook.createCellStyle();
        baseStyle.setWrapText(true);
        baseStyle.setFont(defaultFont);
        return baseStyle;
    }

}

/* This Java-Class is generated by {@link QueryObjectsPrinter} (c) <PERSON> */
package ch.eisenring.reporting.util;

public interface QueryObjects {

	public interface DbObject {
		String def();
	}

	public interface DbValue extends DbObject {
	}

	public class DbTable implements DbObject {
		public final String table;
		public final String alias;

		public DbTable(String tableName, String tableAlias) {
			this.table = tableName;
			this.alias = tableAlias;
		}

		@Override
		public String toString() {
			return table;
		}

		@Override
		public String def() {
			return new StringBuilder().append(table).append(" as ").append(alias).toString();
		}
	}

	public class DbField implements DbValue {
		public final String column;
		public final String field;
		public final String alias;

		public DbField(String column, DbTable table, String alias) {
			this.column = column;
			this.field = new StringBuilder().append(table.alias).append(".").append(this.column).toString();
			this.alias = alias;
		}
		public DbField(String column, DbTable table) {
			this(column, table, new StringBuilder().append(table.alias).append("_").append(column).toString().toLowerCase());
		}

		@Override
		public String toString() {
			return field;
		}

		@Override
		public String def() {
			return new StringBuilder().append(field).append(" as ").append(alias).toString();
		}
	}

	public class DbExpression implements DbValue {
		public final String expr;
		public final String alias;

		public DbExpression(String expression, String alias) {
			this.expr = expression;
			this.alias = alias;
		}

		@Override
		public String toString() {
			return expr;
		}

		@Override
		public String def() {
			return new StringBuilder().append(expr).append(" as ").append(alias).toString();
		}
	}

	public class Query {

		public static StringBuilder select(DbValue... fields) {
			StringBuilder sql = null;
			for (DbValue field : fields) {
				if (sql == null) {
					sql = new StringBuilder().append("SELECT ");
				} else {
					sql.append(", ");
				}
				sql.append(field.def());
			}
			return sql;
		}

		public static StringBuilder group(DbValue... fields) {
			StringBuilder sql = null;
			for (DbValue field : fields) {
				if (sql == null) {
					sql = new StringBuilder().append(" GROUP BY ");
				} else {
					sql.append(", ");
				}
				sql.append(field);
			}
			return sql;
		}
	}

}
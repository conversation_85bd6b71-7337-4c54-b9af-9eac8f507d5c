package ch.eisenring.reporting.server;

import org.junit.Test;

import java.lang.reflect.Method;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.Locale;

import static org.junit.Assert.*;

public class CrontabTest {

    private static LocalDateTime SUNDAY = LocalDateTime.of(2025, 1, 5, 1, 0, 0);
    private static LocalDateTime MONDAY = LocalDateTime.of(2025, 1, 6, 1, 0, 0);
    private static LocalDateTime TUESDAY = LocalDateTime.of(2025, 1, 7, 1, 0, 0);
    private static LocalDateTime WEDNESDAY = LocalDateTime.of(2025, 1, 8, 1, 0, 0);
    private static LocalDateTime THURSDAY = LocalDateTime.of(2025, 1, 9, 1, 0);
    private static LocalDateTime FRIDAY = LocalDateTime.of(2025, 1, 10, 1, 0, 0);
    private static LocalDateTime SATURDAY = LocalDateTime.of(2025, 1, 11, 1, 0, 0);

    private String convertMonth(String spec) throws Exception {
        Method m = Crontab.class.getDeclaredMethod("convertMonth", String.class);
        m.setAccessible(true);
        return (String) m.invoke(null, spec);
    }

    private String convertDayOfWeek(String spec) throws Exception {
        Method m = Crontab.class.getDeclaredMethod("convertDayOfWeek", String.class);
        m.setAccessible(true);
        return (String) m.invoke(null, spec);
    }

    private String convertDayOfWeekSpec(String spec) throws Exception {
        Method m = Crontab.class.getDeclaredMethod("convertDayOfWeekSpec", String.class);
        m.setAccessible(true);
        return (String) m.invoke(null, spec);
    }

    @Test
    public void showDayOfWeekName() throws Exception {
        Calendar cal = new GregorianCalendar(2020, Calendar.MAY, 4);
        for (int i = 0; i < 7; i++) {
            System.out.print(cal.getTime());
            for (String lang : new String[]{"th", "ru", "en", "de"}) {
                System.out.print(", ");
                System.out.print(new SimpleDateFormat("EEEE", new Locale(lang)).format(cal.getTime()));
            }
            System.out.println();
            cal.add(Calendar.DATE, 1);
        }
    }

    @Test
    public void testDayOfWeekRangeWithComma() throws Exception {
        Crontab cronWithWeekdaysAsNumbers = Crontab.createFromSingleString("0 1 * * 1,2,5");
        assertFalse(cronWithWeekdaysAsNumbers.matchTime(convert(SUNDAY)));
        assertTrue(cronWithWeekdaysAsNumbers.matchTime(convert(MONDAY)));
        assertTrue(cronWithWeekdaysAsNumbers.matchTime(convert(TUESDAY)));
        assertFalse(cronWithWeekdaysAsNumbers.matchTime(convert(WEDNESDAY)));
        assertFalse(cronWithWeekdaysAsNumbers.matchTime(convert(THURSDAY)));
        assertTrue(cronWithWeekdaysAsNumbers.matchTime(convert(FRIDAY)));
        assertFalse(cronWithWeekdaysAsNumbers.matchTime(convert(SATURDAY)));

        Crontab cronWithWeekdayNames = Crontab.createFromSingleString("0 1 * * mo,mi,fr");
        assertFalse(cronWithWeekdayNames.matchTime(convert(SUNDAY)));
        assertTrue(cronWithWeekdayNames.matchTime(convert(MONDAY)));
        assertFalse(cronWithWeekdayNames.matchTime(convert(TUESDAY)));
        assertTrue(cronWithWeekdayNames.matchTime(convert(WEDNESDAY)));
        assertFalse(cronWithWeekdayNames.matchTime(convert(THURSDAY)));
        assertTrue(cronWithWeekdayNames.matchTime(convert(FRIDAY)));
        assertFalse(cronWithWeekdayNames.matchTime(convert(SATURDAY)));

        Crontab cronWorkingDays = Crontab.createFromSingleString("0 1 * * mo,di,mi,do,fr");
        assertFalse(cronWorkingDays.matchTime(convert(SUNDAY)));
        assertTrue(cronWorkingDays.matchTime(convert(MONDAY)));
        assertTrue(cronWorkingDays.matchTime(convert(TUESDAY)));
        assertTrue(cronWorkingDays.matchTime(convert(WEDNESDAY)));
        assertTrue(cronWorkingDays.matchTime(convert(THURSDAY)));
        assertTrue(cronWorkingDays.matchTime(convert(FRIDAY)));
        assertFalse(cronWorkingDays.matchTime(convert(SATURDAY)));

    }

    @Test
    public void testDayOfWeekName() throws Exception {
        Calendar cal = new GregorianCalendar(2020, Calendar.MAY, 3);
        for (int i = 0; i < 7; i++) {
            for (Locale lang : new Locale[]{Locale.ENGLISH, Locale.GERMAN}) {
                String dayNameShort = new SimpleDateFormat("EE", lang).format(cal.getTime());
                assertEquals(Integer.toString(i), convertDayOfWeek(dayNameShort));
            }
            cal.add(Calendar.DATE, 1);
        }
    }

    @Test
    public void testMonthName() throws Exception {
        Calendar cal = new GregorianCalendar(2020, Calendar.JANUARY, 15);
        for (int i = 0; i < 12; i++) {
            for (Locale lang : new Locale[]{Locale.ENGLISH, Locale.GERMAN}) {
                String monthNameShort = new SimpleDateFormat("MMM", lang).format(cal.getTime());
                assertEquals(Integer.toString(i + 1), convertMonth(monthNameShort));
            }
            cal.add(Calendar.MONTH, 1);
        }
    }

    @Test
    public void testNullReportingConfig() throws Exception {
        Crontab cron = Crontab.createFromSingleString(null);
        assertFalse(cron.matchTime(System.currentTimeMillis()));
    }

    @Test
    public void testEmptyReportingConfig1() throws Exception {
        Crontab cron = Crontab.createFromSingleString("");
        assertFalse(cron.matchTime(System.currentTimeMillis()));
    }

    @Test
    public void testEmptyReportingConfig2() throws Exception {
        Crontab cron = new Crontab("", "", "", "", "");
        assertFalse(cron.matchTime(System.currentTimeMillis()));
    }

    @Test
    public void testAllDayConfig() throws Exception {
        Crontab cron = new Crontab("*", "*", "*", "*", "*");
        assertTrue(cron.matchTime(System.currentTimeMillis()));
    }

    @Test
    public void testToDayConfig() throws Exception {
        Date date = new Date();
        for (Locale lang : new Locale[]{Locale.ENGLISH, Locale.GERMAN}) {
            Crontab cron = new Crontab("*", "*", "*", "*", new SimpleDateFormat("EE", lang).format(date));
            assertTrue(cron.matchTime(date.getTime()));
        }
    }

    @Test
    public void testNow() throws Exception {
        Date date = new Date();
        for (String monat : new String[]{"MM", "MMM"}) {
            for (Locale lang : new Locale[]{Locale.ENGLISH, Locale.GERMAN}) {
                String runTime = new SimpleDateFormat("mm HH dd " + monat + " EE", lang).format(date);
                Crontab cron = Crontab.createFromSingleString(runTime);
                assertTrue(cron.matchTime(date.getTime()));
            }
        }
    }

    /**
     * Dieser Test bestätigt, dass die Implementierung nicht jede Minute laufen kann
     * T: 2025-03-21T10:26:53.408 sleeps for 6667
     * 0: 2025-03-21T10:27:00.081
     * 0: 2025-03-21T10:28:30.081
     * T: 2025-03-21T10:28:30.081 sleeps for 29919
     * 1: 2025-03-21T10:29:00.001
     * 1: 2025-03-21T10:30:30.011
     *
     * @throws InterruptedException
     */
    @Test
    public void testNextCrontab() throws InterruptedException {
        Crontab crontab = Crontab.createFromSingleString("1 0 * * *");
        int counter = 0;
        while (counter < 10) {
            long sleepTime = 60_000 - System.currentTimeMillis() % 60_000;
            System.out.println("T: " + LocalDateTime.now() + " sleeps for " + sleepTime);
            Thread.sleep(sleepTime);
            System.out.println(counter + ": " + LocalDateTime.now());
            Thread.sleep(90_000);
            System.out.println(counter + ": " + LocalDateTime.now());
            counter++;
        }
    }

    private long convert(LocalDateTime localDateTime) {
        return localDateTime
                .atZone(ZoneId.systemDefault()) // Convert to ZonedDateTime
                .toInstant()                    // Convert to Instant
                .toEpochMilli();
    }

}

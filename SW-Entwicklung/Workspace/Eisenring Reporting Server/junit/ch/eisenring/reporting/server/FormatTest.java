package ch.eisenring.reporting.server;

import static org.junit.Assert.assertEquals;
import java.util.Calendar;
import java.util.GregorianCalendar;
import org.junit.Test;

public class FormatTest {

	@Test
	public void testSqlDate() {
		assertEquals("'2020-06-02'", String.format("'%tF'", new GregorianCalendar(2020, Calendar.JUNE, 2).getTime()));
		assertEquals("'2020-06-02'", String.format("'%tF'", new GregorianCalendar(2020, Calendar.JUNE, 2, 23, 59, 59).getTime()));
	}

}

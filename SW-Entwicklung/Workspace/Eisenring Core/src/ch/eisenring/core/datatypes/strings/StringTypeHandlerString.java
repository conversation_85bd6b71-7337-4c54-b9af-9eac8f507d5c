package ch.eisenring.core.datatypes.strings;

import ch.eisenring.core.datatypes.primitives.Primitives;

final class StringTypeHandlerString implements StringTypeHandler {

	@Override
	public String toString(final Object value) {
		return (String) value;
	}

	@Override
	public char[] toCharArray(final Object value) {
		final String s = (String) value;
		return s.length() == 0 ? Primitives.EMPTY_CHAR_ARRAY : s.toCharArray();
	}

}

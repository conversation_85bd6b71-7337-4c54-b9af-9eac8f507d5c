//package ch.eisenring.core.datatypes.primitives;
//
//import ch.eisenring.core.datatypes.strings.StringMaker;
//import ch.eisenring.core.datatypes.strings.StringMakerFriendly;
//import ch.eisenring.core.datatypes.strings.Strings;
//
///**
// * A mutable int wrapper, basically the same as java.lang.Integer
// * except its value can be altered after creation.
// */
//@SuppressWarnings("serial")
//public final class Int extends Number implements StringMakerFriendly {
//
//	protected int value;
//
//	public Int(final int value) {
//		this.value = value;
//	}
//
//	public Int(final Number value) {
//		this.value = value.intValue();
//	}
//
//	// --------------------------------------------------------------
//	// ---
//	// --- Extra API
//	// ---
//	// --------------------------------------------------------------
//	public void set(final int value) {
//		this.value = value;
//	}
//
//	public int increment() {
//		return ++value;
//	}
//
//	public void add(final int value) {
//		this.value += value;
//	}
//
//	public void add(final Number value) {
//		if (value != null)
//			this.value += value.intValue();
//	}
//
//	// --------------------------------------------------------------
//	// ---
//	// --- Number implementation
//	// ---
//	// --------------------------------------------------------------
//	@Override
//	public byte byteValue() {
//		return (byte) value;
//	}
//
//	@Override
//	public short shortValue() {
//		return (short) value;
//	}
//
//	@Override
//	public int intValue() {
//		return value;
//	}
//
//	@Override
//	public long longValue() {
//		return value;
//	}
//
//	@Override
//	public float floatValue() {
//		return value;
//	}
//
//	@Override
//	public double doubleValue() {
//		return value;
//	}
//	
//	// --------------------------------------------------------------
//	// ---
//	// --- Object overrides
//	// ---
//	// --------------------------------------------------------------
//	@Override
//	public final int hashCode() {
//		return value;
//	}
//
//	@Override
//	public boolean equals(final Object o) {
//		return o instanceof Int && ((Int) o).value == value;
//	}
//
//	@Override
//	public String toString() {
//		return Strings.toString(value);
//	}
//
//	@Override
//	public void toStringMaker(final StringMaker target) {
//		target.append(value);
//	}
//	
//}

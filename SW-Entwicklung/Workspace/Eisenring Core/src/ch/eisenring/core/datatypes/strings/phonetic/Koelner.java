package ch.eisenring.core.datatypes.strings.phonetic;

import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.Strings;

public final class <PERSON><PERSON><PERSON> extends PhoneticCodeBase {

	private final static String UPPER = "ABCDEFGHIJKLMNOPQRSTUVWXYZÄÖÜ";
	private final static String LOWER = "abcdefghijklmnopqrstuvwxyzäöü";
	
	public Koelner() {
		super("Kölner");
	}
	
	@Override
	public final String getPhoneticCode(final CharSequence string) {
		final String clean = Strings.clean(convertAccentsToBaseLetter(string));
		if (clean == null) {
			return null;
		}
		// build kölner code
		final StringMaker result = StringMaker.obtain();
		int i = -1;
		while (++i < clean.length()) {
			final char p = charAt(clean, i-1);
			final char c = charAt(clean, i);
			final char n = charAt(clean, i+1);
			final String a;
			switch (c) {
				case 'a': case 'e': case 'i': case 'j':
				case 'o': case 'u':	case 'y': case 'ä':
				case 'ö': case 'ü':
					a = result.length() > 0 ? null : "0";
					break;
				case 'b':
					a = "1";
					break;
				case 'p':
					switch (n) {
						case 'h':
							a = "3";
							break;
						default:
							a = "1";
							break;
					}
					break;
				case 'd': case 't':
					switch (n) {
						case 's': case 'c': case 'z':
							a = "8";
							break;
						default:
							a = "2";
							break;
					}
					break;
				case 'f': case 'v': case 'w':
					a = "3";
					break;
				case 'g': case 'k': case 'q':
					a = "4";
					break;
				case 'c':
					if (p == 's' || p == 'z') {
						a = "8";
					} else {
						switch (n) {
							case 'a': case 'h': case 'k': case 'l':
							case 'o': case 'q': case 'r': case 'u':
							case 'x':
								a = "4";
								break;
							default:
								a = "8";
								break;
						}
					}
					break;
				case 'x':
					switch (p) {
						case 'c': case 'k': case 'q':
							a = "8";
							break;
						default:
							a = "48";
							break;
					}
					break;
				case 'l':
					a = "5";
					break;
				case 'm': case 'n':
					a = "6";
					break;
				case 'r':
					a = "7";
					break;
				case 's': case 'z':
					a = "8";
					break;
				case 'h':
				default:
					a = null;
					break;
			}
			if (a != null) {
				if (a.length() > 1 || result.length() <= 0) {
					result.append(a);
				} else if (a.charAt(0) != result.charAt(result.length()-1)) {
					result.append(a);
				}
			}
		}
		return result.release();
	}

	private final static char toLower(final char c) {
		int i;
		if (LOWER.indexOf(c) >= 0) {
			return c;
		} else if ((i = UPPER.indexOf(c)) >= 0) {
			return LOWER.charAt(i);
		} else {
			return c == 'ß' ? 's' : 0;
		}
	}

	private final static char charAt(final String string, final int index) {
		if (string == null || index < 0 || index >= string.length()) {
			return 0;
		} else {
			return toLower(string.charAt(index));
		}
	}

}

package ch.eisenring.core.datatypes.date.format;

import java.util.Calendar;
import java.util.TimeZone;

import ch.eisenring.core.collections.Lookup;
import ch.eisenring.core.collections.impl.AtomicArrayLookup;
import ch.eisenring.core.datatypes.strings.StringMaker;

/**
 * Predefined date element instances
 */
final class DateElementFactory {

	public final static String[] MONTH_NAMES_SHORT = {
		"Jan", "Feb", "Mrz", "Apr", "Mai", "Jun", "Jul", "Aug", "Sep", "Okt", "Nov", "Dez" 	
	};	

	public final static String[] MONTH_NAMES_LONG = {
		"<PERSON><PERSON>r", "<PERSON>ruar", "<PERSON><PERSON><PERSON>", "April", "<PERSON>", "Jun<PERSON>", "Juli", "August", "September", "Oktober", "November", "Dezember" 	
	};

	public final static String[] WEEKDAYS_LONG = {
		null, "Sonntag", "<PERSON>ag", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>eitag", "Samstag"
	};

	public final static String[] WEEKDAYS_SHORT = {
		null, "So", "<PERSON>", "<PERSON>", "<PERSON>", "Do", "Fr", "Sa"
	};
	
	public final static DateElement WEEKDAY_SHORT = new DateElement() {
		@Override
		public void append(final StringMaker maker, final Calendar calendar) {
			final int weekday = calendar.get(Calendar.DAY_OF_WEEK);
			maker.append(WEEKDAYS_SHORT[weekday]);
		}
	};

	public final static DateElement WEEKDAY_LONG = new DateElement() {
		@Override
		public void append(final StringMaker maker, final Calendar calendar) {
			final int weekday = calendar.get(Calendar.DAY_OF_WEEK);
			maker.append(WEEKDAYS_LONG[weekday]);
		}
	};

	public final static DateElement ERA_DESIGNATOR = new DateElement() {
		@Override
		public void append(final StringMaker maker, final Calendar calendar) {
			maker.append("n. Chr.");
		}
	};

	public final static DateElement AM_PM = new DateElement() {
		@Override
		public void append(final StringMaker maker, final Calendar calendar) {
			maker.append(calendar.get(Calendar.AM_PM) == Calendar.AM ? 'A' : 'P');
			maker.append('M');
		}
	};

	public final static DateElement MONTH_SHORT = new DateElement() {
		@Override
		public void append(final StringMaker maker, final Calendar calendar) {
			final int m = calendar.get(Calendar.MONTH);
			maker.append(MONTH_NAMES_SHORT[m]);
		}
	};

	public final static DateElement MONTH_LONG = new DateElement() {
		@Override
		public void append(final StringMaker maker, final Calendar calendar) {
			final int m = calendar.get(Calendar.MONTH);
			maker.append(MONTH_NAMES_LONG[m]);
		}		
	};

	public final static DateElement SHORT_YEAR = new DateElement() {
		@Override
		public void append(final StringMaker maker, final Calendar calendar) {
			append(maker, calendar.get(Calendar.YEAR) % 100, 2);
		}
	};

	public final static DateElement SHORT_YEAR_OF_WEEK = new DateElement() {
		@Override
		public void append(final StringMaker maker, final Calendar calendar) {
			append(maker, getYearOfWeek(calendar) % 100, 2);
		}
	};

	public final static DateElement TIMEZONE_RFC822 = new DateElement() {
		@Override
		public void append(final StringMaker maker, final Calendar calendar) {
			int value = TimeZone.getDefault().getOffset(calendar.getTimeInMillis());
			if (value < 0) {
				maker.append('-');
				value /= -60000;
			} else {
				maker.append('+');
				value /= 60000;
			}
			append(maker, value / 60, 2);
			append(maker, value % 60, 2);
		}
	};

	// --------------------------------------------------------------
	// ---
	// --- Date element caching
	// ---
	// --------------------------------------------------------------
	private final static Lookup<String, DateElement> CACHE = new AtomicArrayLookup<>();

	/**
	 * Get date element for character and count
	 */
	public final static DateElement get(final String pattern) throws IllegalArgumentException {
		DateElement result = CACHE.get(pattern);
		if (result != null)
			return result;
		result = create(pattern.charAt(0), pattern.length());
		CACHE.put(pattern, result);
		return result;
	}

	protected final static DateElement create(final char formatChar, final int patternLength) {
		switch (formatChar) {
			default:
				return new DateElementChar(formatChar, patternLength);
			// era designator
			case 'G':
				return ERA_DESIGNATOR;
			// year
			case 'Y':
				if (patternLength == 2)
					return SHORT_YEAR_OF_WEEK;
				return new DateElement() {
					@Override
					public void append(final StringMaker maker, final Calendar calendar) {
						append(maker, getYearOfWeek(calendar), patternLength);
					}
				};
			case 'y':
				if (patternLength == 2)
					return SHORT_YEAR;
				return new DateElementField(Calendar.YEAR, patternLength);
			// month in year
			case 'M':
				switch (patternLength) {
					case 1:
					case 2: return new DateElement() {
						@Override
						public void append(final StringMaker maker, final Calendar calendar) {
							append(maker, calendar.get(Calendar.MONTH) + 1, patternLength);
						}
					};
					case 3: return MONTH_SHORT;
					default: return MONTH_LONG;
				}
			// week in year
			case 'w':
				return new DateElementField(Calendar.WEEK_OF_YEAR, patternLength);
			// week in month
			case 'W':
				return new DateElementField(Calendar.WEEK_OF_MONTH, patternLength);
			// day in year
			case 'D':
				return new DateElementField(Calendar.DAY_OF_YEAR, patternLength);
			// day in month
			case 'd':
				return new DateElementField(Calendar.DAY_OF_MONTH, patternLength);
			// day of week in month
			case 'F':
				return new DateElementField(Calendar.DAY_OF_WEEK_IN_MONTH, patternLength);
			// day in week
			case 'E':
				return patternLength <= 3 ? WEEKDAY_SHORT : WEEKDAY_LONG;
			// AM/PM marker
			case 'a':
				return AM_PM;
			// Hour of day (0-23)
			case 'H':
				return new DateElementField(Calendar.HOUR_OF_DAY, patternLength);
			// Hour of day (1-24)
			case 'k':
				return new DateElement() {
					@Override
					public void append(final StringMaker maker, final Calendar calendar) {
						int hour = calendar.get(Calendar.HOUR_OF_DAY);
						if (hour == 0)
							hour = 24;
						append(maker, hour, patternLength);
					}
				};
			// Hour in AM/PM (0-11)
			case 'K':
				return new DateElementField(Calendar.HOUR, patternLength);
			// Hour in AM/PM (1-12)
			case 'h':
				return new DateElement() {
					@Override
					public void append(final StringMaker maker, final Calendar calendar) {
						int hour = calendar.get(Calendar.HOUR);
						if (hour == 0)
							hour = 12;
						append(maker, hour, patternLength);
					}
				};
			// minute
			case 'm':
				return new DateElementField(Calendar.MINUTE, patternLength);
			// second
			case 's':
				return new DateElementField(Calendar.SECOND, patternLength);
			// second
			case 'S':
				return new DateElementField(Calendar.MILLISECOND, patternLength);
				
			// time zone
			case 'X':
			case 'z':
				throw new IllegalArgumentException();
			case 'Z':
				return TIMEZONE_RFC822;
			// quarter
			case 'q':
				return new DateElement() {
					@Override
					public void append(final StringMaker maker, final Calendar calendar) {
						int quarter = (calendar.get(Calendar.MONTH) / 3) + 1;
						append(maker, quarter, patternLength);
					}
				};
			// day number of week
			case 'u':
				return new DateElement() {
					@Override
					public void append(final StringMaker maker, final Calendar calendar) {
						int day = calendar.get(Calendar.DAY_OF_WEEK) - 1;
						if (day < 1)
							day += 7;
						append(maker, day, patternLength);
					}
				};
		}
	}

}

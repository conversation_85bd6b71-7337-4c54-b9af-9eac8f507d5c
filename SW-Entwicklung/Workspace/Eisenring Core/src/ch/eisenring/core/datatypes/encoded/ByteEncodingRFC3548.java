//package ch.eisenring.core.datatypes.encoded;
//
//import java.io.ByteArrayOutputStream;
//
//import ch.eisenring.core.datatypes.strings.StringMaker;
//import ch.eisenring.core.datatypes.strings.Strings;
//
//final class ByteEncodingRFC3548 extends ByteEncoding {
//
//	private final static char[] DIGITS = {
//        'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H',
//        'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P',
//        'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X',
//        'Y', 'Z', '2', '3', '4', '5', '6', '7'
//	};
//	
//	private final static char PAD = '=';
//
//	ByteEncodingRFC3548(final int id) {
//		super(id, "R3548", "Base32 (RFC 3548)", "R3548:");
//	}
//
//	@Override
//	protected byte[] decodeImpl(final CharSequence encoded) throws IllegalArgumentException {
//		if (Strings.startsWithIgnoreCase(encoded, prefix))
//			return decodeImpl(encoded, 6, encoded.length());
//		return decodeImpl(encoded, 0, encoded.length());
//	}
//
//	private static byte[] decodeImpl(final CharSequence encoded, final int startIndex, final int endIndex) throws IllegalArgumentException {
//		// TODO : implement
//		final ByteArrayOutputStream byteOut = new ByteArrayOutputStream();
//		int i = startIndex;
//		int buffer = 0;
//		boolean upper = true;
//		while (i < endIndex) {
//			final char c = encoded.charAt(i);
//			// skip white spaces
//			if (Strings.isWhitespace(c))
//				continue;
//			final int nibble = Strings.getHEXValue(c);
//			if (nibble < 0)
//				throw new IllegalArgumentException("invalid character '" + c + "' at index " + i);
//			if (upper) {
//				buffer = nibble << 4;
//			} else {
//				buffer |= nibble;
//				byteOut.write(buffer);
//			}
//			upper = !upper;
//			++i;
//		}
//		// ensure we have parsed an even number of digits (no half-byte pending) 
//		if (!upper) {
//			throw new IllegalArgumentException("odd number of hex-digits");
//		}
//		return byteOut.toByteArray();		
//	}
//
//	@Override
//	protected String encodeImpl(final byte[] bytes, final boolean prefixed) {
//		final StringMaker buffer = StringMaker.obtain();
//		if (prefixed)
//			buffer.append(prefix);
//		// TODO : implement
//		for (final byte b : bytes) {
//		}
//		return buffer.release();
//	}
//
//}

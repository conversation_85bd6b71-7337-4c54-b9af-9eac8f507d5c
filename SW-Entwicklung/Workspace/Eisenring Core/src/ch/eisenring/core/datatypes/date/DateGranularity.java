package ch.eisenring.core.datatypes.date;

import java.util.Calendar;
import java.util.Date;

import ch.eisenring.core.datatypes.date.format.HEAGDateFormat;

/**
 * Application interface for date rounding providers
 */
public interface DateGranularity {

	/**
	 * Rounds the date and then alters it by offset times granularity.
	 * (e.g. for calendar week it will add 7 times offset days).
	 * 
	 * A NULL argument gives a NULL result.
	 */
	public Date round(final Date date, final int offset);

	/**
	 * Rounds the date stored in the given calendar.
	 * Calendar must not be null.
	 */
	public void round(final Calendar calendar, final int offset);
	
	/**
	 * Rounds the date represented by timestamp.
	 * 
	 * A NULL argument gives a NULL result.
	 */
	public long round(final long timestamp, final int offset);

	/**
	 * Converts date to String suitable for representing the granularity,
	 * (e.g. for calendar week its YYYY/ww, for quarter its yyyy/QQ etc.)
	 * 
	 * If the date is NULL the provided NULL-value is returned.
	 */
	public String toString(final Date date, final String nullValue);

	/**
	 * Gets a DateFormat suitable to represent the granularity in human readable form.
	 * (e.g. for calendar week its YYYY/ww, for quarter its yyyy/QQ etc.)
	 */
	public HEAGDateFormat getFormat();

}

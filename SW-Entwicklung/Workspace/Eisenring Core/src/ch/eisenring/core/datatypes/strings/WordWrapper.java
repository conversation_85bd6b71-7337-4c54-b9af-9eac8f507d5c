package ch.eisenring.core.datatypes.strings;

final class WordWrapper {

	static String wordWrapToLineLength(final CharSequence source, final int maxLineLength) {
		if (maxLineLength <= 0)
			return Strings.toString(source);
		if (source == null)
			return null;
		final int sourceLen = source.length();
		if (sourceLen <= 0)
			return "";

		final int maxTrackBack = Math.max(maxLineLength >> 2, Math.min(maxLineLength, 8));
		final StringMaker b = StringMaker.obtain();

		int i = 0;
		int lineLen = 0;
		int lastWhiteSpace = 0;
		while (i < sourceLen) {
			final char c = source.charAt(i++);
			final boolean isWhiteSpace = Strings.isWhitespace(c);
			if (isWhiteSpace) {
				if (c == '\n') {
					lineLen = 0;
					lastWhiteSpace = -1;
					b.append(c);
					continue;
				}
				if (lineLen == 0 && (c == ' ' || c == '\t'))
					continue;
				lastWhiteSpace = i - 1;
			}
			// normal character or fall through
			b.append(c);
			++lineLen;
			if (lineLen >= maxLineLength) {
				// did we encounter a white space recently?
				if (lastWhiteSpace > 0 && ((i - lastWhiteSpace) <= maxTrackBack)) {
					// rewind n characters
					int rewind = i - lastWhiteSpace;
					i -= rewind;
					++i;
					b.setLength(b.length() - rewind);
				}
				b.append('\n');
				lineLen = 0;
				lastWhiteSpace = -1;
			}
		}
		return b.release();
	}

//	static String TEST = "In das DMS ist ein einfacher Bildanzeiger integriert, mit dem ein ganzer Ordner direkt im DMS durchblättert werden kann. Auch wenn Sie für nur ein ganz spezielles, einzelnes Bild Anzeigen gewählt haben, bietet der integrierte Bildanzeiger immer die Möglichkeit zum nächsten/vorhergehenden Bild im Ordner zu blättern ohne erst in das Hauptfenster zurückkehren zu müssen.";
//	
//	public static void main(String[] argv) {
//		for (int l = 30; l <= 30; ++l) {
//			String f = Strings.wordWrapToLineLength(TEST, l);
//			System.out.println(Strings.repeat('-', 80));
//			System.out.println(f);
//		}
//	}

}

package ch.eisenring.core.datatypes.strings.format.impl;

import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.datatypes.strings.format.api.ObjectFormat;

public final class TrimCharsequenceFormat implements ObjectFormat<CharSequence> {

	@Override
	public void appendTo(final StringMaker target, final CharSequence value) {
		if (value == null)
			return;
		final int length = value.length();
		if (length == 0)
			return;
		int start = 0;
		while (start < length && Strings.isWhitespace(value.charAt(start)))
			++start;
		if (start >= length)
			return;
		int end = length;
		while (--end >= start && Strings.isWhitespace(value.charAt(end)));
		++end;
		if (end <= start)
			return;
		if (start <= 0 && end >= length) {
			target.append(value);
		} else {
			target.append(value, start, end);
		}
	}

}

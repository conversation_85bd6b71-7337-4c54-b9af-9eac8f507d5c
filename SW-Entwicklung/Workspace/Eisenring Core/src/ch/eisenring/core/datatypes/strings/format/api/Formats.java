package ch.eisenring.core.datatypes.strings.format.api;

import ch.eisenring.core.datatypes.strings.format.impl.ByteSizeDescription;
import ch.eisenring.core.datatypes.strings.format.impl.FileExtensionFormat;
import ch.eisenring.core.datatypes.strings.format.impl.FixedPointFormat;
import ch.eisenring.core.datatypes.strings.format.impl.HexFullFormat;
import ch.eisenring.core.datatypes.strings.format.impl.HexRGBFormat;
import ch.eisenring.core.datatypes.strings.format.impl.HexShortFormat;
import ch.eisenring.core.datatypes.strings.format.impl.IntegerFormat;
import ch.eisenring.core.datatypes.strings.format.impl.JavaSourceStringFormat;
import ch.eisenring.core.datatypes.strings.format.impl.SimpleClassNameFormat;
import ch.eisenring.core.datatypes.strings.format.impl.StripFileExtensionFormat;
import ch.eisenring.core.datatypes.strings.format.impl.TrimCharsequenceFormat;

public interface Formats {

	/**
	 * Formats integer as hexadecimal with leading zeros (number of digits
	 * is decided by type, e.g. byte = 2, short = 4, int = 8, long = 16. 
	 */
	public final static PrimitiveFormat HEX_FULL = new HexFullFormat();

	/**
	 * Formats value as hexadecimal 24-bit RGB value.
	 * Excess upper bits are ignored. When formatting a smaller type (byte, short)
	 * the value is zero-extended before being used.
	 */
	public final static PrimitiveFormat HEX_RGB = new HexRGBFormat();

	/**
	 * Formats integer as hexadecimal without leading zeros. 
	 */
	public final static PrimitiveFormat HEX_SHORT = new HexShortFormat();

	/**
	 * Formats integer as human readable size (e.g. "1.23 MB")
	 */
	public final static ByteSizeDescription BYTESIZE = ByteSizeDescription.INSTANCE;

	/**
	 * Takes CharSequence and cuts away leading/trailing whitespaces
	 */
	public final static ObjectFormat<CharSequence> TRIM = new TrimCharsequenceFormat();

	/**
	 * Formats integer with grouping separators.
	 */
	public final static PrimitiveFormat INTEGER = new IntegerFormat();

	/**
	 * Fixed point format (2 decimal places)
	 */
	public final static PrimitiveFormat FIXED_POINT_2 = new FixedPointFormat(3, "'", 2, ".");

	/**
	 * Fixed point format (3 decimal places)
	 */
	public final static PrimitiveFormat FIXED_POINT_3 = new FixedPointFormat(3, "'", 3, ".");

	/**
	 * Appends the arguments file extension.
	 * 
	 * If the argument is NULL or empty, nothing is appended.
	 * If there no file extension in the argument, nothing is appended.
	 * If a file extension if found in the argument, a separating dot is inserted,
	 * if the target is not empty and the last character in the target is not a dot.
	 */
	public final static ObjectFormat<Object> FILE_EXTENSION = new FileExtensionFormat();

	/**
	 * Appends the argument without file extension.
	 */
	public final static ObjectFormat<Object> STRIP_FILE_EXTENSION = new StripFileExtensionFormat(); 

	/**
	 * Appends a string formatting control characters as if it were java source code
	 */
	public final static ObjectFormat<CharSequence> JAVA_SOURCE_STRING = new JavaSourceStringFormat();

	/**
	 * Appends the arguments last dot-separated element.
	 */
	public final static ObjectFormat<Object> SIMPLE_CLASSNAME = new SimpleClassNameFormat();

}

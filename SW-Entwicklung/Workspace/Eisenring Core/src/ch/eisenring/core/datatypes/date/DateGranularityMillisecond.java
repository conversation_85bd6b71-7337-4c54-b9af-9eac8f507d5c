package ch.eisenring.core.datatypes.date;

import java.util.Calendar;
import java.util.Date;

import ch.eisenring.core.datatypes.date.format.HEAGDateFormat;

final class DateGranularityMillisecond extends DateGranularityCode {

	DateGranularityMillisecond(final int id) {
		super(id, Integer.valueOf(id), "", "", HEAGDateFormat.get("dd.MM.yyyy HH:mm:ss"));
	}
	
	@Override
	public Date round(final Date date, final int offset) {
		if (offset == 0 || date == null)
			return date;
		final long millis = TimestampUtil.toTimestamp(date);
		return TimestampUtil.toDate(millis + offset);
	}

	@Override
	protected String toStringImpl(final Date date) {
		return formatDate(date);
	}

	@Override
	public void round(final Calendar calendar, final int offset) {
		calendar.add(Calendar.MILLISECOND, offset);
	}

}

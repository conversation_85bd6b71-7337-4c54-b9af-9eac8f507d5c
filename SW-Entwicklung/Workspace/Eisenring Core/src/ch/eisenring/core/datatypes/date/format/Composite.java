package ch.eisenring.core.datatypes.date.format;

import java.util.Calendar;

import ch.eisenring.core.datatypes.strings.StringMaker;

final class Composite extends HEAGDateFormat {

	protected final DateElement[] elements;
	
	public Composite(final String pattern, final DateElement[] elements) {
		super(pattern);
		this.elements = elements;
	}

	@Override
	public void appendTo(final StringMaker target, final Calendar calendar) {
		for (final DateElement element : elements)
			element.append(target, calendar);
	}

}

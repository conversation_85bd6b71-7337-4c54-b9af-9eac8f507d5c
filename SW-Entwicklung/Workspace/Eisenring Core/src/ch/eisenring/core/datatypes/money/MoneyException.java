package ch.eisenring.core.datatypes.money;

/**
 * Exception thrown when a currency conversion is impossible,
 * or currency is invalid for the use case.
 */
@SuppressWarnings("serial")
public class MoneyException extends RuntimeException {

	public MoneyException(final String message) {
		super(message);
	}

	public MoneyException(final String message, final Throwable cause) {
		super(message, cause);
	}

}

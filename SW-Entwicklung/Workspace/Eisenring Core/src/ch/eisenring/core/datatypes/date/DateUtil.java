package ch.eisenring.core.datatypes.date;

import java.util.Calendar;
import java.util.Date;

//@Deprecated
public final class DateUtil extends TimestampUtil {

	// Use Date.from(Instant.ofEpochMilli()) for Java 21 compatibility
	public final static Date MIN_KWDATE = Date.from(java.time.Instant.ofEpochMilli(MIN_KWTIMESTAMP));
	public final static Date MAX_KWDATE = Date.from(java.time.Instant.ofEpochMilli(MAX_KWTIMESTAMP));
	public final static Date MIN_DATE = Date.from(java.time.Instant.ofEpochMilli(MIN_TIMESTAMP));
	public final static Date MAX_DATE = Date.from(java.time.Instant.ofEpochMilli(MAX_TIMESTAMP));

	private DateUtil() {
	}

	/**
	 * Gets the earliest of all given dates
	 */
	//@Deprecated
	public static Date min(final Date ... dates) {
		if (dates == null)
			return null;
		Date min = null;
		for (int i=dates.length-1; i>=0; --i) {
			final Date date = dates[i];
			if (min == null) {
				min = date;
			} else if (date != null && min.after(date)) {
				min = date;
			}
		}
		return min;
	}
	
	/**
	 * Gets the latest of all given dates
	 */
	//@Deprecated
	public static Date max(final java.util.Date ... dates) {
		if (dates == null)
			return null;
		Date max = null;
		for (int i=dates.length-1; i>=0; --i) {
			final Date date = dates[i];
			if (max == null) {
				max = date;
			} else if (date != null && max.before(date)) {
				max = date;
			}
		}
		return max;
	}

	/**
	 * Create a new date from date with time as given by parameters.
	 */
	//@Deprecated
	public static Date setTime(final Date date, final int hours, final int minutes, final int seconds, final int millis) {
		if (date == null)
			return null;
		final HEAGCalendar calendar = HEAGCalendar.obtain(date);
		calendar.setTime(hours, minutes, seconds, millis);
		return Date.from(java.time.Instant.ofEpochMilli(calendar.release().getTimeInMillis()));
	}
	
	/**
	 * Gets the most recent monday previous to date.
	 * If date is already a monday, the date is the same.
	 * The time is set to 00:00:00.000.
	 */
	//@Deprecated
	public static Date findMonday(final Date date, final int offset) {
		final HEAGCalendar calendar = (date == null)
				? HEAGCalendar.obtainNow()
				: HEAGCalendar.obtain(date.getTime());
		DateGranularityCode.KW.round(calendar, offset);
		return calendar.release().getTime();
	}

	/**
	 * Adds the specified number of days to date, considering
	 * only working days. Working days are Mo-Fr, Sa-Su are
	 * considered holydays.
	 * No other holydays are taken into account.
	 * 
	 * Note: if adding zero days, the date may still change,
	 * since it will move to the next working day.
	 * 
	 * If days added is negative, exception is thrown.
	 */
	//@Deprecated
	public static Date addWorkingDays(final Date date, final int days) throws IllegalArgumentException {
		if (date == null)
			throw new IllegalArgumentException("date is null");
		final HEAGCalendar calendar = HEAGCalendar.obtain(date.getTime());
		final int direction = days < 0 ? -1 : 1;
		// move date by whole weeks
		int remainder;
		if (days == 0) {
			remainder = 0;
		} else {
			final int weeks = days / (direction * 5);
			calendar.add(Calendar.DAY_OF_YEAR, weeks * (direction * 7));
			remainder = (days * direction) % 5;
		}
		while (remainder > 0) {
			calendar.add(Calendar.DAY_OF_YEAR, direction);
			while (!isWorkDay(calendar))
				calendar.add(Calendar.DAY_OF_YEAR, direction);
			--remainder;
		}
		// advance date until working day
		while (!isWorkDay(calendar)) {
			calendar.add(Calendar.DAY_OF_YEAR, direction);
		}
		return calendar.release().getTime();
	}

	public static boolean isWorkDay(final Calendar calendar) throws IllegalArgumentException {
		if (calendar == null)
			throw new IllegalArgumentException("calendar is null");
		final int weekday = calendar.get(Calendar.DAY_OF_WEEK);
		return weekday != Calendar.SUNDAY && weekday != Calendar.SATURDAY;
	}

	// --------------------------------------------------------------
	// ---
	// --- To String formatting
	// ---
	// --------------------------------------------------------------
	/**
	 * Makes a copy of the given date. If the date is null, null is returned.
	 * For any non-null argument this returns a java.util.Date copy set to the
	 * same time the argument date was set to.
	 * This implies a type conversion if the argument is a derived date type.
	 */
	//@Deprecated
	public static Date copy(final Date date) {
		if (date == null)
			return null;
		return new Date(date.getTime());
	}
	
	/**
	 * Compare two dates (any subclass) if they represent the same time.
	 * (thanks to the date API's screwed design this is actually a problem).
	 */
	//@Deprecated
	public static boolean equals(final Date d1, final Date d2) {
		if (d1 == d2)
			return true;
		if (d1 == null || d2 == null)
			return false;
		return d1.getTime() == d2.getTime();
	}

	//@Deprecated
	public static int compare(final Date d1, final Date d2) {
		if (d1 == null) {
			return d2 == null ? 0 : -1;
		} else if (d2 == null) {
			return 1;
		}
		return d1.compareTo(d2);
	}

/**
	 * Calculates new KW date from date + delta in weeks.
	 * KW date is constrained to KW min/max.
	 */
	public static int getKW(final Date date) {
		Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
		return calendar.get(Calendar.WEEK_OF_YEAR);
	}

}

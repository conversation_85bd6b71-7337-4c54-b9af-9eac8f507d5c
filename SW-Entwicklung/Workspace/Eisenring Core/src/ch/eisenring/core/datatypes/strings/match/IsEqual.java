package ch.eisenring.core.datatypes.strings.match;

import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.iterators.Filter;

final class IsEqual implements Filter<CharSequence> {

	private final CharSequence criterion;

	public IsEqual(final CharSequence criterion) {
		this.criterion = criterion;
	}

	@Override
	public boolean accepts(final CharSequence charSequence) {
		return Strings.equals(charSequence, criterion);
	}

}

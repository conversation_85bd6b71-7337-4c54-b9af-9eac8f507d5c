package ch.eisenring.core.datatypes.strings.format.api;

import ch.eisenring.core.datatypes.strings.StringMaker;

/**
 * Formatter that handles integer types
 */
public interface PrimitiveFormat extends Format {

	public abstract void appendTo(final StringMaker target, final byte value);

	public abstract void appendTo(final StringMaker target, final short value);

	public abstract void appendTo(final StringMaker target, final int value);

	public abstract void appendTo(final StringMaker target, final long value);

}

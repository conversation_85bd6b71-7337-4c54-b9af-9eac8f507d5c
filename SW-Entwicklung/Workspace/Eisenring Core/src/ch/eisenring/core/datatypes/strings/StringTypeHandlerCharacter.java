package ch.eisenring.core.datatypes.strings;

final class StringTypeHandlerCharacter implements StringTypeHandler {

	@Override
	public String toString(final Object value) {
		return toString(((Character) value).charValue());
	}

	@Override
	public char[] toCharArray(final Object value) {
		return new char[((Character) value).charValue()];
	}

	private final static String[] SINGLE_CHAR_STRING_CACHE = new String[256];
	
	public static String toString(final char character) {
		if (character >= SINGLE_CHAR_STRING_CACHE.length)
			return String.valueOf(character);
		String s = SINGLE_CHAR_STRING_CACHE[character];
		if (s == null) {
			s = String.valueOf(character);
			s = s.intern();
			SINGLE_CHAR_STRING_CACHE[character] = s;
		}
		return s;	
	}

}

package ch.eisenring.core.datatypes.primitives;

import java.io.IOException;

import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.StringMakerFriendly;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.io.StreamReader;
import ch.eisenring.core.io.StreamWriter;
import ch.eisenring.core.io.Streamable;
import ch.eisenring.core.threading.ThreadCoreLocal;

/**
 * An int wrapper that features reuse for various applications
 */
@SuppressWarnings("serial")
public final class IntKey extends Number implements Streamable, StringMakerFriendly {

	private final static ThreadCoreLocal<IntKey> LOOKUP_KEY =
			new ThreadCoreLocal<>(() -> { return new IntKey(); });

	private final static IntKey[] CACHE;
	static {
		final IntKey[] c = new IntKey[128];
		for (int i=0; i<c.length; ++i)
			c[i] = new IntKey(i);
		CACHE = c;
	}
	
	private int value;

	private IntKey() {
	}

	private IntKey(final int value) {
		this.value = value;
	}

	// --------------------------------------------------------------
	// ---
	// --- Factory methods
	// ---
	// --------------------------------------------------------------
	public static IntKey valueOf(final int value) {
		if (value >= 0 && value < CACHE.length)
			return CACHE[value];
		return new IntKey(value);
	}

	public static IntKey valueOf(final Number value) {
		if (value instanceof IntKey)
			return (IntKey) value;
		return valueOf(value.intValue());
	}

	private static IntKey getLookupKey(final int value) {
		final IntKey key = LOOKUP_KEY.get();
		key.value = value;
		return key;
	}

	// --------------------------------------------------------------
	// ---
	// --- Lookup methods
	// ---
	// --------------------------------------------------------------
	/**
	 * Performs a lookup for value (does not create garbage)
	 */
	public static <U> U get(final java.util.Map<IntKey, U> map, final int value) {
		return map.get(getLookupKey(value));
	}

	/**
	 * Performs a key lookup for value (does not create garbage)
	 */
	public static boolean containsKey(final java.util.Map<IntKey, ?> map, final int value) {
		return map.containsKey(getLookupKey(value));
	}

	/**
	 * Removes the value stored under key from map
	 */
	public static <U> U remove(final java.util.Map<IntKey, U> map, final int key) {
		return map.remove(getLookupKey(key));
	}

	/**
	 * Adds value under key to map
	 */
	public static <U> U put(final java.util.Map<IntKey, U> map, final int key, final U value) {
		return map.put(valueOf(key), value);
	}

	// --------------------------------------------------------------
	// ---
	// --- Number implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public byte byteValue() {
		return (byte) value;
	}

	@Override
	public short shortValue() {
		return (short) value;
	}

	@Override
	public int intValue() {
		return value;
	}

	@Override
	public long longValue() {
		return value;
	}

	@Override
	public float floatValue() {
		return value;
	}

	@Override
	public double doubleValue() {
		return value;
	}

	// --------------------------------------------------------------
	// ---
	// --- Streamable implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public void read(final StreamReader reader) throws IOException {
		value = reader.readInt();
	}

	@Override
	public void write(final StreamWriter writer) throws IOException {
		writer.writeInt(value);
	}

	// --------------------------------------------------------------
	// ---
	// --- Object overrides
	// ---
	// --------------------------------------------------------------
	@Override
	public int hashCode() {
		return value;
	}

	@Override
	public boolean equals(final Object o) {
		return o instanceof IntKey && ((IntKey) o).value == this.value;
	}

	@Override
	public String toString() {
		return Strings.toString(value);
	}

	@Override
	public void toStringMaker(final StringMaker target) {
		target.append(value);
	}

}

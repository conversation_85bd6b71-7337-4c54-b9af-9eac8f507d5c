package ch.eisenring.core.datatypes.strings.format.impl;

import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.datatypes.strings.format.api.ObjectFormat;

/**
 * Appends the last dot-separated element, 
 * or the original sequence if not dot occurs.
 */
public class SimpleClassNameFormat implements ObjectFormat<Object> {

	public void appendTo(final StringMaker target, final CharSequence value) {
		if (value == null)
			return;
		final int i = Strings.lastIndexOf(value, '.');
		if (i < 0)
			target.append(value);
		else
			target.append(value, i+1, value.length());
	}

	@Override
	public void appendTo(final StringMaker target, final Object value) {
		if (value == null) {
			return;
		} else if (value instanceof CharSequence) {
			appendTo(target, (CharSequence) value);
		} else if (value instanceof Class) {
			appendTo(target, ((Class<?>) value).getName());
		} else {
			appendTo(target, value.getClass().getName());
		}
	}
	
}

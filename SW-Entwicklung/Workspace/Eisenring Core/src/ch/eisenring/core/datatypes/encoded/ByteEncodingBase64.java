package ch.eisenring.core.datatypes.encoded;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.util.Arrays;

import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.io.base64.Base64Constants;
import ch.eisenring.core.io.binary.BinaryHolder;

final class ByteEncodingBase64 extends ByteEncoding {

	private final static char PAD = Base64Constants.PADDING_CHAR;

	private final static char[] DIGITS = Base64Constants.BASE_64_CHARS;

	private final static byte[] SYMBOLS;
	
	static {
		final byte[] s = new byte[128];
		Arrays.fill(s, (byte) -1);
		for (int i=0; i<DIGITS.length; ++i) {
			s[DIGITS[i]] = (byte) i; 
		}
		SYMBOLS = s;
	}
	
	ByteEncodingBase64(final int id) {
		super(id, "B64", "Base 64", "B64:");
	}

	@Override
	public byte[] decode(final CharSequence encoded, final int startIndex, final int endIndex) throws IllegalArgumentException {
		final ByteArrayOutputStream byteOut = new ByteArrayOutputStream();
		int bits = 0;
		int shift = 18;
		int pads = 0;
		int i = startIndex;
		while (true) {
			final char c = i < endIndex ? encoded.charAt(i++) : PAD;
			if (Strings.isWhitespace(c))
				continue;
			if (c == PAD) {
				++pads;
			} else {
				final int v = c > SYMBOLS.length ? -1 : SYMBOLS[c];
				if (v < 0)
					throw new IllegalArgumentException("invalid character '" + c + "' at index " + i);
				bits |= v << shift;
			}
			shift -= 6;
			if (shift < 0) {
				if (pads <= 2)
					byteOut.write(bits >> 16);
				if (pads <= 1)
					byteOut.write(bits >> 8);
				if (pads <= 0)
					byteOut.write(bits);
				shift = 18;
				bits = 0;
				pads = 0;
				if (i >= endIndex)
					break;
			}
		}
		return byteOut.toByteArray();
	}
	
	@Override
	protected String encodeImpl(final byte[] bytes, final boolean prefixed) {
		final int length = bytes.length;
		final int estimatedLength = estimateEncodedLength(length, prefixed);
		final StringMaker b = StringMaker.obtain(estimatedLength);
		if (prefixed)
			b.append(prefix);
		int i = 0;
		while (i < length) {
			int count = 1;
			int bits = (bytes[i++] & 0xFF) << 16;
			if (i < length) {
				bits |= (bytes[i++] & 0xFF) << 8;
				++count;
			}
			if (i < length) {
				bits |= (bytes[i++] & 0xFF);
				++count;
			}
			// encode 1st char
			b.append(DIGITS[(bits >> 18) & 0x3F]);
			// encode 2nd char
			b.append(DIGITS[(bits >> 12) & 0x3F]);
			// encode 3rd char
			b.append(count >= 2 ? DIGITS[(bits >> 6) & 0x3F] : PAD);
			// encode 4th char
			b.append(count >= 3 ? DIGITS[bits & 0x3F] : PAD);
		}
		return b.release();
	}

	private int estimateEncodedLength(final long byteLength, final boolean prefixed) {
		long estimate = (byteLength << 2) / 3;
		if (prefixed)
			estimate += Strings.length(prefix);
		estimate += 8;
		if (estimate >= 0x7FFF_FFF0)
			throw new OutOfMemoryError();
		return (int) estimate;
	}

	@Override
	protected String encodeImpl(final BinaryHolder binary, boolean prefixed) throws Exception {
		final long length = binary.size();
		final int estimatedLength = estimateEncodedLength(length, prefixed);
		final StringMaker b = StringMaker.obtain(estimatedLength);
		if (prefixed)
			b.append(prefix);
		try (final InputStream input = binary.getInputStream()) {
			while (true) {
				final int b1 = input.read();
				final int b2 = input.read();
				final int b3 = input.read();
				if (b1 < 0)
					break;
				int bits = (b1 & 0xFF) << 16;
				if (b2 >= 0) {
					bits |= (b2 & 0xFF) << 8;
				}
				if (b3 >= 0) {
					bits |= (b3 & 0xFF);
				}
				// encode 1st char
				b.append(DIGITS[(bits >> 18) & 0x3F]);
				// encode 2nd char
				b.append(DIGITS[(bits >> 12) & 0x3F]);
				// encode 3rd char
				b.append(b2 >= 0 ? DIGITS[(bits >> 6) & 0x3F] : PAD);
				// encode 4th char
				b.append(b3 >= 0 ? DIGITS[bits & 0x3F] : PAD);
			}
		}
		return b.release();
	}

}

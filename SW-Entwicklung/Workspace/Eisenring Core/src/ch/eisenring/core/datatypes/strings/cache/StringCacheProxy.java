package ch.eisenring.core.datatypes.strings.cache;

import java.util.concurrent.atomic.AtomicReference;

/**
 * A redirecting StringCache, useful as an indirection
 * between clients and an immutable StringCache.
 * Allows atomic replacement of the underlying cache instance.
 */
public final class StringCacheProxy implements StringCache {

	private final AtomicReference<StringCache> CACHE =
			new AtomicReference<>(StringCache.DUMMY);

	public StringCacheProxy() {
	}

	public StringCacheProxy(final StringCache cache) {
		if (cache != null)
			CACHE.set(cache);
	}

	@Override
	public String get(final CharSequence charSeq) {
		return CACHE.get().get(charSeq);
	}

	@Override
	public String get(final String string) {
		return CACHE.get().get(string);
	}

	/**
	 * Changes the underlying cache delegate. Setting this to NULL
	 * sets a pass-through default instance.
	 */
	public void setDelegate(final StringCache delegate) {
		CACHE.set(delegate == null ? StringCache.DUMMY : delegate);
	}

}

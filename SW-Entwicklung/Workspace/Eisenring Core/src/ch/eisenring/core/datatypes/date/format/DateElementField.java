package ch.eisenring.core.datatypes.date.format;

import java.util.Calendar;

import ch.eisenring.core.datatypes.strings.StringMaker;

final class DateElementField extends DateElement {
	
	final int calendarField;
	final int minDigits;

	protected DateElementField(final int calendarField, final int minDigits) {
		this.calendarField = calendarField;
		this.minDigits = minDigits;
	}

	@Override
	public void append(final StringMaker maker, final Calendar calendar) {
		append(maker, calendar.get(calendarField), minDigits);
	}

}

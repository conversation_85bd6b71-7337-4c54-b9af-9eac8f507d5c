package ch.eisenring.core.datatypes.strings.format.impl;

import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.datatypes.strings.format.api.FloatFormat;
import ch.eisenring.core.datatypes.strings.format.api.PrimitiveFormat;

/**
 * Formatting of value as hexadecimal, with leading zeros.
 * 
 * For float and double, this yields the hexadecimal representation
 * of the raw bit representation of the value. 
 */
public final class HexFullFormat implements PrimitiveFormat, FloatFormat {

	@Override
	public void appendTo(final StringMaker target, final byte b) {
		target.append(Strings.HEX_DIGITS[(b >> 4) & 15]);
		target.append(Strings.HEX_DIGITS[ b       & 15]);
	}

	@Override
	public void appendTo(final StringMaker target, final short value) {
		target.ensureCapacity(target.length() + 4);
		for (int s=12; s>=0; s-=4) {
			target.append(Strings.HEX_DIGITS[(value >> s) & 15]);
		}
	}

	@Override
	public void appendTo(final StringMaker target, final int value) {
		target.ensureCapacity(target.length() + 8);
		for (int s=28; s>=0; s-=4) {
			target.append(Strings.HEX_DIGITS[(value >> s) & 15]);
		}
	}

	@Override
	public void appendTo(final StringMaker target, final long value) {
		target.ensureCapacity(target.length() + 16);
		for (int s=60; s>=0; s-=4) {
			target.append(Strings.HEX_DIGITS[((int) (value >> s)) & 15]);
		}
	}

	@Override
	public void appendTo(final StringMaker target, final float value) {
		appendTo(target, Float.floatToRawIntBits(value));
	}

	@Override
	public void appendTo(final StringMaker target, final double value) {
		appendTo(target, Double.doubleToRawLongBits(value));
	}

}

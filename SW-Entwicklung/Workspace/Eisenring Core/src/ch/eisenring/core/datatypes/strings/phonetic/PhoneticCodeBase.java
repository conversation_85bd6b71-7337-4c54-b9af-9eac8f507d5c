package ch.eisenring.core.datatypes.strings.phonetic;

import ch.eisenring.core.datatypes.strings.Strings;

abstract class PhoneticCodeBase implements PhoneticCode {

	protected final String name;
	
	protected PhoneticCodeBase(final CharSequence codeName) {
		this.name = Strings.toString(codeName);
	}

	public final String getName() {
		return this.name;
	}

	// --------------------------------------------------------------
	// ---
	// --- Utility methods
	// ---
	// --------------------------------------------------------------
    private final static String ACCENT = "ÀÁÂÃÅàáâãåÇçÈÉÊËèéêëÌÍÎÏìíîïÑñÒÓÔÕòóôõÙÚÛùúûÝýÿ";
    private final static String BASECH = "AAAAAaaaaaCcEEEEeeeeIIIIiiiiNnOOOOooooUUUuuuYyy";
	
	protected static CharSequence convertAccentsToBaseLetter(final CharSequence string) {
		if (string == null || string.length() <= 0)
			return "";
		final StringBuilder b = new StringBuilder(string.length());
		for (int i=0; i<string.length(); ++i) {
			final char a = string.charAt(i);
			final int j = ACCENT.indexOf(a);
			if (j < 0) {
				b.append(a);
			} else {
				b.append(BASECH.charAt(j));
			}
		}
		return b;
	}

	// --------------------------------------------------------------
	// ---
	// --- Object overrides
	// ---
	// --------------------------------------------------------------
	@Override
	public String toString() {
		return name;
	}
}

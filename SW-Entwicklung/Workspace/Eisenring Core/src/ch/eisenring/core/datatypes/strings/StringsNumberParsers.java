package ch.eisenring.core.datatypes.strings;

import ch.eisenring.core.math.IntegerMath;

/**
 * Helper class that contains number related helper code.
 */
class StringsNumberParsers {
	
	protected StringsNumberParsers() {
	}

	// --------------------------------------------------------------
	// ---
	// --- Number parsing
	// ---
	// --------------------------------------------------------------
	private static NumberFormatException createNumberFormatException(final CharSequence charSeq,
			final int beginIndex, final int endIndex) {
		final StringMaker b = StringMaker.obtain(32);
		b.append("For input string: \"");
		if (charSeq == null) {
			b.append("null");
		} else {
			for (int i=beginIndex; i<endIndex; ++i)
				b.append(charSeq.charAt(i));
		}
		b.append('"');
		return new NumberFormatException(b.release());		
	}
	
	private static NumberFormatException createNumberFormatException(final CharSequence reason, 
			final CharSequence charSeq,	final int beginIndex, final int endIndex) {
		final StringMaker b = StringMaker.obtain(64);
		b.append(reason);
		b.append(" for input string: \"");
		if (charSeq == null) {
			b.append("null");
		} else {
			for (int i=beginIndex; i<endIndex; ++i)
				b.append(charSeq.charAt(i));
		}
		b.append('"');
		return new NumberFormatException(b.release());		
	}	
	/**
	 * Parse int from part of CharSequence/SubSequence
	 */
    public static int parseInt(final CharSequence charSeq, final int radix,
    		final int beginIndex, final int endIndex) throws NumberFormatException {
    	if (charSeq == null)
    		throw new NumberFormatException("null");
    	if (radix < Character.MIN_RADIX)
    		throw new NumberFormatException(Strings.concat("radix ", radix, " less than Character.MIN_RADIX"));
    	if (radix > Character.MAX_RADIX)
    		throw new NumberFormatException(Strings.concat("radix ", radix, " greater than Character.MAX_RADIX"));

	    int result = 0;
	    boolean negative = false;
	    int i = beginIndex;
	    int len = endIndex - beginIndex;
	    int limit = -Integer.MAX_VALUE;
	    int multmin;
	    int digit;

	    if (len <= 0)
	    	throw createNumberFormatException(charSeq, beginIndex, endIndex);

	    digit = charSeq.charAt(i);
        if (digit < '0') { // Possible leading "+" or "-"
            if (digit == '-') {
                negative = true;
                limit = Integer.MIN_VALUE;
            } else if (digit != '+')
    	    	throw createNumberFormatException(charSeq, beginIndex, endIndex);
            if (len == 1) // Cannot have lone "+" or "-"
    	    	throw createNumberFormatException(charSeq, beginIndex, endIndex);
            i++;
        }
        multmin = limit / radix;
        while (i < endIndex) {
            // Accumulating negatively avoids surprises near MAX_VALUE
            digit = Character.digit(charSeq.charAt(i++),radix);
            if (digit < 0)
    	    	throw createNumberFormatException(charSeq, beginIndex, endIndex);
            if (result < multmin)
    	    	throw createNumberFormatException(charSeq, beginIndex, endIndex);
            result *= radix;
            if (result < limit + digit)
    	    	throw createNumberFormatException(charSeq, beginIndex, endIndex);
            result -= digit;
        }
        return negative ? result : -result;
    }

    public static long parseLong(final CharSequence charSeq, final int radix,
    		final int beginIndex, final int endIndex) throws NumberFormatException {
    	if (charSeq == null)
    		throw new NumberFormatException("null");
    	if (radix < Character.MIN_RADIX)
    		throw new NumberFormatException(Strings.concat("radix ", radix, " less than Character.MIN_RADIX"));
    	if (radix > Character.MAX_RADIX)
    		throw new NumberFormatException(Strings.concat("radix ", radix, " greater than Character.MAX_RADIX"));

    	long result = 0;
    	boolean negative = false;
    	int i = beginIndex;
    	final int len = endIndex - beginIndex;
    	long limit = -Long.MAX_VALUE;
    	long multmin;
    	int digit;

	    if (len <= 0)
	    	throw createNumberFormatException(charSeq, beginIndex, endIndex);

	    digit = charSeq.charAt(i);
        if (digit < '0') { // Possible leading "+" or "-"
            if (digit == '-') {
                negative = true;
                limit = Integer.MIN_VALUE;
            } else if (digit != '+')
    	    	throw createNumberFormatException(charSeq, beginIndex, endIndex);
            if (len == 1) // Cannot have lone "+" or "-"
    	    	throw createNumberFormatException(charSeq, beginIndex, endIndex);
            i++;
        }
    	
        multmin = limit / radix;
        while (i < endIndex) {
            // Accumulating negatively avoids surprises near MAX_VALUE
            digit = Character.digit(charSeq.charAt(i++), radix);
            if (digit < 0)
    	    	throw createNumberFormatException(charSeq, beginIndex, endIndex);
            if (result < multmin)
    	    	throw createNumberFormatException(charSeq, beginIndex, endIndex);
            result *= radix;
            if (result < limit + digit)
    	    	throw createNumberFormatException(charSeq, beginIndex, endIndex);
            result -= digit;
        }
        return negative ? result : -result;
    }

    // --------------------------------------------------------------
    // ---
    // --- User input parser
    // ---
    // --------------------------------------------------------------
    static class State {
    	public State feed(final Accumulator a, final char c) throws NumberFormatException {
    		throw new NumberFormatException("invalid character " + c);
    	}
    }

    final static State NOMORE = new State();
    
    final static State INITIAL = new State() {
    	@Override
    	public State feed(final Accumulator a, final char c) throws NumberFormatException {
    		// parse sign / detect format
    		if (c == '+') {
				return DETECT;
			} else if (c == '-') {
				a.sign = -1;
				return DETECT;
			} else if (c == '$') {
				return HEX;
			} else if (c == '0') {
				++a.digitCount;
				return CHEX_DETECT;
			} else if (c == '%') {
				return BINARY;
			} else if (Character.digit(c, 10) >= 0) {
				return DECIMAL.feed(a, c);
			}
    		throw new NumberFormatException("invalid character: " + c);
		}
    };

    final static State DETECT = new State() {
    	@Override
    	public State feed(final Accumulator a, final char c) throws NumberFormatException {
    		if (c == '$') {
				return HEX;
			} else if (c == '%') {
				return BINARY;
			} else if (c == '0') {
				++a.digitCount;
				return CHEX_DETECT;
			} else if (Character.digit(c, 10) >= 0) {
				return DECIMAL.feed(a, c);
			}
    		throw new NumberFormatException("invalid character: " + c);
		}
    };
    
    final static State CHEX_DETECT = new State() {
    	@Override
    	public State feed(final Accumulator a, final char c) throws NumberFormatException {
    		if (c == 'x' || c == 'X') {
    			a.digitCount = 0;
    			return HEX;
			} else {
				return DECIMAL.feed(a, c);
			}
		}
    };

    final static State BINARY = new State() {
    	@Override
    	public State feed(final Accumulator a, final char c) throws NumberFormatException {
    		final int v = Character.digit(c, 2);
    		if (v >= 0) {
    			a.accumulate(v, 2);
    			return this;
    		}
    		final int s = getSizeSuffixValue(c);
    		if (s > 0) {
    			a.shift = s;
    			return NOMORE;
    		}
    		throw new NumberFormatException("invalid character: " + c);
		}
    };
    
    final static State DECIMAL = new State() {
    	@Override
    	public State feed(final Accumulator a, final char c) throws NumberFormatException {
    		final int v = Character.digit(c, 10);
    		if (v >= 0) {
    			a.accumulate(v, 10);
    			return this;
    		}
    		final int s = getSizeSuffixValue(c);
    		if (s > 0) {
    			a.shift = s;
    			return NOMORE;
    		}
    		throw new NumberFormatException("invalid character: " + c);
		}
    };

    final static State HEX = new State() {
    	@Override
    	public State feed(final Accumulator a, final char c) throws NumberFormatException {
    		final int v = Character.digit(c, 16);
    		if (v >= 0) {
    			a.accumulate(v, 16);
    			return this;
    		}
    		final int s = getSizeSuffixValue(c);
    		if (s > 0) {
    			a.shift = s;
    			return NOMORE;
    		}
    		throw new NumberFormatException("invalid character: " + c);
		}
    };
    
    final static class Accumulator {
    	State state = INITIAL;
    	long accumulator;
    	int shift;
    	int digitCount;
    	int sign = 1;
    	
    	public void feed(final char c) throws ArithmeticException, NumberFormatException {
    		state = state.feed(this, c);
    	}

    	public void accumulate(final int value, final int radix) throws ArithmeticException {
    		++digitCount;
    		accumulator = IntegerMath.mul(accumulator, radix);
    		accumulator = IntegerMath.add(accumulator, value);
    	}

    	public long get() throws ArithmeticException {
    		long result = accumulator;
    		if (shift > 0) {
    			result = IntegerMath.asl(result, shift);
    		}
    		if (sign < 0){
    			result = IntegerMath.neg(result);
    		}
    		return result;
    	}
    }

    /**
     * Parses long in free format, accepts:
     * 
     * 12345		(decimal)
     * +123456
     * -1234567
     * $0123ABCDEF	(hex)
     * 0x01234567	(hex)
     * %1000110101	(binary)
     * 
     * tail modifiers:
     * k (kilo 2^10), m (mega 2^20), g (giga 2^30), t (tera 2^40), p (peta 2^50), e (exa 2^60)
     */
    public static long parseLongUserInput(final CharSequence charSeq,
    		final int beginIndex, final int endIndex) throws NumberFormatException {
    	if (charSeq == null)
    		throw new NumberFormatException("null");
    	final Accumulator accumulator = new Accumulator();
    	try {
	    	for (int i=beginIndex; i<endIndex; ++i) {
	    		final char c = charSeq.charAt(i);
	    		accumulator.feed(c);
	    	}
    	} catch (final NumberFormatException e) {
    		throw e;
    	} catch (final RuntimeException e) {
    		throw createNumberFormatException(e.getMessage(), charSeq, beginIndex, endIndex);
    	}
    	// check that we really parsed anything
    	if (accumulator.digitCount <= 0)
    		throw createNumberFormatException(charSeq, beginIndex, endIndex);
		final long result;
    	try {
    		result = accumulator.get();
		} catch (final ArithmeticException e) {
			throw createNumberFormatException(e.getMessage(), charSeq, beginIndex, endIndex);
		}
    	return result;
    }

    public final static int getSizeSuffixValue(final char c) {
    	switch (c) {
    		default: return -1;
    		case 'k': case 'K': return 10;
    		case 'm': case 'M': return 20;
    		case 'g': case 'G': return 30;
    		case 't': case 'T': return 40;
    		case 'p': case 'P': return 50;
    		case 'e': case 'E': return 60;
    	}
    }

    public static int parseIntUserInput(final CharSequence charSeq,
    		final int beginIndex, final int endIndex) throws NumberFormatException {
    	final long value = parseLongUserInput(charSeq, beginIndex, endIndex);
    	if (value < Integer.MIN_VALUE || value > Integer.MAX_VALUE)
    		throw createNumberFormatException(charSeq, beginIndex, endIndex);
    	return (int) value;
    }
   
}

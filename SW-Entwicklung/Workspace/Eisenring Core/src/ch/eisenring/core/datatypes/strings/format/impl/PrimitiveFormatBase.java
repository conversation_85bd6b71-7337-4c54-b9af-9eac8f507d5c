package ch.eisenring.core.datatypes.strings.format.impl;

import ch.eisenring.core.math.IntegerMath;

/**
 * Utility class providing helper methods for formatting primitives
 */
class PrimitiveFormatBase {

	final static int[] INT_POW10 = IntegerMath.POW10I;
	
	final static long[] LONG_POW10 = IntegerMath.POW10L;

	final static char[] CHAR_ARRAY_INTMINVALUE = { '2', '1', '4', '7', '4', '8', '3', '6', '4', '8' };
	
	final static char[] CHAR_ARRAY_LONGMINVALUE = { '9', '2', '2', '3', '3', '7', '2', '0', '3', '6', '8', '5', '4', '7', '7', '5', '8', '0', '8' };
	
	/**
	 * Determines the number of digits in value when converted to decimal
	 */
	public static int numDigits(final long value) {
		if (value == Long.MIN_VALUE)
			return 19;
		final long v = Math.abs(value);
        long p = 10;
        int i = 1;
        while (v >= p && i<19) {
        	++i;
        	p *= 10;
        }
        return i;
	}

	/**
	 * Determines the number of digits in value when converted to decimal
	 */
	public static int numDigits(final int value) {
		if (value == Integer.MIN_VALUE)
			return 10;
		final int v = Math.abs(value);
        int p = 10;
        int i = 1;
        while (v >= p && i<10) {
        	++i;
        	p *= 10;
        }
        return i;
	}	

	/**
	 * Builds a bit mask containing a one at every n'th bit number.
	 */
	static int prepareGroupMask(final int groupDigits) {
		int groupMask = 0;
		for (int i=1; i<32; ++i) {
			if ((i % groupDigits) == 0)
				groupMask |= (1 << i);
		}
		return groupMask;
	}

	
	// --------------------------------------------------------------
	// ---
	// --- Decimal conversion
	// ---
	// --------------------------------------------------------------
	/**
	 * Gets the n'th digit of decimal number.
	 * 
	 * The lowest significant digit is index 0, the most significant digit is index 18.
	 */
	public static char getDigit(final int index, final long value) {
		if (index > 18)
			return '0';
		if (value == Long.MIN_VALUE)
			return CHAR_ARRAY_LONGMINVALUE[18 - index];
		final int digit = (int) ((Math.abs(value) / LONG_POW10[index]) % 10);
		return (char) (digit + 48);
	}

	/**
	 * Gets the n'th digit of decimal number.
	 * 
	 * The lowest significant digit is index 0, the most significant digit is index 18.
	 */
	public static char getDigit(final int index, final int value) {
		if (index > 9)
			return '0';
		if (value == Integer.MIN_VALUE)
			return CHAR_ARRAY_INTMINVALUE[9 - index];
		final int digit = (Math.abs(value) / INT_POW10[index]) % 10;
		return (char) (digit + 48);
	}

}

package ch.eisenring.core.resource.image;

import java.awt.Color;
import java.awt.Component;
import java.awt.Dimension;
import java.awt.Graphics;
import java.awt.Graphics2D;

import javax.swing.Icon;

import ch.eisenring.core.resource.Drawable;

/**
 * Wraps a Drawable for use as an Icon
 */
public final class DrawableIcon implements Icon {

	protected final Drawable drawable;
	protected final Color color;
	protected final int size;
	protected final int width;
	protected final int height;
	
	public DrawableIcon(final Drawable drawable, final int size) {
		this(drawable, null, size);
	}

	public DrawableIcon(final Drawable drawable, final Color color, final int size) {
		this.drawable = drawable;
		this.color = color;
		this.size = size;
		final Dimension d = drawable.getSize(null, size);
		this.width = d.width;
		this.height = d.height;
	}

	@Override
	public final int getIconWidth() {
		return width;
	}

	@Override
	public final int getIconHeight() {
		return height;
	}

	@Override
	public final void paintIcon(final Component c, final Graphics g, final int x, final int y) {
		final Color color = this.color;
		if (color != null)
			g.setColor(color);
		drawable.draw((Graphics2D) g, x, y, size);
	}

}

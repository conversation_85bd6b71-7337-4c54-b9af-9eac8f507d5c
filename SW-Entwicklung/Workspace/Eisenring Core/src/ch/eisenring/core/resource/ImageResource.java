package ch.eisenring.core.resource;

import java.awt.Graphics2D;
import java.awt.Image;

import javax.swing.Icon;

import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.resource.image.DrawableIcon;

/**
 * ImageResource provides access to an image in different sizes.
 * 
 * Depending on a particular implementation of an ImageSource,
 * images can come from practically anywhere and be created by any
 * means (e.g. they can be bitmaps, vector graphics or procedurally
 * generated).
 * 
 * Note: When actually rendering an ImageResource there are three
 * possible ways to achieve it:
 * 1.) call draw() on the ImageResource. Thats the best method to use,
 *     as it allows the resource implementation to do its thing efficiently.
 * 2.) obtain an Icon from the resource using getIcon() and call paintIcon()
 *     on the icon. That is fairly efficient, as the resource can give you
 *     an icon that implements paintIcon() however the resource can do best.
 * 3.) obtain an Image from the resource using getImage() and then render
 *     that image in any way you like anywhere. This should be avoided unless
 *     absolutely necessary. Calling getImage() forces the resource to create
 *     an image, even if the resource is not based on images. Thats a waste
 *     of memory and quite likely a speed penalty as well.
 *     
 * Do note that with the help of DisabledComposite ImageResource.draw() and
 * Icon.paintIcon() can be rendered in disabled state without the need to create
 * an extra image to make a disabled version of it. 
 */
public interface ImageResource extends Drawable {

	public final static int[] FOUR_STANDARD_SIZES = { 16, 32, 64, 128 };

	/**
	 * Gets an icon from this resource (using default size)
	 */
	public default Icon getIcon() {
		return getIcon(16);
	}
	
	/**
	 * Gets an icon from this resource (using specified size)
	 */
	public default Icon getIcon(final int size) {
		return new DrawableIcon(this, size);
	}

	/**
	 * Gets an image of the specified size (width, height is scaled accordingly).
	 * 
	 * Note that this method should not be called if avoidable, instead use
	 * the draw()-method to actually render the resource.
	 */
	public abstract Image getImage(final int size);

	/**
	 * Gets a list of images in various precomputed sizes for this resource.
	 * The actual sizes depend on the resource (there may be only one, but
	 * some resources provide many).
	 * 
	 * This call is only intended to be used for setting a Window's icon.
	 */
	public default List<Image> getImageList() {
		final int[] sizes = FOUR_STANDARD_SIZES;
		final ArrayList<Image> result = new ArrayList<>(sizes.length);
		for (final int size : sizes) {
			result.add(getImage(size));
		}
		return result;
	}

	/**
	 * Draws the image to the specified position in the specified size.
	 *
	 * This call is the preferred way to render the resource, as opposed to
	 * calling getImage() and then drawImage() yourself.
	 */
	public void draw(final Graphics2D g, final int x, final int y, final int size);

}

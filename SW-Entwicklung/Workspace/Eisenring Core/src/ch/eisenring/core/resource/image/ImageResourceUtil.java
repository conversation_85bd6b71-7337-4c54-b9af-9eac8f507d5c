package ch.eisenring.core.resource.image;

import java.awt.Color;
import java.awt.Graphics2D;
import java.awt.Image;
import java.awt.RenderingHints;
import java.awt.image.BufferedImage;

abstract class ImageResourceUtil {

	protected ImageResourceUtil() {
	}

	/**
	 * Gets the image in the requested size, selecting
	 * an exactly matching image if possible.
	 * If not, choose the next larger (or largest if the requested
	 * size exceeds the largest image) and scale the image
	 * to match the requested size.
	 */
	public static Image getImage(final int size, final ImageSource[] images) {
		ImageSource closest = null;
		int delta = Integer.MAX_VALUE;
		int count = images.length;
		for (int i=0; i<count; ++i) {
			final ImageSource imageSource = images[i];
			int w = imageSource.getWidth();
			if (w <= 0)
				continue;
			if (size == 0) {
				// use default size
				return imageSource.getImage();
			}
			int d = w - size;
			if (d == 0) {
				// exact match, stop searching
				return imageSource.getImage();
			} else if (d < delta) {
				closest = imageSource;
				delta = d;
			} else if (closest == null || w > closest.getWidth()) {
				closest = imageSource; 
			}
		}
		final BufferedImage scaled;
		if (closest == null) {
			scaled = getMissingImage(size, size);
		} else {
			int w0 = closest.getWidth();
			int h0 = closest.getHeight();
			int w1, h1;
			if (w0 >= h0) {
				w1 = size;
				h1 = Math.max(1, Math.round(h0 * (((float) size) / w0)));
			} else {
				w1 = Math.max(1, Math.round(w0 * (((float) size) / h0)));
				h1 = size;
			}
			w1 = Math.min(w1, size);
			h1 = Math.min(h1, size);
			scaled = new BufferedImage(w1, h1, BufferedImage.TYPE_INT_ARGB);
			final Graphics2D g = scaled.createGraphics();
			g.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
			g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
			g.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BICUBIC);
			g.setRenderingHint(RenderingHints.KEY_COLOR_RENDERING, RenderingHints.VALUE_COLOR_RENDER_QUALITY);
			g.setRenderingHint(RenderingHints.KEY_ALPHA_INTERPOLATION, RenderingHints.VALUE_ALPHA_INTERPOLATION_QUALITY);
			g.drawImage(closest.getImage(), 0, 0, w1, h1, 0, 0, w0, h0, null);
			g.dispose();
		}
		return scaled;
	}

	/**
	 * Gets a "missing image" image of the specified size
	 */
	public static BufferedImage getMissingImage(final int width, final int height) { 
		final BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
		final Graphics2D g2d = image.createGraphics();
		g2d.setPaintMode();
		g2d.setColor(Color.RED);
		g2d.fillRect(0, 0, width, height);
		g2d.setColor(Color.BLACK);
		g2d.drawString("Missing Image", 1, 9);
		g2d.dispose();
		return image;
	}

}

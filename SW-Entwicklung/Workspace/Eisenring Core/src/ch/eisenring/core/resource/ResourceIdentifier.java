package ch.eisenring.core.resource;

import java.io.InputStream;

import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.StringMakerFriendly;
import ch.eisenring.core.datatypes.strings.Strings;

/**
 * Identifies a resource
 */
public final class ResourceIdentifier implements StringMakerFriendly {

	public final String resourceName;

	public final Class<?> locationClass;

	public ResourceIdentifier(final CharSequence resourceName, final Class<?> locationClass) {
		this.resourceName = Strings.toString(resourceName);
		this.locationClass = locationClass;
	}

	// --------------------------------------------------------------
	// ---
	// --- Resource API
	// ---
	// --------------------------------------------------------------
	public InputStream getInputStream() {
		return locationClass.getResourceAsStream(resourceName);
	}

	// --------------------------------------------------------------
	// ---
	// --- Object overrides
	// ---
	// --------------------------------------------------------------
	@Override
	public int hashCode() {
		int hash = Strings.hashCode(resourceName);
		if (locationClass != null)
			hash = (hash * 71) ^ locationClass.hashCode(); 
		return hash;
	}

	@Override
	public boolean equals(final Object o) {
		if (o instanceof ResourceIdentifier) {
			final ResourceIdentifier r = (ResourceIdentifier) o;
			return Strings.equals(r.resourceName, resourceName) &&
				   (locationClass == null ? (r.locationClass == null) : (locationClass.equals(r.locationClass)));
		}
		return false;
	}

	@Override
	public void toStringMaker(final StringMaker target) {
		target.append("ResourceIdentifier(");
		target.append(resourceName);
		target.append(", ");
		target.append(locationClass);
		target.append(')');
	}

	@Override
	public String toString() {
		return StringMakerFriendly.toString(this);
	}

}

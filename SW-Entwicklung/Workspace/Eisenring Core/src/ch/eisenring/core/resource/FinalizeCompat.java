package ch.eisenring.core.resource;

/**
 * Compatibility utility for handling finalize() method deprecation in Java 21.
 * 
 * This class provides utilities to help migrate from finalize() to ResourceFinalizer
 * for proper resource cleanup in Java 21 and later versions.
 * 
 * Usage example:
 * 
 * Instead of:
 * <pre>
 * public class MyClass {
 *     @Override
 *     protected void finalize() throws Throwable {
 *         cleanup();
 *     }
 * }
 * </pre>
 * 
 * Use:
 * <pre>
 * public class MyClass {
 *     private final ResourceFinalizer resourceFinalizer;
 *     
 *     public MyClass() {
 *         this.resourceFinalizer = FinalizeCompat.createFinalizer(this, this::cleanup);
 *     }
 *     
 *     private void cleanup() {
 *         // cleanup code here
 *     }
 * }
 * </pre>
 */
public final class FinalizeCompat {
    
    /**
     * Creates a ResourceFinalizer that will call the cleanup action when the
     * trigger object is garbage collected.
     * 
     * @param trigger The object whose garbage collection will trigger cleanup
     * @param cleanupAction The action to perform during cleanup
     * @return A ResourceFinalizer instance
     */
    public static ResourceFinalizer createFinalizer(Object trigger, Runnable cleanupAction) {
        return new ResourceFinalizer(trigger) {
            @Override
            protected void cleanup() {
                try {
                    cleanupAction.run();
                } catch (Exception e) {
                    // Ignore cleanup failures during finalization
                    // This matches the behavior of finalize() methods
                }
            }
        };
    }
    
    /**
     * Creates a ResourceFinalizer for objects that implement a destroy() method.
     * 
     * @param trigger The object whose garbage collection will trigger cleanup
     * @param destroyable The object with a destroy() method to call
     * @return A ResourceFinalizer instance
     */
    public static ResourceFinalizer createDestroyableFinalizer(Object trigger, 
                                                               DestroyableResource destroyable) {
        return new ResourceFinalizer(trigger) {
            @Override
            protected void cleanup() {
                try {
                    destroyable.destroy();
                } catch (Exception e) {
                    // Ignore cleanup failures during finalization
                }
            }
        };
    }
    
    /**
     * Interface for objects that can be destroyed/cleaned up.
     */
    public interface DestroyableResource {
        void destroy() throws Exception;
    }
    
    /**
     * Checks if finalize() methods are deprecated in the current Java version.
     * 
     * @return true if finalize() is deprecated, false otherwise
     */
    public static boolean isFinalizeDeprecated() {
        // finalize() was deprecated in Java 9
        String version = System.getProperty("java.version");
        if (version.startsWith("1.8")) {
            return false;
        }
        return true; // Java 9+ has deprecated finalize()
    }
    
    /**
     * Gets information about finalize() deprecation status.
     * 
     * @return A descriptive message about finalize() status
     */
    public static String getFinalizeStatusMessage() {
        if (isFinalizeDeprecated()) {
            return "finalize() is deprecated in this Java version - use ResourceFinalizer instead";
        } else {
            return "finalize() is available in this Java version but ResourceFinalizer is recommended";
        }
    }
    
    // Private constructor to prevent instantiation
    private FinalizeCompat() {
    }
}

package ch.eisenring.core.clipboard;

import java.awt.datatransfer.DataFlavor;
import java.awt.datatransfer.UnsupportedFlavorException;
import java.io.IOException;

public class GenericSelection<T> extends AbstractSelection {

	protected final T selected;
	
	public GenericSelection(final T selected, final DataFlavor ... supportedFlavors) {
		super(supportedFlavors);
		this.selected = selected;
	}

	@Override
    public Object getTransferData(final DataFlavor flavor) throws UnsupportedFlavorException, IOException {
        if (!isDataFlavorSupported(flavor))
            throw new UnsupportedFlavorException(flavor);
        return selected;
    }
	
}

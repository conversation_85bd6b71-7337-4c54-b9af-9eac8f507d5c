package ch.eisenring.core.fileformat.matcher;

import java.io.IOException;

import ch.eisenring.core.io.binary.BinaryHolder;

public final class MagicNumberMatcher extends AbstractMatcher {

	private final int minSize;
	private final int mask;
	private final int magic;

	public MagicNumberMatcher(final int magic, final int mask, final int minSize) {
		this.magic = magic & mask;
		this.mask = mask;
		this.minSize = minSize;
	}

	@Override
	public boolean matches(final BinaryHolder binary) throws IOException {
		if (binary.size() < minSize)
			return false;
		final int magic = getMagic4(binary, 0) & mask;
		return this.magic == (magic & mask);
	}

}

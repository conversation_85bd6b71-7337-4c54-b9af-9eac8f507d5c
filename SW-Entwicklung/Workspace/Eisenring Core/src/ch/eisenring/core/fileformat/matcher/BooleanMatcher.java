package ch.eisenring.core.fileformat.matcher;

import java.io.IOException;

import ch.eisenring.core.io.binary.BinaryHolder;

/**
 * Matcher that accepts everything
 */
public final class BooleanMatcher extends AbstractMatcher {

	public final static BooleanMatcher ACCEPT = new BooleanMatcher(true);
	public final static BooleanMatcher REJECT = new BooleanMatcher(false);

	private final boolean matches;
	
	private BooleanMatcher(final boolean matches) {
		this.matches = matches;
	}

	@Override
	public boolean matches(final BinaryHolder binary) throws IOException {
		// we don't care one bit about the contents of the binary
		return matches;
	}

}

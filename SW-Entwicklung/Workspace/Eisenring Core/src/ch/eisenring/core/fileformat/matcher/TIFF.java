package ch.eisenring.core.fileformat.matcher;

import java.io.IOException;

import ch.eisenring.core.io.binary.BinaryHolder;

/**
 * Matcher detecting TIF format
 */
public final class TIFF extends AbstractMatcher {

	@Override
	public boolean matches(final BinaryHolder binary) throws IOException {
		if (binary.size() <= 8)
			return false;
		final int magic = getMagic4(binary, 0);
		switch (magic) {
			case 0x4D4D002A: // motorola byte order TIF marker 
			case 0x49492A00: // intel byte order TIF marker
				return true;
			default:
				return false;
		}
	}

}

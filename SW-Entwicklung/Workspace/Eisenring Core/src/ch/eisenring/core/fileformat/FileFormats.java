package ch.eisenring.core.fileformat;

import ch.eisenring.core.fileformat.matcher.EXE;
import ch.eisenring.core.fileformat.matcher.MagicNumberMatcher;
import ch.eisenring.core.fileformat.matcher.TIFF;
import static ch.eisenring.core.fileformat.FileTypeCategory.*;

public interface FileFormats {

	FileFormat _7Z = new GenericFileFormat(UNKNOWN, new MagicNumberMatcher(0x377A0000, 0xFFFFFFFF, 9), "7z");
	FileFormat BMP = new GenericFileFormat(IMAGE, new MagicNumberMatcher(0x424D0000, 0xFFFFFFFF, 9), "bmp", "dib");
	FileFormat CLASS = new GenericFileFormat(UNKNOWN, new MagicNumberMatcher(0xCAFEBABE, 0xFFFFFFFF, 9), "class");
	FileFormat EXE = new GenericFileFormat(UNKNOWN, new EXE(), "exe");
	FileFormat COM = new GenericFileFormat(UNKNOWN, new EXE(), "com");
	FileFormat DLL = new GenericFileFormat(UNKNOWN, new EXE(), "dll");
	FileFormat FLAC = new GenericFileFormat(AUDIO, new MagicNumberMatcher(0x664C6143, 0xFFFFFFFF, 6), "flac");
	FileFormat GIF = new GenericFileFormat(IMAGE, new MagicNumberMatcher(0x47494638, 0xFFFFFFFF, 9), "gif");
	FileFormat IFF = new GenericFileFormat(IMAGE, new MagicNumberMatcher(0x464F524D, 0xFFFFFFFF, 13), "iff", "ilbm", "lbm");
	FileFormat JPG = new GenericFileFormat(IMAGE, new MagicNumberMatcher(0xFFD8FF00, 0xFFFFFF00, 7), "jpg", "jpeg", "jpe", "jfif");
	FileFormat PCX = new GenericFileFormat(IMAGE, new MagicNumberMatcher(0x0A000101, 0xFFF0FFFF, 6), "pcx");
	FileFormat PDF = new GenericFileFormat(DOCUMENT, new MagicNumberMatcher(0x25504446, 0xFFFFFFFF, 6), "pdf");
	FileFormat PNG = new GenericFileFormat(IMAGE, new MagicNumberMatcher(0x89504E47, 0xFFFFFFFF, 13), "png");
	FileFormat PSD = new GenericFileFormat(IMAGE, new MagicNumberMatcher(0x38425053, 0xFFFFFFFF, 8), "psd");
	FileFormat PST = new GenericFileFormat(DOCUMENT, new MagicNumberMatcher(0x2142444E, 0xFFFFFFFF, 13), "pst");
	FileFormat RTF = new GenericFileFormat(DOCUMENT, new MagicNumberMatcher(0x7B5C7274, 0xFFFFFFFF, 6), "rtf");
	FileFormat SWF = new GenericFileFormat(DOCUMENT, new MagicNumberMatcher(0x46575300, 0xFFFFFF00, 6), "swf");
	FileFormat TIFF = new GenericFileFormat(IMAGE, new TIFF(), "tif", "tiff");
	FileFormat ZIP = new GenericFileFormat(UNKNOWN, new MagicNumberMatcher(0x504B0304, 0xFFFFFFFF, 5), "zip");
	FileFormat JAR = new GenericFileFormat(UNKNOWN, new MagicNumberMatcher(0x504B0304, 0xFFFFFFFF, 5), "jar");

}

package ch.eisenring.core.collections.impl;

import java.util.Iterator;
import ch.eisenring.core.collections.Lookup;
import ch.eisenring.core.collections.SizingPolicy;

@SuppressWarnings("serial")
public class HashMap<K, V> extends java.util.HashMap<K, V> implements Lookup<K, V>, ch.eisenring.core.collections.Map<K, V> {

	public HashMap() {
	}

	public HashMap(final int initialCapacity) {
		super(initialCapacity);
	}

	public HashMap(final int initialCapacity, final float loadFactor) {
		super(initialCapacity, loadFactor);
	}

	public HashMap(final java.util.Map<? extends K, ? extends V> map) {
		super(map);
	}

	@Override
	public void trimToSize() {
		// not possible by design
	}

	@Override
	public void ensureCapacity(final int capacity) {
		// not possible by design
	}

	@Override
	public void setPolicy(final SizingPolicy policy) {
		// NO-OP
	}

	@Override
	public Iterator<K> keyIterator() {
		return keySet().iterator();
	}

	@Override
	public Iterator<V> valueIterator() {
		return values().iterator();
	}

	public HashMap<K, V> add(K key, V value) {
		put(key, value);
		return this;
	}

}

package ch.eisenring.core.collections;


/**
 * SizingPolicy can be used with a HEAG collection to control the collections general 
 * growth and shrink behaviors (very coarsely).
 */
public final class SizingPolicy {

	/**
	 * Integer constant defining the PERFORMANCE mode.
	 * This mode features aggressive growth and no (or very reluctant) shrinking.
	 * Trades memory for performance
	 * 
	 * Only ever used by implementors of compatible collections.
	 */
	public final static int ID_PERFORMANCE = 0;

	/**
	 * Integer constant defining the REASONABLE mode.
	 * This mode features fast growth and moderate shrinking.
	 * Offers reasonable performance with some consideration for memory usage.
	 * 
	 * Only ever used by implementors of compatible collections.
	 */
	public final static int ID_REASONABLE = 1;
	
	/**
	 * Integer constant defining the CONSERVATIVE mode.
	 * This mode features conservative growth and fast shrinking.
	 * 
	 * Only ever used by implementors of compatible collections.
	 */
	public final static int ID_CONSERVATIVE = 2;
	
	/**
	 * Integer constant defining the MIMEMORY mode.
	 * This mode features slow growth and aggressive shrinking.
	 * 
	 * Only ever used by implementors of compatible collections.
	 */
	public final static int ID_MINMEMORY = 3;


	public final int policyId;
	public final String name;
	
	public final static SizingPolicy PERFORMANCE = new SizingPolicy(ID_PERFORMANCE, "PERFORMANCE");
	public final static SizingPolicy REASONABLE =  new SizingPolicy(ID_REASONABLE, "REASONABLE");
	public final static SizingPolicy CONSERVATIVE =  new SizingPolicy(ID_CONSERVATIVE, "CONSERVATIVE");
	public final static SizingPolicy MINMEMORY =  new SizingPolicy(ID_MINMEMORY, "MINMEMORY");

	public static SizingPolicy DEFAULT = PERFORMANCE;

	
	private SizingPolicy(final int id, final String name) {
		this.policyId = id;
		this.name = name;
	}

	// --------------------------------------------------------------
	// ---
	// --- API
	// ---
	// --------------------------------------------------------------
	public int getPolicyId() {
		return policyId;
	}

	// --------------------------------------------------------------
	// ---
	// --- Object overrides
	// ---
	// --------------------------------------------------------------
	@Override
	public int hashCode() {
		return policyId;
	}

	@Override
	public boolean equals(final Object o) {
		return o instanceof SizingPolicy && ((SizingPolicy) o).policyId == policyId;
	}

	@Override
	public String toString() {
		return name;
	}
	
}
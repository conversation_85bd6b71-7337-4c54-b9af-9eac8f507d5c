package ch.eisenring.core.collections.alterableview;

import java.util.Collection;
import java.util.ConcurrentModificationException;
import java.util.Iterator;

import ch.eisenring.core.collections.Set;
import ch.eisenring.core.collections.SizingPolicy;
import ch.eisenring.core.collections.impl.ArraySet;

final class AlterableSetView<E> implements Set<E> {

	private Set<E> set;
	private boolean isCopied;

	AlterableSetView(final Set<E> set) {
		this.set = set;
	}

	// --------------------------------------------------------------
	// ---
	// --- Internal methods
	// ---
	// --------------------------------------------------------------
	protected final void ensureCopy() {
		if (!isCopied) {
			set = new ArraySet<E>(set);
			isCopied = true;
		}
	}

	// --------------------------------------------------------------
	// ---
	// --- Standard java.util.Set implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public boolean add(final E element) {
		ensureCopy();
		return set.add(element);
	}

	@Override
	public boolean addAll(final Collection<? extends E> collection) {
		ensureCopy();
		return set.addAll(collection);
	}

	@Override
	public void clear() {
		if (isCopied) {
			set.clear();
		} else {
			set = new ArraySet<>();
		}
	}

	@Override
	public boolean contains(final Object object) {
		return set.contains(object);
	}

	@Override
	public boolean containsAll(final Collection<?> collection) {
		return set.containsAll(collection);
	}

	@Override
	public int hashCode() {
		return set.hashCode();
	}

	@Override
	public boolean equals(final Object object) {
		return set.equals(object);	
	}

	@Override
	public boolean isEmpty() {
		return set.isEmpty();
	}

	@Override
	public boolean remove(final Object object) {
		ensureCopy();
		return set.remove(object);
	}

	@Override
	public boolean removeAll(final Collection<?> collection) {
		ensureCopy();
		return set.removeAll(collection);
	}

	@Override
	public boolean retainAll(final Collection<?> collection) {
		ensureCopy();
		return set.retainAll(collection);
	}

	@Override
	public int size() {
		return set.size();
	}

	@Override
	public Object[] toArray() {
		return set.toArray();
	}

	@Override
	public <T> T[] toArray(final T[] array) {
		return set.toArray(array);
	}
	
	// --------------------------------------------------------------
	// ---
	// --- Iteration
	// ---
	// --------------------------------------------------------------
	final class SetItr implements Iterator<E> {
		final Iterator<E> iterator;

        public SetItr(final Iterator<E> iterator) {
        	this.iterator = iterator;
        }        

        public boolean hasNext() {
        	if (isCopied)
            	throw new ConcurrentModificationException();
       		return iterator.hasNext();
        }

        @SuppressWarnings("unchecked")
        public E next() {
        	if (isCopied)
            	throw new ConcurrentModificationException();
       		return iterator.next();
        }

        public void remove() {
            throw new UnsupportedOperationException();
        }
	}

	@Override
	public Iterator<E> iterator() {
		if (isCopied)
			return set.iterator();
		return new SetItr(set.iterator());
	}

	// --------------------------------------------------------------
	// ---
	// --- Extended set interface implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public void ensureCapacity(final int capacity) {
		ensureCopy();
		set.ensureCapacity(capacity);
	}
	
	@Override
	public void setPolicy(final SizingPolicy policy) {
		ensureCopy();
		set.setPolicy(policy);
	}

	@Override
	public void trimToSize() {
		if (isCopied)
			set.trimToSize();
	}

}

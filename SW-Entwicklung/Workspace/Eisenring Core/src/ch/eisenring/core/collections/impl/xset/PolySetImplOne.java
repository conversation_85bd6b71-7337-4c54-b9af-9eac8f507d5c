package ch.eisenring.core.collections.impl.xset;

import java.util.Collection;
import java.util.Iterator;
import java.util.NoSuchElementException;

import ch.eisenring.core.datatypes.strings.StringMaker;

/**
 * Special case implementation, single entry set.
 * 
 * This one is special in that its only ever used when either trimming
 * a size one set, or constructing the set with initial capacity of
 * exactly one.
 */
final class PolySetImplOne extends PolySetImpl {

	protected Object backingStore;

	protected PolySetImplOne() {
		backingStore = PolySet.PRIVATE_OBJECT;
	}

	@Override
	protected int hashCodeImpl() {
		// no need to check size here
		return hashCode(backingStore);
	}

	@Override
	protected void clear(final PolySet<?> set) {
		if (size > 0) {
			++set.modCount;
			backingStore = PolySet.PRIVATE_OBJECT;
			size = 0;
		}
	}

	@Override
	protected boolean contains(final Object element) {
		// no need to check size here
		if (element == null)
			return backingStore == null;
		return element.equals(backingStore);
	}

	@Override
	protected boolean containsAll(final java.util.Collection<?> collection) {
		for (final Object e : collection) {
			if (!contains(e))
				return false;
		}
		return true;
	}

	@Override
	protected boolean retainAll(final java.util.Collection<?> collection, final PolySet<?> set) {
		if (collection.contains(backingStore))
			return false;
		backingStore = PolySet.PRIVATE_OBJECT;
		size = 0;
		return true;
	}

	@Override
	protected boolean removeAll(final Collection<?> collection, final PolySet<?> set) {
		if (!collection.contains(backingStore))
			return false;
		++set.modCount;
		backingStore = PolySet.PRIVATE_OBJECT;
		size = 0;
		return true;
	}

	@Override
	protected boolean add(final PolySet<?> set, final Object element) {
		if (size == 0) {
			++set.modCount;
			backingStore = element;
			size = 1;
			return true;
		} else if (element == null) {
			if (backingStore == null)
				return false;
		} else if (element.equals(backingStore)) {
			return false;
		}
		// add the element
		++set.modCount;
		final PolySetImpl newImpl = PolySet.create(PolySet.DEFAULT_CAPACITY, set.loadFactor);
		addContentTo(newImpl);
		set.implementation = newImpl;
		return true;
	}

	@Override
	protected void addInternal(final Object element) {
		backingStore = element;
		size = 1;
	}

	@Override
	protected void addContentTo(final PolySetImpl target) {
		if (size > 0)
			target.addInternal(backingStore);
	}

	@Override
	protected boolean remove(final PolySet<?> set, final Object element) {
		// no need to check size here
		final boolean result;
		if (element == null) {
			result = backingStore == null;
		} else {
			result = element.equals(backingStore);
		}
		if (result) {
			++set.modCount;
			backingStore = PolySet.PRIVATE_OBJECT;
			size = 0;
		}
		return result;
	}

	@Override
	protected void trimToSize(final PolySet<?> set) {
		// NO-OP
	}

	@Override
	protected void ensureCapacity(final PolySet<?> set, final int freeCapacity) {
		final int neededCapacity = neededCapacity(freeCapacity);
		if (neededCapacity <= 1)
			return;
		final PolySetImpl newImpl = PolySet.create(neededCapacity, set.loadFactor);
		addContentTo(newImpl);
		++set.modCount;
		set.implementation = newImpl;
	}

	@Override
	protected int selectGrowToCapacity(final int currentCapacity) {
		// always switches to default capacity
		return PolySet.DEFAULT_CAPACITY;
	}

	protected Iterator<?> iterator(final PolySet<?> set) {
		return new SetItrOne(set);
	}

	@Override
	protected void toArray(final Object[] array) {
		// there is no check here because the method will not be called for size == 0
		array[0] = backingStore;
	}

	@Override
	protected String toString(final PolySet<?> set) {
		if (size == 0)
			return PolySet.EMPTY_TOSTRING;
		StringMaker b = StringMaker.obtain();
		b.append('[');
		if (set == backingStore) {
			b.append(PolySet.SELF_TOSTRING);
		} else {
			b.append(backingStore);
		}
		b.append(']');
		return b.release();
	}

	/**
	 * Implements iterating over the single element
	 */
	final class SetItrOne extends SetItrBase {
		SetItrOne(final PolySet<?> set) {
			super(set);
		}

		@Override
		public boolean hasNext() {
			return index == 0;
		}

		@Override
		public Object next() {
			checkForComodification();
			if (index == 0) {
				index = -1;
				removableIndex = 0;
				return backingStore;
			}
			throw new NoSuchElementException();
		}

		@Override
		public void remove() {
			prepareRemoval();
			removableIndex = -1;
			size = 0;
			backingStore = PolySet.PRIVATE_OBJECT;
		}
	}

}

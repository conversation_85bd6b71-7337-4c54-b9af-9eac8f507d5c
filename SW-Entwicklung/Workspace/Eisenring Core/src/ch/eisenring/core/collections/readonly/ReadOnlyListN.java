//package ch.eisenring.core.collections.readonly;
//
//import java.util.Iterator;
//import java.util.ListIterator;
//
//import ch.eisenring.core.collections.List;
//import ch.eisenring.core.datatypes.primitives.Primitives;
//import ch.eisenring.core.iterators.ArrayIterator;
//
//public class ReadOnlyListN<T> extends ReadOnlyList<T> { 
//
//	private final Object[] array;
//
//	public ReadOnlyListN(final List<? extends T> source) {
//		array = source.toArray(Primitives.EMPTY_OBJECT_ARRAY);
//	}
//
//	@Override
//	public int size() {
//		return array.length;
//	}
//
//	@Override
//	public ListIterator<T> listIterator() {
//		// TODO Auto-generated method stub
//		return null;
//	}
//
//	@SuppressWarnings("unchecked")
//	@Override
//	public Iterator<T> iterator() {
//		return new ArrayIterator<T>((T[]) array, 0, array.length);
//	}
//
//	@Override
//	public Object[] toArray() {
//		return array.clone();
//	}
//
//}

package ch.eisenring.core.collections.impl;

import java.lang.reflect.Array;
import java.util.AbstractCollection;
import java.util.AbstractSet;
import java.util.Iterator;
import java.util.Map.Entry;

import ch.eisenring.core.collections.Lookup;
import ch.eisenring.core.collections.Map;
import ch.eisenring.core.collections.Set;
import ch.eisenring.core.collections.SizingPolicy;
import ch.eisenring.core.datatypes.primitives.Primitives;
import ch.eisenring.core.datatypes.strings.Strings;

abstract class ArrayLookupBase<K, V> extends ArrayCollectionBase implements Lookup<K, V> {

	/**
	 * The hash assumed for a NULL key
	 */
	final static int NULL_HASH = Integer.MIN_VALUE;
	
	/**
	 * The default size used when no size was specified
	 */
	final static int DEFAULT_SIZE = 12;
	
	/**
	 * The maximum size of the backing array
	 */
	final static int MAX_SIZE = 0x3FFFFFFE;

	// --------------------------------------------------------------
	// ---
	// --- Internal implementation
	// ---
	// --------------------------------------------------------------
	abstract Object[] elements();
	
	abstract int size(final Object[] elements);
	
	/**
	 * Finds the index where key is located.
	 * 
	 * If return value is positive, its the index of the key.
	 * Otherwise its the inverted index where key needs to be inserted.
	 */
	static int indexOfKey(final Object[] elements, final int size, final Object key) {
		Object value;
		int index;
		if (key == null) {
			index = -2;
			while ((index += 2) < size) {
				value = elements[index];
				if (value == null)
					return index;
				if (value.hashCode() > NULL_HASH)
					break;
			}
			return ~0;
		} else {
			final int nHash = key.hashCode();
			int l = 0;
	        int h = size - 2;
	        while (l <= h) {
	            index = ((l + h) >>> 1) & -2;
	            final int vHash = hashOf(elements[index]);
	            if (vHash < nHash)
	                l = index + 2;
	            else if (vHash > nHash)
	                h = index - 2;
	            else {
	            	// check for direct hit
	            	if (key.equals(elements[index]))
	            		return index;
	            	// walk upwards
	            	int indexh = index;
	            	while ((indexh += 2) < size) {
	            		value = elements[indexh];
	            		if (hashOf(value) != nHash)
	            			break;
	            		if (key.equals(value))
	            			return indexh;
	            	}
	            	// walk downwards
	            	while ((index -= 2) >= 0) {
	            		value = elements[index];
	            		if (hashOf(value) != nHash)
	            			break;
	            		if (key.equals(value))
	            			return index;
	            	}
	            	return ~(index + 2); 
	            }
	        }
        	return ~l;
		}
	}
	
	/**
	 * Returns the index of first appearance of value (or negative index
	 * if the value is not present). Note: the index is odd, it points to
	 * the index where value is located. Its not the index of the key!
	 */
	static int indexOfValue(final Object[] elements, final int size, final Object value) {
		int index = size + 1;
		if (value == null) {
			while ((index-=2) >= 0) {
				if (elements[index] == null)
					return index;
			}
			return -1;
		}
		while ((index-=2) >= 0) {
			if (value.equals(elements[index]))
				return index;
		}
		return -1;
	}

	/**
	 * Gets the hashCode of object, handles NULL as special case
	 */
	static int hashOf(final Object object) {
		return object == null ? NULL_HASH : object.hashCode();
	}

	// --------------------------------------------------------------
	// ---
	// --- Array conversion
	// ---
	// --------------------------------------------------------------
	static Object[] toArray(final Object[] elements, final int size, final int start) {
		final int length = size >>> 1;
		if (length == 0)
			return Primitives.EMPTY_OBJECT_ARRAY;
		final Object[] result = new Object[length];
		int j = -1;
		int i = start;
		while ((i += 2) < size) {
			result[++j] = elements[i];
		}
		return result;
	}
	
	@SuppressWarnings("unchecked")
	static <U> U[] toArray(final Object[] elements, final int size, final int start, final U[] array) {
		final int length = size >>> 1;
		final U[] result;
		if (array.length >= length) {
			result = array;
			if (result.length > length)
				result[length] = null;
		} else {
			result = (U[]) Array.newInstance(array.getClass().getComponentType(), size);
		}
		int j = -1;
		int i = start;
		while ((i += 2) < size) {
			result[++j] = (U) elements[i];
		}
		return result;
	}

	// --------------------------------------------------------------
	// ---
	// --- Generic implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public boolean isEmpty() {
		final Object[] elements = elements();
		return size(elements) == 0;
	}

	@Override
	public int size() {
		return size(elements()) >> 1;
	}	

	@Override
	public boolean containsKey(final Object key) {
		final Object[] elements = elements();
		return indexOfKey(elements, size(elements), key) >= 0;
	}

	@Override
	public boolean containsValue(final Object value) {
		final Object[] elements = elements();
		return indexOfValue(elements, size(elements), value) >= 0;
	}

	@SuppressWarnings("unchecked")
	@Override
	public V get(final Object key) {
		final Object[] elements = elements();
		int index = indexOfKey(elements, size(elements), key);
		return index < 0 ? null : (V) elements[index + 1];
	}

	// --------------------------------------------------------------
	// ---
	// --- Object overrides
	// ---
	// --------------------------------------------------------------
	@Override
	public int hashCode() {
		final Object[] elements = elements();
		int hashCode = 0;
		int i = size(elements);
		while (i > 0) {
			final Object value = elements[--i];
			final Object key = elements[--i];
			hashCode += (value == null ? 0 : value.hashCode()) ^ (key == null ? 0 : key.hashCode()); 
		}
		return hashCode;
	}

	@Override
	public boolean equals(final Object otherObject) {
		if (this == otherObject)
			return true;
		final Object[] elements = elements();
		final int size = size(elements);
		if (otherObject instanceof Lookup) {
			final Lookup<?, ?> otherLookup = (Lookup<?, ?>) otherObject;
			if (otherLookup.size() != (size >> 1))
				return false;
			int i = size;
			while (i > 0) {
				final Object value = elements[--i];
				final Object key = elements[--i];
		        if (value == null) {
	                if (!(otherLookup.get(key) == null && otherLookup.containsKey(key)))
	                    return false;
	            } else {
	                if (!value.equals(otherLookup.get(key)))
	                    return false;
	            }
			}
			return true;
		} else if (otherObject instanceof java.util.Map) {
			final java.util.Map<?, ?> otherMap = (java.util.Map<?, ?>) otherObject;
			if (otherMap.size() != (size >> 1))
				return false;
			int i = size;
			while (i > 0) {
				final Object value = elements[--i];
				final Object key = elements[--i];
		        if (value == null) {
	                if (!(otherMap.get(key) == null && otherMap.containsKey(key)))
	                    return false;
	            } else {
	                if (!value.equals(otherMap.get(key)))
	                    return false;
	            }
			}
			return true;
		}
		return false;
	}

	// --------------------------------------------------------------
	// ---
	// --- Part of Map interface
	// ---
	// --------------------------------------------------------------
	public void putAll(final java.util.Map<? extends K, ? extends V> map) {
		if (map.isEmpty())
			return;
		for (final Map.Entry<? extends K, ? extends V> entry : map.entrySet()) {
			put(entry.getKey(), entry.getValue());
		}
	}

	// --------------------------------------------------------------
	// ---
	// --- KeySet implementation
	// ---
	// --------------------------------------------------------------
	static class KeySet<T> extends AbstractSet<T> implements ch.eisenring.core.collections.Set<T> {
		final ArrayLookupBase<T, ?> lookup;
		
		KeySet(final ArrayLookupBase<T, ?> lookup) {
			this.lookup = lookup;
		}

        @Override
		public Iterator<T> iterator() {
            return lookup.keyIterator();
        }
        @Override
        public int size() {
            return lookup.size();
        }
        
        @Override
        public boolean contains(Object o) {
            return lookup.containsKey(o);
        }

        @Override
        public boolean remove(Object o) {
            return lookup.remove(o) != null;
        }

        @Override
        public void clear() {
            lookup.clear();
        }
 
        @Override
        public void trimToSize() {
        	// NO-OP
        }

        @Override
        public void ensureCapacity(final int capacity) {
        	// NO-OP
        }
       
        @Override
        public void setPolicy(final SizingPolicy policy) {
        	// NO-OP
        }
	}

	public java.util.Set<K> keySet() {
		return new KeySet<>(this);
	}

	// --------------------------------------------------------------
	// ---
	// --- Values implementation
	// ---
	// --------------------------------------------------------------
	static class Values<V> extends AbstractCollection<V> implements ch.eisenring.core.collections.Collection<V> {
		final ArrayLookupBase<?, V> lookup;
		
		Values(final ArrayLookupBase<?, V> lookup) {
			this.lookup = lookup;
		}

        @Override
		public Iterator<V> iterator() {
            return lookup.valueIterator();
        }

        @Override
        public int size() {
            return lookup.size();
        }

        @Override
        public boolean contains(final Object o) {
            return lookup.containsValue(o);
        }

        @Override
        public boolean remove(final Object o) {
        	final Iterator<V> i = lookup.valueIterator();
        	while (i.hasNext()) {
        		final Object v = i.next();
        		if (Primitives.equals(v, o)) {
        			i.remove();
        			return true;
        		}
        	}
        	return false;
        }

        @Override
        public void clear() {
            lookup.clear();
        }
 
        @Override
        public void trimToSize() {
        	// NO-OP
        }

        @Override
        public void ensureCapacity(final int capacity) {
        	// NO-OP
        }

        @Override
        public void setPolicy(final SizingPolicy policy) {
        	// NO-OP
        }
	}

	public java.util.Collection<V> values() {
		return new Values<>(this);
	}

	// --------------------------------------------------------------
	// ---
	// --- EntrySet implementation
	// ---
	// --------------------------------------------------------------
	static class ALBEntry<K, V> implements Entry<K, V> {
		final ArrayLookupBase<K, V> lookup;
		final K key;

        ALBEntry(final ArrayLookupBase<K, V> lookup, final K key) {
        	this.lookup = lookup;
        	this.key = key;
        }

        @Override
        public final K getKey() {
            return key;
        }

        @Override
        public final V getValue() {
            return lookup.get(key);
        }

        @Override
        public final V setValue(final V newValue) {
        	return lookup.put(key, newValue);
        }

        @Override
        public final boolean equals(Object o) {
            if (!(o instanceof Entry))
                return false;
            final Entry<?, ?> e = (Entry<?, ?>)o;
            if (key == null) {
                if (e.getKey() != null)
                	return false;
            } else {
            	if (!key.equals(e.getKey()))
            		return false;
            }
            final Object value = getValue();
            if (value == null) {
            	return e.getValue() == null;
            } else {
            	return value.equals(e.getValue());
            }
        }

        public final int hashCode() {
        	final Object value = getValue();
            return (key == null   ? 0 : key.hashCode()) ^
                   (value == null ? 0 : value.hashCode());
        }

        public final String toString() {
        	return Strings.concat(key, "=", getValue());
        }
	}

    final static class EntrySet<K, V> extends AbstractSet<Entry<K, V>> implements Set<Entry<K, V>> {
    	final ArrayLookupBase<K, V> lookup;
    	
    	EntrySet(final ArrayLookupBase<K, V> lookup) {
			this.lookup = lookup;
		}

    	public Iterator<Entry<K,V>> iterator() {
    		return new EntryIterator<>(lookup);
        }

    	@SuppressWarnings("unchecked")
    	@Override
        public boolean contains(final Object o) {
            if (!(o instanceof Entry))
                return false;
            final Entry<K,V> e = (Entry<K,V>) o;
            final Object eKey = e.getKey();
            final Object lValue = lookup.get(eKey);
            if (lValue == null) {
            	if(!lookup.containsKey(eKey))
            		return false;
            	return e.getValue() == null;
            } else {
            	return lValue.equals(e.getValue());
            }
        }

        @Override
    	@SuppressWarnings("unchecked")
        public boolean remove(final Object o) {
            if (!(o instanceof Entry))
                return false;
            final Entry<K,V> e = (Entry<K,V>) o;
            final Object key = e.getKey();
            if (!lookup.containsKey(key))
            	return false;
            // incorrect if key mapped to NULL
            return lookup.remove(key) != null;
        }

        @Override
        public int size() {
            return lookup.size();
        }

        public void clear() {
            lookup.clear();
        }

        @Override
        public void trimToSize() {
        	// NO-OP
        }

        @Override
        public void ensureCapacity(final int capacity) {
        	// NO-OP
        }

        @Override
        public void setPolicy(final SizingPolicy policy) {
        	// NO-OP
        }
    }

	static class EntryIterator<K, V> implements Iterator<Entry<K, V>> {
		final ArrayLookupBase<K, V> lookup;
		final Iterator<K> i;

		EntryIterator(final ArrayLookupBase<K, V> lookup) {
			this.lookup = lookup;
			this.i = lookup.keyIterator();
		}
		
		@Override
		public boolean hasNext() {
			return i.hasNext();
		}

		@Override
		public Entry<K, V> next() {
			return new ALBEntry<>(lookup, i.next());
		}

		@Override
		public void remove() {
			i.remove();
		}
	}

	public Set<Entry<K, V>> entrySet() {
		return new EntrySet<>(this);
	}

}

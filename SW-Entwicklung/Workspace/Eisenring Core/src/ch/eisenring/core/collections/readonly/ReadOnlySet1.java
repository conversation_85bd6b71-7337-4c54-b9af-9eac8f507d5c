package ch.eisenring.core.collections.readonly;

import java.util.Iterator;
import java.util.NoSuchElementException;

import ch.eisenring.core.collections.Set;

public final class ReadOnlySet1<E> extends ReadOnlyCollection<E> implements Set<E> {

	private final E element;

	public ReadOnlySet1(final E element) {
		this.element = element;
	}

	@Override
	protected ReadOnlySet1<E> clone() throws CloneNotSupportedException {
		return this;
	}

	@Override
	public boolean contains(final Object object) {
		return object == null ? element == null : object.equals(element);
	}

	@Override
	public int size() {
		return 1;
	}

	@Override
	public boolean isEmpty() {
		return false;
	}

	final class Itr implements Iterator<E> {
		boolean hasNext = true;
		
		@Override
		public boolean hasNext() {
			return hasNext;
		}
	
		@Override
		public E next() {
			if (hasNext) {
				hasNext = false;
				return element;
			}
			throw new NoSuchElementException();
		}

		@Override
		public void remove() {
			 throw new UnsupportedOperationException();			
		}
	}

	@Override
	public Iterator<E> iterator() {
		return new Itr();
	}

	@Override
	public Object[] toArray() {
		return new Object[] { element };
	}

}

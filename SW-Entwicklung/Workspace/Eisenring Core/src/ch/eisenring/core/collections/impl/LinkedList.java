package ch.eisenring.core.collections.impl;

import ch.eisenring.core.collections.List;

@SuppressWarnings("serial")
public class LinkedList<E> extends java.util.LinkedList<E> implements List<E> {

	public LinkedList() {
	}

	public LinkedList(final java.util.Collection<? extends E> collection) {
		super(collection);
	}

	// --------------------------------------------------------------
	// ---
	// --- Extra implementation
	// ---
	// --------------------------------------------------------------

	// None so far

}

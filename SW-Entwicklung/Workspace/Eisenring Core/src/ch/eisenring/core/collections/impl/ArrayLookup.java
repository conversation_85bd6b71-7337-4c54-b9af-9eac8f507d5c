package ch.eisenring.core.collections.impl;

import static ch.eisenring.core.collections.SizingPolicy.ID_CONSERVATIVE;
import static ch.eisenring.core.collections.SizingPolicy.ID_MINMEMORY;
import static ch.eisenring.core.collections.SizingPolicy.ID_PERFORMANCE;
import static ch.eisenring.core.collections.SizingPolicy.ID_REASONABLE;

import java.util.Arrays;
import java.util.ConcurrentModificationException;
import java.util.Iterator;
import java.util.NoSuchElementException;

import ch.eisenring.core.collections.Lookup;
import ch.eisenring.core.collections.Map;
import ch.eisenring.core.collections.SizingPolicy;
import ch.eisenring.core.datatypes.primitives.Primitives;

/**
 * Implementation of java.util.Map backed by an array. Each entry occupies
 * two indices in the array (even key, odd value) and the entries are
 * ordered by key.hashCode for binary search lookups. 
 * 
 * Characteristics:
 * - no real support for entrySet(), keySet() and value() (they return snapshots
 *   of the current state, not views).
 * - relatively fast lookup/replace O(log(N))
 * - slow insert: O(N)
 * - low memory overhead (only two references per entry, no extra objects)
 * - supports null key and values
 * 
 * Use for:
 * - small maps (N < 20)
 * - maps that are mostly looked up but rarely modified
 * 
 * Don't use for:
 * - large maps
 * - maps that are mostly written
 * - keys that have expensive hashCode methods (hashes are not cached)
 * - maps where any of the view methods are required
 */
@SuppressWarnings("unchecked")
public final class ArrayLookup<K, V> extends ArrayLookupBase<K, V> implements Map<K, V> {
	
	Object[] elements;
	int size;
	transient int modCount;
	transient int policyId;

	public ArrayLookup() {
		this(SizingPolicy.REASONABLE);
	}

	public ArrayLookup(final SizingPolicy policy) {
		this.policyId = policy.getPolicyId();
		elements = newArray(defaultSizeForPolicy(policyId, DEFAULT_SIZE));
	}
	
	public ArrayLookup(final int initialCapacity) {
		this(SizingPolicy.PERFORMANCE, initialCapacity);
	}

	public ArrayLookup(final SizingPolicy policy, final int initialCapacity) {
		this.policyId = policy.getPolicyId();
		int capacity;
		if (initialCapacity < 0) {
			capacity = defaultSizeForPolicy(policyId, DEFAULT_SIZE);
		} else {
			capacity = initialCapacity << 1;
			if (capacity < 0 || capacity > MAX_SIZE)
				capacity = MAX_SIZE;
		}
		elements = newArray(ArrayCollectionBase.optimalArrayLength(capacity));
	}
	
	public ArrayLookup(final Lookup<? extends K, ? extends V> lookup) {
		this(lookup.size());
		putAll(lookup);
	}

	@Override
	final Object[] elements() {
		return elements;
	}

	@Override
	final int size(final Object[] elements) {
		return size;
	}

	// --------------------------------------------------------------
	// ---
	// --- Extra API (not part or Map interface)
	// ---
	// --------------------------------------------------------------
	@Override
	public void setPolicy(final SizingPolicy policy) {
		this.policyId = policy.getPolicyId();
	}

	/**
	 * Shrink the backing array to the minimum possible size.
	 */
	@Override
	public void trimToSize() {
		int capacity = elements.length;
		if (capacity == size)
			return;
		if (capacity == 0) {
			// drop the backing array
			elements = Primitives.EMPTY_OBJECT_ARRAY;
		} else {
			// shrink backing array
			final Object[] newElements = new Object[size];
			System.arraycopy(elements, 0, newElements, 0, size);
			elements = newElements;
		}
	}

	@Override
	public void ensureCapacity(final int capacity) {
		int requestedCapacity = capacity << 1;
		if (requestedCapacity > MAX_SIZE)
			requestedCapacity = MAX_SIZE;
		if (requestedCapacity <= this.elements.length)
			return;
		// allocate larger backing array
		final Object[] newElements = new Object[requestedCapacity];
		System.arraycopy(this.elements, 0, newElements, 0, this.size);
		this.elements = newElements;
	}

	// --------------------------------------------------------------
	// ---
	// --- Map implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public void clear() {
		if (size != 0) {
			switch (policyId) {
				default:
				case ID_PERFORMANCE:
				case ID_REASONABLE:
					Arrays.fill(elements, 0, size, null);
					break;
				case ID_CONSERVATIVE:
				case ID_MINMEMORY:
					elements = Primitives.EMPTY_OBJECT_ARRAY;
					break;
			}
		}
		size = 0;
		++modCount;
	}

	@Override
	public V put(final K key, final V value) {
		++modCount;
		int index;
		if (key == null) {
			// put for null key
			index = 0;
			while (index < size) {
				final Object element = elements[index];
				if (element == null) {
					// replace value of existing mapping
					final Object result = elements[++index];
					elements[index] = value;
					return (V) result;
				}
				if (hashOf(element) > NULL_HASH)
					break;
				index += 2;
			}
		} else {
			index = indexOfKey(elements, size, key);
			if (index < 0) {
				index = ~index;
			} else {
				// replace value of existing mapping
				final Object result = elements[++index];
				elements[index] = value;
				return (V) result;
			}
		}
		// add a new element
		allocateIndex(index);
		elements[index] = key;
		elements[++index] = value;
		size += 2;
		return null;
	}

	@Override
	public V remove(final Object key) {
		final int index = indexOfKey(elements, size, key);
		return index < 0 ? null : (V) removeIndex(index);
	}

	public void putAll(final Lookup<? extends K, ? extends V> lookup) {
		if (lookup.isEmpty())
			return;
		if (this.size == 0) {
			// special fast putAll when the lookup is empty and the source 
			// is a known implementation of lookup.
			++modCount;
			if (lookup instanceof ArrayLookup) {
				final ArrayLookup<?, ?> arrayMap = (ArrayLookup<?, ?>) lookup;
				// copy the source map's internal state
				if (this.elements.length < arrayMap.size) {
					this.elements = new Object[arrayMap.size];
				}
				System.arraycopy(arrayMap.elements, 0, this.elements, 0, arrayMap.size);
				this.size = arrayMap.size;
				return;
			} else if (lookup instanceof AtomicArrayLookup) {
				final Object[] source = ((AtomicArrayLookup<?, ?>) lookup).holder.get();
				this.size = source.length;
				// copy the source array
				if (this.elements.length < size) {
					this.elements = new Object[size];
				}
				System.arraycopy(source, 0, this.elements, 0, size);
				return;
			}
		}
		// copy mapping of the  source lookup through normal API calls
		for (Iterator<?> i = lookup.keyIterator(); i.hasNext(); ) {
			final K key = (K) i.next();
			final V value = (V) lookup.get(key);
			put(key, value);
		}
	}

	/**
	 * Allocate a new slot in the elements array. This will allocate a larger array
	 * if the current element array is too small. Otherwise it will just copy the entries
	 * from index to index + 2.
	 */
	void allocateIndex(final int index) {
		int newSize = size + 2;
		if (newSize < 0)
			throw new OutOfMemoryError("Too many entries");
		if (elements.length >= newSize) {
			// just move the entries behind index up by 2 slots
			System.arraycopy(elements, index, elements, index + 2, size - index);
			return;
		}
		// allocate new element array
		// allocate new element array
		switch (policyId) {
			case ID_MINMEMORY:
				break;
			case ID_CONSERVATIVE:
				newSize += (size < 64) ? 6 : (size >> 4);
				break;
			case ID_REASONABLE:
				newSize += (size < 64) ? 12 : (size >> 2);
				break;
			default:
			case ID_PERFORMANCE:
				newSize <<= 1;
				break;
		}
		if (newSize <= 0 || newSize > MAX_SIZE)
			newSize = MAX_SIZE;
		final Object[] newElements = new Object[newSize];
		System.arraycopy(elements, 0, newElements, 0, index);
		System.arraycopy(elements, index, newElements, index + 2, size - index);
		elements = newElements;
	}

	/**
	 * Removes the element at index. Moves remaining array entries as needed.
	 */
	Object removeIndex(final int index) {
		++modCount;
		final int size = this.size = this.size - 2;
		final Object result = elements[index + 1];
		final boolean shrink;
		switch (policyId) {
			default:
			case ID_PERFORMANCE:
				shrink = false;
				break;
			case ID_REASONABLE:
				shrink = (elements.length >> 2 > size);
				break;
			case ID_CONSERVATIVE:
				shrink = (elements.length >> 1 > size);
				break;
			case ID_MINMEMORY:
				shrink = true;
				break;
		}
		if (shrink) {
			if (size == 0) {
				elements = Primitives.EMPTY_OBJECT_ARRAY;
				return result;
			} else {
				Object[] newElements = new Object[size];
				System.arraycopy(elements, 0, newElements, 0, index);
				System.arraycopy(elements, index + 2, newElements, index, size - index);
				elements = newElements;
				return result;
			}
		}
		System.arraycopy(elements, index + 2, elements, index, size - index);
		elements[size] = null;
		elements[size + 1] = null;
		return result;
	}

	// --------------------------------------------------------------
	// ---
	// --- Iteration
	// ---
	// --------------------------------------------------------------
	static class ContentIterator<E> implements Iterator<E> {
		final ArrayLookup<?, ?> lookup;
		int modCount;
		int index;
		int removedIndex;
		
		ContentIterator(final ArrayLookup<?, ?> lookup, final int startIndex) {
			this.lookup = lookup;
			this.modCount = lookup.modCount;
			this.index = startIndex;
			this.removedIndex = startIndex;
		}

		@Override
		public boolean hasNext() {
			if (modCount != lookup.modCount)
				throw new ConcurrentModificationException();
			return (index + 2) < lookup.size;
		}

		@SuppressWarnings("unchecked")
		@Override
		public E next() {
			if (modCount != lookup.modCount)
				throw new ConcurrentModificationException();
			final int index = this.index + 2; 
			if (index >= lookup.size)
				throw new NoSuchElementException();
			return (E) lookup.elements[(this.index = index)];
		}

		@Override
		public void remove() {
			if (modCount != lookup.modCount)
				throw new ConcurrentModificationException();
			final int index = this.index;
			if (removedIndex == index || index < 0 || index >= lookup.size)
				throw new IllegalStateException();
			// make sure the index is even (for value iteration)
			lookup.removeIndex(index & -2);
			removedIndex = this.index = (index - 2);
			modCount = lookup.modCount;
		}
	}

	@Override
	public Iterator<K> keyIterator() {
		return new ContentIterator(this, -2);
	}

	@Override
	public Iterator<V> valueIterator() {
		return new ContentIterator<V>(this, -1);
	}

	// --------------------------------------------------------------
	// ---
	// --- Object overrides
	// ---
	// --------------------------------------------------------------
	@Override
	protected Lookup<K, V> clone() throws CloneNotSupportedException {
		final ArrayLookup<K, V> result = (ArrayLookup<K, V>) super.clone();
		result.elements = Primitives.EMPTY_OBJECT_ARRAY;
		result.size = 0;
		result.putAll((Lookup<K, V>) this);
		return result;
	}

}

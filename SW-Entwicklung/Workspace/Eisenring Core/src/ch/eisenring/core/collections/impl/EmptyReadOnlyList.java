package ch.eisenring.core.collections.impl;

import java.util.Collection;
import java.util.Iterator;
import java.util.ListIterator;

import ch.eisenring.core.collections.SizingPolicy;
import ch.eisenring.core.datatypes.primitives.Primitives;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.iterators.EmptyIterator;

/**
 * Special implementation of the List interface.
 * 
 * This list can not be altered and is always empty.
 * As such it consumes no memory and all its operations are insanely fast :)
 */
final class EmptyReadOnlyList<T> implements ch.eisenring.core.collections.List<T> {

	EmptyReadOnlyList() {
	}

	@Override
	public void add(final int index, final Object element) {
		throw new UnsupportedOperationException();
	}

	@Override
	public boolean add(final Object element) {
		throw new UnsupportedOperationException();
	}

	@Override
	public boolean addAll(final Collection<? extends T> c) {
		throw new UnsupportedOperationException();
	}

	@Override
	public boolean addAll(final int index, final Collection<? extends T> c) {
		throw new UnsupportedOperationException();
	}

	@Override
	public void clear() {
		// does nothing
	}

	@Override
	public boolean contains(final Object o) {
		return false;
	}

	public boolean containsAll(final Collection<?> c) {
		return c.isEmpty();
	}

	@Override
	public T get(final int index) {
		throw new IndexOutOfBoundsException(Strings.toString(index));
	}

	@Override
	public int indexOf(final Object o) {
		return -1;
	}

	@Override
	public boolean isEmpty() {
		return true;
	}

	@SuppressWarnings("unchecked")
	@Override
	public Iterator<T> iterator() {
		return (Iterator<T>) EmptyIterator.INSTANCE;
	}

	@Override
	public int lastIndexOf(final Object o) {
		return -1;
	}

	@SuppressWarnings("unchecked")
	@Override
	public ListIterator<T> listIterator() {
		return (ListIterator<T>) EmptyIterator.INSTANCE;
	}

	@SuppressWarnings("unchecked")
	@Override
	public ListIterator<T> listIterator(final int index) {
		return (ListIterator<T>) EmptyIterator.INSTANCE;
	}

	@Override
	public T remove(final int index) {
		return null;
	}

	@Override
	public boolean remove(final Object o) {
		return false;
	}

	@Override
	public boolean removeAll(final Collection<?> c) {
		return false;
	}

	@Override
	public boolean retainAll(final Collection<?> c) {
		return false;
	}

	public T set(final int index, final T element) {
		throw new UnsupportedOperationException();
	}

	@Override
	public int size() {
		return 0;
	}

	@Override
	public java.util.List<T> subList(final int fromIndex, final int toIndex) {
        if (fromIndex != 0)
            throw new IndexOutOfBoundsException(Strings.concat("fromIndex = ", fromIndex));
        if (toIndex != 0)
            throw new IndexOutOfBoundsException(Strings.concat("toIndex = ", toIndex));
        return this;
	}

	@Override
	public Object[] toArray() {
		return Primitives.EMPTY_OBJECT_ARRAY;
	}

	@Override
	public <U extends Object> U[] toArray(final U[] a) {
		if (a.length > 0)
			a[0] = null;
		return a;
	}

	@Override
	public void trimToSize() {
		// NO-OP
	}

	@Override
	public void ensureCapacity(final int capacity) {
		// NO-OP
	}

    @Override
    public void setPolicy(final SizingPolicy policy) {
    	// NO-OP (can't grow or shrink anyway)
    }

	// --------------------------------------------------------------
	// ---
	// --- Object overrides
	// ---
	// --------------------------------------------------------------
	@Override
	public int hashCode() {
		return 1;
	}

	@Override
	public boolean equals(final Object o) {
		return o instanceof java.util.List && ((java.util.List<?>) o).isEmpty();
	}

	@Override
	public String toString() {
		return "[]";
	}

	@Override
	protected EmptyReadOnlyList<T> clone() throws CloneNotSupportedException {
		return this;
	}

}

package ch.eisenring.core.collections;

import ch.eisenring.core.datatypes.primitives.Primitives;

//@Deprecated
public final class ArrayContainerManager<T> {

	private final Class<T> componentType;
	
	public ArrayContainerManager(final Class<T> componentType) {
		this.componentType = componentType;
	}
	
	/**
	 * Create a new array with the component type of this manager.
	 * If length is 0, a shared instance may be returned.
	 */
	@Deprecated
	public T[] newArray(final int length) {
		return Primitives.newArray(componentType, length);
	}

	/**
	 * Get the first index that contains element, 
	 * -1 if element is not in array. 
	 */
	@Deprecated
	public int indexOf(final T[] array, final T element) {
		if (array == null)
			return -1;
		final int l = array.length;
		if (element == null) {
			for (int i=0; i<l; ++i) {
				if (array[i] == null)
					return i;
			}
		} else {
			for (int i=0; i<l; ++i) {
				if (element.equals(array[i]))
					return i;
			}
		}
		return -1;
	}

	/**
	 * Returns true if array contains element
	 */
	//@Deprecated
	public boolean contains(final T[] array, final T element) {
		return indexOf(array, element) >= 0;
	}

	/**
	 * Removes element from array and returns a new array.
	 * If element is not in array, input array is returned.
	 */
	//@Deprecated
	public T[] remove(final T[] array, final T element) {
		final int index = indexOf(array, element);
		if (index < 0)
			return array;
		final int length = array.length;
		if (length > 1) {
			final int newLen = length - 1;
			final T[] temp = Primitives.newArray(componentType, newLen);
			int j = 0;
			for (int i=0; i<length; ++i) {
				if (i != index) {
					temp[j++] = array[i];
				}
			}
			return temp;
		} else {
			return newArray(0);
		}
	}

	/**
	 * Adds the element to array and returns a new array.
	 */
	//@Deprecated
	public T[] add(final T[] array, final T element) {
		final T[] result;
		if (array == null) {
			result = Primitives.newArray(componentType, 1);
		} else {
			for (int i=array.length-1; i>=0; --i) {
				if (element.equals(array[i]))
					return array;
			}
			result = Primitives.newArray(componentType, array.length + 1);
			System.arraycopy(array, 0, result, 0, array.length);
		}
		result[result.length - 1] = element;
		return result;
	}

}

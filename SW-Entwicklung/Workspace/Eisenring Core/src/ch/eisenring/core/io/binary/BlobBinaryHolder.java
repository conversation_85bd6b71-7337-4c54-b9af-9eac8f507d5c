package ch.eisenring.core.io.binary;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.sql.Blob;
import java.sql.SQLException;
import java.sql.SQLFeatureNotSupportedException;

import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.io.Streams;
import ch.eisenring.core.io.util.LimitedInputStream;

/**
 * Wraps a BinaryHolder to be used as Blob.
 * 
 * We go through the trouble of providing our own Blob implementation
 * because its the only way to avoid duplicating large amounts of data
 * in memory.
 * 
 * This implementation does not support all features, as it is only
 * used for storing, not for retrieving data.
 */
final class BlobBinaryHolder implements Blob {

	private final BinaryHolder holder;
	private final long length;
	
	BlobBinaryHolder(final BinaryHolder holder) throws SQLException {
		try {
			this.holder = holder;
			this.length = holder.size();
		} catch (final IOException e) {
			throw new SQLException(e);
		}
	}

	@Override
	public void free() throws SQLException {
		// does nothing for this class
	}

	@Override
	public long length() throws SQLException {
		return length;
	}

	@Override
	public byte[] getBytes(final long pos, final int length) throws SQLException {
		if (pos <= 0)
			throw new SQLException(Strings.concat("invalid position: ", pos));
		InputStream input = null;
		try {
			if (pos == 1 && length == length())
				return holder.toByteArray();
			input = getBinaryStream(pos, length);
			final BinaryHolder subHolder = BinaryHolderUtil.create(input);
			return subHolder.toByteArray();
		} catch (final IOException e) {
			throw new SQLException(e);
		} finally {
			Streams.closeSilent(input);
		}
	}

	@Override
	public InputStream getBinaryStream() throws SQLException {
		try {
			return holder.getInputStream();
		} catch (final IOException e) {
			throw new SQLException(e);
		}
	}

	@Override
	public InputStream getBinaryStream(final long pos, final long length) throws SQLException {
		if (pos <= 0)
			throw new SQLException(Strings.concat("invalid position: ", pos));
		if (pos == 1 && length == this.length)
			return getBinaryStream();
		InputStream binaryInput = null;
		InputStream limitedInput = null;
		try {
			binaryInput = holder.getInputStream();
			binaryInput.skip(pos - 1);
			limitedInput = new LimitedInputStream(binaryInput, length);
			return limitedInput;
		} catch (final IOException e) {
			Streams.closeSilent(limitedInput);
			Streams.closeSilent(binaryInput);
			throw new SQLException(e);
		}
	}

	@Override
	public long position(final Blob pattern, final long start) throws SQLException {
		throw new SQLFeatureNotSupportedException("not implemented");
	}

	@Override
	public long position(final byte[] pattern, final long start) throws SQLException {
		throw new SQLFeatureNotSupportedException("not implemented");
	}

	@Override
	public OutputStream setBinaryStream(final long pos) throws SQLException {
		throw new SQLFeatureNotSupportedException("immutable Blob");
	}

	@Override
	public int setBytes(final long pos, final byte[] bytes) throws SQLException {
		throw new SQLFeatureNotSupportedException("immutable Blob");
	}

	@Override
	public int setBytes(final long pos, final byte[] bytes, final int offset, final int len) throws SQLException {
		throw new SQLFeatureNotSupportedException("immutable Blob");
	}

	@Override
	public void truncate(final long len) throws SQLException {
		throw new SQLFeatureNotSupportedException("immutable Blob");
	}

}

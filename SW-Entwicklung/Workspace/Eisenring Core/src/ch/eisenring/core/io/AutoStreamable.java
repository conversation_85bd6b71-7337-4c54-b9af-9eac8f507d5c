package ch.eisenring.core.io;

/**
 * Marker interface for automatically Streamable classes.
 * 
 * Fields marked with the @AutoStreamed annotation are
 * to be streamed. Note that if performance is of the
 * utmost concern, implementing Streamable instead offers
 * a small advantage - at the cost of needing to implement
 * the read() and write() methods manually.
 */
public interface AutoStreamable {

}

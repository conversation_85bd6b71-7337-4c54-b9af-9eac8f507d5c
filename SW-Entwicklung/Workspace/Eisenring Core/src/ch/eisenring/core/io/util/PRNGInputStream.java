package ch.eisenring.core.io.util;

import java.io.IOException;
import java.io.InputStream;
import java.util.Random;

import ch.eisenring.core.io.StreamProperties;

/**
 * Provides an InputStream based on pseudo random number generator.
 * 
 * This is useful for testing scenarios where a large amount of data is
 * needed that can be easily reproduced.
 * 
 * This stream fully supports mark/reset as well as backwards skip,
 * but backward skip/reset performs poorly for large streams.
 */
public class PRNGInputStream extends InputStream implements StreamProperties {

	private final static int BUF_SIZE = 4 << 10;
	private final static int BUFPTR_MASK = BUF_SIZE - 1;
	private final static long BLOCKPTR_MASK = -1L ^ BUFPTR_MASK;

	/** Random generator for this stream */
	private Random random;
	
	/** Random seed value */
	private long randomSeed;
	
	/** Nominal length of this stream */
	private long streamLength;

	/** Position in stream */
	private long readPos;
	
	/** Position marked for reset */
	private long markPos;

	/** Random generator position */
	private long randomPos;

	/** Flag indicating stream is closed */
	private boolean closed;

	/** Internal buffer for random generation */
	private final byte[] randomBuffer = new byte[BUF_SIZE];

	/**
	 * Creates a PRNGInputStream with specified length and seed value.
	 * 
	 * @throws IllegalArgumentException if length is negative
	 */
	public PRNGInputStream(final long streamLength, final long seed) throws IllegalArgumentException {
		if (streamLength < 0)
			throw new IllegalArgumentException("length must not be negative");
		this.randomSeed = seed;
		this.streamLength = streamLength;
		this.randomPos = -BUF_SIZE;
	}

	// --------------------------------------------------------------
	// ---
	// --- Close management
	// ---
	// --------------------------------------------------------------
	@Override
	public void close() throws IOException {
		closed = true;
	}
	
	private void ensureOpen() throws IOException {
		if (closed)
			throw new IOException("stream already closed");
	}
	
	@Override
	public int read() throws IOException {
		ensureOpen();
		if (readPos >= streamLength)
			return -1;
		positionRandom();
		int bufPointer = ((int) readPos++) & BUFPTR_MASK;
		return randomBuffer[bufPointer] & 0xFF;
	}

	@Override
	public int read(final byte[] buffer, final int offset, final int length) throws IOException {
		ensureOpen();
        if (buffer == null) {
            throw new NullPointerException();
        } else if (offset < 0 || length < 0 || length > buffer.length - offset) {
            throw new IndexOutOfBoundsException();
        } else if (length == 0) {
            return 0;
        } else if (readPos >= streamLength) {
			return -1; // EoS
        }
		// determine how many bytes can be read
		int maxRead = (int) Math.min(streamLength - readPos, length);
		maxRead = Math.max(maxRead, 0);
		if (maxRead == 0)
			return 0;
		final long startReadPos = readPos;
		int putPointer = offset;
		while (maxRead > 0) {
			positionRandom();
			// copy current buffer portion
			final int bufPointer = ((int) readPos) & BUFPTR_MASK;
			final int copyLength = Math.min(maxRead, BUF_SIZE - bufPointer);
			System.arraycopy(randomBuffer, bufPointer, buffer, putPointer, copyLength);
			readPos += copyLength;
			maxRead -= copyLength;
			putPointer += copyLength;
		}
		// return the actual number of bytes read
		return (int) (readPos - startReadPos);
	}

	// --------------------------------------------------------------
	// ---
	// --- Random generation
	// ---
	// --------------------------------------------------------------
	/**
	 * Ensures random buffer is filled for current readPos 
	 */
	private void positionRandom() {
		final long readPos = this.readPos & BLOCKPTR_MASK;
		long randomPos = this.randomPos;
		if (randomPos == readPos)
			return;
		if (randomPos < 0 || randomPos > readPos) {
			// reset random
			random = new Random(randomSeed);
			randomPos = -BUF_SIZE;
		}
		// generate blocks until correct position reached
		final Random random = this.random;
		final byte[] buffer = this.randomBuffer;
		while (randomPos < readPos) {
			randomPos += buffer.length;
			if (randomPos == readPos) {
				// generate this block
				for (int i=0; i<BUF_SIZE; i+=8) {
					long l = random.nextLong();
					final int uh = (int) (l >> 32);
					final int lh = (int)  l;
					int j = i - 1;
					buffer[++j] = (byte) (uh >> 24);
					buffer[++j] = (byte) (uh >> 16);
					buffer[++j] = (byte) (uh >> 8);
					buffer[++j] = (byte)  uh;
					buffer[++j] = (byte) (lh >> 24);
					buffer[++j] = (byte) (lh >> 16);
					buffer[++j] = (byte) (lh >> 8);
					buffer[++j] = (byte)  lh;
				}
			} else {
				// skip this block (somewhat faster than generating it)
				for (int i=BUF_SIZE >> 3; i>0; --i)
					random.nextLong();
			}
		}
		this.randomPos = randomPos;
	}

	// --------------------------------------------------------------
	// ---
	// --- Mark, reset and skip support
	// ---
	// --------------------------------------------------------------
	@Override
	public boolean markSupported() {
		return true;
	}

	@Override
	public void mark(final int readlimit) {
		markPos = readPos;
	}

	@Override
	public void reset() throws IOException {
		ensureOpen();
		readPos = markPos;
	}

	@Override
	public long skip(final long n) throws IOException {
		ensureOpen();
		long newPos = readPos + n;
		if (n >= 0) {
			if (n == 0)
				return 0;
			// forward seek
			if (newPos < readPos) { // check for overflow
				newPos = streamLength;
			} else {
				newPos = Math.min(newPos, streamLength);
			}
		} else {
			// backward seek
			if (newPos >= readPos) { // overflow
				newPos = 0;
			} else {
				newPos = Math.max(newPos, 0);
			}
		}
		final long result = newPos - readPos;
		readPos = newPos;
		return result;
	}

	// --------------------------------------------------------------
	// ---
	// --- StreamProperties extension
	// ---
	// --------------------------------------------------------------
	@Override
	public boolean isBuffered() {
		return true;
	}

//	public static void main(String[] argv) {
//		try {
//			long start = System.currentTimeMillis();
//			OutputStream out = new FileOutputStream("C:\\Test\\ByteSeq.data");
//			InputStream in = new PRNGInputStream(4096L << 20, 12345678L);
//			
//			long copied = Streams.copyFully(in, out, 3000);
//			copied += Streams.copy(in, out, 65536);
//			System.out.println("bytes copied: " + copied);
//			out.close();
//			in.close();
//			System.out.println("time: " + (System.currentTimeMillis() - start) + "ms");
//			
//			// test if 2 streams yield the same content
//			PRNGInputStream in1 = new PRNGInputStream(2716384, -7); 
//			PRNGInputStream in2 = new PRNGInputStream(2716384, -7); 
//			System.out.println("verify1: " + Streams.verifySame(in1, in2));
//			in1.skip(-327001);
//			in2.skip(-327001);
//			System.out.println("verify2: " + Streams.verifySame(in1, in2));
//			in1.reset();
//			in2.reset();
//			System.out.println("verify3: " + Streams.verifySame(in1, in2));
//			in1.reset();
//			in2.reset();
//			in1.skip(1);
//			System.out.println("verify4: " + Streams.verifySame(in1, in2));
//			
//		} catch (final Exception e) {
//			e.printStackTrace();
//		}
//	}

}

package ch.eisenring.core.io.file;

import java.io.File;

/**
 * Default implementation of FileItemFactory
 */
final class FileItemFactoryFile implements FileItemFactory {

	@Override
	public int getPriority() {
		return Integer.MIN_VALUE;
	}

	@Override
	public String getStandardPrefix() {
		return "FILE://";
	}

	@Override
	public boolean accepts(final String specifier) {
		return true;
	}

	@Override
	public FileItem get(final String specifier) throws IllegalArgumentException {
		// cut off the prefix if needed
		return new FileItemFile(new File(specifier));
	}

}

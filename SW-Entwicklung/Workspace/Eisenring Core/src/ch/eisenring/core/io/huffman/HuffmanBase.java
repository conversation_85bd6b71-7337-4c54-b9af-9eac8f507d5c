package ch.eisenring.core.io.huffman;

abstract class HuffmanBase {

	public final long probability;
	public HuffmanNode parent;

	protected <PERSON><PERSON>manBase(final long probability) {
		this.probability = probability;
	}

}

final class HuffmanLeaf extends HuffmanBase {

	public final int symbol;

	public HuffmanLeaf(final long probability, final int symbol) {
		super(probability);
		this.symbol = symbol;
	}
	
}

final class HuffmanNode extends HuffmanBase {

	public final HuffmanBase child0;
	public final HuffmanBase child1;

	public HuffmanNode(final long probability, final HuffmanBase child0, final HuffmanBase child1) {
		super(probability);
		this.child0 = child0;
		this.child1 = child1;
		child0.parent = this;
		child1.parent = this;
	}

}

package ch.eisenring.core.io.stats;

import java.io.IOException;

/**
 * Abstract interface for recording stream statistics
 */
public interface StreamStats {

	/**
	 * Adds boolean to statistics record
	 */
	public default void add(final boolean b) {
		// nothing
	}

	/**
	 * Adds byte to statistics record
	 */
	public default void add(final byte b) {
		// nothing
	}

	/**
	 * Adds short to statistics record
	 */
	public default void add(final short s) {
		// nothing
	}

	/**
	 * Adds int to statistics record
	 */
	public default void add(final int i) {
		// nothing
	}
	
	/**
	 * Adds long to statistics record
	 */
	public default void add(final long l) {
		// nothing
	}

	/**
	 * Adds float to statistics record
	 */
	public default void add(final float f) {
		// nothing
	}

	/**
	 * Adds double to statistics record
	 */
	public default void add(final double d) {
		// nothing
	}

	/**
	 * Adds character to statistics record
	 */
	public default void add(final char c) {
		// nothing
	}

	/**
	 * Adds string to statistics record
	 */
	public default void add(final CharSequence charSeq) {
		// nothing
	}

	// --------------------------------------------------------------
	// ---
	// --- Management
	// ---
	// --------------------------------------------------------------
	/**
	 * Resets this statistics. Reset means put the instance
	 * into the state it would have had after creation,
	 * usually all counts zero.
	 */
	public default void reset() {
		// not supported by default implementation
	}
	
	/**
	 * Adds the counts from another statistics instance.
	 * Only works for compatible implementations, returns false if types are incompatible
	 */
	public default boolean add(final StreamStats source) {
		return false;
	}

	/**
	 * Dumps the statistics in human readable form.
	 */
	public default void append(final Appendable target) throws IOException {
		target.append(getClass().getName());
		target.append(": Stats writing not supported");
	}

	// --------------------------------------------------------------
	// ---
	// --- Dummy instance
	// ---
	// --------------------------------------------------------------
	/**
	 * Non-recording instance
	 */
	public final static StreamStats NULL = new StreamStats() {};

	// --------------------------------------------------------------
	// ---
	// --- Factory
	// ---
	// --------------------------------------------------------------
	public interface Factory {

		public StreamStats create();

	}

	/**
	 * Factory for a no-op dummy instance
	 */
	public final static Factory NULL_FACTORY = new Factory() {
		@Override
		public StreamStats create() {
			return NULL;
		}
	};
	
}

package ch.eisenring.core.io.memory;

import java.io.IOException;
import java.io.OutputStream;

import ch.eisenring.core.datatypes.primitives.Primitives;
import ch.eisenring.core.datatypes.strings.Strings;

final class MemoryBlockArray extends MemoryBlock {

	int usedCapacity;
	byte[] array;

	/**
	 * Allocates a new array block
	 */
	MemoryBlockArray(final MemoryBlock pred, final int capacity) {
		this.array = Primitives.newByteArray(capacity);
		if (pred == null) {
			baseOffset = 0;
		} else {
			baseOffset = pred.baseOffset + pred.getCapacity();
			this.pred = pred;
			pred.succ = this;
		}
	}

	@Override
	public long getCapacity() {
		return array.length;
	}

	@Override
	public long getFreeCapacity() {
		return array.length - usedCapacity;
	}

	@Override
	public long getUsedCapacity() {
		return usedCapacity;
	}

	@Override
	public int write(final int b) throws IOException {
		if (usedCapacity >= array.length)
			return 0;
		array[usedCapacity] = (byte) b;
		usedCapacity += 1;
		return 1;
	}

	@Override
	public int write(final byte[] buffer, final int offset, final int length) throws IOException {
		int writeLength = Math.min(length, array.length - usedCapacity);
		System.arraycopy(buffer, offset, array, usedCapacity, writeLength);
		usedCapacity += writeLength;
		return writeLength;
	}

	@Override
	public void writeTo(final OutputStream output) throws IOException {
		if (usedCapacity <= 0)
			return;
		output.write(array, 0, usedCapacity);
	}

	@Override
	public int read(final long baseOffset) throws IOException {
		if (baseOffset < this.baseOffset)
			throw new IOException(Strings.concat("baseOffset outside block range: ", baseOffset));
		if (baseOffset >= this.baseOffset + usedCapacity)
			throw new IOException(Strings.concat("baseOffset outside block range: ", baseOffset));
		return array[(int) (baseOffset - this.baseOffset)] & 0xFF;
	}

	@Override
	public int read(final long baseOffset, final byte[] buffer, final int offset, final int length) throws IOException {
		if (baseOffset < this.baseOffset)
			return 0;
		if (baseOffset >= this.baseOffset + usedCapacity)
			return 0;
		final int arrayOffset = (int) (baseOffset - this.baseOffset);
		final int readLength = Math.min(usedCapacity - arrayOffset, length);
		System.arraycopy(array, arrayOffset, buffer, offset, readLength);
		return readLength;
	}

	@Override
	public void dispose() {
		// nothing for this class
	}

}

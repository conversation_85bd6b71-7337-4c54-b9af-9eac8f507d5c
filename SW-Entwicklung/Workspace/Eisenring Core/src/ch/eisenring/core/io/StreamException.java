package ch.eisenring.core.io;

import java.io.IOException;

/**
 * Exception thrown by the streaming system.
 */
@SuppressWarnings("serial")
public class StreamException extends IOException {

	public StreamException(final String message) {
		super(message);
	}
	
	public StreamException(final String message, final Throwable cause) {
		super(message, cause);
	}
	
	public StreamException(final Throwable cause) {
		super(cause);
	}
	
}

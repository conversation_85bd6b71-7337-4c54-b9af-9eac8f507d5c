package ch.eisenring.core.io.file;

/**
 * Represents the properties of a FileItem.
 * 
 * It depends entirely on the FileItem implementation how these
 * methods respond, anything between caching the values when
 * the properties are created to live query is acceptable. 
 */
public interface FileItemProperties {

	/**
	 * Gets the file item these properties belong to
	 */
	public FileItem getFile();

	/**
	 * Gets the length of this file, the return value is unspecified
	 * if this item does not represent a file.
	 */
	public long length();

	/**
	 * Returns true if this item exists
	 */
	public boolean exists();
	
	/**
	 * Returns true if this item represents a directory
	 */
	public boolean isDirectory();
	
	/**
	 * Returns true of this item represents a file
	 */
	public boolean isFile();

	/**
	 * Returns the timestamp when this item was last modified.
	 * The return value is unspecified if this item does not exist.
	 */
	public long lastModified();

	/**
	 * Sets the lastModified timestamp. Returns false if the 
	 * timestamp could not be altered. Possibly not supported by
	 * all implementations.
	 */
	public boolean setLastModified(final long lastModified);

	/**
	 * Updates the modifier timestamp to current system time
	 */
	public default boolean touch() {
		return setLastModified(System.currentTimeMillis());
	}

	/**
	 * Returns true if the item is considered writable.
	 * If the implementation does not have the concept or
	 * cannot implement it, may simply return true. 
	 */
	public boolean isWritable();

	/**
	 * Returns true if the item is considered readable.
	 * If the implementation does not have the concept or
	 * cannot implement it, may simply return true. 
	 */
	public boolean isReadable();

}

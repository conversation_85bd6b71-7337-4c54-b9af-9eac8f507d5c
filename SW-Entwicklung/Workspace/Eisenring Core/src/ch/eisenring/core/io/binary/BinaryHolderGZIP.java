package ch.eisenring.core.io.binary;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.zip.GZIPInputStream;
import java.util.zip.GZIPOutputStream;

import ch.eisenring.core.io.Streams;
import ch.eisenring.core.io.memory.MemoryOutputStream;

public final class BinaryHolderGZIP extends BinaryHolderCompressed {

	public final static BinaryHolderCompressor COMPRESSOR = new BinaryHolderCompressor() {
		@Override
		public BinaryHolder transform(final BinaryHolder uncompressed) throws IOException {
			return create(uncompressed);
		}
	};
	
	public BinaryHolderGZIP(final BinaryHolder baseHolder) throws IOException {
		super(baseHolder);
	}

	@Override
	public InputStream getInputStream() throws IOException {
		synchronized (getLock()) {
			InputStream stream = super.getInputStream();
			return new GZIPInputStream(stream, 16384);
		}
	}

	@Override
	protected void disposeImpl() {
		// nothing for this type
	}

	// --------------------------------------------------------------
	// ---
	// --- Create a GZIP compressed version of an existing BinaryHolder
	// ---
	// --------------------------------------------------------------
	/**
	 * Takes the BinaryHolder and creates a new BinaryHolder that
	 * contains the same data compressed using GZIP.
	 */
	public static BinaryHolder create(final BinaryHolder holder) throws IOException {
		InputStream input = null;
		MemoryOutputStream memOut = null;
		OutputStream cmpOut = null;
		try {
			synchronized (holder.getLock()) {
				final long size = holder.size();
				memOut = new MemoryOutputStream();
				memOut.write((int) (size >> 56));
				memOut.write((int) (size >> 48));
				memOut.write((int) (size >> 40));
				memOut.write((int) (size >> 32));
				memOut.write((int) (size >> 24));
				memOut.write((int) (size >> 16));
				memOut.write((int) (size >> 8));
				memOut.write((int) (size));
				cmpOut = new GZIPOutputStream(memOut, 16384);
				input = holder.getInputStream(0, size);
				Streams.copy(input, cmpOut, size);
			}
			cmpOut.flush();
			cmpOut.close();
			final BinaryHolder gzipHolder = BinaryHolderUtil.create(memOut, true); 
			return gzipHolder;
		} finally {
			Streams.closeSilent(input);
			Streams.closeSilent(cmpOut);
			Streams.closeSilent(memOut);
		}
	}
	
}

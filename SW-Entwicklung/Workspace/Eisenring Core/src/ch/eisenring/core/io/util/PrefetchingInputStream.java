package ch.eisenring.core.io.util;

import java.io.IOException;
import java.io.InputStream;
import java.lang.ref.Cleaner;

import ch.eisenring.core.io.StreamProperties;
import ch.eisenring.core.threading.ThreadPool;

/**
 * InputStream wrapper that actively prefetches in a separate thread.
 * 
 * Almost all processing is implemented in a separate class to ensure
 * the prefetching thread does not hold a reference to the actual stream
 * object exposed as API. 
 * This was done as a safety measure to enable garbage collection of
 * the API object, and using a finalizer on the API object, to shut
 * down the prefetcher when the stream is accidentally not closed. 
 */
public class PrefetchingInputStream extends InputStream implements StreamProperties {

	private final static int DEFAULT_FETCHSIZE = 32 << 10;
	private final static int DEFAULT_BUFFERSIZE = 32 << 20;

	private final PrefetchingInputStreamPrivate impl; 

	// --------------------------------------------------------------
	// ---
	// --- Constructors
	// ---
	// --------------------------------------------------------------
	/**
	 * Creates PrefetchingInputStream with default buffer and fetch size
	 */
	public PrefetchingInputStream(final InputStream baseStream) {
		this(baseStream, DEFAULT_BUFFERSIZE, DEFAULT_FETCHSIZE);
	}

	/**
	 * Creates PrefetchingInputStream with selected buffer size
	 */
	public PrefetchingInputStream(final InputStream baseStream, final int bufferSize) {
		this(baseStream, bufferSize, Math.min(bufferSize >> 2, DEFAULT_FETCHSIZE));
	}

	/**
	 * Creates PrefetchingInputStream with selected buffer and block size
	 */
	public PrefetchingInputStream(final InputStream baseStream, final int bufferSize, final int fetchSize) {
		if (baseStream == null)
			throw new NullPointerException("baseStream is null");
		if (bufferSize < 256)
			throw new IllegalArgumentException("bufferSize must be >= 256, is " + bufferSize);
		if (fetchSize < 16)
			throw new IllegalArgumentException("fetchSize must be >= 16, is " + fetchSize);
		if (bufferSize < fetchSize)
			throw new IllegalArgumentException("bufferSize must be >= fetchSize, is (" + bufferSize + ", " + fetchSize + ")");
		this.impl = new PrefetchingInputStreamPrivate(baseStream, bufferSize, fetchSize);
		createPrefetherThread(impl);
	}

	// --------------------------------------------------------------
	// ---
	// --- Special hook for thread creation
	// ---
	// --------------------------------------------------------------
	/**
	 * The actual prefetcher thread is started using this hook.
	 * 
	 * It can be overwritten to control where the thread comes from,
	 * e.g. instead of creating a thread a pool could be used.
	 */
	protected void createPrefetherThread(final Runnable runnable) {
		ThreadPool.DEFAULT.start(runnable, "PrefetchingInputStream");
	}

	// --------------------------------------------------------------
	// ---
	// --- InputStream API overrides
	// ---
	// --------------------------------------------------------------
	@Override
	public void mark(final int readlimit) {
		// not supported, NO-OP
	}

	@Override
	public boolean markSupported() {
		return false;
	}

	@Override
	public void reset() throws IOException {
		throw new IOException("mark/reset not supported");
	}

	@Override
	public int available() throws IOException {
		return impl.available();
	}

	@Override
	public int read() throws IOException {
		return impl.read();
	}

	@Override
	public int read(final byte[] b) throws IOException {
		return impl.read(b, 0, b.length);
	}

	@Override
	public int read(final byte[] b, final int off, final int len) throws IOException {
		return impl.read(b, off, len);
	}

	@Override
	public void close() throws IOException {
		impl.close();
	}

	// --------------------------------------------------------------
	// ---
	// --- StreamProperties implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public boolean isBuffered() {
		return true;
	}

	// --------------------------------------------------------------
	// ---
	// --- Object overrides
	// ---
	// --------------------------------------------------------------
	@Override
	protected void finalize() throws Throwable {
		synchronized (impl) {
			// emulate a close without closing the base stream,
			// its up to the base stream to handle itself
			impl.baseStream = null;
			impl.notifyAll();
		}
	}

}

/**
 * This class implements the actual logic.
 * 
 * It is separate from the stream object to enable garbage collection
 * of the stream facade (the prefetching thread doesn't have a reference
 * to it) and thus enabling termination of leaked instances through finalize.
 */
final class PrefetchingInputStreamPrivate implements Runnable {

	/**
	 * Holds a reference to the underlying stream,
	 * the field is nulled when the stream is closed.
	 */
	InputStream baseStream;

	/**
	 * Holds a reference to the thread executing the prefetch. 
	 */
	Thread prefetchThread;

	/**
	 * Buffer holding the prefetched data. Its used as a circular buffer
	 * with two indices (get and put).
	 */
	byte[] buffer;
	
	/**
	 * Index where the next byte to be get from the buffer resides.
	 */
	int getIndex;

	/**
	 * Index where the next byte is to be put in the buffer
	 */
	int putIndex;

	/**
	 * The prefetcher will chunk its reads from the base stream
	 * to this size (sometimes smaller). Each chunk becomes
	 * immediately available for reading.
	 */
	final int fetchSize;

	/**
	 * Keeps track of how many bytes in the buffer are currently valid.
	 * 0 indicates the buffer is empty, otherwise the value is positive.
	 */
	int availableBuffered;

	/**
	 * In case the stream is read in very small blocks, awaking the
	 * prefetcher every time a few bytes of the buffer are free
	 * would result in poor reading performance. Thus, the prefetcher
	 * is only awakened when the buffer contains less than a predetermined
	 * number of bytes ready.
	 * Calculated simply as buffer.length - fetchSize.
	 */
	final int notifyThreshhold;
	
	/**
	 * When the prefetcher encounters an exception, it will be stored here.
	 * The exception needs to be wrapped an rethrown when the reading side
	 * empties the buffer. 
	 * It may be ignored if the stream is closed before being read up to
	 * that point. 
	 */
	Throwable pendingException;

	/**
	 * Flag signaling baseStream has reached end of stream.
	 * When the buffer is empty and this flag has been set, it means
	 * there will be no more bytes and any read() should return -1.
	 */
	boolean endOfBaseStream;

	/**
	 * This flag is controlled by the prefetcher and is set to true
	 * when the prefetcher goes to wait (because the buffer is full).
	 * When the prefetcher wakes again it goes back to false.
	 * 
	 * Used to bypass a (useless) notify when the prefetcher is not waiting. 
	 */
	boolean prefetcherWaiting;

	/**
	 * This flag is set if the client is currently waiting for the prefetcher.
	 * 
	 * Used to bypass a notification when the client isn't waiting.
	 */
	boolean clientWaiting;

	// --------------------------------------------------------------
	// ---
	// --- Constructor
	// ---
	// --------------------------------------------------------------
	/**
	 * Note that there is no argument checking here, as this constructor
	 * is to be used only by the API implementation.
	 */
	PrefetchingInputStreamPrivate(final InputStream baseStream, final int bufferSize, final int fetchSize) {
		this.baseStream = baseStream;
		this.baseStream = baseStream;
		this.fetchSize = fetchSize;
		this.notifyThreshhold = bufferSize - fetchSize;
		this.buffer = new byte[bufferSize];
	}

	// --------------------------------------------------------------
	// ---
	// --- Prefetch Thread
	// ---
	// --------------------------------------------------------------
	/**
	 * Entry point for prefetch thread
	 */
	public void run() {
		try {
			while (prefetchLoop());
		} catch (final Throwable t) {
			synchronized (this) {
				pendingException = t;
			}
		}
	}

	boolean prefetchLoop() {
		int readSize;
		synchronized (this) {
			// if base stream is nulled, stream was closed. terminate.
			if (baseStream == null)
				return false;
			
			// buffer still sufficiently full?
			if (notifyThreshhold <= availableBuffered) {
				try {
					prefetcherWaiting = true;
					wait();
				} catch (final InterruptedException e) {
					pendingException = e;
					notifyAll();
					return false;
				} finally {
					prefetcherWaiting = false;
				}
				return true;
			}
			
			// determine how much we can read
			if (getIndex < putIndex) {
				readSize = buffer.length - putIndex;
			} else {
				readSize = getIndex - putIndex;
			}
		}
		
		// perform the read from base stream
		readSize = Math.max(readSize, fetchSize);
		int zeroCount = 3;
		int nread = 0;
		Exception readException = null;
		do {
			try {
				nread = baseStream.read(buffer, putIndex, readSize);
			} catch (final IOException e) {
				readException = e;
			}
		} while (nread == 0 && --zeroCount > 0);
		
		synchronized (this) {
			if (clientWaiting)
				notifyAll();
			if (readException != null) {
				pendingException = readException;
				return false;
			} else if (nread <= 0) {
				endOfBaseStream = true;
				return false;
			}
			putIndex += nread;
			if (putIndex >= buffer.length)
				putIndex = 0;
			availableBuffered += nread;
		}
		
		return true;
	}

	/**
	 * Advances the get index by n.
	 */
	void advanceGet(final int n) {
		this.availableBuffered -= n;
		int i = getIndex + n;
		if (i >= buffer.length)
			i -= buffer.length;
		this.getIndex = i;
		if (prefetcherWaiting && availableBuffered < notifyThreshhold)
			notifyAll();
	}

	void checkOpen() throws IOException {
		if (baseStream == null)
			throw new IOException("Stream closed");
		if (availableBuffered <= 0 && pendingException != null)
			throw new IOException(pendingException.getMessage(), pendingException);
	}

	int waitAvailable() throws IOException {
		while (availableBuffered <= 0) {
			if (pendingException != null)
				throw new IOException(pendingException.getMessage(), pendingException);
			if (endOfBaseStream)
				return -1;
			// safety check, if we are entering waiting state,
			// ensure the prefetcher isn't waiting for us either
			if (prefetcherWaiting)
				notifyAll();
			try {
				clientWaiting = true;
				wait();
			} catch (final InterruptedException e) {
				throw new IOException(e.getMessage(), e);
			} finally {
				clientWaiting = false;
			}
		}	
		return availableBuffered;
	}

	// --------------------------------------------------------------
	// ---
	// --- Stream overrides
	// ---
	// --------------------------------------------------------------
	synchronized int available() throws IOException {
		checkOpen();
		return availableBuffered;
	}

	synchronized int read() throws IOException {
		checkOpen();
		final int available = waitAvailable();
		int result = -1;
		if (available > 0) {
			result = buffer[getIndex] & 0xFF;
			advanceGet(1);
		}
		return result;
	}

	synchronized int read(final byte[] b, int off, int len) throws IOException {
		checkOpen();
		final int available = waitAvailable();
		if (available < 0)
			return -1;
		int nread; 
		if (getIndex < putIndex) {
			nread = putIndex - getIndex;
		} else {
			nread = buffer.length - getIndex;
		}
		nread = Math.min(len, nread);
		System.arraycopy(buffer, getIndex, b, off, nread);
		advanceGet(nread);
		return nread;
	}

	synchronized void close() throws IOException {
		if (baseStream == null)
			return;
		try {
			baseStream.close();
		} finally {
			baseStream = null;
			// notify unconditionally
			notifyAll();
		}
	}

}

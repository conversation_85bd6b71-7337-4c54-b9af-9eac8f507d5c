package ch.eisenring.core.codetype;

import static java.lang.annotation.ElementType.TYPE;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

import java.lang.annotation.Retention;
import java.lang.annotation.Target;

/**
 * Annotation used by DynamicCode to mark if the
 * code type may be serialized. Types not annotated
 * are to be considered as serializable.
 */
@Retention(RUNTIME) 
@Target(TYPE)
public @interface Serializable {

	boolean value();
	
}

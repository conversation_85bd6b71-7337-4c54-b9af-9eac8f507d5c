package ch.eisenring.core.codetype;

import static java.lang.annotation.ElementType.TYPE;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

import java.lang.annotation.Retention;
import java.lang.annotation.Target;

/**
 * Annotation used by DynamicCode to specify the
 * SQL-query used to populate the code type. 
 */
@Retention(RUNTIME) 
@Target(TYPE)
public @interface LoadQuery {

	String value(); 

}

package ch.eisenring.core.iterators;

import java.util.AbstractCollection;
import java.util.Iterator;

import ch.eisenring.core.collections.Collection;
import ch.eisenring.core.collections.SizingPolicy;

/**
 * Read-Only collection wrapper that contains only the subset of the
 * underlying collection that is accepted by the filter.
 */
public final class FilterCollection<T> extends AbstractCollection<T> implements Collection<T> {

	private final Collection<T> baseCollection;
	private final Filter<T> filter;
	
	@SuppressWarnings("unchecked")
	public FilterCollection(final Collection<T> baseCollection, final Filter<T> filter) {
		this.baseCollection = baseCollection;
		this.filter = filter == null ? (Filter<T>) Filter.ACCEPT : filter;
	}

	// --------------------------------------------------------------
	// ---
	// --- Collection implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public boolean add(final T e) {
		throw new UnsupportedOperationException("read-only");
	}

	@Override
	public boolean addAll(final java.util.Collection<? extends T> collection) {
		throw new UnsupportedOperationException("read-only");
	}

	@Override
	public void clear() {
		throw new UnsupportedOperationException("read-only");
	}

	@SuppressWarnings("unchecked")
	@Override
	public boolean contains(final Object o) {
		if (!baseCollection.contains(o))
			return false;
		return filter.accepts((T) o);
	}

	@Override
	public boolean isEmpty() {
		return baseCollection.isEmpty();
	}

	@Override
	public int size() {
		return baseCollection.size();
	}

	@Override
	public Iterator<T> iterator() {
		return new FilterIterator<T>(baseCollection.iterator(), filter);
	}

	// --------------------------------------------------------------
	// ---
	// --- Extended collection API
	// ---
	// --------------------------------------------------------------
	@Override
	public void ensureCapacity(int capacity) {
		// NO-OP
	}

	@Override
	public void trimToSize() {
		baseCollection.trimToSize();
	}

	@Override
	public void setPolicy(SizingPolicy policy) {
		// NO-OP
	}

}

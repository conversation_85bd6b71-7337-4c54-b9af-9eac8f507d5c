package ch.eisenring.core.platform;

import java.io.File;

import javax.swing.filechooser.FileSystemView;

class PlatformGeneric extends Platform {

	protected PlatformGeneric(final String platformName) {
		super(platformName);
	}
	
	private PlatformFileUtil GENERIC;

	@Override
	public PlatformFileUtil getFileUtil() {
		if (GENERIC == null)
			GENERIC = new PlatformFileUtilGeneric();
		return GENERIC;
	}

	// --------------------------------------------------------------
	// ---
	// --- Platform specific paths
	// ---
	// --------------------------------------------------------------
	@Override
	public File getPath(final PlatformPath platformPath) {
		final FileSystemView fsv = FileSystemView.getFileSystemView();
		switch (platformPath) {
			case APP_TEMP: {
				final File tempSys = getPath(PlatformPath.SYS_TEMP);
				final String subpath = TEMP_SUB_PATH.get();
				if (subpath == null || subpath.length() <= 0)
					return tempSys;
				final File tempApp = new File(tempSys, subpath);
				tempApp.mkdirs();
				return tempApp;	
			}
			case SYS_TEMP: {
				final String path = System.getProperty("java.io.tmpdir");
				final File result = new File(path);
				result.mkdirs();
				return result;
			}
			case SETTINGS:
			case HOME:
				return fsv.getHomeDirectory();
			case DOCUMENTS:
			case DESKTOP:
			default:
				return fsv.getDefaultDirectory();
		}
	}

	@Override
	public void shutdown() {
		// Use secure ProcessBuilder approach instead of deprecated Runtime.exec(String)
		execSecure("shutdown", "-h", "now");
	}

	@Override
	public void reboot() {
		// Use secure ProcessBuilder approach instead of deprecated Runtime.exec(String)
		execSecure("reboot", "-i");
	}

	@Override
	public PlatformType getType() {
		return PlatformType.UNKNOWN;
	}

}

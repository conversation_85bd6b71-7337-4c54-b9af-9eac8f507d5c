package ch.eisenring.core.platform;

import java.awt.Desktop;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.lang.reflect.Method;
import java.net.URI;

import ch.eisenring.core.datatypes.primitives.Primitives;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.logging.Logger;

class PlatformWindows extends PlatformGeneric {

	private final static PlatformFileUtil FILE_UTIL = new PlatformFileUtilWindows();
	
	protected PlatformWindows(final String platformName) {
		super(platformName);
	}

	@Override
	public PlatformFileUtil getFileUtil() {
		return FILE_UTIL;
	}

	// --------------------------------------------------------------
	// ---
	// --- User desktop path
	// ---
	// --------------------------------------------------------------
	@Override
	public File getPath(final PlatformPath platformPath) {
		switch (platformPath) {
			default:
				return super.getPath(platformPath);
			case DESKTOP:
				return getUserDesktopPathImpl();
			case SETTINGS:
				return getUserSettingsPathImpl();
		}
	}

	protected File getUserDesktopPathImpl() {
		try {
			final String userHome = System.getProperty("user.home");
			if (!Strings.isEmpty(userHome)) {
				for (final String possiblePath : DESKTOP_PATH_POSSIBILITIES) {
					final File path = new File(userHome, possiblePath);
					if (path.exists() && path.isDirectory()) {
						return path.getAbsoluteFile();
					}
				}
			}
		} catch (final Throwable t) {
			// not that we really care
		}
		// fall back to standard
		return super.getPath(PlatformPath.DOCUMENTS);
	}

	private final static String[] DESKTOP_PATH_POSSIBILITIES = {
		"Desktop_Client",
		"Desktop",
		"..\\Desktop"
	};

	protected final File getUserSettingsPathImpl() {
		// first try to read the environment variable APPDATA,
		// which should point to the correct path on recent
		// windows versions.
		try {
			return getAppdataFolder();
		} catch (final Throwable t) {
			// not that we really care, ignore
		}
		
		// if that didn't work out, use a hardcoded path in
		// the users home directory
		try {
			final String userHome = System.getProperty("user.home");
			if (!Strings.isEmpty(userHome)) {
				final File path = new File(userHome, "Anwendungsdaten");
				if (path.exists() && path.isDirectory()) {
					return path.getAbsoluteFile();
				}
			}
		} catch (final Throwable t) {
			// not that we really care
			Logger.error(Strings.concat("System.getProperty(\"user.home\") failed: ", t.getMessage()));
			Logger.error(t);
		}

		// use generic default:
		return super.getPath(PlatformPath.SETTINGS);
	}
	
	// --------------------------------------------------------------
	// ---
	// --- System control
	// ---
	// --------------------------------------------------------------
	@Override
	public void shutdown() {
		// Use secure ProcessBuilder approach instead of deprecated Runtime.exec(String)
		execSecure("shutdown.exe", "-s", "-t", "0");
	}

	@Override
	public void reboot() {
		// Use secure ProcessBuilder approach instead of deprecated Runtime.exec(String)
		execSecure("shutdown.exe", "-r", "-t", "0");
	}

	public void openFile(final String path) {
		boolean useCmdLineHack = true;
		if (Desktop.isDesktopSupported()) {
			try {
				final Desktop desktop = Desktop.getDesktop();
				if (Strings.contains(path, "://")) {
					if (desktop.isSupported(Desktop.Action.BROWSE)) {
						final URI uri = new URI(path);
						desktop.browse(uri);
						useCmdLineHack = false;
						if (Logger.isDebugEnabled())
							Logger.debug(Strings.concat("browsing URL (", path, ") using java.awt.Desktop::browse()"));
					}
				} else {
					if (desktop.isSupported(Desktop.Action.OPEN)) {
						File file = new File(path);
						file = file.getCanonicalFile();
						desktop.open(file);
						useCmdLineHack = false;
						if (Logger.isDebugEnabled())
							Logger.debug(Strings.concat("opened file (", path, ") using java.awt.Desktop::open()"));
					}
				}
			} catch (final Exception e) {
				Logger.warn(Strings.concat("java.awt.Desktop::open() failed: ", e.getMessage()));
				Logger.warn(e);
			}
		}
		if (useCmdLineHack) {
			Logger.warn("Using using Runtime.exec() hack as fallback to open file");
			try {
				// let the operating system deal with showing the file
				final String[] cmd = { "rundll32", "url.dll,FileProtocolHandler", path };
				final ProcessBuilder pb = new ProcessBuilder(cmd);
				pb.start();
			} catch (final IOException e) {
				Logger.error(Strings.concat("error viewing file: ", path));
				Logger.error(e);
			}
		}
	}

	@Override
	public PlatformType getType() {
		return PlatformType.WINDOWS;
	}

	/**
	 * Windows specific: Locate SendTo folder of current user.
	 * This call may throw UnsupportedOperationException or
	 * FileNotFoundException, depending on OS version and
	 * folder location. 
	 */
	public File getSendToFolder() throws UnsupportedOperationException, IOException {
		File appData = getAppdataFolder();
		// with WindowsXP/2003 the folder is located one directory up and named "SendTo",
		// try this one first.
		try {
			final File xpPath = new File(appData.getParentFile(), "SendTo");
			if (xpPath.exists() && xpPath.isDirectory())
				return xpPath;
		} catch (final Throwable e) {
			// ignore
		}
		// In Windows Vista/7, the folder is located in "%APPDATA%\Microsoft\Windows\SendTo".
		File w7Path = new File(appData, "Microsoft");
		w7Path = new File(w7Path, "Windows");
		w7Path = new File(w7Path, "SendTo");
		if (w7Path.exists() && w7Path.isDirectory())
			return w7Path;
		throw new FileNotFoundException("Can't locate SendTo-Folder (Windows Version not supported or Folder missing)");
	}

	/**
	 * Locates the APPDATA folder. Throws if it can't be located.
	 */
	public File getAppdataFolder() throws IOException {
		try {
			final String result = System.getenv("APPDATA");
			if (Strings.isEmpty(result))
				throw new FileNotFoundException("Can't locate APPDATA folder (environment variable not set)");
			File path = new File(result);
			path = path.getCanonicalFile();
			if (path.exists() && path.isDirectory())
				return path;
			throw new FileNotFoundException("Can't locate APPDATA folder (Folder does not exist?)");
		} catch (final IOException e) {
			throw new FileNotFoundException(Strings.concat("Can't locate APPDATA folder (", e.getMessage(), ")"));			
		}
	}

	// --------------------------------------------------------------
	// ---
	// --- Implementation of replaceCLIVars (Windows-specific)
	// ---
	// --------------------------------------------------------------
	@Override
	public String replaceCLIVars(final CharSequence path) {
		if (Strings.isEmpty(path))
			return Strings.toString(path);
		final StringMaker result = StringMaker.obtain(path.length());
		final StringMaker varname = StringMaker.obtain();
		final int length = path.length();
		boolean inQuote = false;
		for (int i=0; i<length; ++i) {
			final char c = path.charAt(i);
			if (inQuote) {
				if (c == '%') {
					final String value = resolveCMDVar(varname);
					result.append(value);
					varname.setLength(0);
					inQuote = false;
				} else {
					varname.append(c);
				}
			} else {
				if (c == '%') {
					inQuote = true;
				} else {
					result.append(c);
				}
			}
		}
		varname.releaseSilent();
		return result.release();
	}

	private static String resolveCMDVar(final CharSequence name) {
		final String name2 = Strings.toString(name);
		do {
			if (Strings.isEmpty(name))
				break;
			try {
				final String value = System.getenv(name2);
				if (value != null)
					return value;
			} catch (final Exception e) {
				// can't resolve, leave as is
			}
		} while (false);
		return Strings.concat("%", name2, "%");
	}

	// --------------------------------------------------------------
	// ---
	// --- Testing
	// ---
	// --------------------------------------------------------------
	public static void main(String[] argv) {
		final Platform platform = Platform.getPlatform();
		System.out.println(platform);
		final PlatformPath[] pathes = PlatformPath.values();
		for (final PlatformPath path : pathes) {
			final File filePath = platform.getPath(path);
			System.out.println(path.name() + " = " + filePath.getAbsolutePath());
		}
	}

}

package ch.eisenring.core.platform;

import java.awt.Desktop;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.net.URI;
import java.util.Enumeration;
import java.util.concurrent.atomic.AtomicReference;

import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.datatypes.strings.format.api.Formats;
import ch.eisenring.core.io.Streams;
import ch.eisenring.core.io.binary.BinaryHolder;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.core.threading.ThreadPool;
import ch.eisenring.core.util.file.FileUtil;

/**
 * Represents the OS Platform the application is running on.
 * 
 * This contains methods to deal with OS specific stuff, like
 * the users settings path etc.
 */
public abstract class Platform {

	protected final static AtomicReference<String> TEMP_SUB_PATH = new AtomicReference<String>((String) null);
	
	protected final String platformName;
	
	/**
	 * Gets the platform the application is running on
	 */
	public final static Platform getPlatform() {
		return defaultPlatform;
	}

	
	private final static Platform defaultPlatform = PlatformDetector.getPlatformImpl();

	protected Platform(final String platformName) {
		this.platformName = platformName;
	}

	/**
	 * Gets the platform name
	 */
	public String getPlatformName() {
		return platformName;
	}
	
	/**
	 * Gets a platform specific path
	 */
	public abstract File getPath(final PlatformPath platformPath);

	/**
	 * Converts a file system path to platform specific representation
	 * (e.g. replaces slashes with backslashes on windows)
	 */
	public String toPlatformSpecificPath(final CharSequence path) {
		// default implementation, specific platforms may overwrite
		// this with a more specific conversion.
		if (path == null)
			return null;
		final StringMaker maker = StringMaker.obtain();
		maker.append(path);
		if (File.separatorChar != '/')
			maker.replace('/', File.separatorChar);
		if (File.separatorChar != '\\')
			maker.replace('\\', File.separatorChar);
		return maker.release();
	}
	
	/**
	 * Converts a file system path to a platform independent representation
	 * (e.g. replaces backslashes with slashes on windows)
	 * Not all pathes can be made completely platform independent.
	 */
	public String toPlatformIndependentPath(final CharSequence path) {
		// default implementation, specific platforms may overwrite
		// this with a more specific conversion.
		if (path == null)
			return null;
		final StringMaker maker = StringMaker.obtain();
		maker.append(path);
		maker.replace(File.separatorChar, '/');
		return maker.release();
	}

	/**
	 * Attempts to shut down the computer.
	 * May or may not return (giving no indication if it will shut down or not)
	 */
	public abstract void shutdown();

	/**
	 * Attempts to reboot the computer.
	 * May or may not return (giving no indication if it will shut down or not)
	 */
	public abstract void reboot();

	/**
	 * Gets the current users name login name.
	 * Returns null if the logged in user can't be determined.
	 */
	public String getLoginName() {
		try {
			final String userName = System.getProperty("user.name", null);
			return userName;
		} catch (final Throwable t) {
			return null;
		}
	}

	/**
	 * Gets the current users id. The id representation is platform specific.
	 * 
	 * This may either be the user name, or some id identifying the user on the
	 * current platform.
	 */
	public String getLoginId() {
		final String id = getLoginName();
		return id == null ? "" : id;
	}
	
	/**
	 * Executes a platform command using ProcessBuilder (secure, Java 21 compatible).
	 * @param command The command string to execute
	 * @return true if command was started successfully, false otherwise
	 * @deprecated Use execSecure(String...) instead for better security
	 */
	@Deprecated
	protected final boolean exec(final String command) {
		try {
			if (Logger.isTraceEnabled())
				Logger.trace(Strings.concat("executing platform command: \"", command, "\""));

			// Parse command string into array for ProcessBuilder (more secure)
			String[] commandArray = parseCommand(command);
			return execSecure(commandArray);
		} catch (final Exception e) {
			Logger.error(e);
			return false;
		}
	}

	/**
	 * Executes a platform command using ProcessBuilder (secure, Java 21 compatible).
	 * @param command The command and arguments as separate strings
	 * @return true if command was started successfully, false otherwise
	 */
	protected final boolean execSecure(final String... command) {
		try {
			if (Logger.isTraceEnabled()) {
				StringBuilder sb = new StringBuilder("executing platform command: [");
				for (int i = 0; i < command.length; i++) {
					if (i > 0) sb.append(", ");
					sb.append("\"").append(command[i]).append("\"");
				}
				sb.append("]");
				Logger.trace(sb.toString());
			}

			ProcessBuilder pb = new ProcessBuilder(command);
			pb.start(); // Start process but don't wait for completion
			return true;
		} catch (final IOException e) {
			Logger.error(e);
			return false;
		}
	}

	/**
	 * Simple command parser that splits on spaces (basic implementation).
	 * For more complex commands, override this method in platform-specific classes.
	 */
	protected String[] parseCommand(String command) {
		if (command == null || command.trim().isEmpty()) {
			return new String[0];
		}
		// Simple split on spaces - platform-specific classes can override for more complex parsing
		return command.trim().split("\\s+");
	}

	@Override
	public String toString() {
		return getPlatformName();
	}

	/**
	 * Gets the local host name. If the name can't be determined, the IP
	 * is returned. If the IP can't be determined either, "localhost" is returned.
	 */
	public String getLocalHostName() {
		while (true) {
			String result = localHostNameCache.get();
			if (result != null)
				return result;
			result = getLocalHostNameImpl();
			if (localHostNameCache.compareAndSet(null, result))
				return result;
		}
	}

	/**
	 * Caches the local host name
	 */
	private AtomicReference<String> localHostNameCache = new AtomicReference<>();

	/**
	 * Implements actually getting the local host name.
	 */
	private String getLocalHostNameImpl() {
		try {
			InetAddress bestMatch = null;
			final Enumeration<NetworkInterface> nets = NetworkInterface.getNetworkInterfaces();
			while (nets.hasMoreElements()) {
				final NetworkInterface net = nets.nextElement();
			    final Enumeration<InetAddress> adrs = net.getInetAddresses();
			    while (adrs.hasMoreElements()) {
			    	final InetAddress adr = adrs.nextElement();
			    	if (adr.isLoopbackAddress()) {
			    		// ignore loopback
			    		continue;
			    	}
			    	final String hostName = adr.getHostName();
			    	final String hostAddr = adr.getHostAddress();
			    	if (Strings.equals(hostName, hostAddr)) {
			    		if (bestMatch == null) {
			    			bestMatch = adr;
			    		}
			    	} else {
			    		bestMatch = adr;
			    		break;
			    	}
		        }
		    }
			if (bestMatch != null) {
				return bestMatch.getHostName();
			}
		} catch (final SocketException e) {
			// intentionally ignored
		}
		return "localhost";
	}

	public final void openFile(final String path, final boolean async) {
		if (async) {
			final Runnable r = new Runnable() {
				@Override
				public void run() {
					openFile(path);
				}
			};
			ThreadPool.DEFAULT.start(r, "Platform.openFile()", Thread.NORM_PRIORITY);
		} else {
			openFile(path);
		}
	}

	public final void editFile(final String path, final boolean async) {
		if (async) {
			final Runnable task = new Runnable() {
				@Override
				public void run() {
					editFile(path);
				}
			};
			ThreadPool.DEFAULT.startDaemon(task, "Platform.editFile()");
			return;
		} else {
			editFile(path);
		}
	}

	public final void printFile(final String path, final boolean async) {
		if (async) {
			final Runnable task = new Runnable() {
				@Override
				public void run() {
					printFile(path);
				}
			};
			ThreadPool.DEFAULT.startDaemon(task, "Platform.printFile()");
			return;
		} else {
			openFile(path);
		}
	}

	public void openFile(final String path) {
		if (Desktop.isDesktopSupported()) {
			try {
				final Desktop desktop = Desktop.getDesktop();
				if (Strings.contains(path, "://")) {
					if (desktop.isSupported(Desktop.Action.BROWSE)) {
						final URI uri = new URI(path);
						desktop.browse(uri);
						if (Logger.isDebugEnabled())
							Logger.debug(Strings.concat("browsing URL (", path, ") using java.awt.Desktop::browse()"));
					}
				} else {
					if (desktop.isSupported(Desktop.Action.OPEN)) {
						final File file = new File(path);
						desktop.open(file);
						if (Logger.isDebugEnabled())
							Logger.debug(Strings.concat("opened file (", path, ") using java.awt.Desktop::open()"));
					}
				}
			} catch (final Exception e) {
				Logger.warn(Strings.concat("java.awt.Desktop::open() failed: ", e.getMessage()));
				Logger.warn(e);
			}
		}
	}

	public void editFile(final String path) {
		if (Desktop.isDesktopSupported()) {
			try {
				final Desktop desktop = Desktop.getDesktop();
				if (desktop.isSupported(Desktop.Action.EDIT)) {
					final File file = new File(path);
					desktop.open(file);
					if (Logger.isDebugEnabled())
						Logger.debug(Strings.concat("edited file (", path, ") using java.awt.Desktop::open()"));
				}
			} catch (final Exception e) {
				Logger.warn(Strings.concat("java.awt.Desktop::edit() failed: ", e.getMessage()));
				Logger.warn(e);
			}
		}
	}

	/**
	 * Sends the file identified by path name to the system default printer
	 */
	public void printFile(final String path) {
		if (Desktop.isDesktopSupported()) {
			try {
				final Desktop desktop = Desktop.getDesktop();
				if (desktop.isSupported(Desktop.Action.PRINT)) {
					final File file = new File(path);
					desktop.print(file);
					if (Logger.isDebugEnabled())
						Logger.debug(Strings.concat("printed file (", path, ") using java.awt.Desktop::print()"));
				}
			} catch (final Exception e) {
				Logger.warn(Strings.concat("java.awt.Desktop::print() failed: ", e.getMessage()));
				Logger.warn(e);
			}
		}
	}

	/**
	 * Creates a temporary file from binary data.
	 * Return the file where the file was written.
	 */
	public File createTempFile(final BinaryHolder binary,
			                   final String filename,
			                   final boolean deleteOnExit) {
		File file = new File(toPlatformSpecificPath(filename));
		final StringMaker maker = StringMaker.obtain();
		maker.append(getPath(PlatformPath.APP_TEMP).getAbsolutePath());
		maker.append(File.separatorChar);
		maker.append(file.getName(), Formats.STRIP_FILE_EXTENSION);
		maker.append('_');
		maker.append(FileUtil.getUniqueFileId());
		maker.append(file.getName(), Formats.FILE_EXTENSION);
		file = new File(maker.release());
		InputStream input = null;
		OutputStream output = null;
		try {
			input = binary.getInputStream();
			output = new FileOutputStream(file);
			Streams.copy(input, output, binary.size());
			if (deleteOnExit)
				file.deleteOnExit();
			return file;
		} catch (final IOException e) {
			return null;
		} finally {
			Streams.closeSilent(output);
			Streams.closeSilent(input);
		}
	}

	/**
	 * Roughly classifies the platform
	 */
	public abstract PlatformType getType();

	// --------------------------------------------------------------
	// ---
	// --- Temp path / file management
	// ---
	// --------------------------------------------------------------
	/**
	 * Sets the sub path name to be used within system temp dir
	 * (defaults to NULL = no subdir).
	 * This sub path is used to create PlatformPath.APP_TEMP
	 */
	public static void setTempSubPath(final String subpath) {
		TEMP_SUB_PATH.set(subpath);
	}

	/**
	 * Replaces platform specific variables in path. This is a platform dependent
	 * function, on windows platforms it will replace variables like the cmd would.
	 * 
	 * If the platform does not support replacement, it will return the input unchanged.
	 */
	public String replaceCLIVars(final CharSequence path) {
		return Strings.toString(path);
	}

	/**
	 * Sanitizes file name to match the platform limits, this may replace
	 * characters not allowed by the current platform and/or shorten the file
	 * name to adhere to file name limits.
	 */
	public abstract PlatformFileUtil getFileUtil();

}

package ch.eisenring.core.platform;


final class PlatformMacOSX extends PlatformGeneric {

	protected PlatformMacOSX(final String platformName) {
		super(platformName);
	}

	@Override
	public void shutdown() {
		// Use secure ProcessBuilder approach instead of deprecated Runtime.exec(String)
		execSecure("shutdown", "-h", "now");
	}

	@Override
	public void reboot() {
		// Use secure ProcessBuilder approach instead of deprecated Runtime.exec(String)
		execSecure("reboot", "-i");
	}

	@Override
	public PlatformType getType() {
		return PlatformType.MAC;
	}

}

package ch.eisenring.core.platform;

import ch.eisenring.core.collections.Set;
import ch.eisenring.core.datatypes.strings.Strings;

/**
 * Implements building the default platform instance
 */
class PlatformDetector {

    /**
     * Windows versions supported by generic PlatformWindows
     */
    private final static Set<String> WINDOWS_GENERIC_VERSIONS = Set.asReadonlySet(
            "Windows 95",
            "Windows 98",
            "Windows Me",
            "Windows NT",
            "Windows 2000",
            "Windows 2003",
            "Windows XP",
            "Windows Vista",
            "Windows 7");

    /**
     * Windows versions supported by PlatformWindows8
     */
    private final static Set<String> WINDOWS_8_VERSIONS = Set.asReadonlySet(
            "Windows 8",
            "Windows 8.1");

    static Platform getPlatformImpl() {
        final Platform result;
        final String osName = System.getProperty("os.name");
        if (Strings.startsWithIgnoreCase(osName, "Windows")) {
            result = new PlatformWindows(Strings.concat("Windows (", osName, ")"));
        } else if (Strings.startsWithIgnoreCase(osName, "mac os x")) {
            result = new PlatformMacOSX("Apple MacOSX");
        } else {
            result = new PlatformGeneric(Strings.concat("Unknown OS (", osName, ")"));
        }
        return result;
    }

}

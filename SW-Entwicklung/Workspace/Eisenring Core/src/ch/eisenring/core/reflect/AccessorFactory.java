package ch.eisenring.core.reflect;

import java.lang.reflect.Constructor;
import java.lang.reflect.Field;

import ch.eisenring.core.collections.Lookup;
import ch.eisenring.core.collections.impl.AtomicArrayLookup;

/**
 * Wrapper class to make use of sun.misc.Unsafe in a way transparent
 * to the client code (if no unsafe is available, alternate implementations
 * take over the job).
 */
public final class AccessorFactory {

	private static Constructor<FieldAccessor> ACCESSOR_CONSTRUCTOR = getAccessorConstructor();

	@SuppressWarnings("unchecked")
	private static Constructor<FieldAccessor> getAccessorConstructor() {
		// determine the type of accessor to be used on this VM
		// (this is a little tricky, since for best performance we must only load a single implementing class)
		Class<?> accessorClass = null;

		// Check if we're running on Java 21 or later - use reflection only
		if (UnsafeProvider.isJava21OrLater()) {
			// For Java 21+, use reflection-based accessor due to module restrictions
			accessorClass = FieldAccessorReflect.class;
		} else if (UnsafeProvider.isJava9OrLater()) {
			try {
				// Use VarHandle-based accessor for Java 9-20
				accessorClass = FieldAccessorVarHandle.class;
			} catch (final Throwable t) {
				// ignore any errors here (will use fallback)
				accessorClass = null;
			}
		} else if (UnsafeProvider.getUnsafe() != null) {
			try {
				// use the unsafe accessor for Java 8 only
				accessorClass = FieldAccessorUnsafe.class;
			} catch (final Throwable t) {
				// ignore any errors here (will use fallback)
				accessorClass = null;
			}
		}

		// fallback solution: use reflection
		if (accessorClass == null) {
			accessorClass = FieldAccessorReflect.class;
		}

		// Log which accessor is being used for debugging
		String accessorName = accessorClass.getSimpleName();
		String javaVersion = System.getProperty("java.version");
		System.out.println("AccessorFactory: Using " + accessorName + " for Java " + javaVersion);

		try {
			return (Constructor<FieldAccessor>) accessorClass.getDeclaredConstructor(Field.class);
		} catch (final Exception e) {
			throw new ExceptionInInitializerError(e);
		}
	}

	private final static Lookup<Field, FieldAccessor> ACCESSOR_CACHE =
			new AtomicArrayLookup<Field, FieldAccessor>();

	/**
	 * Gets a field accessor for the specified field, if possible it will
	 * use direct field access. If not, reflection will be used.
	 */
	public static FieldAccessor getFieldAccessor(final Field field) {
		FieldAccessor result = ACCESSOR_CACHE.get(field);
		if (result != null)
			return result;

		// First try the preferred accessor type
		try {
			result = (FieldAccessor) ACCESSOR_CONSTRUCTOR.newInstance(field);
			ACCESSOR_CACHE.put(field, result);
			return result;
		} catch (final Exception e) {
			// If the preferred accessor fails, try the fallback accessor
			try {
				// Always fall back to reflection-based accessor if the preferred one fails
				result = new FieldAccessorReflect(field);
				ACCESSOR_CACHE.put(field, result);
				return result;
			} catch (final Exception e2) {
				// If even the fallback fails, throw an exception
				throw new RuntimeException("Failed to create field accessor for " + field, e);
			}
		}
	}

}

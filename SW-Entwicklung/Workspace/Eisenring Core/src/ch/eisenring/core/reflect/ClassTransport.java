package ch.eisenring.core.reflect;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Constructor;

import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.io.StreamReader;
import ch.eisenring.core.io.StreamWriter;
import ch.eisenring.core.io.Streamable;
import ch.eisenring.core.io.Streams;
import ch.eisenring.core.logging.Logger;

/**
 * Transport object for moving a class over network
 */
public final class ClassTransport implements Streamable {

	private String name;
	private byte[] data;

	private ClassTransport() {
	}

	// --------------------------------------------------------------
	// ---
	// --- Factory methods
	// ---
	// --------------------------------------------------------------
	/**
	 * Transforms the given class into a transport object.
	 * 
	 * Be wary that to get the class for a class the class actually needs to be
	 * pulled into the VM, meaning problems will occur if dependencies are missing.
	 */
	public final static ClassTransport create(final Class<?> theClass) throws ClassNotFoundException {
		return create(theClass.getName(), theClass.getClassLoader());
	}

	/**
	 * Transforms the class identified by name (e.g. "foo.bar.MyClass") into a transport object,
	 * using the supplied class loader. If class loader is null, a default class loader is substituted.
	 * 
	 * If using this method, the class is not loaded into the VM as a class, thus using this
	 * method even classes with missing dependencies can be loaded.
	 */
	public final static ClassTransport create(final String name, final ClassLoader classLoader) throws ClassNotFoundException {
		final ClassTransport result = new ClassTransport();
		result.name = name;
		final String resourceName = name.replace('.', '/') + ".class";
		final ClassLoader cl = classLoader == null ? ClassTransport.class.getClassLoader() : classLoader;
		try (final InputStream in = cl.getResourceAsStream(resourceName)) {
			final ByteArrayOutputStream out = new ByteArrayOutputStream();
			Streams.copyFully(in, out, Integer.MAX_VALUE);
			out.close();
			result.data = out.toByteArray();
		} catch (final IOException e) {
			throw new ClassNotFoundException(name);
		}
		return result;
	}

	// --------------------------------------------------------------
	// ---
	// --- API
	// ---
	// --------------------------------------------------------------
	/**
	 * Gets the qualified name of the encapsulated class (e.g. "foo.bar.MyClass")
	 */
	public String getClassName() {
		return name;
	}

	/**
	 * Gets the class file equivalent as a byte[]
	 */
	public byte[] getClassData() {
		return data;
	}

	/**
	 * Attempts to load the encapsulated class.
	 */
	public Class<?> getClassClass() throws ClassNotFoundException {
		final BinaryClassLoader classLoader = BinaryClassLoader.getDefaultInstance();
		classLoader.addClass(getClassName(), getClassData());
		final Class<?> theClass = Class.forName(getClassName(), false, classLoader);
		return theClass;
	}

	/**
	 * Attempts to load the encapsulated class and creates an instance
	 * using the default constructor.
	 */
	@SuppressWarnings("deprecation")
	public Object getInstance(final Object ... args) throws ClassNotFoundException, IllegalAccessException, InstantiationException {
		final Class<?> theClass = getClassClass();
		if (args == null || args.length == 0) {
			try {
				return theClass.getDeclaredConstructor().newInstance();
			} catch (Exception e) {
				// TODO: Fallback to deprecated method for compatibility when moving to Java 21 completely
				return theClass.newInstance();
			}
		} else {
			final Constructor<?>[] constructors = theClass.getDeclaredConstructors();
			for (final Constructor<?> c : constructors) {
				try {
					if (c.getParameterTypes().length == args.length) {
						c.setAccessible(true);
						return c.newInstance(args);
					}
				} catch (final Exception e) {
					Logger.warn(e); 
					// ignore
				}
			}
		}
		throw new IllegalArgumentException("no suitable constructor found");
	}

	// --------------------------------------------------------------
	// ---
	// --- Streamable implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public void read(final StreamReader reader) throws IOException {
		name = reader.readString();
		data = reader.readBinary();
	}

	@Override
	public void write(final StreamWriter writer) throws IOException {
		writer.writeString(name);
		writer.writeBinary(data);
	}

	// --------------------------------------------------------------
	// ---
	// --- Object overrides
	// ---
	// --------------------------------------------------------------
	@Override
	public String toString() {
		return Strings.concat(name, "[", data.length, "]");
	}

}

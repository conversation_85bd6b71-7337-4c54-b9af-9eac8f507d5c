package ch.eisenring.core.reflect;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;

import ch.eisenring.core.util.ReflectionUtil;

/**
 * Field accessor using VarHandle for direct access.
 * This is the Java 9+ replacement for FieldAccessorUnsafe.
 *
 * This class uses reflection to access VarHandle functionality
 * to maintain compatibility with Java 8.
 */
final class FieldAccessorVarHandle extends FieldAccessor {

    private final Object varHandle;
    private final Method getMethod;
    private final Method setMethod;

    FieldAccessorVarHandle(final Field field) {
        super(field);
        try {
            // Make the field accessible - Java 21 compatible
            if (!ReflectionUtil.isFieldAccessible(field) && !Modifier.isStatic(field.getModifiers())) {
                field.setAccessible(true);
            }

            // Get the MethodHandles class
            Class<?> methodHandlesClass = Class.forName("java.lang.invoke.MethodHandles");

            // Get the Lookup class
            Class<?> lookupClass = Class.forName("java.lang.invoke.MethodHandles$Lookup");

            // Get the VarHandle class
            Class<?> varHandleClass = Class.forName("java.lang.invoke.VarHandle");

            // Get the lookup() method
            Method lookupMethod = methodHandlesClass.getMethod("lookup");

            // Get the privateLookupIn method (Java 9+)
            Method privateLookupInMethod = methodHandlesClass.getMethod("privateLookupIn",
                    Class.class, lookupClass);

            // Get the unreflectVarHandle method
            Method unreflectVarHandleMethod = lookupClass.getMethod("unreflectVarHandle", Field.class);

            // Create a lookup object
            Object lookup = lookupMethod.invoke(null);

            // Create a private lookup
            Object privateLookup = privateLookupInMethod.invoke(null, field.getDeclaringClass(), lookup);

            // Create a VarHandle for the field
            varHandle = unreflectVarHandleMethod.invoke(privateLookup, field);

            // Get the get and set methods from VarHandle
            // VarHandle methods take variable arguments, not an array
            getMethod = varHandleClass.getMethod("get", Object.class);
            setMethod = varHandleClass.getMethod("set", Object.class, Object.class);

        } catch (Exception e) {
            throw new RuntimeException("Failed to create VarHandle for field: " + field, e);
        }
    }

    @Override
    public Object get(final Object object) {
        try {
            // Call get with the object as a parameter
            return getMethod.invoke(varHandle, object);
        } catch (Exception e) {
            throw new RuntimeException("Failed to get field value", e);
        }
    }

    @Override
    public void set(final Object object, final Object value) {
        try {
            // Call set with the object and value as parameters
            setMethod.invoke(varHandle, object, value);
        } catch (Exception e) {
            throw new RuntimeException("Failed to set field value", e);
        }
    }

    // --------------------------------------------------------------
    // ---
    // --- Direct primitive access
    // ---
    // --------------------------------------------------------------
    @Override
    public boolean getBoolean(final Object object) {
        try {
            return (boolean) getMethod.invoke(varHandle, object);
        } catch (Exception e) {
            throw new RuntimeException("Failed to get boolean field value", e);
        }
    }

    @Override
    public byte getByte(final Object object) {
        try {
            return (byte) getMethod.invoke(varHandle, object);
        } catch (Exception e) {
            throw new RuntimeException("Failed to get byte field value", e);
        }
    }

    @Override
    public short getShort(final Object object) {
        try {
            return (short) getMethod.invoke(varHandle, object);
        } catch (Exception e) {
            throw new RuntimeException("Failed to get short field value", e);
        }
    }

    @Override
    public char getChar(final Object object) {
        try {
            return (char) getMethod.invoke(varHandle, object);
        } catch (Exception e) {
            throw new RuntimeException("Failed to get char field value", e);
        }
    }

    @Override
    public int getInt(final Object object) {
        try {
            return (int) getMethod.invoke(varHandle, object);
        } catch (Exception e) {
            throw new RuntimeException("Failed to get int field value", e);
        }
    }

    @Override
    public long getLong(final Object object) {
        try {
            return (long) getMethod.invoke(varHandle, object);
        } catch (Exception e) {
            throw new RuntimeException("Failed to get long field value", e);
        }
    }

    @Override
    public float getFloat(final Object object) {
        try {
            return (float) getMethod.invoke(varHandle, object);
        } catch (Exception e) {
            throw new RuntimeException("Failed to get float field value", e);
        }
    }

    @Override
    public double getDouble(final Object object) {
        try {
            return (double) getMethod.invoke(varHandle, object);
        } catch (Exception e) {
            throw new RuntimeException("Failed to get double field value", e);
        }
    }

    @Override
    public void setBoolean(final Object object, final boolean value) {
        try {
            setMethod.invoke(varHandle, object, value);
        } catch (Exception e) {
            throw new RuntimeException("Failed to set boolean field value", e);
        }
    }

    @Override
    public void setByte(final Object object, final byte value) {
        try {
            setMethod.invoke(varHandle, object, value);
        } catch (Exception e) {
            throw new RuntimeException("Failed to set byte field value", e);
        }
    }

    @Override
    public void setShort(final Object object, final short value) {
        try {
            setMethod.invoke(varHandle, object, value);
        } catch (Exception e) {
            throw new RuntimeException("Failed to set short field value", e);
        }
    }

    @Override
    public void setChar(final Object object, final char value) {
        try {
            setMethod.invoke(varHandle, object, value);
        } catch (Exception e) {
            throw new RuntimeException("Failed to set char field value", e);
        }
    }

    @Override
    public void setInt(final Object object, final int value) {
        try {
            setMethod.invoke(varHandle, object, value);
        } catch (Exception e) {
            throw new RuntimeException("Failed to set int field value", e);
        }
    }

    @Override
    public void setLong(final Object object, final long value) {
        try {
            setMethod.invoke(varHandle, object, value);
        } catch (Exception e) {
            throw new RuntimeException("Failed to set long field value", e);
        }
    }

    @Override
    public void setFloat(final Object object, final float value) {
        try {
            setMethod.invoke(varHandle, object, value);
        } catch (Exception e) {
            throw new RuntimeException("Failed to set float field value", e);
        }
    }

    @Override
    public void setDouble(final Object object, final double value) {
        try {
            setMethod.invoke(varHandle, object, value);
        } catch (Exception e) {
            throw new RuntimeException("Failed to set double field value", e);
        }
    }

}

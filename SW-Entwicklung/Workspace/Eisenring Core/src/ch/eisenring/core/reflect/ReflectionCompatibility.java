package ch.eisenring.core.reflect;

import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;

/**
 * Utility class for Java version compatibility in reflection operations.
 * Handles the transition from deprecated isAccessible() to modern canAccess() methods.
 * 
 * This class provides compatibility between Java 8 and Java 21+ by:
 * - Using canAccess() for Java 9+ (modern approach)
 * - Falling back to isAccessible() for Java 8 (legacy support)
 */
public final class ReflectionCompatibility {

    private ReflectionCompatibility() {
        // Utility class - no instances
    }

    /**
     * Checks if a field is accessible, compatible with both Java 8 and Java 21.
     * Uses canAccess() for Java 9+ and falls back to isAccessible() for Java 8.
     * 
     * @param field The field to check
     * @return true if the field is accessible, false otherwise
     */
    public static boolean isFieldAccessible(Field field) {
        try {
            // Try Java 9+ canAccess() method first
            if (Modifier.isStatic(field.getModifiers())) {
                return field.canAccess(null);
            } else {
                // For instance fields, check if the field is public
                return Modifier.isPublic(field.getModifiers()) && 
                       Modifier.isPublic(field.getDeclaringClass().getModifiers());
            }
        } catch (NoSuchMethodError e) {
            // Fall back to deprecated isAccessible() for Java 8
            return isAccessibleLegacy(field);
        } catch (Exception e) {
            // If canAccess() throws an exception, assume not accessible
            return false;
        }
    }

    /**
     * Checks if a method is accessible, compatible with both Java 8 and Java 21.
     * Uses canAccess() for Java 9+ and falls back to isAccessible() for Java 8.
     * 
     * @param method The method to check
     * @return true if the method is accessible, false otherwise
     */
    public static boolean isMethodAccessible(Method method) {
        try {
            // Try Java 9+ canAccess() method first
            if (Modifier.isStatic(method.getModifiers())) {
                return method.canAccess(null);
            } else {
                // For instance methods, check if the method is public
                return Modifier.isPublic(method.getModifiers()) && 
                       Modifier.isPublic(method.getDeclaringClass().getModifiers());
            }
        } catch (NoSuchMethodError e) {
            // Fall back to deprecated isAccessible() for Java 8
            return isAccessibleLegacy(method);
        } catch (Exception e) {
            // If canAccess() throws an exception, assume not accessible
            return false;
        }
    }

    /**
     * Checks if a constructor is accessible, compatible with both Java 8 and Java 21.
     * Uses canAccess() for Java 9+ and falls back to isAccessible() for Java 8.
     * 
     * @param constructor The constructor to check
     * @return true if the constructor is accessible, false otherwise
     */
    public static boolean isConstructorAccessible(Constructor<?> constructor) {
        try {
            // Try Java 9+ canAccess() method first
            return constructor.canAccess(null);
        } catch (NoSuchMethodError e) {
            // Fall back to deprecated isAccessible() for Java 8
            return isAccessibleLegacy(constructor);
        } catch (Exception e) {
            // If canAccess() throws an exception, assume not accessible
            return false;
        }
    }

    /**
     * Legacy fallback method for Java 8 compatibility.
     * Uses reflection to call the deprecated isAccessible() method.
     * 
     * @param accessibleObject The field, method, or constructor to check
     * @return true if accessible, false otherwise
     */
    private static boolean isAccessibleLegacy(java.lang.reflect.AccessibleObject accessibleObject) {
        try {
            Method isAccessibleMethod = java.lang.reflect.AccessibleObject.class.getMethod("isAccessible");
            return (Boolean) isAccessibleMethod.invoke(accessibleObject);
        } catch (Exception e) {
            // If reflection fails, assume not accessible
            return false;
        }
    }

    /**
     * Safely makes a field accessible, handling both Java 8 and Java 21.
     * 
     * @param field The field to make accessible
     * @return true if successful, false otherwise
     */
    public static boolean makeAccessible(Field field) {
        if (isFieldAccessible(field)) {
            return true; // Already accessible
        }
        try {
            field.setAccessible(true);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * Safely makes a method accessible, handling both Java 8 and Java 21.
     * 
     * @param method The method to make accessible
     * @return true if successful, false otherwise
     */
    public static boolean makeAccessible(Method method) {
        if (isMethodAccessible(method)) {
            return true; // Already accessible
        }
        try {
            method.setAccessible(true);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * Safely makes a constructor accessible, handling both Java 8 and Java 21.
     * 
     * @param constructor The constructor to make accessible
     * @return true if successful, false otherwise
     */
    public static boolean makeAccessible(Constructor<?> constructor) {
        if (isConstructorAccessible(constructor)) {
            return true; // Already accessible
        }
        try {
            constructor.setAccessible(true);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
}

package ch.eisenring.core.reflect;

import java.lang.reflect.Field;

import ch.eisenring.core.datatypes.primitives.Primitives;

/**
 * Provider for accessing low-level field operations.
 * In Java 8, this uses sun.misc.Unsafe.
 * In Java 9+, this can use VarHandle via reflection.
 * In Java 21+, this avoids using Unsafe due to module restrictions.
 */
public class UnsafeProvider {

    /**
     * Reference to the unsafe instance. To enable this class to work
     * properly even if no unsafe exists, it is typed as simple object.
     * <p>
     * Will be null if unsafe is not available.
     */
    private final static Object UNSAFE;

    static {
        Object unsafe = null;
        try {
            // Check if we're running on Java 21 or later - completely avoid Unsafe
            boolean isJava21OrLater = isJava21OrLater();

            // Check if we're running on Java 9 or later but before Java 21
            boolean isJava9OrLater = isJava9OrLater();

            // Only use Unsafe for Java 8 (not Java 9+ due to module restrictions)
            if (!isJava9OrLater && !isJava21OrLater) {
                final Class<?> unsafeClass = Primitives.classForName("sun.misc.Unsafe");
                final Field unsafeField = unsafeClass.getDeclaredField("theUnsafe");
                unsafeField.setAccessible(true);
                unsafe = unsafeField.get((Object) null);
            }
        } catch (final Throwable e) {
            // no problem, it just means we can't use unsafe
        }
        UNSAFE = unsafe;
    }

    public static Object getUnsafe() {
        return UNSAFE;
    }

    /**
     * Checks if we're running on Java 9 or later
     */
    public static boolean isJava9OrLater() {
        try {
            // Check for the existence of a class that was introduced in Java 9
            Class.forName("java.lang.Module");
            return true;
        } catch (ClassNotFoundException e) {
            return false;
        }
    }

    /**
     * Checks if we're running on Java 21 or later
     */
    public static boolean isJava21OrLater() {
        try {
            String javaVersion = System.getProperty("java.version");
            if (javaVersion != null) {
                // Parse the major version number
                int majorVersion;
                if (javaVersion.contains(".")) {
                    // For versions like "1.8.0" or "11.0.2"
                    String[] parts = javaVersion.split("\\.");
                    if (parts[0].equals("1")) {
                        // Old format (1.8.0)
                        majorVersion = Integer.parseInt(parts[1]);
                    } else {
                        // New format (11.0.2)
                        majorVersion = Integer.parseInt(parts[0]);
                    }
                } else {
                    // For versions without dots
                    majorVersion = Integer.parseInt(javaVersion);
                }

                return majorVersion >= 21;
            }
        } catch (Exception e) {
            // Ignore, assume it's not Java 21
        }
        return false;
    }
}

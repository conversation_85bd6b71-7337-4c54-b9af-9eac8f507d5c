package ch.eisenring.core.reflect;

import java.lang.reflect.Field;

/**
 * Fast field accessor using the oracle specific Unsafe class for direct access.
 * 
 * Not available on all JRE's, but great for bypassing most of the reflection
 * overhead if Unsafe is available. 
 */
final class FieldAccessorUnsafe extends FieldAccessor {

	final static sun.misc.Unsafe UNSAFE; static {
		// Safety check: This class should not be loaded in Java 21+
		if (UnsafeProvider.isJava21OrLater()) {
			throw new ExceptionInInitializerError("FieldAccessorUnsafe should not be used in Java 21+. Use FieldAccessorReflect instead.");
		}

		UNSAFE = (sun.misc.Unsafe) UnsafeProvider.getUnsafe();
		if (UNSAFE == null)
			throw new ExceptionInInitializerError("Unsafe not available");
	}
	
	final long offset;
	
	FieldAccessorUnsafe(final Field field) {
		super(field);
		offset = UNSAFE.objectFieldOffset(field);
	}

	@Override
	public Object get(final Object object) {
		return UNSAFE.getObject(object, offset);
	}

	@Override
	public void set(final Object object, final Object value) {
		UNSAFE.putObject(object, offset, value);
	}

	// --------------------------------------------------------------
	// ---
	// --- Direct primitive access
	// ---
	// --------------------------------------------------------------
	@Override
	public boolean getBoolean(final Object object) {
		return UNSAFE.getBoolean(object, offset);
	}

	@Override
	public byte getByte(final Object object) {
		return UNSAFE.getByte(object, offset);
	}

	@Override
	public short getShort(final Object object) {
		return UNSAFE.getShort(object, offset);
	}

	@Override
	public char getChar(final Object object) {
		return UNSAFE.getChar(object, offset);
	}
	
	@Override
	public int getInt(final Object object){
		return UNSAFE.getInt(object, offset);
	}

	@Override
	public long getLong(final Object object) {
		return UNSAFE.getLong(object, offset);
	}

	@Override
	public float getFloat(final Object object) {
		return UNSAFE.getFloat(object, offset);
	}

	@Override
	public double getDouble(final Object object) {
		return UNSAFE.getDouble(object, offset);
	}

	@Override
	public void setBoolean(final Object object, final boolean value) {
		UNSAFE.putBoolean(object, offset, value);
	}

	@Override
	public void setByte(final Object object, final byte value) {
		UNSAFE.putByte(object, offset, value);
	}

	@Override
	public void setShort(final Object object, final short value) {
		UNSAFE.putShort(object, offset, value);
	}

	@Override
	public void setChar(final Object object, final char value) {
		UNSAFE.putChar(object, offset, value);
	}

	@Override
	public void setInt(final Object object, final int value) {
		UNSAFE.putInt(object, offset, value);
	}

	@Override
	public void setLong(final Object object, final long value) {
		UNSAFE.putLong(object, offset, value);
	}

	@Override
	public void setFloat(final Object object, final float value) {
		UNSAFE.putFloat(object, offset, value);
	}

	@Override
	public void setDouble(final Object object, final double value) {
		UNSAFE.putDouble(object, offset, value);
	}

}

package ch.eisenring.core.threading;

import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;

/**
 * Factory interface for subdividing something into concurrently
 * executable portions.
 */
public interface SlicingFactory {

	/**
	 * Creates slices. The number of slices is system dependent.
	 */
	public default Iterable<Runnable> createSlices() {
		return createSlices(Executor.getDefaultConcurrency());
	}

	/**
	 * Creates M slices for the given workload.
	 */
	public default Iterable<Runnable> createSlices(final int sliceCount) {
		final ArrayList<Runnable> slices = new ArrayList<>(sliceCount);
		for (int i=0; i<sliceCount; ++i) {
			final Runnable slice = createSlice(i, sliceCount);
			if (slice != null)
				slices.add(slice);
		}
		return slices;
	}

	/**
	 * Creates a slice N of M. Its ok if the factory decides
	 * to return null for one or even all of the slices. This causes
	 * the executor to simply skip those slices.
	 */
	public Runnable createSlice(final int n, final int m);

	/**
	 * Specialized factory that distributes a quantity fairly.
	 */
	public static abstract class FairQuantity implements SlicingFactory {
		@Override
		public final List<Runnable> createSlices(final int sliceCount) {
			final ArrayList<Runnable> slices = new ArrayList<>(sliceCount);
			final int quantity = getQuantity();
			final int baseShare = quantity / sliceCount;
			final int remainder = quantity - (sliceCount * baseShare);
			int i0 = 0, i1 = baseShare, remaining = remainder;
			for (int sliceNo=0; sliceNo<sliceCount; ++sliceNo) {
				// distribute remaining
				if (sliceNo > 0 && remaining > 0) {
					++i1;
					--remaining;
				}
				// ensure i1 does not go over
				i1 = Math.min(i1, quantity);
				// skip creation of slices that would be empty
				if (i0 < i1) {
					final Runnable slice = createSlice(i0, i1);
					if (slice != null)
						slices.add(slice);
				}
				i0 = i1;
				i1 += baseShare;
			}
			return slices;
		}

		/**
		 * Gets the quantity that needs to be split among the slices.
		 */
		public abstract int getQuantity();

		/**
		 * Creates slice working on quantity indices from 
		 * i0 (inclusive) to i1 (exclusive).
		 */
		@Override
		public abstract Runnable createSlice(int i0, int i1);
	}

}

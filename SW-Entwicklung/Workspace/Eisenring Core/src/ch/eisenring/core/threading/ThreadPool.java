package ch.eisenring.core.threading;

import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;

import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.StringMakerFriendly;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.logging.Logger;

/**
 * Class for maintaining a pool of threads.
 * 
 * Designed for low synchronization overhead.
 */
public final class ThreadPool implements StringMakerFriendly {

	/**
	 * Name of this pool
	 */
	protected final String name;

	/**
	 * Maximum number of threads to be kept in the idle pool
	 */
	protected int maxPooledCount;
	
	/**
	 * Maximum number of threads to be used by the pool
	 */
	protected int maxThreadCount;

	/**
	 * Contains the current settings to be used for the
	 * threads used by this pool.
	 */
	protected final AtomicReference<ThreadParameters> parameters = new AtomicReference<>();
	
	/**
	 * The value is true as long as the pool is alive
	 */
	protected volatile boolean alive = true;
	
	/**
	 * Creates a thread pool using default limits
	 */
	public ThreadPool(final CharSequence name) {
		this(name, 10, 5, 128 << 10, true); 
	}

	/**
	 * Creates a thread pool using the given limits
	 */
	public ThreadPool(final CharSequence name,
			          final int maxThreadCount,
			          final int maxIdleCount,
			          final long stackSize,
			          final boolean daemon) {
		this.name = Strings.toString(name);
		final ThreadGroup group = new ThreadGroup(this.name);
		this.parameters.set(new ThreadParameters(group, stackSize, daemon));
		setLimits(maxThreadCount, maxIdleCount);
		ThreadCore.addThreadKillListener(killListener);
	}

	// --------------------------------------------------------------
	// ---
	// --- Thread kill listener
	// ---
	// --------------------------------------------------------------
	private final ThreadKillListener killListener = new ThreadKillListener() {
		@Override
		public void threadKilled(final Thread killedThread, final boolean hardKilled) {
			if (!hardKilled)
				return;
			if (!(killedThread instanceof ThreadPoolThread))
				return;
			final ThreadPoolThread poolThread = (ThreadPoolThread) killedThread;
			if (poolThread.getPool() != ThreadPool.this)
				return;
			if (removeFromIdle(poolThread)) {
				// no action if it was an idle thread
			} else {
				busyCount.decrementAndGet();
			}
		}
	};

	
	// --------------------------------------------------------------
	// ---
	// --- Pool size control
	// ---
	// --------------------------------------------------------------
	/**
	 * Reconfigures the ThreadPool's limits.
	 * The limits may not take effect immediately.
	 */
	public void setLimits(final int maxThreadCount, final int maxIdleCount) {
		this.maxThreadCount = Math.max(Math.min(maxThreadCount, 30_000), 5);
		this.maxPooledCount = Math.max(Math.min(maxIdleCount, 2), 0);
	}

	// --------------------------------------------------------------
	// ---
	// --- Stack size control
	// ---
	// --------------------------------------------------------------
	/**
	 * Gets the stack size the threads of this pool use
	 */
	public long getStackSize() {
		return parameters.get().getStackSize();
	}

	/**
	 * Sets the stack size to be used when creating threads.
	 * All threads given out after this call returns will
	 * obey the new stack size.
	 */
	public void setStackSize(final long stackSize) {
		long newSize = stackSize;
		newSize = Math.max(newSize, 16 << 10);
		newSize = Math.min(newSize, 1 << 30);
		newSize = (stackSize + 4095) & -4096; 
		final ThreadParameters newParams = new ThreadParameters(parameters.get(), newSize);
		setParameters(newParams);
	}

	// --------------------------------------------------------------
	// ---
	// --- Thread type control
	// ---
	// --------------------------------------------------------------
	public void setDaemon(final boolean daemon) {
		final ThreadParameters newParams = new ThreadParameters(parameters.get(), daemon);
		setParameters(newParams);
	}

	public boolean isDaemon() {
		return parameters.get().isDaemon();
	}

	/**
	 * Sets new thread parameters (if they differ from the current).
	 * 
	 * Changes to the parameters take effect for any thread scheduled
	 * after setting the new parameters.
	 */
	protected void setParameters(final ThreadParameters parameters) {
		final ThreadParameters oldParams = this.parameters.get();
		if (!parameters.equals(oldParams)) {
			this.parameters.set(parameters);
		}
	}
	
	// --------------------------------------------------------------
	// ---
	// ---
	// ---
	// --------------------------------------------------------------
	/**
	 * Contains the first object in the pool (pooled objects form a single linked list).
	 */
	final AtomicReference<ThreadPoolThread> first = new AtomicReference<ThreadPoolThread>();
	
	/**
	 * Counts how many objects are currently in the pool.
	 * The value may not be completely accurate.
	 */
	final AtomicInteger pooledCount = new AtomicInteger();
	
	/**
	 * Counts how many busy threads exist.
	 * The value may not be completely accurate.
	 */
	final AtomicInteger busyCount = new AtomicInteger();

	/**
	 * Gets a thread from the idle pool. If no more threads are available,
	 * this will create a fresh thread. If the pools has given out the
	 * maximum number of threads, this call will block until a thread
	 * becomes available.
	 */
	ThreadPoolThread obtain() {
		// if there are too many threads out there, delay execution a tiny bit
		if (busyCount.get() >= maxThreadCount) {
			ThreadCore.randomRetryWait();
		}
		while (true) {
			ThreadPoolThread idle = first.get();
			if (idle == null)
				break;
			if (!first.compareAndSet(idle, idle.next))
				continue;
			pooledCount.decrementAndGet();
			// Make sure the obtained entry does not reference
			// other entries in the pool when we give it out.
			// This could lead to nasty memory leaks.
			idle.next = null;
			if (validate(idle)) {
				busyCount.incrementAndGet();
				return idle;
			} else {
				// version mismatch, kill the thread and try the next one
				destroy(idle);
			}
		}
		// no more idle threads available, create a fresh thread
		if (Logger.isTraceEnabled())
			Logger.trace(this + ": no idle thread available, new thread created");
		busyCount.incrementAndGet();
		return create();
	}

	/**
	 * Returns a thread to the idle pool. If the idle pool contains already
	 * the number of threads allowed for this pool, the thread is terminated
	 * and dropped.
	 */
	void release(final ThreadPoolThread thread) {
		busyCount.decrementAndGet();
		if (alive) {
			while (true) {
				if (pooledCount.get() >= maxPooledCount)
					break;
				final ThreadPoolThread object = first.get();
				thread.next = object;
				if (first.compareAndSet(object, thread)) {
					pooledCount.incrementAndGet();
					return;
				}
			}
		}
		// the idle pool is full or the entire pool is shutting down,
		// tell the thread to buzz off and die.
		destroy(thread);
	}

	/**
	 * Creates a new thread
	 */
	ThreadPoolThread create() {
		return new ThreadPoolThread(this, parameters.get());
	}
	
	/**
	 * Tells the pool that the given thread has died and should be removed
	 * from the pool.
	 */
	void destroy(final ThreadPoolThread thread) {
		if (thread != null) {
			thread.issueShutdown();
		}
	}
	
	/**
	 * Checks if the thread uses the current parameter set.
	 */
	boolean validate(final ThreadPoolThread thread) {
		return thread.parameters == parameters.get();
	}

	/**
	 * Attempts to remove thread from idle list.
	 * Returns true if the thread was removed, false if not found.
	 */
	boolean removeFromIdle(final ThreadPoolThread thread) {
		ThreadPoolThread head;
		while (true) {
			head = first.get();
			if (first.compareAndSet(head, null))
				break;
		}
		// now check each thread in chain, returning each thread to the idle
		// list, except the one that is to be removed. There is no better
		// way to do this, since the list is lock-free.
		boolean result = false;
		while (head != null) {
			final ThreadPoolThread next = head.next;
			if (head == thread) {
				result = true;
			} else {
				// return this thread to idle list
				while (true) {
					head.next = first.get();
					if (first.compareAndSet(head.next, head))
						break;
				}
			}
			head = next;
		}
		return result;
	}

	// --------------------------------------------------------------
	// ---
	// --- Public API
	// ---
	// --------------------------------------------------------------
	/**
	 * Returns true if this pool is alive, false otherwise.
	 */
	public boolean isAlive() {
		return alive;
	}

	/**
	 * Starts the given task
	 */
	public Thread start(final Task task) {
		if (task.isDaemon()) {
			return startDaemon(task, task.getName(), task.getPriority());
		} else {
			return start(task, task.getName(), task.getPriority());
		}
	}

	/**
	 * Executes runnable asynchronously and anonymously.
	 * This call is equivalent to start(task, (String) null);
	 */
	public Thread start(final Runnable task) {
		if (task instanceof Task) {
			return start((Task) task);
		} else {
			return start(task, Strings.NULL, Thread.NORM_PRIORITY);
		}
	}

	/**
	 * Executes runnable asynchronously, use provided name as task name.
	 */
	public Thread start(final Runnable task, final CharSequence taskName) {
		return start(task, taskName, Thread.NORM_PRIORITY);
	}
	
	/**
	 * Executes runnable asynchronously, use provided name as task name.
	 */
	public Thread start(final Runnable task, final CharSequence taskName, final int priority) {
		final ThreadPoolThread thread = obtain();
		thread.issueTask(task, taskName, priority);
		return thread;
	}

	public Thread startDaemon(final Runnable task, final CharSequence taskName) {
		return startDaemon(task, taskName, Thread.NORM_PRIORITY);
	}

	/**
	 * Executes runnable as daemon. Depending on the pool configuration
	 * this may or may not use threads from the pool.
	 */
	public Thread startDaemon(final Runnable task, final CharSequence taskName, final int priority) {
		final ThreadParameters parameters = this.parameters.get();
		if (parameters.isDaemon()) {
			return start(task, taskName, priority);
		} else {
			final Thread thread = new ThreadCore(task);
			thread.setPriority(priority);
			thread.setDaemon(true);
			if (taskName != null)
				thread.setName(Strings.toString(taskName));
			thread.start();
			return thread;
		}
	}

	/**
	 * Shuts down this thread pool
	 */
	public void shutDown() {
		alive = false;
		ThreadCore.removeThreadKillListener(killListener);
		clearPool();
	}

	final void clearPool() {
		// shut down all idle threads in pool
		while (true) {
			final ThreadPoolThread idle = first.get();
			if (idle == null)
				break;
			if (first.compareAndSet(idle, idle.next)) {
				pooledCount.decrementAndGet();
				destroy(idle);
			}
		}
	}

	// --------------------------------------------------------------
	// ---
	// --- StringMakerFriendly implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public void toStringMaker(final StringMaker maker) {
		maker.append("ThreadPool[");
		maker.append(name);
		maker.append("(max=");
		maker.append(maxThreadCount);
		maker.append(";busy=");
		maker.append(busyCount);
		maker.append(")]");
	}
	
	// --------------------------------------------------------------
	// ---
	// --- Object overrides
	// ---
	// --------------------------------------------------------------
	@Override
	public String toString() {
		return StringMakerFriendly.toString(this);
	}

	// --------------------------------------------------------------
	// ---
	// --- Static Public API (uses the default thread pool)
	// ---
	// --------------------------------------------------------------
	/**
	 * Default ThreadPool instance
	 */
	public final static ThreadPool DEFAULT = new ThreadPool("DefaultThreadPool");

}

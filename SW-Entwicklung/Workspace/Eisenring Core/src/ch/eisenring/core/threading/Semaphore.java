//package ch.eisenring.core.threading;
//
//import java.util.Arrays;
//import java.util.concurrent.atomic.AtomicLong;
//
//import ch.eisenring.core.collections.Collection;
//import ch.eisenring.core.collections.Lookup;
//import ch.eisenring.core.collections.impl.ArrayLookup;
//import ch.eisenring.core.datatypes.strings.Strings;
//import ch.eisenring.core.sort.Comparator;
//
///**
// * Semaphore with nested exclusive/shared locking as well as deadlock
// * detection capability.
// *
// * Naturally this doesn't come as cheap as normal locking mechanisms
// * as this semaphore will keep track of all threads that currently
// * have a lock on it.
// * 
// * A deadlock occurs when a thread attempts to lock any semaphore
// * that has an owner that is currently waiting for any semaphore(s) held
// * by the thread.
// * 
// * To obtain multiple semaphores, this class provides the (static)
// * obtain(Semaphore ...) and release(Semaphore ...) to obtain/release
// * multiple Semaphores atomically.
// */
//public final class Semaphore { 
//
//	static enum Mode {
//		SHARED,
//		EXCLUSIVE
//	}
//	
//	// --------------------------------------------------------------
//	// ---
//	// --- Current semaphore state
//	// ---
//	// --------------------------------------------------------------
//	/**
//	 * Locking object used to protected the semaphore state
//	 */
//	private final Object stateLock = new Object();
//
//	/**
//	 * Map that holds all locking threads 
//	 */
//	private final Lookup<Thread, SemaphoreLock> lockMap = new ArrayLookup<>(7);
//
//	/**
//	 * Total number of locks on this Semaphore
//	 * (shared and exclusive)
//	 */
//	private int totalCount;
//
//	/**
//	 * Number of exclusive locks on this Semaphore
//	 */
//	private int exclusiveCount;
//	
//	/**
//	 * The exclusive lock on the semaphore, NULL if no exclusive lock exists.
//	 */
//	private SemaphoreLock exclusiveLock;
//
//	/**
//	 * The chain of pending requests, single linked list, oldest first
//	 */
//	private SemaphoreRequest firstRequest;
//	
//	/**
//	 * Obtains an exclusive lock on this semaphore.
//	 */
//	public void obtain() throws InterruptedException, SemaphoreException {
//		obtain(Mode.EXCLUSIVE);
//	}
//
//	/**
//	 * Obtains a shared lock on this semaphore. If the caller
//	 * already holds an exclusive lock this will become an exclusive lock as well.
//	 */
//	public void obtainShared() throws InterruptedException, SemaphoreException {
//		obtain(Mode.SHARED);
//	}
//
//	private void obtain(final Mode mode) throws InterruptedException, SemaphoreException {
//		final Thread thread = Thread.currentThread();
//		SemaphoreRequest request;
//		synchronized (stateLock) {
//			if (attempt(thread, mode) != null)
//				return;
//			// enqueue new request
//			request = obtainRequest(thread, mode);
//		}
//		wait(request);
//		releaseRequest(request);
//	}
//
//	/**
//	 * Attempts to lock semaphore in mode, returns the current lock
//	 * on success, otherwise NULL on failure.
//	 * 
//	 * Note: Must hold stateLock while calling!
//	 */
//	private SemaphoreLock attempt(final Thread thread, final Mode mode) throws SemaphoreException {
//		SemaphoreLock lock = obtainLock(thread, false);
//		if (mode == Mode.EXCLUSIVE) {
//			if (lock == null) {
//				// there are other shared or exclusive locks blocking
//				if (totalCount > 0)
//					return null;
//			} else if (lock == exclusiveLock) {
//				// just add a nesting level
//			} else if (totalCount == lock.nestCount) {
//				// caller is the only shared owner, convert lock
//			} else if (lock.nestCount > 0) {
//				// attempting to convert shared to exclusive, this is a potential deadlock.
//				throw new SemaphoreDeadLockException("conversion from shared to exclusive!");
//			} else {
//				return null;
//			}
//		} else {
//			// shared lock can't be obtained if there is an exclusive
//			// lock by a thread other than the caller
//			if (exclusiveLock != null && exclusiveLock != lock)
//				return null;
//		}
//		lock = obtainLock(thread, true);
//		add(lock, mode);
////System.out.println(thread.getName() + " became " + mode.name() + " holder of " + this + " (" + lock.nestCount + ")");
//		return lock;
//	}
//
//	/**
//	 * Releases the topmost lock on this semaphore held by the current thread
//	 */
//	public void release() throws SemaphoreException {
//		final Thread thread = Thread.currentThread();
//		SemaphoreLock lock;
//		synchronized (stateLock) {
//			lock = lockMap.get(thread);
//			if (lock == null)
//				throw new SemaphoreStateException("current thread has no lock");
//			releaseLock(lock);
//			// check if any pending requests can be granted now
//			if (exclusiveCount > 0)
//				return;
//			// satisfy as many requests as possible
//			while (this.firstRequest != null) {
//				final SemaphoreRequest request = firstRequest;
//				try {
//					lock = attempt(request.thread, request.mode);
//					if (lock == null)
//						return;
//					synchronized (request) {
//						this.firstRequest = request.next;
//						request.lock = lock; 
//						request.notify();
//					}
//				} catch (final SemaphoreException e) {
//					// should not be possible in this context... if it occurs
//					// thats very odd and most likely a major programming error
//					throw new SemaphoreStateException("error processing pending requests", e);
//				}
//			}
//		}
//	}
//
//	/**
//	 * Adds a lock level to lock
//	 */
//	private final void add(final SemaphoreLock lock, final Mode mode) {
//		++lock.nestCount;
//		++this.totalCount;
//		// if 
//		if (mode == Mode.EXCLUSIVE)
//			this.exclusiveLock = lock;
//		// if the lock is exclusive, increment the exclusive lock count,
//		// regardless of desired mode. This silently converts shared locks
//		// into exclusive locks when the thread already has an exclusive lock.
//		if (this.exclusiveLock == lock)
//			++this.exclusiveCount;
//	}
//
//	// --------------------------------------------------------------
//	// ---
//	// --- Identity management
//	// ---
//	// --------------------------------------------------------------
//	private final static AtomicLong NEXT_ID = new AtomicLong();
//
//	/**
//	 * Each semaphore has it unique, comparable identity
//	 */
//	private final long id = NEXT_ID.incrementAndGet();
//
//	/**
//	 * Semaphore name (useful for debugging mostly)
//	 */
//	private final String name;
//
//	// --------------------------------------------------------------
//	// ---
//	// --- Constructors
//	// ---
//	// --------------------------------------------------------------
//	public Semaphore() {
//		name = Strings.concat("Semaphore#", id);
//	}
//
//	public Semaphore(final String name) {
//		this.name = name;
//	}
//
//	// --------------------------------------------------------------
//	// ---
//	// --- Object overrides
//	// ---
//	// --------------------------------------------------------------
//	@Override
//	public int hashCode() {
//		// since id's are assigned strictly sequential, the lower
//		// 32 bits should have nearly perfect distribution.
//		return (int) id;
//	}
//
//	@Override
//	public boolean equals(final Object obj) {
//		return obj == this;
//	}
//
//	@Override
//	public String toString() {
//		return name + "[" + totalCount + "," + exclusiveCount + "]";
//	}
//
//	// --------------------------------------------------------------
//	// ---
//	// --- Request cache
//	// ---
//	// --------------------------------------------------------------
//	private SemaphoreRequest requestCache = new SemaphoreRequest();
//	
//	/**
//	 * Creates SemaphoreRequest for arguments.
//	 * The request is enqueued in the semaphores request queue.
//	 */
//	private SemaphoreRequest obtainRequest(final Thread thread, final Mode mode) {
//		SemaphoreRequest request = requestCache;
//		if (request == null) {
//			request = new SemaphoreRequest();
//		} else {
//			requestCache = request.next;
//			request.next = null;
//		}
//		request.thread = thread;
//		request.mode = mode;
//		// enqueue the request
//		SemaphoreRequest node = this.firstRequest;
//		if (node == null) {
//			this.firstRequest = request;
//		} else {
//			while (node.next != null)
//				node = node.next;
//			node.next = request;
//		}
//		return request;
//	}
//
//	/**
//	 * Returns a SemaphoreRequest to the local cache
//	 */
//	private void releaseRequest(final SemaphoreRequest request) {
//		request.thread = null;
//		request.lock = null;
//		request.next = requestCache;
//		requestCache = request;
//	}
//
//	/**
//	 * Waits for request to be satisfied. Blocks until the request can be satisfied.
//	 * Releasing/dequeuing the request is left to the caller.
//	 */
//	private /*static*/ void wait(final SemaphoreRequest request) throws InterruptedException, SemaphoreException {
//System.out.println(request.thread.getName() + " waiting for " + this);		
//		synchronized (request) {
//			if (request.lock != null)
//				return;
//			request.wait();
//		}
//	}
//
//	// --------------------------------------------------------------
//	// ---
//	// --- Lock cache
//	// ---
//	// --------------------------------------------------------------
//	private SemaphoreLock lockCache = new SemaphoreLock();
//
//	/**
//	 * Gets SemaphoreLock for thread, optionally creating a lock if none exists.
//	 */
//	private SemaphoreLock obtainLock(final Thread thread, final boolean createIfNotExists) {
//		SemaphoreLock lock = this.lockMap.get(thread);
//		if (lock != null || !createIfNotExists)
//			return lock;
//		lock = this.lockCache;
//		if (lock == null) {
//			lock = new SemaphoreLock();
//		} else {
//			this.lockCache = lock.next;
//			lock.next = null;
//		}
//		lock.thread = thread;		
//		this.lockMap.put(thread, lock);
//		return lock;
//	}
//
//	/**
//	 * Removes a level from lock, takes care to update the semaphores
//	 * lock count and also removes the lock from lock map when the
//	 * nesting count reaches zero and returns it to the local cache.
//	 */
//	private void releaseLock(final SemaphoreLock lock) throws SemaphoreException {
//		final int nestCount = lock.nestCount - 1;
//		if (nestCount < 0)
//			throw new SemaphoreStateException("release() while not holding lock");
//		if (this.exclusiveLock == lock) {
//			--this.exclusiveCount;
//			if (this.exclusiveCount <= 0)
//				this.exclusiveLock = null;
//		}
//		--this.totalCount;
//		if (nestCount == 0) {
//System.out.println(lock.thread.getName() + " has released the final nest of " + this + " (0)");			
//			this.lockMap.remove(lock.thread);
//			lock.thread = null;
//			lock.next = this.lockCache;
//			this.lockCache = lock;
//		} else {
//System.out.println(lock.thread.getName() + " has released one nest of " + this + " (" + lock.nestCount + ")");			
//		}
//		lock.nestCount = nestCount;
//	}
//
//	// --------------------------------------------------------------
//	// ---
//	// --- Multi-allocation
//	// ---
//	// --------------------------------------------------------------
//	/**
//	 * Obtains all of the specified semaphores, will block until all have been obtained.
//	 */
//	public static void obtain(final java.util.Collection<? extends Semaphore> semaphores) throws InterruptedException, SemaphoreException {
//		final Semaphore[] array = Collection.toArray(semaphores, Semaphore.class);
//		Arrays.sort(array, Order.Id);
//		obtain(array);
//	}
//	
//	private static void obtain(final Semaphore[] semaphores) throws InterruptedException, SemaphoreException {
//		if (semaphores == null)
//			return;
//		final int count = semaphores.length;
//		if (count == 0) {
//			return;
//		} else if (count == 1) {
//			semaphores[0].obtain();
//			return;
//		}
//
//		int critical = 0;
//		final Thread thread = Thread.currentThread();
//		final boolean flags[] = new boolean[count];
//		int holdCount;
//		do {
//			holdCount = 0;
//			try {
//				// obtain the critical one normally
//				final Semaphore first = semaphores[critical];
//				first.obtain();
//				flags[critical] = true;
//				
//				// now attempt the others
//				for (int i=0; i<count; ++i) {
//					if (i == critical)
//						continue;
//					final Semaphore other = semaphores[i];
//					synchronized (other.stateLock) {
//						if (other.attempt(thread, Mode.EXCLUSIVE) != null) {
//							flags[i] = true;
//						} else {
//							// attempt this one first next time
//							critical = i;
//							break;
//						}
//					}
//				}
//				
//				// how many did we get?
//				for (int i=0; i<count; ++i)
//					if (flags[i])
//						++holdCount;
//			} finally {
//				// release everything we got on failure
//				if (holdCount != count) {
//					for (int i=0; i<semaphores.length; ++i) {
//						if (flags[i]) {
//							semaphores[i].release();
//							flags[i] = false;
//						}
//					}
//				}
//			}
//		} while (holdCount != count);
//	}
//
//	// --------------------------------------------------------------
//	// ---
//	// --- Ordering
//	// ---
//	// --------------------------------------------------------------
//	public static class Order {
//		public final static Comparator<Semaphore> Name = Comparator.wrapNullSafe(
//				new Comparator<Semaphore>() {
//			@Override
//			public int compare(final Semaphore s1, final Semaphore s2) {
//				return Strings.compare(s1.name, s2.name);
//			}
//		});
//
//		public final static Comparator<Semaphore> Id = Comparator.wrapNullSafe(
//				new Comparator<Semaphore>() {
//			@Override
//			public int compare(final Semaphore s1, final Semaphore s2) {
//				return compareSigned(s1.id, s2.id);
//			}
//		});
//	}
//
//}

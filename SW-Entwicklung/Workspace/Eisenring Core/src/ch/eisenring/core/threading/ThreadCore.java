package ch.eisenring.core.threading;

import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;

import ch.eisenring.core.collections.impl.AtomicArraySet;

/**
 * Extended thread class, provides a means for fast thread locals,
 * and by fast we mean local field fast.
 */
public class ThreadCore extends Thread {

	public final static AtomicLong MIN_STACK_SIZE = new AtomicLong(256 << 10);
	public final static AtomicLong MAX_STACK_SIZE = new AtomicLong(16 << 20);
	
	/**
	 * Instance of ThreadCoreLocals used by this thread
	 */
	final ThreadCoreLocals threadLocals = new ThreadCoreLocals();

	// --------------------------------------------------------------
	// ---
	// --- Constructors
	// ---
	// --------------------------------------------------------------
	ThreadCore(final Runnable runnable) {
		super(runnable);
	}

	ThreadCore(final ThreadGroup group, final Runnable runnable, final String name, final long stacksize) {
		super(group, runnable, name, stacksize);
	}

	// --------------------------------------------------------------
	// ---
	// --- Utility methods
	// ---
	// --------------------------------------------------------------
	/**
	 * Sleep until time is reached or passed.
	 * If time is null, return immediately
	 */
	public static void sleepUntil(final Long time) {
		if (time != null)
			sleepUntil(time.longValue());
	}
	
	/**
	 * Sleep until time is reached (or passed).
	 * May return immediately if the argument timestamp is smaller
	 * than the current system timestamp.
	 */
	public static void sleepUntil(final long time) {
		boolean interrupted = Thread.interrupted();
		long current;
		while (time > (current = System.currentTimeMillis())) {
			long delay = (time - current) >> 1;
			if (delay <= 1) {
				Thread.yield();
			} else {
				try {
					Thread.sleep(delay < 1L ? 1L : delay);
				} catch (final InterruptedException e) {
					interrupted = true;
				}
			}
		}
		// restore/set interrupt status
		if (interrupted)
			Thread.currentThread().interrupt();
	}
	
	/**
	 * Sleep for specified number of milliseconds (approximately).
	 */
	public static void sleep(int milliSeconds) {
		if (milliSeconds <= 1) {
			Thread.yield();
		} else {
			try {
				Thread.sleep(milliSeconds);
			} catch (final InterruptedException e) {
				Thread.currentThread().interrupt();
			}
		}
	}

	private final static Random RND = new Random();

	/**
	 * Sleeps for a small, random period.
	 * 
	 * This call is useful for copy-modify-replace when detecting
	 * that the object was already replaced to randomize retry
	 * execution.
	 */
	public static void randomRetryWait() {
		final int delay = RND.nextInt() & 3;
		if (delay <= 1) {
			if (delay > 0)
				Thread.yield();
		} else {
			try {
				Thread.sleep(delay - 1);
			} catch (final InterruptedException e) {
				Thread.currentThread().interrupt();
			}
		}
	}

	public static ThreadCore create(final Runnable task, final String name) {
		final ThreadCore result = new ThreadCore(task);
		if (name != null)
			result.setName(name);
		return result;
	}

	/**
	 * Creates a thread with the given parameters.
	 */
	public static ThreadCore create(final Runnable task, final String name, final int stackSizeKB) {
		final long stackSize = Math.min(MAX_STACK_SIZE.get(),
				Math.max(((long) stackSizeKB) << 10, MIN_STACK_SIZE.get()));
		return new ThreadCore((ThreadGroup) null, task, name, stackSize);
	}

	// --------------------------------------------------------------
	// ---
	// --- Thread killing
	// ---
	// --------------------------------------------------------------
	private final static AtomicArraySet<ThreadKillListener> killListeners = new AtomicArraySet<>();
	
	public static void addThreadKillListener(final ThreadKillListener listener) {
		if (listener == null)
			return;
		killListeners.add(listener);
	}

	public static void removeThreadKillListener(final ThreadKillListener listener) {
		if (listener == null)
			return;
		killListeners.remove(listener);
	}

	private static void fireThreadKilled(final Thread killedThread, final boolean hardKilled) {
		for (final ThreadKillListener listener : killListeners) {
			try {
				listener.threadKilled(killedThread, hardKilled);
			} catch (final Exception e) {
				// we don't care
			}
		}
	}

	/**
	 * Attempts to kill the given thread. This is inherently unsafe
	 * and may leave the system in an unpredictable state.
	 *
	 * This is a two step method, first the thread is interrupted,
	 * and if this succeeds in making the thread terminate by itself
	 * within a time limit, everything is well.
	 *
	 * In Java 21+, Thread.stop() is no longer available, so we can only
	 * interrupt the thread and wait for it to terminate gracefully.
	 * If the thread doesn't respond to interruption, we log a warning
	 * but cannot forcefully terminate it.
	 */
	public static void kill(final Thread thread) {
		if (thread == null)
			return;

		// Check Java version to determine available methods
		boolean isJava21OrLater = !System.getProperty("java.version").startsWith("1.8") &&
				Integer.parseInt(System.getProperty("java.version").split("\\.")[0]) >= 21;

		// first, attempt to interrupt the thread repeatedly.
		// if it responds to that we're fine.
		long timeout = System.currentTimeMillis() + 150;
		while (timeout > System.currentTimeMillis()) {
			thread.interrupt();
			sleep(10);
			if (!thread.isAlive()) {
				fireThreadKilled(thread, false);
				return;
			}
		}

		// For Java 21+, we cannot use Thread.stop() as it's been removed
		if (isJava21OrLater) {
			// Extended wait for Java 21+ - give the thread more time to respond to interruption
			long extendedTimeout = System.currentTimeMillis() + 5000; // 5 more seconds
			while (extendedTimeout > System.currentTimeMillis()) {
				thread.interrupt();
				sleep(100);
				if (!thread.isAlive()) {
					fireThreadKilled(thread, false);
					return;
				}
			}
			// Log warning and give up - we cannot forcefully kill threads in Java 21+
			System.err.println("WARNING: Thread " + thread.getName() +
					" did not respond to interruption after extended timeout. Cannot forcefully terminate in Java 21+.");
			System.err.println("The thread may continue running in the background.");
			fireThreadKilled(thread, false); // Mark as killed even though it might still be running
		} else {
			// For Java 8, we can still use the deprecated Thread.stop() method
			try {
				// Use reflection to avoid compilation issues in Java 21
				java.lang.reflect.Method stopMethod = Thread.class.getMethod("stop");
				stopMethod.invoke(thread);
				fireThreadKilled(thread, true);
			} catch (final Exception e) {
				// Thread.stop() might not be available
				System.err.println("WARNING: Could not forcefully terminate thread " + thread.getName() +
						": " + e.getMessage());
				fireThreadKilled(thread, false);
			}
		}
	}

}

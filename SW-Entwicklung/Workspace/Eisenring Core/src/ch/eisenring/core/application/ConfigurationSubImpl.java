package ch.eisenring.core.application;

import ch.eisenring.core.collections.Map;
import ch.eisenring.core.datatypes.strings.Strings;

final class ConfigurationSubImpl implements Configuration {

	private final Map<String, String> propertyMap;
	private final String keyPrefix;
	
	protected ConfigurationSubImpl(final Configuration configuration, final String keyPrefix) {
		this.propertyMap = configuration.getPropertyMap();
		this.keyPrefix = keyPrefix;
	}

	private String prefixKey(final String propertyKey) {
		return keyPrefix == null ? propertyKey : Strings.concat(keyPrefix, propertyKey);
	}

	// --------------------------------------------------------------
	// ---
	// --- Configuration implementation / overrides
	// ---
	// --------------------------------------------------------------
	@Override
	public Map<String, String> getPropertyMap() {
		return propertyMap;
	}

	@Override
	public boolean keyExists(final String propertyKey) {
		return propertyMap.containsKey(prefixKey(propertyKey));
	}

	/**
	 * Gets raw data for property key
	 */
	@Override
	public String getRawData(final String propertyKey) {
		return propertyMap.get(prefixKey(propertyKey));
	}

}

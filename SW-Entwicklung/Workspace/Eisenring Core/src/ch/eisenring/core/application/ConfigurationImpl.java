package ch.eisenring.core.application;

import ch.eisenring.core.collections.Map;

final class ConfigurationImpl implements Configuration {

	private final Map<String, String> propertyMap;
	
	protected ConfigurationImpl(final Map<String, String> propertyMap) {
		this.propertyMap = propertyMap;
	}

	// --------------------------------------------------------------
	// ---
	// --- Configuration implementation / overrides
	// ---
	// --------------------------------------------------------------
	@Override
	public Map<String, String> getPropertyMap() {
		return propertyMap;
	}

	@Override
	public boolean keyExists(String key) {
		return propertyMap.containsKey(key);
	}

	/**
	 * Gets raw data for property key
	 */
	@Override
	public String getRawData(final String propertyKey) {
		return propertyMap.get(propertyKey);
	}

}

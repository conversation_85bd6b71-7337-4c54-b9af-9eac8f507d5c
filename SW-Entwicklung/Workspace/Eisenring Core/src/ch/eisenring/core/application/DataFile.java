package ch.eisenring.core.application;

import java.io.File;
import java.io.IOException;
import java.time.Duration;

import ch.eisenring.core.datatypes.primitives.Primitives;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.io.file.FileItem;
import ch.eisenring.core.logging.Logger;

/**
 * Base class for change tracked data files.
 * 
 * The class keeps track of the change state of a file,
 * if the file is changed when getData() is called, the data will be
 * rebuild through a call to createData().
 * 
 * The base class takes care of change detection and thread locking.
 */
public final class DataFile<T> extends ManagedFile {

	protected T data;
	protected DataFileParser<T> parser;
	protected long maxAgeMillis;
	private final FileItem fileItem;

	public DataFile(final CharSequence filename, final DataFileParser<T> parser) {
		this(filename, 10000L, parser);
	}

	@Deprecated
	public DataFile(final CharSequence filename, final long maxAgeMillis, final DataFileParser<T> parser) {
		super(filename);
		this.fileItem = FileItem.create(filename);
		this.parser = parser;
		this.maxAgeMillis = maxAgeMillis;
		isChanged();
	}

	public DataFile(final CharSequence filename, final Duration maxAge, final DataFileParser<T> parser) {
		super(filename);
		this.fileItem = FileItem.create(filename);
		this.parser = parser;
		this.maxAgeMillis = maxAge.toMillis();
		isChanged();
	}

	@Override
	protected long getMinChangeCheckInterval() {
		return maxAgeMillis;
	}

	@Override
	protected FileItem getFileItem() {
		return fileItem;
	}


	/**
	 * Gets the data from this data file as parsed by the associated DataFileParser.
	 * 
	 * This method will automatically decide if the parsed data is currently valid,
	 * or if it needs to be parsed again.
	 */
	public final T getData() throws IOException {
		T result;
		synchronized (this) {
			result = this.data;
			if (data == null || isChanged()) {
				if (data == null) {
					Logger.info("Loading data from \"{}\"", this);
				} else {
					Logger.info("Reloading data from \"{}\"", this);
				}
				// make sure data is invalidated now, in case an exception
				// occurs when attempting to create the new data.
				this.data = null;
				try {
					Logger.debug("Loading " + getFileItem().getAbsolutePath());
					result = parser.parseFile(this);
					this.data = result;
				} catch (final Exception e) {
					final String msg = logEx(e);
					throw new IOException(msg, e);
				}
			}
		}
		return result;
	}

	private String logEx(final Throwable t) {
		final StringMaker maker = StringMaker.obtain(128);
		maker.append(Primitives.getSimpleName(t.getClass()));
		maker.append(" loading data from \"");
		maker.append(this);
		maker.append("\": ");
		maker.append(t.getMessage());
		final String message = maker.release();
		Logger.error(message);
		return message;
	}

}

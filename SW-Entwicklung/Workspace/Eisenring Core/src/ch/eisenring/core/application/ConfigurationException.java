package ch.eisenring.core.application;

import ch.eisenring.core.datatypes.strings.Strings;

@SuppressWarnings("serial")
public class ConfigurationException extends RuntimeException {

	public ConfigurationException(final CharSequence message) {
		super(Strings.toString(message));
	}
	
	public ConfigurationException(final Throwable cause) {
		super(cause);
	}
	
	public ConfigurationException(final CharSequence message, final Throwable cause) {
		super(Strings.toString(message), cause);
	}

}

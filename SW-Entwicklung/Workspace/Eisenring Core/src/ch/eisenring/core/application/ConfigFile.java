package ch.eisenring.core.application;

import ch.eisenring.core.io.file.FileItem;
import ch.eisenring.core.io.file.FileItemProperties;
import ch.eisenring.core.logging.Logger;
import java.io.File;

final class ConfigFile extends ManagedFile {

	private FileItem fileItem;

	public ConfigFile(final CharSequence filename) {
		super(filename);
		this.fileItem = FileItem.create(filename);

		// Add detailed logging about the file
		Logger.info("Created ConfigFile for: '{}'", filename);
		Logger.info("Absolute path: '{}'", new File(filename.toString()).getAbsolutePath());

		// Check if file exists using different methods
		File javaFile = new File(filename.toString());
		FileItemProperties props = fileItem.getProperties();
		Logger.info("File exists (java.io.File): {}", javaFile.exists());
		Logger.info("File exists (FileItem): {}", props.exists());
		Logger.info("Is directory: {}, Is file: {}", props.isDirectory(), props.isFile());

		// If parent directory exists, list its contents
		File parentDir = javaFile.getParentFile();
		if (parentDir != null && parentDir.exists()) {
			Logger.info("Parent directory exists: '{}'", parentDir.getAbsolutePath());
			File[] files = parentDir.listFiles();
			if (files != null) {
				Logger.info("Files in parent directory:");
				for (File file : files) {
					Logger.info("  - {} ({})", file.getName(), file.isDirectory() ? "dir" : "file");
				}
			}
		} else if (parentDir != null) {
			Logger.info("Parent directory does not exist: '{}'", parentDir.getAbsolutePath());
		}

		isChanged();
	}

	@Override
	protected long getMinChangeCheckInterval() {
		return 5000;
	}

	@Override
	protected FileItem getFileItem() {
		return fileItem;
	}

}

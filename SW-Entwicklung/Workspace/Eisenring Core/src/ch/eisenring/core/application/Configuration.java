package ch.eisenring.core.application;

import java.net.InetSocketAddress;
import java.net.SocketAddress;

import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.Map;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.datatypes.primitives.ConversionUtil;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.io.file.FileItem;
import ch.eisenring.core.io.file.FileItemProperties;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.core.logging.Logger.LogLevel;

/**
 * Configuration read access API definition
 */
public interface Configuration {

	/**
	 * Gets underlying property map. Avoid accessing this.
	 */
	public abstract Map<String, String> getPropertyMap();

	/**
	 * Gets raw data for property key.
	 */
	public abstract String getRawData(final String key);

	/**
	 * Returns true if configuration key exists
	 */
	public abstract boolean keyExists(final String key);

	/**
	 * Gets a configuration instance where all accesses to the properties are
	 * automatically prefixed by the given prefix.
	 */
	public default Configuration subConfiguration(final String keyPrefix) {
		return new ConfigurationSubImpl(this, keyPrefix);
	}

	/**
	 * Get Boolean property
	 */
	public default Boolean getBoolean(final String propertyKey, final Boolean defaultValue) {
		final String rawData = getRawData(propertyKey);
		return ConversionUtil.convert(rawData, defaultValue);
	}

	/**
	 * Gets LogLevel property
	 */
	public default LogLevel getLogLevel(final String propertyKey, final LogLevel defaultValue) {
		final String rawData = getRawData(propertyKey);
		final LogLevel level = LogLevel.fromString(rawData);
		return level == null ? defaultValue : level;
	}

	/**
	 * Get String property
	 */
	public default String getString(final String propertyKey, final String defaultValue) {
		String rawData = getRawData(propertyKey);
		rawData = Strings.trim(rawData);
		return ConfigurationPrivate.resolveVariables(rawData.length() <= 0 ? defaultValue : rawData);
	}

	/**
	 * Gets a multiple String property (separated by separator character)
	 */
	public default List<String> getStrings(final String propertyKey, final List<String> defaultValue, final char separator) {
		final String rawValue = getRawData(propertyKey);
		if (rawValue == null)
			return defaultValue;
		String[] array = Strings.explode(rawValue, separator);
		if (array == null)
			return defaultValue;
		for (int i=0; i<array.length; ++i) {
			array[i] = ConfigurationPrivate.resolveVariables(array[i]);
		}
		return List.asList(array);
	}

	/**
	 * Gets a multiple String property (located in numbered keys e.g. Param1, Param2, Param3 ...)
	 */
	public default List<String> getStrings(final String propertyKey,
			final int minId, final int maxId, final boolean includeNulls) {
		final List<String> list = new ArrayList<>();
		for (int id=minId; id<=maxId; ++id) {
			final String keyId = Strings.concat(propertyKey, id);
			final String value = getRawData(keyId);
			if (value != null || includeNulls) {
				list.add(ConfigurationPrivate.resolveVariables(value));
			}
		}
		return list;
	}

	/**
	 * Gets a port number property.
	 * Returns either a valid port number [0 ... 65535] or -1 if the property
	 * is not set or set to a value outside the valid range.
	 */
	public default int getPortNumber(final String propertyKey) {
		int port = getInteger(propertyKey, Integer.MIN_VALUE, Integer.MAX_VALUE, -1);
		if (port >= 0 && port <= 65535)
			return port;
		return -1;
	}

	/**
	 * Get Integer property
	 */
	public default Integer getInteger(final String propertyKey,
			final int minValue, final int maxValue, final int defaultValue) {
		int value = defaultValue;
		String s = getRawData(propertyKey);
		if (s != null) {
			try {
				final long l = Strings.parseLongUserInput(s);
				if (l > Integer.MAX_VALUE) {
					value = Integer.MAX_VALUE;
				} else if (l < Integer.MIN_VALUE) {
					value = Integer.MIN_VALUE;
				} else {
					value = (int) l;
				}
				// limit value range
				if (value < minValue) {
					value = minValue;
				} else if (value > maxValue) {
					value = maxValue;
				}
			} catch (final Exception e) {
				Logger.error(Strings.concat("Property: ", propertyKey, " - ", e.getMessage()));
				Logger.error(e);
			}
		}
		return value;
	}

	/**
	 * Get Long property
	 */
	public default long getLong(final String propertyKey,
			final long minValue, final long maxValue, final long defaultValue) {
		long value = defaultValue;
		String s = getRawData(propertyKey);
		if (s != null) {
			try {
				value = Strings.parseLongUserInput(s);
				if (value<minValue) {
					value = minValue;
				} else if (value>maxValue) {
					value = maxValue;
				}
			} catch (final Exception e) {
				Logger.error(Strings.concat("Property: ", propertyKey, " - ", e.getMessage()));
				Logger.error(e);
			}
		}
		return value;
	}

	/**
	 * Gets a network address (name/ip + port)
	 */
	public default SocketAddress getSocketAddress(final String hostKey, final String portKey) throws ConfigurationException {
		return getSocketAddress(hostKey, portKey, false);
	}

	/**
	 * Gets a network address (name/ip + port)
	 */
	public default SocketAddress getSocketAddress(final String hostKey, final String portKey, final boolean isOptional) throws ConfigurationException {
		try {
			Logger.info("Getting socket address - hostKey: {}, portKey: {}, isOptional: {}", hostKey, portKey, isOptional);

			// Get host value
			String host = getString(hostKey, null);
			Logger.info("Host value from configuration: {}", host);

			if (Strings.isEmpty(host) && isOptional) {
				Logger.info("Host is empty and optional, returning null");
				return null;
			}

			// Get port value
			int port = getInteger(portKey, 1, 65535, -1);
			Logger.info("Port value from configuration: {}", port);

			// Check if host contains port information
			final int i = host.lastIndexOf(':');
			if (i > 0) {
				Logger.info("Host contains port information: {}", host);
				int port2;
				try {
					port2 = Strings.parseInt(host, i + 1, host.length());
					host = Strings.subString(host, 0, i);
					Logger.info("Extracted port from host: {}, new host: {}", port2, host);
				} catch (final Throwable t) {
					Logger.error("Error parsing port from host: {}", t.getMessage());
					throw new ConfigurationException(t.getMessage(), t);
				}
				if (port2 < 1 || port2 > 65535) {
					Logger.error("Port from host is invalid: {}", port2);
					throw new ConfigurationException(Strings.concat(hostKey, " must specify a port number in range 1 to 65535 or no port at all"));
				}
				if (port < 0 || port == port2) {
					port = port2;
					Logger.info("Using port from host: {}", port);
				} else {
					Logger.error("Conflicting ports - from host: {}, from port key: {}", port2, port);
					throw new ConfigurationException(Strings.concat(hostKey, " specifies port conflicting with ", portKey));
				}
			}

			// Validate final port
			if (port < 1 || port > 65535) {
				Logger.error("Final port is invalid: {}", port);
				throw new ConfigurationException(Strings.concat(hostKey, "/", portKey, " port specification missing or invalid"));
			}

			Logger.info("Creating socket address with host: {}, port: {}", host, port);
			return new InetSocketAddress(host, port);
		} catch (final Exception e) {
			Logger.error("Error creating socket address: {}", e.getMessage());
			throw new ConfigurationException(Strings.concat("configuration problem: ", e.getMessage()), e);
		}
	}

	/**
	 * Gets a directory path.
	 */
	public default FileItem getDirectory(final String propertyKey, final FileItem defaultValue, final boolean createIfNotExists) {
		final String rawData = getString(propertyKey, (String) null);
		if (Strings.isEmpty(rawData))
			return defaultValue;
		do {
			try {
				final FileItem path = FileItem.create(rawData);
				final FileItemProperties properties = path.getProperties();
				if (properties.exists()) {
					if (!properties.isDirectory())
						break;
				} else if (createIfNotExists) {
					path.mkdirs();
					if (!properties.exists())
						break;
				} else {
					// directory does not exist, but may be valid
				}
				return path;
			} catch (final Exception e) {
				// problem with file item, fall back to default value
			}
		} while (false);
		return defaultValue;
	}

	/**
	 * Gets a file path
	 */
	public default FileItem getFile(final String propertyKey, final FileItem defaultValue) {
		final String rawData = getString(propertyKey, (String) null);
		if (Strings.isEmpty(rawData))
			return defaultValue;
		do {
			try {
				final FileItem path = FileItem.create(rawData);
				final FileItemProperties properties = path.getProperties();
				if (properties.exists()) {
					if (!properties.isFile())
						break;
				} else {
					// file does not exist, but may be valid
				}
				return path;
			} catch (final Exception e) {
				// problem with file item, fall back to default value
			}
		} while (false);
		return defaultValue;
	}

}

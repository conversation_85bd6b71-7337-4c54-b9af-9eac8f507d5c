package ch.eisenring.core.application;

import java.io.IOException;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;

public interface RollingDataFileParser<T> {

	/**
	 * Parses the given data file, returns parsed data structure
	 */
	public T parseFile(final RollingDataFile<T> dataFile) throws IOException, Exception;

	public static RollingDataFileParser<String> getTextParser(final Charset encoding) {
		return new RollingDataFileParser<String>() {
			@Override
			public String parseFile(final RollingDataFile<String> dataFile) throws IOException, Exception {
				return dataFile.getTextContent(StandardCharsets.ISO_8859_1);
			}
		};
	}

}

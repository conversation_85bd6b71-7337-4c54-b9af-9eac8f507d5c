package ch.eisenring.core.crypt;

import java.io.IOException;
import java.io.InputStream;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;

import ch.eisenring.core.datatypes.encoded.ByteEncoding;
import ch.eisenring.core.datatypes.primitives.Primitives;
import ch.eisenring.core.io.Streams;
import ch.eisenring.core.io.binary.BinaryHolder;
import ch.eisenring.core.io.file.FileItem;

/**
 * Generic interface to represent a hash.
 * 
 * Warning: Changing implementations opens a can of worms for compatibility
 * of authentication process and update file fingerprinting.
 */
public interface Hash {

	/**
	 * Returns the length of the hash in bits
	 */
	public int getHashLength();

	/**
	 * Gets the hash method that was used to create this hash
	 */
	public HashMethodCode getHashMethod();

	/**
	 * Gets the raw bytes of this hash. Do not modify the returned
	 * array, as it is potentially a reference.
	 */
	public byte[] toByteArray();

	/**
	 * Gets the hexadecimal representation of this hash
	 */
	public default String toHexString() {
		return ByteEncoding.HEXADECIMAL.encode(toByteArray(), false);
	}
	
	/**
	 * Checks two hashes for equality. This compares value as well as hashing method.
	 */
	public static boolean equals(final Hash hash1, final Hash hash2) {
		if (hash1 == null || hash2 == null)
			return false;
		return Primitives.equals(hash1.getHashMethod(), hash2.getHashMethod())
				&& Arrays.equals(hash1.toByteArray(), hash2.toByteArray());
	}

	/**
	 * Creates a generic hash with given values
	 */
	public static Hash create(final byte[] hashValue, final HashMethodCode hashMethod) {
		return new HashImpl(hashValue, hashMethod);
	}

	/**
	 * Creates generic hash for File
	 */
	public static Hash create(final FileItem file, final HashMethodCode hashMethod) throws IOException {
		return create(file.getInputStream(), hashMethod);
	}

	/**
	 * Creates generic hash for InputStream.
	 * The stream will be close by this method, no matter if an exception is thrown.
	 */
	public static Hash create(final InputStream stream, final HashMethodCode hashMethod) throws IOException {
		try {
			final byte[] buffer;
			try {
				buffer = Streams.createBuffer(1 << 16);
			} catch (final OutOfMemoryError e) {
				throw new IOException(e);
			}
    		final MessageDigest md = hashMethod.newDigest();
    		while (true) {
    			int l = stream.read(buffer);
    			if (l <= 0)
    				break;
    			md.update(buffer, 0, l);
    		}
    		return new HashImpl(md.digest(), hashMethod);
		} catch (final NoSuchAlgorithmException e) {
			throw new IOException(e.getMessage(), e);
		} finally {
			Streams.closeSilent(stream);
		}
	}

	/**
	 * Creates generic hash for BinaryHolder
	 */
	public static Hash create(final BinaryHolder binary, final HashMethodCode hashMethod) throws IOException {
		try (final InputStream in = binary.getInputStream()) {
			return create(in, hashMethod);
		}
	}

}

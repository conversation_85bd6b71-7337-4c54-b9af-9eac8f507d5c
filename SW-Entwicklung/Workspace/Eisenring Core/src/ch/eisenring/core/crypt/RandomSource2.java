package ch.eisenring.core.crypt;

import java.security.SecureRandom;
import java.util.Random;

import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.format.api.Formats;

/**
 * Local random source that can be updated with data
 * supplied from various sources.
 */
class RandomSource2 implements RandomSource {

	private final static short[] FACTOR_TABLE = {
		1009, 1013, 1019, 1021, 1031, 1033, 1039, 1049, 1051, 1061, 1063, 1069, 1087, 1091, 1093, 1097,
		1201, 1213, 1217, 1223, 1229, 1231, 1237, 1249, 1259, 1277, 1279, 1283, 1289, 1291, 1319, 1321,
		1427, 1429, 1433, 1439, 1447, 1451, 1453, 1459, 1499, 1511, 1523, 1531, 1543, 1549, 1571, 1601,
		1607, 1609, 1613, 1619, 1621, 1637, 1657, 1663, 1667, 1669, 1709, 1721, 1733, 1747, 1753, 1759,
		1777, 1783, 1811, 1831, 1847, 1861, 1867, 1871, 1873, 1879, 1889, 1901, 1907, 1913, 1931, 1933,
		1951, 1973, 1987, 1993, 2003, 2011, 2017, 2029, 2039, 2053, 2063, 2069, 2081, 2083, 2111, 2129,
		2131, 2141, 2153, 2179, 2213, 2267, 2269, 2273, 2281, 2287, 2293, 2297, 2309, 2311, 2333, 2339,
		2357, 2377, 2389, 2393, 2399, 2411, 2467, 2473, 2477, 2521, 2531, 2539, 2543, 2549, 2551, 2609,
		2617, 2621, 2633, 2647, 2677, 2683, 2687, 2689, 2699, 2707, 2711, 2719, 2729, 2791, 2801, 2819,
		2833, 7393, 7411, 7417, 7517, 7523, 7529, 7537, 7541, 7547, 7549, 7559, 7561, 7603, 7607, 7621,
		7699, 7703, 7717, 7723, 7727, 7741, 7753, 7873, 7877, 8009, 8011, 8017, 8039, 8123, 8147, 8161,
		8191, 8209, 8231, 8233, 8237, 8269, 8317, 8353, 8387, 8419, 8431, 8461, 8537, 8563, 8647, 8663,
		8719, 8741, 8783, 8807, 8887, 8999, 9001, 9041, 9091, 9133, 9239, 9337, 9397, 9421, 9431, 9433,
		9491, 9511, 9521, 9533, 9539, 9547, 9551, 9587, 9601, 9613, 9619, 9623, 9629, 9631, 9733, 9739,
		9743, 9749, 9767, 2909, 2917, 3023, 3037, 3119, 3167, 3229, 3373, 3457, 3511, 4049, 4133, 4703,
		5231, 5233, 5303, 5437, 5647, 5801, 5807, 5813, 6047, 6053, 6067, 6211, 6287, 6451, 6529, 7001		
	};

	private final static int COUNT = 1 << 11;
	private final static int MASK = COUNT - 1;

	private final byte[] state = new byte[COUNT];
	private Random slowRnd;
	private Random fastRnd;
	private final int rounds;
	private int getIndex;
	
	public RandomSource2() {
		this.slowRnd = getRandom(true);
		this.fastRnd = getRandom(false);
		this.rounds = 17;
		updateState(state, rounds);
	}
	
	private void updateState(final byte[] state, final int rounds) {
		for (int round=0; round<rounds; ++round) {
			xor(state, fastRnd);
			muldiv(state, FACTOR_TABLE[slowRnd.nextInt() & 0xFF], FACTOR_TABLE[slowRnd.nextInt() & 0xFF]);
			//final int n = random.nextInt();
			//rotate(state, (n >>> 6));
			//shift(state, random.nextInt());
			scramble(state, fastRnd);
			
			if (slowRnd.nextBoolean())
				fastRnd = getRandom(false);
		}
	}

	private static void xor(final byte[] state, final Random random) {
		int i = state.length;
		while (i > 0) {
			int r = random.nextInt();
			state[--i] ^= (byte) r;
			state[--i] ^= (byte) (r >> 8);
			state[--i] ^= (byte) (r >> 16);
			state[--i] ^= (byte) (r >> 24);
		}
	}

	private static void scramble(final byte[] state, final Random random) {
		final int length = state.length;
		for (int i=0; i<length; ++i) {
			final int j = random.nextInt() & MASK;
			final byte b = state[i];
			state[i] = state[j];
			state[j] = b;
		}
	}

	private static Random getRandom(final boolean secure) {
		if (secure) {
			try {
				return SecureRandom.getInstanceStrong();
			} catch (final Exception e) {
				// ignored
			}
		}
		return new Random();
	}

	private static void muldiv(final byte[] state, final int p1, final int p2) {
		final int m, d;
		if (p1 > p2) {
			m = p1; d = p2; 
		} else {
			m = p2; d = p1;
		}
		for (int i=0; i<state.length; ++i) {
			int p = state[i] * m;
			int x = p / d;
			int y = p - (x * d);
			state[i] ^= (byte) (p ^ x ^ y);
		}
	}

	static void rotate(final byte[] array, int order) {
		order = order & 0xFF;
		final int a = 256 - order; 
		reverse(array, 0, a);
		reverse(array, a, 256);
		reverse(array, 0, 256);
	 
	}

	static void reverse(final byte[] array, int left, int right){
		--left;
		while(++left < --right){
			final byte b = array[left];
			array[left] = array[right];
			array[right] = b;
		}	
	}

	// --------------------------------------------------------------
	// ---
	// --- RandomSource implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public synchronized void getBytes(final byte[] buffer, final int offset, final int length) {
		int start = offset;
		int end = offset + length;
		int get = this.getIndex;
		final byte[] state = this.state;
		while (start < end) {
			buffer[start++] = state[--get & MASK];
			if (get < 0) {
				updateState(state, 1);
				get &= MASK;
			}
		}
		this.getIndex = get;
	}

	@Override
	public synchronized void xorBytes(final byte[] buffer, final int offset, final int length) {
		int start = offset;
		int end = offset + length;
		int get = this.getIndex;
		final byte[] state = this.state;
		while (start < end) {
			buffer[start++] ^= state[--get & MASK];
			if (get < 0) {
				updateState(state, 1);
				get &= MASK;
			}
		}
		this.getIndex = get;
	}

	@Override
	public void reseed(final RandomSource other) {
		if (other == null || other == this)
			return;
		final byte[] buffer = new byte[COUNT];
		other.getBytes(buffer);
		reseed(buffer);
	}

	@Override
	public void reseed(final byte[] bytes) {
		if (bytes == null || bytes.length <= 0)
			return;
		synchronized (this) {
			final byte[] state = this.state;
			for (final byte b : bytes) {
				final int index = fastRnd.nextInt() & MASK;
				state[index] ^= b;
			}
			updateState(state, 1);
		}
	}

	// --------------------------------------------------------------
	// ---
	// --- Object overrides
	// ---
	// --------------------------------------------------------------
	@Override
	public String toString() {
		final StringMaker b = StringMaker.obtain(10000);
		final byte[] data = state;
		int col = 0;
		for (final byte d : data) {
			if (col >= 16) {
				b.append('\n');
				col = 0;
			}
			Formats.HEX_FULL.appendTo(b, d);
			b.append(' ');
			++col;
		}
		return b.release();
	}

}

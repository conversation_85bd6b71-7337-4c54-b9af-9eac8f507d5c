package ch.eisenring.core.crypt;

import java.io.IOException;
import java.lang.ref.Cleaner;

import javax.security.auth.DestroyFailedException;
import javax.security.auth.Destroyable;

import ch.eisenring.core.io.StreamReader;
import ch.eisenring.core.io.StreamWriter;
import ch.eisenring.core.io.Streamable;

/**
 * Object use to transport secret (keys) over the network.
 * 
 * The secret transported is not directly usable, as the transport
 * object does not provide the key to decrypt it. That key
 * must be pre-shared or provided by other means.
 */
public final class SecretTransport implements Streamable, Destroyable {

	private static final Cleaner cleaner = Cleaner.create();

	private transient byte[] initVector;
	private transient byte[] secret;

	// Cleanable registered with the cleaner
	private final Cleaner.Cleanable cleanable;

	SecretTransport() {
		this.cleanable = cleaner.register(this, new State(this));
	}

	public SecretTransport(final byte[] secret, final byte[] initVector) {
		this.initVector = initVector.clone();
		this.secret = secret.clone();
		this.cleanable = cleaner.register(this, new State(this));
	}

	// --------------------------------------------------------------
	// ---
	// --- API
	// ---
	// --------------------------------------------------------------
	@Override
	public void destroy() throws DestroyFailedException {
		cleanable.clean(); // trigger cleanup manually
		// Clear the arrays in the main object too
		if (initVector != null) {
			CryptUtil.destroy(initVector);
			initVector = null;
		}
		if (secret != null) {
			CryptUtil.destroy(secret);
			secret = null;
		}
	}

	public byte[] getInitVector() {
		return initVector;
	}

	public byte[] getSecret() {
		return secret;
	}

	// --------------------------------------------------------------
	// ---
	// --- Streamable implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public void read(final StreamReader reader) throws IOException {
		initVector = reader.readBinary();
		secret = reader.readBinary();
	}

	@Override
	public void write(final StreamWriter writer) throws IOException {
		writer.writeBinary(initVector);
		writer.writeBinary(secret);
	}

	// --------------------------------------------------------------
	// ---
	// --- Object overrides
	// ---
	// --------------------------------------------------------------
	// finalize() method removed - now using Cleaner for Java 21 compatibility

	/**
	 * Cleanup logic that securely destroys secret data.
	 */
	private static final class State implements Runnable {
		private byte[] initVector;
		private byte[] secret;

		State(SecretTransport transport) {
			this.initVector = transport.initVector;
			this.secret = transport.secret;
		}

		@Override
		public void run() {
			if (initVector != null) {
				CryptUtil.destroy(initVector);
				initVector = null;
			}
			if (secret != null) {
				CryptUtil.destroy(secret);
				secret = null;
			}
		}
	}
}

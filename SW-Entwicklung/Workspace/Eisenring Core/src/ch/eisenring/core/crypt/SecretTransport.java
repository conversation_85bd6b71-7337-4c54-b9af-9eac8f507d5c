package ch.eisenring.core.crypt;

import java.io.IOException;

import javax.security.auth.DestroyFailedException;
import javax.security.auth.Destroyable;

import ch.eisenring.core.crypt.CryptUtil;
import ch.eisenring.core.io.StreamReader;
import ch.eisenring.core.io.StreamWriter;
import ch.eisenring.core.io.Streamable;
import ch.eisenring.core.resource.ResourceFinalizer;

/**
 * Object use to transport secret (keys) over the network.
 * 
 * The secret transported is not directly usable, as the transport
 * object does not provide the key to decrypt it. That key
 * must be pre-shared or provided by other means.
 */
public final class SecretTransport implements Streamable, Destroyable {

	private transient byte[] initVector;
	private transient byte[] secret;

	// ResourceFinalizer for Java 1.8 and Java 21 compatibility
	private final ResourceFinalizer resourceFinalizer;

	SecretTransport() {
		this.resourceFinalizer = new ResourceFinalizer(this) {
			@Override
			protected void cleanup() {
				try {
					CryptUtil.destroy(SecretTransport.this);
				} catch (Exception e) {
					// Ignore cleanup failures during finalization
				}
			}
		};
	}

	public SecretTransport(final byte[] secret, final byte[] initVector) {
		this.initVector = initVector.clone();
		this.secret = secret.clone();
		this.resourceFinalizer = new ResourceFinalizer(this) {
			@Override
			protected void cleanup() {
				try {
					CryptUtil.destroy(SecretTransport.this);
				} catch (Exception e) {
					// Ignore cleanup failures during finalization
				}
			}
		};
	}

	// --------------------------------------------------------------
	// ---
	// --- API
	// ---
	// --------------------------------------------------------------
	@Override
	public void destroy() throws DestroyFailedException {
		CryptUtil.destroy(initVector);
		CryptUtil.destroy(secret);
		initVector = null;
		secret = null;
	}

	public byte[] getInitVector() {
		return initVector;
	}

	public byte[] getSecret() {
		return secret;
	}

	// --------------------------------------------------------------
	// ---
	// --- Streamable implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public void read(final StreamReader reader) throws IOException {
		initVector = reader.readBinary();
		secret = reader.readBinary();
	}

	@Override
	public void write(final StreamWriter writer) throws IOException {
		writer.writeBinary(initVector);
		writer.writeBinary(secret);
	}

	// --------------------------------------------------------------
	// ---
	// --- Object overrides
	// ---
	// --------------------------------------------------------------
	// finalize() method removed - now using ResourceFinalizer for Java 1.8 and Java 21 compatibility

}

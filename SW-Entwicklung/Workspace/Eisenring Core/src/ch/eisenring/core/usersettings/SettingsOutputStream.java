package ch.eisenring.core.usersettings;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;

import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.io.Streams;
import ch.eisenring.core.io.memory.MemoryOutputStream;

/**
 * Specialized OutputStream for writing settings data.
 * 
 * Basically this will write the data to the given file,
 * but only when close() is called.
 * 
 * Its fine to not close this stream (no file handle
 * will be leaked) in case an error occurs somewhere.
 */
final class SettingsOutputStream extends OutputStream {

	private File file;
	private MemoryOutputStream byteOut = new MemoryOutputStream();

	public SettingsOutputStream(final CharSequence fileName) throws IOException {
		this(new File(Strings.toString(fileName)));
	}
	
	public SettingsOutputStream(final File file) throws IOException {
		this.file = file;
	}
	
	@Override
	public final void write(int b) throws IOException {
		if (byteOut == null)
			throw new IOException("stream closed");
		byteOut.write(b);
	}
	
	@Override
	public final void write(byte[] b) throws IOException {
		if (byteOut == null)
			throw new IOException("stream closed");
		byteOut.write(b);
	}
	
	@Override
	public final void write(byte[] b, int off, int len) throws IOException {
		if (byteOut == null)
			throw new IOException("stream closed");
		byteOut.write(b, off, len);
	}
	
	@Override
	public final void flush() throws IOException {
		if (byteOut == null)
			throw new IOException("stream closed");
		// does nothing for this stream
	}

	@Override
	public final void close() throws IOException {
		if (byteOut == null)
			return;
		OutputStream out = null;
		try {
			final File path = file.getParentFile();
			if (path != null) {
				path.mkdirs();
			}
			out = new FileOutputStream(file);
			byteOut.writeTo(out);
			out.flush();
		} finally {
			byteOut = null;
			Streams.closeSilent(out);
		}
	}

}

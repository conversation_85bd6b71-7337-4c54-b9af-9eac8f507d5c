package ch.eisenring.core.usersettings;

import java.awt.Component;
import java.awt.Frame;
import java.awt.Rectangle;
import java.io.IOException;

import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.io.StreamReader;
import ch.eisenring.core.io.StreamWriter;
import ch.eisenring.core.io.Streamable;

@SuppressWarnings("serial")
final class ComponentBounds extends Rectangle implements Streamable {

	private final static int FORMAT1 = 0xF0000001;
	
	private int extendedState;

	protected ComponentBounds() {
	}

	// --------------------------------------------------------------
	// ---
	// --- Factory methods
	// ---
	// --------------------------------------------------------------
	public static ComponentBounds create(final Rectangle bounds) {
		final ComponentBounds result = new ComponentBounds();
		if (bounds != null) {
			result.x = bounds.x;
			result.y = bounds.y;
			result.width = bounds.width;
			result.height = bounds.height;
			if (bounds instanceof ComponentBounds) {
				result.extendedState = ((ComponentBounds) bounds).extendedState;
			}
		}
		return result;
	}

	public static ComponentBounds create(final Component component) {
		final ComponentBounds result = create(component.getBounds());
		if (component instanceof Frame) {
			result.setExtendedState(((Frame) component).getExtendedState());
		}
		return result;
	}

	// --------------------------------------------------------------
	// ---
	// --- API
	// ---
	// --------------------------------------------------------------
	public int getExtendedState() {
		return extendedState;
	}

	public void setExtendedState(final int extendedState) {
		this.extendedState = extendedState;
	}

	// --------------------------------------------------------------
	// ---
	// --- Streamable implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public void read(final StreamReader reader) throws IOException {
		final int format = reader.readInt();
		switch (format) {
			case FORMAT1:
				this.x = reader.readInt();
				this.y = reader.readInt();
				this.width = reader.readInt();
				this.height = reader.readInt();
				this.extendedState = reader.readInt();
				break;
			default:
				throw new IOException(Strings.concat("unknown ComponentBounds format: 0x", Integer.toHexString(format)));
		}
	}

	@Override
	public void write(final StreamWriter writer) throws IOException {
		writer.writeInt(FORMAT1);
		writer.writeInt(this.x);
		writer.writeInt(this.y);
		writer.writeInt(this.width);
		writer.writeInt(this.height);
		writer.writeInt(this.extendedState);
	}
	
}

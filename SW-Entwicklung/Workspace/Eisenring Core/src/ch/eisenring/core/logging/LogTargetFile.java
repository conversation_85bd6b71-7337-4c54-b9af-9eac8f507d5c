package ch.eisenring.core.logging;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.io.OutputStreamWriter;
import java.io.Writer;
import java.nio.charset.Charset;

import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.io.Streams;
import ch.eisenring.core.platform.Platform;

final class LogTargetFile implements LogTarget {

	private final File file;
	private final Writer writer;
	private boolean closed;

	public LogTargetFile(final CharSequence filename, final Charset charSet) throws IOException {
		final Platform platform = Platform.getPlatform();
		final String platformFileName = platform.replaceCLIVars(filename);
		this.file = new File(platformFileName);
		this.file.getParentFile().mkdirs();
		OutputStream rawStream = new FileOutputStream(file);
		try {
			rawStream = Streams.makeBuffered(rawStream, 4096);
		} catch (final RuntimeException e) {
			Streams.closeSilent(rawStream);
			throw e;
		}
		this.writer = new OutputStreamWriter(rawStream, charSet);
	}

	public File getFile() {
		return file;
	}

	// --------------------------------------------------------------
	// ---
	// --- LogTarget implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public void write(final StringMaker buffer) throws IOException {
		if (closed)
			throw new IOException("stream closed");
		buffer.appendTo(writer);
	}

	@Override
	public void flush() throws IOException {
		if (closed)
			throw new IOException("stream closed");
		writer.flush();
	}

	@Override
	public void close() throws IOException {
		// does not really close, because the wrapped stream should stay open
		if (!closed) {
			closed = true;
			writer.close();
		}
	}

	// --------------------------------------------------------------
	// ---
	// --- Object overrides
	// ---
	// --------------------------------------------------------------
	@Override
	public int hashCode() {
		return file.hashCode();
	}

	@Override
	public boolean equals(final Object o) {
		return o instanceof LogTargetFile && ((LogTargetFile) o).file.equals(file);
	}


}

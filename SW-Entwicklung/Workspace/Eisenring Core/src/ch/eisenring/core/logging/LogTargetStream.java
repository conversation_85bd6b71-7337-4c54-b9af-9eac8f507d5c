package ch.eisenring.core.logging;

import java.io.IOException;
import java.io.OutputStream;
import java.io.OutputStreamWriter;
import java.io.Writer;
import java.nio.charset.Charset;

import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.io.Streams;

final class LogTargetStream implements LogTarget {

	private final OutputStream rawStream;
	private final Writer writer;
	private boolean closed;

	public LogTargetStream(final OutputStream output, final Charset charSet) throws IOException {
		this.rawStream = output;
		OutputStream buffered = Streams.makeBuffered(rawStream, 4096);
		this.writer = new OutputStreamWriter(buffered, charSet);
	}

	// --------------------------------------------------------------
	// ---
	// --- LogTarget implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public void write(final StringMaker buffer) throws IOException {
		if (closed)
			throw new IOException("stream closed");
		buffer.appendTo(writer);
	}

	@Override
	public void flush() throws IOException {
		if (closed)
			throw new IOException("stream closed");
		writer.flush();
	}

	@Override
	public void close() throws IOException {
		// does not really close, because the wrapped stream should stay open
		if (!closed) {
			closed = true;
			writer.flush();
		}
	}

	// --------------------------------------------------------------
	// ---
	// --- Object overrides
	// ---
	// --------------------------------------------------------------
	@Override
	public int hashCode() {
		return rawStream.hashCode();
	}

	@Override
	public boolean equals(final Object o) {
		return o instanceof LogTargetStream && ((LogTargetStream) o).rawStream.equals(rawStream);
	}

}

package ch.eisenring.core.util;

import java.awt.Color;
import java.awt.Component;
import java.awt.EventQueue;
import java.awt.Graphics2D;
import java.awt.RenderingHints;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.util.Iterator;

import javax.imageio.IIOImage;
import javax.imageio.ImageIO;
import javax.imageio.ImageWriteParam;
import javax.imageio.ImageWriter;
import javax.imageio.stream.ImageOutputStream;

import ch.eisenring.core.collections.Map;
import ch.eisenring.core.collections.impl.HashMap;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.io.binary.BinaryHolder;
import ch.eisenring.core.io.binary.BinaryHolderUtil;
import ch.eisenring.core.io.memory.MemoryOutputStream;
import ch.eisenring.core.logging.Logger;

/**
 * Utility class to create ScreenShots of a component
 */
public class ScreenShotUtil {

	private final static int IMAGE_TYPE = BufferedImage.TYPE_INT_RGB;
	
	public final static String FORMAT_PNG = "png";
	public final static String FORMAT_JPG = "jpg";
	
	public static String FILENAME_ALLOWED_CHARS =
		"0123456789" +
		"-_" +
		"abcdefghijklmnopqrstuvwxyz" +
		"ABCDEFGHIJKLMNOPQRSTUVWXYZ";
	
	private final Object LOCK = new Object();
	private double scale = 1D;
	private BufferedImage image;
	private Graphics2D graphics2D;

	public ScreenShotUtil() {
	}
	
	/**
	 * Disposes of all resources currently allocated
	 * by ScreenShots made.
	 */
	public void dispose() {
		synchronized (LOCK) {
			if (graphics2D != null) {
				graphics2D.dispose();
				graphics2D = null;
			}
			if (image != null) {
				image.flush();
				image = null;
			}
		}
	}

	protected BufferedImage createImage(final int width, final int height) {
		synchronized (LOCK) {
			dispose();
			final int w = (int) (width * scale);
			final int h = (int) (height * scale);
			image = new BufferedImage(w, h, IMAGE_TYPE);
			image.setAccelerationPriority(0F);
			return image;
		}
	}

	private final static Map<RenderingHints.Key, Object> RENDERING_HINTS = new HashMap<>();
	static {
		RENDERING_HINTS.put(RenderingHints.KEY_ALPHA_INTERPOLATION, RenderingHints.VALUE_ALPHA_INTERPOLATION_QUALITY);
		RENDERING_HINTS.put(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
		RENDERING_HINTS.put(RenderingHints.KEY_COLOR_RENDERING, RenderingHints.VALUE_COLOR_RENDER_QUALITY);
		RENDERING_HINTS.put(RenderingHints.KEY_FRACTIONALMETRICS, RenderingHints.VALUE_FRACTIONALMETRICS_ON);
		RENDERING_HINTS.put(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BICUBIC);
		RENDERING_HINTS.put(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
		RENDERING_HINTS.put(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
	}

	protected Graphics2D createGraphics() {
		synchronized (LOCK) {
			if (graphics2D == null) {
				if (image != null) {
					graphics2D = image.createGraphics();
					graphics2D.scale(scale,  scale);
					graphics2D.setRenderingHints(RENDERING_HINTS);
					graphics2D.setPaintMode();
					graphics2D.setColor(Color.WHITE);
					graphics2D.setBackground(Color.WHITE);
					graphics2D.fillRect(0, 0, image.getWidth(), image.getHeight());
				}
			}
			return graphics2D;
		}
	}

	/**
	 * Creates a ScreenShot of the given component.
	 * The component should be showing for this to work properly!
	 */
	public BufferedImage screenShot(final Component component) {
		return screenShot(component, 1D);
	}
	
	public BufferedImage screenShot(final Component component, final double scale) {
		if (component == null) {
			return null;
		}
		final Runnable r = new Runnable() {
			@Override
			public void run() {
				ScreenShotUtil.this.scale = scale;
				createImage(component.getWidth(), component.getHeight());
				component.printAll(createGraphics());
			}
		};
		if (EventQueue.isDispatchThread()) {
			r.run();
		} else {
			try {
				EventQueue.invokeAndWait(r);
			} catch (final Exception e) {
				dispose();
				Logger.error(Strings.concat("Problem creating component screenshot: ", e.getMessage()));
				Logger.error(e);
			}
		}
		return image;
	}

	public static BinaryHolder toBinary(final BufferedImage image,
										final String format) throws IOException {
		final MemoryOutputStream memOut = new MemoryOutputStream();
		final ImageOutputStream imgOut = ImageIO.createImageOutputStream(memOut);
		final Iterator<ImageWriter> i = ImageIO.getImageWritersByFormatName(format);
		if (!i.hasNext()) {
			throw new IOException("no image writer for \"" + format + "\" available");
		}
		final ImageWriter writer = i.next();
		writer.setOutput(imgOut);
		final ImageWriteParam param = writer.getDefaultWriteParam();
		//param.setCompressionMode(ImageWriteParam.MODE_EXPLICIT);
		//1.0 specifies minimum compression and maximum quality
		//param.setCompressionQuality(1F);
		try {
			final IIOImage iioImage = new IIOImage(image, null, null);
			writer.write(null, iioImage, param);
        } finally {
            writer.dispose();
            imgOut.flush();
        }
		return BinaryHolderUtil.create(memOut, true);
	}
	
	public String toSuitableFileName(final String string, final String extension) {
		final StringMaker b = StringMaker.obtain(64);
		if (string == null) {
			b.append("Unbenannt");
		} else {
			b.append(replaceNotAllowedChars(string, FILENAME_ALLOWED_CHARS, "_"));
		}
		if (extension != null) {
			if (!extension.startsWith(".")) {
				b.append('.');
			}
			b.append(replaceNotAllowedChars(extension, FILENAME_ALLOWED_CHARS, "_"));
		}
		return b.release();
	}

	/**
	 * Replaces all characters in string which are not in allowed with replacement char.
	 */
	public static String replaceNotAllowedChars(final CharSequence charSeq, final String allowed, final String replacement) {
		if (charSeq == null) {
			return null;
		}
		if (allowed == null)
			return "";
		final int l = charSeq.length();
		final StringMaker b = StringMaker.obtain(l);
		for (int i=0; i<l; ++i) {
			final char c = charSeq.charAt(i);
			if (allowed.indexOf(c) < 0) {
				if (replacement != null) {
					b.append(replacement);
				}
			} else {
				b.append(c);
			}
		}
		return b.release();
	}
	
	/**
	 * Simply scale image by a factor.
	 */
	public static BufferedImage scaleImage(final BufferedImage image, final float factor) {
		if (image == null)
			return image;
		final int w0 = image.getWidth();
		final int h0 = image.getHeight();
		int w1 = Math.round(w0 * factor);
		int h1 = Math.round(h0 * factor);
		if (w1 <= 0) {
			w1 = 1;
		}
		if (h1 <= 0) {
			h1 = 1;
		}
		final BufferedImage result = new BufferedImage(w1, h1, IMAGE_TYPE);
		result.setAccelerationPriority(0F);
		final Graphics2D g = result.createGraphics();
		g.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
		g.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BICUBIC);
		g.drawImage(image, 0, 0, w1, h1, 0, 0, w0, h0, null);
		g.dispose();
		return result;
	}

}

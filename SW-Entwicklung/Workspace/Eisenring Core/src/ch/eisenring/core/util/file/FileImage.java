package ch.eisenring.core.util.file;

import java.io.IOException;

import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.io.AutoStreamable;
import ch.eisenring.core.io.binary.BinaryHolder;
import ch.eisenring.core.sort.Comparator;

/**
 * Common interface for objects representing a file
 */
public interface FileImage extends AutoStreamable {

	/**
	 * Gets the name of the file. This includes possibly a file extension,
	 * and depending on the actual implementation, also a path.
	 */
	public String getFilename();

	/**
	 * Gets the contents of the file.
	 */
	public BinaryHolder getFiledata();
	
	/**
	 * Gets the extension of the file name, excluding the dot. If there
	 * is no extension present in the name or the extension cannot be
	 * determined somehow, returns null.
	 */
	public default String getFileExtension() {
		return FileUtil.getFileExtension(getFilename());
	}
	
	/**
	 * Gets the size of the file. May throw RuntimeException if there is
	 * something wrong with the file image.
	 */
	public default long getFileSize() {
		try {
			return getFiledata().size();
		} catch (final IOException e) {
			throw new RuntimeException(e.getMessage(), e);
		}
	}

	// --------------------------------------------------------------
	// ---
	// --- Factory methods
	// ---
	// --------------------------------------------------------------
	/**
	 * Creates a FileImage using default implementation.
	 * The default implementation is a simple immutable and streamable holder.
	 */
	public static FileImage create(final String fileName, final BinaryHolder fileData) {
		return new FileImageSimple(fileName, fileData);
	}

	/**
	 * Makes an immutable copy of the given FileImage (that is also streamable).
	 * If the argument is already using the default implementation, returns the argument. 
	 */
	public static FileImage create(final FileImage fileImage) {
		if (fileImage == null) {
			return null;
		} else if (fileImage instanceof FileImageSimple) {
			return fileImage;
		} else {
			return new FileImageSimple(fileImage.getFilename(), fileImage.getFiledata());
		}
	}

	/**
	 * Makes immutable copies of all given FileImage (ensure streamable).
	 */
	public static List<FileImage> create(final java.util.Collection<FileImage> fileImages) {
		final ArrayList<FileImage> result = new ArrayList<>(fileImages.size());
		for (final FileImage original : fileImages) {
			final FileImage copy = create(original);
			result.add(copy);
		}
		return result;
	}

	/**
	 * Makes an immutable copy of the given collection for FileImage's.
	 * 
	 * If passed null, returns null. If there are null's in the collection,
	 * nulls are inserted into the result list as well.
	 */
	public static List<FileImage> copySimple(final java.util.Collection<? extends FileImage> fileImages) {
		if (fileImages == null)
			return null;
		final ArrayList<FileImage> result = new ArrayList<>(fileImages.size());
		addAllSimple(fileImages, result);
		return result;
	}
	
	/**
	 * Makes an immutable copy of the given collection into another collection.
	 * 
	 * If fileImages is null, does nothing. If there are null's inside fileImages,
	 * nulls are added to the target collection as well. The target collection
	 * is not cleared before adding.
	 */
	public static void addAllSimple(final java.util.Collection<? extends FileImage> fileImages, final java.util.Collection<FileImage> target) {
		if (fileImages == null)
			return;
		for (final FileImage fileImage : fileImages) {
			target.add(FileImage.create(fileImage));
		}
	}

	// --------------------------------------------------------------
	// ---
	// --- Sorting
	// ---
	// --------------------------------------------------------------
	public static interface Order {
		public final static Comparator<FileImage> Filename = Comparator.wrapNullSafe(
				new Comparator<FileImage>() {
					@Override
					public int compare(final FileImage o1, final FileImage o2) {
						return Strings.compareNatural(o1.getFilename(), o2.getFilename());
					}
				});
		public final static Comparator<FileImage> Filesize = Comparator.wrapNullSafe(
				new Comparator<FileImage>() {
					public int compare(final FileImage o1, final FileImage o2) {
						try {
							return compareUnsigned(o1.getFiledata().size(),  o2.getFiledata().size());
						} catch (final Exception e) {
							return 0;
						}
					}
				});
	}

	// --------------------------------------------------------------
	// ---
	// --- Getter
	// ---
	// --------------------------------------------------------------
//	public static interface Access {
//		public final static Getter<FileImage, String> Filename = new Getter<FileImage, String>() {
//			public String get(final FileImage fileImage) {
//				return fileImage.getFilename();
//			}
//		};
//		public final static Getter<FileImage, Long> Filesize = new Getter<FileImage, Long>() {
//			public Long get(final FileImage fileImage) {
//				try {
//					return fileImage.getFiledata().size();
//				} catch (final Exception e) {
//					return Long.valueOf(0);
//				}
//			}
//		};
//		public final static Getter<FileImage, String> FilesizeText = new Getter<FileImage, String>() {
//			public String get(final FileImage fileImage) {
//				try {
//					final StringMaker b = StringMaker.obtain();
//					Formats.BYTESIZE.appendTo(b, fileImage.getFiledata().size());
//					return b.release();
//				} catch (final Exception e) {
//					return "N/A";
//				}
//			}
//		};
//	}
	
}


//package ch.eisenring.core.util;
//
//import java.math.RoundingMode;
//import java.text.NumberFormat;
//
//import ch.eisenring.core.datatypes.strings.StringMaker;
//import ch.eisenring.core.datatypes.strings.Strings;
//
//public final class GPSUtil {
//
//	private GPSUtil() {
//	}
//
//	/*
//	 	Format(e):
//	 	
//	 	Es gibt verschiedene Methoden der Darstellung, z. B.:
//
//   		1. Grad, Minuten, Sekunden: 66° 43´ 12"
//   		2. Dezimalgrad: 66,7200°
//   		3. Grad, Dezimalminuten: 66° 43,20´
//   		4. Grad, Minuten, Dezimalsekunden: 66° 43´ 12,0"
//	 */
//
//	public static String toStringGrad(Double grad) {
//		if (null==grad || grad.isNaN()) {
//			return "";
//		} else {
//			return toStringGrad(grad.doubleValue());
//		}
//	}
//
//	public static String toStringGrad(double grad) {
//		if (Double.isNaN(grad))
//			return null;
//		NumberFormat nf = NumberFormat.getNumberInstance();
//		nf.setGroupingUsed(false);
//		nf.setMinimumIntegerDigits(1);
//		nf.setMaximumIntegerDigits(10);
//		nf.setMinimumFractionDigits(15);
//		nf.setMaximumFractionDigits(15);
//		nf.setRoundingMode(RoundingMode.CEILING);
//		return nf.format(grad);
//	}
//	
//	public static String toStringBGMS(double grad) {
//		if (Double.isNaN(grad)) {
//			return "";
//		}
//		char prefix;
//		if (grad<0D) {
//			prefix = 'S';
//			grad = -grad;
//		} else {
//			prefix = 'N';
//		}
//		return prefix+toStringGMS(grad);
//	}
//
//	public static String toStringLGMS(double grad) {
//		if (Double.isNaN(grad)) {
//			return "";
//		}
//		char prefix;
//		if (grad<0D) {
//			prefix = 'W';
//			grad = -grad;
//		} else {
//			prefix = 'E';
//		}
//		return prefix+toStringGMS(grad);
//	}
//
//	private static void integerAppend(final StringMaker b, int i, int d) {
//		String s = Strings.toString(i);
//		d -= s.length();
//		while (d>0) {
//			b.append(0);
//			--d;
//		}
//		b.append(s);
//	}
//	
//	private static String toStringGMS(double grad) {
//		int iGrad = (int) StrictMath.floor(grad);
//		grad -= iGrad;
//		grad *= 60D;
//		int iMinuten = (int) StrictMath.floor(grad);
//		grad -= iMinuten;
//		grad *= 60D;
//		int iSekunden = (int) StrictMath.floor(grad);
//		grad -= iSekunden;
//		grad *= 100000D;
//		int iDecimal = (int) StrictMath.floor(grad);
//	
//		StringMaker b = StringMaker.obtain(20);
//		integerAppend(b, iGrad, 2);
//		b.append('°');
//		integerAppend(b, iMinuten, 2);
//		b.append('\'');
//		integerAppend(b, iSekunden, 2);
//		b.append('.');
//		integerAppend(b, iDecimal, 5);
//		b.append('"');
//		return b.release();
//	}
//	
//	public static double parseBreite(final CharSequence s) {
//		int i = 0;
//		char c = getCharAt(s, i);
//		boolean sign = false;
//		switch (c) {
//			case 'n':
//			case 'N':
//			case '+':
//				++i;
//				break;
//			case 's':
//			case 'S':
//			case '-':
//				++i;
//				sign = true;
//				break;
//			default:
//				break;
//		}
//		double grad = parseGrad(s, i);
//		if (Double.isNaN(grad) || grad<0D || grad>90D) {
//			return Double.NaN;
//		} else {
//			if (sign)
//				grad = -grad;
//			return grad;
//		}
//	}
//	
//	public static double parseLaenge(final CharSequence s) {
//		int i = 0;
//		char c = getCharAt(s, i);
//		boolean sign = false;
//		switch (c) {
//			case 'e':
//			case 'E':
//			case '+':
//				++i;
//				break;
//			case 'w':
//			case 'W':
//			case '-':
//				++i;
//				sign = true;
//				break;
//			default:
//				break;
//		}
//		double grad = parseGrad(s, i);
//		if (Double.isNaN(grad) || grad<0D || grad>180D) {
//			return Double.NaN;
//		} else {
//			if (sign)
//				grad = -grad;
//			return grad;
//		}
//	}
//
//	private static double parseGrad(final CharSequence s, int i) {
//		try {
//			char c;
//			
//			// gradzahl parsen
//			String grad = parseNumber(s, i);
//			if (null==grad) {
//				// keine gültige zahl
//				return Double.NaN;
//			}
//			
//			i += grad.length();
//			if (isDecimalNumber(grad) || i==s.length()) {
//				// dezimalgrad, es darf nichts weiter kommen
//				if (i<s.length()) {
//					// fehler, es kommt noch was...
//					return Double.NaN;
//				}
//				return Double.parseDouble(grad);
//			} else {
//				c = getCharAt(s, i);
//				++i;
//				if ('°'==c) {
//					String minuten = parseNumber(s, i);
//					if (null==minuten) {
//						// keine gültige zahl
//						return Double.NaN;
//					}
//					
//					i += minuten.length();
//					if (isDecimalNumber(minuten) || i==s.length()) {
//						// dezimalminuten, es darf nichts weiter kommen
//						if (i<s.length()) {
//							// fehler, es kommt noch was...
//							return Double.NaN;
//						}
//						
//						return (Double.parseDouble(minuten) / 60D)
//						     +  Double.parseDouble(grad);
//					} else {
//						c = getCharAt(s, i);
//						++i;
//						if ('\''==c || '´'==c || '`'==c) {
//							// es folgen sekunden
//							String sekunden = parseNumber(s, i);
//							if (null==sekunden) {
//								// keine gültige zahl
//								return Double.NaN;
//							}
//							
//							i+= sekunden.length();
//							if (i<s.length()) {
//								c = getCharAt(s, i);
//								if ('"'==c) {
//									++i;
//								} else {
//									// unerwartetes zeichen...
//									return Double.NaN;
//								}
//							}
//							return (Double.parseDouble(sekunden) / 3600D)
//							     + (Double.parseDouble(minuten) / 60D)
//								 +  Double.parseDouble(grad);
//						} else {
//							// fehler: sekunden-zeichen erwartet...
//							return Double.NaN;
//						}
//					}
//						
//				} else {
//					// fehler: grad-zeichen erwartet...
//					return Double.NaN;
//				}
//			}
//		} catch (Exception e) {
//			return Double.NaN;
//		}
//	}
//
//	private static boolean isDecimalNumber(final CharSequence s) {
//		return Strings.indexOf(s, '.') >= 0;
//	}
//
//	private static String parseNumber(final CharSequence s, int i) {
//		--i;
//		char c;
//		final StringMaker b = StringMaker.obtain();
//		boolean hasDecimal = false;
//		while (true) {
//			++i;
//			c = getCharAt(s, i);
//			switch (c) {
//				case '0':
//				case '1':
//				case '2':
//				case '3':
//				case '4':
//				case '5':
//				case '6':
//				case '7':
//				case '8':
//				case '9':
//					b.append(c);
//					break;
//				case '.':
//				case ',':
//					if (hasDecimal || b.length()<=0) {
//						// fehler: doppeltes dezimaltrennzeichen oder keine
//						// ziffer(n) vor dezimaltrennzeichen
//						return null;
//					}
//					hasDecimal = true;
//					b.append('.');
//					break;
//				default:
//					return b.release((String) null);
//			}
//		}
//	}
//
//	private static char getCharAt(final CharSequence s, int i) {
//		if (null==s || i>=s.length())
//			return 0;
//		return s.charAt(i);
//	}
//
//}

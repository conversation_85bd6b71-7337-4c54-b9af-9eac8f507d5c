package ch.eisenring.core.util;

import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;

import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.Lookup;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.collections.impl.AtomicArrayLookup;
import ch.eisenring.core.datatypes.primitives.Primitives;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.iterators.Filter;

/**
 * Utility class to perform simple tasks using reflection
 */
public abstract class ReflectionUtil {

	protected ReflectionUtil() {
	}

	/**
	 * Calls a specified instance method by reflection.
	 * Fails silently (returns NULL) if the method is not found
	 * or any other error condition occurs.
	 */
	public static Object call(final Object instance,
			                  final CharSequence methodName,
			                  final Class<?>[] signature,
			                  final Object[] args) {
		try {
			final String methodNameString = Strings.toString(methodName);
			if (instance == null)
				return null;
			Class<?> instanceClass = instance.getClass();
			while (instanceClass != null) {
				try {
					final Method method = instanceClass.getDeclaredMethod(methodNameString, signature);
					if (!isMethodAccessible(method))
						method.setAccessible(true);
					return method.invoke(instance, args);
				} catch (final NoSuchMethodException e) {
					// ignore
				}
				instanceClass = instanceClass.getSuperclass();
			}
		} catch (final Throwable e) {
			return null;
		}
		return null;
	}

	// --------------------------------------------------------------
	// ---
	// --- Introspection
	// ---
	// --------------------------------------------------------------
	public static List<Field> getDeclaredFields(final Class<?> theClass, final boolean includeSuperFields, final Filter<Field> filter) {
		final ArrayList<Field> result = new ArrayList<>();
		Class<?> currentClass = theClass;
		do {
			final Field[] fields = currentClass.getDeclaredFields();
			for (final Field field : fields) {
				if (filter == null || filter.accepts(field)) {
					if (!isFieldAccessible(field))
						field.setAccessible(true);
					result.add(field);
				}
			}
			currentClass = currentClass.getSuperclass();
		} while (currentClass != null && includeSuperFields);
		result.trimToSize();
		return result;
	}

	@SuppressWarnings("unchecked")
	public static <U> List<U> getStaticFieldValues(final Class<?> theClass, final boolean includeSuperFields, final Class<U> type) {
		final ArrayList<U> result = new ArrayList<>();
		Class<?> currentClass = theClass;
		do {
			final Field[] fields = currentClass.getDeclaredFields();
			for (final Field field : fields) {
				if (!Modifier.isStatic(field.getModifiers()))
					continue;
				if (!type.isAssignableFrom(field.getType()))
					continue;
				if (!isFieldAccessible(field))
					field.setAccessible(true);
				try {
					final U value = (U) field.get(null);
					result.add(value);
				} catch (final Exception e) {
					throw new RuntimeException(e.getMessage(), e);
				}
			}
			currentClass = currentClass.getSuperclass();
		} while (currentClass != null && includeSuperFields);
		result.trimToSize();
		return result;
	}

	// --------------------------------------------------------------
	// ---
	// --- Constructor access
	// ---
	// --------------------------------------------------------------
	@SuppressWarnings("unchecked")
	final static Lookup<Class<?>, Constructor<?>> DEFAULT_CONSTRUCTORS = new AtomicArrayLookup<>();

	/**
	 * Gets the default constructor for class. This will find a parameterless
	 * constructor for class, regardless of declared access type.
	 * The constructor is set accessible if it is not already accessible.
	 */
	@SuppressWarnings("unchecked")
	public static <T> Constructor<T> getDefaultConstructor(final Class<T> theClass) throws NoSuchMethodException {
		Constructor<T> result = (Constructor<T>) DEFAULT_CONSTRUCTORS.get(theClass);
		if (result == null) {
			result = getDefaultConstructorImpl(theClass);
			DEFAULT_CONSTRUCTORS.put(theClass, result);
		}
		return result;
	}
	
	private static <T> Constructor<T> getDefaultConstructorImpl(final Class<T> theClass) throws NoSuchMethodException {
		try {
			final Constructor<T> constructor = theClass.getDeclaredConstructor(Primitives.EMPTY_CLASS_ARRAY);
			if (constructor == null)
				throw new NoSuchMethodException(Strings.concat("no default constructor for ", theClass, " found"));
			if (!isConstructorAccessible(constructor))
				constructor.setAccessible(true);
			return constructor;
		} catch (final SecurityException e) {
			throw new NoSuchMethodException(e.getMessage());
		}
	}

	/**
	 * Creates a new instance of class (using the default constructor)
	 */
	public static <T> T newInstance(final Class<T> theClass) throws Exception {
		final Constructor<T> constructor = getDefaultConstructor(theClass);
		return constructor.newInstance(Primitives.EMPTY_OBJECT_ARRAY);
	}

	/**
	 * Creates a new instance of class, using a constructor that matches the
	 * given constructor arguments.
	 */
	@SuppressWarnings("unchecked")
	public static <B, C extends B> B newInstance(final Class<C> theClass, final Object ... args) throws Exception {
		if (args == null || args.length == 0)
			return newInstance(theClass);
		final Constructor<?>[] constructors = theClass.getDeclaredConstructors();
CLoop:	for (final Constructor<?> constructor : constructors) {
			// number of parameters matches?
			if (constructor.getParameterCount() != args.length)
				continue CLoop;
			// parameter types compatible?
			final Class<?>[] parameterTypes = constructor.getParameterTypes();
			for (int i=0; i<args.length; ++i) {
				final Object arg = args[i];
				final Class<?> parameterType = parameterTypes[i];
				if (arg == null) {
					if (parameterType.isPrimitive())
						continue CLoop;
				}
				final Class<?> argType = arg.getClass();
				if (!parameterType.isAssignableFrom(argType))
					continue CLoop;
			}
			// types are compatible
			if (!isConstructorAccessible(constructor))
				constructor.setAccessible(true);
			// do it
			return (B) constructor.newInstance(args);
		}
		throw new NoSuchMethodException("No constructor matching arguments");
	}

	// --------------------------------------------------------------
	// ---
	// --- Java 21 Compatibility Helper Methods
	// ---
	// --------------------------------------------------------------

	/**
	 * Checks if a field is accessible, compatible with both Java 8 and Java 21.
	 * Uses canAccess() for Java 9+ and falls back to isAccessible() for Java 8.
	 */
	public static boolean isFieldAccessible(Field field) {
		try {
			// Try Java 9+ canAccess() method first
			if (Modifier.isStatic(field.getModifiers())) {
				return field.canAccess(null);
			} else {
				// For instance fields, check if the field is public
				return Modifier.isPublic(field.getModifiers()) &&
				       Modifier.isPublic(field.getDeclaringClass().getModifiers());
			}
		} catch (NoSuchMethodError e) {
			// Fall back to deprecated isAccessible() for Java 8
			try {
				Method isAccessibleMethod = Field.class.getMethod("isAccessible");
				return (Boolean) isAccessibleMethod.invoke(field);
			} catch (Exception ex) {
				// If both fail, assume not accessible
				return false;
			}
		} catch (Exception e) {
			// If canAccess() throws an exception, assume not accessible
			return false;
		}
	}

	/**
	 * Checks if a method is accessible, compatible with both Java 8 and Java 21.
	 * Uses canAccess() for Java 9+ and falls back to isAccessible() for Java 8.
	 */
	public static boolean isMethodAccessible(Method method) {
		try {
			// Try Java 9+ canAccess() method first
			if (Modifier.isStatic(method.getModifiers())) {
				return method.canAccess(null);
			} else {
				// For instance methods, check if the method is public
				return Modifier.isPublic(method.getModifiers()) &&
				       Modifier.isPublic(method.getDeclaringClass().getModifiers());
			}
		} catch (NoSuchMethodError e) {
			// Fall back to deprecated isAccessible() for Java 8
			try {
				Method isAccessibleMethod = Method.class.getMethod("isAccessible");
				return (Boolean) isAccessibleMethod.invoke(method);
			} catch (Exception ex) {
				// If both fail, assume not accessible
				return false;
			}
		} catch (Exception e) {
			// If canAccess() throws an exception, assume not accessible
			return false;
		}
	}

	/**
	 * Checks if a constructor is accessible, compatible with both Java 8 and Java 21.
	 * Uses canAccess() for Java 9+ and falls back to isAccessible() for Java 8.
	 */
	public static boolean isConstructorAccessible(Constructor<?> constructor) {
		try {
			// Try Java 9+ canAccess() method first
			return constructor.canAccess(null);
		} catch (NoSuchMethodError e) {
			// Fall back to deprecated isAccessible() for Java 8
			try {
				Method isAccessibleMethod = Constructor.class.getMethod("isAccessible");
				return (Boolean) isAccessibleMethod.invoke(constructor);
			} catch (Exception ex) {
				// If both fail, assume not accessible
				return false;
			}
		} catch (Exception e) {
			// If canAccess() throws an exception, assume not accessible
			return false;
		}
	}

}

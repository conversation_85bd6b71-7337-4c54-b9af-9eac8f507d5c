package ch.eisenring.core.util.file;

import java.io.File;
import java.util.concurrent.TimeUnit;

import ch.eisenring.core.logging.Logger;
import ch.eisenring.core.platform.Platform;
import ch.eisenring.core.platform.PlatformPath;

public final class FileWalker {

	public static interface EntryHandler {

		public void handleFile(final File file);
		
		public void handleDirectory(final File directory);

	}

	public static class DeleteOlderThanEntryHandler implements EntryHandler {
		private final long timeLimit;

		public DeleteOlderThanEntryHandler(final long maxAgeMillis) {
			this.timeLimit = System.currentTimeMillis() - maxAgeMillis;
		}
		
		@Override
		public void handleFile(final File file) {
			if (file.lastModified() > timeLimit)
				return;
			if (Logger.isTraceEnabled())
				Logger.trace("deleting FILE: {}", file.getAbsolutePath());
			file.setWritable(true, false);
			file.delete();
		}

		@Override
		public void handleDirectory(final File directory) {
			if (Logger.isTraceEnabled())
				Logger.trace("deleting DIRECTORY: {}", directory.getAbsolutePath());
			directory.delete();
		}
	}
	
	/**
	 * Recursively walks the directory and invokes the given
	 * EntryHandler for each entry found.
	 */
	public static void walkDirectory(final File directory, final EntryHandler entryHandler) {
		if (directory == null)
			return;
		// handle directory contents
		final File[] contents = directory.listFiles();
		if (contents != null && contents.length > 0) {
			for (final File entry : contents) {
				if (entry.isFile()) {
					entryHandler.handleFile(entry);
				} else if (entry.isDirectory()) {
					walkDirectory(entry, entryHandler);
					entryHandler.handleDirectory(entry);
				}
			}
		}
	}

	/**
	 * Cleans system TEMP directory of anything more than the specified time
	 * in the past.
	 */
	public static void cleanSystemTemp(final long duration, final TimeUnit sourceUnit) {
		cleanSystemTemp(TimeUnit.MILLISECONDS.convert(duration, sourceUnit));
	}

	private static void cleanSystemTemp(final long maxAgeMillis) {
		final Platform platform = Platform.getPlatform();
		final File tempPath = platform.getPath(PlatformPath.APP_TEMP);
		final EntryHandler handler = new DeleteOlderThanEntryHandler(maxAgeMillis);
		walkDirectory(tempPath, handler);
	}

}

package ch.eisenring.core.jdbc;

import java.io.IOException;

import ch.eisenring.core.datatypes.primitives.Primitives;
import ch.eisenring.core.io.StreamReader;
import ch.eisenring.core.io.StreamWriter;
import ch.eisenring.core.io.Streamable;
import ch.eisenring.core.sort.Comparator;

/**
 * Associates ChangeState with an Object
 */
public final class ObjectState<T> implements Streamable {

	private transient T object;
	private transient ChangeState state;

	public ObjectState(final T object, final ChangeState state) {
		if (object == null || state == null)
			throw new NullPointerException();
		this.object = object;
		this.state = state;
	}

	static class OSComparator<X> extends Comparator<ObjectState<X>> {
		private final java.util.Comparator<X> comparator;
		
		public OSComparator(final java.util.Comparator<X> comparator) {
			this.comparator = comparator;
		}
		
		@Override
		public int compare(final ObjectState<X> s1, final ObjectState<X> s2) {
			if (s1 == null)
				return s2 == null ? -1 : 0;
			if (s2 == null)
				return 1;
			return comparator.compare(s1.getObject(), s2.getObject());
		}
	};
	
	@SuppressWarnings("unchecked")
	public static <U> Comparator<ObjectState<U>> getComparator(final java.util.Comparator<U> comparator) {
		return (Comparator<ObjectState<U>>) new OSComparator(comparator);
	}

	// --------------------------------------------------------------
	// ---
	// --- API
	// ---
	// --------------------------------------------------------------
	public T getObject() {
		return object;
	}

	public ChangeState getState() {
		return state;
	}

	public void modify() {
		state = state.modify();
	}

	public void delete() {
		state = state.delete();
	}

	public ChangeState persist() {
		return (state = state.persist());
	}

	// --------------------------------------------------------------
	// ---
	// --- Streamable
	// ---
	// --------------------------------------------------------------
	@SuppressWarnings("unchecked")
	@Override
	public void read(final StreamReader reader) throws IOException {
		object = (T) reader.readObject();
		state = reader.readCode(ChangeState.class);
	}

	@Override
	public void write(final StreamWriter writer) throws IOException {
		writer.writeObject(object);
		writer.writeCode(state);
	}

	// --------------------------------------------------------------
	// ---
	// --- Object overrides
	// ---
	// --------------------------------------------------------------
	@Override
	public int hashCode() {
		return object.hashCode();
	}

	@Override
	public boolean equals(final Object o) {
		return o instanceof ObjectState && Primitives.equals(((ObjectState<?>) o).object, object);
	}

}

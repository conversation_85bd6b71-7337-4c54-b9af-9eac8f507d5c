package ch.eisenring.core.cache;

import java.lang.ref.SoftReference;

import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.sort.Comparator;

/**
 * Very simple cache with timeout.
 * 
 * In principle this is just a soft references map with limited capacity.
 */
public class SimpleSoftCache<K, V> {

	static class CacheEntry {
		final Object key;
		SoftReference<Object> item;
		long timeout;
		
		public CacheEntry(final Object key, final Object item, final long timeout) {
			this.key = key;
			this.item = new SoftReference<>(item);
			this.timeout = timeout;
		}

		public boolean matchesKey(final Object key) {
			return this.key == null ? key == null : this.key.equals(key);
		}
	
		public boolean isValid() {
			return item.get() != null && timeout > System.currentTimeMillis();
		}
	}

	private final static Comparator<CacheEntry> ORDER = new Comparator<CacheEntry>() {
		@Override
		public int compare(final CacheEntry e1, final CacheEntry e2) {
			return compareSigned(e2.timeout, e1.timeout);
		}
	};

	private int capacity;
	private long validMillis;
	private final List<CacheEntry> cacheList;

	public SimpleSoftCache(final int capacity, final long validMillis) {
		this.capacity = capacity;
		this.validMillis = validMillis;
		this.cacheList = new ArrayList<>(capacity);
	}
	
	private Object getLock() {
		return cacheList;
	}

	// --------------------------------------------------------------
	// ---
	// --- API
	// ---
	// --------------------------------------------------------------
	public void put(final K key, final V value) {
		if (value == null)
			return;
		final List<CacheEntry> list = this.cacheList;
		synchronized (getLock()) {
			// check if key already exists
			CacheEntry entry = null;
			for (int i=list.size()-1; i>=0; --i) {
				final CacheEntry e = list.get(i);
				if (e.matchesKey(key)) {
					// update this entry
					entry = e;
					break;
				}
			}
			final long timeout = System.currentTimeMillis() + validMillis;
			if (entry == null) { // create new entry ?
				trimTo(capacity - 1);
				entry = new CacheEntry(key, value, timeout);
				list.add(entry);
			} else { // update existing entry
				entry.timeout = timeout;
				entry.item = new SoftReference<Object>(value);
				trimTo(capacity);
			}
		}
	}

	/**
	 * Gets the cached value for key or NULL if no cached value 
	 */
	@SuppressWarnings("unchecked")
	public V get(final K key) {
		final List<CacheEntry> list = this.cacheList;
		synchronized (getLock()) {
			for (int i=list.size()-1; i>=0; --i) {
				final CacheEntry e = list.get(i);
				if (!e.isValid()) {
					list.remove(i);
				} else if (e.matchesKey(key)) {
					return (V) e.item.get();
				}
			}
		}
		return null;
	}

	// --------------------------------------------------------------
	// ---
	// --- Cache trimming (Expired item removal and capacity compliance)
	// ---
	// --------------------------------------------------------------
	/**
	 * Ensure to synchronize when calling this method
	 */
	private void trimTo(final int size) {
		final List<CacheEntry> list = this.cacheList;
		if (list.isEmpty())
			return;
		// check for expired entries and remove these entries
		for (int i=list.size() - 1; i>=0; --i) {
			final CacheEntry e = list.get(i);
			// remove expired/invalid entry
			if (!e.isValid())
				list.remove(i);
		}
		// check for excess items 
		if (list.size() > size) {
			list.sort(ORDER);
			while (list.size() > size) {
				// drop oldest item
				list.removeLast();
			}
		}
	}

}

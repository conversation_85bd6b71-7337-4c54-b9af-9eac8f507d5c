package ch.eisenring.core.codetable;

public interface SeverityChecker {

	/**
	 * Determines if protocol is considered "successful".
	 */
	public default boolean isSuccess(final Protocol protocol) {
		return getFirstError(protocol) == null;
	}

	/**
	 * Gets the first non-success message from protocol
	 */
	public default ProtocolEntry getFirstError(final Protocol protocol) {
		for (final ProtocolEntry result : protocol) {
			final ErrorMessage message = result.getMessage();
			if (isSuccess(message))
				continue;
			return result;
		}
		return null;
	}

	/**
	 * Determines if message is considered "successful".
	 */
	public default boolean isSuccess(final ErrorMessage message) {
		return message != null && isSuccess(message.getSeverity());
	}

	/**
	 * Determines if severity is considered "successful".
	 */
	public abstract boolean isSuccess(final MessageClassCode severity);

	/**
	 * Default protocol checker, considers all not-Ok messages as "Error".
	 */
	public final static SeverityChecker DEFAULT = new SeverityChecker() {
		@Override
		public boolean isSuccess(final MessageClassCode severity) {
			return MessageClassCode.OK.equals(severity);
		}
	};

	/**
	 * Lenient protocol checker, considers only "Error" severity as "Error".
	 */
	public final static SeverityChecker LENIENT = new SeverityChecker() {
		@Override
		public boolean isSuccess(final MessageClassCode severity) {
			return !MessageClassCode.ERROR.equals(severity);
		}
	};

}

package ch.eisenring.core.codetable;

import ch.eisenring.core.codetype.StaticCode;

public final class SoftwareType extends StaticCode {

	public final static SoftwareType NULL = new SoftwareType(0, null, "Unbekannt", "Unbekannt");

	public final static SoftwareType SERVICE = new SoftwareType(1, "Dienst");
	public final static SoftwareType SERVER = new SoftwareType(2, "Server");
	public final static SoftwareType APPLICATION = new SoftwareType(3, "Anwendung");
	public final static SoftwareType COMPONENT = new SoftwareType(4, "Komponente");
	
	private SoftwareType(final int id, final String text) {
		this(id, Integer.valueOf(id), text, text);
	}

	private SoftwareType(final int id, final Object key,
			             final String shortText, final String longText) {
		super(id, key, shortText, longText);
	}

}

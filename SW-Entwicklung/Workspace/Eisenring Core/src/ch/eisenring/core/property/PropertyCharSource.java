package ch.eisenring.core.property;

import java.util.Iterator;
import java.util.NoSuchElementException;

import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.iterators.EmptyIterator;

/**
 * PropertyCharSource is an abstract representation of something that provides characters. CharSources can
 * be constructed for anything that is string-alike. Charcter based parsers etc. should always work
 * on a PropertyCharSource, so the parser can be used on any input data required.
 * <p>Title: WPF (Web Publishing Framework) </p>
 * <p>Description: </p>
 * <p>Copyright: Copyright (c) 2004-2008</p>
 * <p>Company: SpennBerg GmbH</p>
 * <AUTHOR>
 * @version 1.1
 */
public abstract class PropertyCharSource extends Object {

    /**
     * Special PropertyCharSource used for empty source object. The NULL_SOURCE is always empty and returns always
     * a NullIterator, avoiding unneccessary construction of CharSources.
     */
    private final static PropertyCharSource NULL_SOURCE = new CharSourceCharSequence("");

    /** Template method for the length() method - child classes implement this according to the source of the characters. */
    public abstract int length();

    /** Template method for the charAt() method - child classes implement this according to the source of the characters. */
    public abstract char charAt(int pIndex);

    /** Returns an Iterator of the CharSources characters. The iterator returns java.lang.Character objects. */
    public final Iterator<Character> iterator() {
        return length() == 0 ? EmptyIterator.get(Character.class) : new CharSourceIterator(this);
    }

    public static PropertyCharSource create(final CharSequence charSeq) {
    	return Strings.length(charSeq) <= 0 ? NULL_SOURCE : new CharSourceCharSequence(charSeq);
    }

    protected static class CharSourceIterator implements Iterator<Character> {
        private PropertyCharSource mSource;
        private int mPosition = 0;
        public CharSourceIterator(PropertyCharSource pSource) { mSource = pSource; }
        public Character next() {
            if (mPosition>=mSource.length()) {
                throw new NoSuchElementException();
            }
            final char c = mSource.charAt(mPosition++);
            return Character.valueOf(c);
        }
        public boolean hasNext() { return mPosition<mSource.length(); }
        public void remove() { throw new UnsupportedOperationException(); }
    }

    protected static class CharSourceCharSequence extends PropertyCharSource {
    	private final CharSequence charSeq;
    	public CharSourceCharSequence(final CharSequence charSeq) {
    		this.charSeq = charSeq;
    	}
    	@Override
    	public int length() {
    		return charSeq.length();
    	}
    	@Override
    	public char charAt(final int index) {
    		return charSeq.charAt(index);
    	}
    }

}
package ch.eisenring.core.csv;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.Reader;
import java.nio.charset.Charset;

import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.io.Streams;
import ch.eisenring.core.io.file.FileItem;
import ch.eisenring.core.logging.Logger;

/**
 * Simple CSV file reader
 */
public class CSVReader {

	protected CSVReader() {
	}

	public static CSVFile readCSV(final String fileName, final char columnSeparator) {
		final FileItem file = FileItem.create(fileName);
		return readCSV(file, columnSeparator);
	}

	public static CSVFile readCSV(final FileItem file, final char columnSeparator) {
		InputStream inputStream = null;
		try {
			inputStream = file.getInputStream();
			return readCSV(inputStream, columnSeparator);
		} catch (final FileNotFoundException e) {
			Logger.error(Strings.concat("Can't find CSV File: \"", file.getAbsolutePath(), "\""));
		} catch (final IOException e) {
			Logger.error(Strings.concat("Can't read CSV File: \"", file.getAbsolutePath(), "\""));
		} finally {
			if (inputStream != null) {
				try {
					inputStream.close();
				} catch (IOException e) {
				}
			}
		}
		return null;
	}

	public static CSVFile readCSV(final InputStream inputStream, char columnSeparator) {
		return readCSV(inputStream, (Charset) null, columnSeparator);
	}

	public static CSVFile readCSV(final InputStream inputStream, final Charset charset, final char columnSeparator) {
		CSVOptions options = new CSVOptions();
		options.charset = charset;
		options.columnSeparator = columnSeparator;
		return readCSV(inputStream, options);
	}
	
	public static CSVFile readCSV(final InputStream inputStream, final CSVOptions options) {
		try {
			final CSVFile csvFile = new CSVFile(options);
			final InputStream bufferedStream = Streams.makeBuffered(inputStream, 32768);
			final Charset charset = options.charset;
			final InputStreamReader streamReader;
			if (charset == null) {
				streamReader = new InputStreamReader(bufferedStream);
			} else {
				streamReader = new InputStreamReader(bufferedStream, charset);
			}
			StringMaker line = readLine(streamReader, options);
			while (line != null) {
				csvFile.addLine(line);
				line.releaseSilent();
				line = readLine(streamReader, options);
			}
			return csvFile;
		} catch (final IOException e) {
			Logger.error(Strings.concat("Error reading CSV stream: ", e.getMessage()));
			Logger.error(e);
			return null;
		}
	}

	protected static StringMaker readLine(final Reader reader, final CSVOptions options) throws IOException {
		final StringMaker b = StringMaker.obtain(256);
		final char quoteChar = options.getQuote();
		boolean inQuote = false;
		while (true) {
			final int c = reader.read();
			if (c < 0) { // end of stream?
				if (b.length() <= 0) {
					b.releaseSilent();
					return null;
				} else {
					break;
				}
			}
			if (inQuote) {
				// inside quote, no control character semantics
				b.append((char) c);
			} else if (c == 10) {
				// LF = end of line
				break;
			} else if (c == 13) {
				// simply skip any CR
				continue;
			} else {
			    b.append((char) c);
			}
			if (c == quoteChar)
				inQuote = !inQuote;
		}
		return b;
	}

}

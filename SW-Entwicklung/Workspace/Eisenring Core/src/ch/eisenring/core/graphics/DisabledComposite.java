package ch.eisenring.core.graphics;

import java.awt.Composite;
import java.awt.CompositeContext;
import java.awt.RenderingHints;
import java.awt.image.ColorModel;

/**
 * Composite (for use in Graphics2D) that makes all rendering appear
 * in "disabled UI style". 
 */
public class DisabledComposite implements Composite {

	public final static Composite INSTANCE = new DisabledComposite();

	private DisabledComposite() {
	}

	@Override
	public CompositeContext createContext(
			final ColorModel srcColorModel,
			final ColorModel dstColorModel,
			final RenderingHints hints) {
		return new DisabledCompositeContext(srcColorModel, dstColorModel);
	}
	
}

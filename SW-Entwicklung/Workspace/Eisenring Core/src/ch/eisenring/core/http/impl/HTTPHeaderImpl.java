package ch.eisenring.core.http.impl;

import java.io.IOException;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;

import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.http.HTTPField;
import ch.eisenring.core.http.HTTPHeader;
import ch.eisenring.core.io.Streams;

public class HTTPHeaderImpl<T extends HTTPField> implements HTTPHeader<T> {

	protected final T field;
	protected final String value;

	public HTTPHeaderImpl(final T field, final CharSequence value) {
		this.field = field;
		this.value = value == null ? "" : Strings.toString(value);
	}

	// --------------------------------------------------------------
	// ---
	// --- HTTPHeader implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public final HTTPField getField() {
		return field;
	}
	
	@Override
	public String getValue() {
		return value;
	}
	
	@Override
	public void write(final OutputStream output) throws IOException {
		field.write(output);
		output.write(HTTPConstants.B3A20);
		Streams.copy(value, StandardCharsets.US_ASCII, output);
		output.write(HTTPConstants.B0D0A);
	}

	// --------------------------------------------------------------
	// ---
	// --- Object overrides
	// ---
	// --------------------------------------------------------------
	@Override
	public int hashCode() {
		return (field.hashCode() * 73) ^ Strings.hashCode(value);
	}

	@Override
	public boolean equals(final Object o) {
		if (o instanceof HTTPHeader) {
			
		}
		return false;
	}

	@Override
	public String toString() {
		return field.toString() + ": " + value;
	}

}

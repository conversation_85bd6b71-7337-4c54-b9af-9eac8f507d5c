package ch.eisenring.core.http.server;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.concurrent.atomic.AtomicReference;

import ch.eisenring.core.collections.Map;
import ch.eisenring.core.collections.Set;
import ch.eisenring.core.collections.impl.HashMap;
import ch.eisenring.core.collections.impl.HashSet;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.http.HTTPRequest;
import ch.eisenring.core.http.HTTPResponse;
import ch.eisenring.core.http.HTTPStatus;
import ch.eisenring.core.logging.Logger;

public class URIDispatcher {

	private final AtomicReference<Map<String, URIHandler>> fastPath =
			new AtomicReference<>(new HashMap<>());

	private final AtomicReference<Set<URIHandler>> handlers =
			new AtomicReference<>(new HashSet<>());

	/**
	 * Adds a handler to this instance
	 */
	public void addHandler(final URIHandler handler) {
		if (handler == null)
			return;
		final AtomicReference<Set<URIHandler>> handlers = this.handlers;
		while (true) {
			final Set<URIHandler> expect = handlers.get();
			if (expect.contains(handler))
				return;
			final Set<URIHandler> update = new HashSet<>(expect);
			update.add(handler);
			if (handlers.compareAndSet(expect, update))
				return;
		}
	}

	private void setFastPath(final String uri, final URIHandler handler) {
		final AtomicReference<Map<String, URIHandler>> fastPath = this.fastPath;
		while (true) {
			final Map<String, URIHandler> expect = fastPath.get();
			final Map<String, URIHandler> update = new HashMap<>(expect);
			expect.put(uri, handler);
			if (fastPath.compareAndSet(expect, update))
				return;
		}
	}

	public void dispatch(final HTTPRequest request, final HTTPResponse response) {
		// decode the URI
		final URI uri;
		try {
			uri = new URI(request.getURI());
		} catch (final URISyntaxException e) {
			// invalid URI, return 400 Bad Request
			response.setStatus(HTTPStatus.S400);
			return;
		}
		final String path = Strings.toLower(uri.getPath());
		// has parameters?
		final String lookupPath;
		{
			final int i = Strings.indexOf(path, '&');
			lookupPath = i >= 0
					? Strings.subString(path, 0, i + 1) : path;
		}
		
		// try the fast lookup path 
		URIHandler handler = fastPath.get().get(lookupPath);
		if (handler == null) {
			// try the slow lookup path
			for (final URIHandler h : handlers.get()) {
				if (h.accepts(path)) {
					handler = h;
					break;
				}
			}
			if (handler != null) {
				// add the handler to the fast path
				setFastPath(lookupPath, handler);
			}
		}

		if (handler == null) {
			// handler not found, return 404
			response.setStatus(HTTPStatus.S404);
			return;
		} else {
			// call the handler
			try {
				final RequestContext callContext = new RequestContext(request, response);
				final MethodHandler methodHandler = handler.getMethodHandler(callContext); 
				methodHandler.handle(callContext);
			} catch (final Exception e) {
				// handler error, return 400 Bad Request
				Logger.error(e);
				response.setStatus(HTTPStatus.S400);
			}
		}
	}

}

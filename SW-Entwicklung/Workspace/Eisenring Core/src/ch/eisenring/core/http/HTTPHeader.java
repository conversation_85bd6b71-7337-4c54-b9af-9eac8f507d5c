package ch.eisenring.core.http;

import java.io.IOException;
import java.io.OutputStream;

public interface HTTPHeader<T extends HTTPField> {

	/**
	 * Gets the field of the header line
	 */
	public HTTPField getField();

	/**
	 * Gets the raw value of the header line
	 */
	public String getValue();

	/**
	 * Writes this header to output stream
	 */
	public void write(final OutputStream output) throws IOException;

}

import java.io.File;
import java.util.ArrayList;

public class Starter {

	public static void main(final String[] argv) {
		final File exeFile = new File("Eisenring-DMS.exe");
		final File absFile = exeFile.getAbsoluteFile();
		final String exePath = absFile.getAbsolutePath();
		final ArrayList<String> args = new ArrayList<String>();
		args.add(exePath);
		final ProcessBuilder pb = new ProcessBuilder(args);
		for (int i=0; i<argv.length; ++i) {
			args.add(argv[i]);
		}
		pb.directory(absFile.getParentFile());
		try {
			pb.start();
		} catch (final Exception e) {
			throw new RuntimeException(e.getMessage(), e);
		}
	}

}

package ch.eisenring.edi;

public enum SeparatorType {

	COMPONENT {
		@Override
		public char get<PERSON>haracter(final UNA una) {
			return una.COMPONENT_ELEMENT_SEPARATOR;
		}
	},
	DATA {
		@Override
		public char get<PERSON>haracter(final UNA una) {
			return una.DATA_ELEMENT_SEPARATOR;
		}
	},
	SEGMENT {
		@Override
		public char getCharacter(final UNA una) {
			return una.SEGMENT_TERMINATOR;
		}
	};

	/**
	 * Gets the separator character used for this separator type.
	 */
	public abstract char get<PERSON>haracter(final UNA una);

}

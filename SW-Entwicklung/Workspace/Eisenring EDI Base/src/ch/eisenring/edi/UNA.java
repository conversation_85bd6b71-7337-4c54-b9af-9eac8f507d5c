package ch.eisenring.edi;

import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.Strings;

/**
 * Class used to store the UNA characters used in a message.
 * 
 * Note: UNA is not treated as normal Segment.
 * UNA instances are immutable and can be shared across parsers/threads.
 */
public final class UNA implements Cloneable {

	public final char COMPONENT_ELEMENT_SEPARATOR;
	public final char DATA_ELEMENT_SEPARATOR;
	public final char DECIMAL_NOTIFICATION;
	public final char RELEASE_CHARACTER;
	public final char RESERVED;
	public final char SEGMENT_TERMINATOR;	

	private final String string; 
	
	private final static CharSequence NAME = "UNA";
	private final static UNA DEFAULT_INSTANCE = new UNA(":+.? '");
	
	/**
	 * Creates UNA with the specified characters
	 */
	protected UNA(final CharSequence characters) {
		if (characters == null || characters.length() < 6)
			throw new IllegalArgumentException(Strings.concat("invalid character specfication: \"", characters, "\""));
		this.COMPONENT_ELEMENT_SEPARATOR = Strings.charAt(characters, 0);
		this.DATA_ELEMENT_SEPARATOR = Strings.charAt(characters, 1);
		this.DECIMAL_NOTIFICATION = Strings.charAt(characters, 2);
		this.RELEASE_CHARACTER = Strings.charAt(characters, 3);
		this.RESERVED = Strings.charAt(characters, 4);
		this.SEGMENT_TERMINATOR = Strings.charAt(characters, 5);
		if (RESERVED != ' ')
			throw new IllegalArgumentException("reserved character must be a space (0x20)");
		// pre-build toString() representation
		final StringMaker maker = StringMaker.obtain();
		maker.append(NAME);
		maker.append(COMPONENT_ELEMENT_SEPARATOR);
		maker.append(DATA_ELEMENT_SEPARATOR);
		maker.append(DECIMAL_NOTIFICATION);
		maker.append(RELEASE_CHARACTER);
		maker.append(RESERVED);
		maker.append(SEGMENT_TERMINATOR);
		this.string = maker.release();
	}

	// --------------------------------------------------------------
	// ---
	// --- Factory methods
	// ---
	// --------------------------------------------------------------
	public static UNA getInstance(final CharSequence unaCharacters) {
		if (Strings.startsWith(unaCharacters, ":+.? '"))
			return DEFAULT_INSTANCE;
		return new UNA(unaCharacters);
	}

	public static UNA getInstance() {
		return DEFAULT_INSTANCE;
	}

	// --------------------------------------------------------------
	// ---
	// --- API
	// ---
	// --------------------------------------------------------------
	/**
	 * Returns the name of this segment
	 */
	public CharSequence getName() {
		return NAME;
	}

	/**
	 * Returns true if the given character needs escaping
	 * (character equals the escape character or one of the
	 * three terminator characters)
	 */
	public boolean requiresEscape(final char c) {
		return COMPONENT_ELEMENT_SEPARATOR == c
		    || DATA_ELEMENT_SEPARATOR == c
		    || RELEASE_CHARACTER == c
		    || SEGMENT_TERMINATOR == c;
	}

	// --------------------------------------------------------------
	// ---
	// --- Cloning
	// ---
	// --------------------------------------------------------------
	@Override
	public UNA clone() throws CloneNotSupportedException {
		// is immutable, return self
		return this;
	}

	// --------------------------------------------------------------
	// ---
	// --- Object overrides
	// ---
	// --------------------------------------------------------------
	@Override
	public int hashCode() {
		return string.hashCode();
	}
	
	@Override
	public boolean equals(final Object o) {
		return o instanceof UNA && ((UNA) o).string.equals(string);
	}

	@Override
	public String toString() {
		return string;
	}
	
}

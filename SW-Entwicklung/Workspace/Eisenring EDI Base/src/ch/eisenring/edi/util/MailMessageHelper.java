package ch.eisenring.edi.util;

import javax.mail.Address;
import javax.mail.Message;

import java.util.Date;

/**
 * Returns javax.mail.Message properties without causing any exceptions
 */
public class MailMessageHelper {

    public static Address[] getFrom(Message m) {
        try {
            return m.getFrom();
        } catch (Exception e) {
            return new Address[0];
        }
    }

    public static Address[] getAllRecipients(Message m) {
        try {
            return m.getAllRecipients();
        } catch (Exception e) {
            return new Address[0];
        }
    }

    public static Date getSentDate(Message m) {
        try {
            return m.getSentDate();
        } catch (Exception e) {
            return null;
        }
    }

    public static String getSubject(Message m) {
        try {
            return m.getSubject();
        } catch (Exception e) {
            return null;
        }
    }

}

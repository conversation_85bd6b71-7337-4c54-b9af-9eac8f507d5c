package ch.eisenring.dispo.server.auftragbewertung;

import ch.eisenring.logiware.code.pseudo.AbwicklungsartCode;
import ch.eisenring.logiware.code.soft.LWGranit1Code;
import ch.eisenring.logiware.code.soft.LWGranit2Code;
import ch.eisenring.logiware.code.soft.LWGranit3Code;
import ch.eisenring.logiware.code.soft.LWGranit4Code;
import ch.eisenring.logiware.code.soft.LWGranit5Code;
import ch.eisenring.logiware.code.soft.LWGranitCCode;
import ch.eisenring.lw.model.navigable.LWAuftrag;
import ch.eisenring.lw.model.navigable.LWAuftragKopf;

public final class AuftragBewertungAccessorLW implements AuftragBewertungAccessor<LWAuftrag> {

	public final static AuftragBewertungAccessor<LWAuftrag> INSTANCE = new AuftragBewertungAccessorLW();

	@Override
	public AbwicklungsartCode getAbwicklungsArt(final LWAuftrag auftrag) {
		return auftrag.getAbwicklungsart();
	}

	@Override
	public LWGranit1Code getGranit1Code(final LWAuftrag auftrag) {
		final LWAuftragKopf kopf = auftrag.getAuftragKopf();
		return kopf == null ? null : kopf.getGranit1();
	}

	@Override
	public LWGranit2Code getGranit2Code(final LWAuftrag auftrag) {
		final LWAuftragKopf kopf = auftrag.getAuftragKopf();
		return kopf == null ? null : kopf.getGranit2();
	}
	
	@Override
	public LWGranit3Code getGranit3Code(final LWAuftrag auftrag) {
		final LWAuftragKopf kopf = auftrag.getAuftragKopf();
		return kopf == null ? null : kopf.getGranit3();
	}

	@Override
	public LWGranit4Code getGranit4Code(final LWAuftrag auftrag) {
		final LWAuftragKopf kopf = auftrag.getAuftragKopf();
		return kopf == null ? null : kopf.getGranit4();
	}

	@Override
	public LWGranit5Code getGranit5Code(final LWAuftrag auftrag) {
		final LWAuftragKopf kopf = auftrag.getAuftragKopf();
		return kopf == null ? null : kopf.getGranit5();
	}

	@Override
	public LWGranitCCode getGranitCCode(final LWAuftrag auftrag) {
		final LWAuftragKopf kopf = auftrag.getAuftragKopf();
		return kopf == null ? null : kopf.getGranitC();
	}

}

package ch.eisenring.dispo.server.entities;

import ch.eisenring.dispo.shared.model.AbstractDocument;
import ch.eisenring.dispo.shared.model.AbstractModel;

public abstract class AbstractDocumentEntity extends AbstractEntity {

	private int byteSize;
	private long binaryId;
	private String fileName;

	public long getBinaryId() {
		return binaryId;
	}

	public void setBinaryId(long binaryId) {
		this.binaryId = binaryId;
	}

	public int getByteSize() {
		return byteSize;
	}

	public void setByteSize(int byteSize) {
		this.byteSize = byteSize;
	}

	public String getFileName() {
		return fileName;
	}

	public void setFileName(String fileName) {
		this.fileName = fileName;
	}

	@Override
	public void updateEntity(AbstractModel model) {
		super.updateEntity(model);
		final AbstractDocument document = (AbstractDocument) model;
		updateDirty(getByteSize(), document.getByteSize());
		setByteSize(document.getByteSize());
		updateDirty(getFileName(), document.getFileName());
		setFileName(document.getFileName());
		updateDirty(getBinaryId(), document.getBinaryId());
		setBinaryId(document.getBinaryId());
	}
	
	@Override
	public void updateModel(AbstractModel model) {
		super.updateModel(model);
		final AbstractDocument document = (AbstractDocument) model;
		document.setByteSize(getByteSize());
		document.setFileName(getFileName());
		document.setBinaryId(getBinaryId());
	}
	
	@Override
	public void hookDelete(EntityTransactionContext transaction) {
		super.hookDelete(transaction);
		// delete the binary associated with this entity
		// since we don't have a reference to the object
		// and we don't want to load it (it might be 
		// several MB heavy), we make a fresh instance and
		// set the PK on the object. Hibernate doesn't care
		// about the instances attributes on deletes, as
		// long as the primary key is set.
		final BinaryEntity binary = new BinaryEntity();
		binary.setId(Long.valueOf(getBinaryId()));
		transaction.delete(binary);
	}
	
}

package ch.eisenring.dispo.server.poi;

import java.io.IOException;
import ch.eisenring.core.poi.POIUtil;
import org.apache.poi.ss.usermodel.Workbook;
import ch.eisenring.app.shared.ServiceLocator;
import ch.eisenring.app.shared.ServiceNotFoundException;
import ch.eisenring.app.shared.services.DataFileService;
import ch.eisenring.core.io.binary.BinaryHolder;
import ch.eisenring.core.resource.DataFileSource;
import ch.eisenring.core.util.file.FileImage;
import ch.eisenring.core.util.file.FileUtil;

public class ServerPOIUtil {

	/**
	 * Loads existing XLSX workbook from data files
	 */
	public static Workbook loadWorkbook(final ServiceLocator server, final String fileName) throws ServiceNotFoundException, IOException {
		final DataFileService service = server.locateService(DataFileService.class);
		final DataFileSource source = service.getDataFileSource();
		final BinaryHolder binary = source.getAsBinary(fileName);
		final FileImage fileImage = FileImage.create(FileUtil.getFileName(fileName), binary);
		return POIUtil.loadWorkbook(fileImage);
	}

}
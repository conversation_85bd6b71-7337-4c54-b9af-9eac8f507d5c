package ch.eisenring.dispo.server.ticklisteners;

import java.sql.SQLException;
import java.time.Duration;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;

import ch.eisenring.app.shared.CoreTickListenerAdapter;
import ch.eisenring.core.collections.Map;
import ch.eisenring.core.collections.impl.HashMap;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.threading.ThreadCore;
import ch.eisenring.dispo.server.DSPServer;
import ch.eisenring.jdbc.JDBCResultSet;
import ch.eisenring.jdbc.RowHandler;
import ch.eisenring.jdbc.StatementParameters;
import ch.eisenring.logiware.LWContextSource;
import ch.eisenring.logiware.LWProjektKey;
import ch.eisenring.logiware.code.pseudo.AbwicklungsartCode;
import ch.eisenring.logiware.code.soft.GSECode;
import ch.eisenring.logiware.code.soft.LWAuftragStatusCode;
import ch.eisenring.logiware.code.soft.LWMonteurSirCode;
import ch.eisenring.logiware.util.LWEndmontageUtil;
import ch.eisenring.lw.LWConstants;
import ch.eisenring.model.TransactionContext;

public final class DSPDerivedDataCache extends CoreTickListenerAdapter {

	public final static String FEATURE_NAME = "DSPDerivedDataCache";
	
	private final static long UPDATE_INTERVAL = Duration.ofMinutes(60).toMillis();

	private final DSPServer server;

	public DSPDerivedDataCache(final DSPServer server) {
		super(server, FEATURE_NAME, ASYNCHRONOUS);
		this.server = server;
	}

	static class DerivedData {
		public LWMonteurSirCode kuechenMonteur = LWMonteurSirCode.NULL;
		public int anzahlEndmontagen = 0;
	}

	// --------------------------------------------------------------
	// ---
	// --- API
	// ---
	// --------------------------------------------------------------
	private final AtomicReference<Map<LWProjektKey, DerivedData>> cachedMap = new AtomicReference<>();

	private Map<LWProjektKey, DerivedData> getMap() {
		int delay = 10;
		while (true) {
			Map<LWProjektKey, DerivedData> map = cachedMap.get();
			if (map != null)
				return map;
			// Busy loop. Its ugly, but simple
			ThreadCore.sleep(delay);
			delay = Math.max(delay << 1, 2000);
		}
	}

	/**
	 * Gets cached Anzahl Endmontagen
	 */
	public int getAnzahlEndmontagen(final LWProjektKey key) {
		final LWProjektKey basisKey = key.getCanonicalBasisobjektKey();
		final DerivedData data = getMap().get(basisKey);
		return data == null ? 0 : data.anzahlEndmontagen;
	}
	
	/**
	 * Gets Küchenmonteur for Projekt
	 */
	public LWMonteurSirCode getKuechenMonteur(final LWProjektKey key) {
		final LWProjektKey basisKey = key.getCanonicalBasisobjektKey();
		final DerivedData data = getMap().get(basisKey);
		return data == null ? LWMonteurSirCode.NULL : data.kuechenMonteur;
	}

	// --------------------------------------------------------------
	// ---
	// --- Timing management
	// ---
	// --------------------------------------------------------------
	private final AtomicLong NEXT_DUE_TIMESTAMP = new AtomicLong(Long.MIN_VALUE);

	/**
	 * Controls if this listener is due.
	 * The default implementation simply returns true.
	 */
	@Override
	protected boolean isDue(final long now) {
		return now > NEXT_DUE_TIMESTAMP.get();
	}

	// --------------------------------------------------------------
	// ---
	// --- Internal implementation
	// ---
	// --------------------------------------------------------------
	@Override
	protected void tickImpl(final long now) {
		try {
			final Map<LWProjektKey, DerivedData> map = getImpl(server);
			cachedMap.set(map);
			NEXT_DUE_TIMESTAMP.set(now + UPDATE_INTERVAL);
		} catch (final Exception e) {
		}
	}

	private static Map<LWProjektKey, DerivedData> getImpl(final DSPServer server) throws Exception {
		final Map<LWProjektKey, DerivedData> resultMap = new HashMap<>();
		updateAnzahlEndmontagen(server, resultMap);
		updateKuechenMonteur(server, resultMap);
		return resultMap;
	}

	private static void updateAnzahlEndmontagen(final DSPServer server, final Map<LWProjektKey, DerivedData> dataMap) throws Exception {
		final LWContextSource source = new LWContextSource(LWConstants.VERTRIEB) {
			@Override
			protected void loadImpl(final TransactionContext context) throws SQLException {
				final StatementParameters params = new StatementParameters();
				final StringMaker sql = StringMaker.obtain();
				sql.append("SELECT");
				sql.append(" A.PAuftrNr, A.Id_GSE_PA, F.FormDatum");
				sql.append(" FROM AUFTRAG A " + WITH_NOLOCK + ", FORMULAR F " + WITH_NOLOCK);
				sql.append(" WHERE");
				sql.append(" A.AbwArt = ?");
				params.add(LWEndmontageUtil.EM_ABWICKLUNGSART);
				sql.append(" AND A.Status NOT IN (?)");
				params.add(LWAuftragStatusCode.STORNIERT);
				sql.append(" AND A.AuftrNr = F.AuftrNr AND A.Id_GSE = F.Id_GSE");
				sql.append(" AND F.FormArt = ?");
				params.add(LWEndmontageUtil.EM_FORMULARART);
				sql.append(" ORDER BY A.PAuftrNr, A.Id_GSE_PA, F.FormDatum");
				
				final AtomicReference<LWProjektKey> lastKey = new AtomicReference<>(LWProjektKey.INVALID);
				final AtomicLong lastDate = new AtomicLong();
				doQuery(sql.release(), params, new RowHandler() {
					@Override
					public void handleRow(final JDBCResultSet resultSet) throws SQLException {
						final String projektnummer = resultSet.getString(1);
						final GSECode gse = resultSet.getCode(2, GSECode.class);
						final LWProjektKey basisKey;
						try {
							basisKey = LWProjektKey.get(LWProjektKey.getCanonicalBasisobjektNummer(projektnummer), gse);
						} catch (final IllegalArgumentException e) {
							return;
						}
						final long date = LWEndmontageUtil.getCanonicalEMDate(resultSet.getLongTimestamp(3));
						// 24h rule
						if (basisKey.equals(lastKey.get()) && date == lastDate.get()) {
							return;
						}
						lastKey.set(basisKey);
						lastDate.set(date);
						DerivedData data = dataMap.get(basisKey);
						if (data == null) {
							data = new DerivedData();
							dataMap.put(basisKey, data);
						}
						data.anzahlEndmontagen++;
					}
				});
			}
		};
		final TransactionContext context = new TransactionContext();
		context.load(source);
	}

	private static void updateKuechenMonteur(final DSPServer server, final Map<LWProjektKey, DerivedData> dataMap) throws Exception {
		final LWContextSource source = new LWContextSource(LWConstants.VERTRIEB) {
			@Override
			protected void loadImpl(final TransactionContext context) throws SQLException {
				final StatementParameters params = new StatementParameters();
				final StringMaker sql = StringMaker.obtain();
				sql.append("SELECT");
				sql.append(" A.PAuftrNr, A.Id_GSE_PA, K.MonteurSir");
				sql.append(" FROM AUFTRAG A " + WITH_NOLOCK + ", AuftragsKopfZusatz K " + WITH_NOLOCK);
				sql.append(" WHERE");
				sql.append(" A.AbwArt = ?");
				params.add(AbwicklungsartCode.AWA135);
				sql.append(" AND A.Status NOT IN (?)");
				params.add(LWAuftragStatusCode.STORNIERT);
				sql.append(" AND A.AuftrNr = K.AuftrNr AND A.Id_GSE = K.Id_GSE");
				doQuery(sql.release(), params, (resultSet) -> {
					final String projektnummer = resultSet.getString(1);
					final GSECode gse = resultSet.getCode(2, GSECode.class);
					final LWProjektKey basisKey;
					try {
						basisKey = LWProjektKey.get(LWProjektKey.getCanonicalBasisobjektNummer(projektnummer), gse);
					} catch (final IllegalArgumentException e) {
						return;
					}
					final LWMonteurSirCode monteur = resultSet.getCode(3, LWMonteurSirCode.class);
					DerivedData data = dataMap.get(basisKey);
					if (data == null) {
						data = new DerivedData();
						dataMap.put(basisKey, data);
					}
					data.kuechenMonteur = monteur;
				});
			}
		};
		final TransactionContext context = new TransactionContext();
		context.load(source);
	}

}

package ch.eisenring.dispo.server.ticklisteners;

import ch.eisenring.app.shared.CoreProvider;
import ch.eisenring.app.shared.CoreTickListenerAdapter;
import ch.eisenring.app.shared.SoftwareComponent;
import ch.eisenring.core.datatypes.date.TimeOfDay;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.jdbc.ConnectionInfo;
import ch.eisenring.logiware.dataimport.CodeImport;
import ch.eisenring.lw.LWConstants;

/**
 * Forces full GC in regular intervals
 */
public final class LWCodeCacheUpdater extends CoreTickListenerAdapter {

	private long nextDueTimestamp = Long.MIN_VALUE;
	
	public LWCodeCacheUpdater(final CoreProvider coreProvider) {
		super(coreProvider, "LWCodeCacheUpdater");
	}

	@Override
	protected boolean isDue(final long now) {
		if (now < nextDueTimestamp)
			return false;
		nextDueTimestamp = selectNextDueTimestamp(now);
		return true;
	}

	@Override
	protected void tickImpl(final long now) {
		try {
			loadCodes(false);
		} catch (final Exception e) {
			// ignore (will try again soon)
			nextDueTimestamp = Math.min(nextDueTimestamp, System.currentTimeMillis() + 300_000);
		}
	}

	public static void loadCodes(final SoftwareComponent server) throws Exception {
		LWCodeCacheUpdater updater = server.getFeatureLookup().getFeature(LWCodeCacheUpdater.class);
		if (updater == null)
			throw new IllegalStateException("LWCodeCaseUpdater: feature not found");
		updater.loadCodes(true);
	}

	
	/**
	 * Attempts to load/update codes from Logiware.
	 */
	private void loadCodes(final boolean initialLoad) throws Exception {
		// create import
		final ConnectionInfo info = getBasisConnectionInfo();
		final CodeImport codeImport = new CodeImport(info);
		
		// now try to load the codes...
		try {
			codeImport.loadCodes();
		} catch (final Exception e) {
			Logger.error("Problem loading code tables from Logiware:");
			Logger.error(e);
			if (initialLoad)
				throw e;
		}
		Logger.info("Loaded codes from Logiware");
	}

	private static ConnectionInfo getBasisConnectionInfo() throws Exception {
		final ConnectionInfo result = LWConstants.BASIS.getConnectionInfo();
		if (result == null)
			throw new NullPointerException("Connection info for BASIS not available");
		return result;
	}

	/**
	 * Determines the timestamp when the next GC should be forced.
	 */
	long selectNextDueTimestamp(final long now) {
		TimeOfDay[] times = null;
		long next = now + 86_400_000;
		try {
			// get current configuration
			final ConnectionInfo info = LWConstants.BASIS.getConnectionInfo();
			final String list = info.getProperty("CodeUpdateTimes");
			times = TimeOfDay.parseList(list, ',');
			next = (info.getPropertyInteger("CodeUpdateInterval", 86400) * 1000) + now;
		} catch (final Exception e) {
			// ignore parsing errors
		}
		return selectNextDueTimestamp(now, times, next);
	}

}

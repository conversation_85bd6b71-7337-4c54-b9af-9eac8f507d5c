package ch.eisenring.dispo.server.network;

import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.util.file.FileImage;
import ch.eisenring.dispo.server.DSPServer;
import ch.eisenring.dispo.shared.network.packets.PacketGetDocumentsReply;
import ch.eisenring.dispo.shared.network.packets.PacketGetDocumentsRequest;
import ch.eisenring.dms.service.DMSService;
import ch.eisenring.dms.service.api.DMSDocumentImage;
import ch.eisenring.dms.service.codetables.DMSDocumentType;
import ch.eisenring.dms.service.codetables.DMSFolderType;
import ch.eisenring.dms.service.proxy.DMSFolderHandle;
import ch.eisenring.network.PacketSink;
import ch.eisenring.network.packet.AbstractPacket;

final class GetDocumentsRequestHandler extends AbstractDSPPacketHandler {

	public GetDocumentsRequestHandler(final DSPServer server) {
		super(server, PacketGetDocumentsRequest.class);
	}

	public void handle(final AbstractPacket abstractPacket, final PacketSink sink) {
		final PacketGetDocumentsRequest request = (PacketGetDocumentsRequest) abstractPacket;
		PacketGetDocumentsReply reply;

		try {
			// first, locate the DMS service
			final DMSService service;
			service = server.locateService(DMSService.class);

			// prepare parameters for call to service
			final int codeId = request.getDocumentType().getId();
			final String projectnumber = request.getProjectnumber();

			// request the documents from the service
			final List<DMSDocumentImage> documents;
			if (codeId >= 0) {
				final DMSDocumentType documentType = AbstractCode.getById(codeId, DMSDocumentType.class);
				documents = service.getDocumentImages(projectnumber, documentType);
			} else {
				final DMSFolderType folderType = AbstractCode.getById(-codeId, DMSFolderType.class);
				final DMSFolderHandle folder = service.getProjectContentFolder(projectnumber, folderType);
				if (folder == null) {
					documents = new ArrayList<DMSDocumentImage>();
				} else {
					documents = service.getDocumentImages(folder, DMSDocumentType.NULL);
				}
			}

			// prepare the reply packet
			final List<FileImage> files = new ArrayList<FileImage>();
			files.addAll(documents);
			reply = PacketGetDocumentsReply.create(request, files);
		} catch (final Exception e) {
			reply = PacketGetDocumentsReply.create(request, new ErrorMessage(e));
		}

		sink.sendPacket(reply, false);
	}

}

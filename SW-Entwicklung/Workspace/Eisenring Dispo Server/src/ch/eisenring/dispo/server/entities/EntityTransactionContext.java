package ch.eisenring.dispo.server.entities;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import ch.eisenring.core.collections.Map;
import ch.eisenring.core.collections.impl.HashMap;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.core.logging.Logger.LogLevel;
import ch.eisenring.dispo.server.hibernate.HibernateUtil;

final class EntityTransactionContext extends HibernateUtil {

	private final Map<AbstractEntity, StoreAction> actionMap = new HashMap<>(256);
	private final long contextDate;
	private final EntityContext context;
	private final boolean eventLogEnabled;
	
	private final static StoreAction INSERT = new StoreAction(1, "INSERT");
	private final static StoreAction UPDATE = new StoreAction(2, "UPDATE");
	private final static StoreAction DELETE = new StoreAction(3, "DELETE");

	protected EntityTransactionContext(final EntityContext context) {
		this.context = context;
		this.contextDate = this.context.getModelContext().getBegin();
		this.eventLogEnabled = Boolean.TRUE.equals(context.getServer().EVENTLOG_ENABLED.get());
	}
	
	public void insert(final AbstractEntity entity) {
		final StoreAction sa = actionMap.get(entity);
		if (DELETE.equals(sa)) {
			// delete takes precedence over insert
		} else {
			actionMap.put(entity, INSERT);
		}
	}
	
	public void update(final AbstractEntity entity) {
		final StoreAction sa = actionMap.get(entity);
		if (DELETE.equals(sa)) {
			// delete takes precedence over update
		} else if (INSERT.equals(sa)) {
			// insert takes precedence over update
		} else {
			actionMap.put(entity, UPDATE);
		}
	}

	public void delete(final AbstractEntity entity) {
		// delete takes precedence over any action
		actionMap.put(entity, DELETE);
	}

	/**
	 * Commits this transaction
	 */
	public void commit(boolean reallyCommit) throws Exception {
		if (actionMap.isEmpty())
			return;
		Iterator<AbstractEntity> i;
		List<AbstractEntity> l;
		boolean rollback = true;
		transactionStart();
		try {
			// perform INSERTS
			l = new ArrayList<AbstractEntity>(actionMap.keySet()); 
			i = l.iterator();
			while (i.hasNext()) {
				final AbstractEntity entity = i.next();
				final StoreAction action = actionMap.get(entity);
				if (INSERT.equals(action)) {
					session.save(entity);
					if (eventLogEnabled)
						session.save(new EventLogEntry(entity, contextDate, "INSERT"));
				}
			}
	
			// perform UPDATES
			l = new ArrayList<AbstractEntity>(actionMap.keySet()); 
			i = l.iterator();
			while (i.hasNext()) {
				final AbstractEntity entity = i.next();
				final StoreAction action = actionMap.get(entity);
				if (UPDATE.equals(action)) {
					session.update(entity);
					if (eventLogEnabled)
						session.save(new EventLogEntry(entity, contextDate, "UPDATE"));
				}
			}
	
			// perform DELETES
			l = new ArrayList<AbstractEntity>(actionMap.keySet()); 
			i = l.iterator();
			while (i.hasNext()) {
				final AbstractEntity entity = i.next();
				final StoreAction action = actionMap.get(entity);
				if (DELETE.equals(action)) {
					// give the entity a chance to perform cascading deletes
					entity.hookDelete(this);
					session.delete(entity);
					if (eventLogEnabled)
						session.save(new EventLogEntry(entity, contextDate, "DELETE"));
				}
			}
			
			if (reallyCommit) {
				transactionCommit();
				rollback = false;
			} else {
				Logger.log(LogLevel.INFO, "rolling back transaction on application request (debug-mode?)");
			}
		} finally {
			if (rollback) {
				transactionRollback();
			}
		}
	}

}

final class StoreAction {
	
	private final int id;
	private final String name;

	public StoreAction(final int id, final String name) {
		this.id = id;
		this.name = name;
	}
	
	@Override
	public boolean equals(final Object o) {
		return o instanceof StoreAction && ((StoreAction) o).id == id;
	}
	
	@Override
	public int hashCode() {
		return id;
	}
	
	@Override
	public String toString() {
		return name;
	}

}

package ch.eisenring.dispo.server.ticklisteners;

import static ch.eisenring.dispo.shared.DSPConstants.MONT_PRESENTO_ID;
import static ch.eisenring.dispo.shared.DSPConstants.MONT_SHORTNAME;
import static ch.eisenring.dispo.shared.codetables.EmploymentRole.MITARBEITER_CHAUFEUR;
import static ch.eisenring.dispo.shared.codetables.EmploymentRole.MITARBEITER_MONTEUR;
import static ch.eisenring.dispo.shared.codetables.ZuordnungCode.ZUORDNUNG_EMPLOYMENT;

import java.util.Iterator;

import ch.eisenring.app.shared.timing.PeriodicTimer;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.datatypes.date.DateGranularityCode;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.iterators.Filter;
import ch.eisenring.dispo.server.DSPServer;
import ch.eisenring.dispo.server.model.ServerContextCache;
import ch.eisenring.dispo.server.model.ServerModelContext;
import ch.eisenring.dispo.shared.DSPConstants;
import ch.eisenring.dispo.shared.codetables.EinAusCode;
import ch.eisenring.dispo.shared.codetables.EmploymentRole;
import ch.eisenring.dispo.shared.model.AbstractMitarbeiter;
import ch.eisenring.dispo.shared.model.AbstractModel;
import ch.eisenring.dispo.shared.model.FeinZuordnung;
import ch.eisenring.dispo.shared.model.MasterMitarbeiter;
import ch.eisenring.dispo.shared.model.Mitarbeiter;
import ch.eisenring.dispo.shared.model.ModelContext;
import ch.eisenring.logiware.code.soft.LWMonteurSirCode;

public class ServerEmployeeUpdater extends AbstractTickUpdater {

	public ServerEmployeeUpdater(final DSPServer server) {
		super(server, new PeriodicTimer(3600));
	}

	@Override
	protected void tickImpl() {
		updateEmployees(server);
	}

	@SuppressWarnings("unchecked")
	public static void updateEmployees(final DSPServer server) {
		final ModelContext global = server.getGlobalContext();
		if (global == null) {
			return;
		}

		final ServerContextCache contextCache = server.getContextCache();
		final List<ServerModelContext> contextList = (List) contextCache.getContextList();
		for (final ServerModelContext context : contextList) {
			context.obtain();
			try {
				final boolean changed = updateEmployees(context, global, server);
				if (changed) {
					server.sendContextChanges(context);
				}
			} finally {
				context.release();
			}
		}
	}

	/**
	 * Updates all employees in context
	 */
	@SuppressWarnings("unchecked")
	public static boolean updateEmployees(final ModelContext context, final ModelContext global, final DSPServer server) {
		boolean changed = false;
		
		// update the context
		global.obtain();
		context.obtain();
		final List<MasterMitarbeiter> masterList = (List) global.getModelList(new Filter<AbstractModel>() {
			@Override
			public boolean accepts(final AbstractModel model) {
				return model instanceof MasterMitarbeiter;
			}
		});
		try {
			for (final MasterMitarbeiter master : masterList) {
				final EmploymentRole role = master.getEmploymentRole();
				final boolean employed;
				if (DSPConstants.MONT_PRESENTO_ID == master.getPresentoId()) {
					// MONT is always employed
					employed = true;
				} else if (MITARBEITER_CHAUFEUR.equals(role)) {
					employed = master.isEmployableWithin(context);
				} else if (MITARBEITER_MONTEUR.equals(role)) {
					employed = master.isEmployableWithin(context);
				} else {
					employed = false;
				}

				// do we have an employee that matches this person?
				Mitarbeiter mitarbeiter = (Mitarbeiter) context.getMitarbeiter(master.getPresentoId(), Mitarbeiter.class);
				if (mitarbeiter == null && employed) {
					// the employee must be new
					mitarbeiter = new Mitarbeiter(context, master);
					changed = true;
				}
				if (mitarbeiter != null) {
					// update the employees attributes
					mitarbeiter.copyAttributes(master);
					mitarbeiter.setEmployed(employed);
					updateLogiwareId(server, mitarbeiter);
					changed |= mitarbeiter.isDirty();
	
					// update the employees Absences
					changed |= updateEmploymentAbsences(mitarbeiter, master);
				}
			}
		} finally {
			global.release();
			context.release();
		}
		return changed;
	}
	
	private static boolean updateEmploymentAbsences(final Mitarbeiter mitarbeiter, final MasterMitarbeiter master) {
		boolean changed = false;
		final ModelContext context = mitarbeiter.getContext();
		context.obtain();
		try {
			final List<FeinZuordnung> absenceList = new ArrayList<FeinZuordnung>();
			final Iterator<FeinZuordnung> i = mitarbeiter.newFeinZuordnungIterator();
			while (i.hasNext()) {
				final FeinZuordnung z = i.next();
				if (ZUORDNUNG_EMPLOYMENT.equals(z.getType())) {
					absenceList.add(z);
				}
			}
			long begin = context.getBegin();
			while (begin < context.getEnd()) {
				final long end = DateGranularityCode.DAY.round(begin, 1);

				// is there an absence for this day?
				FeinZuordnung absence = null;
				for (int j=0; j<absenceList.size(); ++j) {
					absence = absenceList.get(j);
					if (absence.intersect(begin, end)) {
						break;
					} else {
						absence = null;
					}
				}
				
				// update absence
				final EinAusCode code = master.getEinAusCode(begin);
				if (code.isEmployed()) {
					if (absence != null) {
						// delete outdated absence
						changed = true;
						absence.delete();
					}
				} else {
					if (absence == null) {
						// create new absence
						changed = true;
						absence = new FeinZuordnung(context);
						absence.setCreatedBy("ABSENZ");
						absence.setRange(begin, end);
						absence.setType(ZUORDNUNG_EMPLOYMENT);
						absence.setMitarbeiter(mitarbeiter);
						mitarbeiter.addFeinZuordnung(absence);
					}
					absence.setLabel(code.getDisplayText());
				}
				
				// process next day
				begin = DateGranularityCode.DAY.round(begin, 1);
			}
		} finally {
			context.release();
		}
		return changed;
	}

	private static void updateLogiwareId(final DSPServer server, final AbstractMitarbeiter employee) {
		final String shortName = employee.getShortName();
		if (shortName == null) {
			employee.setLogiwareId(null);
			return;
		} else if (MONT_SHORTNAME.equalsIgnoreCase(shortName)) {
			if (employee.getPresentoId() != MONT_PRESENTO_ID) {
				employee.setLogiwareId(null);
				return;
			}
		}
		final LWMonteurSirCode monteurSir = LWMonteurSirCode.getMonteur(employee.getLogiwareId(), shortName);
		if (monteurSir == null) {
			employee.setLogiwareId(null);
		} else {
			employee.setLogiwareId(Strings.toString(monteurSir.getKey()));
		}
	}

}

package ch.eisenring.dispo.server.ticklisteners;

import static ch.eisenring.dispo.shared.DSPConstants.READONLY_DISPO;
import static ch.eisenring.dispo.shared.DSPConstants.TEMPORARY_CONTEXT_TIMESTAMP;

import java.time.Duration;

import ch.eisenring.app.server.ServerCore;
import ch.eisenring.app.shared.ComponentNotFoundException;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.dispo.server.DSPServer;
import ch.eisenring.dispo.server.model.ServerModelContext;
import ch.eisenring.dispo.shared.codetables.DispoEventCode;
import ch.eisenring.dispo.shared.model.DispoEvent;
import ch.eisenring.email.EMail;
import ch.eisenring.email.EMailAddress;
import ch.eisenring.email.server.MailServiceServer;
import ch.eisenring.user.server.USRServer;
import ch.eisenring.user.shared.model.UserContext;
import ch.eisenring.user.shared.model.UserModel;

public class ServerDispoEventMailerHK extends AbstractServerHouseKeeper {

	private final static long INTERVAL = Duration.ofMinutes(60).toMillis();
	
	public ServerDispoEventMailerHK(DSPServer server) {
		super(server, INTERVAL);
	}

	@Override
	public String getName() {
		return "ServerDispoEventMailer";
	}

	@Override
	public int getMaxErrorCount() {
		return 0;
	}

	@Override
	protected void doHousekeeping() {
		// no mails when operating in readonly-mode
		if (READONLY_DISPO)
			return;
		try {
			Logger.debug("Checking for pending mail messages");

			// use a temporary context
			final ServerModelContext context = new ServerModelContext(TEMPORARY_CONTEXT_TIMESTAMP, getServer());
			context.getEntityContext().loadDispoEvents();

			// process all pending messages
			List<DispoEvent> eventList = context.getModelList(DispoEvent.class);
			for (int i=eventList.size()-1; i>=0; --i) {
				final DispoEvent event = eventList.get(i);

				// create mail
				EMail mail = new EMail();
				mail.setSubject(event.getMainObject());
				mail.setPlainText(event.getDetailMessage());
				mail.setFromName(server.STATE_MAILER_FROMNAME.get());
				mail.setFromAddress(server.STATE_MAILER_FROMEMAIL.get());
				mail.setReplyTo(server.STATE_MAILER_REPLYTO.get());

				// get all subscribing users
				List<EMailAddress> recipients;
				recipients = getDispoEventSubscribers(getServer().getCore(), event.getEventType());

				// find (and add) specific event receiver
				// disabled for 1.0.96+
				//Benutzer benutzer = (Benutzer) global.getModel(event.getRecipientUserId());
				//if (benutzer != null && !recipients.contains(benutzer)) {
				//	recipients.add(benutzer);
				//}
				
				// set all recipients on mail
				for (final EMailAddress address : recipients) {
					mail.addTo(address);
				}
				
				// send to general address
				mail.addTo(server.STATE_MAILER_BCASTEMAIL.get(),
						   server.STATE_MAILER_BCASTNAME.get());

				if (mail.getRecipientCount() > 0) {
					// find the mail service
					final MailServiceServer mailService = (MailServiceServer) getServer().getComponent(MailServiceServer.class, false);
					if (mailService != null) {
						mailService.enqueue(mail, null);
					}
				}
				
				event.setWasMailed(true);
			}

			// --- store changes (if any)
			if (!eventList.isEmpty()) {
				context.store();
			}
		} catch (Exception e) {
			Logger.error(Strings.concat("Uncaught Exception: ", e.getMessage()));
			Logger.error(e);
		}
	}

	private List<EMailAddress> getDispoEventSubscribers(final ServerCore core, final DispoEventCode event) {
		final List<EMailAddress> result = new ArrayList<>();
		final USRServer server;
		try {
			server = (USRServer) core.getComponent(USRServer.class);
			if (server == null)
				return result;
		} catch (final ComponentNotFoundException e) {
			// ignore and return empty result
			return result;
		}
		final UserContext context = server.getContext();
		if (context == null)
			return result;
		final List<UserModel> userList = context.getModels(UserModel.class);
		final int optionId = event.getId(); 
		for (final UserModel user : userList) {
			if (user.isOptionSelected(optionId) && user.isLoginAllowed()) {
				final String address = user.getEMailAddress();
				if (!Strings.isEmpty(address)) {
					final EMailAddress eMail = new EMailAddress(address);
					if (eMail.isValid()) {
						result.add(eMail);
					}
				}
			}
		}
		return result;
	}

}

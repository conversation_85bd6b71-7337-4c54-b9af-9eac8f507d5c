package ch.eisenring.dispo.server.network;

import java.io.IOException;
import java.sql.SQLException;
import java.util.Collections;
import java.util.Comparator;

import ch.eisenring.core.poi.POIUtil;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;

import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.Map;
import ch.eisenring.core.collections.Set;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.collections.impl.ArraySet;
import ch.eisenring.core.csv.CSVFile;
import ch.eisenring.core.csv.CSVLine;
import ch.eisenring.core.datatypes.date.DateGranularityCode;
import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.io.binary.BinaryHolder;
import ch.eisenring.core.util.file.FileImage;
import ch.eisenring.dispo.server.DSPServer;
import ch.eisenring.dispo.shared.network.packets.PacketFehlendeFotosReply;
import ch.eisenring.dispo.shared.network.packets.PacketFehlendeFotosRequest;
import ch.eisenring.dms.service.DMSService;
import ch.eisenring.dms.service.codetables.DMSFolderType;
import ch.eisenring.jdbc.JDBCResultSet;
import ch.eisenring.jdbc.RowHandler;
import ch.eisenring.jdbc.StatementParameters;
import ch.eisenring.logiware.LWAuftragKey;
import ch.eisenring.logiware.LWContextSource;
import ch.eisenring.logiware.LWProjektKey;
import ch.eisenring.logiware.code.pseudo.AbwicklungsartCode;
import ch.eisenring.logiware.code.soft.GSECode;
import ch.eisenring.logiware.code.soft.LWGranit2Code;
import ch.eisenring.logiware.code.soft.LWMonteurPfyCode;
import ch.eisenring.logiware.code.soft.LWMonteurSirCode;
import ch.eisenring.logiware.code.soft.LWObjektBetreuerCode;
import ch.eisenring.lw.LWConstants;
import ch.eisenring.model.TransactionContext;
import ch.eisenring.network.PacketSink;
import ch.eisenring.network.packet.AbstractPacket;
import org.apache.poi.xssf.usermodel.XSSFPrintSetup;

public final class FehlendeFotosRequestHandler extends AbstractDSPPacketHandler {

	public FehlendeFotosRequestHandler(final DSPServer server) {
		super(server, PacketFehlendeFotosRequest.class);
	}

	@Override
	public void handle(final AbstractPacket packet, final PacketSink sink) {
		final PacketFehlendeFotosRequest request = (PacketFehlendeFotosRequest) packet;
		final PacketFehlendeFotosReply reply = handle(server, request);
		sink.sendPacket(reply, false);
	}

	public static PacketFehlendeFotosReply handle(final DSPServer server, final PacketFehlendeFotosRequest request) {
		try {
			final PacketFehlendeFotosReply reply = handleImpl(server, request);
			if (reply != null)
				return reply;
		} catch (final Exception e) {
			return PacketFehlendeFotosReply.create(request, new ErrorMessage(e));
		}
		return PacketFehlendeFotosReply.create(request, ErrorMessage.ERROR);
	}

	private static PacketFehlendeFotosReply handleImpl(final DSPServer server, final PacketFehlendeFotosRequest request) throws Exception {
		final List<ProjektInfo> projekte = getProjekte(server, request);
		Collections.sort(projekte, ORDER);
		final Set<String> projektnummern = new ArraySet<>();
		for (final ProjektInfo info : projekte)
			projektnummern.add(info.projektKey.getProjektnummer());
		final DMSService service = server.locateService(DMSService.class);
		final Map<String, Integer> countMap = service.countFolderContents(projektnummern, DMSFolderType.FOTOS_MONTAGE_MOEBEL);
		for (final ProjektInfo info : projekte) {
			final String projektnummer = info.projektKey.getProjektnummer();
			final Integer count = countMap.get(projektnummer);
			if (count == null) {
				info.fotoCount = -1;
			} else {
				info.fotoCount = count.intValue();
			}
		}
		final CSVFile csvFile = generateCSV(projekte);
		final BinaryHolder xlsx = generateXLSX(csvFile);
		final FileImage fileImage = FileImage.create("Fehlende_Fotos.xlsx", xlsx);
		return PacketFehlendeFotosReply.create(request, fileImage);
	}

	private static BinaryHolder generateXLS(final CSVFile csvFile) throws IOException {
		final Workbook workbook = POIUtil.createWorkbook();
		final Sheet sheet = POIUtil.createSheet(workbook);
		sheet.getPrintSetup().setLandscape(false);
		sheet.getPrintSetup().setPaperSize(XSSFPrintSetup.A4_PAPERSIZE);
		sheet.getPrintSetup().setFitHeight((short) 1);
		sheet.getPrintSetup().setFitWidth((short) 1);
		sheet.setAutobreaks(true);
		
		workbook.setSheetName(0, "Fehlende Fotos");
		POIUtil.popuplateSheet(sheet, csvFile);
		// basic formatting
		POIUtil.setFillForegroundColor(sheet, IndexedColors.GREY_25_PERCENT.getIndex(), 0, 0, csvFile.getColumnCount(), 1);
		POIUtil.addBorder(sheet, IndexedColors.BLACK.getIndex(), 0, 0, csvFile.getColumnCount(), 1);
		
		return POIUtil.toBinary(workbook);
	}

		private static BinaryHolder generateXLSX(final CSVFile csvFile) throws IOException {
		final Workbook workbook = POIUtil.createWorkbook();
		final Sheet sheet = POIUtil.createSheet(workbook);
		sheet.getPrintSetup().setLandscape(false);
		sheet.getPrintSetup().setPaperSize(XSSFPrintSetup.A4_PAPERSIZE);
		sheet.getPrintSetup().setFitHeight((short) 1);
		sheet.getPrintSetup().setFitWidth((short) 1);
		sheet.setAutobreaks(true);

		workbook.setSheetName(0, "Fehlende Fotos");
		POIUtil.popuplateSheet(sheet, csvFile);
		// basic formatting
		POIUtil.setFillForegroundColor(sheet, IndexedColors.GREY_25_PERCENT.getIndex(), 0, 0, csvFile.getColumnCount(), 1);
		POIUtil.addBorder(sheet, IndexedColors.BLACK.getIndex(), 0, 0, csvFile.getColumnCount(), 1);

		return POIUtil.toBinary(workbook);
	}

	private static CSVFile generateCSV(final List<ProjektInfo> projekte) {
		final CSVFile csvFile = new CSVFile();
		final CSVLine header = csvFile.addLine();
		header.setColumn(0, "Projekt");
		header.setColumn(1, "Auftrag");
		header.setColumn(2, "GSE");
		header.setColumn(3, "AbwArt");
		header.setColumn(4, "Wunschdatum");
		header.setColumn(5, "Objektbetreuer");
		header.setColumn(6, "Monteur SIR");
		header.setColumn(7, "Monteur PFY");
		header.setColumn(8, "Problem");
		header.setColumn(9, "Objektbezeichnung");

		for (final ProjektInfo info : projekte) {
			if (info.fotoCount > 0)
				continue;
			final CSVLine csvLine = csvFile.addLine();
			csvLine.setColumn(0, info.projektKey.getProjektnummer());
			csvLine.setColumn(1, info.auftragKey.getAuftragnummer());
			csvLine.setColumn(2, AbstractCode.getKey(info.auftragKey.getGSE(), (Object) null));
			csvLine.setColumn(3, AbstractCode.getKey(info.abwicklungsArt, (Object) null));
			csvLine.setColumn(4, TimestampUtil.DATE10.format(info.wunschDatum));
			csvLine.setColumn(5, info.objektBetreuer);
			csvLine.setColumn(6, info.monteurSIR);
			csvLine.setColumn(7, info.monteurPFY);
			csvLine.setColumn(8, info.fotoCount < 0 ? "Nicht eröffnet" : "Keine Fotos");
			csvLine.setColumn(9, Strings.toSingleLine(info.projektBezeichnung));
		}
		return csvFile;
	}

	private static List<ProjektInfo> getProjekte(final DSPServer server, final PacketFehlendeFotosRequest request) throws Exception {
		final List<ProjektInfo> result = new ArrayList<>();
		final LWContextSource source = new LWContextSource(LWConstants.VERTRIEB) {
			@Override
			protected void loadImpl(final TransactionContext context) throws SQLException {
				final StatementParameters params = new StatementParameters();
				final StringMaker sql = StringMaker.obtain(512);
				sql.append("SELECT A.PAuftrNr, A.Id_GSE_PA, A.AuftrNr, A.Id_GSE");
				sql.append(", A.DatWu, A.AbwArt, P.PFaktPerCd");
				sql.append(", K.MonteurSir, K.MonteurPfy, K.Sch_Block2");
				sql.append(", P.PBez");
				sql.append(" FROM AUFTRAG A");
				sql.append(" JOIN PAUFTRAG P ON (A.PAuftrNr = P.PAuftrNr AND A.Id_GSE_PA = P.Id_GSE_PA)");
				sql.append(" JOIN AuftragsKopfZusatz K ON (A.AuftrNr = K.AuftrNr AND A.Id_GSE = K.ID_GSE)");
				sql.append(" WHERE A.DatWu >= ? AND A.DatWu < ?");
				sql.append(" AND A.Status NOT IN ('090', '099')");
				sql.append(" AND A.AbwArt IN ");
				prepareIn(sql, request.getAbwicklungsArten());
				params.addDate(request.getDateFrom());
				params.addDate(DateGranularityCode.DAY.round(request.getDateUpto(), 1));
				params.addAll(request.getAbwicklungsArten());
				doQuery(sql.release(), params, new RowHandler() {
					@Override
					public void handleRow(final JDBCResultSet resultSet) throws SQLException {
						final String projektnummer = resultSet.getString(1);
						final GSECode gseProjekt = resultSet.getCode(2, GSECode.class);
						final int auftragnummer = resultSet.getInt(3);
						final GSECode gseAuftrag = resultSet.getCode(4, GSECode.class);
						final ProjektInfo info = new ProjektInfo();
						info.projektKey = LWProjektKey.get(projektnummer, gseProjekt);
						info.auftragKey = LWAuftragKey.get(auftragnummer, gseAuftrag);
						info.wunschDatum = resultSet.getLongDate(5);
						info.abwicklungsArt = resultSet.getCode(6, AbwicklungsartCode.class);
						info.objektBetreuer = resultSet.getCode(7, LWObjektBetreuerCode.class);
						info.monteurSIR = resultSet.getCode(8, LWMonteurSirCode.class);
						info.monteurPFY = resultSet.getCode(9, LWMonteurPfyCode.class);
						info.granit2 = resultSet.getCode(10, LWGranit2Code.class);
						info.projektBezeichnung = resultSet.getString(11);
						result.add(info);
					}
				});
			}
		};
		final TransactionContext context = new TransactionContext();
		context.load(source);
		return result;
	}

	final static class ProjektInfo {
		public LWProjektKey projektKey;
		public LWAuftragKey auftragKey;
		public long wunschDatum;
		public AbwicklungsartCode abwicklungsArt;
		public LWObjektBetreuerCode objektBetreuer;
		public LWMonteurSirCode monteurSIR;
		public LWMonteurPfyCode monteurPFY;
		public LWGranit2Code granit2;
		public String projektBezeichnung;
		public boolean exists;
		public int fotoCount;
	}

	final static Comparator<ProjektInfo> ORDER = new Comparator<ProjektInfo>() {
		@Override
		public int compare(final ProjektInfo i1, final ProjektInfo i2) {
			return i1.projektKey.compareTo(i2.projektKey);
		}
	};
}

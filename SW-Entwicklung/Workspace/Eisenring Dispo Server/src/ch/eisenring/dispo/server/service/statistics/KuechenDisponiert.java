package ch.eisenring.dispo.server.service.statistics;

import static ch.eisenring.logiware.ILogiwareVertriebConstants.COL_ORD_DISPONENT_FK;
import static ch.eisenring.logiware.ILogiwareVertriebConstants.COL_ORD_WUNSCHDATUM;
import static ch.eisenring.logiware.ILogiwareVertriebConstants.TBL_ORDER;

import java.sql.Connection;
import java.sql.SQLException;

import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.core.logging.Logger.LogLevel;
import ch.eisenring.dispo.server.DSPServer;
import ch.eisenring.jdbc.JDBCResultSet;
import ch.eisenring.statistics.shared.codetables.STDateGranularityCode;
import ch.eisenring.statistics.shared.data.DataPoint;
import ch.eisenring.statistics.shared.network.PacketStatisticRequest;
import ch.eisenring.statistics.shared.network.PacketStatisticResponse;

public class KuechenDisponiert extends DispositionBase {

	public KuechenDisponiert(final DSPServer server) {
		super(server);
	}

	@Override
	public PacketStatisticResponse createResponse(PacketStatisticRequest packet) {
		PacketStatisticResponse result = null;
		final String sql = createQuery(packet);
		Logger.log(LogLevel.TRACE, "SQL: "+sql);

		// --- execute query
		final Connection connection = connectToLogiware();
		if (null!=connection) {
			try {
				final JDBCResultSet resultSet = doFetchQuery(sql);
				final List<DataPoint> list = new ArrayList<DataPoint>(64);
				while (resultSet.next()) {
					String key = resultSet.getString(COL_ORD_DISPONENT_FK);
					String label = getDisponentFromKey(key);
					final int value = resultSet.getInt(COUNTED_COLUMN);
					label = label + " (" + ((int) value) + ")";
					final DataPoint point = new DataPoint(label, value);
					list.add(point);
				}
				resultSet.close();

				result = PacketStatisticResponse.create(packet);

				// --- chart title
				String title = STDateGranularityCode.getDateRangeText(result);
				title = "Aufträge disponiert\n("+title+")";
				result.setChartTitle(title);

				// --- values
				double[] data = getValues(list);
				result.setDataSeries(new double[][] { data });

				// --- labels
				String[] labels = getLabels(list);
				result.setLabels(labels);
			} catch (final SQLException e) {	 
				Logger.log(LogLevel.ERROR, e);
			} finally {
				close();
			}
		}
		return result;
	}

	
	private String createQuery(final PacketStatisticRequest request) {
		final StringMaker b = StringMaker.obtain(512);
		b.append("SELECT ");
		b.append(COL_ORD_DISPONENT_FK);
		b.append(", COUNT(*) AS ");
		b.append(COUNTED_COLUMN);
		b.append(" FROM ");
		qualifyTableName(b, TBL_ORDER);
		append(b, " WHERE ", getDateRestrictionClause(request.getDateFrom(), request.getDateUpto(), COL_ORD_WUNSCHDATUM));
		b.append(" GROUP BY ");
		b.append(COL_ORD_DISPONENT_FK);
		b.append(" ORDER BY ");
		b.append(COL_ORD_DISPONENT_FK);
		return b.release();
	}

}

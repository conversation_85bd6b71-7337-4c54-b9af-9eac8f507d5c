package ch.eisenring.dispo.server.network;

import java.sql.SQLException;
import java.util.Calendar;
import java.util.Date;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;

import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.collections.Collection;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.Map;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.collections.impl.HashMap;
import ch.eisenring.core.datatypes.date.DateUtil;
import ch.eisenring.core.datatypes.date.HEAGCalendar;
import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.core.datatypes.date.format.HEAGDateFormat;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.dispo.server.DSPServer;
import ch.eisenring.dispo.shared.network.packets.PacketReportEndmontagenReply;
import ch.eisenring.dispo.shared.network.packets.PacketReportEndmontagenRequest;
import ch.eisenring.jdbc.JDBCResultSet;
import ch.eisenring.jdbc.RowHandler;
import ch.eisenring.jdbc.StatementParameters;
import ch.eisenring.logiware.LWContextSource;
import ch.eisenring.logiware.LWProjektKey;
import ch.eisenring.logiware.code.soft.GSECode;
import ch.eisenring.logiware.code.soft.LWKundenberaterCode;
import ch.eisenring.logiware.code.soft.LWObjektBetreuerCode;
import ch.eisenring.logiware.util.LWEndmontageUtil;
import ch.eisenring.lw.LWConstants;
import ch.eisenring.model.TransactionContext;
import ch.eisenring.network.PacketSink;
import ch.eisenring.network.packet.AbstractPacket;
import ch.eisenring.statistics.shared.data.DataPoint;

final class ReportEndmontagenRequestHandler extends AbstractDSPPacketHandler {

	ReportEndmontagenRequestHandler(final DSPServer server) {
		super(server, PacketReportEndmontagenRequest.class);
	}

	public void handle(final AbstractPacket abstractPacket, final PacketSink sink) {
		final PacketReportEndmontagenRequest request = (PacketReportEndmontagenRequest) abstractPacket;
		final PacketReportEndmontagenReply reply = handle(server, request);
		sink.sendPacket(reply, false);
	}

	public static PacketReportEndmontagenReply handle(final DSPServer server, final PacketReportEndmontagenRequest request) {
		try {
			final PacketReportEndmontagenReply reply = handleImpl(server, request);
			if (reply != null)
				return reply;
			return PacketReportEndmontagenReply.create(request, ErrorMessage.ERROR);
		} catch (final Exception e) {
			Logger.error(e);
			return PacketReportEndmontagenReply.create(request, new ErrorMessage(e));
		}
	}

	private static PacketReportEndmontagenReply handleImpl(
			final DSPServer server, final PacketReportEndmontagenRequest request) throws Exception {

		final Date dateFrom = normalize(request.getDateFrom(), 0);
		final Date dateUpto = normalize(request.getDateUpto(), 1);
		final Map<LWProjektKey, QAuftrag> auftragMap = new HashMap<>();
		
		final LWContextSource source = new LWContextSource(LWConstants.VERTRIEB) {
			@Override
			protected void loadImpl(final TransactionContext context) throws SQLException {
				// fetch 135'er
				final String sql1 =
						"SELECT"
						  + " A.PAuftrNr"
						  + ", A.Id_GSE_PA"
						  + ", A.DatWu"
						  + ", P.Vert1"
						  + ", P.PFaktPerCd"
						  + ", P.Vertreter"
						+ " FROM"
						  + " AUFTRAG A"
						  + ", PAUFTRAG P"
						+ " WHERE"
						  + " A.AbwArt = '135' AND A.Status NOT IN ('099')"
						  + " AND A.DatWu >= ? AND A.DatWu < ?"
						  + " AND A.PAuftrNr = P.PAuftrNr AND A.Id_GSE_PA = P.Id_GSE_PA";
				final StatementParameters params1 = new StatementParameters(2);
				params1.addDate(dateFrom);
				params1.addDate(dateUpto);
				doQuery(sql1, params1, new RowHandler() {
					@Override
					public void handleRow(final JDBCResultSet resultSet) throws SQLException {
						final String projektnummer = resultSet.getString(1);
						final GSECode gse = resultSet.getCode(2, GSECode.class);
						final QAuftrag auftrag = new QAuftrag(projektnummer, gse);
						auftrag.wunschdatum = normalize(resultSet.getTimestamp(3), 0);
						auftrag.kundenberater = resultSet.getCode(4, LWKundenberaterCode.class);
						auftrag.objektbetreuer = resultSet.getCode(5, LWObjektBetreuerCode.class);
						auftrag.verkaeufer = resultSet.getInt(6);
						if (resultSet.wasNull())
							auftrag.verkaeufer = null;
						auftragMap.put(auftrag.projektKey, auftrag);
					}
				});

				// fetch and count 115'er
				final String sql2 =
						"SELECT"
						  + " A.PAuftrNr"
						  + ", A.Id_GSE_PA"
						  + ", F.FormDatum"
						+ " FROM"
						  + " AUFTRAG A"
						  + ", FORMULAR F"
						+ " WHERE"
						  + " A.AbwArt = ? AND A.Status NOT IN ('099')"
						  + " AND A.AuftrNr = F.AuftrNr AND A.Id_GSE = F.Id_GSE"
						  + " AND F.FormArt = ?"
						+ " ORDER BY"
						  + " A.PAuftrNr, A.Id_GSE_PA, F.FormDatum";
				final StatementParameters params2 = new StatementParameters();
				params2.add(LWEndmontageUtil.EM_ABWICKLUNGSART);
				params2.add(LWEndmontageUtil.EM_FORMULARART);
				final AtomicLong lastTimestamp = new AtomicLong(TimestampUtil.NULL_TIMESTAMP);
				final AtomicReference<LWProjektKey> lastKey = new AtomicReference<>(LWProjektKey.INVALID);
				doQuery(sql2, params2, new RowHandler() {
					@Override
					public void handleRow(final JDBCResultSet resultSet) throws SQLException {
						String projektnummer = resultSet.getString(1);
						try {
							projektnummer = LWProjektKey.getSignificantProjektnummer(projektnummer);
						} catch (final Exception e) {
							// ignore this record
							return;
						}
						final GSECode gse = resultSet.getCode(2, GSECode.class);
						long timestamp = resultSet.getLongTimestamp(3);
						timestamp = LWEndmontageUtil.getCanonicalEMDate(timestamp);
						final LWProjektKey key = LWProjektKey.get(projektnummer, gse);
						final QAuftrag auftrag = auftragMap.get(key);
						if (auftrag == null)
							return;
						if (lastKey.get().equals(key)) {
							if (lastTimestamp.get() == timestamp)
								return;
						} else {
							lastKey.set(key);
						}
						lastTimestamp.set(timestamp);
						auftrag.endmontagen += 1;
					}
				});
			}
		};
		final TransactionContext context = new TransactionContext();
		context.load(source);
		
		// prepare data set
		final List<DataPoint> dataPoints = new ArrayList<DataPoint>();
		Date kw = dateFrom;
		while (kw.before(dateUpto)) {
			// get all auftrag in KW
			List<QAuftrag> all = filter(auftragMap.values(), kw);
			final int count0 = count(all, request, 0);
			final int count1 = count(all, request, 1) - count0;
			final int count2 = count(all, request, 2) - count1 - count0;
			final int countN = count(all, request, Integer.MAX_VALUE) - count2 - count1 - count0;
			final int countA = count0 + count1 + count2 + countN;
			
			double v0 = (countA <= 0) ? 0D : ((double) count0) / countA;
			double v1 = (countA <= 0) ? 0D : ((double) count1) / countA;
			double v2 = (countA <= 0) ? 0D : ((double) count2) / countA;
			double vN = (countA <= 0) ? 0D : ((double) countN) / countA;

			final String mnString = HEAGDateFormat.get("yyyy/MM").format(kw);
			dataPoints.add(new DataPoint("0 EM\n" + mnString, v0 * 100));
			dataPoints.add(new DataPoint("1 EM\n" + mnString, v1 * 100));
			dataPoints.add(new DataPoint("2 EM\n" + mnString, v2 * 100));
			dataPoints.add(new DataPoint(">2 EM\n" + mnString, vN * 100));
			kw = normalize(kw, 1);
		}
		return PacketReportEndmontagenReply.create(request, dataPoints);
	}

	private static List<QAuftrag> filter(final java.util.Collection<QAuftrag> auftraege, final Date kw) {
		final List<QAuftrag> result = new ArrayList<QAuftrag>(100);
		for (final QAuftrag auftrag : auftraege) {
			if (DateUtil.equals(auftrag.wunschdatum, kw))
				result.add(auftrag);
		}
		result.trimToSize();
		return result;
	}

	private static int count(final Collection<QAuftrag> auftraege, final PacketReportEndmontagenRequest request, final int maxEndmontagen) {
		int result = 0;
		LWObjektBetreuerCode obCode = request.getObjektbetreuer();
		if (AbstractCode.isNull(obCode))
			obCode = null;
		LWKundenberaterCode idCode = request.getKundenberater();
		if (AbstractCode.isNull(idCode))
			idCode = null;
		Integer verkaeufer = request.getVerkaeufer();
		if (verkaeufer == null || verkaeufer.intValue() == 0)
			verkaeufer = null;
		for (final QAuftrag auftrag : auftraege) {
			if (obCode != null && !AbstractCode.equals(auftrag.objektbetreuer, obCode))
				continue;
			if (idCode != null && !AbstractCode.equals(auftrag.kundenberater, idCode))
				continue;
			if (verkaeufer != null && !verkaeufer.equals(auftrag.verkaeufer))
				continue;
			if (auftrag.endmontagen > maxEndmontagen)
				continue;
			++result;
		}
		return result;
	}

	static Date normalize(final Date date, final int offset) {
		final HEAGCalendar c = HEAGCalendar.obtain(date);
		c.setTime(0, 0, 0, 0);
		c.set(Calendar.DAY_OF_MONTH, 1);
		if (offset != 0)
			c.add(Calendar.MONTH, 1);
		return c.getTime();
	}

	static class QAuftrag {
		public final LWProjektKey projektKey;
		public Date wunschdatum; 
		public LWObjektBetreuerCode objektbetreuer;
		public LWKundenberaterCode kundenberater;
		public Integer verkaeufer;
		public int endmontagen;
		
		public QAuftrag(final String projektnummer, final GSECode gse) {
			this.projektKey = LWProjektKey.get(projektnummer, gse);
		}
	}

}

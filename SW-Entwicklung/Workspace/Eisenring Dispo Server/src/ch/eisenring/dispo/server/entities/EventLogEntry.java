package ch.eisenring.dispo.server.entities;

import java.util.Date;

import ch.eisenring.core.HashUtil;
import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.logging.Logger;

/**
 * This is no entity. The class is only used to write log entries
 * to the database.
 */
public class EventLogEntry {

	private long rowId;
	private Date contextDate;
	private Date serverTimeStamp;
	private long objectId;
	private String actionType;
	private String windowsUser;
	private String objectDescription;

	public EventLogEntry() {
	}

	public EventLogEntry(AbstractEntity entity, final long contextDate, String actionType) {
		setContextDate(TimestampUtil.toDate(contextDate));
		setServerTimeStamp(new Date());
		setObjectId(entity.getId());
		setWindowsUser(entity.getContext().getModelContext().getLastChangedBy());
		try {
			setObjectDescription(entity.getObjectDescription());
		} catch (Exception e) {
			setObjectDescription("Object description not available: "+e.getMessage());
			Logger.warn("Object description could not be built (ignoring exception)");
			Logger.warn(e);
		}
		setActionType(actionType);
	}

	public Date getContextDate() {
		return contextDate;
	}

	public void setContextDate(Date contextDate) {
		this.contextDate = contextDate;
	}

	public long getObjectId() {
		return objectId;
	}

	public void setObjectId(long objectId) {
		this.objectId = objectId;
	}

	public String getWindowsUser() {
		return windowsUser;
	}

	public void setWindowsUser(String windowsUser) {
		this.windowsUser = Strings.clean(windowsUser, 30);
	}

	public Date getServerTimeStamp() {
		return serverTimeStamp;
	}

	public void setServerTimeStamp(Date serverTimeStamp) {
		this.serverTimeStamp = serverTimeStamp;
	}

	public String getObjectDescription() {
		return objectDescription;
	}

	public void setObjectDescription(String objectDescription) {
		objectDescription = Strings.limit(objectDescription, 250);
		this.objectDescription = objectDescription;
	}

	public String getActionType() {
		return actionType;
	}

	public void setActionType(String actionType) {
		this.actionType = Strings.clean(actionType, 30);
	}

	public long getRowId() {
		return rowId;
	}

	public void setRowId(long rowId) {
		this.rowId = rowId;
	}

	@Override
	public boolean equals(final Object o) {
		return o instanceof EventLogEntry && ((EventLogEntry) o).rowId == rowId;
	}
	
	@Override
	public int hashCode() {
		return HashUtil.hashCode(rowId);
	}

}

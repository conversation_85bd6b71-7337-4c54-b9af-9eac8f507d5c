package ch.eisenring.dispo.server.network;

import static ch.eisenring.jdbc.sqlbuilder.SQLKeyword.AND;
import static ch.eisenring.jdbc.sqlbuilder.SQLKeyword.FROM;
import static ch.eisenring.jdbc.sqlbuilder.SQLKeyword.OR;
import static ch.eisenring.jdbc.sqlbuilder.SQLKeyword.SELECT;
import static ch.eisenring.jdbc.sqlbuilder.SQLKeyword.WHERE;
import static ch.eisenring.jdbc.sqlbuilder.SQLRelationalOperator.GE;
import static ch.eisenring.jdbc.sqlbuilder.SQLRelationalOperator.LE;

import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.Date;

import ch.eisenring.app.server.model.ContextSource;
import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.jdbc.Column;
import ch.eisenring.dispo.server.DSPServer;
import ch.eisenring.dispo.shared.DSPConstants;
import ch.eisenring.dispo.shared.codetables.DSPExtSearchInCode;
import ch.eisenring.dispo.shared.codetables.DSPExtSearchRangeCode;
import ch.eisenring.dispo.shared.codetables.DSPObjectType;
import ch.eisenring.dispo.shared.codetables.ZuordnungCode;
import ch.eisenring.dispo.shared.metamodel.DSPFeinZuordnungMetaClass;
import ch.eisenring.dispo.shared.metamodel.DSPGrobZuordnungMetaClass;
import ch.eisenring.dispo.shared.network.packets.PacketSearchExtReply;
import ch.eisenring.dispo.shared.network.packets.PacketSearchExtRequest;
import ch.eisenring.dispo.shared.pojo.DSPContext;
import ch.eisenring.dispo.shared.service.ExtSearchResult;
import ch.eisenring.jdbc.JDBCResultSet;
import ch.eisenring.jdbc.JDBCUtil;
import ch.eisenring.jdbc.StatementWrapper;
import ch.eisenring.jdbc.sqlbuilder.SQLLike;
import ch.eisenring.jdbc.sqlbuilder.SQLRelationalExpression;
import ch.eisenring.jdbc.sqlbuilder.SQLStatement;
import ch.eisenring.jdbc.sqlbuilder.SQLValue;
import ch.eisenring.logiware.code.service.LogiwareService;
import ch.eisenring.logiware.code.soft.LWAuftragStatusCode;
import ch.eisenring.lw.condition.Factory;
import ch.eisenring.lw.condition.LWCondition;
import ch.eisenring.lw.meta.LWAuftragKopfMeta;
import ch.eisenring.lw.meta.LWAuftragMeta;
import ch.eisenring.lw.meta.LWProjektMeta;
import ch.eisenring.lw.model.navigable.LWAuftrag;
import ch.eisenring.lw.model.navigable.LWAuftragKopf;
import ch.eisenring.lw.model.navigable.LWObjectCache;
import ch.eisenring.lw.model.navigable.LWProjekt;
import ch.eisenring.model.TransactionContext;
import ch.eisenring.model.TransactionSource;
import ch.eisenring.model.engine.jdbc.SingleTableMapping;
import ch.eisenring.model.engine.jdbc.TableMapping;
import ch.eisenring.network.PacketSink;
import ch.eisenring.network.implementation.PacketDispatchMode;
import ch.eisenring.network.packet.AbstractPacket;

final class SearchExtRequestHandler extends AbstractDSPPacketHandler {

	SearchExtRequestHandler(final DSPServer server) {
		super(server, PacketSearchExtRequest.class, PacketDispatchMode.ASYNCHRONOUS);
	}

	@Override
	public PacketSearchExtReply handle(final AbstractPacket packet) {
		final PacketSearchExtRequest request = (PacketSearchExtRequest) packet;
		return handle(server, request);
	}

	@Override
	public void handle(final AbstractPacket packet, final PacketSink sink) {
		final PacketSearchExtRequest request = (PacketSearchExtRequest) packet;
		final PacketSearchExtReply reply = handle(server, request);
		sink.sendPacket(reply);
	}

	private static PacketSearchExtReply handle(final DSPServer server, final PacketSearchExtRequest request) {
		final PacketSearchExtReply reply = PacketSearchExtReply.create(request);
		try {
			final List<ExtSearchResult> resultList;
			final DSPExtSearchInCode searchIn = request.getSearchInCode();
			if (DSPExtSearchInCode.GROBPLANUNG.equals(searchIn)) {
				resultList = doSearchGrob(request);
			} else if (DSPExtSearchInCode.FEINPLANUNG.equals(searchIn)) {
				resultList = doSearchFein(request);
			} else if (DSPExtSearchInCode.AUFTRAG.equals(searchIn)) {
				resultList = doSearchAuftrag(server, request);
			} else {
				resultList = null;
			}
			if (resultList == null) {
				reply.setMessage(new ErrorMessage("Nicht unterstützter Suchmodus: " + searchIn));
			} else {
				reply.setMessage(ErrorMessage.OK);
				reply.setResults(resultList);
			}
		} catch (final Exception e) {
			reply.setMessage(new ErrorMessage(e));
		}
		return reply;
	}

	/**
	 * Creates part of a WHERE clause for date range
	 */
	private static Object[] getDateRangeClause(
			final PacketSearchExtRequest request,
			final Column fromColumn,
			final Column uptoColumn) throws SQLException {
		final List<Object> result = new ArrayList<Object>();
		final DSPExtSearchRangeCode rangeCode = request.getSearchRangeCode();
		final Date now = new Date();
		final Date dateFrom = rangeCode.getDateFrom(now);
		final Date dateUpto = rangeCode.getDateUpto(now);
		if (fromColumn != null && dateUpto != null) {
			result.add(AND);
			result.add(new SQLRelationalExpression(fromColumn, SQLValue.create(new Timestamp(dateUpto.getTime())), LE));
		}
		if (uptoColumn != null && dateFrom != null) {
			result.add(AND);
			result.add(new SQLRelationalExpression(uptoColumn, SQLValue.create(new Timestamp(dateFrom.getTime())), GE));
		}
		return result.toArray();
	}

	/**
	 * Creates part of a WHERE clause in the form
	 * (col1 LIKE word1 OR col2 LIKE word1 OR colN LIKE word1) AND
	 * (col1 LIKE word2 OR col2 LIKE word2 OR colN LIKE word2) AND
	 * (col1 LIKE wordN OR col2 LIKE wordN OR colN LIKE wordN) AND
	 */
	private static Object[] getLikeFragments(final String searchWords, final Column ... columns) throws SQLException {
		final List<Object> result = new ArrayList<Object>();
		final List<String> likeWords = getLikeCriteria(searchWords);
		result.add("(");
		for (int j=0; j<likeWords.size(); ++j) {
			final String likeWord = likeWords.get(j);
			if (j > 0)
				result.add(AND);
			result.add("(");
			for (int i=0; i<columns.length; ++i) {
				final Column column = columns[i];
				if (i > 0)
					result.add(OR);
				result.add(new SQLLike(column, likeWord));
			}
			result.add(")");
		}
		result.add(")");
		return result.toArray();
	}

	private static List<String> getLikeCriteria(final String searchWords) {
		final List<String> result = new ArrayList<>();
		if (!Strings.isEmpty(searchWords)) {
			final int length = searchWords.length();
			final StringMaker word = StringMaker.obtain();
			int i = 0;
			while (i < length) {
				final char c = searchWords.charAt(i++);
				if (Strings.isWhitespace(c)) {
					if (word.length() > 0) {
						result.add(word.toString());
						word.setLength(0);
					}
				} else {
					word.append(c);
				}
			}
			if (word.length() > 0) {
				result.add(word.release());
			} else {
				word.releaseSilent();
			}
		}
		for (int i=0; i<result.size(); ++i) {
			String word = result.get(i);
			if (!JDBCUtil.containsWildcards(word)) {
				word = Strings.concat("%", word, "%");
			} else { 
				word = JDBCUtil.replaceWildcards(result.get(i));
			}
			result.remove(i);
			result.add(i, word);
		}
		return result;
	}

	public final static Date toDate(final Timestamp timestamp) {
		if (timestamp == null)
			return null;
		return new Date(timestamp.getTime());
	}

	public final static <T extends AbstractCode> T toCode(final Integer id, final Class<T> typeClass) {
		if (id == null)
			return AbstractCode.getNull(typeClass);
		return AbstractCode.getById(id, typeClass);
	}

	private static List<ExtSearchResult> doSearchAuftrag(final DSPServer server, final PacketSearchExtRequest request) throws Exception {
		final LWObjectCache cache = server.locateService(LogiwareService.class).createObjectCache();
		
		final List<LWCondition> conditions = new ArrayList<>();
		conditions.add(Factory.eq(LWAuftragMeta.ATR_AUFTRAGNUMMER, LWAuftragKopfMeta.ATR_AUFTRAGNUMMER));
		conditions.add(Factory.eq(LWAuftragMeta.ATR_GSE_AUFTRAG, LWAuftragKopfMeta.ATR_GSE_AUFTRAG));
		conditions.add(Factory.eq(LWAuftragMeta.ATR_PROJEKTNUMMER, LWProjektMeta.ATR_PROJEKTNUMMER));
		conditions.add(Factory.eq(LWAuftragMeta.ATR_GSE_PROJEKT, LWProjektMeta.ATR_GSE_PROJEKT));
		conditions.add(Factory.ne(LWAuftragMeta.ATR_STATUSCODE, LWAuftragStatusCode.STORNIERT));
		conditions.add(Factory.in(LWAuftragMeta.ATR_ABWICKLUNGSART, DSPConstants.ABWICKLUNGSARTEN_DISPO));
		{
			List<String> criteria = getLikeCriteria(request.getSearchWords());
			List<LWCondition> likes = new ArrayList<>();
			for (final String criterion : criteria) {
				likes.add(Factory.like(LWAuftragMeta.ATR_PROJEKTNUMMER, criterion));
				likes.add(Factory.like(LWProjektMeta.ATR_PROJEKTBEZEICHNUNG, criterion));
			}
			if (!likes.isEmpty())
				conditions.add(Factory.or(likes.toArray(LWCondition.class)));
		}
		{
			final DSPExtSearchRangeCode rangeCode = request.getSearchRangeCode();
			final Date now = new Date();
			final Date dateFrom = rangeCode.getDateFrom(now);
			final Date dateUpto = rangeCode.getDateUpto(now);
			if (dateFrom != null)
				conditions.add(Factory.ge(LWAuftragMeta.ATR_WUNSCHDATUM, dateFrom));
			if (dateUpto != null)
				conditions.add(Factory.le(LWAuftragMeta.ATR_WUNSCHDATUM, dateUpto));
		}
		
		final List<LWAuftrag> auftraege = cache.load(LWAuftrag.class, LWAuftragKopf.class, LWProjekt.class,
				conditions.toArray(LWCondition.class));

		final List<ExtSearchResult> resultList = new ArrayList<>();
		for (final LWAuftrag auftrag : auftraege) {
			final ExtSearchResult result = new ExtSearchResult();
			result.setRowId(auftrag.getAuftragnummer());
			result.setDateFrom(auftrag.getWunschDatum());
			result.setLabel(auftrag.getProjektnummer());
			result.setDescription(auftrag.getProjekt().getProjektbezeichnung());
			result.setMainType(DSPObjectType.AUFTRAG);
			result.setSubType(auftrag.getAbwicklungsart());
			resultList.add(result);
		}
	
		return resultList;
	}
	
	private static List<ExtSearchResult> doSearchFein(final PacketSearchExtRequest request) throws Exception {
		final List<ExtSearchResult> resultList = new ArrayList<>();
		final TransactionSource source = new ContextSource(DSPConstants.DSP_DATABASE) {
			protected void loadImpl(final TransactionContext context) throws SQLException {
				final SingleTableMapping mapping = TableMapping.get(DSPFeinZuordnungMetaClass.METACLASS, DSPFeinZuordnungMetaClass.TABLE);
				// build the query
				SQLStatement sql = new SQLStatement(
						SELECT,
						  mapping.getPrimaryKeyColumn(),
						  mapping.getColumn(DSPFeinZuordnungMetaClass.ATR_DATEFROM),
						  mapping.getColumn(DSPFeinZuordnungMetaClass.ATR_LABEL),
						  mapping.getColumn(DSPFeinZuordnungMetaClass.ATR_BEMERKUNGEN),
						  mapping.getColumn(DSPFeinZuordnungMetaClass.ATR_TYPE),
						FROM,
						  mapping.getTableSpecifier(),
						WHERE,
						  getLikeFragments(request.getSearchWords(),
								  mapping.getColumn(DSPFeinZuordnungMetaClass.ATR_LABEL),
								  mapping.getColumn(DSPFeinZuordnungMetaClass.ATR_BEMERKUNGEN)),
						  getDateRangeClause(request,
								  mapping.getColumn(DSPFeinZuordnungMetaClass.ATR_DATEFROM),
								  mapping.getColumn(DSPFeinZuordnungMetaClass.ATR_DATEUPTO))
						);
				final StatementWrapper statement = sql.prepareStatement(getConnection());
				try {
					final JDBCResultSet resultSet = statement.executeQuery();
					try {
						while (resultSet.next()) {
							final ExtSearchResult result = new ExtSearchResult();
							result.setRowId(resultSet.getLong(1));
							result.setDateFrom(resultSet.getLongTimestamp(2));
							result.setLabel(resultSet.getString(3));
							result.setDescription(resultSet.getString(4));
							result.setMainType(DSPObjectType.FEINPLANUNG);
							result.setSubType(toCode(resultSet.getInt(5), ZuordnungCode.class));
							resultList.add(result);
						}
					} finally {
						JDBCUtil.closeSilent(resultSet);
					}
				} finally {
					JDBCUtil.closeSilent(statement);
				}
			}
		};
		final TransactionContext context = new DSPContext();
		context.load(source);
		return resultList;
	}

	private static List<ExtSearchResult> doSearchGrob(final PacketSearchExtRequest request) throws Exception {
		final List<ExtSearchResult> resultList = new ArrayList<>();
		final TransactionSource source = new ContextSource(DSPConstants.DSP_DATABASE) {
			protected void loadImpl(final TransactionContext context) throws SQLException {
				final SingleTableMapping mapping = TableMapping.get(DSPGrobZuordnungMetaClass.METACLASS, DSPGrobZuordnungMetaClass.TABLE);
				// build the query
				SQLStatement sql = new SQLStatement(
						SELECT,
						  mapping.getPrimaryKeyColumn(), 
						  mapping.getColumn(DSPGrobZuordnungMetaClass.ATR_DATEFROM),
						  mapping.getColumn(DSPGrobZuordnungMetaClass.ATR_LABEL),
						  mapping.getColumn(DSPGrobZuordnungMetaClass.ATR_BEMERKUNGEN),
						FROM,
						  mapping.getTableSpecifier(),
						WHERE,
						  getLikeFragments(request.getSearchWords(),
								  mapping.getColumn(DSPGrobZuordnungMetaClass.ATR_LABEL),
								  mapping.getColumn(DSPGrobZuordnungMetaClass.ATR_BEMERKUNGEN)),
						  getDateRangeClause(request,
								  mapping.getColumn(DSPGrobZuordnungMetaClass.ATR_DATEFROM),
								  mapping.getColumn(DSPGrobZuordnungMetaClass.ATR_DATEUPTO))
						);
				final StatementWrapper statement = sql.prepareStatement(getConnection());
				try {
					final JDBCResultSet resultSet = statement.executeQuery();
					try {
						while (resultSet.next()) {
							final ExtSearchResult result = new ExtSearchResult();
							result.setRowId(resultSet.getLong(1));
							result.setDateFrom(resultSet.getLongTimestamp(2));
							result.setLabel(resultSet.getString(3));
							result.setDescription(resultSet.getString(4));
							result.setMainType(DSPObjectType.GROBPLANUNG);
							result.setSubType(ZuordnungCode.ZUORDNUNG_GROBZUORDNUNG);
							resultList.add(result);
						}
					} finally {
						JDBCUtil.closeSilent(resultSet);
					}
				} finally {
					JDBCUtil.closeSilent(statement);
				}
			}
		};
		final TransactionContext context = new DSPContext();
		context.load(source);
		return resultList;
	}

}

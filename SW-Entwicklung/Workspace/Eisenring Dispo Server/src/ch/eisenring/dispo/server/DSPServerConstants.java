package ch.eisenring.dispo.server;

import ch.eisenring.core.observable.Observable;
import ch.eisenring.dispo.shared.DSPConstants;

public interface DSPServerConstants extends DSPConstants {

	// server states
	public Observable<Integer> STATE_CACHE_SIZE =
		Observable.create(false, "ServerCacheSize", 52);
	public Observable<Integer> STATE_KEEPALIVE_INTERVAL =
		Observable.create(false, "KeepAliveInterval", 60);
	public Observable<Integer> STATE_UPDATE_CLIENT_MAXATTEMPTS	=
		Observable.create(false, "UpdateClientMaxAttempts", 3);
	public Observable<String> STATE_RCFG_STAT_WEIGHTING2501ALL	=
		Observable.create(false, "STATE_RCFG_STAT_WEIGHTING2501ALL", null);
	public Observable<String> STATE_RCFG_STAT_WEIGHTING2502ALL	=
		Observable.create(false, "STATE_RCFG_STAT_WEIGHTING2502ALL", null);
	public Observable<String> STATE_RCFG_STAT_WEIGHTING2503ALL	=
		Observable.create(false, "STATE_RCFG_STAT_WEIGHTING2503ALL", null);
	public Observable<String> STATE_RCFG_STAT_AUSLAST_KW =
		Observable.create(false, "STATE_RCFG_STAT_AUSLAST_KW", null);
	public Observable<String> STATE_RCFG_STAT_AUSLAST_TAG =
		Observable.create(false, "STATE_RCFG_STAT_AUSLAST_TAG", null);
	public Observable<String> STATE_RCFG_STAT_AUSLAST_PFYN =
		Observable.create(false, "STATE_RCFG_STAT_AUSLAST_PFYN", null);
	public Observable<String> STATE_RCFG_STAT_WEIGHTING2501KA =
		Observable.create(false, "STATE_RCFG_STAT_WEIGHTING2501KA", null);
	public Observable<String> STATE_RCFG_STAT_WEIGHTING2502KA =
		Observable.create(false, "STATE_RCFG_STAT_WEIGHTING2502KA", null);
	public Observable<String> STATE_RCFG_STAT_WEIGHTING2503KA =
		Observable.create(false, "STATE_RCFG_STAT_WEIGHTING2503KA", null);
	public Observable<String> STATE_RCFG_STAT_KW_MINUS_3 =
		Observable.create(false, "XSTATE_RCFG_STAT_KW_MINUS_3", null);
		
	// server status flags
	public final int SSF_ALIVE		= 0x01;
	public final int SSF_ACTIVE		= 0x02;
	public final int SSF_DEAD		= 0x04;
	
	// server status codes
	public final static int SERVER_IN_CREATION	= 0x00000000|SSF_ALIVE;
	public final static int SERVER_IN_STARTUP	= 0x10000000|SSF_ALIVE;
	public final static int SERVER_ACTIVE		= 0x20000000|SSF_ALIVE|SSF_ACTIVE;
	public final static int SERVER_IN_SHUTDOWN	= 0x30000000|SSF_DEAD;
	public final static int SERVER_TERMINATED	= 0x40000000|SSF_DEAD;

}

package ch.eisenring.dispo.server.network;

import ch.eisenring.dispo.server.DSPServer;
import ch.eisenring.dispo.server.model.ServerContextCache;
import ch.eisenring.dispo.shared.network.packets.PacketModelRequest;
import ch.eisenring.network.PacketSink;
import ch.eisenring.network.implementation.PacketDispatchMode;
import ch.eisenring.network.packet.AbstractPacket;

final class ModelRequestHandler extends AbstractDSPPacketHandler {

	ModelRequestHandler(final DSPServer server) {
		super(server, PacketModelRequest.class, PacketDispatchMode.ASYNCHRONOUS);
	}
	
	@Override
	public void handle(final AbstractPacket abstractPacket, final PacketSink sink) {
		final PacketModelRequest packet = (PacketModelRequest) abstractPacket;
		final long date = packet.getDate();
		final ServerContextCache cache = server.getContextCache();
		cache.sendModel(date, sink);
	}

}

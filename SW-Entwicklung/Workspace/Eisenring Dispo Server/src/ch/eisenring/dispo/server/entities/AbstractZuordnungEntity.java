package ch.eisenring.dispo.server.entities;

import java.util.Date;

import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.dispo.shared.model.AbstractModel;
import ch.eisenring.dispo.shared.model.AbstractZuordnung;
import ch.eisenring.logiware.code.soft.GSECode;

public abstract class AbstractZuordnungEntity extends AbstractEntity {

	private Integer auftragNummer;
	private String auftragGSE;
	private Date dateFrom;
	private Date dateUpTo;
	private String label;
	private String createdBy;
	private String lockedBy;
	
	public String getLabel() {
		return label;
	}
	
	public void setLabel(String label) {
		this.label = label;
	}

	public String getCreatedBy() {
		return createdBy;
	}
	
	public void setCreatedBy(final String createdBy) {
		this.createdBy = createdBy;
	}

	public String getLockedBy() {
		return lockedBy;
	}

	public void setLockedBy(final String lockedBy) {
		this.lockedBy = lockedBy;
	}

	public Date getDateFrom() {
		return dateFrom;
	}
	
	public void setDateFrom(Date dateFrom) {
		this.dateFrom = dateFrom;
	}
	
	public Date getDateUpTo() {
		return dateUpTo;
	}
	
	public void setDateUpTo(Date dateUpTo) {
		this.dateUpTo = dateUpTo;
	}
	
	public Integer getAuftragNummer() {
		return auftragNummer;
	}
	
	public void setAuftragNummer(Integer auftragNummer) {
		this.auftragNummer = auftragNummer;
	}

	public String getAuftragGSE() {
		return auftragGSE;
	}

	public void setAuftragGSE(final String gse) {
		this.auftragGSE = gse;
	}

	public int getAuftragNummerInt() {
		return null==auftragNummer ? 0 : auftragNummer.intValue();
	}
	
	public void setAuftragNummerInt(int auftragNummer) {
		this.auftragNummer = 0==auftragNummer ? null : Integer.valueOf(auftragNummer);
	}

	@Override
	public void updateModel(AbstractModel model) {
		super.updateModel(model);
		final AbstractZuordnung zuordnung = (AbstractZuordnung) model;
		zuordnung.setAuftragNummer(getAuftragNummerInt());
		zuordnung.setAuftragGSE(AbstractCode.getByKey(getAuftragGSE(), GSECode.class));
		zuordnung.setRange(TimestampUtil.toTimestamp(getDateFrom()),
				TimestampUtil.toTimestamp(getDateUpTo()) + 1000);
		zuordnung.setLabel(getLabel());
		zuordnung.setCreatedBy(getCreatedBy());
		zuordnung.setLockedBy(getLockedBy());
	}
	
	@SuppressWarnings("deprecation")
	@Override
	public void updateEntity(final AbstractModel model) {
		super.updateEntity(model);
		final AbstractZuordnung zuordnung = (AbstractZuordnung) model;
		updateDirty(getAuftragNummerInt(), zuordnung.getAuftragNummer());
		setAuftragNummerInt(zuordnung.getAuftragNummer());
		final String gseKey = AbstractCode.getKey(zuordnung.getAuftragGSE(), (String) null);
		updateDirty(getAuftragGSE(), gseKey);
		setAuftragGSE(gseKey);
		updateDirty(getLabel(), zuordnung.getLabel());
		setLabel(zuordnung.getLabel());
		updateDirty(getCreatedBy(), zuordnung.getCreatedBy());
		setCreatedBy(zuordnung.getCreatedBy());
		updateDirty(getLockedBy(), zuordnung.getLockedBy());
		setLockedBy(zuordnung.getLockedBy());
		// update begin / end
		Date d;
		d = TimestampUtil.toDate(zuordnung.getBegin());
		updateDirty(getDateFrom(), d);
		setDateFrom(d);
		d = TimestampUtil.toDate(zuordnung.getEnd() - 1000);
		updateDirty(getDateUpTo(), d);
		setDateUpTo(d);
	}

}

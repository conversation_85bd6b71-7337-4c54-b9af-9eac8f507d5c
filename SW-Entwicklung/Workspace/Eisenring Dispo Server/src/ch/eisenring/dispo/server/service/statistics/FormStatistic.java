package ch.eisenring.dispo.server.service.statistics;

import static ch.eisenring.dispo.shared.codetables.FormClassCode.FORM_GRANITSERVICEAUFTRAG;
import static ch.eisenring.dispo.shared.codetables.FormClassCode.FORM_SERVICEAUFTRAG;
import static ch.eisenring.logiware.ILogiwareVertriebConstants.COL_ORD_PK;
import static ch.eisenring.logiware.ILogiwareVertriebConstants.COL_ORD_WUNSCHDATUM;
import static ch.eisenring.logiware.ILogiwareVertriebConstants.TBL_ORDER_STATISTICS;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.Collections;
import java.util.Comparator;

import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.csv.CSVFile;
import ch.eisenring.core.csv.CSVLine;
import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.core.datatypes.primitives.Primitives;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.core.logging.Logger.LogLevel;
import ch.eisenring.core.valueset.ValueSet;
import ch.eisenring.dispo.server.DSPServer;
import ch.eisenring.dispo.shared.codetables.FormClassCode;
import ch.eisenring.jdbc.JDBCResultSet;
import ch.eisenring.logiware.code.soft.LWMonteurPfyCode;
import ch.eisenring.statistics.shared.codetables.STDateGranularityCode;
import ch.eisenring.statistics.shared.network.PacketStatisticRequest;
import ch.eisenring.statistics.shared.network.PacketStatisticResponse;

public class FormStatistic extends StatisticsBase {

	public FormStatistic(final DSPServer server) {
		super(server);
	}

	@Override
	public PacketStatisticResponse createResponse(final PacketStatisticRequest packet) {

		// load all Auftrag in date range
		List<StatisticAuftrag> auftragList = loadAuftraege(packet);

		final FormClassCode type = (FormClassCode) packet.getType().getSubType1(); 
		// load all Dispo-Auftrag
		List<StatisticForm> formList = loadFormulare(auftragList, type);

		// create CSV
		final CSVFile csvFile;
		if (FORM_GRANITSERVICEAUFTRAG.equals(type)) {
			csvFile = buildCSVGranitService(formList);
		} else if (FORM_SERVICEAUFTRAG.equals(type)) {
			csvFile = buildCSVServiceSC(formList);
		} else {
			csvFile = new CSVFile(); 
		}

		final PacketStatisticResponse result;
		result = PacketStatisticResponse.create(packet);
		String title = STDateGranularityCode.getDateRangeText(result);
		title = "Serviceaufträge\n("+title+")";
		result.setChartTitle(title);
		result.setReportTitle(title);
		result.addAttachment("Serviceauftraege_Liste.csv", csvFile.toBinary(null));
		result.setXAxisLabel("Unbenannt");
		result.setYAxisLabel("Unbenannt");
		result.setDataSeries(new double[0][0]);
		result.setLabels(Primitives.EMPTY_STRING_ARRAY);
		return result;
	}

	private List<StatisticAuftrag> loadAuftraege(final PacketStatisticRequest request) {
		final List<StatisticAuftrag> result = new ArrayList<StatisticAuftrag>(1024);
		final StringMaker w = StringMaker.obtain(128);
		final StringMaker b = StringMaker.obtain(512);
		append(w, "", getDateRestrictionClause(request.getDateFrom(), request.getDateUpto(), COL_ORD_WUNSCHDATUM));
		b.append("SELECT ");
		b.append(COL_ORD_PK);
		b.append(", ");
		new StatisticAuftrag().getSelectColumns(b);
		b.append(" FROM ");
		qualifyTableName(b, TBL_ORDER_STATISTICS);
		append(b, " WHERE ", w);
		w.releaseSilent();
		final Connection connection = connectToLogiware();
		if (connection != null) {
			try {
			    final JDBCResultSet resultSet = doFetchQuery(b.release());
				while (resultSet.next()) {
					final StatisticAuftrag auftrag = new StatisticAuftrag();
					auftrag.loadRow(resultSet);
					result.add(auftrag);
				}
			} catch (final SQLException e) {	 
				Logger.log(LogLevel.ERROR, e);
				result.clear();
			} finally {
				close();
			}
		}
		return result;
	}
	
	private List<StatisticForm> loadFormulare(final List<StatisticAuftrag> auftragList, final FormClassCode type) {
		final List<StatisticForm> result = new ArrayList<StatisticForm>();
		final Connection connection = connectToDispo();
		final int auftragCount = auftragList.size();
		int index = 0;
		if (connection != null) {
			try {
				while (index < auftragCount) {
					// build the IN-clause (max. of 250 Auftrag)
					int chunkCount = 0;
					final StringBuilder b = new StringBuilder(2048);
					while (index < auftragCount && chunkCount < 250) {
						final StatisticAuftrag auftrag = auftragList.get(index++);
						if (b.length() > 0)
							b.append(", ");
						b.append(auftrag.auftragNr);
						++chunkCount;
					}
					// build query
					final String sql = "SELECT " +
					                     "A.auftragNummer, " +
					                     "A.projektNummer, " +
					                     "F.typeId, " +
					                     "F.description, " +
					                     "F.content " +
					                   "FROM " +
					                     "Auftrag A, "+
					                     "AuftragFormular F " +
					                   "WHERE " +
					                     "F.auftrag_rowId = A.rowId AND " +
					                     "F.typeId = "+type.getId()+" AND " +
					                     "A.auftragNummer IN ("+b.toString()+")";
				    final JDBCResultSet resultSet = doFetchQuery(sql);
					while (resultSet.next()) {
						final StatisticForm form = new StatisticForm();
						form.loadRow(resultSet);
						result.add(form);
					}
				}
			} catch (final SQLException e) {
				Logger.log(LogLevel.ERROR, e);
				result.clear();
			} finally {
				close();
			}
		}
		return result;
	}
	
	public CSVFile buildCSVServiceSC(final List<StatisticForm> formList) {
		Collections.sort(formList, new Comparator<StatisticForm>() {
			@Override
			public int compare(final StatisticForm o1, final StatisticForm o2) {
				final ValueSet v1 = o1.getValueSet();
				final ValueSet v2 = o2.getValueSet();
				final long t1 = v1.getTimestamp("Eingangsdatum", TimestampUtil.NULL_TIMESTAMP);
				final long t2 = v2.getTimestamp("Eingangsdatum", TimestampUtil.NULL_TIMESTAMP);
				int r = TimestampUtil.compare(t1, t2);
				if (r == 0) {
					final String p1 = v1.getString("Projektnummer", null);
					final String p2 = v2.getString("Projektnummer", null);
					r = Strings.compare(p1, p2);
				}
				return r;
			}
		});
		final CSVFile csvFile = new CSVFile();
		CSVLine csvLine = csvFile.addLine();
		csvLine.setColumn(0, "AuftragsNummer");
		csvLine.setColumn(1, "ProjektNummer");
		csvLine.setColumn(2, "Eingangsdatum");
		csvLine.setColumn(3, "Objekt");

		for (final StatisticForm form : formList) {
			final ValueSet valueSet = form.getValueSet();
			csvLine = csvFile.addLine();
			csvLine.setColumn(0, form.auftragNummer);
			csvLine.setColumn(1, form.projektNummer);
			final long eingangsDatum = valueSet.getTimestamp("Eingangsdatum", TimestampUtil.NULL_TIMESTAMP); 
			csvLine.setColumn(2, TimestampUtil.DATE10.format(eingangsDatum));
			final String objekt = valueSet.getString("Objekt", ""); 
			csvLine.setColumn(3, objekt.replace('\n', ' '));
		}
		return csvFile;
	}

	public CSVFile buildCSVGranitService(final List<StatisticForm> formList) {
		Collections.sort(formList, new Comparator<StatisticForm>() {
			@Override
			public int compare(final StatisticForm o1, final StatisticForm o2) {
				final ValueSet v1 = o1.getValueSet();
				final ValueSet v2 = o2.getValueSet();
				final String m1 = v1.getString("Monteur", null);
				final String m2 = v2.getString("Monteur", null);
				int r = Strings.compare(m1, m2);
				if (r == 0) {
					final String p1 = v1.getString("ProjektNummer", null);
					final String p2 = v2.getString("ProjektNummer", null);
					r = Strings.compare(p1, p2);
				}
				return r;
			}
		});
		
		final CSVFile csvFile = new CSVFile();
		CSVLine csvLine = csvFile.addLine();
		csvLine.setColumn(0, "AuftragsNummer");
		csvLine.setColumn(1, "ProjektNummer");
		csvLine.setColumn(2, "Eingangsdatum");
		csvLine.setColumn(3, "Monteur");
		csvLine.setColumn(4, "Objekt");
		csvLine.setColumn(5, "Servicegrund");

		for (final StatisticForm form : formList) {
			final ValueSet valueSet = form.getValueSet();
			csvLine = csvFile.addLine();
			csvLine.setColumn(0, form.auftragNummer);
			csvLine.setColumn(1, form.projektNummer);
			final long eingangsDatum = valueSet.getTimestamp("Eingangsdatum", TimestampUtil.NULL_TIMESTAMP); 
			csvLine.setColumn(2, TimestampUtil.DATE10.format(eingangsDatum));
			final String monteur = AbstractCode.toString(AbstractCode.getByKey(valueSet.getString("Monteur", null), LWMonteurPfyCode.class));
			csvLine.setColumn(3, monteur);
			final String objekt = valueSet.getString("Objekt", ""); 
			csvLine.setColumn(4, objekt.replace('\n', ' '));
			final List<String> gruende = new ArrayList<String>(16);
			if (valueSet.getBoolean("Silikonfuge", false))
				gruende.add("Silikonfuge");
			if (valueSet.getBoolean("Steinkittfuge", false))
				gruende.add("Steinkittfuge");
			if (valueSet.getBoolean("Riss", false))
				gruende.add("Riss");
			if (valueSet.getBoolean("Fleck", false))
				gruende.add("Fleck");
			if (valueSet.getBoolean("Nachpolitur", false))
				gruende.add("Nachpolitur");
			if (valueSet.getBoolean("Lieferung", false))
				gruende.add("Lieferung");
			if (valueSet.getBoolean("Richten", false))
				gruende.add("Richten");
			if (valueSet.getBoolean("Sonstiges", false))
				gruende.add(valueSet.getString("SonstigesText", ""));
			final StringBuilder grund = new StringBuilder(32);
			for (String text : gruende) {
				if (grund.length() > 0)
					grund.append(", ");
				grund.append(text);
			}
			csvLine.setColumn(5, grund.toString());
		}
		return csvFile;
	}

}

final class StatisticForm {

	public int auftragNummer;
	public String projektNummer;
	public FormClassCode typeId;
	public String description;
	private String content;
	private ValueSet valueSet;

	public void loadRow(final JDBCResultSet resultSet) throws SQLException {
		auftragNummer = resultSet.getInt("auftragNummer");
		projektNummer = resultSet.getString("projektNummer");
		int typeId = resultSet.getInt("typeId");
		this.typeId = (FormClassCode) AbstractCode.getById(typeId, FormClassCode.class);
		description = resultSet.getString("description");
		content = resultSet.getString("content");
	}

	public ValueSet getValueSet() {
		if (valueSet == null) {
			 valueSet = ValueSet.fromString(content);
		}
		return valueSet;
	}

}
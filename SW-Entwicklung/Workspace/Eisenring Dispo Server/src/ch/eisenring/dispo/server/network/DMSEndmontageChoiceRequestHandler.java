package ch.eisenring.dispo.server.network;

import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.dispo.server.DSPServer;
import ch.eisenring.dispo.shared.endmontageprint.EndmontagePrintDocument;
import ch.eisenring.dispo.shared.network.packets.PacketDMSEndmontageChoiceReply;
import ch.eisenring.dispo.shared.network.packets.PacketDMSEndmontageChoiceRequest;
import ch.eisenring.dms.service.DMSService;
import ch.eisenring.dms.service.api.DMSDocumentImage;
import ch.eisenring.dms.service.api.DMSObject;
import ch.eisenring.dms.service.codetables.DMSDocumentType;
import ch.eisenring.dms.service.codetables.DMSPropertyCode;
import ch.eisenring.dms.service.proxy.DMSDocumentHandle;
import ch.eisenring.dms.service.proxy.DMSFolderHandle;
import ch.eisenring.logiware.LWProjektKey;
import ch.eisenring.network.PacketSink;
import ch.eisenring.network.packet.AbstractPacket;

public class DMSEndmontageChoiceRequestHandler extends AbstractDSPPacketHandler {

	private DMSEndmontageChoiceRequestHandler(final DSPServer server) {
		super(server, PacketDMSEndmontageChoiceRequest.class);
	}

	@Override
	public PacketDMSEndmontageChoiceReply handle(final AbstractPacket packet) {
		final PacketDMSEndmontageChoiceRequest request = (PacketDMSEndmontageChoiceRequest) packet;
		final PacketDMSEndmontageChoiceReply reply = handle(server, request);
		return reply;
	}

	@Override
	public void handle(final AbstractPacket packet, final PacketSink sink) {
		final PacketDMSEndmontageChoiceRequest request = (PacketDMSEndmontageChoiceRequest) packet;
		final PacketDMSEndmontageChoiceReply reply = handle(server, request);
		sink.sendPacket(reply);
	}

	public static PacketDMSEndmontageChoiceReply handle(final DSPServer server,
			final PacketDMSEndmontageChoiceRequest request) {
		try {
			final PacketDMSEndmontageChoiceReply reply = handleImpl(server, request);
			if (reply != null)
				return reply;
			return PacketDMSEndmontageChoiceReply.create(request, ErrorMessage.ERROR);
		} catch (final Exception e) {
			return PacketDMSEndmontageChoiceReply.create(request, new ErrorMessage(e));
		}
	}

	static interface F1 {
		public void process(final DMSObject object);
	}

	private static void doType(
			final DMSService service,
			final DMSFolderHandle projectFolder,
			final DMSDocumentType docType,
			final F1 processor) {
		try { // Y Montagerapport
			List<DMSDocumentHandle> l = service.getDocumentsByProperty(projectFolder, DMSPropertyCode.DOCTYPE.getId(), Strings.toString(docType.getKey()));
			for (final DMSDocumentHandle id : l) {
				final DMSObject object = service.getObject(id);
				processor.process(object);
			}
		} catch (final Exception e) {
			// ignore
		}
	}

	private static PacketDMSEndmontageChoiceReply handleImpl(final DSPServer server,
			final PacketDMSEndmontageChoiceRequest request) throws Exception {
		// locate project folder
		final DMSService service = server.locateService(DMSService.class);
		final LWProjektKey projectKey = request.getProjektKey();
		final DMSFolderHandle projectFolder = service.getProjectFolder(projectKey.getProjektnummer());
		final PacketDMSEndmontageChoiceReply reply = PacketDMSEndmontageChoiceReply.create(request, ErrorMessage.OK);

		doType(service, projectFolder, DMSDocumentType.MONTAGERAPPORT_APP, (object) -> {
			reply.addDocument(EndmontagePrintDocument.create(
					object, DMSDocumentType.MONTAGERAPPORT_APP, true));
		});
		doType(service, projectFolder, DMSDocumentType.MONTAGESCHEIN_135, (object) -> {
			reply.addDocument(EndmontagePrintDocument.create(
					object, DMSDocumentType.MONTAGESCHEIN_135, true));
		});
		doType(service, projectFolder, DMSDocumentType.MONTAGESCHEIN_134, (object) -> {
			reply.addDocument(EndmontagePrintDocument.create(
					object, DMSDocumentType.MONTAGESCHEIN_134, true));
		});
		doType(service, projectFolder, DMSDocumentType.STATUSRAPPORT, (object) -> {
			reply.addDocument(EndmontagePrintDocument.create(
					object, DMSDocumentType.STATUSRAPPORT, true));
		});
		try { // Ausführungspläne
			List<DMSDocumentImage> l = service.getAusfuehrungsplaene(projectKey.getProjektnummer());
			for (final DMSDocumentImage file : l) {
				final DMSObject object = service.getObject(file);
				if (Strings.contains(object.getObjectname(), "Perspektive"))
					continue;
				reply.addDocument(EndmontagePrintDocument.create(object, DMSDocumentType.ABDECKUNGSPLAN,
						"Ausführungsplan", true));
				
			}
		} catch (final Exception e) {
			// ignore
		}
		doType(service, projectFolder, DMSDocumentType.MONTAGESCHEIN_115, (object) -> {
			reply.addDocument(EndmontagePrintDocument.create(
					object, DMSDocumentType.MONTAGESCHEIN_115, true));
		});


//		DMSObject object = service.getAusfuehrungsplaene(projectKey.getProjektnummer());
//		reply.addDocument(EndmontagePrintDocument.create(object, 
//				DMSDocumentType.KROKI, "Test", true));
//		final String projectNumber = LWProjektKey.getSignificantProjektnummer(request.getProjectNumber());
//		final String extensionNumber = LWProjektKey.get
//		service.getFolderByProperty(null, DMSPropertyCode.PROJECTNUMBER, );
		
		return reply;
	}

}

package ch.eisenring.dispo.server.logiware;

import java.io.IOException;

import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.io.StreamReader;
import ch.eisenring.core.io.StreamWriter;
import ch.eisenring.core.io.Streamable;
import ch.eisenring.logiware.LWAuftragKey;
import ch.eisenring.logiware.LWPositionKey;
import ch.eisenring.logiware.code.pseudo.PositionStatusCode;
import ch.eisenring.logiware.code.soft.GSECode;
import ch.eisenring.lw.stringcache.LWStringCaches;

/**
 * Plain POJO that represents open order position.
 * 
 * This object does not physically exist in the data model,
 * its a derived state.
 */
public final class LWAusstandPosition implements Streamable {

	private transient int auftragNummer;
	private transient GSECode gse;
	private transient double positionNummer;

	private transient String produktNummer;
	private transient String produktBezeichnung;
	private transient PositionStatusCode positionStatus;
	private transient boolean baustellenLieferung;
	private transient Integer belegNummerBestellung;

	private LWAusstandPosition() {
	}

	// --------------------------------------------------------------
	// ---
	// --- Factory methods
	// ---
	// --------------------------------------------------------------
	
	// none

	// --------------------------------------------------------------
	// ---
	// --- Getters
	// ---
	// --------------------------------------------------------------
	public int getAuftragNummer() {
		return auftragNummer;
	}

	public GSECode getGse() {
		return gse;
	}

	public double getPositionNummer() {
		return positionNummer;
	}

	public String getProduktNummer() {
		return produktNummer;
	}

	public String getProduktBezeichnung() {
		return produktBezeichnung;
	}

	public PositionStatusCode getPositionStatus() {
		return positionStatus;
	}

	public boolean isBaustellenLieferung() {
		return baustellenLieferung;
	}

	public Integer getBelegNummerBestellung() {
		return belegNummerBestellung;
	}	

	// --------------------------------------------------------------
	// ---
	// --- Derived getters
	// ---
	// --------------------------------------------------------------
	private transient LWAuftragKey auftragKey;
	private transient LWPositionKey positionKey;

	public LWAuftragKey getAuftragKey() {
		if (auftragKey == null)
			auftragKey = LWAuftragKey.get(auftragNummer, gse);
		return auftragKey;
	}

	public LWPositionKey getPositionKey() {
		if (positionKey == null)
			positionKey = LWPositionKey.get(positionNummer, getAuftragKey());
		return positionKey;
	}

	// --------------------------------------------------------------
	// ---
	// --- Streamable
	// ---
	// --------------------------------------------------------------
	@Override
	public void read(final StreamReader reader) throws IOException {
		auftragNummer = reader.readInt();
		gse = reader.readCode(GSECode.class);
		positionNummer = reader.readDouble();
		produktNummer = reader.readString(LWStringCaches.PRODUKTNUMMER);
		produktBezeichnung = reader.readString(LWStringCaches.PRODUKTBEZEICHNUNG);
		positionStatus = reader.readCode(PositionStatusCode.class);
		baustellenLieferung = reader.readBoolean();
		belegNummerBestellung = (Integer) reader.readObject();
	}

	@Override
	public void write(final StreamWriter writer) throws IOException {
		writer.writeInt(auftragNummer);
		writer.writeCode(gse);
		writer.writeDouble(positionNummer);
		writer.writeString(produktNummer);
		writer.writeString(produktBezeichnung);
		writer.writeCode(positionStatus);
		writer.writeBoolean(baustellenLieferung);
		writer.writeObject(belegNummerBestellung);
	}
	
	// --------------------------------------------------------------
	// ---
	// --- Object overrides
	// ---
	// --------------------------------------------------------------
	@Override
	public int hashCode() {
		return getPositionKey().hashCode();
	}

	@Override
	public boolean equals(final Object o) {
		if (!(o instanceof LWAusstandPosition))
			return false;
		final LWAusstandPosition that = (LWAusstandPosition) o;
		return this.positionNummer == that.positionNummer
			&& this.auftragNummer == that.auftragNummer
			&& AbstractCode.equals(this.gse, that.gse);
	}

}

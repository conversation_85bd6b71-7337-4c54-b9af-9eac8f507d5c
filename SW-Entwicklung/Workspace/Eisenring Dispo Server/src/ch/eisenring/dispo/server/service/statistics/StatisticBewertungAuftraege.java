package ch.eisenring.dispo.server.service.statistics;

import static ch.eisenring.logiware.ILogiwareVertriebConstants.COL_ORD_ABWICKLUNGSART;
import static ch.eisenring.logiware.ILogiwareVertriebConstants.COL_ORD_GSE;
import static ch.eisenring.logiware.ILogiwareVertriebConstants.COL_ORD_PK;
import static ch.eisenring.logiware.ILogiwareVertriebConstants.COL_ORD_WUNSCHDATUM;
import static ch.eisenring.logiware.ILogiwareVertriebConstants.TBL_ORDER_STATISTICS;

import java.sql.Connection;
import java.sql.SQLException;

import ch.eisenring.core.csv.CSVFile;
import ch.eisenring.core.csv.CSVLine;
import ch.eisenring.core.datatypes.primitives.Primitives;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.core.logging.Logger.LogLevel;
import ch.eisenring.dispo.server.DSPServer;
import ch.eisenring.dispo.server.auftragbewertung.AuftragBewertungType;
import ch.eisenring.dispo.shared.codetables.DSPStatisticTypeCodes;
import ch.eisenring.jdbc.JDBCResultSet;
import ch.eisenring.statistics.shared.codetables.STDateGranularityCode;
import ch.eisenring.statistics.shared.network.PacketStatisticRequest;
import ch.eisenring.statistics.shared.network.PacketStatisticResponse;

public class StatisticBewertungAuftraege extends StatisticsBase {

	public StatisticBewertungAuftraege(final DSPServer server) {
		super(server);
	}

	@Override
	public PacketStatisticResponse createResponse(final PacketStatisticRequest packet) {
		final AuslastungKWAuftragBewertung bewertung = AuslastungKWAuftragBewertung.create(server, DSPStatisticTypeCodes.STATTYPE_RESOURCES_GSE2501_ALL); 

		PacketStatisticResponse result = null;
		final String sql = buildQuery(packet);

		final CSVFile csvFile = new CSVFile();
		CSVLine csvLine = csvFile.addLine();
		csvLine.setColumn(0, "AuftragsNummer");
		csvLine.setColumn(1, "ProjektNummer");
		csvLine.setColumn(2, "WunschDatum");
		csvLine.setColumn(3, "AbwicklungsArt");
		csvLine.setColumn(4, "Bewertung");
		// --- execute query
		final Connection connection = connectToLogiware();
		if (null!=connection) {
			try {
				final JDBCResultSet resultSet = doFetchQuery(sql);
				while (resultSet.next()) {
					final StatisticAuftrag auftrag = new StatisticAuftrag();
					auftrag.loadRow(resultSet);
					csvLine = csvFile.addLine();
					csvLine.setColumn(0, auftrag.auftragNr);
					csvLine.setColumn(1, auftrag.projektNr);
					csvLine.setColumn(2, auftrag.wunschDatum);
					csvLine.setColumn(3, auftrag.abwicklungsArt);
					final double v = bewertung.getBewertung(auftrag, auftrag, AuftragBewertungType.FA_ANZAHL);
					csvLine.setColumn(4, v);
				}
				resultSet.close();
			} catch (final SQLException e) {	 
				Logger.log(LogLevel.ERROR, e);
			} finally {
				close();
			}
		}
		result = PacketStatisticResponse.create(packet);
		String title = STDateGranularityCode.getDateRangeText(result);
		title = "Bewertung Aufträge\n("+title+")";
		result.setChartTitle(title);
		result.setReportTitle(title);
		result.addAttachment("Bewertung_Auftraege.csv", csvFile.toBinary(null));
		result.setXAxisLabel("Unbenannt");
		result.setYAxisLabel("Unbenannt");
		result.setDataSeries(new double[0][0]);
		result.setLabels(Primitives.EMPTY_STRING_ARRAY);
		return result;		
	}

	protected String buildQuery(final PacketStatisticRequest request) {
		final StringMaker w = StringMaker.obtain(128);
		final StringMaker b = StringMaker.obtain(256);
		append(w, "", getDateRestrictionClause(request.getDateFrom(), request.getDateUpto(), COL_ORD_WUNSCHDATUM));
		append(w, " AND ", COL_ORD_ABWICKLUNGSART + " IN (115, 134, 135)");
		append(w, " AND ", COL_ORD_GSE + " IN ('2501')");
		b.append("SELECT ");
		new StatisticAuftrag().getSelectColumns(b);
		b.append(" FROM ");
		qualifyTableName(b, TBL_ORDER_STATISTICS);
		append(b, " WHERE ", w);
		append(b, " ORDER BY ", COL_ORD_WUNSCHDATUM);
		append(b, ", ", COL_ORD_PK);
		w.releaseSilent();
		return b.release();
	}

}

package ch.eisenring.dispo.server.network;

import java.sql.SQLException;

import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.collections.Collection;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.Map;
import ch.eisenring.core.collections.Set;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.collections.impl.ArraySet;
import ch.eisenring.core.collections.impl.HashMap;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.logging.Logger.LogLevel;
import ch.eisenring.dispo.server.DSPServer;
import ch.eisenring.dispo.server.context.DSPContextSink;
import ch.eisenring.dispo.service.codetables.YesNoCode;
import ch.eisenring.dispo.service.model.DSPBasisProjekt;
import ch.eisenring.dispo.shared.metamodel.DSPBasisProjektMeta;
import ch.eisenring.dispo.shared.network.packets.PacketBasisprojektGetReply;
import ch.eisenring.dispo.shared.network.packets.PacketBasisprojektGetRequest;
import ch.eisenring.dispo.shared.pojo.DSPContext;
import ch.eisenring.dms.service.DMSService;
import ch.eisenring.dms.service.codetables.DMSDocumentType;
import ch.eisenring.jdbc.JDBCResultSet;
import ch.eisenring.jdbc.StatementParameters;
import ch.eisenring.jdbc.StatementWrapper;
import ch.eisenring.logiware.LWProjektKey;
import ch.eisenring.logiware.code.service.LogiwareService;
import ch.eisenring.logiware.code.soft.GSECode;
import ch.eisenring.lw.condition.Factory;
import ch.eisenring.lw.meta.LWProjektMeta;
import ch.eisenring.lw.model.navigable.LWObjectCache;
import ch.eisenring.lw.model.navigable.LWProjekt;
import ch.eisenring.model.TransactionContext;
import ch.eisenring.model.engine.jdbc.JDBCObjectMapper;
import ch.eisenring.model.engine.jdbc.SingleTableMapping;
import ch.eisenring.model.engine.jdbc.TableMapping;
import ch.eisenring.model.mapping.ObjectMapping;
import ch.eisenring.model.mapping.POJOMapping;
import ch.eisenring.network.PacketSink;
import ch.eisenring.network.packet.AbstractPacket;

public final class BasisprojektGetRequestHandler extends AbstractDSPPacketHandler {

	public BasisprojektGetRequestHandler(final DSPServer server) {
		super(server, PacketBasisprojektGetRequest.class);
	}

	@Override
	public PacketBasisprojektGetReply handle(final AbstractPacket packet) {
		final PacketBasisprojektGetRequest request = (PacketBasisprojektGetRequest) packet;
		return handle(server, request);
	}

	@Override
	public void handle(final AbstractPacket packet, final PacketSink sink) {
		final PacketBasisprojektGetReply reply = handle(packet);
		sink.sendPacket(reply, false);
	}

	public static PacketBasisprojektGetReply handle(final DSPServer server, final PacketBasisprojektGetRequest request) {
		try {
			final PacketBasisprojektGetReply reply = handleImpl(server, request);
			if (reply != null)
				return reply;
			return PacketBasisprojektGetReply.create(request, ErrorMessage.ERROR);
		} catch (final Exception e) {
			return PacketBasisprojektGetReply.create(request, new ErrorMessage(e));
		}
		
	}

	final static ObjectMapping<DSPBasisProjekt> PM =
			POJOMapping.get(DSPBasisProjektMeta.METACLASS, DSPBasisProjekt.class);
	final static SingleTableMapping TM =
			TableMapping.get(PM.getMetaClass(), DSPBasisProjektMeta.TABLE);
	final static JDBCObjectMapper<DSPBasisProjekt> OM =
			JDBCObjectMapper.get(TM, PM);
		
	private static PacketBasisprojektGetReply handleImpl(
			final DSPServer server, final PacketBasisprojektGetRequest request) throws Exception {
		final Map<LWProjektKey, DSPBasisProjekt> result = new HashMap<>();

		final List<LWProjektKey> requestedKeys = request.getRequestedKeys();
		if (requestedKeys.isEmpty()) {
			return PacketBasisprojektGetReply.create(request, List.emptyReadOnly(DSPBasisProjekt.class));
		}
		// fetch the requested basisprojekt
		final DSPContextSink sink = new DSPContextSink() {
			@Override
			protected void storeImpl(final TransactionContext context) throws SQLException {
				final StringMaker sql = StringMaker.obtain(128);
				final Collection<String> projektnummern = LWProjektKey.getProjektnummern(requestedKeys);
				OM.appendSelectSQL(sql, this, TableMapping.NO_TABLE_ALIAS);
				sql.append(" WHERE ");
				sql.append(TM.getColumn(DSPBasisProjektMeta.ATR_PROJEKTNUMMER));
				sql.append(" IN ");
				prepareIn(sql, projektnummern);
				final StatementWrapper select = prepareStatement(sql.release());
				final List<DSPBasisProjekt> basisprojekte;
				try {
					final StatementParameters params = new StatementParameters();
					params.add(projektnummern);
					select.setParameters(params);
					final JDBCResultSet resultSet = select.executeQuery();
					try {
						// load the result set
						basisprojekte = OM.loadRows(context, resultSet, requestedKeys.size());
					} finally {
						closeSilent(resultSet);
					}
				} finally {
					closeSilent(select);
				}
				// filter out records where GSE does not match (there shouldn't be any,
				// but maybe we will have more than one GSE one day).
				for (int i=basisprojekte.size()-1; i>=0; --i) {
					final DSPBasisProjekt basisprojekt = basisprojekte.get(i);
					final LWProjektKey key = basisprojekt.getKey();
					if (requestedKeys.contains(key)) {
						result.put(key, basisprojekt);
					}
				}
				
				// update properties derived from DMS and LW
				try {
					updateFromDMS(server, result.values());
					updateFromLogiware(server, result.values());
				} catch (final Exception e) {
					throw new SQLException(e.getMessage(), e);
				}
				
				// now create all projects that we did not find in the database
				final List<DSPBasisProjekt> insertObjects = new ArrayList<DSPBasisProjekt>();
				for (final LWProjektKey key : requestedKeys) {
					if (result.get(key) != null)
						continue;
					final DSPBasisProjekt basisprojekt = DSPBasisProjekt.create(key);
					result.put(key, basisprojekt);
					insertObjects.add(basisprojekt);
				}
				OM.insert(this, insertObjects);
			}
		}; 
		
		final DSPContext context = new DSPContext();
		sink.setLogLevel(LogLevel.TRACE);
		context.store(sink);
		return PacketBasisprojektGetReply.create(request, result.values());
	}

	/**
	 * Determine if "Werkvertrag/Deklaration Solidarhaftung" are present in DMS.
	 */
	private static void updateFromDMS(final DSPServer server, final java.util.Collection<DSPBasisProjekt> projekte) throws Exception {
		if (projekte == null || projekte.isEmpty())
			return;
		// locate service
		final DMSService service = server.locateService(DMSService.class);

		for (final DSPBasisProjekt projekt : projekte) {
			final String projektnummer = projekt.getProjektnummer();
			try {
				final int count = service.countDocuments(projektnummer, DMSDocumentType.WERKVERTRAG);
				projekt.setWerkvertrag(YesNoCode.valueOf(count > 0));
			} catch (final Exception e) {
				projekt.setWerkvertrag(YesNoCode.NO);
			}
			try {
				final int count = service.countDocuments(projektnummer, DMSDocumentType.SOLIDARHAFTUNG_DEKLARATION);
				projekt.setDeklarationSolidarhaftung(YesNoCode.valueOf(count > 0));
			} catch (final Exception e) {
				projekt.setDeklarationSolidarhaftung(YesNoCode.NO);
			}
		}
	}

	/**
	 * Update data from Logiware in DSPBasisProjekt
	 */
	private static void updateFromLogiware(final DSPServer server, final java.util.Collection<DSPBasisProjekt> dspProjekte) throws Exception {
		if (dspProjekte == null || dspProjekte.isEmpty())
			return;
		// load from Logiware
		final LogiwareService service = server.locateService(LogiwareService.class);
		final LWObjectCache cache = service.createObjectCache();
		final Set<String> projektnummern = new ArraySet<>();
		for (final DSPBasisProjekt basisProjekt : dspProjekte) {
			projektnummern.add(basisProjekt.getProjektnummer());
		}
		final Collection<LWProjekt> lwProjekte = cache.load(LWProjekt.class,
				Factory.in(LWProjektMeta.ATR_PROJEKTNUMMER, (Object[]) projektnummern.toArray()));
		// update DSPBasisProjekt instances
		for (final LWProjekt lwProjekt : lwProjekte) {
			// find matching DSPBasisProjekt
			final String projectnumber = lwProjekt.getProjektnummer();
			final GSECode gse = lwProjekt.getGSEProjekt();
			for (final DSPBasisProjekt dspProjekt : dspProjekte) {
				if (Strings.equals(projectnumber, dspProjekt.getProjektnummer()) &&
					AbstractCode.equals(gse, dspProjekt.getGSE())) {
					// update DSPBasisProjekt
					dspProjekt.setAnzahlKuechen(lwProjekt.getObjektkuechenAnzahl());
				}
			}
		}
	}

}

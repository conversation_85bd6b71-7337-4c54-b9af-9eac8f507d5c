package ch.eisenring.dispo.server.montageprint;

import ch.eisenring.app.shared.SoftwareComponent;
import ch.eisenring.core.collections.Collection;
import ch.eisenring.core.collections.Map;
import ch.eisenring.core.collections.impl.HashMap;
import ch.eisenring.dispo.shared.pojo.mbp.MBPAuftrag;
import ch.eisenring.dispo.shared.pojo.mbp.MBPStatusReportLine;
import ch.eisenring.print.shared.model.AbstractReportSource;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.WriterException;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.QRCodeWriter;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;

import java.awt.image.BufferedImage;

public class MBPQRCodePrintSource extends AbstractReportSource {

    protected final SoftwareComponent component;
    protected final MBPAuftrag auftrag;

    public MBPQRCodePrintSource(final SoftwareComponent component,
                                MBPAuftrag auftrag,
                                final Collection<MBPStatusReportLine> resultList) {
        super(resultList);
        this.auftrag = auftrag;
        this.component = component;
    }

    @Override
    public void populateParameters(final Map<String, Object> map) {
        super.populateParameters(map);
        map.put("PrintTimestamp", TIMESTAMP_FORMAT.format(System.currentTimeMillis()));
        map.put("projektNummer", auftrag.getProjektKey().getProjektnummer());
        map.put("qrCode", generateQRCodeImage());
    }

    /**
     * Original QR-Code Format:
     * HEAG_ZL     Formatkennzeichen
     * 123456      Projektnummer aka Kommisionsnummer
     * 7890123     Belegnummer aka Auftragsnummer // --> Not relevant here
     * 45678901    Formularnummer von Montageschein //--> Not relevant here
     * 1           Verarbeitungsart: 1 = Montage, 2 = Endmontage // --> Not relevant here
     *
     * @return
     */
    public BufferedImage generateQRCodeImage() {
        final String qrCodeString = buildQRCodeString();
        return createQRCodeFromString(qrCodeString);
    }

    public BufferedImage createQRCodeFromString(String qrCodeString) {
        QRCodeWriter qrCodeWriter = new QRCodeWriter();
        final Map<EncodeHintType, Object> encodingHints = new HashMap<>();
        encodingHints.put(EncodeHintType.MARGIN, 0);
        encodingHints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.H);

        BitMatrix bitMatrix = null;
        try {
            bitMatrix = qrCodeWriter.encode(qrCodeString, BarcodeFormat.QR_CODE, 68, 68, encodingHints);
        } catch (WriterException e) {
            e.printStackTrace();
        }
        return MatrixToImageWriter.toBufferedImage(bitMatrix);
    }

    private String buildQRCodeString() {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("HEAG_ZL");
        stringBuilder.append("\n");
        stringBuilder.append(auftrag.getProjektKey().getProjektnummer());
        return stringBuilder.toString();
    }
}

package ch.eisenring.dispo.server.entities;

import java.util.Date;

import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.dispo.shared.codetables.DispoEventCode;
import ch.eisenring.dispo.shared.model.AbstractModel;
import ch.eisenring.dispo.shared.model.DispoEvent;

public class DispoEventEntity extends AbstractEntity {

	private int eventType;
	private Date occuredWhen;
	private Date concernsWhen;
	private String mainObject;
	private String triggeredBy;
	private String detailMessage;
	private long recipientUserId;
	private int wasMailed;
	
	@Override
	public Class<? extends AbstractModel> getModelClass() {
		return DispoEvent.class;
	}

	@Override
	public void updateModel(AbstractModel model) {
		super.updateModel(model);
		final DispoEvent event = (DispoEvent) model;
		event.setEventType((DispoEventCode) AbstractCode.getById(getEventType(), DispoEventCode.class));
		event.setOccuredWhen(getOccuredWhen());
		event.setConcernsWhen(getConcernsWhen());
		event.setMainObject(getMainObject());
		event.setTriggeredBy(getTriggeredBy());
		event.setDetailMessage(getDetailMessage());
		event.setRecipientUserId(getRecipientUserId());
		event.setWasMailed(getWasMailed()!=0);
	}
	
	@Override
	public void updateEntity(AbstractModel model) {
		super.updateEntity(model);
		final DispoEvent event = (DispoEvent) model;

		int eventId = event.getEventType().getId();
		updateDirty(eventId, getEventType());
		setEventType(eventId);

		updateDirty(event.getOccuredWhen(), getOccuredWhen());
		setOccuredWhen(event.getOccuredWhen());

		updateDirty(event.getConcernsWhen(), getConcernsWhen());
		setConcernsWhen(event.getConcernsWhen());
		
		updateDirty(event.getMainObject(), getMainObject());
		setMainObject(event.getMainObject());
		
		updateDirty(event.getTriggeredBy(), getTriggeredBy());
		setTriggeredBy(event.getTriggeredBy());
		
		updateDirty(event.getDetailMessage(), getDetailMessage());
		setDetailMessage(event.getDetailMessage());
		
		updateDirty(event.getRecipientUserId(), getRecipientUserId());
		setRecipientUserId(event.getRecipientUserId());
		
		int wasMailed = event.getWasMailed() ? 1 : 0;
		updateDirty(wasMailed, getWasMailed());
		setWasMailed(wasMailed);
	}
	
	@Override
	public String getObjectDescription() {
		final StringMaker b = StringMaker.obtain(128);
		b.append("DispoEvent(");
		b.append("Type=");
		b.append(AbstractCode.getById(getEventType(), DispoEventCode.class).toString());
		b.append(";ConcernsWhen=");
		b.append(getConcernsWhen(), TimestampUtil.DATE10);
		b.append(')');
		return b.release();
	}

	@Override
	public boolean evictOnTransactionBoundary() {
		return true;
	}

	/****************************************************************
	 * Getters / Setters
	 ****************************************************************/
	public int getEventType() {
		return eventType;
	}

	public void setEventType(int eventType) {
		this.eventType = eventType;
	}

	public Date getOccuredWhen() {
		return occuredWhen;
	}

	public void setOccuredWhen(Date occuredWhen) {
		this.occuredWhen = occuredWhen;
	}

	public Date getConcernsWhen() {
		return concernsWhen;
	}

	public void setConcernsWhen(Date concernsWhen) {
		this.concernsWhen = concernsWhen;
	}

	public String getMainObject() {
		return mainObject;
	}

	public void setMainObject(String mainObject) {
		this.mainObject = mainObject;
	}

	public String getTriggeredBy() {
		return triggeredBy;
	}

	public void setTriggeredBy(String triggeredBy) {
		this.triggeredBy = triggeredBy;
	}

	public String getDetailMessage() {
		return detailMessage;
	}

	public void setDetailMessage(String detailMessage) {
		this.detailMessage = detailMessage;
	}

	public long getRecipientUserId() {
		return recipientUserId;
	}

	public void setRecipientUserId(long recipientUserId) {
		this.recipientUserId = recipientUserId;
	}

	public int getWasMailed() {
		return wasMailed;
	}

	public void setWasMailed(int wasMailed) {
		this.wasMailed = wasMailed;
	}
	
}

package ch.eisenring.dispo.server.network;

import static ch.eisenring.lw.LWMapping.TM_AUFTRAG;
import static ch.eisenring.lw.LWMapping.TM_AUFTRAGKOPF;

import java.sql.SQLException;
import java.util.Collections;

import ch.eisenring.core.poi.POIUtil;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;

import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.Map;
import ch.eisenring.core.collections.Set;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.collections.impl.HashMap;
import ch.eisenring.core.collections.impl.HashSet;
import ch.eisenring.core.csv.CSVFile;
import ch.eisenring.core.csv.CSVLine;
import ch.eisenring.core.datatypes.date.DateGranularityCode;
import ch.eisenring.core.datatypes.date.Period;
import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.io.binary.BinaryHolder;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.core.sort.Comparator;
import ch.eisenring.core.sort.Sorter;
import ch.eisenring.core.util.file.FileImage;
import ch.eisenring.core.util.file.FileUtil;
import ch.eisenring.dispo.server.DSPServer;
import ch.eisenring.dispo.shared.network.packets.PacketReportKOKReply;
import ch.eisenring.dispo.shared.network.packets.PacketReportKOKRequest;
import ch.eisenring.jdbc.JDBCResultSet;
import ch.eisenring.jdbc.RowHandler;
import ch.eisenring.jdbc.StatementParameters;
import ch.eisenring.logiware.LWAuftragKey;
import ch.eisenring.logiware.LWContextSource;
import ch.eisenring.logiware.code.pseudo.AbwicklungsartCode;
import ch.eisenring.logiware.code.soft.GSECode;
import ch.eisenring.logiware.code.soft.LWAuftragStatusCode;
import ch.eisenring.logiware.code.soft.LWGranit2Code;
import ch.eisenring.logiware.code.soft.LWTerminCD;
import ch.eisenring.lw.LWConstants;
import ch.eisenring.lw.meta.LWAuftragKopfMeta;
import ch.eisenring.lw.meta.LWAuftragMeta;
import ch.eisenring.model.TransactionContext;
import ch.eisenring.model.TransactionSource;
import ch.eisenring.network.PacketSink;
import ch.eisenring.network.packet.AbstractPacket;

class ReportKOKRequestHandler  extends AbstractDSPPacketHandler {

	ReportKOKRequestHandler(final DSPServer server) {
		super(server, PacketReportKOKRequest.class);
	}

	public void handle(final AbstractPacket abstractPacket, final PacketSink sink) {
		final PacketReportKOKRequest request = (PacketReportKOKRequest) abstractPacket;
		final PacketReportKOKReply reply = handle(server, request);
		sink.sendPacket(reply, false);
	}

	public static PacketReportKOKReply handle(final DSPServer server, final PacketReportKOKRequest request) {
		try {
			final PacketReportKOKReply reply = handleImpl(server, request);
			if (reply != null)
				return reply;
			return PacketReportKOKReply.create(request, ErrorMessage.ERROR);
		} catch (final Exception e) {
			Logger.error(e);
			return PacketReportKOKReply.create(request, new ErrorMessage(e));
		}
	}

	static class AuftragInfo {
		public LWAuftragKey auftragKey;
		public String projektNummer;
		public AbwicklungsartCode abwicklungsArt;
		public LWGranit2Code objektTyp;
		public Set<Long> koks = new HashSet<>();
	}

	private static PacketReportKOKReply handleImpl(
			final DSPServer server, final PacketReportKOKRequest request) throws Exception {
		final Map<LWAuftragKey, AuftragInfo> map = new HashMap<>();
		final TransactionSource source = new LWContextSource(LWConstants.VERTRIEB) {
			@Override
			protected void loadImpl(final TransactionContext context) throws SQLException {
				final StringMaker sql = StringMaker.obtain();
				final StatementParameters params = new StatementParameters();
				sql.append("SELECT A.");
				sql.append(TM_AUFTRAG.get(LWAuftragMeta.ATR_PROJEKTNUMMER));
				sql.append(", A.");
				sql.append(TM_AUFTRAG.get(LWAuftragMeta.ATR_AUFTRAGNUMMER));
				sql.append(", A.");
				sql.append(TM_AUFTRAG.get(LWAuftragMeta.ATR_GSE_AUFTRAG));
				sql.append(", A.");
				sql.append(TM_AUFTRAG.get(LWAuftragMeta.ATR_ABWICKLUNGSART));
				sql.append(", K.");
				sql.append(TM_AUFTRAGKOPF.get(LWAuftragKopfMeta.ATR_GRANIT2CODE));
				sql.append(", T.DatWu");
				sql.append(" FROM ");
				sql.append(TM_AUFTRAG.getTableSpecifier());
				sql.append(" A " + WITH_NOLOCK + ", ");
				sql.append(TM_AUFTRAGKOPF.getTableSpecifier());
				sql.append(" K " + WITH_NOLOCK + ", TERMINGRUPPE T " + WITH_NOLOCK);
				sql.append(" WHERE ");
				// --- table join conditions
				sql.append("A.");
				sql.append(TM_AUFTRAG.get(LWAuftragMeta.ATR_AUFTRAGNUMMER));
				sql.append(" = K.");
				sql.append(TM_AUFTRAGKOPF.get(LWAuftragKopfMeta.ATR_AUFTRAGNUMMER));
				sql.append(" AND A.");
				sql.append(TM_AUFTRAG.get(LWAuftragMeta.ATR_GSE_AUFTRAG));
				sql.append(" = K.");
				sql.append(TM_AUFTRAGKOPF.get(LWAuftragKopfMeta.ATR_GSE_AUFTRAG));
				sql.append(" AND A.");
				sql.append(TM_AUFTRAG.get(LWAuftragMeta.ATR_AUFTRAGNUMMER));
				sql.append(" = T.AuftrNr");
				sql.append(" AND A.");
				sql.append(TM_AUFTRAG.get(LWAuftragMeta.ATR_GSE_AUFTRAG));
				sql.append(" = T.Id_GSE_Auftrag");
				// --- NOT storno
				sql.append(" AND A.");
				sql.append(TM_AUFTRAG.get(LWAuftragMeta.ATR_STATUSCODE));
				sql.append(" != ?");
				params.add(LWAuftragStatusCode.STORNIERT);
				// --- AWA condition 
				{
					sql.append(" AND A.");
					sql.append(TM_AUFTRAG.get(LWAuftragMeta.ATR_ABWICKLUNGSART));
					sql.append(" IN ");
					final Set<AbwicklungsartCode> awa = request.getAbwicklungsarten();
					prepareIn(sql, awa);
					params.addAll(awa);
				}
				// --- Period (Wunschdatum)
				{
					final Period period = request.getPeriod();
					Object begin = TimestampUtil.toDate(period.getBegin(), java.sql.Timestamp.class);
					Object end = TimestampUtil.toDate(period.getEnd(), java.sql.Timestamp.class);
					if (begin != null) {
						params.add(begin);
						sql.append(" AND A.");
						sql.append(TM_AUFTRAG.get(LWAuftragMeta.ATR_WUNSCHDATUM));
						sql.append(" >= ?");
					}
					if (end != null) {
						params.add(end);
						sql.append(" AND A.");
						sql.append(TM_AUFTRAG.get(LWAuftragMeta.ATR_WUNSCHDATUM));
						sql.append(" < ?");
					}
				}
				// --- TerminCD (Gruppe)
				{
					sql.append(" AND T.TerminCD IN ");
					final Set<LWTerminCD> gruppen = request.getGruppen();
					prepareIn(sql, gruppen);
					params.addAll(gruppen);
				}
				doQuery(sql.release(), params, new RowHandler() {
					@Override
					public void handleRow(final JDBCResultSet resultSet) throws SQLException {
						final String projektNummer = resultSet.getString(1);
						final int auftragNummer = resultSet.getInt(2);
						final GSECode gse = resultSet.getCode(3, GSECode.class);
						final AbwicklungsartCode abwicklungsArt = resultSet.getCode(4, AbwicklungsartCode.class);
						final LWGranit2Code granit2 = resultSet.getCode(5, LWGranit2Code.class);
						final long kok = DateGranularityCode.DAY.round(resultSet.getLongTimestamp(6), 0);
						final LWAuftragKey key = LWAuftragKey.get(auftragNummer, gse);
						AuftragInfo info = map.get(key);
						if (info == null) {
							info = new AuftragInfo();
							info.auftragKey = key;
							info.projektNummer = projektNummer;
							info.abwicklungsArt = abwicklungsArt;
							info.objektTyp = granit2;
							map.put(key, info);
						}
						info.koks.add(kok);
					}
				});
			}
		};
		final TransactionContext context = new TransactionContext();
		context.load(source);
		
		// aufträge sortieren nach #KOKs, projektnummer
		final List<AuftragInfo> infos = new ArrayList<>(map.values());
		Collections.sort(infos, new Comparator<AuftragInfo>() {
			@Override
			public int compare(final AuftragInfo i1, final AuftragInfo i2) {
				int r = -compareSigned(i1.koks.size(), i2.koks.size()); 
				if (r != 0)
					return r;
				r = Strings.compare(i1.projektNummer, i2.projektNummer);
				if (r != 0)
					return r;
				return i1.auftragKey.compareTo(i2.auftragKey);
			}
		});

		// Report ausgeben
		final CSVFile csvFile = new CSVFile();
		
		int firstKOKCol = 0;
		{ // header line
			final CSVLine csvLine = csvFile.addLine();
			int x = -1;
			csvLine.setColumn(++x, "Projekt");
			csvLine.setColumn(++x, "Auftr-Nr.");
			csvLine.setColumn(++x, "AWA");
			csvLine.setColumn(++x, "Objekttyp");
			firstKOKCol = ++x;
		}

		for (final AuftragInfo info : infos) {
			final CSVLine csvLine = csvFile.addLine();
			int x = -1;
			csvLine.setColumn(++x, info.projektNummer);
			csvLine.setColumn(++x, info.auftragKey.getAuftragnummer());
			csvLine.setColumn(++x, AbstractCode.getKey(info.abwicklungsArt, (Object) "???"));
			csvLine.setColumn(++x, AbstractCode.getShortText(info.objektTyp, "?"));
			final List<Long> koks = new ArrayList<>(info.koks);
			Sorter.sort(koks);
			for (final long kok : koks) {
				csvLine.setColumn(++x, TimestampUtil.DATE10.format(kok));
			}
		}

		{
			final CSVLine header = csvFile.getLine(0);
			final int colCount = csvFile.getColumnCount();
			for (int col = firstKOKCol; col < colCount; ++col) {
				header.setColumn(col, Strings.concat("KOK ", col - firstKOKCol + 1));
			}
		}

		// produce XLS
		final Workbook workbook = POIUtil.createWorkbook();
		final Sheet sheet = POIUtil.createSheet(workbook);
		sheet.getPrintSetup().setLandscape(true);
		workbook.setSheetName(0, "Liste");
		POIUtil.popuplateSheet(sheet, csvFile);
		// make header gray
		POIUtil.setFillForegroundColor(sheet, IndexedColors.GREY_25_PERCENT.getIndex(), 0, 0, csvFile.getColumnCount(), 1);
		POIUtil.addBorder(sheet, IndexedColors.BLACK.getIndex(), 0, 0, csvFile.getColumnCount(), 1);
//			// basic formatting
//			sheet.getRow(y + 2).setHeightInPoints(36F);
//		}
//		int cWidth = 1567; {
//			if (reportContext.ktColumn >=0)
//				sheet.setColumnWidth(reportContext.ktColumn, 1200);
//			int column = reportContext.wkvColumn;
//			sheet.setColumnWidth(column++, cWidth);
//			sheet.setColumnWidth(column++, cWidth);
//			sheet.setColumnWidth(column++, cWidth);
//			sheet.setColumnWidth(column++, cWidth);
//		}

		final BinaryHolder filedata = POIUtil.toBinary(workbook);
		String filename = "Auswertung_KOKs";
		filename = Strings.concat(FileUtil.sanitizeFilename(filename), ".xlsx");
		final FileImage fileimage = FileImage.create(filename, filedata);
		return PacketReportKOKReply.create(request, fileimage);
	}

}

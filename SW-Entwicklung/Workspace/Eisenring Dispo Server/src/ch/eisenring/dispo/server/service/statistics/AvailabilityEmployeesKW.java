package ch.eisenring.dispo.server.service.statistics;

import static ch.eisenring.dispo.shared.codetables.EmploymentRole.MITARBEITER_MONTEUR;

import java.util.Collections;
import java.util.Comparator;
import java.util.Date;

import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.Map;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.collections.impl.HashMap;
import ch.eisenring.core.csv.CSVFile;
import ch.eisenring.core.csv.CSVLine;
import ch.eisenring.core.datatypes.date.DateGranularityCode;
import ch.eisenring.core.datatypes.date.DateUtil;
import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.core.datatypes.primitives.Primitives;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.iterators.Filter;
import ch.eisenring.dispo.server.DSPServer;
import ch.eisenring.dispo.shared.model.AbstractMitarbeiter;
import ch.eisenring.dispo.shared.model.AbstractModel;
import ch.eisenring.dispo.shared.model.MasterMitarbeiter;
import ch.eisenring.dispo.shared.model.ModelContext;
import ch.eisenring.logiware.code.soft.GSECode;
import ch.eisenring.presento.server.api.PresentoCache;
import ch.eisenring.presento.server.api.PresentoPerson;
import ch.eisenring.statistics.shared.codetables.STDateGranularityCode;
import ch.eisenring.statistics.shared.codetables.StatisticTypeCode;
import ch.eisenring.statistics.shared.data.DataPoint;
import ch.eisenring.statistics.shared.network.PacketStatisticRequest;
import ch.eisenring.statistics.shared.network.PacketStatisticResponse;

public class AvailabilityEmployeesKW extends StatisticsBase {

	private final static int FIRST_KW_COLUMN = 7;

	private static Double AVAILABILITY_PER_DAY = Primitives.valueOf(20D);
	
	private Date kwFrom;
	private Date kwUpto;
	private CSVFile csvFile;
	
	public AvailabilityEmployeesKW(final DSPServer server) {
		super(server);
	}

	@Override
	public PacketStatisticResponse createResponse(final PacketStatisticRequest packet) {
		this.kwFrom = getKWStartDatumNotNull(packet.getDateFrom());
		this.kwUpto = getKWEndDatumNotNull(packet.getDateUpto());
		this.csvFile = new CSVFile();
		
		final List<DataPoint> dataList = new ArrayList<DataPoint>();
		calculateAvailability(packet, dataList);
		
		final PacketStatisticResponse result = PacketStatisticResponse.create(packet);
		smartSort(dataList);
		
		// --- chart title
		String title = STDateGranularityCode.getDateRangeText(result);
		title = Strings.concat(packet.getType(), "\n(", title, ")");
		result.setChartTitle(title);

		// --- values
		double[] data = getValues(dataList);
		DataPoint.round(data);
		result.setDataSeries(new double[][] { data });

		// --- labels
		String[] labels = getLabels(dataList);
		result.setLabels(labels);
		result.setXAxisLabel("Wert in %");
		result.setYAxisLabel("Kalenderwoche");
		
		// --- file attachment
		result.addAttachment("Auswertung_Verfuegbarkeit_Monteure.csv", csvFile.toBinary(null));

		return result;
	}

	/**
	 * Convert DataMiningType -> GSEIdType
	 */
	public static GSECode toGSE(final StatisticTypeCode dataMiningType) {
		return (GSECode) dataMiningType.getSubType1();
	}

	@SuppressWarnings("unchecked")
	private void calculateAvailability(final PacketStatisticRequest packet, final List<DataPoint> targetList) {
		final Date fromKW = DateUtil.findMonday(kwFrom, 0);
		final Date uptoKW = DateUtil.findMonday(kwUpto, 1);
		final ModelContext global = server.getGlobalContext();
		final PresentoCache cache = server.getPresentoCache();
		final GSECode gse = toGSE(packet.getType());
		final Map<MasterMitarbeiter, CSVLine> lineMap = new HashMap<>();
		
		global.obtain();
		try {
			final List<MasterMitarbeiter> employeeList = (List) global.getModelList(MasterMitarbeiter.class, new Filter<AbstractModel>() {
				@Override
				public boolean accepts(final AbstractModel model) {
					if (model instanceof MasterMitarbeiter) {
						final MasterMitarbeiter master = (MasterMitarbeiter) model;
						return gse.equals(master.getGSE()) &&
							   master.getEmploymentType().isCountedAsEmployee() &&
							   MITARBEITER_MONTEUR.equals(master.getEmploymentRole());
					}
					return false;
				}
			});
			Collections.sort(employeeList, AbstractMitarbeiter.Order.StandardDisplayOrder);

			// --- Auszählen aller betroffenen KW's
			Date kw = fromKW;
			int kwColumn = FIRST_KW_COLUMN;
			while (kw.before(uptoKW)) {
				final String kwString = TimestampUtil.KWDATE7.format(kw);
				final HashMap<MasterMitarbeiter, Double> availMap = new HashMap<>();
	
				// --- Jeden einzelnen Wochentag abchecken...
				for (int weekDay=0; weekDay<5; ++weekDay) {
					final long day = DateGranularityCode.DAY.round(TimestampUtil.toTimestamp(kw), weekDay);
					
					// --- Verfügbarkeit aller Monteure
					for (int i=employeeList.size()-1; i>=0; --i) {
						final MasterMitarbeiter master = employeeList.get(i);
						// --- get Person from presento
						final PresentoPerson person = cache.getPerson(master.getPresentoId());
						boolean working = false;
						if (person != null) {
							// --- must be internal employee
							working = person.isWorking(day);
						} else {
							working = master.getEmployment().isWorking(day);
						}
						if (working) {
							Double value = availMap.get(master);
							if (value == null) {
								availMap.put(master, AVAILABILITY_PER_DAY);
							} else {
								availMap.put(master, AVAILABILITY_PER_DAY + value);
							}
						}
					}
				}

				// --- output KW data
				final List<MasterMitarbeiter> masterList = new ArrayList<MasterMitarbeiter>(availMap.keySet());
				Collections.sort(masterList, AbstractMitarbeiter.Order.StandardDisplayOrder);
				final CSVLine csvHeaderLine = csvFile.getLine(0, true);
				csvHeaderLine.setColumn(0, "Name");
				csvHeaderLine.setColumn(1, "Vorname");
				csvHeaderLine.setColumn(2, "Art");
				csvHeaderLine.setColumn(3, "KurzZ.");
				csvHeaderLine.setColumn(4, "Kurzbem.");
				csvHeaderLine.setColumn(5, "GSE");
				csvHeaderLine.setColumn(6, "Sterne");
				csvHeaderLine.setColumn(kwColumn, "KW"+kwString);
				for (int rowIndex=0; rowIndex<masterList.size(); ++rowIndex) {
					final MasterMitarbeiter employee = masterList.get(rowIndex);
					final Double value = availMap.get(employee);
					CSVLine csvLine = lineMap.get(employee);
					if (csvLine == null) {
						csvLine = csvFile.addLine();
						lineMap.put(employee, csvLine);
					}
					csvLine.setColumn(0, employee.getLastName());
					csvLine.setColumn(1, employee.getFirstName());
					csvLine.setColumn(2, employee.getEmploymentType());
					csvLine.setColumn(3, employee.getShortName());
					csvLine.setColumn(4, employee.getShortComment());
					csvLine.setColumn(5, employee.getGSE());
					csvLine.setColumn(6, AbstractCode.getShortText(employee.getSterne(), ""));
					csvLine.setColumn(kwColumn, value.intValue());
					
					final String label = kwString + "\n" + employee.getName();
					final DataPoint p = new DataPoint(label, value);
					targetList.add(p);
				}
				
				// --- nächste kw
				kw = DateUtil.findMonday(kw, 1);
				++kwColumn;
			}
			
			// --- properly order the CSV file
			final CSVLine csvHeader = csvFile.getLine(0);
			final Comparator<CSVLine> comparator = new Comparator<CSVLine>() {
				@Override
				public int compare(final CSVLine l1, final CSVLine l2) {
					if (l1 == csvHeader) {
						return l2 == csvHeader ? 0 : -1;
					} else if (l2 == csvHeader) {
						return l1 == csvHeader ? 0 : 1;
					}
					int result = Strings.compare(l1.getColumn(0), l2.getColumn(0));
					if (result == 0) {
						result = Strings.compare(l1.getColumn(1), l2.getColumn(1));
						if (result == 0) {
							result = Strings.compare(l1.getColumn(2), l2.getColumn(2));
						}
					}
					return result;
				}
			};
			csvFile.sort(comparator);
			csvFile.replaceValues(null, "0");
			final int lineCount = csvFile.getLineCount();
			final CSVLine sumLine = csvFile.addLine();
			for (int i=csvFile.getColumnCount()-1; i>=FIRST_KW_COLUMN; --i) {
				final String excelColumn = CSVFile.getExcelColumn(i);
				sumLine.setColumn(i, "=SUMME("+excelColumn+"2:"+excelColumn+Strings.toString(lineCount)+")");
			}
		} finally {
			global.release();
		}
	}

}

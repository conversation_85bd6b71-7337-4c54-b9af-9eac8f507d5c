package ch.eisenring.dispo.server.logiware;

import java.io.IOException;
import java.sql.SQLException;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.Map;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.collections.impl.HashMap;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.dispo.shared.model.Auftrag;
import ch.eisenring.dispo.shared.model.AuftragPosition;
import ch.eisenring.jdbc.StatementParameters;
import ch.eisenring.logiware.LWAuftragKey;
import ch.eisenring.logiware.LWContextSource;
import ch.eisenring.logiware.code.pseudo.PositionStatusCode;
import ch.eisenring.lw.LWConstants;
import ch.eisenring.model.TransactionContext;
import ch.eisenring.model.TransactionSource;
import ch.eisenring.model.engine.jdbc.JDBCObjectMapper;
import ch.eisenring.model.engine.jdbc.SingleTableMapping;
import ch.eisenring.model.engine.jdbc.TableMapping;
import ch.eisenring.model.mapping.ObjectMapping;
import ch.eisenring.model.mapping.POJOMapping;

/**
 * Import handling for AusstandPosition(en)
 */
final class PositionImport {

	// --------------------------------------------------------------
	// ---
	// --- Mapping
	// ---
	// --------------------------------------------------------------
	private final static SingleTableMapping TM_AUSSTANDPOSITION = TableMapping.get(LWAusstandPositionMeta.METACLASS, LWAusstandPositionMeta.TABLE);
	private final static ObjectMapping<LWAusstandPosition> PM_AUSSTANDPOSITION = POJOMapping.get(LWAusstandPositionMeta.METACLASS, LWAusstandPosition.class);
	private final static JDBCObjectMapper<LWAusstandPosition> OM_AUSSTANDPOSITION = JDBCObjectMapper.get(TM_AUSSTANDPOSITION, PM_AUSSTANDPOSITION);

	private PositionImport() {
	}

	public static void updatePositions(final java.util.Collection<Auftrag> auftraege) throws Exception {
		final Map<Auftrag, List<LWAusstandPosition>> posData = PositionImport.load(auftraege);
		for (final Auftrag auftrag : auftraege) {
			final List<LWAusstandPosition> positionen = posData.get(auftrag);
			if (positionen == null) {
				PositionImport.updatePositions(auftrag, List.emptyReadOnly(LWAusstandPosition.class));
			} else {
				PositionImport.updatePositions(auftrag, positionen);
			}
		}
	}

	/**
	 * Loads / updates the position of the given Auftrag
	 */
	private static Map<Auftrag, List<LWAusstandPosition>> load(final java.util.Collection<Auftrag> auftraege) throws IOException {
		final Map<Auftrag, List<LWAusstandPosition>> result = new HashMap<>();
		if (auftraege == null || auftraege.isEmpty())
			return result;

		// construct lookup map key->auftrag
		final Map<LWAuftragKey, Auftrag> auftragMap = new HashMap<>();
		for (final Auftrag auftrag : auftraege)
			auftragMap.put(auftrag.getAuftragKey(), auftrag);

		// list of keys to be loaded
		final List<LWAuftragKey> keyList = new ArrayList<>();
		keyList.addAll(auftragMap.keySet());
		if (keyList.isEmpty())
			return result;
		keyList.sort(LWAuftragKey.Order.AuftragNummer);

		final TransactionSource source = new LWContextSource(LWConstants.VERTRIEB) {
			@Override
			protected void loadImpl(final TransactionContext context) throws SQLException {
				// load in blocks
				for (final List<LWAuftragKey> chunk : chunkInParameters(keyList)) {

					final StringMaker sql = StringMaker.obtain(512);
					final StatementParameters params = new StatementParameters();
					TM_AUSSTANDPOSITION.appendSelectSQL(sql, this, TableMapping.NO_TABLE_ALIAS);
					sql.append(WITH_NOLOCK);
					sql.append(" WHERE ");

					// optimize query scope by adding additional constraint(s) to auftragnummer
					if (chunk.size() > 1) {
						final int auftrNrMin = chunk.get(0).getAuftragnummer();
						final int auftrNrMax = chunk.get(chunk.size() - 1).getAuftragnummer();
						sql.append(TM_AUSSTANDPOSITION.getColumn(LWAusstandPositionMeta.ATR_AUFTRAGNUMMER));
						sql.append(" >= ? AND ");
						sql.append(TM_AUSSTANDPOSITION.getColumn(LWAusstandPositionMeta.ATR_AUFTRAGNUMMER));
						sql.append(" <= ? AND ");
						params.add(auftrNrMin);
						params.add(auftrNrMax);
					}

					sql.append(TM_AUSSTANDPOSITION.getColumn(LWAusstandPositionMeta.ATR_AUFTRAGNUMMER));
					sql.append(" IN ");
					prepareIn(sql, chunk);
					for (final LWAuftragKey key : chunk)
						params.add(key.getAuftragnummer());
					doQuery(sql.release(), params, (resultSet) -> {
						final LWAusstandPosition position = OM_AUSSTANDPOSITION.loadRow(context, resultSet);
						final LWAuftragKey auftragKey = position.getAuftragKey();
						final Auftrag auftrag = auftragMap.get(auftragKey);
						if (auftrag == null)
							return;
						// update/extend result map
						List<LWAusstandPosition> list = result.get(auftrag);
						if (list == null) {
							list = new ArrayList<>();
							result.put(auftrag, list);
						}
						list.add(position);
					});
				}
			}
		};
		final TransactionContext context = new TransactionContext();
		source.load(context);
		return result;
	}

	private static void updatePositions(final Auftrag auftrag, final List<LWAusstandPosition> ausstandPositionen) {
		final List<AuftragPosition> lMPos = auftrag.getPositionRelation().list();
		for (int i = lMPos.size() - 1; i >= 0; --i) {
			final AuftragPosition mp = lMPos.get(i);
			boolean found = false;
			for (final LWAusstandPosition ausstand : ausstandPositionen) {
				if (ausstand.getPositionNummer() == mp.getPositionsNr()) {
					found = true;
					break;
				}
			}
			if (!found) {
				auftrag.removePosition(mp);
				mp.delete();
			}
		}

		for (final LWAusstandPosition ausstand : ausstandPositionen) {
			AuftragPosition position = getPosition(lMPos, ausstand.getPositionNummer());
			if (!PositionStatusCode.POSITION_STATUS_AUSSTAND.equals(ausstand.getPositionStatus())) {
				// alles was nicht ausstand ist ignorieren
				if (position != null) {
					// wenn position von AUS auf ??? wechselt, entfernen!
					auftrag.removePosition(position);
					position.delete();
				}
				continue;
			}
			if (null == position) {
				position = new AuftragPosition(auftrag.getContext());
			}
			auftrag.addPosition(position);
			position.setPositionsNr(ausstand.getPositionNummer());
			position.setProduktNummer(ausstand.getProduktNummer());
			position.setBezeichnung(ausstand.getProduktBezeichnung());
			position.setPositionStatus(ausstand.getPositionStatus());
			position.setBaustellenLieferung(ausstand.isBaustellenLieferung());
			position.setBelegnummerBestellung(ausstand.getBelegNummerBestellung());
		}
	}

	private static AuftragPosition getPosition(final List<AuftragPosition> list, final double positionNummer) {
		for (final AuftragPosition position : list) {
			if (position.getPositionsNr() == positionNummer)
				return position;
		}
		return null;
	}

}

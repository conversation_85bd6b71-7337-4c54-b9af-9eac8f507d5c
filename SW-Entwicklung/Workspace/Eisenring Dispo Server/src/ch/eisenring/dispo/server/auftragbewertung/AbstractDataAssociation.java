package ch.eisenring.dispo.server.auftragbewertung;

import java.util.Date;
import java.util.Iterator;

import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.csv.CSVFile;
import ch.eisenring.core.csv.CSVLine;
import ch.eisenring.core.csv.CSVReader;
import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.core.logging.Logger.LogLevel;

/**
 * Base class for handling associative data tables
 * (stored in CSV files)
 */
public abstract class AbstractDataAssociation<K, V extends AbstractDataAssociationEntry<K>> {

	protected final String csvFileName;
	protected final char columnSeparator;

	protected final Date timestamp = new Date();
	protected final ArrayList<V> entryList = new ArrayList<V>(32);
	protected final boolean loadOk;
	
	protected AbstractDataAssociation(final String csvFileName) {
		this(csvFileName, ';', true);
	}

	protected AbstractDataAssociation(final String csvFileName, final char columnSeparator, final boolean skipFirstLine) {
		this.csvFileName = csvFileName;
		this.columnSeparator = columnSeparator;
		this.loadOk = loadCSV(csvFileName, columnSeparator, skipFirstLine);
	}

	private boolean loadCSV(final String csvFileName, final char columnSeparator, final boolean skipFirstLine) {
		boolean result = true;
		final CSVFile csvFile = CSVReader.readCSV(csvFileName, columnSeparator);
		if (csvFile == null) {
			Logger.log(LogLevel.ERROR, "Can't load CSV file: \""+csvFileName+"\"");
			result = false;
		} else {
			Logger.log(LogLevel.INFO, "Loaded CSV File: \""+csvFileName+"\" ("+csvFile.getLineCount()+" rows, "+csvFile.getColumnCount()+" columns)");
			final Iterator<CSVLine> lineItr = csvFile.getLineIterator();
			int lineNumber = 0;
			if (skipFirstLine && lineItr.hasNext()) {
				++lineNumber;
				lineItr.next();
			}
			while (lineItr.hasNext()) {
				final CSVLine csvLine = lineItr.next();
				try {
					final V entry = parseLine(csvLine, lineNumber);
					if (entry != null) {
						entryList.add(entry);
					}
				} catch (final Exception e) {
					Logger.error(Strings.concat("Exception parsing CSV line #", lineNumber, ": ", e.getMessage()));
					Logger.error(e);
					result = false;
				}
				++lineNumber;
			}
			Logger.info(Strings.concat("Parsed ", lineNumber, " lines from \"", csvFileName, "\""));
		}
		return result;
	}

	/**
	 * Gets the value object for key
	 */
	public final <U> V getEntry(final U auftrag, final AuftragBewertungAccessor<U> accessor) {
		final int count = entryList.size();
		V bestEntry = null;
		for (int i=count-1; i>=0; --i) {
			final V entry = entryList.get(i);
			if (entry.matches(auftrag, accessor)) {
				final int score = entry.getScore();
				if (bestEntry == null || score > bestEntry.getScore()) {
					bestEntry = entry;
				} 
			}
		}
		return bestEntry == null ? getDefaultEntry() : bestEntry;
	}

	/**
	 * Parses a line from CSV into value object 
	 */
	protected abstract V parseLine(final CSVLine csvLine, final int lineNumber);

	/**
	 * Gets the default entry (used when no entry matches)
	 */
	public abstract V getDefaultEntry();
	
	/**
	 * Returns true, if any errors occurred building this association
	 */
	public final boolean isFaulty() {
		return !loadOk;
	}

	/****************************************************************
	 * Object overrides
	 ****************************************************************/
	@Override
	public int hashCode() {
		return Strings.hashCode(csvFileName);
	}
	
	@Override
	public boolean equals(final Object o) {
		if (o instanceof AbstractDataAssociation) {
			if (getClass().equals(o.getClass())) {
				final AbstractDataAssociation<?, ?> a = (AbstractDataAssociation<?, ?>) o;
				return Strings.equals(csvFileName, a.csvFileName);
			}
		}
		return false;
	}

	@Override
	public String toString() {
		final StringMaker b = StringMaker.obtain(128);
		b.append(csvFileName);
		b.append("[Loaded=");
		b.append(timestamp, TimestampUtil.DATETIME);
		b.append("]");
		return b.release();
	}
	
}

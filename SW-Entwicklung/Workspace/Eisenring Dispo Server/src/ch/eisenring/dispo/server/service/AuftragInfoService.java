package ch.eisenring.dispo.server.service;

import static ch.eisenring.dispo.shared.codetables.ZuordnungCode.ZUORDNUNG_GROBZUORDNUNG;

import java.sql.Connection;
import java.sql.SQLException;

import org.hibernate.Session;
import org.hibernate.Transaction;

import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.datatypes.date.DateGranularityCode;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.core.logging.Logger.LogLevel;
import ch.eisenring.dispo.server.DSPServer;
import ch.eisenring.dispo.server.hibernate.HibernateSessionFactory;
import ch.eisenring.dispo.shared.DSPConstants;
import ch.eisenring.dispo.shared.codetables.ZuordnungCode;
import ch.eisenring.dispo.shared.network.packets.PacketInfoReply;
import ch.eisenring.dispo.shared.network.packets.PacketInfoRequest;
import ch.eisenring.dispo.shared.service.ZuordnungInfoEntry;
import ch.eisenring.jdbc.ConnectionConfigurator;
import ch.eisenring.jdbc.JDBCBase;
import ch.eisenring.jdbc.JDBCConnection;
import ch.eisenring.jdbc.JDBCResultSet;
import ch.eisenring.jdbc.RowHandler;
import ch.eisenring.network.PacketSink;
import org.hibernate.jdbc.Work;

public final class AuftragInfoService extends ServiceBase {

	public AuftragInfoService(final DSPServer server, final PacketInfoRequest packet, final PacketSink sink) {
		super(server, packet, sink);
	}
	
	static class AuftragInfoHelper extends JDBCBase {
		protected Session session;
		protected Transaction transaction;

		public AuftragInfoHelper(final DSPServer server) {
			super(DSPConstants.DSP_DATABASE.getConnectionInfo());
		}
		
		@Override
		protected void configure(final Connection connection) throws SQLException {
			ConnectionConfigurator.READONLY.configure(connection);
		}
	
		@SuppressWarnings("deprecation")
		public boolean connectToDispo() {
			close();
			session = HibernateSessionFactory.openSession();
			transaction = session.beginTransaction();
			try {
				session.doWork(new Work() {
					@Override
					public void execute(Connection connection) throws SQLException {
						final JDBCConnection jdbcCon = new JDBCConnection(connection,
					connectionInfo.getDatabase(), connectionInfo.getSchema());
						open(jdbcCon);
					}
				});
			} catch (final Exception e) {
				Logger.error(e);
				return false;
			}
			return true;
		}

		@Override
		protected void hookCloseConnection(Connection connection) {
			super.hookCloseConnection(connection);
			if (session != null) {
				try {
					session.close();
				} catch (Throwable t) {
					Logger.warn(t);
				} finally {
					session = null;
				}
			}
		}

		public List<ZuordnungInfoEntry> doInfoQuery(String sql) {
			final List<ZuordnungInfoEntry> result = new ArrayList<ZuordnungInfoEntry>();
			final boolean isReady = connectToDispo();
			if (isReady) {
				try {
					doQuery(sql, new RowHandler() {
						@Override
						public void handleRow(final JDBCResultSet resultSet) throws SQLException {
							final ZuordnungInfoEntry entry = new ZuordnungInfoEntry();
							entry.setBemerkungen(resultSet.getString("bemerkungen"));
							entry.setLabel(resultSet.getString("label"));
							entry.setBegin(resultSet.getLongTimestamp("dateFrom"));
							long end = resultSet.getLongTimestamp("dateUpTo");
							end = DateGranularityCode.SECOND.round(end, 1);
							entry.setEnd(end);
							entry.setOID(resultSet.getLong("rowId"));
							entry.setPresentoId(resultSet.getLong("PID"));
							entry.setZuordnungType(resultSet.getCode("ZType", ZuordnungCode.class));
							result.add(entry);
						}
					});
				} catch (SQLException e) {	 
					Logger.log(LogLevel.ERROR, e);
				} finally {
					close();
				}
			}
			return result;
		}		

	};
	
	@Override
	protected void performServiceImpl() {
		final PacketInfoRequest request = (PacketInfoRequest) getPacket();

		final String sql = getZuordnungQueryForAuftrag(request.getAuftragNr());
		List<ZuordnungInfoEntry> result = null;
		
		result = new AuftragInfoHelper(server).doInfoQuery(sql);
		if (Logger.isDebugEnabled()) {
			final int count = null==result ? 0 : result.size();
			Logger.debug(Strings.concat("AuftragInfoService returning ", count, " records"));
		}

		// send response
		final PacketInfoReply response = PacketInfoReply.create(request);
		response.addSearchResults(result);
		
		// notify client (if there was any)
		PacketSink connection = getConnection();
		if (connection != null) {
			connection.sendPacket(response, false);
		}
	}

	public static String getZuordnungQueryForAuftrag(final int auftragNummer) {
		final String aCols = " Z.rowId, Z.dateFrom, Z.dateUpTo, Z.label, Z.bemerkungen ";
		final String gCols = " "+ZUORDNUNG_GROBZUORDNUNG.getId()+" AS ZType, NULL AS PID, "+aCols;
		final String fCols = " Z.type AS ZType, Z.mitarbeiter_PID AS PID, "+aCols;
		final String sql = 
		                 "SELECT "+gCols+" FROM GrobZuordnung Z WHERE (auftrag_rowId IS NULL) AND (auftrag_Nummer=186234)\n"+
		      " UNION ALL SELECT "+gCols+" FROM GrobZuordnung Z, Auftrag A WHERE (A.rowId=Z.auftrag_rowId) AND (A.auftragNummer=186234)\n"+
		      " UNION ALL SELECT "+fCols+" FROM FeinZuordnung Z WHERE (auftrag_rowId IS NULL) AND (auftrag_Nummer=186234)\n"+
		      " UNION ALL SELECT "+fCols+" FROM FeinZuordnung Z, Auftrag A WHERE (A.rowId=Z.auftrag_rowId) AND (A.auftragNummer=186234)\n"+
		      " ORDER BY dateFrom DESC, dateUpTo;";
		return sql.replace("186234", Strings.toString(auftragNummer));
	}

}

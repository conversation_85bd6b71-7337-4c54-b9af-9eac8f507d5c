package ch.eisenring.dispo.server.service.statistics;

import java.util.Date;

import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.datatypes.date.DateUtil;
import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.core.iterators.Filter;
import ch.eisenring.dispo.server.DSPServer;
import ch.eisenring.dispo.shared.model.AbstractModel;
import ch.eisenring.dispo.shared.model.ModelContext;
import ch.eisenring.statistics.shared.network.PacketStatisticRequest;

/**
 * Base class for simple "Auftrag" from KW to KW statistics 
 */
public abstract class KWStatisticBase extends StatisticsBase {

	
	
	protected KWStatisticBase(final DSPServer server) {
		super(server);
	}

	// --------------------------------------------------------------
	// ---
	// --- Helper Methods
	// ---
	// --------------------------------------------------------------
//	private Date dateFrom;
//	private Date dateUpto;
	private List<ModelContext> contextList = new ArrayList<ModelContext>(32);

	/**
	 * Initializes all necessary data fields
	 */
	protected void init(final PacketStatisticRequest requestt) {
		final Date kwFrom = getKWStartDatumNotNull(request.getDateFrom());
		final Date kwUpto = getKWEndDatumNotNull(request.getDateUpto());
		Date itrKW = DateUtil.findMonday(kwFrom, 0);
		Date endKW = DateUtil.findMonday(kwUpto, 0);
		while (!itrKW.after(endKW)) {
			// get the context for KW
			final ModelContext context = server.getContextCache().getOrLoad(TimestampUtil.toTimestamp(itrKW), true);
			contextList.add(context);
			itrKW = DateUtil.findMonday(itrKW, 1);
		}
	}

	/**
	 * Fetches all Model objects that match filter in request
	 */
	public List<AbstractModel> getModelList(final Filter<AbstractModel> filter) {
		final List<AbstractModel> result = new ArrayList<AbstractModel>(256); 
		for (final ModelContext context : contextList) {
			final List<AbstractModel> modelList = context.getModelList(filter);
			result.addAll(modelList);
		}
		return result;
	}

}
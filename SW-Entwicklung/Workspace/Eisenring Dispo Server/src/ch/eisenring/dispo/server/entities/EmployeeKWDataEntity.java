package ch.eisenring.dispo.server.entities;

import java.util.Date;

import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.dispo.shared.model.AbstractModel;
import ch.eisenring.dispo.shared.model.EmployeeKWData;

public class EmployeeKWDataEntity extends AbstractEntity {

	private long presentoId;
	private Date kwDate;
	private int flagId;
	
	@Override
	public Class<? extends AbstractModel> getModelClass() {
		return EmployeeKWData.class;
	}

	@Override
	public String getObjectDescription() {
		final StringMaker b = StringMaker.obtain(200);
		b.append("EmployeeKWData(");
		b.append("presentoId=");
		b.append(presentoId);
		b.append(";kwDate=");
		b.append(kwDate, TimestampUtil.DATE10);
		b.append(";flagId=");
		b.append(flagId);
		b.append(')');
		return b.release();
	}

	@Override
	public void updateModel(final AbstractModel model) {
		super.updateModel(model);
		final EmployeeKWData kwData = (EmployeeKWData) model;
		kwData.setPresentoId(getPresentoId());
		kwData.setKWDate(TimestampUtil.toTimestamp(getKwDate()));
		kwData.setFlagId(getFlagId());
	}

	@Override
	public void updateEntity(final AbstractModel model) {
		super.updateEntity(model);
		final EmployeeKWData kwData = (EmployeeKWData) model;
		updateDirty(kwData.getPresentoId(), getPresentoId());
		setPresentoId(kwData.getPresentoId());
		
		final Date date = TimestampUtil.toDate(kwData.getKWDate());
		updateDirty(date, getKwDate());
		setKwDate(date);

		updateDirty(kwData.getFlagId(), getFlagId());
		setFlagId(kwData.getFlagId());
	}

	/***************************************************************
	 * Getters / Setters
	 ***************************************************************/
	public long getPresentoId() {
		return presentoId;
	}

	public void setPresentoId(long presentoId) {
		this.presentoId = presentoId;
	}

	public Date getKwDate() {
		return kwDate;
	}

	public void setKwDate(Date kwDate) {
		this.kwDate = kwDate;
	}

	public int getFlagId() {
		return flagId;
	}

	public void setFlagId(int flagId) {
		this.flagId = flagId;
	}

}

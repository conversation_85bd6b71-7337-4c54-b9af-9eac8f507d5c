package ch.eisenring.dispo.server.service.statistics;

import static ch.eisenring.statistics.shared.data.DataPoint.SEPARATOR;

import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.Iterator;

import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.Map;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.collections.impl.HashMap;
import ch.eisenring.core.csv.CSVFile;
import ch.eisenring.core.csv.CSVLine;
import ch.eisenring.core.datatypes.date.DateUtil;
import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.iterators.ArrayIterator;
import ch.eisenring.core.iterators.Filter;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.dispo.server.DSPServer;
import ch.eisenring.dispo.server.model.ServerModelContext;
import ch.eisenring.dispo.shared.codetables.AbrechnungCode;
import ch.eisenring.dispo.shared.codetables.EmploymentType;
import ch.eisenring.dispo.shared.codetables.ZuordnungCode;
import ch.eisenring.dispo.shared.model.AbstractMitarbeiter;
import ch.eisenring.dispo.shared.model.AbstractModel;
import ch.eisenring.dispo.shared.model.AbstractZuordnung;
import ch.eisenring.dispo.shared.model.Auftrag;
import ch.eisenring.dispo.shared.model.FeinZuordnung;
import ch.eisenring.dispo.shared.model.ModelContext;
import ch.eisenring.dispo.shared.model.VirtualMitarbeiter;
import ch.eisenring.dispo.shared.modelapi.Zuordnung;
import ch.eisenring.logiware.code.pseudo.AbwicklungsartCode;
import ch.eisenring.logiware.code.soft.GSECode;
import ch.eisenring.logiware.code.soft.LWGranit2Code;
import ch.eisenring.statistics.shared.codetables.STDateGranularityCode;
import ch.eisenring.statistics.shared.data.DataPoint;
import ch.eisenring.statistics.shared.network.PacketStatisticRequest;
import ch.eisenring.statistics.shared.network.PacketStatisticResponse;

public class LeistungMonteur extends StatisticsBase {

	private EmploymentType empType;
	private GSECode gse;
	private Date kwFrom;
	private Date kwUpto;
	
	public LeistungMonteur(final DSPServer server) {
		super(server);
	}

	@Override
	public PacketStatisticResponse createResponse(final PacketStatisticRequest packet) {
		this.kwFrom = getKWStartDatumNotNull(packet.getDateFrom());
		this.kwUpto = getKWEndDatumNotNull(packet.getDateUpto());
		this.gse = (GSECode) packet.getType().getSubType1();
		this.empType = (EmploymentType) packet.getType().getSubType2();
		Logger.trace("Abfrage-Zeitraum:  " + TimestampUtil.SQLDATETIME.format(kwFrom) +
				" " + TimestampUtil.SQLDATETIME.format(kwUpto));			

		final List<DataPoint> dataList = new ArrayList<DataPoint>();
		
		PacketStatisticResponse result = null;

		// Iteriere über alle KW im Abfragezeitraum
		Date itrKW = DateUtil.findMonday(kwFrom, 0);
		Date endKW = DateUtil.findMonday(kwUpto, 0);
		while (!itrKW.after(endKW)) {
			// get the context for KW
			final ModelContext context = server.getContextCache().getOrLoad(TimestampUtil.toTimestamp(itrKW), true);
			processKW(context);
			itrKW = DateUtil.findMonday(itrKW, 1);
		}

		// build CSV file with all Auftrag
		final CSVFile csvFile = new CSVFile();
		CSVLine csvLine = csvFile.addLine();
		csvLine.setColumn(0, "1. Monteur Name");
		csvLine.setColumn(1, "1. Monteur Vorname");
		csvLine.setColumn(2, "2. Monteur Name");
		csvLine.setColumn(3, "2. Monteur Vorname");
		csvLine.setColumn(4, "Kom-Nr.");
		csvLine.setColumn(5, "Auftr-Nr.");
		csvLine.setColumn(6, "AbwArt");
		csvLine.setColumn(7, "Obj.-Art");
		csvLine.setColumn(8, "Anzahl Küchen");
		csvLine.setColumn(9, "Verr.");
		csvLine.setColumn(10, "Bezeichnung");
		csvLine.setColumn(11, "Basis-Kom-Nr.");
		csvLine.setColumn(12, "Wunschdatum");
		csvLine.setColumn(13, "Küchen-Ende");
		csvLine.setColumn(14, "Ausführung");
		csvLine.setColumn(15, "Ausf.-Front");
		final List<AuftragEntry> auftraege = getAuftraege();
		Collections.sort(auftraege);
		for (final AuftragEntry auftrag : auftraege) {
			// only show AbwArt 134 + 135
			final AbwicklungsartCode abwArt = auftrag.abwicklungsArt;
			if (!(AbwicklungsartCode.AWA134.equals(abwArt) ||
				  AbwicklungsartCode.AWA135.equals(abwArt)))
				  continue;
			final Iterator<MonteurHelper> monteurItr = auftrag.getMonteure();
			final MonteurHelper monteur1 = monteurItr.hasNext() ? monteurItr.next() : null;
			final MonteurHelper monteur2 = monteurItr.hasNext() ? monteurItr.next() : null;
			// restrict to Auftrag where Monteur is of selected type
			boolean matchEmpType = (monteur1 != null && monteur1.empType.equals(this.empType)) ||
								   (monteur2 != null && monteur2.empType.equals(this.empType));
			if (!matchEmpType)
				continue;
			// create next line in CSV-file and populate with data
			csvLine = csvFile.addLine();
			if (monteur1 != null) {
				csvLine.setColumn(0, monteur1.lastName);
				csvLine.setColumn(1, monteur1.firstName);
			}
			if (monteur2 != null) {
				csvLine.setColumn(2, monteur2.lastName);
				csvLine.setColumn(3, monteur2.firstName);
			}
			csvLine.setColumn(4, auftrag.projektNummer);
			csvLine.setColumn(5, auftrag.auftragNummer);
			csvLine.setColumn(6, auftrag.abwicklungsArt.getId());
			csvLine.setColumn(7, AbstractCode.getLongText(auftrag.granit2Code, null));
			csvLine.setColumn(8, auftrag.objektkuechenAnzahl);
			csvLine.setColumn(9, AbrechnungCode.YES.equals(auftrag.abrechnungCode) ? "Ja" : "Nein");
			csvLine.setColumn(10, auftrag.bezeichnung);
			csvLine.setColumn(11, auftrag.basisNummer);
			csvLine.setColumn(12, auftrag.wunschDatum);
			csvLine.setColumn(13, auftrag.kuechenEnde);
			csvLine.setColumn(14, auftrag.ausfuehrung);
			csvLine.setColumn(15, auftrag.ausfuehrungFront);
		}
		
		final List<MonteurHelper> monteure = getMonteure();
		for (final MonteurHelper monteur : monteure) {
			if (!monteur.empType.equals(this.empType))
				continue;
			if (monteur.isAllZero())
				continue;
			final String prefix = "0" + SEPARATOR + monteur.name + SEPARATOR;
			dataList.add(new DataPoint(prefix + "115", monteur.getScore(AbwicklungsartCode.AWA115)));
			dataList.add(new DataPoint(prefix + "134", monteur.getScore(AbwicklungsartCode.AWA134)));
			dataList.add(new DataPoint(prefix + "135", monteur.getScore(AbwicklungsartCode.AWA135)));
			dataList.add(new DataPoint(prefix + "Begleitung", monteur.scoreBegleit));
			dataList.add(new DataPoint(prefix + "Fahrauftrag", monteur.scoreFahr));
/*
	public final static AbwicklungsartCode ABWART112	= new AbwicklungsartCode( 112, "112", "112", "Servicecenter");			// "Auftrag Service"
	public final static AbwicklungsartCode ABWART113	= new AbwicklungsartCode( 113, "113", "113", "Kleinauftrag Service");	// "Auftrag Service KL"
	public final static AbwicklungsartCode ABWART115	= new AbwicklungsartCode( 115, "115", "115", "Endmontage");				// "Folgeauftrag"
	public final static AbwicklungsartCode ABWART130	= new AbwicklungsartCode( 130, "130", "130", "Terminauftrag mit KOK");
	public final static AbwicklungsartCode ABWART134	= new AbwicklungsartCode( 134, "134", "134", "Kleinauftrag");			// "Terminauftrag zu Winner"
	public final static AbwicklungsartCode ABWART135	= new AbwicklungsartCode( 135, "135", "135", "Küchenmontage");			// "Winner Auftrag"
 */
		}
		smartSort(dataList);
		result = PacketStatisticResponse.create(packet);

		// --- chart title
		String title = STDateGranularityCode.getDateRangeText(result);
		title = packet.getType()+"\n("+title+")";
		result.setChartTitle(title);

		// --- values
		double[] data = getValues(dataList);
		DataPoint.round(data);
		result.setDataSeries(new double[][] { data });

		// --- labels
		String[] labels = getLabels(dataList);
		result.setLabels(labels);
		result.setXAxisLabel("Wert (100 = 1 Personenwoche)");
		result.setYAxisLabel("Monteur");
		
		// --- CSV file
		result.addAttachment("AuftragListe.csv", csvFile.toBinary(null));
		return result;
	}

	/**
	 * Processes all data in KW context and populates data collections
	 */
	@SuppressWarnings("unchecked")
	void processKW(final ModelContext context) {
		final List<AbstractZuordnung> zuordnungList = (List) context.getModelList(new Filter<AbstractModel>() {
			@Override
			public boolean accepts(final AbstractModel model) {
				return model instanceof FeinZuordnung;
			}
		});
		for (final AbstractZuordnung zuordnung : zuordnungList) {
			final MonteurHelper monteur = getMonteur(zuordnung);
			if (monteur == null)
				continue;
			final AuftragEntry auftrag = getAuftrag(zuordnung);
			final ZuordnungCode type = zuordnung.getType();
			if (ZuordnungCode.ZUORDNUNG_SUPERVISOR.equals(type)) {
				// count "Begleitung""
				monteur.scoreBegleit += Zuordnung.getSlotLength(zuordnung) * (20/12D);
			} else if (ZuordnungCode.ZUORDNUNG_FAHRAUFTRAG.equals(type)) {
				// count "Fahrauftrag"
				monteur.scoreFahr += Zuordnung.getSlotLength(zuordnung) * (20/12D);
			} else if (ZuordnungCode.ZUORDNUNG_MONTAGE.equals(type)) {
				monteur.addAuftrag(auftrag);
			} else if (ZuordnungCode.ZUORDNUNG_ZUSATZ.equals(type)) {
				monteur.addAuftrag(auftrag);
			}
		}
	}
	
	// --------------------------------------------------------------
	// ---
	// --- Zuordnung Helper
	// ---
	// --------------------------------------------------------------
	MonteurHelper getMonteur(final Object zuordnung) {
		if (zuordnung instanceof FeinZuordnung) {
			final FeinZuordnung fein = (FeinZuordnung) zuordnung;
			final AbstractMitarbeiter mitarbeiter = fein.getMitarbeiter();
			return getMonteur(mitarbeiter);
		}
		return null;
	}
	
	// --------------------------------------------------------------
	// ---
	// --- Monteur Helper
	// ---
	// --------------------------------------------------------------
	static class MonteurHelper {
		long presentoId;
		String name;
		String firstName;
		String lastName;
		EmploymentType empType;

		AuftragEntry[] auftraege;

		double scoreBegleit;
		double scoreFahr;
		
		void addAuftrag(final AuftragEntry auftrag) {
			if (auftrag == null)
				return;
			if (auftraege == null) {
				auftraege = new AuftragEntry[] { auftrag };
			} else {
				final int l = auftraege.length;
				for (int i=l-1; i>=0; --i) {
					if (auftraege[i].equals(auftrag))
						return;
				}
				final AuftragEntry[] temp = new AuftragEntry[l+1];
				System.arraycopy(auftraege, 0, temp, 0, l);
				temp[l] = auftrag;
				auftraege = temp;
			}
			auftrag.addMonteur(this);
		}

		double getScore(final AbwicklungsartCode abwArt) {
			double result = 0;
			if (auftraege != null) {
				for (final AuftragEntry auftrag : auftraege) {
					if (abwArt.equals(auftrag.abwicklungsArt))
						result += auftrag.getBewertung();
				}
			}
			return result;
		}

		boolean isAllZero() {
			return scoreBegleit == 0 &&
				   scoreFahr == 0 &&
				   getScore(AbwicklungsartCode.AWA115) == 0 &&
				   getScore(AbwicklungsartCode.AWA134) == 0 &&
				   getScore(AbwicklungsartCode.AWA135) == 0;
		}
	
		@Override
		public int hashCode() {
			return (int) presentoId;
		}
	
		@Override
		public boolean equals(final Object o) {
			return o instanceof MonteurHelper && ((MonteurHelper) o).presentoId == presentoId;
		}
	}

	private final Map<Object, MonteurHelper> monteurMap = new HashMap<>(64);
	
	MonteurHelper getMonteur(final AbstractMitarbeiter mitarbeiter) {
		if (mitarbeiter == null ||
				!mitarbeiter.isMonteur() ||
				(mitarbeiter instanceof VirtualMitarbeiter))
			return null;
		final ServerModelContext global = server.getGlobalContext();
		final GSECode mitarbeiterGse = mitarbeiter.getGSE(global);
		if (!this.gse.equals(mitarbeiterGse))
			return null;
		final long pid = mitarbeiter.getPresentoId();
		MonteurHelper monteur = monteurMap.get(pid);
		if (monteur == null) {
			monteur = new MonteurHelper();
			monteur.name = mitarbeiter.getName();
			monteur.firstName = mitarbeiter.getFirstName();
			monteur.lastName = mitarbeiter.getLastName();
			monteur.presentoId = pid;
			monteur.empType = mitarbeiter.getEmploymentType();
			monteurMap.put(pid, monteur);
		}
		return monteur;
	}

	List<MonteurHelper> getMonteure() {
		final List<MonteurHelper> result = new ArrayList<MonteurHelper>(monteurMap.values());
		Collections.sort(result, new Comparator<MonteurHelper>() {
			@Override
			public int compare(final MonteurHelper o1, final MonteurHelper o2) {
				return o1.name.compareToIgnoreCase(o2.name);
			}
		});
		return result;
	}

	// --------------------------------------------------------------
	// ---
	// --- Auftrag Helper
	// ---
	// --------------------------------------------------------------
	static class AuftragEntry implements Comparable<AuftragEntry> {
		int auftragNummer;
		AbwicklungsartCode abwicklungsArt;
		double bewertung;
		String projektNummer;
		String basisNummer;
		String bezeichnung;
		String ausfuehrung;
		String ausfuehrungFront;
		String objektkuechenAnzahl;
		Date wunschDatum;
		Date kuechenEnde;
		LWGranit2Code granit2Code;
		AbrechnungCode abrechnungCode;
		
		MonteurHelper[] monteure;

		double getBewertung() {
			return bewertung / (monteure == null ? 1 : monteure.length);
		}

		void addMonteur(final MonteurHelper monteur) {
			if (monteur == null)
				return;
			if (monteure == null) {
				monteure = new MonteurHelper[] { monteur };
			} else {
				final int l = monteure.length;
				for (int i=l-1; i>=0; --i) {
					if (monteure[i].equals(monteur))
						return;
				}
				final MonteurHelper[] temp = new MonteurHelper[l+1];
				System.arraycopy(monteure, 0, temp, 0, l);
				temp[l] = monteur;
				monteure = temp;
			}
			monteur.addAuftrag(this);
		}

		@SuppressWarnings("unchecked")
		Iterator<MonteurHelper> getMonteure() {
			return ArrayIterator.get(monteure);
		}

		public MonteurHelper getFirstMonteur() {
			return monteure != null && monteure.length > 0 ? monteure[0] : null;
		}

		@Override
		public int compareTo(final AuftragEntry auftragEntry) {
			int r = Strings.compareIgnoreCase(basisNummer, auftragEntry.basisNummer);
			if (r == 0)
				r = Strings.compareIgnoreCase(projektNummer, auftragEntry.projektNummer);
			return r;
		}
	
		@Override
		public int hashCode() {
			return auftragNummer;
		}
	
		@Override
		public boolean equals(final Object o) {
			return o instanceof AuftragEntry && ((AuftragEntry) o).auftragNummer == auftragNummer;
		}
	}

	private final Map<Object, AuftragEntry> auftragMap = new HashMap<>(64);
	
	protected AuftragEntry getAuftrag(final AbstractZuordnung zuordnung) {
		final Auftrag auftrag = zuordnung.getAuftrag();
		if (auftrag == null)
			return null;
		final int auftragNummer = auftrag.getAuftragnummer();
		AuftragEntry entry = auftragMap.get(auftragNummer);
		if (entry == null) {
			entry = new AuftragEntry();
			entry.auftragNummer = auftragNummer;
			entry.projektNummer = auftrag.getProjektnummer();
			entry.basisNummer = auftrag.getBasisnummer();
			entry.wunschDatum = auftrag.getWunschdatum();
			entry.kuechenEnde = auftrag.getAbschlussdatum();
			entry.abwicklungsArt = auftrag.getAbwicklungsart();
			entry.bewertung = auftrag.getKuechenBewertungPercent();
			entry.bezeichnung = Strings.toSingleLine(auftrag.getProjektbezeichnung());
			entry.granit2Code = auftrag.getGranitschluessel2();
			entry.ausfuehrung = auftrag.getAusfuehrung();
			entry.ausfuehrungFront = auftrag.getAusfuehrungFront();
			entry.objektkuechenAnzahl = auftrag.getObjektKuechenAnzahl();
			entry.abrechnungCode = auftrag.getAbgerechnet();
			auftragMap.put(auftragNummer, entry);
		}
		return entry;
	}

	protected List<AuftragEntry> getAuftraege() {
		return new ArrayList<AuftragEntry>(auftragMap.values());
	}

}
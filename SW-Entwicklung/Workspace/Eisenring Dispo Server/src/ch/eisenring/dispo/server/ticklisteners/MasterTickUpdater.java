package ch.eisenring.dispo.server.ticklisteners;

import ch.eisenring.app.shared.timing.AlwaysDueTimer;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.dispo.server.DSPServer;

public class MasterTickUpdater extends AbstractTickUpdater {

	private final ArrayList<AbstractTickUpdater> updaters =
			new ArrayList<AbstractTickUpdater>();
	
	public MasterTickUpdater(final DSPServer server) throws Exception {
		super(server, new AlwaysDueTimer());
		// --- all updater's handled by the master updater
		updaters.add(new ServerEvictOveragedUpdater(server));
		updaters.add(new ServerEmployeeUpdater(server));
		updaters.add(new LogiwareSaleUpdater(server));
	}

	protected void tickImpl() {
		final Thread t = Thread.currentThread();
		final String n = t.getName();
		try {
			for (final AbstractTickUpdater updater : updaters) {
				t.setName(updater.getClass().getSimpleName());
				updater.tick();
			}
		} finally {
			t.setName(n);
		}
	}

}

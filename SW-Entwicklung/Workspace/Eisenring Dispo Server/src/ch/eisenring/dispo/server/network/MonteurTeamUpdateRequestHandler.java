package ch.eisenring.dispo.server.network;

import static ch.eisenring.dispo.shared.metamodel.DSPMapping.OM_MONTEURTEAM;

import java.sql.SQLException;

import ch.eisenring.app.server.model.ContextSink;
import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.dispo.server.DSPServer;
import ch.eisenring.dispo.shared.DSPConstants;
import ch.eisenring.dispo.shared.network.packets.PacketMonteurTeamNotify;
import ch.eisenring.dispo.shared.network.packets.PacketMonteurTeamUpdateReply;
import ch.eisenring.dispo.shared.network.packets.PacketMonteurTeamUpdateRequest;
import ch.eisenring.dispo.shared.pojo.DSPMonteurTeam;
import ch.eisenring.model.TransactionContext;
import ch.eisenring.model.primarykey.LongPrimaryKey;
import ch.eisenring.network.PacketSink;
import ch.eisenring.network.packet.AbstractPacket;

final class MonteurTeamUpdateRequestHandler extends AbstractDSPPacketHandler {

	private MonteurTeamUpdateRequestHandler(final DSPServer server) {
		super(server, PacketMonteurTeamUpdateRequest.class);
	}

	@Override
	public PacketMonteurTeamUpdateReply handle(final AbstractPacket packet) {
		final PacketMonteurTeamUpdateRequest request = (PacketMonteurTeamUpdateRequest) packet;
		final PacketMonteurTeamUpdateReply reply = handle(server, request);
		changeNotify(server, reply);
		return reply;
	}

	@Override
	public void handle(final AbstractPacket packet, final PacketSink sink) {
		final PacketMonteurTeamUpdateRequest request = (PacketMonteurTeamUpdateRequest) packet;
		final PacketMonteurTeamUpdateReply reply = handle(server, request);
		sink.sendPacket(reply);
		changeNotify(server, reply);
	}

	private static void changeNotify(final DSPServer server, final PacketMonteurTeamUpdateReply reply) {
		if (!reply.isValid())
			return;
		final PacketMonteurTeamNotify broadcast = PacketMonteurTeamNotify.create();
		server.broadcast(broadcast, false);
	}

	private static PacketMonteurTeamUpdateReply handle(final DSPServer server,
			final PacketMonteurTeamUpdateRequest request) {
		try {
			final PacketMonteurTeamUpdateReply reply = handleImpl(server, request);
			if (reply != null)
				return reply;
			return PacketMonteurTeamUpdateReply.create(request, ErrorMessage.ERROR);
		} catch (final Exception e) {
			return PacketMonteurTeamUpdateReply.create(request, new ErrorMessage(e));
		}
	}

	private static PacketMonteurTeamUpdateReply handleImpl(final DSPServer server,
			final PacketMonteurTeamUpdateRequest request) throws Exception {
		final ContextSink sink = new ContextSink(DSPConstants.DSP_DATABASE) {
			@Override
			protected void storeImpl(final TransactionContext context) throws SQLException {
				final DSPMonteurTeam team = request.getTeam();
				if (team.getRowId() == LongPrimaryKey.AUTO_ASSIGN) {
					OM_MONTEURTEAM.insert(this, team);
				} else {
					OM_MONTEURTEAM.update(this, team);
				}
			}
		};
		final TransactionContext context = new TransactionContext();
		context.store(sink);
		return PacketMonteurTeamUpdateReply.create(request, ErrorMessage.OK);
	}

}

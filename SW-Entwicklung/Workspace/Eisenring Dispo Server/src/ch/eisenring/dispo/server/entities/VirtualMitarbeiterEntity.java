package ch.eisenring.dispo.server.entities;

import java.util.Date;

import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.dispo.shared.codetables.EmploymentRole;
import ch.eisenring.dispo.shared.model.AbstractModel;
import ch.eisenring.dispo.shared.model.VirtualMitarbeiter;
import ch.eisenring.logiware.code.soft.GSECode;

public class VirtualMitarbeiterEntity extends AbstractEntity {

	private long presentoId;
	private String firstName;
	private String lastName;
	private String shortName;
	private String gseKey;
	private int highlight;
	private int sortHint;
	private int type;
	private Date validDate;
	
	@Override
	public Class<? extends AbstractModel> getModelClass() {
		return VirtualMitarbeiter.class;
	}
	
	@Override
	public void updateModel(AbstractModel model) {
		super.updateModel(model);
		final VirtualMitarbeiter mitarbeiter = (VirtualMitarbeiter) model;
		mitarbeiter.setPresentoId(getPresentoId());
		mitarbeiter.setShortName(getShortName());
		mitarbeiter.setFirstName(getFirstName());
		mitarbeiter.setLastName(getLastName());
		mitarbeiter.setHighlight(getHighlight());
		mitarbeiter.setSortHint(getSortHint());
		mitarbeiter.setEmploymentRole((EmploymentRole) AbstractCode.getById(getType(), EmploymentRole.class));
		mitarbeiter.setGSE((GSECode) AbstractCode.getByKey(getGseKey(), GSECode.class));
	}
	
	@Override
	public void updateEntity(AbstractModel model) {
		super.updateEntity(model);
		final VirtualMitarbeiter mitarbeiter = (VirtualMitarbeiter) model;
		updateDirty(getPresentoId(), mitarbeiter.getPresentoId());
		setPresentoId(mitarbeiter.getPresentoId());
		updateDirty(getShortName(), mitarbeiter.getShortName());
		setShortName(mitarbeiter.getShortName());
		updateDirty(getFirstName(), mitarbeiter.getFirstName());
		setFirstName(mitarbeiter.getFirstName());
		updateDirty(getLastName(), mitarbeiter.getLastName());
		setLastName(mitarbeiter.getLastName());
		updateDirty(getHighlight(), mitarbeiter.getHighlight());
		setHighlight(mitarbeiter.getHighlight());
		updateDirty(getSortHint(), mitarbeiter.getSortHint());
		setSortHint(mitarbeiter.getSortHint());
		updateDirty(getType(), mitarbeiter.getEmploymentRole().getId());
		setType(mitarbeiter.getEmploymentRole().getId());

		final Date contextDate = TimestampUtil.toDate(mitarbeiter.getContext().getBegin());
		updateDirty(getValidDate(), contextDate);
		setValidDate(contextDate);

		final String gseKey = (String) AbstractCode.getKey(mitarbeiter.getGSE(null), null);
		updateDirty(getGseKey(), gseKey);
		setGseKey(gseKey);
	}

	public Date getValidDate() {
		return validDate;
	}

	public void setValidDate(Date date) {
		this.validDate = date;
	}

	public long getPresentoId() {
		return presentoId;
	}
	
	public void setPresentoId(long presentoId) {
		this.presentoId = presentoId;
	}

	public String getFirstName() {
		return firstName;
	}

	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}

	public String getLastName() {
		return lastName;
	}

	public void setLastName(String lastName) {
		this.lastName = lastName;
	}

	public String getShortName() {
		return shortName;
	}

	public void setShortName(String shortName) {
		this.shortName = shortName;
	}

	public int getHighlight() {
		return highlight;
	}

	public void setHighlight(int highlight) {
		this.highlight = highlight;
	}

	public int getSortHint() {
		return sortHint;
	}

	public void setSortHint(int sortHint) {
		this.sortHint = sortHint;
	}

	public int getType() {
		return type;
	}

	public void setType(int type) {
		this.type = type;
	}

	public String getGseKey() {
		return gseKey;
	}

	public void setGseKey(String gseKey) {
		this.gseKey = gseKey;
	}

	@Override
	public String getObjectDescription() {
		final VirtualMitarbeiter mitarbeiter = (VirtualMitarbeiter) getModel();
		final StringMaker b = StringMaker.obtain(128);
		b.append("VirtualMitarbeiter(");
		b.append("Name=");
		b.append(mitarbeiter.getName());
		b.append(";Week=");
		b.append(mitarbeiter.getContext().getBegin(), TimestampUtil.SQLDATETIME);
		b.append(')');
		return b.release();
	}		

}

package ch.eisenring.dispo.server.resources;

import java.time.Duration;

import ch.eisenring.app.server.util.MailTemplateUtil;
import ch.eisenring.core.application.DataFile;

public interface EMailTemplates {

	static final Duration MAX_AGE = Duration.ofMinutes(30);

	/**
	 * Template text for notification MontageBewertung
	 */
	public final static DataFile<String> TEMPLATE_MONTAGEBEWERTUNG_SOLLABWEICHUNG =
			new DataFile<>("../data/dsp/email-templates/Montagebewertung-Sollabweichung.template",
					MAX_AGE, MailTemplateUtil.MAIL_TEMPLATE_PARSER);

}

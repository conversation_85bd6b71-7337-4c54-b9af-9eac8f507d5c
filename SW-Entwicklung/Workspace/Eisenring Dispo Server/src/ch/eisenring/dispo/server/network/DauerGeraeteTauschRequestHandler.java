package ch.eisenring.dispo.server.network;

import java.nio.charset.StandardCharsets;
import java.sql.SQLException;

import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.csv.CSVFile;
import ch.eisenring.core.csv.CSVLine;
import ch.eisenring.core.datatypes.date.Period;
import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.core.datatypes.date.WorkingDays;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.io.binary.BinaryHolder;
import ch.eisenring.core.util.file.FileImage;
import ch.eisenring.dispo.server.DSPServer;
import ch.eisenring.dispo.shared.network.packets.PacketDauerGeraeteTauschReply;
import ch.eisenring.dispo.shared.network.packets.PacketDauerGeraeteTauschRequest;
import ch.eisenring.jdbc.JDBCResultSet;
import ch.eisenring.jdbc.RowHandler;
import ch.eisenring.jdbc.StatementParameters;
import ch.eisenring.logiware.LWContextSource;
import ch.eisenring.lw.LWConstants;
import ch.eisenring.model.TransactionContext;
import ch.eisenring.network.PacketSink;
import ch.eisenring.network.packet.AbstractPacket;

final class DauerGeraeteTauschRequestHandler extends AbstractDSPPacketHandler {

	DauerGeraeteTauschRequestHandler(final DSPServer server) {
		super(server, PacketDauerGeraeteTauschRequest.class);
	}

	@Override
	public void handle(final AbstractPacket packet, final PacketSink sink) {
		final PacketDauerGeraeteTauschReply reply = handle(packet);
		sink.sendPacket(reply);
	}

	@Override
	public PacketDauerGeraeteTauschReply handle(final AbstractPacket packet) {
		final PacketDauerGeraeteTauschRequest request = (PacketDauerGeraeteTauschRequest) packet;
		final PacketDauerGeraeteTauschReply reply = handle(server, request);
		return reply;
	}

	static PacketDauerGeraeteTauschReply handle(final DSPServer server, final PacketDauerGeraeteTauschRequest request) {
		try {
			final PacketDauerGeraeteTauschReply reply = handleImpl(server, request);
			if (reply != null)
				return reply;
			return PacketDauerGeraeteTauschReply.create(request, ErrorMessage.ERROR);
		} catch (final Exception e) {
			return PacketDauerGeraeteTauschReply.create(request, new ErrorMessage(e));
		}
	}

	static PacketDauerGeraeteTauschReply handleImpl(final DSPServer server, final PacketDauerGeraeteTauschRequest request) throws Exception {
		final CSVFile csv = getCSV(server, request);
		final BinaryHolder binary = csv.toBinary(StandardCharsets.ISO_8859_1);
		final FileImage file = FileImage.create("Dauer_Geraetetausch.csv", binary);
		PacketDauerGeraeteTauschReply reply = PacketDauerGeraeteTauschReply.create(request, ErrorMessage.OK);
		reply.setFile(file);
		return reply;
	}

	static CSVFile getCSV(final DSPServer server, final PacketDauerGeraeteTauschRequest request) throws Exception {
		final CSVFile csvFile = new CSVFile();
		// add header line
		{
			final CSVLine header = csvFile.addLine();
			header.setColumn(0, "W-Dat");
			header.setColumn(1, "Eröffn. Datum");
			header.setColumn(2, "Kom.");
			header.setColumn(3, "Auftrnr.");
			header.setColumn(4, "AWA");
			header.setColumn(5, "d-AT");
		}
		final LWContextSource source = new LWContextSource(LWConstants.VERTRIEB) {
			@Override
			protected void loadImpl(final TransactionContext context) throws SQLException {
				final Period period = request.getPeriod();
				// prepare the query
				final StatementParameters params = new StatementParameters();
				final StringMaker sql = StringMaker.obtain();
				sql.append("SELECT DISTINCT A.DatWu, A.AufDat, A.PAuftrNr, A.AuftrNr, A.AbwArt " +
						"FROM Auftrag A, AUFPOS P, PRODZORD Z WHERE ");
				if (!TimestampUtil.isNull(period.getBegin())) {
					sql.append("A.DatWu >= ? AND ");
					params.addDate(period.getBegin());
				}
				if (!TimestampUtil.isNull(period.getEnd())) {
					sql.append("A.DatWu < ? AND ");
					params.addDate(period.getEnd());
				}
				sql.append("A.Status != '099' AND A.AbwArt IN ('113', '114', '136') AND " +
						  "A.AuftrNr = P.AuftrNr AND A.Id_GSE = P.Id_GSE AND P.Status != '099' AND " +
						  "P.ProdNr = Z.ProdNr AND P.Id_GSE_PRODUKT = Z.Id_GSE AND " + 
						  "Z.PRODGrArt = '020' AND Z.Gruppe = '99' " +
						"ORDER BY A.AuftrNr");
				doQuery(sql.release(), params, new RowHandler() {
					@Override
					public void handleRow(final JDBCResultSet resultSet) throws SQLException {
						final long datWu = resultSet.getLongTimestamp(1);
						final long aufDat = resultSet.getLongTimestamp(2);
						final String komNr = resultSet.getString(3);
						final int auftrNr = resultSet.getInt(4);
						final String abwArt = resultSet.getString(5);
						// create CSV line
						final CSVLine line = csvFile.addLine();
						line.setColumn(0, TimestampUtil.DATE10.format(datWu));
						line.setColumn(1, TimestampUtil.DATE10.format(aufDat));
						line.setColumn(2, komNr);
						line.setColumn(3, auftrNr);
						line.setColumn(4, abwArt);
						line.setColumn(5, WorkingDays.numberOfWeekDaysBetween(aufDat, datWu));
					}
				});
			}
		};
		final TransactionContext context = new TransactionContext();
		context.load(source);
		return csvFile;
	}

}

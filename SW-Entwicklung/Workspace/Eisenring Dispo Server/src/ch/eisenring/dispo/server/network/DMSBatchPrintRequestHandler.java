package ch.eisenring.dispo.server.network;

import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.print.PageFormatCode;
import ch.eisenring.core.print.PageOrderCode;
import ch.eisenring.dispo.server.DSPServer;
import ch.eisenring.dispo.shared.montageprint.MBPBatchJob;
import ch.eisenring.dispo.shared.network.packets.PacketDMSBatchPrintReply;
import ch.eisenring.dispo.shared.network.packets.PacketDMSBatchPrintRequest;
import ch.eisenring.dms.service.DMSService;
import ch.eisenring.dms.service.api.DMSDocumentImage;
import ch.eisenring.dms.service.api.DMSObjectIdentity;
import ch.eisenring.network.PacketSink;
import ch.eisenring.network.packet.AbstractPacket;

final class DMSBatchPrintRequestHandler extends AbstractDSPPacketHandler {

	private DMSBatchPrintRequestHandler(final DSPServer server) {
		super(server, PacketDMSBatchPrintRequest.class);
	}

	@Override
	public PacketDMSBatchPrintReply handle(final AbstractPacket packet) {
		final PacketDMSBatchPrintRequest request = (PacketDMSBatchPrintRequest) packet;
		final PacketDMSBatchPrintReply reply = handle(server, request);
		return reply;
	}

	@Override
	public void handle(final AbstractPacket packet, final PacketSink sink) {
		final PacketDMSBatchPrintRequest request = (PacketDMSBatchPrintRequest) packet;
		final PacketDMSBatchPrintReply reply = handle(server, request);
		sink.sendPacket(reply);
	}

	private static PacketDMSBatchPrintReply handle(final DSPServer server, final PacketDMSBatchPrintRequest request) {
		try {
			final PacketDMSBatchPrintReply reply = handleImpl(server, request);
			if (reply != null)
				return reply;
			return PacketDMSBatchPrintReply.create(request, ErrorMessage.ERROR);
		} catch (final Exception e) {
			return PacketDMSBatchPrintReply.create(request, new ErrorMessage(e));
		}
	}

	private static PacketDMSBatchPrintReply handleImpl(final DSPServer server, final PacketDMSBatchPrintRequest request) throws Exception {
		final DMSService service = server.locateService(DMSService.class);
		final MBPBatchJob batch = new MBPBatchJob();
		batch.setName(request.getPrintJobName());
		for (final DMSObjectIdentity documentId : request.getObjectIds()) {
			final DMSDocumentImage file;
			try {
				file = service.getDocumentImage(documentId);
			} catch (final Exception e) {
				throw new RuntimeException(Strings.concat("Dokument Id ", documentId, " nicht gefunden"), e);
			}
			try {
				batch.addPDF(file.getFiledata(), PageFormatCode.A4, PageOrderCode.NORMAL);
			} catch (final Exception e) {
				throw new RuntimeException(Strings.concat("Dokument Id ", documentId, " Fehler in Druckvorbereitung"), e);
			}
		}
		batch.print(request.getPrinter());
		return PacketDMSBatchPrintReply.create(request, ErrorMessage.OK);
	}

}

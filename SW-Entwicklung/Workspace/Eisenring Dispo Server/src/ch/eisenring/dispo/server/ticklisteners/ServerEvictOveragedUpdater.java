package ch.eisenring.dispo.server.ticklisteners;

import ch.eisenring.app.shared.timing.PeriodicTimer;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.dispo.server.DSPServer;

public final class ServerEvictOveragedUpdater extends AbstractTickUpdater {

	public ServerEvictOveragedUpdater(final DSPServer server) {
		super(server, new PeriodicTimer(300));
	}

	@Override
	protected void tickImpl() {
		Logger.info("checking for overaged model contexts");
		final long evictTime = System.currentTimeMillis() - (server.STATE_EVICT_INTERVAL.get() * 1000);
		server.getContextCache().evictOveraged(evictTime);
	}

}

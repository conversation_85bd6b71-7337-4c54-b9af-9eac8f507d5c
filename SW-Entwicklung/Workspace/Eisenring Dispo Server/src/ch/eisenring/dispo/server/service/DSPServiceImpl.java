package ch.eisenring.dispo.server.service;

import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.dispo.server.DSPServer;
import ch.eisenring.dispo.server.network.BasisprojektGetRequestHandler;
import ch.eisenring.dispo.service.DSPService;
import ch.eisenring.dispo.service.codetables.YesNoCode;
import ch.eisenring.dispo.service.model.DSPBasisProjekt;
import ch.eisenring.dispo.service.model.MonteurSirInfo;
import ch.eisenring.dispo.shared.model.MasterMitarbeiter;
import ch.eisenring.dispo.shared.model.ModelContext;
import ch.eisenring.dispo.shared.network.packets.PacketBasisprojektGetReply;
import ch.eisenring.dispo.shared.network.packets.PacketBasisprojektGetRequest;
import ch.eisenring.logiware.LWProjektKey;
import ch.eisenring.logiware.code.soft.LWMonteurSirCode;

public class DSPServiceImpl implements DSPService {

	private DSPServer server;

	public DSPServiceImpl(final DSPServer server) {
		this.server = server;
	}

	// --------------------------------------------------------------
	// ---
	// --- Service methods 
	// ---
	// --------------------------------------------------------------
	@Override
	public DSPBasisProjekt getBasisProjektInfo(final LWProjektKey basisProjektKey) {
		// obtain basis project info
		try {
			final PacketBasisprojektGetRequest request = PacketBasisprojektGetRequest.create(basisProjektKey);
			final PacketBasisprojektGetReply reply = (PacketBasisprojektGetReply) server.handle(request);
			if (!reply.isValid())
				return null;
			return reply.getBasisprojekt(basisProjektKey);
		} catch (final Exception ignored) {
			return null;
		}
	}
	
	@Override
	public MonteurSirInfo getBauleitenderMonteur(final LWProjektKey basisProjektKey) {
		final DSPBasisProjekt projekt = getBasisProjektInfo(basisProjektKey);
		if (projekt == null)
			return null;
		try {
			return getMonteurSirInfo(projekt.getBauleitenderMonteur());
		} catch (final Exception ignored) {
			return null;
		}
	}

	@Override
	public MonteurSirInfo getMonteurSirInfo(final LWMonteurSirCode monteurSir) {
		final MasterMitarbeiter mitarbeiter = getMitarbeiter(monteurSir);
		return createInfo(mitarbeiter, monteurSir);
	}

	@Override
	public boolean getObjektuebergabe(final LWProjektKey basisProjektKey) {
		// obtain basis project info
		try {
			PacketBasisprojektGetRequest request = PacketBasisprojektGetRequest.create(basisProjektKey);
			PacketBasisprojektGetReply reply = BasisprojektGetRequestHandler.handle(server, request);
			if (!reply.isValid())
				return false;
			DSPBasisProjekt projekt = reply.getBasisprojekt(basisProjektKey);
			if (projekt == null)
				return false;
			return YesNoCode.YES.equals(projekt.getObjektUebergabe());
		} catch (final Exception ignored) {
			return false;
		}
	}

	// --------------------------------------------------------------
	// ---
	// --- Service private methods 
	// ---
	// --------------------------------------------------------------
	private MasterMitarbeiter getMitarbeiter(final LWMonteurSirCode monteurSir) {
		if (AbstractCode.isNull(monteurSir))
			return null;
		try {
			final ModelContext context = server.getContextCache().getGlobalContext();
			return context.getMitarbeiter(monteurSir.getShortText(), MasterMitarbeiter.class);
		} catch (Exception ignored) {
			// don't care about any exceptions
			return null;
		}
		
	}

	private MonteurSirInfo createInfo(final MasterMitarbeiter mitarbeiter, final LWMonteurSirCode monteurSir) {
		if (mitarbeiter == null)
			return null;
		final MonteurSirInfo result = new MonteurSirInfo();
		result.setMonteurSirCode(monteurSir);
		result.setNameFirst(mitarbeiter.getFirstName());
		result.setNameLast(mitarbeiter.getLastName());
		result.setPhoneMobile(mitarbeiter.getPhoneMobile());
		return result;
	}

}

package ch.eisenring.dispo.server.network;

import java.sql.SQLException;
import java.util.Date;

import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.Map;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.collections.impl.HashMap;
import ch.eisenring.core.datatypes.date.DateGranularity;
import ch.eisenring.core.datatypes.date.DateUtil;
import ch.eisenring.core.datatypes.primitives.Primitives;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.iterators.Filter;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.dispo.server.DSPServer;
import ch.eisenring.dispo.shared.network.packets.PacketReportAbschlussquotenReply;
import ch.eisenring.dispo.shared.network.packets.PacketReportAbschlussquotenRequest;
import ch.eisenring.jdbc.JDBCResultSet;
import ch.eisenring.jdbc.StatementWrapper;
import ch.eisenring.logiware.LWContextSource;
import ch.eisenring.logiware.LWProjektKey;
import ch.eisenring.logiware.code.soft.GSECode;
import ch.eisenring.logiware.code.soft.LWGranit2Code;
import ch.eisenring.logiware.code.soft.LWObjektStatusCode;
import ch.eisenring.lw.LWConstants;
import ch.eisenring.model.TransactionContext;
import ch.eisenring.network.PacketSink;
import ch.eisenring.network.packet.AbstractPacket;
import ch.eisenring.statistics.shared.data.DataPoint;

final class ReportAbschlussquotenRequestHandler extends AbstractDSPPacketHandler {

	ReportAbschlussquotenRequestHandler(final DSPServer server) {
		super(server, PacketReportAbschlussquotenRequest.class);
	}

	public void handle(final AbstractPacket abstractPacket, final PacketSink sink) {
		final PacketReportAbschlussquotenRequest request = (PacketReportAbschlussquotenRequest) abstractPacket;
		final PacketReportAbschlussquotenReply reply = handle(server, request);
		sink.sendPacket(reply, false);
	}

	public static PacketReportAbschlussquotenReply handle(final DSPServer server, final PacketReportAbschlussquotenRequest request) {
		try {
			final PacketReportAbschlussquotenReply reply = handleImpl(server, request);
			if (reply != null)
				return reply;
			return PacketReportAbschlussquotenReply.create(request, ErrorMessage.ERROR);
		} catch (final Exception e) {
			Logger.error(e);
			return PacketReportAbschlussquotenReply.create(request, new ErrorMessage(e));
		}
	}

	private static PacketReportAbschlussquotenReply handleImpl(
			final DSPServer server, final PacketReportAbschlussquotenRequest request) throws Exception {
		final DateGranularity granularity = request.getGranularity();
		final Date dateFrom = granularity.round(request.getDateFrom(), 0);
		final Date dateUpto = granularity.round(request.getDateUpto(), 1);
		final Map<LWProjektKey, QProjekt> projektMap = getAllProjekte(server);
		getAll175(server, projektMap);
		getAll172(server, projektMap);
		final List<QProjekt> allProjekte = filterAndNormalize(granularity, projektMap.values());

		// prepare data filters
		final Filter<QProjekt> g2filter;
		if (!AbstractCode.isNull(request.getGranit2())) {
			final LWGranit2Code g2code = request.getGranit2();
			g2filter = new Filter<QProjekt>() {
				@Override
				public boolean accepts(final QProjekt projekt) {
					return AbstractCode.equals(g2code, projekt.granit2);
				}
			};
		} else {
			g2filter = Filter.Util.getFixedResultFilter(QProjekt.class, true);
		}
		final Filter<QProjekt> vkFilter;
		final Integer verkaeufer = request.getVerkaeufer();
		if (verkaeufer == null) {
			vkFilter = null;
		} else {
			vkFilter = new Filter<QProjekt>() {
				@Override
				public boolean accepts(final QProjekt projekt) {
					return Primitives.equals(verkaeufer, projekt.verkaeufer);
				}
			};
		}
		// prepare data set
		final List<DataPoint> dataPoints = new ArrayList<>(64);
		Date kw = dateFrom;
		while (kw.before(dateUpto)) {
			// get all auftrag in KW
			final String mnString = granularity.toString(kw, "");
			final Date theDate = kw;
			List<QProjekt> basisMenge = Filter.Util.filter(allProjekte, new Filter<QProjekt>() {
				@Override
				public boolean accepts(final QProjekt projekt) {
					return DateUtil.equals(projekt.date, theDate);
				}
			}, g2filter);
			final List<QProjekt> accepted = Filter.Util.filter(basisMenge, OFFER_ACCEPTED);
			final List<QProjekt> rejected = Filter.Util.filter(basisMenge, OFFER_REJECTED);

			int a, r, t;
			double v;

			// HEAG 
			a = accepted.size();
			r = rejected.size();
			t = a + r;
			v = (a <= 0) ? 0D : ((double) a) / t;
			dataPoints.add(new DataPoint(Strings.concat("HEAG\n", mnString), v * 100));

			if (vkFilter != null) {
				final List<QProjekt> acceptedV = Filter.Util.filter(accepted, vkFilter);
				final List<QProjekt> rejectedV = Filter.Util.filter(rejected, vkFilter);
				a = acceptedV.size();
				r = rejectedV.size();
				t = a + r;
				v = (a <= 0) ? 0D : ((double) a) / t;
				dataPoints.add(new DataPoint(Strings.concat(verkaeufer, "\n", mnString), v * 100));
			}
			
			kw = granularity.round(kw, 1);
		}
		return PacketReportAbschlussquotenReply.create(request, dataPoints);
	}

	private final static Filter<QProjekt> OFFER_ACCEPTED = new Filter<QProjekt>() {
		@Override
		public boolean accepts(final QProjekt projekt) {
			final LWObjektStatusCode status = projekt.objektStatus;
			return !AbstractCode.isNull(status) && status.getOfferState() == LWObjektStatusCode.OFFER_ACCEPTED;
		}
	};

	private final static Filter<QProjekt> OFFER_REJECTED = new Filter<QProjekt>() {
		@Override
		public boolean accepts(final QProjekt projekt) {
			final LWObjektStatusCode status = projekt.objektStatus;
			return !AbstractCode.isNull(status) && status.getOfferState() == LWObjektStatusCode.OFFER_REJECTED;
		}
	};

//	private final static CollectionFilter<QProjekt> FILTER_EINZEL = new CollectionFilter<QProjekt>() {
//		@Override
//		public boolean accepts(final QProjekt projekt) {
//			final Granit2Code granit2 = projekt.granit2;
//			return !AbstractCode.isNull(granit2) && granit2.isEinzelkueche();
//		}
//	};
//
//	private final static CollectionFilter<QProjekt> FILTER_OBJEKT = new CollectionFilter<QProjekt>() {
//		@Override
//		public boolean accepts(final QProjekt projekt) {
//			final Granit2Code granit2 = projekt.granit2;
//			return !AbstractCode.isNull(granit2) && granit2.isObjekt();
//		}
//	};
	
	private static Map<LWProjektKey, QProjekt> getAllProjekte(final DSPServer server) throws Exception {
		final Map<LWProjektKey, QProjekt> result = new HashMap<>();
		final LWContextSource source = new LWContextSource(LWConstants.VERTRIEB) {
			@Override
			protected void loadImpl(final TransactionContext context) throws SQLException {
				final String sql = "SELECT PAuftrNr, Id_GSE_PA, ObjektStatus, Vertreter FROM PAuftrag";
				final StatementWrapper select = prepareStatement(sql);
				try {
					final JDBCResultSet resultSet = select.executeQuery();
					try {
						while (resultSet.next()) {
							final LWObjektStatusCode statusCode = resultSet.getCode(3, LWObjektStatusCode.class);
							if (AbstractCode.isNull(statusCode) ||
									statusCode.getOfferState() == LWObjektStatusCode.OFFER_UNDETERMINED)
								continue;
							final String projektnummer = resultSet.getString(1);
							final GSECode gse = resultSet.getCode(2, GSECode.class);
							final QProjekt projekt = new QProjekt(projektnummer, gse);
							result.put(projekt.projektKey, projekt);
							projekt.objektStatus = statusCode;
							int verkauefer = resultSet.getInt(4);
							if (!resultSet.wasNull()) {
								projekt.verkaeufer = Integer.valueOf(verkauefer);
							}
						}
					} finally {
						closeSilent(resultSet);
					}
				} finally {
					closeSilent(select);
				}
			}
		}; 
		final TransactionContext context = new TransactionContext();
		context.load(source);
		return result;
	}

	private static void getAll175(final DSPServer server, final Map<LWProjektKey, QProjekt> projektMap) throws Exception {
		final LWContextSource source = new LWContextSource(LWConstants.VERTRIEB) {
			@Override
			protected void loadImpl(final TransactionContext context) throws SQLException {
				final String sql = "SELECT"
								+ " P.PAuftrnr_B"
								+ ", P.Id_GSE_PA_B"
								+ ", K.Sch_Block2"
								+ ", F.FormDatum"
							+ " FROM"
								+ " AUFTRAG A"
								+ ", AuftragsKopfZusatz K"
								+ ", PAUFTRAG P"
								+ ", FORMULAR F"
							+ " WHERE"
								+ " A.AbwArt = '175' AND F.FormArt = '206'"
								+ " AND A.AuftrNr = K.AuftrNr AND A.Id_GSE = K.ID_GSE"
								+ " AND A.Status NOT IN ('099')"
								+ " AND A.PAuftrNr = P.PAuftrNr AND A.Id_GSE_PA = P.Id_GSE_PA"
								+ " AND A.AuftrNr = F.AuftrNr AND A.Id_GSE = F.Id_GSE";
				final StatementWrapper select = prepareStatement(sql);
				try {
					final JDBCResultSet resultSet = select.executeQuery();
					try {
						while (resultSet.next()) {
							String basisnummer = resultSet.getString(1);
							try {
								basisnummer = LWProjektKey.getSignificantProjektnummer(basisnummer);
							} catch (final Exception e) {
								continue;
							}
							final GSECode gse = resultSet.getCode(2, GSECode.class);
							final LWProjektKey basisKey = LWProjektKey.get(basisnummer, gse);
							final QProjekt projekt = projektMap.get(basisKey);
							if (projekt == null)
								continue;
							final LWGranit2Code g2 = resultSet.getCode(3, LWGranit2Code.class);
							if (!AbstractCode.isNull(g2) && AbstractCode.isNull(projekt.granit2))
								projekt.granit2 = g2;
							final Date date = DateUtil.copy(resultSet.getTimestamp(4));
							if (projekt.date == null ||
									date.after(projekt.date)) {
								projekt.date = date;
							} 
						}
					} finally {
						closeSilent(resultSet);
					}
				} finally {
					closeSilent(select);
				}
			}
		}; 
		final TransactionContext context = new TransactionContext();
		context.load(source);
	}

	private static void getAll172(final DSPServer server, final Map<LWProjektKey, QProjekt> projektMap) throws Exception {
		final LWContextSource source = new LWContextSource(LWConstants.VERTRIEB) {
			@Override
			protected void loadImpl(final TransactionContext context) throws SQLException {
				final String sql = "SELECT"
								+ " P.PAuftrnr_B"
								+ ", P.Id_GSE_PA_B"
								+ ", K.Sch_Block2"
							+ " FROM"
								+ " AUFTRAG A"
								+ ", AuftragsKopfZusatz K"
								+ ", PAUFTRAG P"
							+ " WHERE"
								+ " A.AbwArt = '172'"
								+ " AND A.AuftrNr = K.AuftrNr AND A.Id_GSE = K.ID_GSE"
								+ " AND A.Status NOT IN ('099')"
								+ " AND A.PAuftrNr = P.PAuftrNr AND A.Id_GSE_PA = P.Id_GSE_PA";
				final StatementWrapper select = prepareStatement(sql);
				try {
					final JDBCResultSet resultSet = select.executeQuery();
					try {
						while (resultSet.next()) {
							String basisnummer = resultSet.getString(1);
							try {
								basisnummer = LWProjektKey.getSignificantProjektnummer(basisnummer);
							} catch (final Exception e) {
								continue;
							}
							final GSECode gse = resultSet.getCode(2, GSECode.class);
							final LWProjektKey basisKey = LWProjektKey.get(basisnummer, gse);
							final QProjekt projekt = projektMap.get(basisKey);
							if (projekt == null)
								continue;
							final LWGranit2Code g2 = resultSet.getCode(3, LWGranit2Code.class);
							if (!AbstractCode.isNull(g2) && AbstractCode.isNull(projekt.granit2))
								projekt.granit2 = g2;
						}
					} finally {
						closeSilent(resultSet);
					}
				} finally {
					closeSilent(select);
				}
			}
		}; 
		final TransactionContext context = new TransactionContext();
		context.load(source);
	}

	private static List<QProjekt> filterAndNormalize(final DateGranularity granularity, final java.util.Collection<QProjekt> projekte) {
		final List<QProjekt> result = new ArrayList<QProjekt>();
		for (final QProjekt projekt : projekte) {
			if (projekt.date == null)
				continue;
			projekt.date = granularity.round(projekt.date, 0);
			result.add(projekt);
		}
		return result;
	}

	static class QProjekt {
		public final LWProjektKey projektKey;
		public LWObjektStatusCode objektStatus;
		public LWGranit2Code granit2;
		public Date date;
		public Integer verkaeufer;
		
		public QProjekt(final String projektnummer, final GSECode gse) {
			this.projektKey = LWProjektKey.get(projektnummer, gse);
		}
	}

}

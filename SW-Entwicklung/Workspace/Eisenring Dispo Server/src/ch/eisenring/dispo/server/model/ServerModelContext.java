package ch.eisenring.dispo.server.model;

import static ch.eisenring.dispo.shared.DSPConstants.GLOBAL_CONTEXT_TIMESTAMP;
import static ch.eisenring.dispo.shared.DSPConstants.MDLF_LOGIDIRTY;

import java.io.IOException;
import java.util.Date;
import java.util.concurrent.atomic.AtomicInteger;

import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.dispo.server.DSPServer;
import ch.eisenring.dispo.server.context.GlobalEntityContext;
import ch.eisenring.dispo.server.entities.EntityContext;
import ch.eisenring.dispo.server.logiware.LWExportBinding;
import ch.eisenring.dispo.server.ticklisteners.LogiwareSaleUpdater;
import ch.eisenring.dispo.shared.model.AbstractModel;
import ch.eisenring.dispo.shared.model.Auftrag;
import ch.eisenring.dispo.shared.model.ModelContext;
import ch.eisenring.lw.export.LWExportAuftrag;
import ch.eisenring.lw.export.LWExporter;

public final class ServerModelContext extends ModelContext {

	protected final DSPServer server;
	protected final EntityContext entityContext;

	protected final AtomicInteger points = new AtomicInteger(5);

	public ServerModelContext(final DSPServer server) {
		super(GLOBAL_CONTEXT_TIMESTAMP, server.getOIDGenerator());
		this.server = server;
		this.entityContext = new GlobalEntityContext(this);
	}

	public ServerModelContext(Date date, DSPServer server) {
		this(TimestampUtil.toTimestamp(date), server);
	}

	public ServerModelContext(final long date, DSPServer server) {
		super(date, server.getOIDGenerator());
		this.server = server;
		this.entityContext = new EntityContext(this);
	}
	
	public DSPServer getServer() {
		return server;
	}

	/**
	 * Gets the entity context associated with this model context
	 */
	public EntityContext getEntityContext() {
		return entityContext;
	}

	/**
	 * Gets a direct reference to the context's internal list
	 * of model objects. This is a very critical collection
	 * and should not be changed directly.
	 * Also it is necessary to work with the list while
	 * maintaining a lock on the context.
	 */
	public List<AbstractModel> getModelListReference() {
		return modelList;
	}

	private List<LWExportAuftrag> prepareLWExport() {
		final List<LWExportAuftrag> result = new ArrayList<>();
		final LWExportBinding binding = new LWExportBinding(server);
		obtain();
		try {
			final int dirtyFlags = MDLF_LOGIDIRTY;
			for (int i=modelList.size()-1; i>=0; --i) {
				final AbstractModel model = modelList.get(i);
				if (model instanceof Auftrag) {
					final Auftrag auftrag = (Auftrag) model;
					final int flags = auftrag.getFlags();
					if (dirtyFlags==(dirtyFlags&flags)) {
						if (!auftrag.isDeleted()) {
							final LWExportAuftrag ex = binding.createExport(auftrag);
							result.add(ex);
						}
					}
				}
			}
		} finally {
			release();
		}
		return result;
	}
	
	/**
	 * Stores this context
	 */
	public boolean store() {
		boolean success = true;
		obtain();
		try {
			do {
				final List<LWExportAuftrag> exportAuftraege = prepareLWExport();
				if (!deleteMovedAuftraege())
					break;
				final EntityContext entityContext = getEntityContext();
				if (!entityContext.storeContext())
					break;
				// check if we really want to write to database
				try {
					final LWExporter exporter = LWExporter.create(server);
					Logger.trace("Exporting Auftraege from DISPO now");
					exporter.export(exportAuftraege);
				} catch (final IOException e) {
					success = false;
					Logger.warn(e);
				}
			} while (false);
		} finally {
			release();
		}
		if (!success) {
			String error = Strings.concat("error storing context: ", this);
			Logger.fatal(error);
			// force removal of this context and notification of all clients
			getServer().getContextCache().ejectContext(this);
		}
		return success;
	}

	/**
	 * Löscht alle Aufträge deren Wunschdatum nicht in der KW des
	 * Context liegt.
	 */
	@SuppressWarnings("unchecked")
	protected boolean deleteMovedAuftraege() {
		boolean result = true;
		obtain();
		try {
			for (int i=modelList.size()-1; i>=0; --i) {
				final AbstractModel model = modelList.get(i);
				if (model instanceof Auftrag) {
					final Auftrag auftrag = (Auftrag) model;
					final long wunschDatum = TimestampUtil.toTimestamp(auftrag.getWunschdatum());

					if (TimestampUtil.isNull(wunschDatum)) {
						continue;
					}
					// delete Auftrag if it is located outside week
					if (!this.intersects(wunschDatum)) {
						LogiwareSaleUpdater.enqeueContextUpdate(wunschDatum);
						auftrag.removeFromWeek();
					}
				}
			}
		} finally {
			release();
		}
		return result;
	}

	public int alterPoints(final int delta) {
		int oldVal, newVal;
		while (true) {
			oldVal = this.points.get();
			newVal = Math.min(oldVal + delta, 10);
			if (oldVal == newVal)
				break;
			if (this.points.compareAndSet(oldVal, newVal))
				break;
		}
		return newVal;
	}

}

package ch.eisenring.dispo.server.network;

import java.util.Collections;
import java.util.Comparator;

import ch.eisenring.core.poi.POIUtil;
import org.apache.poi.ss.usermodel.*;

import ch.eisenring.app.shared.ServiceLocator;
import ch.eisenring.app.shared.ServiceNotFoundException;
import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.collections.Collection;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.Map;
import ch.eisenring.core.collections.Set;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.collections.impl.HashMap;
import ch.eisenring.core.collections.impl.HashSet;
import ch.eisenring.core.csv.CSVFile;
import ch.eisenring.core.csv.CSVLine;
import ch.eisenring.core.datatypes.date.DateGranularityCode;
import ch.eisenring.core.datatypes.date.Period;
import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.core.datatypes.date.format.HEAGDateFormat;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.io.binary.BinaryHolder;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.core.logging.Logger.LogLevel;
import ch.eisenring.core.sort.Sorter;
import ch.eisenring.core.util.file.FileImage;
import ch.eisenring.core.util.file.FileUtil;
import ch.eisenring.dispo.server.DSPServer;
import ch.eisenring.dispo.server.poi.ServerPOIUtil;
import ch.eisenring.dispo.service.codetables.YesNoCode;
import ch.eisenring.dispo.service.model.DSPBasisProjekt;
import ch.eisenring.dispo.shared.network.packets.PacketBasisprojektGetReply;
import ch.eisenring.dispo.shared.network.packets.PacketBasisprojektGetRequest;
import ch.eisenring.dispo.shared.network.packets.PacketObjektuebersichtIDReply;
import ch.eisenring.dispo.shared.network.packets.PacketObjektuebersichtIDRequest;
import ch.eisenring.dms.service.DMSService;
import ch.eisenring.dms.service.DMSServiceException;
import ch.eisenring.dms.service.codetables.DMSDocumentType;
import ch.eisenring.logiware.LWProjektKey;
import ch.eisenring.logiware.code.LWGranitCode;
import ch.eisenring.logiware.code.hard.LWFormularArt;
import ch.eisenring.logiware.code.hard.LWObjektTyp;
import ch.eisenring.logiware.code.hard.LWPlanungStatusCode;
import ch.eisenring.logiware.code.pseudo.AbwicklungsartCode;
import ch.eisenring.logiware.code.service.LogiwareService;
import ch.eisenring.logiware.code.soft.LWGranit1Code;
import ch.eisenring.logiware.code.soft.LWGranit2Code;
import ch.eisenring.logiware.code.soft.LWKundenberaterCode;
import ch.eisenring.logiware.util.LWEndmontageUtil;
import ch.eisenring.lw.condition.Factory;
import ch.eisenring.lw.meta.LWAuftragMeta;
import ch.eisenring.lw.meta.LWFormularMeta;
import ch.eisenring.lw.meta.LWProjektMeta;
import ch.eisenring.lw.model.navigable.LWAuftrag;
import ch.eisenring.lw.model.navigable.LWAuftragKopf;
import ch.eisenring.lw.model.navigable.LWFormular;
import ch.eisenring.lw.model.navigable.LWObjectCache;
import ch.eisenring.lw.model.navigable.LWProjekt;
import ch.eisenring.network.PacketSink;
import ch.eisenring.network.packet.AbstractPacket;
import ch.eisenring.user.shared.codetables.UserTackOnCode;
import ch.eisenring.user.shared.service.USRService;
import ch.eisenring.user.shared.service.UserFacade;
import ch.eisenring.user.shared.service.util.USRCodeCanonicalizer;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFPrintSetup;

final class ObjektuebersichtIDRequestHandler extends AbstractDSPPacketHandler {

	// all activities that are counted
	final static int ACTIVITY_001 = 0;
	final static int ACTIVITY_134 = 1;
	final static int ACTIVITY_135 = 2;
	final static int ACTIVITY_BEM = 3;
	final static int ACTIVITY_175 = 4;
	final static int ACTIVITY_500 = 5;
	final static int ACTIVITY_AB  = 6;
	final static int ACTIVITY_115 = 7;
	final static int ACTIVITY_002 = 8;
	final static int ACTIVITY_132 = 9;
	
	final static int[] ACTIVITY_IDS = {
		ACTIVITY_001, ACTIVITY_002, ACTIVITY_134, ACTIVITY_132, ACTIVITY_135, ACTIVITY_BEM, ACTIVITY_175, ACTIVITY_500, ACTIVITY_AB, ACTIVITY_115
	};

	final static Set<AbwicklungsartCode> ABWICKLUNGSARTEN_KUECHE = Set.asReadonlySet(
			AbwicklungsartCode.AWA001,
			AbwicklungsartCode.AWA135
		);

	static class Pair<X, Y> {
		public final X x;
		public final Y y;
		
		public Pair(X x, Y y) {
			this.x = x;
			this.y = y;
		}
	}

	static class ActivityPoint {
		public final LWKundenberaterCode idCode;
		public final int activityId;
		public final long when;
		public final LWProjektKey basisKey;
		
		public ActivityPoint(final int activityId,
				final LWKundenberaterCode idCode,
				final long when,
				final LWProjektKey basisKey) {
			this.activityId = activityId;
			this.idCode = idCode;
			this.when = when;
			this.basisKey = basisKey;
		}
	}

	static class BasisInfo {
		public final LWProjekt basisProjekt;
		private final List<ActivityPoint> activityPoints = new ArrayList<>();
		public final DSPServer server;
		private boolean objektUebergabe;
		private boolean lwDataLoaded;
		private boolean dmsDataLoaded;
		private boolean dspDataLoaded;
		private Set<LWGranit1Code> granit1 = new HashSet<>(4);
		private Set<LWGranit2Code> granit2 = new HashSet<>(4);
		private boolean werkVertrag;
		private int kuechenOffen;
		
		BasisInfo(final DSPServer server, final LWProjekt basisProjekt) {
			this.server = server;
			this.basisProjekt = basisProjekt;
		}

		public LWObjektTyp getObjektTyp() {
			lwLoadData();
			if (granit2.contains(LWGranit2Code.EIGENTUM)) {
				return LWObjektTyp.EIGENTUM;
			} else if (granit2.contains(LWGranit2Code.MIET)) {
				return LWObjektTyp.MIET;
			}
			return LWObjektTyp.EINZEL;
		}

		private void dmsLoadData() {
			if (dmsDataLoaded)
				return;
			dmsDataLoaded = true;
			final String projektnummer = ((LWProjektKey) basisProjekt.getPK()).getProjektnummer();
			werkVertrag = isDocumentPresent(server, projektnummer, DMSDocumentType.WERKVERTRAG);
		}

		public boolean isWerkvertragPresent() {
			dmsLoadData();
			return werkVertrag;
		}

		public boolean isObjektUebergeben() {
			dspLoadData();
			return objektUebergabe;
		}
	
		private void dspLoadData() {
			if (dspDataLoaded)
				return;
			dspDataLoaded = true;
			final LWProjektKey key = (LWProjektKey) basisProjekt.getPK();
			final PacketBasisprojektGetRequest request = PacketBasisprojektGetRequest.create(key);
			final PacketBasisprojektGetReply reply = BasisprojektGetRequestHandler.handle(server, request);
			if (reply.isValid()) {
				final DSPBasisProjekt dspBasisProjekt = reply.getBasisprojekt(key);
				if (dspBasisProjekt != null) {
					this.objektUebergabe = YesNoCode.YES.equals(dspBasisProjekt.getObjektUebergabe());
				}
			}
		}

		private void lwLoadData() {
			if (lwDataLoaded)
				return;
			try {
				final Collection<LWProjekt> childProjects = basisProjekt.getChildProjekteRecursive();
				for (final LWProjekt child : childProjects) {
					if (child.isStorniert())
						continue;
					final Collection<LWAuftrag> auftraege = child.getAuftraege();
					for (final LWAuftrag auftrag : auftraege) {
						if (auftrag.isStorniert())
							continue;
						final LWAuftragKopf kopf = auftrag.getAuftragKopf();
						if (kopf == null)
							continue;
						final AbwicklungsartCode awa = auftrag.getAbwicklungsart();
						if (ABWICKLUNGSARTEN_KUECHE.contains(awa)) {
							granit1.add(kopf.getGranit1());
							granit2.add(kopf.getGranit2());
							if (AbwicklungsartCode.AWA001.equals(awa)) {
								++kuechenOffen;
							} else if (AbwicklungsartCode.AWA135.equals(awa) && !LWPlanungStatusCode.ABGESCHLOSSEN.equals(kopf.getPlanungStatus())) {
								++kuechenOffen;
							}
						}
					}
				}
				granit1.remove(LWGranit1Code.NULL);
				granit2.remove(LWGranit2Code.NULL);
			} finally {
				lwDataLoaded = true;
			}
		}

		public int getKuechenOffen() {
			lwLoadData();
			return kuechenOffen;
		}

		public String getGranit1() {
			lwLoadData();
			return getShortTextAll(granit1);
		}

		public String getGranit2() {
			lwLoadData();
			return getShortTextAll(granit2);
		}

		private static String getShortTextAll(final Collection<? extends LWGranitCode> codes) {
			final List<LWGranitCode> list = new ArrayList<>(codes);
			Collections.sort(list, AbstractCode.Order.ShortText);
			final StringMaker b = StringMaker.obtain();
			for (final LWGranitCode code : list) {
				b.append(code, AbstractCode.Conversion.ShortText);
			}
			return b.release();
		}

		public int countActivities(final int type, final long when, final LWKundenberaterCode idCode) {
			int result = 0;
			for (final ActivityPoint point : activityPoints) {
				if (point.activityId != type)
					continue;
				if (point.when != when)
					continue;
				if (!AbstractCode.equals(point.idCode, idCode))
					continue;
				++result;
			}
			return result;
		}
	}

	static class ReportContext {
		private CSVFile csvFile;
		private LWObjectCache lwCache;
		private USRCodeCanonicalizer<LWKundenberaterCode> idCanonicalizer;
		int rejectedActivityCount;
		int acceptedActivityCount;
		public final DSPServer server;
		public final Period period;
		public final int kwColumnCount;
		public final int kwColumnMask;
		public int wkvColumn = -1;
		public int firstKWColumn = -1;
		public final List<Pair<LWKundenberaterCode, Integer>> sumLines = new ArrayList<>();
		private final List<ActivityPoint> activityPoints = new ArrayList<>();
		private final Map<Integer, Integer> sumMap = new HashMap<>(32);
		
		public ReportContext(final DSPServer server, final Period period, final boolean columnOption) {
			this.period = period;
			this.server = server;
			this.idCanonicalizer = new USRCodeCanonicalizer<LWKundenberaterCode>(
					LWKundenberaterCode.class, UserTackOnCode.KUNDENBERATER, server);
			this.kwColumnMask = columnOption ? 0b0011_1111_1111 : 0b0000_0001_1111;
			this.kwColumnCount = Integer.bitCount(kwColumnMask);
		}

		public CSVFile getCSVFile() {
			if (csvFile == null) {
				csvFile = new CSVFile();
				addHeader1(csvFile);
			}
			return csvFile;
		}

		public LWObjectCache getLWCache() throws ServiceNotFoundException {
			if (lwCache == null) {
				LogiwareService service = server.locateService(LogiwareService.class);
				lwCache = service.createObjectCache();
				lwCache.setLogLevel(LogLevel.TRACE);
			}
			return lwCache;
		}

		public LWProjektKey getBasisKey(final LWProjektKey projektKey) {
			try {
				final LWProjekt projekt = getLWCache().getObject(projektKey, LWProjekt.class);
				final LWProjekt basis = projekt.getBasisProjekt();
				if (basis != null)
					return (LWProjektKey) basis.getPK();
				return null;
			} catch (final Exception e) {
				return null;
			}
		}

		public void addActivityPoint(final int type, final long when, final LWKundenberaterCode idCode, final LWProjektKey projektKey) {
			LWProjektKey basisKey = null;
			boolean accepted = false;
			final LWKundenberaterCode canonicIdCode = getCanonicId(idCode);
			do {
//				if (AbstractCode.isNull(canonicIdCode))
//				    break;
				if (TimestampUtil.isNull(when))
					break;
				if (!period.intersects(when))
					break;
				basisKey = getBasisKey(projektKey);
				if (basisKey == null)
					break;
				accepted = true;
			} while (false);
			if (accepted) {
				++acceptedActivityCount;
			} else {
				++rejectedActivityCount;
				return;
			}
			// add activity point
			final long monday = TimestampUtil.findMonday(when, 0);
			final ActivityPoint point = new ActivityPoint(type, canonicIdCode, monday, basisKey);
			activityPoints.add(point);
		}

		public List<BasisInfo> getBasisInfos(final LWKundenberaterCode idCode) throws Exception {
			final LWKundenberaterCode canonicIdCode = getCanonicId(idCode);
			final Map<LWProjektKey, BasisInfo> map = new HashMap<>();
			for (final ActivityPoint point : activityPoints) {
				if (canonicIdCode != null && !AbstractCode.equals(point.idCode, canonicIdCode)) {
					continue;
				}
				final LWProjektKey basisKey = point.basisKey;
				BasisInfo info = map.get(basisKey);
				if (info == null) {
					final LWProjekt basisProjekt = getLWCache().getObject(basisKey, LWProjekt.class);
					info = new BasisInfo(server, basisProjekt);
					map.put((LWProjektKey) info.basisProjekt.getPK(), info); 
				}
				info.activityPoints.add(point);
			}
			return new ArrayList<BasisInfo>(map.values());
		}
	
		public void clearSumMap() {
			sumMap.clear();
		}

		public void updateSumMap(final int columnIndex, final Integer value) {
			int inc = (value == null || value.intValue() == 0) ? 0 : value.intValue();
			final Integer key = Integer.valueOf(columnIndex);
			Integer sum = sumMap.get(key);
			if (sum == null) {
				sum = Integer.valueOf(inc);
			} else {
				sum = Integer.valueOf(sum.intValue() + inc);
			}
			sumMap.put(key, sum);
		}
	
		public void addSumLine(final LWKundenberaterCode idCode) {
			// check if all totals are zero
			int totalSum = 0;
			for (final Integer v : sumMap.values()) {
				totalSum += v;
			}
			if (totalSum <= 0)
				return;
			final CSVFile csvFile = getCSVFile();
			csvFile.addLine();
			final CSVLine line = csvFile.addLine();
			line.setColumn(0, "Total");
			for (final Integer column : sumMap.keySet()) {
				final Integer value = sumMap.get(column);
				line.setColumn(column, toCellString(value));
			}
			// keep track of where the sum lines are in the csv
			sumLines.add(new Pair<LWKundenberaterCode, Integer>(idCode, line.getLineNumber()));
		}
	
		public LWKundenberaterCode getCanonicId(final LWKundenberaterCode idCode) {
			return idCanonicalizer.canonicalize(idCode);
		}

		public LWKundenberaterCode getCanonicId(final String sabe) {
			return idCanonicalizer.getCode(sabe);
		}
	
		public String getSabe(final LWKundenberaterCode idCode) {
			return idCanonicalizer.getLoginname(idCode);
		}
	
		/**
		 * Remembers all type separator line indices
		 */
		public Set<Integer> typeLines = new HashSet<>();
		public Set<CSVLine> headerLines = new HashSet<>();
		public Map<CSVLine, Integer> lineHeightMap = new HashMap<>();
		
		public void addHeader1(final CSVFile csvFile) {
			final String title = Strings.concat("Objektübersicht Innendienst, erstellt am ",
					TimestampUtil.DATE10.format(System.currentTimeMillis()));
			final CSVLine line = csvFile.addLine();
			line.setColumn(0, title);
			headerLines.add(line);
		}

		public void addColumnHeaders() {
			final CSVFile csvFile = getCSVFile(); 
			{ // column headers line 1
				final CSVLine line = csvFile.addLine();
				headerLines.add(line);
				lineHeightMap.put(line, 3);
				int column = 0;
				line.setColumn(column++, "Basis / Objekt");
				line.setColumn(column++, "Obj.\nTyp");
				line.setColumn(column++, "Bau-\nArt");
				line.setColumn(column++, "ID");
				line.setColumn(column++, "Verkäufer");
				line.setColumn(column++, "OB");
				if (this.wkvColumn < 0) {
					this.wkvColumn = column;
				}
				line.setColumn(column++, "Werk-\nvertr.");
				line.setColumn(column++, "Objekt-\nÜbergabe");
				line.setColumn(column++, "Anzahl\nKüchen");
				line.setColumn(column++, "Anzahl\nKüchen\noffen");
				if (this.firstKWColumn < 0) {
					this.firstKWColumn = column;
				}
				long kw = period.getBegin();
				column += (kwColumnCount >> 1);
				final HEAGDateFormat dateFormat = HEAGDateFormat.get("\\K\\W\nww");
				do {
					line.setColumn(column, dateFormat, kw);
					column += kwColumnCount;
					kw = TimestampUtil.findMonday(kw, 1);
				} while (kw < period.getEnd());
			}
			{ // column headers line 2
				final CSVLine line = csvFile.addLine();
				headerLines.add(line);
				lineHeightMap.put(line, 2);
				long kw = period.getBegin();
				int column = this.firstKWColumn;
				do {
					if ((kwColumnMask & 0x080) != 0)
						line.setColumn(column++, "ATS\n001");
					if ((kwColumnMask & 0x200) != 0)
						line.setColumn(column++, "ATS\n002");
					if ((kwColumnMask & 0x040) != 0)
						line.setColumn(column++, "TzW\n134");
					if ((kwColumnMask & 0x100) != 0)
						line.setColumn(column++, "KKA\n132");
					if ((kwColumnMask & 0x020) != 0)
						line.setColumn(column++, "WA\n135");
					if ((kwColumnMask & 0x010) != 0)
						line.setColumn(column++, "Bem");
					if ((kwColumnMask & 0x008) != 0)
						line.setColumn(column++, "WO\n175");
					if ((kwColumnMask & 0x004) != 0)
						line.setColumn(column++, "Bes\n500");
					if ((kwColumnMask & 0x002) != 0)
						line.setColumn(column++, "AB");
					if ((kwColumnMask & 0x001) != 0)
						line.setColumn(column++, "StR\n115");
					kw = TimestampUtil.findMonday(kw, 1);
				} while (kw < period.getEnd());
			}
		}

		public void addTypeLine(final LWObjektTyp typ, final LWKundenberaterCode idCode) {
			// add one empty line
			final CSVFile csvFile = getCSVFile();
			csvFile.addLine();
			final CSVLine line = csvFile.addLine();
			if (AbstractCode.isNull(idCode)) {
				line.setColumn(0, typ);
			} else {
				line.setColumn(0, Strings.concat(typ, " ", idCode));
			}
			typeLines.add(line.getLineNumber());
		}
	
		public void addIDLine(final LWKundenberaterCode idCode) {
			final CSVFile csvFile = getCSVFile();
			final CSVLine line = csvFile.addLine();
			headerLines.add(line);
			if (AbstractCode.isNull(idCode)) {
				line.setColumn(0, "Alle Kundenberater");
			} else {
				line.setColumn(0, idCode);
			}
		}
	}

	ObjektuebersichtIDRequestHandler(final DSPServer server) {
		super(server, PacketObjektuebersichtIDRequest.class);
	}

	public void handle(final AbstractPacket abstractPacket, final PacketSink sink) {
		final PacketObjektuebersichtIDRequest request = (PacketObjektuebersichtIDRequest) abstractPacket;
		final PacketObjektuebersichtIDReply reply = handle(server, request);
		sink.sendPacket(reply, false);
	}

	public static PacketObjektuebersichtIDReply handle(final DSPServer server, final PacketObjektuebersichtIDRequest request) {
		try {
			final PacketObjektuebersichtIDReply reply = handleImpl(server, request);
			if (reply != null)
				return reply;
			return PacketObjektuebersichtIDReply.create(request, ErrorMessage.ERROR);
		} catch (final Exception e) {
			Logger.error(e);
			return PacketObjektuebersichtIDReply.create(request, new ErrorMessage(e));
		}
	}

	public static PacketObjektuebersichtIDReply handleImpl(final DSPServer server,
			final PacketObjektuebersichtIDRequest request) throws Exception {
		final Period period = request.getPeriod();
		final List<LWKundenberaterCode> idCodes = new ArrayList<>(request.getKundenberaterCodes());
		final ReportContext reportContext = new ReportContext(server, period, request.getColumnOption());

		//  canonicalize the input codes
		for (int i=0; i<idCodes.size(); ++i) {
			LWKundenberaterCode idCode = idCodes.get(i);
			idCode = reportContext.getCanonicId(idCode);
			idCodes.set(i, idCode);
		}

		{
			final LogLevel logLevel = Logger.setLogLevel(LogLevel.WARN, Logger.SCOPE_THREAD);
			try {
				loadActivities(reportContext);
				Collections.sort(idCodes, LWKundenberaterCode.Order.KeyToString);
				if (idCodes.isEmpty()) {
					createIDSection(reportContext, null);
				} else {
					for (final LWKundenberaterCode idCode : idCodes) {
						createIDSection(reportContext, idCode);				
					}
				}
			} finally {
				Logger.setLogLevel(logLevel, Logger.SCOPE_THREAD);
			}
		}


		final CSVFile csvFile = reportContext.getCSVFile();
		final int totalColumnCount = csvFile.getColumnCount();
		final int totalLineCount = csvFile.getLineCount();
		
		final Workbook workbook = ServerPOIUtil.loadWorkbook(server, "dsp/misc-data/ObjektuebersichtID-Template.xlsx");
		final Sheet sheet1 = workbook.getSheetAt(0);
		POIUtil.clearSheet(sheet1);
		sheet1.getPrintSetup().setLandscape(true);
		sheet1.getPrintSetup().setPaperSize(XSSFPrintSetup.A3_PAPERSIZE);
		sheet1.getPrintSetup().setFitHeight((short) 1);
		sheet1.getPrintSetup().setFitWidth((short) 1);
		sheet1.setAutobreaks(true);
		
		//workbook.setSheetName(0, "Objektübersicht");
		POIUtil.popuplateSheet(sheet1, reportContext.getCSVFile());
		// set line heights
		for (final CSVLine line : reportContext.lineHeightMap.keySet()) {
			final Integer lineHeight = reportContext.lineHeightMap.get(line);
			if (lineHeight != null && lineHeight > 1) {
				final int lineIndex = line.getLineNumber();
				sheet1.getRow(lineIndex).setHeightInPoints(12F * lineHeight);
			}
		}
		
			
		int cWidth = 1567; {
			int column = reportContext.wkvColumn;
			sheet1.setColumnWidth(column++, cWidth);
			sheet1.setColumnWidth(column++, cWidth);
			sheet1.setColumnWidth(column++, cWidth);
			sheet1.setColumnWidth(column++, cWidth);
		}

		final List<Integer> headerLines = new ArrayList<>();
		for (final CSVLine line : reportContext.headerLines) {
			final int index = line.getLineNumber();
			if (index >= 0) {
				headerLines.add(index);
			}
		}
		Sorter.sort(headerLines);

		{ // make non-headers bordered
			for (int i=0; i<totalLineCount; ++i) {
				if (headerLines.contains(i))
					continue;
				POIUtil.addBorder(sheet1, IndexedColors.BLACK.getIndex(), BorderStyle.DASHED, 0, i, totalColumnCount, 1);
			}
		}
		
		{ // color the AbwArt column differently
			long kw = TimestampUtil.findMonday(period.getBegin(), 0);
			int column = reportContext.firstKWColumn;
			do {
				POIUtil.addBorder(sheet1, IndexedColors.BLACK.getIndex(), column, 0, reportContext.kwColumnCount, totalLineCount);
				if (reportContext.kwColumnCount <= 5) {
					POIUtil.setFillForegroundColor(sheet1, IndexedColors.LIGHT_CORNFLOWER_BLUE.getIndex(),
							column + (reportContext.kwColumnCount >> 1), 0, 1, totalLineCount);
				} else if (reportContext.kwColumnCount >= 8) {
					POIUtil.setFillForegroundColor(sheet1, IndexedColors.LIGHT_CORNFLOWER_BLUE.getIndex(),
							column + 4, 0, 1, totalLineCount);
					POIUtil.setFillForegroundColor(sheet1, IndexedColors.GREEN.getIndex(),
							column + 7, 0, 1, totalLineCount);
				}
				column += reportContext.kwColumnCount;
				kw = TimestampUtil.findMonday(kw, 1);
			} while (kw < period.getEnd());
		}

		{ // make headers gray (and bordered)
			headerLines.add(-2);
			int previous = Integer.MIN_VALUE;
			int blockStart = Integer.MIN_VALUE;
			int blockEnd = Integer.MIN_VALUE;
			for (final Integer lineIndex : headerLines) {
				if (lineIndex == previous + 1) {
					blockEnd = lineIndex;
				} else {
					if (blockStart >= 0) {
						final int blockHeight = blockEnd - blockStart + 1;
						POIUtil.setFillForegroundColor(sheet1, IndexedColors.GREY_25_PERCENT.getIndex(), 0, blockStart, totalColumnCount, blockHeight);
						POIUtil.addBorder(sheet1, IndexedColors.BLACK.getIndex(), 0, blockStart, totalColumnCount, blockHeight);
					}
					blockStart = lineIndex;
					blockEnd = lineIndex;
				}
				previous = lineIndex; 
			}
		}
		
		// create the chart data
		addGraphData(server, reportContext, idCodes, workbook);

		//create Innendienst chart data
		addInnendienstAndZusammenfassungGraphData(server, reportContext, workbook, idCodes);
		
		final BinaryHolder filedata = POIUtil.toBinary(workbook);
		String filename = Strings.concat("Objektuebersicht_ID_", System.currentTimeMillis());
		filename = Strings.concat(FileUtil.sanitizeFilename(filename), ".xlsx");
		final FileImage fileimage = FileImage.create(filename, filedata);
		return PacketObjektuebersichtIDReply.create(request, fileimage);
	}

	private static void addGraphData(final DSPServer server,
			final ReportContext reportContext,
			final List<LWKundenberaterCode> idCodes,
			final Workbook workbook) {
		final Sheet sheet1 = workbook.getSheetAt(0);
		final Sheet sheet2 = workbook.getSheetAt(1);
		for (int y=0; y<6; ++y) {
			final int rowIndex = y + 1;
			Pair<LWKundenberaterCode, Integer> location = null;
			if (reportContext.sumLines.size() > y)
				location = reportContext.sumLines.get(y);
			{
				Object value;
				if (location == null) {
					value = Strings.concat("SABE", y);
				} else {
					try {
						final USRService service = server.locateService(USRService.class);
						final UserFacade user = service.getUser(UserTackOnCode.KUNDENBERATER, location.x.getKey());
						value = user == null ? location.x.getKey() : user.getLoginname();
					} catch (Exception e) {
						value = location.x.getKey();
					}
				}
				POIUtil.getOrCreateCell(sheet2, 0, rowIndex).setCellValue(Strings.toString(value));
				final int fy = rowIndex + 1;
				String formula = Strings.concat("SUM(B", fy, ":AE", fy, ") / E11");
				POIUtil.getOrCreateCell(sheet2, 31, rowIndex).setCellFormula(formula);
			}
			{
				long timestamp = reportContext.period.getBegin();
				final HEAGDateFormat f = HEAGDateFormat.get("ww");
				for (int x=1; x<31; ++x) {
					// set KW-number
					POIUtil.getOrCreateCell(sheet2, x, 0).setCellValue(f.format(timestamp));
					// create and set column total formula
					StringMaker b = StringMaker.obtain();
					b.append("SUM(");
					b.append(CSVFile.getExcelColumn(x));
					b.append("2:");
					b.append(CSVFile.getExcelColumn(x));
					b.append("7)");
					POIUtil.getOrCreateCell(sheet2, x, 7).setCellFormula(b.release());
					timestamp = DateGranularityCode.KW.round(timestamp, 1);
				}
			}
			for (int x=0; x<30; ++x) {
				final int sumX0 = reportContext.firstKWColumn + (x * reportContext.kwColumnCount);
				final int sumX1 = sumX0 + reportContext.kwColumnCount - 1;
				if (sumX0 > (255 - reportContext.kwColumnCount))
					continue;
				final int columnIndex = x + 1;
				final Cell cell = POIUtil.getOrCreateCell(sheet2, columnIndex, rowIndex);
				if (location == null) {
					cell.setCellValue("");
				} else {
					StringMaker b = StringMaker.obtain();
					b.append("SUM(");
					b.append(sheet1.getSheetName());
					b.append("!$");
					b.append(CSVFile.getExcelColumn(sumX0));
					b.append("$");
					b.append(location.y + 1);
					b.append(":$");
					b.append(CSVFile.getExcelColumn(sumX1));
					b.append("$");
					b.append(location.y + 1);
					b.append(")");
					cell.setCellValue(0);
					cell.setCellFormula(b.release());
				}
			}
		}
		// Anzahl KW und team grösse berechnen
		{
			long b = DateGranularityCode.KW.round(reportContext.period.getBegin(), 0);
			long e = DateGranularityCode.KW.round(reportContext.period.getEnd(), 0);
			int nkw = Math.abs(TimestampUtil.numberOfCalendarDaysBetween(b, e) / 7);
			POIUtil.getOrCreateCell(sheet2, 4, 10).setCellValue(Math.max(nkw, 1));
			POIUtil.getOrCreateCell(sheet2, 4, 9).setCellValue(Math.max(idCodes.size(), 1));
			POIUtil.getOrCreateCell(sheet2, 31, 7).setCellFormula("SUM(B2:AE7) / (E10 *E11)");
		}
		// formeln für 
	}

	private static void addInnendienstAndZusammenfassungGraphData(final DSPServer server, final ReportContext reportContext,
												final Workbook workbook, final List<LWKundenberaterCode> idCodes) throws Exception {
		if (idCodes.size() == 0) {
			return;
		}
		final USRService service = server.locateService(USRService.class);
		Sheet sheet3 = workbook.getSheetAt(2);
		sheet3.setColumnWidth(0, 6000);
		CSVFile csvFile = reportContext.getCSVFile();
		long kw = reportContext.period.getBegin();
		int weeksCount = 0;
		do {
			weeksCount++;
			kw = TimestampUtil.findMonday(kw, 1);
		} while (kw < reportContext.period.getEnd());

		// Table Auslastung Innendienst
		POIUtil.getOrCreateCell(sheet3, 0, 0).setCellValue("Auslastung Innendienst");
		POIUtil.setFillForegroundColor(sheet3, IndexedColors.GREY_25_PERCENT.getIndex(), 0, 0, weeksCount * idCodes.size() + 1, 1);
		POIUtil.addBorder(sheet3, IndexedColors.BLACK.getIndex(), 0, 0, weeksCount * idCodes.size() + 1, 1);

		Row kalenderWochen = sheet3.createRow(1);
		Row kundenBeraterCodes = sheet3.createRow(2);
		Row row001 = sheet3.createRow(3);
		row001.createCell(0).setCellValue("001");
		Row row135 = sheet3.createRow(4);
		row135.createCell(0).setCellValue("135");
		Row rowBemusterung = sheet3.createRow(5);
		rowBemusterung.createCell(0).setCellValue("Bemusterung");
		Row rowBestellung = sheet3.createRow(6);
		rowBestellung.createCell(0).setCellValue("Bestellung");

		POIUtil.addBorder(sheet3, IndexedColors.BLACK.getIndex(), 0, 1, 1, 6);

		// Table Zusammenfassung
		POIUtil.getOrCreateCell(sheet3, 0, 9).setCellValue("Zusammenfassung");
		POIUtil.setFillForegroundColor(sheet3, IndexedColors.GREY_25_PERCENT.getIndex(), 0, 9, weeksCount + 1, 1);
		POIUtil.addBorder(sheet3, IndexedColors.BLACK.getIndex(), 0, 9, weeksCount + 1, 1);
		POIUtil.getOrCreateCell(sheet3, 0, 10).setCellValue("Anzahl Totale");
		UserFacade user;
		Object value;
		for (LWKundenberaterCode code : idCodes) {
			user = service.getUser(UserTackOnCode.KUNDENBERATER, code.getKey());
			value = user == null ? code.getKey() : user.getLoginname();
			POIUtil.getOrCreateCell(sheet3, 0, 10 + idCodes.indexOf(code) + 1).setCellValue(value.toString());
		}
		POIUtil.getOrCreateCell(sheet3, 0, 10 + idCodes.size() + 1).setCellValue("Total Team");
		POIUtil.addBorder(sheet3, IndexedColors.BLACK.getIndex(), 0, 10, 1, 1);
		POIUtil.addBorder(sheet3, IndexedColors.BLACK.getIndex(), 0, 10, 1, idCodes.size() + 2);

		// Fill tables
		final HEAGDateFormat dateFormat = HEAGDateFormat.get("\\K\\Www");
		int kWColumnIndexAuslastungInnendienst = 1;
		int kWColumnIndexZusammenfassung = 1;
		int kundenBeraterColumnIndex = 1;
		int csvFileKWFirstColumnIndex = reportContext.firstKWColumn;
		kw = reportContext.period.getBegin();
		int lineNumberSumLine = 0;
		do {
			int totalTeam = 0;
			final String kalenderWoche = dateFormat.format(kw);
			POIUtil.getOrCreateCell(sheet3, kWColumnIndexAuslastungInnendienst, kalenderWochen.getRowNum()).setCellValue(kalenderWoche);
			POIUtil.addBorder(sheet3, IndexedColors.BLACK.getIndex(), kWColumnIndexAuslastungInnendienst, kalenderWochen.getRowNum(), idCodes.size(), 1);
			POIUtil.addBorder(sheet3, IndexedColors.BLACK.getIndex(), kWColumnIndexAuslastungInnendienst, row001.getRowNum(), idCodes.size(), 4);
			sheet3.addMergedRegion(new CellRangeAddress(kalenderWochen.getRowNum(), kalenderWochen.getRowNum(), kWColumnIndexAuslastungInnendienst,
					kWColumnIndexAuslastungInnendienst + idCodes.size() - 1));
			POIUtil.getOrCreateCell(sheet3, kWColumnIndexZusammenfassung, 10).setCellValue(kalenderWoche);

			for (LWKundenberaterCode code : idCodes) {
				int totalKundenBerater = 0;
				for (Pair<LWKundenberaterCode, Integer> sumLine : reportContext.sumLines) {
					if (sumLine.x.equals(code)) {
						lineNumberSumLine = sumLine.y;
						break;
					}
				}
				if (lineNumberSumLine == 0) { // no data available to evaluate for this KundenBeraterCode in the selected period
					break;
				}

				// set the cell values for the respective KundenBeraterCode and kalenderWoche in table "Aulastung Innendienst"
				user = service.getUser(UserTackOnCode.KUNDENBERATER, code.getKey());
				value = user == null ? code.getKey() : user.getLoginname();
				POIUtil.getOrCreateCell(sheet3, kundenBeraterColumnIndex, kundenBeraterCodes.getRowNum()).setCellValue(value.toString());
				POIUtil.addBorder(sheet3, IndexedColors.BLACK.getIndex(), kundenBeraterColumnIndex, kundenBeraterCodes.getRowNum(), 1, 1);

				Cell cell001 = POIUtil.getOrCreateCell(sheet3, kundenBeraterColumnIndex, row001.getRowNum());
				if (csvFile.getLine(lineNumberSumLine).getColumn(csvFileKWFirstColumnIndex).isEmpty()) {
					cell001.setCellValue(0);
				} else {
					cell001.setCellValue(Integer.valueOf(csvFile.getLine(lineNumberSumLine).getColumn(csvFileKWFirstColumnIndex)));
				}

				Cell cell135 = POIUtil.getOrCreateCell(sheet3, kundenBeraterColumnIndex, row135.getRowNum());
				if (csvFile.getLine(lineNumberSumLine).getColumn(csvFileKWFirstColumnIndex + 4).isEmpty()){
					cell135.setCellValue(0);
				} else {
					cell135.setCellValue(Integer.valueOf(csvFile.getLine(lineNumberSumLine).getColumn(csvFileKWFirstColumnIndex + 4)));
				}

				Cell cellBemusterung = POIUtil.getOrCreateCell(sheet3, kundenBeraterColumnIndex, rowBemusterung.getRowNum());
				if (csvFile.getLine(lineNumberSumLine).getColumn(csvFileKWFirstColumnIndex + 5).isEmpty()) {
					cellBemusterung.setCellValue(0);
				} else {
					cellBemusterung.setCellValue(Integer.valueOf(csvFile.getLine(lineNumberSumLine).getColumn(csvFileKWFirstColumnIndex + 5)));
				}

				Cell cellBestellung = POIUtil.getOrCreateCell(sheet3, kundenBeraterColumnIndex, rowBestellung.getRowNum());
				if (csvFile.getLine(lineNumberSumLine).getColumn(csvFileKWFirstColumnIndex + 7).isEmpty()) {
					cellBestellung.setCellValue(0);
				} else {
					cellBestellung.setCellValue(Integer.valueOf(csvFile.getLine(lineNumberSumLine).getColumn(csvFileKWFirstColumnIndex + 7)));
				}

				// calculate total of each KundenBeraterCode and set value in table "Zusammenfassung"
				totalKundenBerater += cell001.getNumericCellValue() + cell135.getNumericCellValue()
							+ cellBemusterung.getNumericCellValue() + cellBestellung.getNumericCellValue();
				POIUtil.getOrCreateCell(sheet3, kWColumnIndexZusammenfassung, 10 + idCodes.indexOf(code) + 1).setCellValue(totalKundenBerater);

				totalTeam += totalKundenBerater;
				kundenBeraterColumnIndex++;
			}
			POIUtil.getOrCreateCell(sheet3, kWColumnIndexZusammenfassung, 10 + idCodes.size() + 1).setCellValue(totalTeam);
			POIUtil.addBorder(sheet3, IndexedColors.BLACK.getIndex(), kWColumnIndexZusammenfassung, 10, 1, 1);
			POIUtil.addBorder(sheet3, IndexedColors.BLACK.getIndex(), kWColumnIndexZusammenfassung, 10, 1, idCodes.size() + 2);
			kWColumnIndexAuslastungInnendienst += idCodes.size();
			kWColumnIndexZusammenfassung++;
			csvFileKWFirstColumnIndex += 10;
			weeksCount++;
			kw = TimestampUtil.findMonday(kw, 1);
		} while(kw < reportContext.period.getEnd());

	}

	private final static Object[] FORMULAR_ARTEN = {
		LWFormularArt.F101, // Statusrapport
		LWFormularArt.F201, // Auftragsbestätigung
		LWFormularArt.F206, // Offerte
		LWFormularArt.F220 // Bestellung
	};

	private final static Object[] ABWICKLUNGSARTEN = {
		AbwicklungsartCode.AWA001,
		AbwicklungsartCode.AWA002,
		AbwicklungsartCode.AWA132,
		AbwicklungsartCode.AWA134,
		AbwicklungsartCode.AWA135
	};

	private final static Comparator<BasisInfo> ORDER_BASISINFO = new Comparator<BasisInfo>() {
		@Override
		public int compare(final BasisInfo o1, final BasisInfo o2) {
			return Strings.compare(o1.basisProjekt.getProjektnummer(), o2.basisProjekt.getProjektnummer());
		}
	};
	
	// id code is canonic
	static void createIDSection(final ReportContext reportContext, final LWKundenberaterCode idCode) throws Exception {
		final List<BasisInfo> basisInfos = reportContext.getBasisInfos(idCode);
		reportContext.clearSumMap();
		basisInfos.sort(ORDER_BASISINFO);
		reportContext.addColumnHeaders();
		reportContext.addIDLine(idCode);
		createLines(reportContext, basisInfos, idCode);
		reportContext.addSumLine(idCode);
	}

	static void createLines(final ReportContext reportContext, final Collection<BasisInfo> basisInfos, final LWKundenberaterCode idCode) throws Exception {
		final CSVFile csvFile = reportContext.getCSVFile();
		final LWObjektTyp[] objektTypen = { LWObjektTyp.EIGENTUM, LWObjektTyp.MIET, LWObjektTyp.EINZEL };
		for (final LWObjektTyp objektTyp : objektTypen) {
			int typCount = 0;
			for (final BasisInfo basisInfo : basisInfos) {
				if (!AbstractCode.equals(basisInfo.getObjektTyp(), objektTyp))
					continue;
				++typCount;
				if (typCount == 1) {
					csvFile.addLine();
					final CSVLine typLine = csvFile.addLine();
					typLine.setColumn(0, objektTyp);
					reportContext.headerLines.add(typLine);
				}
				int column = reportContext.firstKWColumn;
				final CSVLine line = csvFile.addLine();
				line.setColumn(0, Strings.concat(basisInfo.basisProjekt.getProjektnummer(), " ",
						Strings.toSingleLine(basisInfo.basisProjekt.getProjektbezeichnung())));
				line.setColumn(1, basisInfo.getGranit2());
				line.setColumn(2, basisInfo.getGranit1());
				line.setColumn(3, reportContext.getSabe(basisInfo.basisProjekt.getKundenberater()));
				line.setColumn(4, Strings.toString(basisInfo.basisProjekt.getVerkaueferSubjektId()));
				line.setColumn(5, AbstractCode.Conversion.ShortText, basisInfo.basisProjekt.getObjektbetreuerCode());
				line.setColumn(6, basisInfo.isWerkvertragPresent() ? "Ja" : "-");
				line.setColumn(7, basisInfo.isObjektUebergeben() ? "Ja" : "-");
				line.setColumn(8, basisInfo.basisProjekt.getObjektkuechenAnzahl());
				line.setColumn(9, basisInfo.getKuechenOffen());
				long kw = reportContext.period.getBegin();
				do {
					int bitMask = 1 << (ACTIVITY_IDS.length - 1);
					for (int i=0; i<ACTIVITY_IDS.length; ++i, bitMask >>= 1) {
						// only show the value if the column is visible
						if ((reportContext.kwColumnMask & bitMask) != 0) {
							final int count = basisInfo.countActivities(ACTIVITY_IDS[i], kw, idCode);
							reportContext.updateSumMap(column, count);
							line.setColumn(column++, toCellString(count));
						}
					}
					kw = DateGranularityCode.KW.round(kw, 1);
				} while (kw < reportContext.period.getEnd());
			}
		}
	}
	
	static String toCellString(final int value) {
		if (value <= 0)
			return null;
		return Strings.toString(value);
	}

	static Collection<LWAuftrag> loadFormulare(final ReportContext reportContext) throws Exception {
		Logger.debug("loadActivities:loadFormulare.start");
		try {
			final LWObjectCache lwCache = reportContext.getLWCache();
			final Collection<LWAuftrag> auftraege = lwCache.load(LWAuftrag.class, LWFormular.class,
					Factory.and(
						Factory.in(LWFormularMeta.ATR_FORMULARART, FORMULAR_ARTEN),
						Factory.inRange(LWFormularMeta.ATR_ADD_DATE, reportContext.period),
						Factory.eq(LWFormularMeta.ATR_AUFTRAGNUMMER, LWAuftragMeta.ATR_AUFTRAGNUMMER),
						Factory.eq(LWFormularMeta.ATR_GSE_FORMULAR, LWAuftragMeta.ATR_GSE_AUFTRAG)
					)
			);
			// 2015-03-16: Convert result list to set to remove duplicate returns and filter out "Storno"
			final Set<LWAuftrag> set = new HashSet<>();
			for (final LWAuftrag auftrag : auftraege) {
				if (auftrag.isStorniert())
					continue;
				set.add(auftrag);
			}
			return set;
		} finally {
			Logger.debug("loadActivities:loadFormulare.end");
		}
	}

	static Collection<LWAuftrag> loadAuftraege(final ReportContext reportContext) throws Exception {
		Logger.debug("loadActivities:loadAuftraege.start");
		try {
			final LWObjectCache lwCache = reportContext.getLWCache();
			final Collection<LWAuftrag> auftraege = lwCache.load(LWAuftrag.class, LWProjekt.class,
					Factory.and(
						Factory.in(LWAuftragMeta.ATR_ABWICKLUNGSART, ABWICKLUNGSARTEN),
						Factory.inRange(LWAuftragMeta.ATR_WUNSCHDATUM, reportContext.period),
						Factory.eq(LWAuftragMeta.ATR_PROJEKTNUMMER, LWProjektMeta.ATR_PROJEKTNUMMER),
						Factory.eq(LWAuftragMeta.ATR_GSE_PROJEKT, LWProjektMeta.ATR_GSE_PROJEKT)
					)
			);
			return auftraege;
		} finally {
			Logger.debug("loadActivities:loadAuftraege.end");
		}
	}

	static Collection<LWProjekt> loadProjekte(final ReportContext reportContext) throws Exception {
		Logger.debug("loadActivities:loadProjekte.start");
		try {
			final LWObjectCache lwCache = reportContext.getLWCache();
			final Collection<LWProjekt> projekte = lwCache.load(LWProjekt.class,
					Factory.or(
							Factory.inRange(LWProjektMeta.ATR_BEMUSTERUNG1, reportContext.period),
							Factory.inRange(LWProjektMeta.ATR_BEMUSTERUNG2, reportContext.period),
							Factory.inRange(LWProjektMeta.ATR_BEMUSTERUNG3, reportContext.period)
					)
			);
			return projekte;
		} finally {
			Logger.debug("loadActivities:loadProjekte.end");
		}
	}

	private final static Map<AbwicklungsartCode, Integer> AWA2ACTIVITY = Map.of(
			AbwicklungsartCode.AWA001, ACTIVITY_001,
			AbwicklungsartCode.AWA134, ACTIVITY_134,
			AbwicklungsartCode.AWA135, ACTIVITY_135,
			AbwicklungsartCode.AWA002, ACTIVITY_002,
			AbwicklungsartCode.AWA132, ACTIVITY_132
		);

	public static void loadActivities(final ReportContext reportContext) throws Exception {
		{ // --- bemusterungen --------------------------------------
			final Collection<LWProjekt> projekte = loadProjekte(reportContext);
			for (final LWProjekt projekt : projekte) {
				if (projekt.isStorniert())
					continue;
				final LWProjektKey basisKey = (LWProjektKey) projekt.getPK();
				final LWKundenberaterCode idCode = projekt.getKundenberater();
				reportContext.addActivityPoint(ACTIVITY_BEM, projekt.getBemusterung1(), idCode, basisKey);
				reportContext.addActivityPoint(ACTIVITY_BEM, projekt.getBemusterung2(), idCode, basisKey);
				reportContext.addActivityPoint(ACTIVITY_BEM, projekt.getBemusterung3(), idCode, basisKey);
			}
		}
		
		{ // --- aufträge -------------------------------------------
			final Collection<LWAuftrag> auftraege = loadAuftraege(reportContext);
			for (final LWAuftrag auftrag : auftraege) {
				if (auftrag.isStorniert())
					continue;
				final LWKundenberaterCode idCode = auftrag.getKundenberater();
				final AbwicklungsartCode awa = auftrag.getAbwicklungsart();
				final LWProjektKey basisKey = reportContext.getBasisKey(auftrag.getProjektKey());
				final Integer activityId = AWA2ACTIVITY.get(awa);
				if (activityId != null) {
					reportContext.addActivityPoint(activityId, auftrag.getWunschDatum(), idCode, basisKey);
				}
			}
		}
		
		{ // --- formulare ------------------------------------------
			final Collection<LWAuftrag> auftraege = loadFormulare(reportContext);
			final Set<String> bestellungenGezaehlt = new HashSet<>();
			
			// alle auftraege die ein auszuwertendes formular tragen
			for (final LWAuftrag auftrag : auftraege) {
				final LWProjektKey basisKey = auftrag.getProjektKey();
				if (basisKey == null)
					continue;
				final List<LWFormular> formulare = new ArrayList<>(auftrag.getFormulare());
				Sorter.sort(formulare, LWFormular.Order.FormularDatum);
				
				{ // Bestellungen
					final Collection<LWFormular> bestellungen = filter(formulare, LWFormularArt.F220, AbwicklungsartCode.AWA500);
					if (!bestellungen.isEmpty()) {
						final String projektNummer = auftrag.getProjektnummer();
						if (bestellungenGezaehlt.add(projektNummer)) {
							// nur eine bestellung pro projektnummer zählt!
							final LWFormular formular = bestellungen.iterator().next();
							final LWKundenberaterCode idCode = reportContext.getCanonicId(formular.getAddSaBe());
							reportContext.addActivityPoint(ACTIVITY_500, formular.getFormularDatum(), idCode, basisKey);
						}
					}
				}

				{ // Offerten
					final Collection<LWFormular> offerten = filter(formulare, LWFormularArt.F206, AbwicklungsartCode.AWA175);
					long lastDay = TimestampUtil.NULL_TIMESTAMP;
					for (final LWFormular formular : offerten) {
						final long formularDay = DateGranularityCode.DAY.round(formular.getFormularDatum(), 0);
						if (lastDay != formularDay) {
							lastDay = formularDay;
							final LWKundenberaterCode idCode = reportContext.getCanonicId(formular.getAddSaBe());
							reportContext.addActivityPoint(ACTIVITY_175, formularDay, idCode, basisKey);
						}
					}
				}

				{ // Auftragsbestätigungen AB
					final Collection<LWFormular> auftragsBestaetigungen = filter(formulare, LWFormularArt.F201, AbwicklungsartCode.AWA135);
					long lastDay = TimestampUtil.NULL_TIMESTAMP;
					for (final LWFormular formular : auftragsBestaetigungen) {
						final long formularDay = DateGranularityCode.DAY.round(formular.getFormularDatum(), 0);
						if (lastDay != formularDay) {
							lastDay = formularDay;
							final LWKundenberaterCode idCode = reportContext.getCanonicId(formular.getAddSaBe());
							reportContext.addActivityPoint(ACTIVITY_AB, formularDay, idCode, basisKey);
						}
					}
				}
				
				{ // Statusrapporte
					final Collection<LWFormular> statusRapporte = filter(formulare, LWEndmontageUtil.EM_FORMULARART, LWEndmontageUtil.EM_ABWICKLUNGSART);
					long lastDay = TimestampUtil.NULL_TIMESTAMP;
					for (final LWFormular formular : statusRapporte) {
						final long formularDay = LWEndmontageUtil.getCanonicalEMDate(formular.getFormularDatum());
						if (lastDay != formularDay) {
							lastDay = formularDay;
							final LWKundenberaterCode idCode = reportContext.getCanonicId(formular.getAddSaBe());
							reportContext.addActivityPoint(ACTIVITY_115, formularDay, idCode, basisKey);
						}
					}
				}
			}
		}
		Logger.info("Activities Accepted: {}, Rejected: {}",
				reportContext.acceptedActivityCount, reportContext.rejectedActivityCount);
	}

	/**
	 * Filter collection of LWFormular by LWFormularArt and AbwicklungsArt of Auftrag, return Collection of matches.
	 */
	private static Collection<LWFormular> filter(
			final Collection<LWFormular> formulare, 
			final LWFormularArt formularArt,
			final AbwicklungsartCode abwicklungsArt) {
		final List<LWFormular> result = new ArrayList<>();
		for (final LWFormular formular : formulare) {
			do {
				if (formular.isStorniert())
					break;
				if (!AbstractCode.equals(formular.getFormularArt(), formularArt))
					break;
				try {
					final LWAuftrag auftrag = formular.getAuftrag();
					if (auftrag == null)
						break;
					if (!AbstractCode.equals(auftrag.getAbwicklungsart(), abwicklungsArt))
						break;
					result.add(formular);
				} catch (final Exception e) {
					break;
				}
			} while (false);
		}
		return result;
	}

	// --------------------------------------------------------------
	// ---
	// --- Look up document(s) in DMS
	// ---
	// --------------------------------------------------------------
	public static boolean isDocumentPresent(final ServiceLocator server, final String projectNumber, final DMSDocumentType documentType) {
		final DMSService service;
		try {
			service = server.locateService(DMSService.class);
		} catch (final ServiceNotFoundException e) {
			return false;
		}
		try {
			final List<?> list = service.getDocumentImages(projectNumber, documentType);
			return !list.isEmpty();
		} catch (final DMSServiceException e) {
			// ignore
			return false;
		}
	}

}

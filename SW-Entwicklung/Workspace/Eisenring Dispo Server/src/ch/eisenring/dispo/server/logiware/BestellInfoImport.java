package ch.eisenring.dispo.server.logiware;

import java.sql.SQLException;

import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.Set;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.dispo.shared.lw.LWBestellInfo;
import ch.eisenring.dispo.shared.lw.LWBestellInfoImpl;
import ch.eisenring.dispo.shared.lw.LWBestellInfoMeta;
import ch.eisenring.jdbc.StatementParameters;
import ch.eisenring.logiware.LWContextSource;
import ch.eisenring.logiware.code.pseudo.AbwicklungsartCode;
import ch.eisenring.lw.LWConstants;
import ch.eisenring.model.TransactionContext;
import ch.eisenring.model.TransactionSource;
import ch.eisenring.model.engine.jdbc.JDBCObjectMapper;
import ch.eisenring.model.engine.jdbc.SingleTableMapping;
import ch.eisenring.model.engine.jdbc.TableMapping;
import ch.eisenring.model.mapping.ObjectMapping;
import ch.eisenring.model.mapping.POJOMapping;

public class BestellInfoImport {

	private BestellInfoImport() {
	}

	// --------------------------------------------------------------
	// ---
	// --- Mapping
	// ---
	// --------------------------------------------------------------
	public final static SingleTableMapping TM_BESTELLINFO =
			TableMapping.get(LWBestellInfoMeta.METACLASS, LWBestellInfoMeta.TABLE);
	public final static ObjectMapping<LWBestellInfoImpl> PM_BESTELLINFO =
			POJOMapping.get(LWBestellInfoMeta.METACLASS, LWBestellInfoImpl.class);
	public final static JDBCObjectMapper<LWBestellInfoImpl> OM_BESTELLINFO =
			JDBCObjectMapper.get(TM_BESTELLINFO, PM_BESTELLINFO); 

	// --------------------------------------------------------------
	// ---
	// --- Object loading
	// ---
	// --------------------------------------------------------------
	private final static Set<AbwicklungsartCode> AWA_BESTELLINFO = Set.asReadonlySet(
			AbwicklungsartCode.AWA500, AbwicklungsartCode.AWA505);

	/**
	 * Load BestellInfos for collection of Projektnummer
	 */
	@SuppressWarnings("unchecked")
	public static List<LWBestellInfo> loadInfos(final java.util.Collection<String> projektNummern) throws Exception {
		final List<LWBestellInfo> result = new ArrayList<>();
		if (projektNummern == null || projektNummern.isEmpty())
			return result;

		final TransactionSource source = new LWContextSource(LWConstants.VERTRIEB) {
			@Override
			protected void loadImpl(TransactionContext context) throws SQLException {
				final StringMaker sql = StringMaker.obtain();
				final StatementParameters params = new StatementParameters();

				sql.append("SELECT ");
				sql.append("  A.Pauftrnr AS ");
				sql.append(TM_BESTELLINFO.getColumn(LWBestellInfoMeta.ATR_PROJEKTNUMMER));
				sql.append(", A.Id_GSE_PA AS ");
				sql.append(TM_BESTELLINFO.getColumn(LWBestellInfoMeta.ATR_GSEPROJEKT));
				sql.append(", A.Auftrnr AS ");
				sql.append(TM_BESTELLINFO.getColumn(LWBestellInfoMeta.ATR_AUFTRAGNUMMER));
				sql.append(", A.Id_GSE AS ");
				sql.append(TM_BESTELLINFO.getColumn(LWBestellInfoMeta.ATR_GSEAUFTRAG));
				sql.append(", A.Name_PLZ_Ort AS ");
				sql.append(TM_BESTELLINFO.getColumn(LWBestellInfoMeta.ATR_LIEFERANTENBEZEICHNUNG));
				sql.append(", A.BestellNr AS ");
				sql.append(TM_BESTELLINFO.getColumn(LWBestellInfoMeta.ATR_BESTELLNUMMER));
				sql.append(", A.DatWu AS ");
				sql.append(TM_BESTELLINFO.getColumn(LWBestellInfoMeta.ATR_WUNSCHDATUM));
			    sql.append(", CASE " +
			                 "WHEN (A.MLiefadr IS NOT NULL) THEN 1 " + 		// abw. lieferadresse
			                 "WHEN (dbo.HEAG_IstSubjektBaustelle(A.id_Subjekt_L) = 0) THEN 0 " +
			               "ELSE 1 " +
			               "END AS ");
			    sql.append(TM_BESTELLINFO.getColumn(LWBestellInfoMeta.ATR_BAUSTELLENLIEFERUNG));
			    sql.append(", P.PBez AS ");
			    sql.append(TM_BESTELLINFO.getColumn(LWBestellInfoMeta.ATR_PROJEKTBEZEICHNUNG));
			    sql.append(", A.id_Subjekt_L AS ");
			    sql.append(TM_BESTELLINFO.getColumn(LWBestellInfoMeta.ATR_LIEFERSUBJEKTID));
			    sql.append(", A.AbwArt AS ");
			    sql.append(TM_BESTELLINFO.getColumn(LWBestellInfoMeta.ATR_ABWICKLUNGSART));
			    sql.append(", CASE " +
			                 "WHEN (K.KOK = '000') THEN K.DatWu " +
			                 "WHEN (K.KOK = '001') THEN K.DatWu " +
			               "ELSE NULL " +
			               "END AS ");
			    sql.append(TM_BESTELLINFO.getColumn(LWBestellInfoMeta.ATR_KOKDATUM));
			    sql.append(" FROM AUFTRAG A " + WITH_NOLOCK);
			    sql.append(" LEFT JOIN PAUFTRAG P " + WITH_NOLOCK + " ON (P.PAuftrNr = A.PAuftrNr AND P.Id_GSE_PA = A.Id_GSE_PA)");
			    sql.append(" LEFT JOIN HE_LBestellWDatum K " + WITH_NOLOCK + " ON (A.AuftrNr = K.AuftrNr AND A.Id_GSE = K.ID_GSE)");
			    sql.append(" WHERE "); 
			    sql.append("A.Pauftrnr IN ");
			    prepareIn(sql, projektNummern);
			    params.addAll(projektNummern);
			    sql.append(" AND A.AbwArt IN ");
			    prepareIn(sql, AWA_BESTELLINFO);
			    params.addAll(AWA_BESTELLINFO);
			    sql.append(" AND A.Id_GSE = '2500'");
			    sql.append(" AND A.Status != '099'");
			    sql.append(" AND A.AbwTyp = '002'");
			    doQuery(sql.release(), params, (resultSet) ->
			    	{	final LWBestellInfo info = OM_BESTELLINFO.loadRow(context, resultSet);
			    		result.add(info); });
			}
		}; 
		
		final TransactionContext context = new TransactionContext();
		context.load(source);
		return result;
	}

}

package ch.eisenring.dispo.server.service.statistics;

import static ch.eisenring.dispo.server.DSPServerConstants.STATE_RCFG_STAT_KW_MINUS_3;
import static ch.eisenring.dispo.shared.codetables.EmploymentRole.MITARBEITER_MONTEUR;
import static ch.eisenring.dispo.shared.codetables.ZuordnungCode.ZUORDNUNG_SUPERVISOR;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.Date;
import java.util.Iterator;

import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.collections.Collection;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.Map;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.collections.impl.HashMap;
import ch.eisenring.core.datatypes.date.DateGranularityCode;
import ch.eisenring.core.datatypes.date.DateUtil;
import ch.eisenring.core.datatypes.date.Period;
import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.iterators.Filter;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.core.logging.Logger.LogLevel;
import ch.eisenring.dispo.server.DSPServer;
import ch.eisenring.dispo.server.auftragbewertung.AuftragBewertungAccessorLW;
import ch.eisenring.dispo.server.auftragbewertung.AuftragBewertungType;
import ch.eisenring.dispo.shared.model.AbstractModel;
import ch.eisenring.dispo.shared.model.MasterMitarbeiter;
import ch.eisenring.dispo.shared.model.ModelContext;
import ch.eisenring.jdbc.JDBCResultSet;
import ch.eisenring.logiware.code.pseudo.AbwicklungsartCode;
import ch.eisenring.logiware.code.service.LogiwareService;
import ch.eisenring.logiware.code.soft.GSECode;
import ch.eisenring.logiware.code.soft.LWAuftragStatusCode;
import ch.eisenring.logiware.code.soft.LWGranit2Code;
import ch.eisenring.lw.condition.Factory;
import ch.eisenring.lw.meta.LWAuftragKopfMeta;
import ch.eisenring.lw.meta.LWAuftragMeta;
import ch.eisenring.lw.model.navigable.LWAuftrag;
import ch.eisenring.lw.model.navigable.LWAuftragKopf;
import ch.eisenring.lw.model.navigable.LWObjectCache;
import ch.eisenring.presento.server.api.PresentoCache;
import ch.eisenring.presento.server.api.PresentoPerson;
import ch.eisenring.statistics.shared.codetables.STDateGranularityCode;
import ch.eisenring.statistics.shared.codetables.StatisticTypeCode;
import ch.eisenring.statistics.shared.data.DataPoint;
import ch.eisenring.statistics.shared.network.PacketStatisticRequest;
import ch.eisenring.statistics.shared.network.PacketStatisticResponse;

public class AuslastungKW extends StatisticsBase {

	private final static AbwicklungsartCode[] ABWICKLUNGSARTEN = {
		AbwicklungsartCode.AWA001,
		AbwicklungsartCode.AWA002,
		AbwicklungsartCode.AWA115,
		AbwicklungsartCode.AWA132,
		AbwicklungsartCode.AWA133,
		AbwicklungsartCode.AWA134,
		AbwicklungsartCode.AWA135
	};

	private final static Map<AbwicklungsartCode, AbwicklungsartCode> AWA_REMAP = Map.of(
			AbwicklungsartCode.AWA002, AbwicklungsartCode.AWA001,
			AbwicklungsartCode.AWA132, AbwicklungsartCode.AWA134
		);

	private final static int OFFSET_VORWOCHE_KW = 3;
	
	public final static String LABEL_BOOKED_COUNT = "Gebucht (Gezählt)\n";
	public final static String LABEL_BOOKED_WEIGHT = "Gebucht (Gewichtet)\n";
	
	private AuslastungKWAuftragBewertung bewertungKW_0;
	private AuslastungKWAuftragBewertung bewertungKW_3;
	
	private boolean stacked = true;
	private Date kwFrom;
	private Date kwUpto;
	private Period kwRange;
	
	public AuslastungKW(final DSPServer server) {
		super(server);
	}

	@Override
	public PacketStatisticResponse createResponse(final PacketStatisticRequest packet) throws Exception {
		this.bewertungKW_0 = AuslastungKWAuftragBewertung.create(server, packet.getType());
		this.bewertungKW_3 = AuslastungKWAuftragBewertung.create(server, packet.getType(), STATE_RCFG_STAT_KW_MINUS_3);

		this.kwRange = packet.getDateRange(DateGranularityCode.KW, true);
		
		this.kwFrom = getKWStartDatumNotNull(packet.getDateFrom());
		this.kwUpto = getKWEndDatumNotNull(packet.getDateUpto());
		Logger.trace("Abfrage-Zeitraum[1]:  " + TimestampUtil.SQLDATETIME.format(kwFrom) + " " + TimestampUtil.SQLDATETIME.format(kwUpto));			
		Logger.trace("Abfrage-Zeitraum[2]:  " + kwRange);			
		
		final List<DataPoint> dataList = new ArrayList<>();
		
		PacketStatisticResponse result = null;
		Connection connection;

		// --- Step 1: Finde alle Aufträge im Zeitraum (aus LOGI)
		final List<LWAuftrag> auftraege = getAuftraege(kwRange);

		// --- Laden aller FeinZuordnungen vom Typ Begleitung im Zeitraum
		final List<StatisticFeinZuordnung> feinZuordnungList = new ArrayList<>();
		connection = connectToDispo();
		if (connection != null) {
			try {
			    final String sql = createQueryDispo(packet);
			    final JDBCResultSet resultSet = doFetchQuery(sql);
				while (resultSet.next()) {
					final StatisticFeinZuordnung feinZuordnung = new StatisticFeinZuordnung();
					feinZuordnung.loadRow(resultSet);
					feinZuordnungList.add(feinZuordnung);
				}
			} catch (final SQLException e) {
				Logger.log(LogLevel.ERROR, e);
				feinZuordnungList.clear();
			} finally {
				close();
			}
		}
		
		
		//dataList.addAll(calculateBedarf(auftragList2, packet, AuftragBewertungType.FA15));
		dataList.addAll(calculateBedarf(auftraege, packet, AuftragBewertungType.FA15));
		dataList.addAll(calculateBegleitung(feinZuordnungList, packet, AuftragBewertungType.FA15));
		//dataList.addAll(calculateBedarf(auftragList2, packet, AuftragBewertungType.FA_ANZAHL));
		dataList.addAll(calculateBedarf(auftraege, packet, AuftragBewertungType.FA_ANZAHL));
		dataList.addAll(calculateBegleitung(feinZuordnungList, packet, AuftragBewertungType.FA_ANZAHL));

		calculateGebucht(packet, dataList);
		smartSort(dataList);
		
		result = PacketStatisticResponse.create(packet);

		// --- chart title
		String title = STDateGranularityCode.getDateRangeText(result);
		title = Strings.concat(packet.getType(), "\n(", title, ")");
		result.setChartTitle(title);

		// --- values
		double[] data = getValues(dataList);
		DataPoint.round(data);
		result.setDataSeries(new double[][] { data });

		// --- labels
		String[] labels = getLabels(dataList);
		result.setLabels(labels);
		result.setXAxisLabel("Wert (100 = 1 Personenwoche)");
		result.setYAxisLabel("Kalenderwoche");
		
		return result;
	}

	/**
	 * Convert DataMiningType -> GSEIdType
	 */
	public static GSECode toGSE(final StatisticTypeCode type) {
		return (GSECode) type.getSubType1();
	}
	
	private List<LWAuftrag> getAuftraege(final Period kwRange) throws Exception {
		final List<LWAuftrag> result;
		long from = kwRange.getBegin();
		from = DateGranularityCode.KW.round(from, -OFFSET_VORWOCHE_KW);
		final Period qyRange = Period.create(from, kwRange.getEnd());
		
		final LogiwareService service = server.locateService(LogiwareService.class);
		final LWObjectCache cache = service.createObjectCache();
		result = cache.load(LWAuftrag.class, LWAuftragKopf.class,
				Factory.inRange(LWAuftragMeta.ATR_WUNSCHDATUM, qyRange),
				Factory.eq(LWAuftragMeta.ATR_AUFTRAGNUMMER, LWAuftragKopfMeta.ATR_AUFTRAGNUMMER),
				Factory.eq(LWAuftragMeta.ATR_GSE_AUFTRAG, LWAuftragKopfMeta.ATR_GSE_AUFTRAG),
				Factory.in(LWAuftragMeta.ATR_ABWICKLUNGSART, (Object[]) ABWICKLUNGSARTEN),
				Factory.notIn(LWAuftragMeta.ATR_STATUSCODE, LWAuftragStatusCode.STORNIERT)

				);

		return result;
	}

	private String createQueryDispo(final PacketStatisticRequest request) {
		final StringMaker w = StringMaker.obtain(256);
		{ // build where clause
			final Date qyFrom = kwFrom;
			final Date qyUpto = kwUpto;
			append(w, "", getDateRestrictionClause(qyFrom, qyUpto, "dateFrom", "dateUpTo"));
			append(w, " AND ", "type = "+ZUORDNUNG_SUPERVISOR.getId());
		}
		final StringMaker b = StringMaker.obtain(512);
		b.append("SELECT * FROM FeinZuordnung WHERE ");
		b.append(w);
		w.releaseSilent();
		return b.release();
	}	

	private Collection<DataPoint> calculateBegleitung(final List<StatisticFeinZuordnung> feinZuordnungList,
			final PacketStatisticRequest packet,
			final AuftragBewertungType modeId) {
		final String label = "Bedarf " + AbstractCode.getLongText(modeId, "???") + '\n';
		final Map<String, DataPoint> bedarfMap = new HashMap<>(1024);
		final Iterator<StatisticFeinZuordnung> i = feinZuordnungList.iterator();
		while (i.hasNext()) {
			final StatisticFeinZuordnung zuordnung = i.next();
			final Date dateFrom = TimestampUtil.toDate(zuordnung.dateFrom);
			final Date dateUpTo = TimestampUtil.toDate(zuordnung.dateUpTo);
			final Date kwDate = TimestampUtil.toDate(TimestampUtil.findMonday(zuordnung.dateFrom, 0));
			final String tailLabel = Strings.concat(TimestampUtil.KWDATE7.format(kwDate), DataPoint.SEPARATOR, "Begleitung");
			// --- calculate the length of the element
			for (int day=0; day<5; ++day) {
				final Date hourFrom = DateUtil.setTime(DateGranularityCode.DAY.round(kwDate, day), 6, 0, 0, 0);
				final Date hourUpTo = DateUtil.setTime(hourFrom, 17, 59, 59, 0);
				if (dateFrom.after(hourUpTo)) {
					continue;
				} else if (dateUpTo.before(hourFrom)) {
					continue;
				}
				final Date d0 = dateFrom.after(hourFrom) ? dateFrom : hourFrom;
				final Date d1 = dateUpTo.after(hourUpTo) ? hourUpTo : dateUpTo;
				final long millis = d1.getTime() - d0.getTime() + 1000;
				final long slots = Math.round(millis / 3600000D);
				DataPoint p = bedarfMap.get(tailLabel);
				if (p == null) {
					p = new DataPoint(label + tailLabel, 0);
					bedarfMap.put(tailLabel, p);
				}
				p.addValue(slots * (20D/12D));
			}
		}
		
		return new ArrayList<>(bedarfMap.values());
	}

	private Collection<DataPoint> calculateBedarf(final List<LWAuftrag> auftragList,
			final PacketStatisticRequest packet,
			final AuftragBewertungType modeId) {
		final String label = Strings.concat("Bedarf ", AbstractCode.getLongText(modeId, "???"), DataPoint.SEPARATOR);
		final Map<String, DataPoint> bedarfMap = new HashMap<>(1024);	
		for (final LWAuftrag auftrag : auftragList) {
			final long aufKW = TimestampUtil.findMonday(auftrag.getWunschDatum(), 0);
			if (kwRange.intersects(auftrag.getWunschDatum())) {
				final String tailLabel = getTailLabel(auftrag, aufKW);
				final AuslastungKWAuftragBewertungEntry wert = bewertungKW_0.getEntry(auftrag, AuftragBewertungAccessorLW.INSTANCE);
				final double value = wert.getValue(modeId);
				DataPoint p = bedarfMap.get(tailLabel);
				if (p == null) {
					p = new DataPoint(Strings.concat(label, tailLabel), 0);
					bedarfMap.put(tailLabel, p);
				}
				p.addValue(value);
			}
		}
		// vorwoche einrechnen
		if (AuftragBewertungType.FA15.equals(modeId)) {
			for (final LWAuftrag auftrag : auftragList) {
				final long addKW = TimestampUtil.findMonday(auftrag.getWunschDatum(), OFFSET_VORWOCHE_KW);
				if (kwRange.intersects(addKW)) {
					final String tailLabel = getTailLabel(auftrag, addKW);
					final AuslastungKWAuftragBewertungEntry wert = bewertungKW_3.getEntry(auftrag, AuftragBewertungAccessorLW.INSTANCE);
					final double value = wert.getValue(modeId);
					DataPoint p = bedarfMap.get(tailLabel);
					if (p == null) {
						p = new DataPoint(Strings.concat(label, tailLabel), 0);
						bedarfMap.put(tailLabel, p);
					}
					p.addValue(value);
				}
			}
		}
		
		return new ArrayList<>(bedarfMap.values());
	}

	private String getTailLabel(final LWAuftrag auftrag, final long date){
		if (stacked) {
			final AbwicklungsartCode abwArt = Map.fixup(AWA_REMAP, auftrag.getAbwicklungsart());
			final String abwArtLabel = Strings.toString(AbstractCode.getKey(abwArt, (Object) null));
			String subType = "0";
			if (AbwicklungsartCode.AWA001.equals(abwArt) ||
				AbwicklungsartCode.AWA135.equals(abwArt)) {
				// differentiate objekt/miet/einzel
				final LWGranit2Code g2 = auftrag.getAuftragKopf().getGranit2();
				if (AbstractCode.isNull(g2)) {
					subType = "5"; // Objekt
				} else if (g2.isEinzelkueche()) {
					subType = "9";
				} else if (g2.isMietobjekt()) {
					subType = "1";
				} else {
					subType = "5"; // Objekt
				}
			}
			StringMaker maker = StringMaker.obtain();
			maker.append(date, TimestampUtil.KWDATE7);
			maker.append(DataPoint.SEPARATOR);
			maker.append(abwArtLabel);
			maker.append(subType);
			return maker.release();
		} else {
			return DateUtil.KWDATE7.format(date);
		}
	}

	@SuppressWarnings("unchecked")
	private void calculateGebucht(final PacketStatisticRequest packet, final List<DataPoint> targetList) {
		final Date fromKW = DateUtil.findMonday(kwFrom, 0);
		final Date uptoKW = DateUtil.findMonday(kwUpto, 1);
		final ModelContext global = server.getGlobalContext();
		final PresentoCache cache = server.getPresentoCache();
		final GSECode gse = toGSE(packet.getType());
		global.obtain();
		try {
			final List<MasterMitarbeiter> employeeList = (List) global.getModelList(MasterMitarbeiter.class, new Filter<AbstractModel>() {
				@Override
				public boolean accepts(final AbstractModel model) {
					if (model instanceof MasterMitarbeiter) {
						final MasterMitarbeiter master = (MasterMitarbeiter) model;
						return gse.equals(master.getGSE()) &&
							   master.getEmploymentType().isCountedAsEmployee() &&
							   MITARBEITER_MONTEUR.equals(master.getEmploymentRole());
					}
					return false;
				}
			});
			
			// --- Auszählen aller betroffenen KW's
			Date kw = fromKW;
			while (kw.before(uptoKW)) {
				final String kwString = TimestampUtil.KWDATE7.format(kw);
				final DataPoint counted = new DataPoint(Strings.concat(LABEL_BOOKED_COUNT, kwString), 0D);
				final DataPoint weighted = new DataPoint(Strings.concat(LABEL_BOOKED_WEIGHT, kwString), 0D);
				//targetList.add(counted);
				targetList.add(weighted);
	
				// --- Jeden einzelnen Wochentag abchecken...
				for (int weekDay=0; weekDay<5; ++weekDay) {
					final long day = DateGranularityCode.DAY.round(TimestampUtil.toTimestamp(kw), weekDay);

					// --- Verfügbarkeit aller Monteure
					for (int i=employeeList.size()-1; i>=0; --i) {
						final MasterMitarbeiter master = employeeList.get(i);
						// --- get Person from presento
						final PresentoPerson person = cache.getPerson(master.getPresentoId());
						if (person != null) {
							// --- must be internal employee
							if (person.isWorking(day)) {
								weighted.addValue(0.2D * master.getLeistungsFaktor()); 
								counted.addValue(20D);
							}
						} else {
							// --- must be external employee
							if (master.getEmployment().isWorking(day)) {
								weighted.addValue(0.2D * master.getLeistungsFaktor()); 
								counted.addValue(20D);
							}
						}
					}
				}
				// --- nächste kw
				kw = DateUtil.findMonday(kw, 1);
			}
		} finally {
			global.release();
		}
	}

}
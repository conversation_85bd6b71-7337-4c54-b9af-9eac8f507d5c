package ch.eisenring.dispo.server.hibernate;

import org.hibernate.Query;
import org.hibernate.Session;
import org.hibernate.Transaction;

import ch.eisenring.core.logging.Logger;

public class HibernateUtil {

	// current session and transaction
	protected Session session;
	protected Transaction transaction;

	/**
	 * Begins a transaction - gets session/transaction as needed.
	 */
	public void transactionStart() {
		if (null==session) {
			session = HibernateSessionFactory.openSession();
			transaction = session.beginTransaction();
			Logger.debug("hibernate transaction started");
		}
	}
	
	public void transactionCommit() {
		if (transaction == null || session == null)
			return;
		try {
			if (transaction.isActive()) {
				transaction.commit();
				Logger.debug("hibernate transaction committed");
			}
		} finally {
			try {
				session.close();
			} catch (Exception e) {} 
			transaction = null;
			session = null;
		}
	}
	
	public void transactionRollback() {
		if (session == null || transaction == null)
			return;
		try {
			if (transaction.isActive()) {
				transaction.rollback();
				Logger.debug("hibernate transaction rolled back");
			}
		} finally {
			try {
				session.close();
			} catch (Exception e) {} 
			transaction = null;
			session = null;
		}
	}
	
	public Query createQuery(String hqlQuery) {
		transactionStart();
		return session.createQuery(hqlQuery);
	}

}

package ch.eisenring.dispo.server.presento;

import ch.eisenring.app.shared.CoreTickListenerAdapter;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.dispo.server.DSPServer;
import ch.eisenring.dispo.server.model.ServerContextCache;
import ch.eisenring.dispo.server.model.ServerModelContext;
import ch.eisenring.presento.server.api.PresentoAccess;
import ch.eisenring.presento.server.api.PresentoCache;

public class ServerPresentoUpdater extends CoreTickListenerAdapter {

	private final DSPServer server;

	public ServerPresentoUpdater(final DSPServer server) {
		super(server, "ServerPresentoUpdater", ASYNCHRONOUS);
		this.server = server;
	}

	private long nextDue = Long.MIN_VALUE;

	@Override
	protected boolean isDue(final long now) {
		if (now < nextDue)
			return false;
		nextDue = selectNextDueTimestamp(now, null, now + 940_000);
		return true;
	}

	@Override
	protected void tickImpl(final long now) {
		updatePresentoCache(server);
	}

	/**
	 * Obtains access to the current cache instance.
	 * Can return null if no cache is available
	 */
	private static PresentoCache getPresentoCache(final DSPServer server) {
		final PresentoAccess cacheSource = server.getFeatureLookup().getFeature(PresentoAccess.class);
		if (cacheSource == null) {
			// the presento feature is not available
			return null;
		}
		return cacheSource.getPresentoCache();
	}
	
	/**
	 * (Re-) Loads the PRESENTO cache and updates all contexts.
	 */
	@SuppressWarnings("unchecked")
	public static void updatePresentoCache(final DSPServer server) {
		Logger.info("updating presento cache");

		final PresentoCache presentoCache = getPresentoCache(server);
		if (presentoCache == null) {
			Logger.error("Failed to update PRESENTO cache data");
			return;
		}

		Logger.info("Set new PRESENTO cache data");
		updateLoadedContexts(server, presentoCache);
	}

	public static void updateGlobalContext(final DSPServer server, final PresentoCache cache) {
		final ServerModelContext global = server.getGlobalContext();
		global.obtain();
		try {
			global.setLastChangedBy("PRESENTO_UPDATER");
			PresentoUpdater.updateMasterMitarbeiter(global, cache);
			if (global.isModified()) {
				global.store();
				server.sendContextChanges(global);
			}
		} finally {
			global.release();
		}
	}

	@SuppressWarnings("unchecked")
	private static void updateLoadedContexts(final DSPServer server, final PresentoCache cache) {
		updateGlobalContext(server, cache);
			
		// update all regular model context instances
		final ServerContextCache contextCache = server.getContextCache();
		final List<ServerModelContext> contextList = (List) contextCache.getContextList();
		for (final ServerModelContext context : contextList) {
			context.obtain();
			try {
				context.setLastChangedBy("PRESENTO_UPDATER");
				PresentoUpdater.updateMitarbeiter(context, cache);
				server.sendContextChanges(context);
			} finally {
				context.release();
			}
		}
	}

}

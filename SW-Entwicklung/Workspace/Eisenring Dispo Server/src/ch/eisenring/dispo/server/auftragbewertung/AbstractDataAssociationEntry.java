package ch.eisenring.dispo.server.auftragbewertung;


public abstract class AbstractDataAssociationEntry<K> {

	protected final int score;
	protected final int sourceLine;

	protected AbstractDataAssociationEntry(final int score, final int sourceLine) {
		this.score = score;
		this.sourceLine = sourceLine;
	}

	public final int getScore() {
		return score;
	}

	public final int getSourceLine() {
		return sourceLine;
	}

	public abstract <U> boolean matches(final U auftrag, final AuftragBewertungAccessor<U> accessor);

}

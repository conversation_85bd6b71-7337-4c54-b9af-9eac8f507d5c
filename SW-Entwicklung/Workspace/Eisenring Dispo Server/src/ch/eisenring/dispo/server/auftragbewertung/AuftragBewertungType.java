package ch.eisenring.dispo.server.auftragbewertung;

import ch.eisenring.core.codetype.StaticCode;

public final class AuftragBewertungType extends StaticCode {

	public final static AuftragBewertungType FA15 =
		new AuftragBewertungType(1, "FA 1.5");

	public final static AuftragBewertungType FA_ANZAHL =
		new AuftragBewertungType(2, "<PERSON> Anzahl");

	private AuftragBewertungType(final int id, final String text) {
		super(id, Integer.valueOf(id), text, text);
	}

}

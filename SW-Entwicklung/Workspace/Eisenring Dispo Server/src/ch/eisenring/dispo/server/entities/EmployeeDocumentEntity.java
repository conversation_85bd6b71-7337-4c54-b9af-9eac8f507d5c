package ch.eisenring.dispo.server.entities;

import java.util.Date;

import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.dispo.shared.codetables.EmployeeDocumentCode;
import ch.eisenring.dispo.shared.model.AbstractModel;
import ch.eisenring.dispo.shared.model.EmployeeDocument;

public class EmployeeDocumentEntity extends AbstractDocumentEntity {

	private MasterMitarbeiterEntity employee;
	private Date creationDate;
	private Date mailDate;
	private Integer typeCode;

	@Override
	public Class<? extends AbstractModel> getModelClass() {
		return EmployeeDocument.class;
	}

	public MasterMitarbeiterEntity getEmployee() {
		return employee;
	}

	public void setEmployee(final MasterMitarbeiterEntity employee) {
		this.employee = employee;
	}

	public Date getCreationDate() {
		return creationDate;
	}

	public void setCreationDate(Date creationDate) {
		this.creationDate = creationDate;
	}

	public Date getMailDate() {
		return mailDate;
	}

	public void setMailDate(Date mailDate) {
		this.mailDate = mailDate;
	}

	public Integer getTypeCode() {
		return typeCode;
	}

	public void setTypeCode(Integer typeCode) {
		this.typeCode = typeCode;
	}

	@Override
	public void updateEntity(AbstractModel model) {
		super.updateEntity(model);
		final EmployeeDocument document = (EmployeeDocument) model;
		// set employee from model
		MasterMitarbeiterEntity e = (MasterMitarbeiterEntity) getContext().getEntity(document.getEmployee(), true);
		updateDirty(getEmployee(), e);
		setEmployee(e);
		updateDirty(document.getMailDate(), getMailDate());
		setMailDate(document.getMailDate());
		updateDirty(document.getCreationDate(), getCreationDate());
		setCreationDate(document.getCreationDate());
		Integer typeCode = (Integer) AbstractCode.getKey(document.getTypeCode(), null);
		updateDirty(typeCode, getTypeCode());
		setTypeCode(typeCode);
	}
	
	@Override
	public void updateModel(AbstractModel model) {
		super.updateModel(model);
		final EmployeeDocument document = (EmployeeDocument) model;
		updateModelRelation(document.getEmployeeRelation(), getEmployee());
		document.setMailDate(getMailDate());
		document.setCreationDate(getCreationDate());
		document.setTypeCode((EmployeeDocumentCode) AbstractCode.getByKey(
				getTypeCode(), EmployeeDocumentCode.class));
	}
	
	@Override
	public String getObjectDescription() {
		final StringMaker b = StringMaker.obtain(128);
		b.append("EmployeeDocument(");
		b.append("BinaryId=");
		b.append(getBinaryId());
		b.append(";ByteSize=");
		b.append(getByteSize());
		b.append(";FileName=");
		b.append(getFileName());
		b.append(";Comment=");
		b.append(getBemerkungen());
		b.append(";TypeCode=");
		b.append(getTypeCode());
		b.append(";MailDate=");
		b.append(getMailDate(), TimestampUtil.DATE10);
		b.append(";CreationDate=");
		b.append(getCreationDate(), TimestampUtil.DATE10);
		b.append(")");
		return b.release();
	}

}

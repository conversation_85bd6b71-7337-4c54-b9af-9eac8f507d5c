package ch.eisenring.dispo.server.network;

import ch.eisenring.dispo.server.DSPServer;
import ch.eisenring.dispo.server.service.AuftragInfoService;
import ch.eisenring.dispo.shared.network.packets.PacketInfoRequest;
import ch.eisenring.network.PacketSink;
import ch.eisenring.network.implementation.PacketDispatchMode;
import ch.eisenring.network.packet.AbstractPacket;

public final class InfoRequestHandler extends AbstractDSPPacketHandler {

	InfoRequestHandler(final DSPServer server) {
		super(server, PacketInfoRequest.class, PacketDispatchMode.ASYNCHRONOUS);
	}

	@Override
	public void handle(final AbstractPacket abstractPacket, final PacketSink sink) {
		final PacketInfoRequest packet = (PacketInfoRequest) abstractPacket;
		final AuftragInfoService service = new AuftragInfoService(server, packet, sink);
		service.performService();
	}
	
}

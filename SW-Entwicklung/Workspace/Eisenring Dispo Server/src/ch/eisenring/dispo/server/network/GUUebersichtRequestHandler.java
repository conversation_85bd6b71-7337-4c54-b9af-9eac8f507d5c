package ch.eisenring.dispo.server.network;

import java.sql.SQLException;
import java.util.Collections;

import ch.eisenring.core.poi.POIUtil;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;

import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.csv.CSVFile;
import ch.eisenring.core.csv.CSVLine;
import ch.eisenring.core.datatypes.date.DateGranularityCode;
import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.core.datatypes.primitives.ConversionUtil;
import ch.eisenring.core.io.binary.BinaryHolder;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.core.sort.Comparator;
import ch.eisenring.core.util.file.FileImage;
import ch.eisenring.core.util.file.FileUtil;
import ch.eisenring.dispo.server.DSPServer;
import ch.eisenring.dispo.shared.network.packets.PacketGUUebersichtReply;
import ch.eisenring.dispo.shared.network.packets.PacketGUUebersichtRequest;
import ch.eisenring.jdbc.JDBCBase;
import ch.eisenring.jdbc.JDBCResultSet;
import ch.eisenring.jdbc.StatementParameters;
import ch.eisenring.jdbc.StatementWrapper;
import ch.eisenring.logiware.LWContextSource;
import ch.eisenring.logiware.code.pseudo.SubjektCode;
import ch.eisenring.logiware.code.soft.LWObjektBetreuerCode;
import ch.eisenring.lw.LWConstants;
import ch.eisenring.model.TransactionContext;
import ch.eisenring.network.PacketSink;
import ch.eisenring.network.packet.AbstractPacket;

public class GUUebersichtRequestHandler extends AbstractDSPPacketHandler {

	static class LineModel {
		public long datWu;
		public LWObjektBetreuerCode objektbetreuer;
		public String kuechenAnzahl;
		public String basisprojektnummer;
		public String objektbezeichnung;
		public SubjektCode generalunternehmer;
		public String baufuehrer;
	}

	public final static Comparator<LineModel> ORDER = Comparator.wrapNullSafe(
			new Comparator<LineModel>() {
		@Override
		public int compare(final LineModel m1, final LineModel m2) {
			final SubjektCode s1 = m1.generalunternehmer;
			final SubjektCode s2 = m2.generalunternehmer;
			final Integer id1 = ConversionUtil.convert(AbstractCode.getKey(s1, null), (Integer) null);
			final Integer id2 = ConversionUtil.convert(AbstractCode.getKey(s2, null), (Integer) null);
			int result = compareUnsigned(id1, id2);
			if (result != 0)
				return result;
			return TimestampUtil.compare(m1.datWu, m2.datWu);
		}
	});
	
	public GUUebersichtRequestHandler(final DSPServer server) {
		super(server, PacketGUUebersichtRequest.class);
	}

	@Override
	public void handle(final AbstractPacket packet, final PacketSink sink) {
		final PacketGUUebersichtRequest request = (PacketGUUebersichtRequest) packet;
		final PacketGUUebersichtReply reply = handle(server, request);
		sink.sendPacket(reply, false);
	}

	public static PacketGUUebersichtReply handle(
			final DSPServer server,
			final PacketGUUebersichtRequest request) {
		final List<LineModel> data = new ArrayList<LineModel>();
		try {
			final LWContextSource source = new LWContextSource(LWConstants.VERTRIEB) {
				@Override
				protected void loadImpl(final TransactionContext context) throws SQLException {
					final StatementWrapper select = buildQuery(this, request);
					try {
						final JDBCResultSet resultSet = select.executeQuery();
						try {
							while (resultSet.next()) {
								final LineModel line = new LineModel();
								line.datWu = resultSet.getLongDate(1);
								line.objektbetreuer = AbstractCode.getByKey(resultSet.getString(2), LWObjektBetreuerCode.class, false);
								line.basisprojektnummer = resultSet.getString(3);
								line.kuechenAnzahl = resultSet.getString(4);
								line.objektbezeichnung = resultSet.getString(5);
								line.baufuehrer = resultSet.getString(6);
								line.generalunternehmer = getGU(resultSet);
								data.add(line);
							}
						} finally {
							closeSilent(resultSet);
						}
					} finally {
						closeSilent(select);
					}
				}
				
				private SubjektCode getGU(final JDBCResultSet resultSet) throws SQLException {
					int subjektNummer = resultSet.getInt(7);
					if (resultSet.wasNull())
						return null;
					final SubjektCode subjekt = AbstractCode.getByKey(subjektNummer, SubjektCode.class, false);
					return AbstractCode.isNull(subjekt) ? null : subjekt;
				}
			};
			final TransactionContext context = new TransactionContext();
			context.load(source);
			Collections.sort(data, ORDER);
			// build the csv file
			final CSVFile csvFile = createCSV(data);
			final Workbook workbook = createXLSX(csvFile);
			final BinaryHolder filedata = POIUtil.toBinary(workbook);
			String filename = "GUUebersicht" + FileUtil.getUniqueFileId();
			filename = FileUtil.sanitizeFilename(filename) + ".xlsx";
			final FileImage fileimage = FileImage.create(filename, filedata);
			final PacketGUUebersichtReply reply = PacketGUUebersichtReply.create(request, fileimage);
			return reply;
		} catch (final Exception e) {
			Logger.error(e);
			return PacketGUUebersichtReply.create(request, new ErrorMessage(e));
		}
	}

	private static StatementWrapper buildQuery(
			final LWContextSource source,
			final PacketGUUebersichtRequest request) throws SQLException {
		final Integer subjektNummer = request.getSubjektNummer();
		final long dateFrom = DateGranularityCode.DAY.round(request.getDateFrom(), 0);
		final long dateUpto = DateGranularityCode.DAY.round(request.getDateUpto(), 1);
		final StatementParameters params = new StatementParameters();
		String sub = "(SELECT MIN(A.DatWu)"
						+ " FROM AUFTRAG A, PAUFTRAG PA"
						+ " WHERE A.AbwArt = '135'"
						+ " AND A.Status NOT IN ('090', '099')"
						+ " AND A.PAuftrNr = PA.PAuftrNr"
						+ " AND A.Id_GSE_PA = PA.Id_GSE_PA"
						+ " AND PA.PAuftrnr_B = P.PAuftrNr"
						+ ")";
		String sql = "SELECT"
				+ " " + sub + " AS DatWu"
				+ ", P.PFaktPerCd AS ObjektBetreuer"
				+ ", P.PAuftrNr AS Basisprojekt"
				+ ", P.Reserve8 AS KuechenAnzahl"
				+ ", P.PBez AS Baustelle"
				+ ", P.Reserve9 AS NameBaufuehrer"
				+ ", Id_Subjekt_I"
				+ " FROM"
				+ " PAUFTRAG P"
				+ "	WHERE"
				+ " P.PAuftrNr_B = P.PAuftrNr"
				+ " AND P.PAuftrNr NOT LIKE '%-%'"
				+ " AND P.Status NOT IN ('090', '099')"
				+ " AND " + sub + " IS NOT NULL";
		if (subjektNummer != null) {
			sql += " AND Id_Subjekt_I = ?";
			params.add(subjektNummer);
		}
		if (!TimestampUtil.isNull(dateFrom)) {
			sql += " AND " + sub + " >= ?";
			params.addDate(dateFrom);
		}
		if (!TimestampUtil.isNull(dateUpto)) {
			sql += " AND " + sub + " < ?";
			params.addDate(dateUpto);
		}
		// prepare and configure the query
		final StatementWrapper statement = source.prepareStatement(sql);
		try {
			statement.setParameters(params);;
		} catch (final SQLException e) {
			JDBCBase.closeSilent(statement);
			throw e;
		}
		return statement;
	}

	private static CSVFile createCSV(final List<LineModel> dataList) {
		final CSVFile csvFile = new CSVFile();
		final CSVLine header = csvFile.addLine();
		header.setColumn(0, "KW\nJahr");
		header.setColumn(1, "OB");
		header.setColumn(2, "Basis");
		header.setColumn(3, "Anzahl");
		header.setColumn(4, "Baustelle / Ortschaft");
		header.setColumn(5, "GU#");
		header.setColumn(6, "GU/Auftraggeber");
		header.setColumn(7, "Bauführer");
		for (final LineModel data : dataList) {
			final CSVLine line = csvFile.addLine();
			line.setColumn(0, TimestampUtil.KWDATE7.format(data.datWu));
			line.setColumn(1, AbstractCode.getShortText(data.objektbetreuer, ""));
			line.setColumn(2, data.basisprojektnummer);
			line.setColumn(3, data.kuechenAnzahl);
			line.setColumn(4, data.objektbezeichnung);
			line.setColumn(5, ConversionUtil.convert(AbstractCode.getKey(data.generalunternehmer, null), ""));
			line.setColumn(6, AbstractCode.getText(data.generalunternehmer, ""));
			line.setColumn(7, data.baufuehrer);
		}
		return csvFile;
	}

	private static Workbook createXLSX(final CSVFile csvFile) {
		final Workbook workbook = POIUtil.createWorkbook();
		final Sheet sheet = POIUtil.createSheet(workbook);
		sheet.getPrintSetup().setLandscape(true);
		workbook.setSheetName(0, "GU-Übersicht");
		POIUtil.popuplateSheet(sheet, csvFile);
		// basic formatting
		sheet.getRow(0).setHeightInPoints(24F);
		// make header gray
		{
//			final CellStyle cellStyle = workbook.createCellStyle();
//			cellStyle.setFillForegroundColor(HSSFColor.GREY_25_PERCENT.index);
//			cellStyle.setFillPattern(CellStyle.SOLID_FOREGROUND);
			POIUtil.setFillForegroundColor(sheet, IndexedColors.GREY_25_PERCENT.getIndex(), 0, 0, csvFile.getColumnCount(), 1);
			POIUtil.addBorder(sheet, IndexedColors.BLACK.getIndex(), 0, 0, csvFile.getColumnCount(), 1);
		}
		return workbook;
	}

}

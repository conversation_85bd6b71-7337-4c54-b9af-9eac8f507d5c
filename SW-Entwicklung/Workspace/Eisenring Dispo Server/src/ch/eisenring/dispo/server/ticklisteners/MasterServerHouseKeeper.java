package ch.eisenring.dispo.server.ticklisteners;

import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.dispo.server.DSPServer;

public class MasterServerHouseKeeper extends AbstractServerHouseKeeper {

	private final List<AbstractServerHouseKeeper> houseKeepers = new ArrayList<AbstractServerHouseKeeper>();
	
	public MasterServerHouseKeeper(final DSPServer server) {
		super(server, 1000L);

		// add all housekeepers
		houseKeepers.add(new ServerDispoEventMailerHK(server));
		houseKeepers.add(new DeadlockWatchDogHK(server));

		start();
	}
	
	@Override
	public String getName() {
		return "ServerMasterHousekeeper";
	}

	@Override
	public int getMaxErrorCount() {
		return 0;
	}

	@Override
	protected void doHousekeeping() {
		if (!getServer().isActive()) {
			// the server is shutting down, crashed
			// or was terminated otherwise
			terminate();
		}
	}
	
	@Override
	protected void start() {
		super.start();
		// start all housekeepers
		for (int i=houseKeepers.size()-1; i>=0; --i) {
			final AbstractServerHouseKeeper k = houseKeepers.get(i);
			k.start();
		}
	}
	
	@Override
	protected void terminate() {
		super.terminate();
		// terminate all housekeepers
		for (int i=houseKeepers.size()-1; i>=0; --i) {
			AbstractServerHouseKeeper k = houseKeepers.get(i);
			k.terminate();
		}
	}
	
	public void shutdown() {
		terminate();
	}

}

package ch.eisenring.dispo.server.service.statistics;

import static ch.eisenring.dispo.server.DSPServerConstants.STATE_RCFG_STAT_AUSLAST_KW;
import static ch.eisenring.dispo.server.DSPServerConstants.STATE_RCFG_STAT_AUSLAST_PFYN;
import static ch.eisenring.dispo.server.DSPServerConstants.STATE_RCFG_STAT_AUSLAST_TAG;
import static ch.eisenring.dispo.server.service.statistics.AuslastungObjektTypBewertung.LABEL_EXCLUDE;
import static ch.eisenring.dispo.server.service.statistics.AuslastungObjektTypBewertung.LABEL_NOT_FOUND;
import static ch.eisenring.dispo.shared.codetables.DSPStatisticTypeCodes.STATTYPE_AUSLAST_OBJTYP_HEAG_KW;
import static ch.eisenring.dispo.shared.codetables.DSPStatisticTypeCodes.STATTYPE_AUSLAST_OBJTYP_HEAG_TAG;
import static ch.eisenring.logiware.ILogiwareVertriebConstants.COL_ORD_WUNSCHDATUM;
import static ch.eisenring.logiware.ILogiwareVertriebConstants.TBL_ORDER_STATISTICS;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.Date;

import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.Map;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.collections.impl.HashMap;
import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.core.logging.Logger.LogLevel;
import ch.eisenring.dispo.server.DSPServer;
import ch.eisenring.jdbc.JDBCResultSet;
import ch.eisenring.statistics.shared.codetables.STDateGranularityCode;
import ch.eisenring.statistics.shared.codetables.StatisticTypeCode;
import ch.eisenring.statistics.shared.data.DataPoint;
import ch.eisenring.statistics.shared.network.PacketStatisticRequest;
import ch.eisenring.statistics.shared.network.PacketStatisticResponse;

public class AuslastungObjektTypHEAG extends StatisticsBase {

	public AuslastungObjektTypHEAG(final DSPServer server) {
		super(server);
	}
	
	@Override
	public PacketStatisticResponse createResponse(final PacketStatisticRequest packet) {
		PacketStatisticResponse result = null;
		final StatisticTypeCode type = packet.getType();
		final String sql = createQuery(packet);
		Logger.log(LogLevel.TRACE, "SQL: "+sql);
		final AuslastungObjektTypBewertung bewertung;
		if (STATTYPE_AUSLAST_OBJTYP_HEAG_KW.equals(type)) {
			bewertung = new AuslastungObjektTypBewertung(server, STATE_RCFG_STAT_AUSLAST_KW);
		} else if (STATTYPE_AUSLAST_OBJTYP_HEAG_TAG.equals(type)){
			bewertung = new AuslastungObjektTypBewertung(server, STATE_RCFG_STAT_AUSLAST_TAG);
		} else {
			bewertung = new AuslastungObjektTypBewertung(server, STATE_RCFG_STAT_AUSLAST_PFYN);
		}
		
		// --- execute query
		final Connection connection = connectToLogiware();
		if (null!=connection) {
			try {
				final JDBCResultSet resultSet = doFetchQuery(sql);
				final Map<String, DataPoint> dataMap = new HashMap<>();
				while (resultSet.next()) {
					final StatisticAuftrag auftrag = new StatisticAuftrag();
					auftrag.loadRow(resultSet);
					final String label = bewertung.getLabel(auftrag);
					if (LABEL_EXCLUDE.equals(label) || LABEL_NOT_FOUND.equals(label)) {
						continue;
					}
					final String when;
					if (STATTYPE_AUSLAST_OBJTYP_HEAG_KW.equals(type)) {
						when = TimestampUtil.KWDATE7.format(auftrag.wunschDatum);
					} else if (STATTYPE_AUSLAST_OBJTYP_HEAG_TAG.equals(type)) {
						when = TimestampUtil.DATE10.format(auftrag.wunschDatum);
					} else {
						when = TimestampUtil.KWDATE7.format(auftrag.wunschDatum);
					}
					final String key = Strings.concat(label, "\n", when);
					DataPoint p = dataMap.get(key);
					if (p == null) {
						p = new DataPoint(key, 0);
						dataMap.put(key, p);
					}
					p.addValue(1);
				}
				resultSet.close();

				final List<DataPoint> list = new ArrayList<DataPoint>(dataMap.values());
				addMissingCategoryValues(list, 0);
				
				result = PacketStatisticResponse.create(packet);

				// --- chart title
				String title = STDateGranularityCode.getDateRangeText(result);
				title = packet.getType().getText()+"\n("+title+")";
				result.setChartTitle(title);

				// --- values
				double[] data = getValues(list);
				result.setDataSeries(new double[][] { data });

				// --- labels
				String[] labels = getLabels(list);
				result.setLabels(labels);
			} catch (SQLException e) {	 
				Logger.log(LogLevel.ERROR, e);
			} finally {
				close();
			}
		}
		return result;
	}

	
	private String createQuery(final PacketStatisticRequest request) {
		final Date from;
		final Date upto;
		if (STATTYPE_AUSLAST_OBJTYP_HEAG_KW.equals(request.getType())) {
			from = getKWStartDatumNotNull(request.getDateFrom());
			upto = getKWEndDatumNotNull(request.getDateUpto());
		} else if (STATTYPE_AUSLAST_OBJTYP_HEAG_TAG.equals(request.getType())) {
			from = request.getDateFrom();
			upto = request.getDateUpto();
		} else {
			from = getKWStartDatumNotNull(request.getDateFrom());
			upto = getKWEndDatumNotNull(request.getDateUpto());
		}
		final StringMaker b = StringMaker.obtain(512);
		b.append("SELECT ");
		new StatisticAuftrag().getSelectColumns(b);
		b.append(" FROM ");
		qualifyTableName(b, TBL_ORDER_STATISTICS);
		append(b, " WHERE ", getDateRestrictionClause(from, upto, COL_ORD_WUNSCHDATUM));
		return b.release();
	}
	
}

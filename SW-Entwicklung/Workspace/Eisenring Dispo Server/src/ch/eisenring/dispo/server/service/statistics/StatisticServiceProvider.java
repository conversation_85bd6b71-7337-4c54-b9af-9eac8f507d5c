package ch.eisenring.dispo.server.service.statistics;

import static ch.eisenring.dispo.shared.codetables.DSPStatisticTypeCodes.*;
import static ch.eisenring.logiware.code.pseudo.AbwicklungsartCode.AWA112;
import static ch.eisenring.logiware.code.pseudo.AbwicklungsartCode.AWA113;
import static ch.eisenring.logiware.code.pseudo.AbwicklungsartCode.AWA115;
import static ch.eisenring.logiware.code.pseudo.AbwicklungsartCode.AWA134;
import static ch.eisenring.logiware.code.pseudo.AbwicklungsartCode.AWA135;

import ch.eisenring.core.logging.Logger;
import ch.eisenring.core.logging.Logger.LogLevel;
import ch.eisenring.dispo.server.DSPServer;
import ch.eisenring.dispo.shared.codetables.DSPStatisticTypeCodes;
import ch.eisenring.statistics.server.StatisticServer;
import ch.eisenring.statistics.server.StatisticService;
import ch.eisenring.statistics.shared.codetables.StatisticTypeCode;
import ch.eisenring.statistics.shared.network.PacketStatisticRequest;

public class StatisticServiceProvider implements ch.eisenring.statistics.server.StatisticServiceProvider {

	static {
		// hack to initialize code tables
		DSPStatisticTypeCodes.STATTYPE_AUFTRAEGE_ALL.toString();
	}

	private final DSPServer dispoServer;
	
	public StatisticServiceProvider(final DSPServer dispoServer) {
		this.dispoServer = dispoServer;
	}

	@Override
	public StatisticService getService(final StatisticServer statisticServer,
									   final PacketStatisticRequest request) {
		final StatisticService service = createService(
				statisticServer, dispoServer, request);
		return service;
	}
	
	public static StatisticsBase createService(
			final StatisticServer statisticServer,
			final DSPServer server,
			final PacketStatisticRequest packet) {
		final StatisticTypeCode type = packet.getType();
		final StatisticsBase statistics;
		if (STATTYPE_KUECHENDISPONIERT.equals(type)) {
			statistics = new KuechenDisponiert(server);
		} else if (STATTYPE_AUFTRAEGE_ALL.equals(type)) {
			statistics = new KuechenDisponiertTyp(server, null);
		} else if (STATTYPE_AUFTRAEGE_ENDMOTAGE.equals(type)) {
			statistics = new KuechenDisponiertTyp(server, AWA115);
		} else if (STATTYPE_AUFTRAEGE_KLEINAUFTRAG.equals(type)) {
			statistics = new KuechenDisponiertTyp(server, AWA134);
		} else if (STATTYPE_AUFTRAEGE_KUECHENMONTAGE.equals(type)) {
			statistics = new KuechenDisponiertTyp(server, AWA135);
		} else if (STATTYPE_AUFTRAEGE_SERVICE.equals(type)) {
			statistics = new KuechenDisponiertTyp(server, AWA112);
		} else if (STATTYPE_AUFTRAEGE_SERVICEKL.equals(type)) {
			statistics = new KuechenDisponiertTyp(server, AWA113);
		} else if (STATTYPE_MONTIERT_ALL.equals(type)) {
			statistics = new MontiertTyp(server, MontiertTyp.BY_ALL, null);
		} else if (STATTYPE_MONTIERT_ENDMONTAGE.equals(type)) {
			statistics = new MontiertTyp(server, MontiertTyp.BY_ABWICKLUNGSART, AWA115);
		} else if (STATTYPE_MONTIERT_KLEINAUFTRAG.equals(type)) {
			statistics = new MontiertTyp(server, MontiertTyp.BY_ABWICKLUNGSART, AWA134);
		} else if (STATTYPE_MONTIERT_KUECHENMONTAGE.equals(type)) {
			statistics = new MontiertTyp(server, MontiertTyp.BY_ABWICKLUNGSART, AWA135);
		} else if (STATTYPE_MONTIERT_SERVICE.equals(type)) {
			statistics = new MontiertTyp(server, MontiertTyp.BY_ABWICKLUNGSART, AWA112);
		} else if (STATTYPE_MONTIERT_SERVICEKL.equals(type)) {
			statistics = new MontiertTyp(server, MontiertTyp.BY_ABWICKLUNGSART, AWA113);
		} else if (STATTYPE_MONTIERT_FAHRAUFTRAG.equals(type)) {
			statistics = new MontiertTyp(server, MontiertTyp.BY_FAHRAUFTRAG, null);
		} else if (STATTYPE_RESOURCES_GSE2501_ALL.equals(type) ||
				   STATTYPE_RESOURCES_GSE2502_ALL.equals(type) ||
				   STATTYPE_RESOURCES_GSE2503_ALL.equals(type) ||
				   STATTYPE_RESOURCES_GSE2501_KA.equals(type) ||
				   STATTYPE_RESOURCES_GSE2502_KA.equals(type) ||
				   STATTYPE_RESOURCES_GSE2503_KA.equals(type)) {
			statistics = new AuslastungKW(server);
		} else if (STATTYPE_AUSLAST_OBJTYP_OBJBET.equals(type)) {
			statistics = new AuslastungObjektTypObjektBetreuer(server);
		} else if (STATTYPE_AUSLAST_OBJTYP_HEAG_KW.equals(type) ||
				   STATTYPE_AUSLAST_OBJTYP_HEAG_TAG.equals(type) ||
				   STATTYPE_AUSLAST_PFYN_KW.equals(type)) {
			statistics = new AuslastungObjektTypHEAG(server);
		} else if (STATTYPE_AVAIL_MONT2501.equals(type) ||
				   STATTYPE_AVAIL_MONT2502.equals(type) ||
				   STATTYPE_AVAIL_MONT2503.equals(type)) {
			statistics = new AvailabilityEmployeesKW(server);
		} else if (STATTYPE_AUSLAST_OBJTYP_OBJBET_KW.equals(type)) {
			statistics = new AuslastungObjektTypObjektBetreuerKW(server);
		} else if (STATTYPE_BESTAND_MONTEURE.equals(type)) {
			statistics = new BestandMonteureAuswertung(server);
		} else if (STATTYPE_SERVICEAUFTRAG.equals(type) ||
				   STATTYPE_SERVICEAUFTRAGSC.equals(type)) {
			statistics = new FormStatistic(server);
		} else if (STATTYPE_BEWERTUNG_AUFTRAEGE.equals(type)) {
			statistics = new StatisticBewertungAuftraege(server);
		} else if (STATTYPE_AUSLASTUNG_OBJEKTBETREUER.equals(type)) {
			statistics = new AuslastungObjektbetreuer(server);
		} else if (STATTYPE_LEISTUNG_INTERNE_2501.equals(type) ||
				   STATTYPE_LEISTUNG_UNTERAKKORDANTEN_2501.equals(type) ||
				   STATTYPE_LEISTUNG_TEMPORAERE_2501.equals(type)) {
			statistics = new LeistungMonteur(server);
		} else if (STATTYPE_FRONTFARBENAUSWERTUNG.equals(type)) {
			statistics = new ProduktmanagementFrontfarbe(server);
		} else if (STATTYPE_KEINBEZUGSDATUM.equals(type)) {
			statistics = new KeinBezugsdatum(server);
		} else if (STATTYPE_PERFORMANCE_HEAG.equals(type) ||
				   STATTYPE_PERFORMANCE_PT_PRO_AUFTRAG.equals(type) ||
				   STATTYPE_PERFORMANCE_115PER135.equals(type)) {
			statistics = new MontagePerformanceHEAG(server);
		} else if (STATTYPE_KONTROLLEN_MONTEURE.equals(type)) {
			statistics = new MonteurKontrollUebersichtKW(server);
		} else {
			Logger.log(LogLevel.ERROR, "Unknown Statistics Type: "+type);
			Logger.log(LogLevel.ERROR, "Request ignored");
			statistics = null;
		}
		if (statistics != null) {
			statistics.request = packet;
		}
		return statistics;
	}

}

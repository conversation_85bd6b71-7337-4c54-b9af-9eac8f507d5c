package ch.eisenring.dispo.server;

import ch.eisenring.app.server.network.ServerSocketListener;
import ch.eisenring.network.security.NetworkSecurityManager;

/**
 * This listener accepts incoming client connections.
 */
final class DSPConnectionListener extends ServerSocketListener<DSPServer> {

	public DSPConnectionListener(final DSPServer server) {
		super("DSPClientListener", server, NetworkSecurityManager.ALLOW_ALL);
	}

}

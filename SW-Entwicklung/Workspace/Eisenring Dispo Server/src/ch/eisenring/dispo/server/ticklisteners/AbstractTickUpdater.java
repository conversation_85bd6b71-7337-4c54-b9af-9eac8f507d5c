package ch.eisenring.dispo.server.ticklisteners;

import ch.eisenring.app.shared.timing.Timer;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.dispo.server.DSPServer;

public abstract class AbstractTickUpdater {

	protected final DSPServer server;
	protected final Timer timer;

	protected AbstractTickUpdater(final DSPServer server, final Timer timer) {
		this.server = server;
		this.timer = timer;
	}

	public final void tick() {
		if (!timer.isDue())
			return;
		try {
			tickImpl();
		} catch (final Exception e) {
			Logger.error(e.getMessage());
			Logger.error(e);
			throw new RuntimeException(e.getMessage(), e);
		}
	}
	
	protected abstract void tickImpl();

}

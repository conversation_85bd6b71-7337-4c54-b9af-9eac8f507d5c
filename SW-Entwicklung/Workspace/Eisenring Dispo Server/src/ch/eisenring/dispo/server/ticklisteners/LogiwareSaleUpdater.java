package ch.eisenring.dispo.server.ticklisteners;

import ch.eisenring.app.shared.timing.AlwaysDueTimer;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.core.logging.Logger.LogLevel;
import ch.eisenring.dispo.server.DSPServer;
import ch.eisenring.dispo.server.logiware.LogiwareAuftragImport;
import ch.eisenring.dispo.server.model.DerivedDataUpdater;
import ch.eisenring.dispo.server.model.ServerContextCache;
import ch.eisenring.dispo.server.model.ServerModelContext;
import ch.eisenring.dispo.shared.network.packets.PacketModelExpunge;
import ch.eisenring.jdbc.ConnectionInfo;
import ch.eisenring.lw.LWConstants;
import ch.eisenring.user.server.USRServer;
import ch.eisenring.user.shared.model.UserContext;

public final class LogiwareSaleUpdater extends AbstractTickUpdater {

	private final static List<ContextUpdateRequest> UPDATE_REQUEST_QUEUE = new ArrayList<ContextUpdateRequest>();
	
	private final long fullRunInterval;
	private long nextFullRun;
	
	public LogiwareSaleUpdater(final DSPServer server) {
		super(server, new AlwaysDueTimer());
		final ConnectionInfo connectionInfo = LWConstants.VERTRIEB.getConnectionInfo(); 
		fullRunInterval = connectionInfo.getPropertyInteger("UpdateInterval", 1200) * 1000;
		nextFullRun = System.currentTimeMillis() + fullRunInterval;
	}

	@Override
	protected void tickImpl() {
		final long now = System.currentTimeMillis();
		while (true) {
			final ContextUpdateRequest request = getNextRequest();
			if (request == null)
				break;
			final long contextDate = request.contextDate;
			final String dateString = TimestampUtil.DATE10.format(contextDate);
			Logger.info(Strings.concat("START - Synchronizing week with Logiware (Verschobener Auftrag): ", dateString));
			updateContext(server, contextDate);
			Logger.info(Strings.concat("END ---- Synchronized week with Logiware (Verschobener Auftrag): ", dateString));
		}
		if (now > nextFullRun) {
			updateAll(server);
			nextFullRun = System.currentTimeMillis() + fullRunInterval;
		}
	}

	@SuppressWarnings("unchecked")
	protected static void updateAll(final DSPServer server) {
		// update all regular model context instances
		final ServerContextCache contextCache = server.getContextCache();
		final List<ServerModelContext> contextList = (List) contextCache.getContextList();

		if (Logger.isInfoEnabled())
			Logger.info(Strings.concat("updating logiware sale data (", contextList.size(), " week contexts)"));

		while (!contextList.isEmpty()) {
			if (Thread.currentThread().isInterrupted())
				return;
			// shutdown signal received?
			if (Thread.interrupted())
				return;
			final ServerModelContext context = contextList.remove(0);
			updateContext(server, context);
		}
	}

	protected static void updateContext(final DSPServer server, final ServerModelContext context) {
		final USRServer userSystem = (USRServer) server.getComponent(USRServer.class, true);
		final UserContext userContext = userSystem.getContext();
		try {
			context.obtain();
			final LogLevel logLevel = Logger.setLogLevel(LogLevel.INFO, false);
			try {
				final LogiwareAuftragImport loader = LogiwareAuftragImport.create(server);
				context.setLastChangedBy("LOGIWARE_UPDATER");
				loader.updateContext(context, userContext);
				DerivedDataUpdater.updateContext(server, context);
				context.clearLogiwareDirty();
				if (context.store()) {
					// notify all clients of the changes
					server.sendContextChanges(context);
					context.clearDirty();
					context.setLastChangedBy("UNKNOWN");
				} else {
					// if the store failed, the context evicts itself,
					// so no further action is taken here
					server.broadcast(PacketModelExpunge.create(context.getBegin()), true);
				}
			} finally {
				removeContextUpdate(context.getBegin());
				Logger.setLogLevel(logLevel, false);
				context.release();
			}
		} catch (Exception e) {
			// ignore, the update will be retried on the next interval
			Logger.error(e);
		}
	}

	/**
	 * Returns true if the context was loaded, false otherwise
	 */
	public static boolean updateContext(final DSPServer server, final long date) {
		final ServerContextCache cache = server.getContextCache();
		if (cache == null)
			return false;
		final ServerModelContext context = (ServerModelContext) server.getContextCache().getContext(date, false);
		if (context == null)
			return false;
		updateContext(server, context);
		return true;
	}

	/**
	 * Gets (and removes) the next due request 
	 */
	public static ContextUpdateRequest getNextRequest() {
		final long now = System.currentTimeMillis();
		synchronized (UPDATE_REQUEST_QUEUE) {
			final int size = UPDATE_REQUEST_QUEUE.size();
			for (int i=0; i<size; ++i) {
				final ContextUpdateRequest request = UPDATE_REQUEST_QUEUE.get(i);
				if (request.dueWhen < now) {
					UPDATE_REQUEST_QUEUE.remove(i);
					return request;
				}
			}
		}
		return null;
	}

	/**
	 * Requests an immediate update of a week.
	 * The request is only processed for weeks that are currently loaded.
	 */
	public static void enqeueContextUpdate(final long date) {
		if (TimestampUtil.isNull(date))
			return;
		final ContextUpdateRequest request = new ContextUpdateRequest(date);
		synchronized (UPDATE_REQUEST_QUEUE) {
			UPDATE_REQUEST_QUEUE.remove(request);
			UPDATE_REQUEST_QUEUE.add(request);
		}
	}

	/**
	 * Removes a context update request
	 */
	public static void removeContextUpdate(final long date) {
		if (TimestampUtil.isNull(date))
			return;
		final ContextUpdateRequest request = new ContextUpdateRequest(date);
		synchronized (UPDATE_REQUEST_QUEUE) {
			UPDATE_REQUEST_QUEUE.remove(request);
		}
	}	

}

final class ContextUpdateRequest {

	public final long dueWhen;
	public final long contextDate;

	public ContextUpdateRequest(final long contextDate) {
		this.dueWhen = System.currentTimeMillis() + 3500L;
		this.contextDate = TimestampUtil.findMonday(contextDate, 0);
	}

	@Override
	public boolean equals(final Object o) {
		return o instanceof ContextUpdateRequest && ((ContextUpdateRequest) o).contextDate == contextDate;
	}
	
	@Override
	public int hashCode() {
		return TimestampUtil.hashCode(contextDate);
	}

}
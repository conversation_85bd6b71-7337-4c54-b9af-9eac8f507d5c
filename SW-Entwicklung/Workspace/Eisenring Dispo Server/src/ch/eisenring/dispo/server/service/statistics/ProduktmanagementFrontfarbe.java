package ch.eisenring.dispo.server.service.statistics;

import ch.eisenring.core.collections.List;
import ch.eisenring.core.csv.CSVFile;
import ch.eisenring.core.csv.CSVLine;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.iterators.Filter;
import ch.eisenring.dispo.server.DSPServer;
import ch.eisenring.dispo.shared.model.AbstractModel;
import ch.eisenring.dispo.shared.model.Auftrag;
import ch.eisenring.logiware.code.pseudo.AbwicklungsartCode;
import ch.eisenring.statistics.shared.network.PacketStatisticRequest;
import ch.eisenring.statistics.shared.network.PacketStatisticResponse;

public class ProduktmanagementFrontfarbe extends KWStatisticBase {

	public ProduktmanagementFrontfarbe(final DSPServer server) {
		super(server);
	}

	@SuppressWarnings("unchecked")
	@Override
	public PacketStatisticResponse createResponse(final PacketStatisticRequest request) {
		init(request);

		final List<Auftrag> auftragList = (List) getModelList(new Filter<AbstractModel>() {
			@Override
			public boolean accepts(final AbstractModel model) {
				if (model instanceof Auftrag) {
					final Auftrag auftrag = (Auftrag) model;
					final AbwicklungsartCode abwArt = auftrag.getAbwicklungsart();
					return AbwicklungsartCode.AWA135.equals(abwArt);
				}
				return false;
			}
		});

		final CSVFile csvFile = new CSVFile();
		CSVLine csvLine = csvFile.addLine();
		csvLine.setColumn(0, "Projekt-Nr.");
		csvLine.setColumn(1, "Projekt-Bez.");
		csvLine.setColumn(2, "Auftrag-Nr.");
		csvLine.setColumn(3, "Sabe");
		csvLine.setColumn(4, "Ausführung");
		csvLine.setColumn(5, "Möbelteile");
		csvLine.setColumn(6, "Frontfarbe");
		csvLine.setColumn(7, "Frontkantenfarbe");

		for (final Auftrag auftrag : auftragList) {
			csvLine = csvFile.addLine();
			csvLine.setColumn(0, auftrag.getProjektnummer());
			csvLine.setColumn(1, Strings.toSingleLine(auftrag.getProjektbezeichnung()));
			csvLine.setColumn(2, auftrag.getAuftragnummer());
			csvLine.setColumn(3, auftrag.getSachbearbeiter());
			csvLine.setColumn(4, auftrag.getAusfuehrung());
			csvLine.setColumn(5, auftrag.getMoebelTeile());
			csvLine.setColumn(6, auftrag.getFrontFarbe());
			csvLine.setColumn(7, auftrag.getFrontKantenFarbe());
		}

		// create reply
		final PacketStatisticResponse result = PacketStatisticResponse.create(request);

		// --- chart title
//		String title = DateGranularityCode.getDateRangeText(result);
//		title = request.getType()+"\n("+title+")";
//		result.setChartTitle(title);

		// --- values
//		double[] data = getValues(dataList);
//		DataPoint.round(data);
//		result.setDataSeries(new double[][] { data });

		// --- labels
//		String[] labels = getLabels(dataList);
//		result.setSeriesLabels(labels);
//		result.setXAxisLabel("Wert (100 = 1 Personenwoche)");
//		result.setYAxisLabel("Monteur");
		
		// --- CSV file
		result.addAttachment("Produktmanagement_Frontfarben.csv", csvFile.toBinary(null));
		return result;
	}

}

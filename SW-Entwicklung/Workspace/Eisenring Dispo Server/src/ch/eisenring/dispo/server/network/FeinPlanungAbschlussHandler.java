package ch.eisenring.dispo.server.network;

import ch.eisenring.dispo.server.DSPServer;
import ch.eisenring.dispo.server.service.FeinPlanungAbschlussService;
import ch.eisenring.dispo.server.service.ServiceBase;
import ch.eisenring.dispo.shared.network.packets.PacketFeinPlanungAbschluss;
import ch.eisenring.network.PacketSink;
import ch.eisenring.network.implementation.PacketDispatchMode;
import ch.eisenring.network.packet.AbstractPacket;

public final class FeinPlanungAbschlussHandler extends AbstractDSPPacketHandler {

	FeinPlanungAbschlussHandler(final DSPServer server) {
		super(server, PacketFeinPlanungAbschluss.class, PacketDispatchMode.ASYNCHRONOUS);
	}
	
	@Override
	public void handle(final AbstractPacket abstractPacket, final PacketSink sink) {
		final PacketFeinPlanungAbschluss packet = (PacketFeinPlanungAbschluss) abstractPacket;
		final ServiceBase service = new FeinPlanungAbschlussService(server, packet, sink);
		service.performService();
	}
	
}

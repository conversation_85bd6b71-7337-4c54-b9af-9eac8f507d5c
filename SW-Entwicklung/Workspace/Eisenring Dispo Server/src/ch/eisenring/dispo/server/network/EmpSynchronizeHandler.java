package ch.eisenring.dispo.server.network;

import ch.eisenring.core.logging.Logger;
import ch.eisenring.core.logging.Logger.LogLevel;
import ch.eisenring.dispo.server.DSPServer;
import ch.eisenring.dispo.server.ticklisteners.ServerEmployeeUpdater;
import ch.eisenring.dispo.shared.network.packets.PacketEmpSynchronize;
import ch.eisenring.network.PacketSink;
import ch.eisenring.network.implementation.PacketDispatchMode;
import ch.eisenring.network.packet.AbstractPacket;

public final class EmpSynchronizeHandler extends AbstractDSPPacketHandler {

	EmpSynchronizeHandler(final DSPServer server) {
		super(server, PacketEmpSynchronize.class, PacketDispatchMode.ASYNCHRONOUS);
	}

	@Override
	public void handle(final AbstractPacket abstractPacket, final PacketSink sink) {
		Logger.log(LogLevel.INFO, "---> begin employee snychronization ");
		try {
			ServerEmployeeUpdater.updateEmployees(server);
		} catch (final Exception e) {
			Logger.log(LogLevel.ERROR, e);
		}
		Logger.log(LogLevel.INFO, "---> end employee synchronization");
	}

}

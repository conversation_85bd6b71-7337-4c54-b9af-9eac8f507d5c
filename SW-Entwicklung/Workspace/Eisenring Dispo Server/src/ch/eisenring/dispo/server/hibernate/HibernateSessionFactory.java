package ch.eisenring.dispo.server.hibernate;

import javax.naming.InitialContext;

import org.hibernate.HibernateException;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.hibernate.cfg.Configuration;
import org.hibernate.cfg.Environment;

import ch.eisenring.core.logging.Logger;
import ch.eisenring.dispo.shared.DSPConstants;
import ch.eisenring.jdbc.ConnectionInfo;

public final class HibernateSessionFactory {
	
	private final static Object LOCK = new Object();

	/**	
	 * Location of hibernate.cfg.xml file. NOTICE: Location should be on the
	 * classpath as Hibernate uses #resourceAsStream style lookup for its
	 * configuration file. That is place the config file in a Java package - the
	 * default location is the default Java package.<br>	 
	 * Examples: <br>
	 * <code>CONFIG_FILE_LOCATION = "/hibernate.conf.xml".
	 * CONFIG_FILE_LOCATION = "/com/foo/bar/myhiberstuff.conf.xml".</code>
	 */
	private static String CONFIG_FILE_LOCATION = "/dispo_hibernate.xml";
	
	/** The single instance of hibernate configuration */
	private final static Configuration cfg = new Configuration();
	
	/** The single instance of hibernate SessionFactory */
	private static SessionFactory INSTANCE;	
	
	private HibernateSessionFactory() {
	}
	
	/**	 
	 * initialises the configuration if not yet done and returns the current
	 * instance
	 */
	public static SessionFactory getInstance() {
		{ // bypass locking if already created
			final SessionFactory result = INSTANCE;
			if (result != null)
				return result;
		}
		synchronized (LOCK) {
			if (INSTANCE == null) {
				try {
					cfg.configure(CONFIG_FILE_LOCATION);

					// overwrite the JDBC properties from server configuration
					final ConnectionInfo cinf = DSPConstants.DSP_DATABASE.getConnectionInfo();
					cfg.setProperty("hibernate.connection.driver_class", cinf.getProperty(ConnectionInfo.KEY_DRIVER));
			        cfg.setProperty("hibernate.connection.url", cinf.getURL());
			        cfg.setProperty("hibernate.connection.username", cinf.getProperty(ConnectionInfo.KEY_USER));
			        cfg.setProperty("hibernate.connection.password", cinf.getProperty(ConnectionInfo.KEY_PASSWORD));						

					String sessionFactoryJndiName = cfg.getProperty(Environment.SESSION_FACTORY_NAME);
					if (sessionFactoryJndiName != null) {
						cfg.buildSessionFactory();
						Logger.debug("Hibernate: get a jndi session factory");
						INSTANCE = (SessionFactory) (new InitialContext()).lookup(sessionFactoryJndiName);
					} else {
						Logger.debug("Hibernate: get a classic factory");
						INSTANCE = cfg.buildSessionFactory();
					}
				} catch (Exception e) {
					Logger.fatal("%%%% Error Creating HibernateSessionFactory %%%%");
					Logger.fatal(e);
					throw new HibernateException("Could not initialize the Hibernate configuration", e);
				}
			}
		}
		return INSTANCE;
	}

	/**
	 * Returns a new Session
	 * <code>SessionFactory</code> if needed.
	 */
	public static Session openSession() {
		Logger.trace("HibernateSessionFactory::openSession()");
		return getInstance().openSession();
	}

	/**
	 * The behaviour of this method depends on the session context you have
	 * configured. This factory is intended to be used with a hibernate.cfg.xml
	 * including the following property <property	 
	 * name="current_session_context_class">thread</property> This would return
	 * the current open session or if this does not exist, will create a new
	 * session
	 */
	public static Session getCurrentSession() {
		Logger.trace("HibernateSessionFactory::getCurrentSession()");
		return getInstance().getCurrentSession();
	}	

	public static void close() {
		synchronized (LOCK) {
			if (INSTANCE != null)
				INSTANCE.close();
		}
	}

}
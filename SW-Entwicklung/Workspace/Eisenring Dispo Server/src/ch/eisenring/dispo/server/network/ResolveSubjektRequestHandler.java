package ch.eisenring.dispo.server.network;

import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.dispo.server.DSPServer;
import ch.eisenring.dispo.shared.network.packets.PacketResolveSubjektReply;
import ch.eisenring.dispo.shared.network.packets.PacketResolveSubjektRequest;
import ch.eisenring.dispo.shared.transfer.Subjekt;
import ch.eisenring.logiware.code.pseudo.SubjektCode;
import ch.eisenring.network.PacketSink;
import ch.eisenring.network.packet.AbstractPacket;

final class ResolveSubjektRequestHandler extends AbstractDSPPacketHandler {

	ResolveSubjektRequestHandler(final DSPServer server) {
		super(server, PacketResolveSubjektRequest.class);
	}

	@Override
	public void handle(final AbstractPacket packet, final PacketSink sink) {
		final PacketResolveSubjektRequest request = (PacketResolveSubjektRequest) packet;
		final PacketResolveSubjektReply reply = handle(server, request);
		sink.sendPacket(reply, false);
	}

	public static PacketResolveSubjektReply handle(final DSPServer server, final PacketResolveSubjektRequest request) {
		try {
			final List<Subjekt> result = new ArrayList<Subjekt>();
			final List<Integer> ids = request.getSubjektIds();
			final List<SubjektCode> subjekte = AbstractCode.getInstances(SubjektCode.class);
			for (final Integer key : ids) {
				for (final SubjektCode subjekt : subjekte) {
					if (key.equals(subjekt.getKey())) {
						result.add(new Subjekt(subjekt));
					}
				}
			}
			return PacketResolveSubjektReply.create(request, result);
		} catch (final Exception e) {
			return PacketResolveSubjektReply.create(request, new ErrorMessage(e));
		}

	}
	
}

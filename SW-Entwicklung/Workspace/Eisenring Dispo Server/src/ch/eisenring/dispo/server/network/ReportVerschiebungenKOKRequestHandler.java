package ch.eisenring.dispo.server.network;

import static ch.eisenring.lw.LWMapping.TM_AUFTRAG;

import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.Collections;

import ch.eisenring.core.poi.POIUtil;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;

import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.Map;
import ch.eisenring.core.collections.Set;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.collections.impl.HashMap;
import ch.eisenring.core.csv.CSVFile;
import ch.eisenring.core.csv.CSVLine;
import ch.eisenring.core.datatypes.date.DateGranularityCode;
import ch.eisenring.core.datatypes.date.Period;
import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.io.binary.BinaryHolder;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.core.poi.POIUtil;
import ch.eisenring.core.sort.Comparator;
import ch.eisenring.core.util.file.FileImage;
import ch.eisenring.core.util.file.FileUtil;
import ch.eisenring.dispo.server.DSPServer;
import ch.eisenring.dispo.server.poi.ServerPOIUtil;
import ch.eisenring.dispo.shared.network.packets.PacketReportVerschiebungenKOKReply;
import ch.eisenring.dispo.shared.network.packets.PacketReportVerschiebungenKOKRequest;
import ch.eisenring.jdbc.JDBCResultSet;
import ch.eisenring.jdbc.RowHandler;
import ch.eisenring.jdbc.StatementParameters;
import ch.eisenring.logiware.LWAuftragKey;
import ch.eisenring.logiware.LWContextSource;
import ch.eisenring.logiware.code.pseudo.AbwicklungsartCode;
import ch.eisenring.logiware.code.soft.*;
import ch.eisenring.lw.LWConstants;
import ch.eisenring.lw.meta.LWAuftragMeta;
import ch.eisenring.lw.meta.LWXEtappeMeta;
import ch.eisenring.lw.meta.LWXPAuftragMeta;
import ch.eisenring.model.TransactionContext;
import ch.eisenring.model.TransactionSource;
import ch.eisenring.network.PacketSink;
import ch.eisenring.network.packet.AbstractPacket;
import java.io.IOException;
import static ch.eisenring.lw.LWMapping.*;

class ReportVerschiebungenKOKRequestHandler extends AbstractDSPPacketHandler {

    ReportVerschiebungenKOKRequestHandler(final DSPServer server) {
        super(server, PacketReportVerschiebungenKOKRequest.class);
    }

    public void handle(final AbstractPacket abstractPacket, final PacketSink sink) {
        final PacketReportVerschiebungenKOKRequest request = (PacketReportVerschiebungenKOKRequest) abstractPacket;
        final PacketReportVerschiebungenKOKReply reply = handle(server, request);
        sink.sendPacket(reply, false);
    }

    public static PacketReportVerschiebungenKOKReply handle(final DSPServer server, final PacketReportVerschiebungenKOKRequest request) {
        try {
            final PacketReportVerschiebungenKOKReply reply = handleImpl(server, request);
            if (reply != null)
                return reply;
            return PacketReportVerschiebungenKOKReply.create(request, ErrorMessage.ERROR);
        } catch (final Exception e) {
            Logger.error(e);
            return PacketReportVerschiebungenKOKReply.create(request, new ErrorMessage(e));
        }
    }

    static class KOKPoint {
        public long kokDate;
        public long addDate;
        public int moveDistanceKW;
        public String ignoreReason;
        public String sabe;

        public void setIgnoreReason(String reason) {
            if (this.ignoreReason != null)
                return;
            this.ignoreReason = reason;
        }

        public boolean isIgnored() {
            return ignoreReason != null;
        }
    }

    static class AuftragInfo {
        public LWAuftragKey auftragKey;
        public long datWu;
        public LWGranit1Code bezeichnungBewohnt;
        public LWGranit2Code objektart;
        public LWKuechenTypCode kuechentyp;
        public Integer kuechenBestellungBelegNr;
        public String kommission;
        public String basis;
        public LWBasisobjektCode basisobjekt;
        public String PBez;
        public String StrassenBez;
        public String PLZ;
        public String Ort;
        public LWObjektBetreuerCode kommPL;
        public LWObjektBetreuerCode techPL;
        public String bfName;
        public String bfPhone;
        public String bfMobile;
        public String bfEmail;
        public String guName;
        public String guZusatzName;
        public String guZusatzBez;
        public List<KOKPoint> koks = new ArrayList<>();
        public long bestellDatum;
        public AbwicklungsartCode abwicklungsart;
        public Long bezugsDatum;
        public int etappeNo;
        public long etappeStart;
        public long etappeEnd;
        public String etappeBezeichnung;
    }

    static class AdditionalAuftragInfo {
        public LWAuftragKey auftragKey;
    }

    static class BestellInfo {
        public Integer auftragKey;
        public long bestellDatum;
    }

    static class EtappenInfo {
        public String kommission;
        public long etappeStart;
        public long etappeEnd;
        public int etappeNo;
        public String bezeichnung;
    }

    private static PacketReportVerschiebungenKOKReply handleImpl(
            final DSPServer server, final PacketReportVerschiebungenKOKRequest request) throws Exception {
        final long baseDate = DateGranularityCode.DAY.round(request.getBaseDate(), 0);
        final Map<LWAuftragKey, AuftragInfo> map = new HashMap<>();
        final TransactionSource source = new LWContextSource(LWConstants.VERTRIEB) {
            @Override
            protected void loadImpl(final TransactionContext context) throws SQLException {
                final StringMaker sql = StringMaker.obtain();
                final StatementParameters params = new StatementParameters();
                sql.append("SELECT A.");
                sql.append(TM_AUFTRAG.get(LWAuftragMeta.ATR_AUFTRAGNUMMER));
                sql.append(", A.");
                sql.append(TM_AUFTRAG.get(LWAuftragMeta.ATR_GSE_AUFTRAG));
                sql.append(", T.DatWu, T.Add_Date, T.Add_Sabe");
                sql.append(", A.");
                sql.append(TM_AUFTRAG.get(LWAuftragMeta.ATR_WUNSCHDATUM) + ", ");
                sql.append("A.");
                sql.append(TM_AUFTRAG.get(LWAuftragMeta.ATR_ABWICKLUNGSART) + ", "); // --Abwicklungsart
                sql.append("AK.Sch_Block1, "); // -- Bezeichnung Bewohnt
                sql.append("AK.Sch_Block2, "); //  --Objektart
                sql.append("AK.KUECHEN, "); //  -- Kuechentyp
                sql.append("AK.AZusatzPStk, "); // -- KuechenbestellungBelegnummer
                sql.append("AK.MDatum4, ");  // --Bezugsdatum
                sql.append("K.PAuftrnr,  ");
                sql.append("K.PAuftrnr_B, ");
                sql.append("B.PAuftrnr_B, ");
                sql.append("B.PAuftrnr, ");
                sql.append("B.Basisobjekt, ");
                sql.append("B.PBez, ");
                sql.append("B.StrassenBez, ");
                sql.append("B.PLZ, ");
                sql.append("B.Ort, ");
                sql.append("B.PGruppe, ");
                sql.append("B.PFaktPerCd, ");
                sql.append("B.Id_SUBJEKT_I, ");
                sql.append("B.Reserve9, "); //  -- BF Name
                sql.append("B.PLeiter, "); //  -- BF Phone
                sql.append("B.Reserve52, "); //  --BF Mobile
                sql.append("B.Reserve10, "); //   --BF Email
                sql.append("S.Name, "); //   -- Name GU
                sql.append("S.ZusatzName, "); //  --Zusatzname GU
                sql.append("S.ZusatzBez "); //  --Zusatz Bez GU
                // --- From
                sql.append(" FROM ");
                sql.append(TM_AUFTRAG.getTableSpecifier());
                sql.append(" A " + WITH_NOLOCK + ", ");
                sql.append(" TERMINGRUPPE T " + WITH_NOLOCK + ", ");
                sql.append(" AuftragsKopfZusatz AK " + WITH_NOLOCK + ", ");
                sql.append(" PAUFTRAG K " + WITH_NOLOCK + ", ");
                sql.append(" PAUFTRAG B " + WITH_NOLOCK + ", ");
                sql.append(" BASIS.dbo.SUBJEKT S " + WITH_NOLOCK);
                sql.append(" WHERE ");
                // --- table join conditions
                sql.append(" A.");
                sql.append(TM_AUFTRAG.get(LWAuftragMeta.ATR_AUFTRAGNUMMER));
                sql.append(" = T.AuftrNr");
                sql.append(" AND A.");
                sql.append(TM_AUFTRAG.get(LWAuftragMeta.ATR_GSE_AUFTRAG));
                sql.append(" = T.Id_GSE_Auftrag");
                sql.append(" AND A.AuftrNr = AK.AuftrNr ");
                sql.append(" AND A.PAuftrNr = K.PAuftrNr ");
                sql.append(" AND K.PAuftrnr_B = B.PAuftrnr ");
                sql.append("AND B.Id_SUBJEKT_I = S.id ");
                // --- NOT storno
                sql.append(" AND A.");
                sql.append(TM_AUFTRAG.get(LWAuftragMeta.ATR_STATUSCODE));
                sql.append(" != ?");
                params.add(LWAuftragStatusCode.STORNIERT);
                { // --- nur Aufträge mit DatWu in Zukunft
                    sql.append(" AND A.");
                    sql.append(TM_AUFTRAG.get(LWAuftragMeta.ATR_WUNSCHDATUM));
                    sql.append(" >= ?");
                    params.add(TimestampUtil.toDate(DateGranularityCode.DAY.round(baseDate, 0), Timestamp.class));
                }
                // --- AWA condition
                {
                    sql.append(" AND A.");
                    sql.append(TM_AUFTRAG.get(LWAuftragMeta.ATR_ABWICKLUNGSART));
                    sql.append(" IN ");
                    final Set<AbwicklungsartCode> awa = request.getAbwicklungsarten();
                    prepareIn(sql, awa);
                    params.addAll(awa);
                }
                // --- TerminCD (Gruppe)
                {
                    sql.append(" AND T.TerminCD IN ");
                    final Set<LWTerminCD> gruppen = request.getGruppen();
                    prepareIn(sql, gruppen);
                    params.addAll(gruppen);
                }
                // --- sorting
                {
                    sql.append(" ORDER BY");
                    sql.append(" A.");
                    sql.append(TM_AUFTRAG.get(LWAuftragMeta.ATR_AUFTRAGNUMMER));
                    sql.append(", A.");
                    sql.append(TM_AUFTRAG.get(LWAuftragMeta.ATR_GSE_AUFTRAG));
                    sql.append(", T.Add_Date");
                }


                doQuery(sql.release(), params, new RowHandler() {
                    @Override
                    public void handleRow(final JDBCResultSet resultSet) throws SQLException {
                        final int auftragNummer = resultSet.getInt(1);
                        final GSECode gse = resultSet.getCode(2, GSECode.class);
                        final LWAuftragKey auftragKey = LWAuftragKey.get(auftragNummer, gse);


                        // find auftrag
                        AuftragInfo info = map.get(auftragKey);
                        if (info == null) {
                            info = new AuftragInfo();
                            info.auftragKey = auftragKey;
                            info.abwicklungsart = resultSet.getCode(7, AbwicklungsartCode.class);
                            info.bezeichnungBewohnt = resultSet.getCode(8, LWGranit1Code.class);
                            info.objektart = resultSet.getCode(9, LWGranit2Code.class);
                            info.kuechentyp = resultSet.getCode(10, LWKuechenTypCode.class);
                            info.kuechenBestellungBelegNr = resultSet.getInteger(11);
                            info.bezugsDatum = resultSet.getLongTimestamp(12);
                            info.kommission = resultSet.getString(13);
                            info.basis = resultSet.getString(14);
                            info.basisobjekt = resultSet.getCode(17, LWBasisobjektCode.class);
                            info.PBez = resultSet.getString(18);
                            info.StrassenBez = resultSet.getString(19);
                            info.PLZ = resultSet.getString(20);
                            info.Ort = resultSet.getString(21);
                            info.kommPL = resultSet.getCode(22, LWObjektBetreuerCode.class);
                            info.techPL = resultSet.getCode(23, LWObjektBetreuerCode.class);
                            info.bfName = resultSet.getString(25);
                            info.bfPhone = resultSet.getString(26);
                            info.bfMobile = resultSet.getString(27);
                            info.bfEmail = resultSet.getString(28);
                            info.guName = resultSet.getString(29);
                            info.guZusatzName = resultSet.getString(30);
                            info.guZusatzBez = resultSet.getString(31);
                            map.put(auftragKey, info);
                        }
                        final long kokDate = DateGranularityCode.KW.round(resultSet.getLongTimestamp(3), 0);
                        final long addDate = DateGranularityCode.DAY.round(resultSet.getLongTimestamp(4), 0);
                        final String sabe = resultSet.getString(5);
                        final long datWu = resultSet.getLongTimestamp(6);
                        final KOKPoint kok = new KOKPoint();
                        kok.kokDate = kokDate;
                        kok.addDate = addDate;
                        kok.sabe = sabe;
                        info.datWu = datWu;
                        info.koks.add(kok);
                    }
                });
            }
        };
        final TransactionContext context = new TransactionContext();
        context.load(source);

        // mark KOK movements on same day as invalid
        final Period last7Days = Period.create(DateGranularityCode.DAY.round(baseDate, -7),
                DateGranularityCode.DAY.round(baseDate, 0));
        prepareKOKs(map.values(), last7Days);
        prepareMoveDistances(map.values());
        addBestellDatum(server, request, map);
        addEtappeInfo(server, request, map);

        // Report ausgeben
        final CSVFile csvFile = new CSVFile();

        { // header line
            final CSVLine csvLine = csvFile.addLine();
            int x = -1;
            ++x;
            csvLine.setColumn(++x, "'1-2 KW");
            csvLine.setColumn(++x, "'3-8 KW");
            csvLine.setColumn(++x, "'9-20 KW");
            csvLine.setColumn(++x, "'>20 KW");
        }

		// verschiebungen
		{
			final Map<Integer, Integer> bucketMap = new HashMap<>();
			for (final AuftragInfo auftrag : map.values()) {
				for (final KOKPoint kok : auftrag.koks) {
					if (kok.isIgnored())
						continue;
					int bucketNo = getKWBucket(baseDate, kok.kokDate);
					incrementBucket(bucketMap, Math.abs(bucketNo));
				}
			}
			final CSVLine csvLine = csvFile.addLine();
			int x = -1;
			csvLine.setColumn(++x, "Verschiebungen");
			csvLine.setColumn(++x, bucketMap.get(1));
			csvLine.setColumn(++x, bucketMap.get(2));
			csvLine.setColumn(++x, bucketMap.get(3));
			csvLine.setColumn(++x, bucketMap.get(4));
		}
	
		// anzahlen
		{
			final Map<Integer, Integer> bucketMap = getAnzahlAuftraege(server, request);
			final CSVLine csvLine = csvFile.addLine();
			int x = -1;
			csvLine.setColumn(++x, "Anzahlen");
			csvLine.setColumn(++x, bucketMap.get(1));
			csvLine.setColumn(++x, bucketMap.get(2));
			csvLine.setColumn(++x, bucketMap.get(3));
			csvLine.setColumn(++x, bucketMap.get(4));
		}
	
		// produce XLS
		final Workbook workbook = ServerPOIUtil.loadWorkbook(server, "dsp/misc-data/VerschiebungenKOK-Template.xlsx");
		final Sheet sheet = workbook.getSheetAt(0);
		POIUtil.popuplateSheet(sheet, csvFile);
		// make header gray
		POIUtil.setFillForegroundColor(sheet, IndexedColors.GREY_25_PERCENT.getIndex(), 0, 0, csvFile.getColumnCount(), 1);
		POIUtil.addBorder(sheet, IndexedColors.BLACK.getIndex(), 0, 0, csvFile.getColumnCount(), 1);

        dumpData(workbook.getSheetAt(1), map.values());

		final BinaryHolder filedata = POIUtil.toBinary(workbook);
		String filename = "Auswertung_VerschiebungenKOK";
		filename = Strings.concat(FileUtil.sanitizeFilename(filename), ".xlsx");
		final FileImage fileimage = FileImage.create(filename, filedata);
		return PacketReportVerschiebungenKOKReply.create(request, fileimage);
	}


    private static void addBestellDatum(final DSPServer server, PacketReportVerschiebungenKOKRequest request, Map<LWAuftragKey, AuftragInfo> map) throws Exception {
        Map<Integer, BestellInfo> bestellInfoMap = getAdditionalPurchaseOrderData(server, request);
        for (AuftragInfo auftragInfo : map.values()) {
            BestellInfo bestellInfo = bestellInfoMap.get(auftragInfo.kuechenBestellungBelegNr);
            if (bestellInfo != null) {
                auftragInfo.bestellDatum = bestellInfo.bestellDatum;
            }
        }
    }

    private static void addEtappeInfo(DSPServer server, PacketReportVerschiebungenKOKRequest request, Map<LWAuftragKey, AuftragInfo> map) throws IOException {
        Map<String, EtappenInfo> etappenInfoMap = getEtappeDaten(server, request);
        for (AuftragInfo auftragInfo : map.values()) {
            EtappenInfo etappenInfo = etappenInfoMap.get(auftragInfo.kommission);
            if (etappenInfo != null) {
                auftragInfo.etappeNo = etappenInfo.etappeNo;
                auftragInfo.etappeStart = etappenInfo.etappeStart;
                auftragInfo.etappeEnd = etappenInfo.etappeEnd;
                auftragInfo.etappeBezeichnung = etappenInfo.bezeichnung;
            }
        }
    }

    /**
     * Dumps out data list to excel sheet
     */
    static void dumpData(final Sheet sheet, final java.util.Collection<AuftragInfo> auftraege0) {
        final List<AuftragInfo> auftraege = new ArrayList<>(auftraege0);
        Collections.sort(auftraege, new Comparator<AuftragInfo>() {
            @Override
            public int compare(final AuftragInfo o1, final AuftragInfo o2) {
                return o1.auftragKey.compareTo(o2.auftragKey);
            }
        });
        int y = 1;
        for (final AuftragInfo auftrag : auftraege) {
            KOKPoint pred = null;
            for (final KOKPoint kok : auftrag.koks) {
//				if (kok.isIgnored())
//					continue;
                Cell cell;
                int columnIndex = 0;
                cell = POIUtil.getOrCreateCell(sheet, columnIndex, y);
                cell.setCellValue(Strings.toString(auftrag.auftragKey.getAuftragnummer()));
                if (pred != null) {
                    cell = POIUtil.getOrCreateCell(sheet, ++columnIndex, y);
                    cell.setCellValue(TimestampUtil.DATE10.format(pred.kokDate));
                } else {
                    ++columnIndex;
                }
                cell = POIUtil.getOrCreateCell(sheet, ++columnIndex, y);
                cell.setCellValue(TimestampUtil.DATE10.format(kok.kokDate));
                cell = POIUtil.getOrCreateCell(sheet, ++columnIndex, y);
                cell.setCellValue(Strings.trim(kok.sabe));
                cell = POIUtil.getOrCreateCell(sheet, ++columnIndex, y);
                cell.setCellValue(TimestampUtil.DATE10.format(kok.addDate));
                cell = POIUtil.getOrCreateCell(sheet, ++columnIndex, y);
                cell.setCellValue(kok.moveDistanceKW);
                if (kok.isIgnored()) {
                    cell = POIUtil.getOrCreateCell(sheet, ++columnIndex, y);
                    cell.setCellValue(kok.ignoreReason);
                } else {
                    ++columnIndex;
                }
                //Additional Data
                cell = POIUtil.getOrCreateCell(sheet, ++columnIndex, y);
                cell.setCellValue(auftrag.kommission == null ? "" : auftrag.kommission);
                cell = POIUtil.getOrCreateCell(sheet, ++columnIndex, y);
                cell.setCellValue(auftrag.abwicklungsart.getShortText());
                cell = POIUtil.getOrCreateCell(sheet, ++columnIndex, y);
                cell.setCellValue(auftrag.datWu <= 0 ? "" : TimestampUtil.DATE10.format(auftrag.datWu));
                cell = POIUtil.getOrCreateCell(sheet, ++columnIndex, y);
                cell.setCellValue(auftrag.etappeNo <= 0 ? "" : "#" + auftrag.etappeNo);
                cell = POIUtil.getOrCreateCell(sheet, ++columnIndex, y);
                cell.setCellValue(auftrag.etappeStart <= 0 ? "" : TimestampUtil.DATE10.format(auftrag.etappeStart));
                cell = POIUtil.getOrCreateCell(sheet, ++columnIndex, y);
                cell.setCellValue(auftrag.etappeEnd <= 0 ? "" : TimestampUtil.DATE10.format(auftrag.etappeEnd));
                cell = POIUtil.getOrCreateCell(sheet, ++columnIndex, y);
                cell.setCellValue(auftrag.etappeBezeichnung);
                cell = POIUtil.getOrCreateCell(sheet, ++columnIndex, y);
                cell.setCellValue(auftrag.bezugsDatum <= 0 ? "" : TimestampUtil.DATE10.format(auftrag.bezugsDatum));
                cell = POIUtil.getOrCreateCell(sheet, ++columnIndex, y);
                cell.setCellValue(auftrag.bestellDatum <= 0 ? "" : TimestampUtil.DATE10.format(auftrag.bestellDatum));
                cell = POIUtil.getOrCreateCell(sheet, ++columnIndex, y);
                cell.setCellValue(auftrag.kuechenBestellungBelegNr == null ? "" : auftrag.kuechenBestellungBelegNr.toString());
                cell = POIUtil.getOrCreateCell(sheet, ++columnIndex, y);
                cell.setCellValue(auftrag.bezeichnungBewohnt.getLongText());
                cell = POIUtil.getOrCreateCell(sheet, ++columnIndex, y);
                cell.setCellValue(auftrag.objektart.getLongText());
                cell = POIUtil.getOrCreateCell(sheet, ++columnIndex, y);
                cell.setCellValue(auftrag.kuechentyp.getLongText());
                cell = POIUtil.getOrCreateCell(sheet, ++columnIndex, y);
                cell.setCellValue(auftrag.basis);
                cell = POIUtil.getOrCreateCell(sheet, ++columnIndex, y);
                cell.setCellValue(auftrag.basisobjekt.getLongText());
                cell = POIUtil.getOrCreateCell(sheet, ++columnIndex, y);
                cell.setCellValue(auftrag.PBez == null ? "" : auftrag.PBez.replaceAll("[\\r\\n ]+", " "));
                cell = POIUtil.getOrCreateCell(sheet, ++columnIndex, y);
                cell.setCellValue(auftrag.StrassenBez);
                cell = POIUtil.getOrCreateCell(sheet, ++columnIndex, y);
                cell.setCellValue(auftrag.PLZ);
                cell = POIUtil.getOrCreateCell(sheet, ++columnIndex, y);
                cell.setCellValue(auftrag.Ort);
                cell = POIUtil.getOrCreateCell(sheet, ++columnIndex, y);
                cell.setCellValue(auftrag.kommPL.getLongText());
                cell = POIUtil.getOrCreateCell(sheet, ++columnIndex, y);
                cell.setCellValue(auftrag.techPL.getLongText());
                cell = POIUtil.getOrCreateCell(sheet, ++columnIndex, y);
                cell.setCellValue(auftrag.bfName);
                cell = POIUtil.getOrCreateCell(sheet, ++columnIndex, y);
                cell.setCellValue(auftrag.bfPhone);
                cell = POIUtil.getOrCreateCell(sheet, ++columnIndex, y);
                cell.setCellValue(auftrag.bfMobile);
                cell = POIUtil.getOrCreateCell(sheet, ++columnIndex, y);
                cell.setCellValue(auftrag.bfEmail);
                cell = POIUtil.getOrCreateCell(sheet, ++columnIndex, y);
                cell.setCellValue(auftrag.guName);
                cell = POIUtil.getOrCreateCell(sheet, ++columnIndex, y);
                cell.setCellValue(auftrag.guZusatzName);
                cell = POIUtil.getOrCreateCell(sheet, ++columnIndex, y);
                cell.setCellValue(auftrag.guZusatzBez);
                pred = kok;
                ++y;
            }
        }
    }

    /**
     * Filter / Calculate KOK data
     */
    static void prepareKOKs(final Iterable<AuftragInfo> auftraege, final Period last7Days) {
        for (final AuftragInfo auftrag : auftraege) {
            final List<KOKPoint> points = auftrag.koks;
            long succDate = TimestampUtil.NULL_TIMESTAMP;
            for (int i = points.size() - 1; i >= 0; --i) {
                final KOKPoint kok = points.get(i);
                final long addDate = DateGranularityCode.DAY.round(kok.addDate, 0);
                if (i == 0) {
                    kok.setIgnoreReason("Eröff");
                } else if (addDate == succDate) {
                    kok.setIgnoreReason("24h");
                } else if (last7Days.intersects(kok.addDate)) {
                    succDate = addDate;
                } else {
                    kok.setIgnoreReason("7d");
                }
            }
        }
    }

    /**
     * Calculate movement distance per KOK
     */
    static void prepareMoveDistances(final Iterable<AuftragInfo> auftraege) {
        for (final AuftragInfo auftrag : auftraege) {
            final List<KOKPoint> points = auftrag.koks;
            long predDatWu = TimestampUtil.NULL_TIMESTAMP;
            for (int i = 0; i < points.size(); ++i) {
                final KOKPoint kok = points.get(i);
                if (TimestampUtil.isNull(predDatWu)) {
                    kok.moveDistanceKW = 0;
                    predDatWu = kok.kokDate;
                } else {
                    kok.moveDistanceKW = Math.abs(getKWDistance(predDatWu, kok.kokDate));
                    if (kok.moveDistanceKW <= 0)
                        kok.setIgnoreReason("dKW=0");
                    predDatWu = kok.kokDate;
                }
            }
        }
    }

    /**
     * Calculates the number of calendar weeks between base and probe time.
     * <p>
     * Note that the implementation is terribly inefficient (iterative).
     */
    static int getKWDistance(long base, long probe) {
        base = DateGranularityCode.KW.round(base, 0);
        probe = DateGranularityCode.KW.round(probe, 0);
        int delta = 0;
        int direction = base < probe ? -1 : 1;
        while (base != probe) {
            probe = DateGranularityCode.KW.round(probe, direction);
            delta -= direction;
        }
        return delta;
    }

    /**
     * Selects bucket for date pair (between 0 and 4)
     */
    static int getKWBucket(long base, long probe) {
        final int d = getKWDistance(base, probe);
        if (d < 1) {
            return 0;
        } else if (d < 3) {
            return 1;
        } else if (d < 9) {
            return 2;
        } else if (d < 20) {
            return 3;
        } else {
            return 4;
        }
    }

    static void incrementBucket(final Map<Integer, Integer> map, final Integer bucketNo) {
        final Integer oldValue = map.get(bucketNo);
        final int newValue = (oldValue == null ? 0 : oldValue.intValue()) + 1;
        map.put(bucketNo, Integer.valueOf(newValue));
    }

    static Map<Integer, Integer> getAnzahlAuftraege(final DSPServer server, PacketReportVerschiebungenKOKRequest request) throws Exception {
        final Map<Integer, Integer> countMap = new HashMap<>();
        final long baseDate = DateGranularityCode.DAY.round(request.getBaseDate(), 0);
        final TransactionSource source = new LWContextSource(LWConstants.VERTRIEB) {
            @Override
            protected void loadImpl(final TransactionContext context) throws SQLException {
                final StringMaker sql = StringMaker.obtain();
                final StatementParameters params = new StatementParameters();
                sql.append("SELECT A.");
                sql.append(TM_AUFTRAG.get(LWAuftragMeta.ATR_WUNSCHDATUM));
                // --- From
                sql.append(" FROM ");
                sql.append(TM_AUFTRAG.getTableSpecifier());
                sql.append(" A " + WITH_NOLOCK);
                sql.append(" WHERE ");
                // --- NOT storno
                sql.append(TM_AUFTRAG.get(LWAuftragMeta.ATR_STATUSCODE));
                sql.append(" != ?");
                params.add(LWAuftragStatusCode.STORNIERT);
                { // --- nur Aufträge mit DatWu in Zukunft
                    sql.append(" AND A.");
                    sql.append(TM_AUFTRAG.get(LWAuftragMeta.ATR_WUNSCHDATUM));
                    sql.append(" >= ?");
                    params.add(TimestampUtil.toDate(DateGranularityCode.DAY.round(baseDate, 0), Timestamp.class));
                }
                // --- AWA condition
                {
                    sql.append(" AND A.");
                    sql.append(TM_AUFTRAG.get(LWAuftragMeta.ATR_ABWICKLUNGSART));
                    sql.append(" IN ");
                    final Set<AbwicklungsartCode> awa = request.getAbwicklungsarten();
                    prepareIn(sql, awa);
                    params.addAll(awa);
                }
                doQuery(sql.release(), params, new RowHandler() {
                    @Override
                    public void handleRow(final JDBCResultSet resultSet) throws SQLException {
                        final long wunschDatum = resultSet.getLongDate(1);
                        final Integer bucketNo = getKWBucket(baseDate, wunschDatum);
                        Integer count = countMap.get(bucketNo);
                        if (count == null) {
                            countMap.put(bucketNo, Integer.valueOf(1));
                        } else {
                            countMap.put(bucketNo, Integer.valueOf(count.intValue() + 1));
                        }
                    }
                });
            }
        };
        final TransactionContext context = new TransactionContext();
        context.load(source);
        return countMap;
    }

    static Map<Integer, BestellInfo> getAdditionalPurchaseOrderData(final DSPServer server, PacketReportVerschiebungenKOKRequest request) throws Exception {
        final Map<Integer, BestellInfo> bestellInfoMap = new HashMap<>();
        final long baseDate = DateGranularityCode.DAY.round(request.getBaseDate(), 0);

        final TransactionSource source = new LWContextSource(LWConstants.VERTRIEB) {
            @Override
            protected void loadImpl(final TransactionContext context) throws SQLException {
                final StringMaker sql = StringMaker.obtain();
                final StatementParameters params = new StatementParameters();
                sql.append("SELECT ");
                sql.append("B.AuftrNr, ");
                sql.append("B.AufDat ");
                sql.append("FROM ");
                sql.append(TM_AUFTRAG.getTableSpecifier() + " B " + WITH_NOLOCK + ", ");
                sql.append(TM_AUFTRAG.getTableSpecifier() + " A " + WITH_NOLOCK);
                sql.append(" WHERE ");
                // --- Link to Main Order
                {
                     sql.append("B." + TM_AUFTRAG.get(LWAuftragMeta.ATR_PROJEKTNUMMER) + " =  A." + TM_AUFTRAG.get(LWAuftragMeta.ATR_PROJEKTNUMMER));
                     sql.append(" AND ");
                     sql.append("A." + TM_AUFTRAG.get(LWAuftragMeta.ATR_WUNSCHDATUM));
                     sql.append(" >= ?");
                     params.add(TimestampUtil.toDate(DateGranularityCode.DAY.round(baseDate, 0), Timestamp.class));
                     sql.append(" AND ");
                     sql.append("A." + TM_AUFTRAG.get(LWAuftragMeta.ATR_STATUSCODE));
                     sql.append(" != ?");
                     params.add(LWAuftragStatusCode.STORNIERT);
                     sql.append(" AND ");
                     sql.append("A." + TM_AUFTRAG.get(LWAuftragMeta.ATR_ABWICKLUNGSART));
                    sql.append(" IN ");
                    final Set<AbwicklungsartCode> awa = request.getAbwicklungsarten();
                    prepareIn(sql, awa);
                    params.addAll(awa);
                }
                // --- NOT storno
                {
                    sql.append(" AND ");
                    sql.append(" B." + TM_AUFTRAG.get(LWAuftragMeta.ATR_STATUSCODE));
                    sql.append(" != ?");
                    params.add(LWAuftragStatusCode.STORNIERT);
                }
                // --- AWA condition
                {
                    sql.append(" AND ");
                    sql.append("B." + TM_AUFTRAG.get(LWAuftragMeta.ATR_ABWICKLUNGSART));
                    sql.append(" = ?");
                    params.add("500");
                }

                doQuery(sql.release(), params, new RowHandler() {
                    @Override
                    public void handleRow(final JDBCResultSet resultSet) throws SQLException {
                        final Integer auftragNummer = resultSet.getInt(1);
                        final long aufDat = resultSet.getLongDate(2);
                        BestellInfo bestellInfo = new BestellInfo();
                        bestellInfo.auftragKey = auftragNummer;
                        bestellInfo.bestellDatum = aufDat;
                        bestellInfoMap.put(bestellInfo.auftragKey, bestellInfo);
                    }
                });
            }
        };
        final TransactionContext context = new TransactionContext();
        context.load(source);
        return bestellInfoMap;
    }

    static Map<String, EtappenInfo> getEtappeDaten(final DSPServer server, PacketReportVerschiebungenKOKRequest request) throws IOException {
        final Map<String, EtappenInfo> etappeMap = new HashMap<>();
        final long baseDate = DateGranularityCode.DAY.round(request.getBaseDate(), 0);
        final TransactionSource source = new LWContextSource(LWConstants.LWX) {
            @Override
            protected void loadImpl(final TransactionContext context) throws SQLException {
                final StringMaker sql = StringMaker.obtain();
                final StatementParameters params = new StatementParameters();
                sql.append("SELECT ");
                sql.append("P.");
                sql.append(TM_LWXPAUFTRAG.get(LWXPAuftragMeta.ATR_PROJEKTNUMMER) + ", ");
                sql.append(TM_LWXETAPPE.get(LWXEtappeMeta.ATR_ETAPPESTART) + ", ");
                sql.append(TM_LWXETAPPE.get(LWXEtappeMeta.ATR_ETAPPEENDE) + ", ");
                sql.append(TM_LWXETAPPE.get(LWXEtappeMeta.ATR_ORDINALNUMBER) + ", ");
                sql.append(TM_LWXETAPPE.get(LWXEtappeMeta.ATR_BEZEICHNUNG) + " ");
                sql.append("FROM ");
                sql.append(TM_LWXPAUFTRAG.getTableSpecifier() + " P " + WITH_NOLOCK + ", ");
                sql.append(TM_LWXETAPPE.getTableSpecifier() + " E " + WITH_NOLOCK + " ");
                sql.append(" WHERE ");
                sql.append(TM_LWXPAUFTRAG.get(LWXPAuftragMeta.ATR_ETAPPEID));
                sql.append(" = ");
                sql.append(TM_LWXETAPPE.get(LWXEtappeMeta.ATR_ROWID));
                sql.append(" and ");
                sql.append(TM_LWXETAPPE.get(LWXEtappeMeta.ATR_ETAPPEENDE));
                sql.append(" >= ?;");
                params.add(TimestampUtil.toDate(DateGranularityCode.DAY.round(baseDate, 0), Timestamp.class));

                doQuery(sql.release(), params, new RowHandler() {
                    @Override
                    public void handleRow(final JDBCResultSet resultSet) throws SQLException {
                        EtappenInfo etappenInfo = new EtappenInfo();
                        etappenInfo.kommission = resultSet.getString(1);
                        etappenInfo.etappeStart = resultSet.getLongDate(2);
                        etappenInfo.etappeEnd = resultSet.getLongDate(3);
                        if (resultSet.getInteger(4) != null) {
                            etappenInfo.etappeNo = resultSet.getInteger(4);
                        } else {
                            etappenInfo.etappeNo = 0;
                        }
                        etappenInfo.bezeichnung = resultSet.getString(5);
                        etappeMap.put(etappenInfo.kommission, etappenInfo);
                    }
                });

            }
        };
        final TransactionContext context = new TransactionContext();
        context.load(source);
        return etappeMap;
    }

}

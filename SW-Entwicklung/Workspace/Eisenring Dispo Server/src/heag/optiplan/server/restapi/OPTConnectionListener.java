package heag.optiplan.server.restapi;

import java.io.IOException;
import java.net.Socket;

import ch.eisenring.app.server.network.ServerSocketListener;
import ch.eisenring.network.NetworkConnection;
import ch.eisenring.network.SocketOptions;
import ch.eisenring.network.security.NetworkSecurityManager;
import heag.optiplan.server.OPTServer;

public class OPTConnectionListener extends ServerSocketListener<OPTServer> {
	
	public OPTConnectionListener(final OPTServer server) {
		// the security manager is not used for this listener
		super("OPTConnectionListener", server, NetworkSecurityManager.DENY_ALL);
	}

	@Override
	protected NetworkConnection createConnection(final Socket socket) throws IOException {
		return new OPTConnection(server, socket, SocketOptions.SERVER_OPTIONS);
	}

}

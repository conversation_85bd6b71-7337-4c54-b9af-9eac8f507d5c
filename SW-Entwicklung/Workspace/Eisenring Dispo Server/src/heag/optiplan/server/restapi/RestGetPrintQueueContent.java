package heag.optiplan.server.restapi;

import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.http.HTTPField;
import ch.eisenring.core.http.HTTPMethod;
import ch.eisenring.core.http.HTTPResponse;
import ch.eisenring.core.http.HTTPStatus;
import ch.eisenring.core.http.server.MethodHandler;
import ch.eisenring.core.http.server.RequestContext;
import ch.eisenring.core.http.server.URIHandler;
import ch.eisenring.logiware.LWAuftragKey;
import ch.eisenring.print.shared.PrintServiceManager;
import ch.eisenring.print.shared.model.PrintServiceIdentifier;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import heag.optiplan.server.OPTConstants;
import heag.optiplan.server.OPTServer;
import heag.optiplan.server.printqueue.PrintRequest;
import heag.optiplan.server.printqueue.PrintStatusCode;

public class RestGetPrintQueueContent implements URIHandler, MethodHandler {

    private final OPTServer server;

    public RestGetPrintQueueContent(final OPTServer server) {
        this.server = server;
    }

    public final static String URI_CALL = OPTConstants.URI_OPTIPLAN + "/GetPrintQueueContent";

    @Override
    public boolean accepts(final String uri) {
        return URIHandler.matches(uri, URI_CALL);
    }

    @Override
    public MethodHandler getMethodHandler(final RequestContext requestContext) {
        final HTTPMethod method = requestContext.getMethod();
        if (HTTPMethod.OPTIONS.equals(method)) {
            return MethodHandler.OPTIONS_CORS_GET;
        } else if (HTTPMethod.GET.equals(method)) {
            return this;
        }
        return MethodHandler.METHOD_NOT_ALLOWED;
    }

    @Override
    public void handle(final RequestContext requestContext) {
        final HTTPResponse response = requestContext.getResponse();
        try {
            final List<PrintRequest> requests = server.printQueue.getAllRequests();
            final JsonObject json = new JsonObject();
            final JsonArray array = new JsonArray();
            for (final PrintRequest request : requests) {
                final JsonObject jsonRequest = new JsonObject();
                JsonSupport.add(jsonRequest, JsonConstants.PrintQueueEntry.ID, request.getId().toString());
                final LWAuftragKey key = request.getAuftrag().getAuftragKey();
                JsonSupport.add(jsonRequest, JsonConstants.PrintQueueEntry.AUFTRAG_NUMMER, key.getAuftragnummer());
                JsonSupport.add(jsonRequest, JsonConstants.PrintQueueEntry.AUFTRAG_GSE, Strings.toString(key.getGSE().getKey()));
                JsonSupport.add(jsonRequest, JsonConstants.PrintQueueEntry.PRINTER, request.getPrinter().getServiceName());
                JsonSupport.add(jsonRequest, JsonConstants.PrintQueueEntry.USER, request.getUserName());
                final PrintStatusCode code = request.getStatusCode();
                JsonSupport.add(jsonRequest, JsonConstants.PrintQueueEntry.STATUSTEXT, AbstractCode.getLongText(code, "???"));
                JsonSupport.add(jsonRequest, JsonConstants.PrintQueueEntry.STATUSCODE, AbstractCode.getId(code, 0));
                array.add(jsonRequest);
            }
            json.add(JsonConstants.QUEUE_ENTRIES, array);
            response.setBody(JsonSupport.encodeJson(json, JsonSupport.DEFAULT_ENCODING));
            response.setHeader(HTTPField.CONTENT_TYPE, JsonSupport.DEFAULT_MIMETYPE.toString());
            response.setStatus(HTTPStatus.S200);
        } catch (final Exception e) {
            response.setStatus(HTTPStatus.S500);
            response.setBody(null);
        }
    }
}

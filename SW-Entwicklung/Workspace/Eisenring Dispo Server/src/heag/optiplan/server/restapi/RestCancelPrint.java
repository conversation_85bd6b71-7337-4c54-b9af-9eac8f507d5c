package heag.optiplan.server.restapi;

import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.http.*;
import ch.eisenring.core.http.server.MethodHandler;
import ch.eisenring.core.http.server.RequestContext;
import ch.eisenring.core.http.server.URIHandler;
import ch.eisenring.core.logging.Logger;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import heag.optiplan.server.OPTConstants;
import heag.optiplan.server.OPTServer;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

public class RestCancelPrint implements URIHandler, MethodHandler {

    private final OPTServer server;

    public RestCancelPrint(final OPTServer server) {
        this.server = server;
    }

    public final static String URI_CALL = OPTConstants.URI_OPTIPLAN + "/CancelPrint";
    @Override
    public boolean accepts(final String uri) {
        return URIHandler.matches(uri, URI_CALL);
    }

    @Override
    public MethodHandler getMethodHandler(final RequestContext requestContext) {
        final HTTPMethod method = requestContext.getMethod();
        if (HTTPMethod.OPTIONS.equals(method)) {
            return MethodHandler.OPTIONS_CORS_POST;
        } else if (HTTPMethod.POST.equals(method)) {
            return this;
        }
        return MethodHandler.METHOD_NOT_ALLOWED;
    }

    @Override
    public void handle(final RequestContext requestContext) {
        final HTTPResponse response = requestContext.getResponse();
        final HTTPRequest request = requestContext.getRequest();
        try {
            final List<UUID> ids = new ArrayList<>();
            final String rawMimeType = request.getHeader(HTTPField.CONTENT_TYPE);
            final JsonObject json = JsonSupport.decodeJson(request.getBody(), rawMimeType);
            final JsonElement e = json.get(JsonConstants.CANCEL_REQUESTS);
            if (e instanceof JsonArray) {
                final JsonArray array = (JsonArray) e;
                for (final JsonElement ae : array) {
                    ids.add(toUUID(ae));
                }
            } else {
                ids.add(toUUID(e));
            }
            Logger.debug("Processing request to cancel the following print requests: {}.", toCSV(ids, ","));
            for (final UUID id : ids) {
                server.printQueue.cancel(id);
            }
            response.setStatus(HTTPStatus.S200);
        } catch (final Exception e) {
            response.setStatus(HTTPStatus.S400);
            response.setBody(null);
        }
    }

    private static UUID toUUID(final JsonElement e) throws Exception  {
        final String s = e.getAsString();
        return Strings.isEmpty(s) ? null : UUID.fromString(s);
    }

    private String toCSV(List<UUID> list, String delimiter) {
        return  list.stream().map(u -> u.toString()).collect(Collectors.joining(delimiter));

    }

}

package heag.optiplan.server.restapi;

import ch.eisenring.core.http.server.URIDispatcher;
import heag.optiplan.server.OPTConstants;
import heag.optiplan.server.OPTServer;

public final class RestAPIDispatcher extends URIDispatcher {

	public RestAPIDispatcher(final OPTServer server) {
		addHandler(new RestGetPrintServices(server));
		addHandler(new RestRequestPrint(server));
		addHandler(new RestGetPrintQueueContent(server));
		addHandler(new RestCancelPrint(server));
	}

}

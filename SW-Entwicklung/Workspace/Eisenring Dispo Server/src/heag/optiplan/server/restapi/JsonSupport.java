package heag.optiplan.server.restapi;

import ch.eisenring.core.datatypes.strings.Msg;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.io.binary.BinaryHolder;
import ch.eisenring.core.io.binary.BinaryHolderUtil;
import com.google.gson.*;

import javax.activation.MimeType;
import javax.activation.MimeTypeParseException;
import java.io.InputStreamReader;
import java.io.Reader;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;

public class JsonSupport {

	public static boolean getBoolean(final JsonElement parent, final String attributeName) {
		if (parent == null)
			throwMissing(attributeName);
		if (!parent.isJsonObject())
			throwType(attributeName);
		final JsonObject obj = parent.getAsJsonObject();
		final JsonElement e = obj.get(attributeName);
		if (e == null)
			throwMissing(attributeName);
		if (e.isJsonNull())
			throwType(attributeName);
		if (!e.isJsonPrimitive())
			throwType(attributeName);
		return e.getAsBoolean();
	}

	public static boolean getOptionalBoolean(final JsonElement parent, final String attributeName, final boolean defaultValue) {
		if (parent == null)
			return defaultValue;
		if (!parent.isJsonObject())
			throwType(attributeName);
		final JsonObject obj = parent.getAsJsonObject();
		final JsonElement e = obj.get(attributeName);
		if (e == null)
			return defaultValue;
		if (e.isJsonNull() || !e.isJsonPrimitive())
			throwType(attributeName);
		return e.getAsBoolean();
	}

	public static Integer getInteger(final JsonElement parent, final String attributeName) {
		if (parent == null)
			throwMissing(attributeName);
		if (!parent.isJsonObject())
			throwType(attributeName);
		final JsonObject obj = parent.getAsJsonObject();
		final JsonElement e = obj.get(attributeName);
		if (e == null)
			throwMissing(attributeName);
		if (e.isJsonNull())
			return null;
		if (!e.isJsonPrimitive())
			throwType(attributeName);
		return Integer.valueOf(e.getAsInt());
	}

	public static Integer getOptionalInteger(final JsonElement parent, final String attributeName) {
		if (parent == null)
			return null;
		if (!parent.isJsonObject())
			throwType(attributeName);
		final JsonObject obj = parent.getAsJsonObject();
		final JsonElement e = obj.get(attributeName);
		if (e == null)
			return null;
		if (e.isJsonNull())
			return null;
		if (!e.isJsonPrimitive())
			throwType(attributeName);
		return Integer.valueOf(e.getAsInt());
	}

	/**
	 * Gets a mandatory string attribute from Json, throws if the attribute
	 * is not found. An explicit NULL is allowed.
	 */
	public static String getString(final JsonElement parent, final String attributeName) {
		if (parent == null)
			throwMissing(attributeName);
		if (!parent.isJsonObject())
			throwType(attributeName);
		final JsonObject obj = parent.getAsJsonObject();
		final JsonElement e = obj.get(attributeName);
		if (e == null)
			throwMissing(attributeName);
		return toString(e, attributeName);
	}

	/**
	 * Gets an optional string attribute from Json, returns NULL if 
	 * no such attribute exists.
	 */
	public static String getOptionalString(final JsonElement parent, final String attributeName) {
		if (parent == null)
			return null;
		if (!parent.isJsonObject())
			throwType(attributeName);
		final JsonObject obj = parent.getAsJsonObject();
		final JsonElement e = obj.get(attributeName);
		if (e == null)
			return null;
		return toString(e, attributeName);
	}

	/**
	 * Converts element to string or throws if the element is not a primitive.
	 */
	public static String toString(final JsonElement element, final String attributeName) {
		if (element.isJsonNull())
			return null;
		if (!element.isJsonPrimitive())
			throwType(attributeName);
		return element.getAsString();
	}

	public static JsonArray getArray(final JsonElement parent, final String attributeName) throws Exception {
		if (parent == null)
			throwMissing(attributeName);
		if (!parent.isJsonObject())
			throwType(attributeName);
		final JsonObject obj = parent.getAsJsonObject();
		final JsonElement e = obj.get(attributeName);
		if (e == null)
			return null;
		if (!e.isJsonArray())
			throwType(attributeName);
		return e.getAsJsonArray();
	}

	/**
	 * Parse json array to collection
	 */
	public static <U> void parseOptionalArray(final JsonElement parent, final String arrayName, final ObjectFactory<U> factory, final java.util.Collection<U> container) throws Exception {
		final JsonArray array = getArray(parent, arrayName);
		if (array == null)
			return;
		for (final JsonElement e : array) {
			final U object = factory.fromJson(e);
			container.add(object);
		}
	}

	/**
	 * Parse json array to collection
	 */
	public static <U> void parseArray(final JsonElement parent, final String arrayName, final ObjectFactory<U> factory, final java.util.Collection<U> container) throws Exception {
		final JsonArray array = getArray(parent, arrayName);
		if (array == null)
			throw new IllegalArgumentException(Msg.mk("missing array {}", arrayName));
		for (final JsonElement e : array) {
			final U object = factory.fromJson(e);
			container.add(object);
		}
	}

	public static void add(final JsonObject object, final String attributeName, final String value) {
		object.add(attributeName, value == null ? JsonNull.INSTANCE : new JsonPrimitive(value));
	}

	public static void add(final JsonObject object, final String attributeName, final int value) {
		object.add(attributeName, new JsonPrimitive(value));
	}

	public static void add(final JsonObject object, final String attributeName, final boolean value) {
		object.add(attributeName, new JsonPrimitive(value));
	}

	public static void addNonEmpty(final JsonObject object, final String attributeName, final String value) {
		if (!Strings.isEmpty(value))
			object.add(attributeName, new JsonPrimitive(value));
	}

	private static void throwMissing(final String attributeName) {
		if (attributeName == null)
			throw new RuntimeException("missing attribute");
		throw new RuntimeException(Msg.mk("Missing attribute '{}'", attributeName));
	}

	private static void throwType(final String attributeName) {
		if (attributeName == null)
			throw new RuntimeException("incompatible element type");
		throw new RuntimeException(Msg.mk("incompatible element type for attribute '{}'", attributeName));
	}

	public static JsonElement valueOf(final CharSequence charSeq) {
		if (charSeq == null)
			return JsonNull.INSTANCE;
		final String s = Strings.toString(charSeq);
		return new JsonPrimitive(s);
	}

	public static JsonElement valueOf(final boolean b) {
		return new JsonPrimitive(b);
	}
	
	public static JsonElement valueOf(final Boolean b) {
		if (b == null)
			return JsonNull.INSTANCE;
		return new JsonPrimitive(b.booleanValue());
	}

	// --------------------------------------------------------------
	// ---
	// --- Json Parsing
	// ---
	// --------------------------------------------------------------
	public final static Charset DEFAULT_ENCODING = StandardCharsets.UTF_8;
	public final static MimeType DEFAULT_MIMETYPE;
	static {
		try {
			DEFAULT_MIMETYPE = new MimeType("application/json; charset=" + DEFAULT_ENCODING.name());
		} catch (final MimeTypeParseException e) {
			throw new ExceptionInInitializerError(e);
		}
	}

	/**
	 * Gets the Json encoded in BinaryHolder
	 */
	public static JsonObject decodeJson(final BinaryHolder binary, final String mimeType) throws Exception {
		// check mime type
		final MimeType usedMimeType;
		if (mimeType == null) {
			usedMimeType = DEFAULT_MIMETYPE;
		} else {
			usedMimeType = new MimeType(mimeType);
		}
		final String rawCharSet = usedMimeType.getParameter("charset");
		final Charset encoding; 
		if (rawCharSet == null) {
			encoding = DEFAULT_ENCODING;
		} else {
			encoding = Charset.forName(rawCharSet);
		}
		try (final Reader reader = new InputStreamReader(binary.getInputStream(), encoding)) {
			final JsonElement element = new JsonParser().parse(reader);
			return element.getAsJsonObject();
		}
	}

	public static BinaryHolder encodeJson(final JsonObject json, final Charset encoding) throws Exception {
		final BinaryHolder binary = BinaryHolderUtil.create(json.toString(), encoding);
		return binary;
	}
	
}

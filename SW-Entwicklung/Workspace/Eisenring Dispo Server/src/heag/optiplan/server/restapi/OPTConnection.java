package heag.optiplan.server.restapi;

import java.io.IOException;
import java.net.Socket;

import ch.eisenring.app.server.network.HTTPConnection;
import ch.eisenring.core.http.HTTPRequest;
import ch.eisenring.core.http.HTTPResponse;
import ch.eisenring.core.http.HTTPStatus;
import ch.eisenring.network.SocketOptions;
import heag.optiplan.server.OPTServer;

public final class OPTConnection extends HTTPConnection<OPTServer> {
	
	OPTConnection(final OPTServer server, final Socket socket, final SocketOptions options) throws IOException {
		super(server, socket, options);
	}


	// --------------------------------------------------------------
	// ---
	// --- Implementation
	// ---
	// --------------------------------------------------------------
	@Override
	protected HTTPResponse createResponse(final HTTPRequest request, final HTTPStatus status) {
		final HTTPResponse response;
		response = HTTPResponse.create(request, status);
		return response;
	}

	@Override
	protected void handle(final HTTPRequest request, final HTTPResponse response) {
		server.restDispatcher.dispatch(request, response);
	}
	
}

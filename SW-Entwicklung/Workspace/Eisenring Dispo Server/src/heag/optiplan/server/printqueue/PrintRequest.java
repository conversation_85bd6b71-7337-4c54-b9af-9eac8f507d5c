package heag.optiplan.server.printqueue;

import ch.eisenring.app.shared.SoftwareComponent;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.datatypes.strings.Msg;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.StringMakerFriendly;
import ch.eisenring.dispo.shared.pojo.mbp.MBPAuftrag;
import ch.eisenring.logiware.LWAuftragKey;
import ch.eisenring.logiware.code.service.LogiwareService;
import ch.eisenring.lw.model.navigable.LWAuftrag;
import ch.eisenring.lw.model.navigable.LWObject;
import ch.eisenring.lw.model.navigable.LWObjectCache;
import ch.eisenring.print.shared.model.PrintServiceIdentifier;
import org.faceless.pdf2.viewer2.feature.Print;

import java.util.UUID;

public class PrintRequest implements StringMakerFriendly {

    private final UUID id = UUID.randomUUID();
    private final long creationTimestamp = System.currentTimeMillis();
    private MBPAuftrag auftrag;
    private PrintServiceIdentifier printer;
    private String userName;
    private PrintStatusCode statusCode;
    private long lastModified;
    private List<Integer> documentIds;
    private PrintAction printAction;

    public static PrintRequest create(final SoftwareComponent component, final LWAuftragKey auftragKey,
                                      final String printerName, final String userName, final List<Integer> documentIds, PrintAction printAction) throws Exception {
        final PrintRequest result = new PrintRequest();
        // locate and validate printer
        result.printer = PrintServiceIdentifier.findByName(printerName);
        if (result.printer == null)
            throw new IllegalArgumentException(Msg.mk("printer \"{}\" not found", printerName));
        // locate and validate auftrag
        final LogiwareService logiwareService = component.locateService(LogiwareService.class);
        final LWObjectCache lwObjectCache = logiwareService.createObjectCache();
        final LWAuftrag lwAuftrag = LWObject.validateExists(lwObjectCache.getObject(auftragKey, LWAuftrag.class));
        result.printAction = printAction;
        result.auftrag = new MBPAuftrag(lwAuftrag);
        if (lwAuftrag == null)
            throw new IllegalArgumentException(Msg.mk("auftrag \"{}\" not found", auftragKey));
        result.documentIds = documentIds;
        result.userName = userName == null ? "" : userName;
        result.setPrintStatusCode(PrintStatusCode.NULL);
        return result;
    }

    public synchronized PrintStatusCode getStatusCode() {
        return statusCode;
    }

    public synchronized void setPrintStatusCode(final PrintStatusCode statusCode) {
        this.statusCode = statusCode;
        this.lastModified = System.currentTimeMillis();
    }

    public PrintServiceIdentifier getPrinter() {
        return printer;
    }

    public MBPAuftrag getAuftrag() {
        return auftrag;
    }

    public String getUserName() {
        return userName;
    }

    public List<Integer> getDocumentIds() {
        return documentIds;
    }

    public synchronized long getLastModified() {
        return lastModified;
    }

    public long getCreationTimestamp() {
        return creationTimestamp;
    }

    public UUID getId() {
        return id;
    }

    public PrintAction getPrintAction() { return printAction; }

    // --------------------------------------------------------------
    // ---
    // --- Object overrides
    // ---
    // --------------------------------------------------------------
    @Override
    public String toString() {
        final StringMaker b = StringMaker.obtain();
        toStringMaker(b);
        return b.release();
    }

    @Override
    public void toStringMaker(final StringMaker target) {
        target.append("Montagedossier-Druckauftrag{");
        target.append(id);
        target.append("}#");
        target.append(auftrag.getAuftragKey().getAuftragnummer());
        target.append(';');
        target.append(auftrag.getAuftragKey().getGSE().getKey());
    }

}

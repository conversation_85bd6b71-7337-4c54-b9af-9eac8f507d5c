package heag.optiplan.server;

import ch.eisenring.core.collections.Set;
import ch.eisenring.logiware.code.pseudo.AbwicklungsartCode;
import ch.eisenring.model.engine.jdbc.DatabaseSpecifier;

public interface OPTConstants {

	public final static DatabaseSpecifier BWEX_DB1 = DatabaseSpecifier.get("BW_EXPORT_DB");
	public final static DatabaseSpecifier BWEX_DB2 = DatabaseSpecifier.get("BWEX_ALT");
	public final static DatabaseSpecifier BWEX_DB3 = DatabaseSpecifier.get("BWEX_TEST");
	public final static DatabaseSpecifier BWEX_DB4 = DatabaseSpecifier.get("BW_OPTIBRICK_PROD");

	public final static Set<AbwicklungsartCode> EXPORT_AWA = Set.asReadonlySet(
			AbwicklungsartCode.AWA001,
			AbwicklungsartCode.AWA115,
			AbwicklungsartCode.AWA134,
			AbwicklungsartCode.AWA135
		);


	public final static String URI_OPTIPLAN = "/HEAG/rest-api/print";

}

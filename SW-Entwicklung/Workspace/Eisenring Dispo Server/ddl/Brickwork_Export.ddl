
DROP TRIGGER DSP_BWExportId_Delete;

DROP TABLE DSP_BWAuftrag;
DROP TABLE DSP_BWProjekt;
DROP TABLE DSP_BWMonteurAbsenz;
DROP TABLE DSP_BWMonteurEinAus;
DROP TABLE DSP_BWMonteur;
DROP TABLE DSP_BWMonteurTeam;
DROP TABLE DSP_BWCodeTable;
DROP TABLE DSP_BWEtappe;
DROP TABLE DSP_BWExportId;


-- ------------------------------------------------------------------
-- Export Identitaet

CREATE TABLE DSP_BWExportId (
  exId BIGINT NOT NULL,
  exTimestamp DATETIME NOT NULL
);

ALTER TABLE DSP_BWExportId ADD PRIMARY KEY (exId);

-- ------------------------------------------------------------------
-- Code Tabelle

CREATE TABLE DSP_BWCodeTable (
  exId               BIGINT NOT NULL,
  PK                 VARCHAR(81) NOT NULL,
  Typ                VARCHAR(64) NOT NULL,
  Rohwert            VARCHAR(16),
  Kurzbezeichnung    VARCHAR(60) NOT NULL,
  Langbezeichnung    VARCHAR(120) NOT NULL
);

ALTER TABLE DSP_BWCodeTable ADD FOREIGN KEY (exId) REFERENCES DSP_BWExportId(exId);

-- ------------------------------------------------------------------
-- Etappen

CREATE TABLE DSP_BWEtappe (
  exId               BIGINT NOT NULL,
  etappeId           BIGINT NOT NULL,
  basisPK            VARCHAR(21) NOT NULL,
  basisNr            VARCHAR(16) NOT NULL,
  basisGSE           VARCHAR(4),
  etappeStart        DATE NOT NULL,
  etappeEnde         DATE NOT NULL,
  bezeichnung        VARCHAR(60),
  granitEnde         DATE
);

ALTER TABLE DSP_BWEtappe ADD FOREIGN KEY (exId) REFERENCES DSP_BWExportId(exId);

-- ------------------------------------------------------------------
-- Monteur-Teams

CREATE TABLE DSP_BWMonteurTeam (
  exId                BIGINT NOT NULL,
  teamId              BIGINT NOT NULL,
  creationTimestamp   DATETIME NOT NULL,
  teamGSE             CHAR(4) NOT NULL,
  teamName            VARCHAR(60) NOT NULL
);

ALTER TABLE DSP_BWMonteurTeam ADD FOREIGN KEY (exId) REFERENCES DSP_BWExportId(exId);

-- ------------------------------------------------------------------
-- Monteure Stammdaten

CREATE TABLE DSP_BWMonteur (
  exId                 BIGINT NOT NULL,
  PK                   CHAR(3) NOT NULL,
  Kurzbezeichnung      VARCHAR(30) NOT NULL,
  Nachname             VARCHAR(30),
  Vorname              VARCHAR(30),
  PresentoId           BIGINT NOT NULL,
  PLZ                  VARCHAR(16),
  Ort                  VARCHAR(60),
  Strasse              VARCHAR(60),
  AdressZusatz         VARCHAR(60),
  EMail                VARCHAR(60),
  TelefonMobil         VARCHAR(30),
  TelefonFestnetz      VARCHAR(30),
  MitarbeiterVon       BIGINT,
  Sterne               INT NOT NULL,
  LeistungsFaktor      INT NOT NULL,
  WerkzeugVorhanden    INT NOT NULL,
  KenntnisEURO         INT NOT NULL,
  KenntnisSMS          INT NOT NULL,
  FahrzeugVorhanden    INT NOT NULL,
  FahrzeugBemerkung    VARCHAR(60),
  GLZSaldoMinuten      INT NOT NULL,
  TeamId               BIGINT,
  MFEinzelkuechen      INT DEFAULT(1),
  MFEigentumskuechen   INT DEFAULT(1),
  MFUmbauBewohnt       INT DEFAULT(0),
  MFSchreinerArbeiten  INT DEFAULT(0),
  MFKuechenUeber25000  INT DEFAULT(0),
  MFQualitaet          INT DEFAULT(1),
  MFLeistungsFaktorMax INT DEFAULT(100),
  MFAuftritt           INT DEFAULT(1),
  MFOrganisation       INT DEFAULT(1),
  MFAdministration     INT DEFAULT(1),
  MFAusbilder          INT DEFAULT(0),
  MFFertigstellung     INT DEFAULT(0),
  EmploymentRole       VARCHAR(16)
);

ALTER TABLE DSP_BWMonteur ADD FOREIGN KEY (exId) REFERENCES DSP_BWExportId(exId);

-- ------------------------------------------------------------------
-- Monteure Beschaeftigungsperioden

CREATE TABLE DSP_BWMonteurEinAus (
  exId                 BIGINT NOT NULL,
  PK                   CHAR(3) NOT NULL,
  Eintritt             DATE NOT NULL,
  Austritt             DATE NOT NULL
);

ALTER TABLE DSP_BWMonteurEinAus ADD FOREIGN KEY (exId) REFERENCES DSP_BWExportId(exId);

-- ------------------------------------------------------------------
-- Monteure Absenzen
--
-- AbsenzAnteil = Bruchteil von 1.0 die der Monteur belegt ist.
-- AbsenzBegin (inklusiv), AbsenzEnde (exklusiv)

CREATE TABLE DSP_BWMonteurAbsenz (
  exId                 BIGINT NOT NULL,
  PK                   CHAR(3) NOT NULL,
  AbsenzBegin          DATE NOT NULL,
  AbsenzEnde           DATE NOT NULL,
  Grund                VARCHAR(16) NOT NULL,
  AbsenzAnteil         FLOAT DEFAULT(1)
);

ALTER TABLE DSP_BWMonteurAbsenz ADD FOREIGN KEY (exId) REFERENCES DSP_BWExportId(exId);

-- ------------------------------------------------------------------
-- Projekte (Basis, Kuechen und sonstige Auftraege)

CREATE TABLE DSP_BWProjekt (
  exId                      BIGINT NOT NULL,
  PK                        VARCHAR(30) NOT NULL,
  Basis                     VARCHAR(30) NOT NULL,
  ProjektNr                 VARCHAR(16) NOT NULL,
  Projektbezeichnung        VARCHAR(250),
  StatusCode                CHAR(3),
  ObjektbetreuerCode        CHAR(3),
  MarketingCode             CHAR(3),
  ObjektkuechenAnzahl       VARCHAR(50),
  ManuelleBauherrAdresse    VARCHAR(200),
  PhoneKunde                VARCHAR(50),
  GPSKoordinaten            VARCHAR(50),
  Verkaeufer                VARCHAR(16),
  Kundenberater             VARCHAR(16),
  ObjektStatus              CHAR(3),
  Bemusterung1              DATE,
  Bemusterung2              DATE,
  Bemusterung3              DATE,
  AuftragsSumme             NUMERIC(11, 2),
  SubjektWert               NUMERIC(11, 2),
  BasisWert                 NUMERIC(11, 2),
  HausSpiegel               VARCHAR(200),
  Geschoss                  CHAR(3),
  Lage                      CHAR(3),
  Modalitaet                CHAR(3),
  ProduktKategorie          CHAR(3),
  ProjektPLZ                VARCHAR(8),
  ProjektOrt                VARCHAR(80),
  ProjektStrasse            VARCHAR(200),
  ProjektWohnungBez         VARCHAR(128),
  ProjektWohnungTyp         VARCHAR(64),
  ProjektFamilienBez        VARCHAR(128),
  ProjektDetailbezeichnung  VARCHAR(255)
);

ALTER TABLE DSP_BWProjekt ADD FOREIGN KEY (exId) REFERENCES DSP_BWExportId(exId);

-- ------------------------------------------------------------------
-- Auftraege (Kuechen und sonstige Auftraege)

CREATE TABLE DSP_BWAuftrag (
  exId                   BIGINT NOT NULL,
  PK                     VARCHAR(30) NOT NULL,
  Projekt                VARCHAR(30) NOT NULL,
  AuftragNr              INT NOT NULL,
  AuftragGSE             VARCHAR(4),
  ProjektNr              VARCHAR(16) NOT NULL,
  ProjektGSE             VARCHAR(4),
  Abwicklungsart         VARCHAR(16),
  GSELager               VARCHAR(4),
  Auftragsdatum          DATE NOT NULL,
  Wunschdatum            DATE NOT NULL,
  AVORTeam               VARCHAR(16),
  Kundenberater          VARCHAR(16),
  Versandart             CHAR(3),
  Verkaeufer             VARCHAR(16),
  Kopftext               VARCHAR(MAX),
  Fusstext               VARCHAR(MAX),
  WinnerplanNummer       VARCHAR(50),
  Sachbearbeiter         VARCHAR(10),
  Bestellnr              VARCHAR(50),
  Auftragstatus          CHAR(3),
  Speditionsvorschrift   CHAR(3),
  AuftragTotal           NUMERIC(11, 2),
  AbweichendeLieferAdresse VARCHAR(255),
  Zahlungskondition      CHAR(3),
  Abdeckung              VARCHAR(40),
  Abschlussdatum         DATE,
  Ausfuehrung            VARCHAR(40),
  Granitschl_1           CHAR(3),
  Granitschl_2           CHAR(3),
  Granitschl_3           CHAR(3),
  Granitschl_4           CHAR(3),
  Granitschl_5           CHAR(3),
  Granitschl_6           CHAR(3),
  Granitschl_7           CHAR(3),
  Granitschl_8           CHAR(3),
  Granitschl_9           CHAR(3),
  Granitschl_A           CHAR(3),
  Granitschl_B           CHAR(3),
  Granitschl_C           CHAR(3),
  DisponentSIR           CHAR(3),
  Bezugsdatum            DATE,
  Abbruchdatum           DATE,
  Spezialdatum           DATE,
  GranitStartdatum       DATE,
  GranitEndedatum        DATE,
  MassKontrolleCode      CHAR(3),
  Zahlungsbedingungen    VARCHAR(250),
  MonteurSIR             CHAR(3),
  MonteurPFY             CHAR(3),
  InfoMasse              VARCHAR(40),
  DienstlSIR             CHAR(3),
  DienstlPFY1            CHAR(3),
  DienstlPFY2            CHAR(3),
  DienstlPFY3            CHAR(3),
  DienstlPFY4            CHAR(3),
  DienstlPFY5            CHAR(3),
  FixTerminCode          CHAR(3),
  GranitMontageCode      CHAR(3),
  PlanungStatusCode      CHAR(3),
  Bemerkungen            VARCHAR(255),
  Kontaktperson          VARCHAR(80),
  KuechenTypCode         CHAR(3),
  DBEKSIR                NUMERIC(11, 2),
  DBRechwert             NUMERIC(11, 2),
  DBKoMontage            NUMERIC(11, 2),
  DBKoMontageAWA         NUMERIC(11, 2),
  ProvRechWert           NUMERIC(11, 2),
  InfoMonteur            VARCHAR(255),
  BemerkungenMonteur     VARCHAR(255),
  TerminZeit             VARCHAR(5),
  DSPPunktwert           FLOAT,
  DSPPunktwertProzent    FLOAT,
  VorherigerAuftragPK    VARCHAR(30),
  EtappeId               BIGINT
);

ALTER TABLE DSP_BWAuftrag ADD FOREIGN KEY (exId) REFERENCES DSP_BWExportId(exId);

-- ------------------------------------------------------------------
-- Feinplanung

CREATE TABLE DSP_BWFeinPlanung (
  exId                   BIGINT NOT NULL,
  rowId                  BIGINT NOT NULL,
  PlanungTyp             INT NOT NULL,
  AuftragNr              INT,
  AuftragGSE             VARCHAR(4),
  Monteur                CHAR(3) NOT NULL,
  PlanungBegin           DATETIME NOT NULL,
  PlanungEnde            DATETIME NOT NULL
);

ALTER TABLE DSP_BWFeinPlanung ADD FOREIGN KEY (exId) REFERENCES DSP_BWExportId(exId);

-- ------------------------------------------------------------------
-- Lazy permissions

GRANT ALL ON DSP_BWAuftrag TO PUBLIC;
GRANT ALL ON DSP_BWProjekt TO PUBLIC;
GRANT ALL ON DSP_BWMonteurAbsenz TO PUBLIC;
GRANT ALL ON DSP_BWMonteurEinAus TO PUBLIC;
GRANT ALL ON DSP_BWMonteur TO PUBLIC;
GRANT ALL ON DSP_BWMonteurTeam TO PUBLIC;
GRANT ALL ON DSP_BWEtappe TO PUBLIC;
GRANT ALL ON DSP_BWCodeTable TO PUBLIC;
GRANT ALL ON DSP_BWFeinPlanung TO PUBLIC;
GRANT ALL ON DSP_BWExportId TO PUBLIC;

-- ------------------------------------------------------------------
-- Triggers for cascading delete etc.
CREATE TRIGGER DSP_BWExportId_Delete ON DSP_BWExportId INSTEAD OF DELETE
AS
  IF @@ROWCOUNT = 0 RETURN
  SET NOCOUNT ON  
  DELETE FROM DSP_BWFeinPlanung WHERE exId IN (SELECT exId FROM deleted)
  DELETE FROM DSP_BWAuftrag WHERE exId IN (SELECT exId FROM deleted)
  DELETE FROM DSP_BWProjekt WHERE exId IN (SELECT exId FROM deleted)
  DELETE FROM DSP_BWMonteurAbsenz WHERE exId IN (SELECT exId FROM deleted)
  DELETE FROM DSP_BWMonteurEinAus WHERE exId IN (SELECT exId FROM deleted)
  DELETE FROM DSP_BWMonteur WHERE exId IN (SELECT exId FROM deleted)
  DELETE FROM DSP_BWMonteurTeam WHERE exId IN (SELECT exId FROM deleted)
  DELETE FROM DSP_BWEtappe WHERE exId IN (SELECT exId FROM deleted)
  DELETE FROM DSP_BWCodeTable WHERE exId IN (SELECT exId FROM deleted)
  DELETE FROM DSP_BWExportId WHERE exId IN (SELECT exId FROM deleted);


<project>

	<property name="base.dir" value="../../../Eisenring Dispo" />
	<property name="workspace.dir" value="../../../SW-Entwicklung/Workspace" />
	<property name="lib.dir" value="${base.dir}/Server/lib" />
	<property name="build.dir" value="ant_build" />
	<property name="classes.dir" value="${build.dir}/classes" />
	<property name="jar.file" value="server.jar" />

	<path id="classpath">
		<fileset dir="${lib.dir}" includes="**/*.jar" excludes="${jar.file}" />
	</path>

	<target name="clean">
		<delete dir="${build.dir}" />
		<delete file="${lib.dir}/${jar.file}" />
		<mkdir dir="${base.dir}/Server/log" />
		<mkdir dir="${base.dir}/Server/cache" />
	</target>

	<target name="compile" depends="clean">
		<mkdir dir="${classes.dir}" />
		<javac srcdir="
			${workspace.dir}/Eisenring Core/src;
			${workspace.dir}/Eisenring Network/src;
			${workspace.dir}/Eisenring JDBC/src;
			${workspace.dir}/Eisenring Model Shared/src;
			${workspace.dir}/Eisenring PLZ/src;
			${workspace.dir}/Eisenring App Shared/src;
			${workspace.dir}/Eisenring App Server/src;
			${workspace.dir}/Eisenring PRT Shared/src;
			${workspace.dir}/Eisenring PRT Server/src;
			${workspace.dir}/Eisenring EMail Shared/src;
			${workspace.dir}/Eisenring EMail Server/src;
			${workspace.dir}/Eisenring EMail Dispatcher Server/src;
			${workspace.dir}/Eisenring Logiware Shared/src;
			${workspace.dir}/Eisenring Logiware Import/src;
			${workspace.dir}/Eisenring Logiware Export/src;
			${workspace.dir}/Eisenring Statistik Shared/src;
			${workspace.dir}/Eisenring Statistik Server/src;
			${workspace.dir}/Eisenring User Shared/src;
			${workspace.dir}/Eisenring User Server/src;
			${workspace.dir}/Eisenring DMS PlaintextExtractor/src;
			${workspace.dir}/Eisenring DMS Shared/src;
			${workspace.dir}/Eisenring DMS Common/src;
			${workspace.dir}/Eisenring DMS Server/src;
			${workspace.dir}/Eisenring DMS Service/src;
			${workspace.dir}/Eisenring EDI Base/src;
			${workspace.dir}/Eisenring EDI Server/src;
			${workspace.dir}/Eisenring VMD Shared/src;
			${workspace.dir}/Eisenring VMD Server/src;
			${workspace.dir}/Eisenring ContainerFileSystem/src;
			${workspace.dir}/Eisenring MBL Shared/src;
			${workspace.dir}/Eisenring MBL Server/src;
			${workspace.dir}/Eisenring NSI Server/src;
			${workspace.dir}/Eisenring HUO Shared/src;
			${workspace.dir}/Eisenring HUO Server/src;
			${workspace.dir}/Eisenring Presento Server/src;
			${workspace.dir}/Eisenring DSP Public/src;
			${workspace.dir}/Eisenring Dispo Service/src;
			${workspace.dir}/Eisenring Dispo Shared/src;
			${workspace.dir}/Eisenring AbacusInterfaces Server/src;
			${workspace.dir}/Eisenring Reporting Server/src;
			src" destdir="${classes.dir}" classpathref="classpath" includeAntRuntime="no" includeJavaRuntime="yes" debug="true" encoding="UTF-8">
			<compilerarg value="-Xlint:deprecation"/>
		</javac>
		<touch datetime="10/10/2010 10:10 am">
			<fileset dir="${classes.dir}" />
		</touch>
	</target>

	<target name="jar" depends="compile">
		<zip destfile="${lib.dir}/${jar.file}" level="9">
			<fileset includes="**/*" dir="${classes.dir}" />
			<fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring Core/src" />
			<fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring Network/src" />
			<fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring JDBC/src" />
			<fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring Model Shared/src" />
			<fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring PLZ/src" />
			<fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring App Shared/src" />
			<fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring App Server/src" />
			<fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring PRT Shared/src" />
			<fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring PRT Server/src" />
			<fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring EMail Shared/src" />
			<fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring EMail Server/src" />
			<fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring EMail Dispatcher Server/src" />
			<fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring Logiware Shared/src" />
			<fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring Logiware Import/src" />
			<fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring Logiware Export/src" />
			<fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring User Shared/src" />
			<fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring User Server/src" />
			<fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring Statistik Shared/src" />
			<fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring Statistik Server/src" />
			<fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring DMS PlaintextExtractor/src" />
			<fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring DMS Shared/src" />
			<fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring DMS Common/src" />
			<fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring DMS Server/src" />
			<fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring DMS Service/src" />
			<fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring EDI Base/src" />
			<fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring EDI Server/src" />
			<fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring DSP Public/src/" />
			<fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring Dispo Service/src" />
			<fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring Dispo Shared/src" />
			<fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring NSI Server/src" />
			<fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring HUO Shared/src" />
			<fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring HUO Server/src" />
			<fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring Presento Server/src" />
			<fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring ContainerFileSystem/src" />
			<fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring MBL Shared/src" />
			<fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring MBL Server/src" />
			<fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring VMD Shared/src" />
			<fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring VMD Server/src" />
			<fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring AbacusInterfaces Server/src" />
			<fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring Reporting Server/src" />
			<fileset excludes="**/*.java" dir="src" />
		</zip>
	</target>

	<target name="build" depends="jar">
		<delete dir="${build.dir}" />
	</target>

</project>

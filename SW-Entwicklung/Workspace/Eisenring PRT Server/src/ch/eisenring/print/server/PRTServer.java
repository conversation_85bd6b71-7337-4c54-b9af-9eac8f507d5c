package ch.eisenring.print.server;

import ch.eisenring.app.server.AbstractServerComponent;
import ch.eisenring.app.shared.Configurable;
import ch.eisenring.core.application.Configuration;
import ch.eisenring.core.application.ConfigurationException;
import ch.eisenring.core.collections.Collection;
import ch.eisenring.core.feature.Feature;
import ch.eisenring.print.server.network.PRTPacketDispatcher;

/**
 * Printing Server module
 */
public final class PRTServer extends AbstractServerComponent implements Configurable {
	
	// --------------------------------------------------------------
	// ---
	// --- Server privates
	// ---
	// --------------------------------------------------------------

	// --------------------------------------------------------------
	// ---
	// --- Server globals
	// ---
	// --------------------------------------------------------------

	// --------------------------------------------------------------
	// ---
	// --- Feature management
	// ---
	// --------------------------------------------------------------
	private final Feature[] features = {
			new PRTPacketDispatcher(this)
	};

	@Override
	public Feature[] getFeatures() {
		return features;
	}

	// --------------------------------------------------------------
	// ---
	// --- Launch / Shutdown
	// ---
	// --------------------------------------------------------------
	@Override
	public void launch() throws Exception {
		super.launch();
	}
	
	@Override
	public void shutdown() throws Exception {
		// continue general shutdown procedure
		super.shutdown();
	}

	// --------------------------------------------------------------
	// ---
	// --- Configurable implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public void getConfigurationFileNames(final Collection<String> fileNames) {
		// nothing
	}
	
	@Override
	public void configure(final Configuration configuration) throws ConfigurationException {
		// nothing
	}

}

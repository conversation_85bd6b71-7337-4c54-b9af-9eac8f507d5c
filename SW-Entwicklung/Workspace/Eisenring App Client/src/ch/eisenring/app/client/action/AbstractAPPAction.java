package ch.eisenring.app.client.action;

import ch.eisenring.app.client.AppComponent;
import ch.eisenring.app.shared.Service;
import ch.eisenring.app.shared.ServiceNotFoundException;
import ch.eisenring.core.datatypes.primitives.Primitives;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.core.observable.Observable;
import ch.eisenring.core.observable.Observer;
import ch.eisenring.core.util.ReflectionUtil;
import ch.eisenring.gui.action.AbstractAction;

public abstract class AbstractAPPAction<T extends AppComponent> extends AbstractAction {

	protected final T client;
	
	protected AbstractAPPAction(final T client, final Object ... properties) {
		super(properties);
		this.client = client;
	}
	
	protected T getClient() {
		return client;
	}

	@Override
	protected boolean isEnabledImpl() {
		return super.isEnabledImpl() &&  client != null;
	}

	// --------------------------------------------------------------
	// ---
	// --- Generalized observer infrastructure
	// ---
	// --------------------------------------------------------------
	private Observer<Object> observer;
	
	/**
	 * Generic observer for this action. This observer is used for forwarding
	 * any change messages from observables this action has been attached to.
	 */
	private Observer<Object> getStateObserver() {
		synchronized (this) {
			if (observer == null) {
				observer = new Observer<Object>() {
					public void observableChanged(final Observable<Object> observable) {
						AbstractAPPAction.this.updateProperties();
					}
				};
			}
		}
		return observer;
	}

	/**
	 * Registers an observable for observation. Changes in the
	 * observable state will trigger an update of the actions states. 
	 */
	protected final void addObservable(final Observable<?> observable) {
		if (observable == null)
			return;
		observable.addObserver(getStateObserver());
	}

	/**
	 * Enabled this actions enabled, visible and checked states
	 * to be based on permissions from the user system.
	 */
	protected final void addPermissionObserver() {
		addPermissionObserver(client, getStateObserver());
	}

	/**
	 * Adds the given observer as permission observer
	 */
	@SuppressWarnings("unchecked")
	public static void addPermissionObserver(final AppComponent client, final Observer<?> observer) {
		// do this with reflection to avoid compile time dependency
		// to any user system classes. this is still ugly, but allows
		// this code to run even if there is no user system at all
		final Class<Service> serviceClass;
		try {
			serviceClass = (Class<Service>) Primitives.classForName(
					"ch.eisenring.user.shared.service.USRService");
		} catch (final ClassNotFoundException e) {
			Logger.warn("Problem attaching to permission observable: {}", e.getMessage());
			Logger.warn(e);
			return;
		}
		try {
			final Service service = client.locateService(serviceClass);
			final Observable<?> observable = (Observable<?>) ReflectionUtil.call(
					service, "getPermissionObservable", (Class<?>[]) null, (Object[]) null);
			if (observable == null) {
				Logger.warn("Problem attaching to permission observable: getPermissionObservable() returned null");
			} else {
				observable.addObserver(observer);
			}
		} catch (final ServiceNotFoundException e) {
			Logger.warn("Problem attaching to permission observable: {}", e.getMessage());
			Logger.warn(e);
		}
	}

}

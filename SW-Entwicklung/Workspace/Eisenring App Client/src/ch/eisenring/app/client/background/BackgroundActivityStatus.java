package ch.eisenring.app.client.background;

import ch.eisenring.core.codetable.ErrorMessage;

final class BackgroundActivityStatus {

	/**
	 * The activity which this status belongs to
	 */
	public final BackgroundActivity activity;

	/**
	 * Start timestamp of the activity.
	 */
	public final long startTimestamp;

	/**
	 * Finish timestamp of the activity.
	 */
	public long finishTimestamp;
	
	/**
	 * Result status of the activity (null until finished)
	 */
	public ErrorMessage result;

	public BackgroundActivityStatus(final BackgroundActivity activity) {
		this.activity = activity;
		this.startTimestamp = System.currentTimeMillis();
	}

	// --------------------------------------------------------------
	// ---
	// --- Object overrides
	// ---
	// --------------------------------------------------------------
	@Override
	public int hashCode() {
		return activity.hashCode();
	}

	@Override
	public boolean equals(final Object o) {
		if (!(o instanceof BackgroundActivityStatus))
			return false;
		final BackgroundActivityStatus other = (BackgroundActivityStatus) o;
		return other.activity == activity;
	}

	@Override
	public String toString() {
		return activity.getDisplayName();
	}

}

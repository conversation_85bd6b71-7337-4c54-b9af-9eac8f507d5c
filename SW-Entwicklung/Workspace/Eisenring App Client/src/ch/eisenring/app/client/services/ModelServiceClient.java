package ch.eisenring.app.client.services;

import java.util.concurrent.atomic.AtomicInteger;

import ch.eisenring.app.client.AbstractAppComponent;
import ch.eisenring.app.shared.Configurable;
import ch.eisenring.core.application.Configuration;
import ch.eisenring.core.application.ConfigurationException;
import ch.eisenring.core.collections.Collection;
import ch.eisenring.core.feature.Feature;
import ch.eisenring.model.primarykey.LongPrimaryKey;

public final class ModelServiceClient extends AbstractAppComponent implements Configurable {

	/**
	 * Size of ranges requested from the server
	 */
	private final AtomicInteger blockSize = new AtomicInteger(50);

	private final LongPrimaryKeyGenerator generator = new LongPrimaryKeyGenerator(this, blockSize);

	// --------------------------------------------------------------
	// ---
	// --- AppComponent implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public void launch() throws Exception {
		super.launch();
		LongPrimaryKey.setDefaultGenerator(generator);
	}
	
	// --------------------------------------------------------------
	// ---
	// --- Feature management
	// ---
	// --------------------------------------------------------------
	private final Feature[] features = {
			new SupplyLongRangeHandler(generator)
	};

	@Override
	public Feature[] getFeatures() {
		return features;
	}

	// --------------------------------------------------------------
	// ---
	// --- Configurable implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public void getConfigurationFileNames(final Collection<String> fileNames) {
		// no configuration names
	}
	
	@Override
	public void configure(final Configuration configuration) throws ConfigurationException {
		blockSize.set(configuration.getInteger("ModelService:Blocksize", 5, 1000, 50));
	}
	
}

package ch.eisenring.app.client.action;

import java.io.File;

import javax.swing.KeyStroke;

import ch.eisenring.app.client.AppComponent;
import ch.eisenring.app.client.images.Images;
import ch.eisenring.core.platform.Platform;

public class ShowHelpAction extends AbstractAPPAction<AppComponent> {

	private final String filepath;
	private final String filename;

	public ShowHelpAction(final AppComponent appComponent,
            			  final String label,
			              final String filepath,
			              final String filename,
			              final KeyStroke keyStroke) {
		super(appComponent, keyStroke,
			  Images.QUESTION, (label == null ? "Hilfe..." : label));
		this.filepath = filepath;
		this.filename = filename;
	}

	public ShowHelpAction(final AppComponent appComponent,
			              final String label,
						  final String filepath,
						  final String filename) {
		this(appComponent, label, filepath, filename, (KeyStroke) null);
	}

	@Override
	protected void performAction() {
		final File file = getAbsoluteFilePath();
		if (file != null) {
			final Platform platform = Platform.getPlatform();
			platform.openFile(file.getAbsolutePath());
		}
	}

	public File getAbsoluteFilePath() {
		File file = null;
		if (filepath != null) {
			file = new File(filepath);
			if (filename != null)
				file = new File(file, filename);
		} else {
			if (filename != null)
				file = new File(filename);
		}
		if (file == null) {
			return null;
		} else if (!file.exists()) {
			return null;
		} else if (!file.isFile()) {
			return null;
		} else if (!file.canRead()) {
			return null;
		}
		return file;
	}

}

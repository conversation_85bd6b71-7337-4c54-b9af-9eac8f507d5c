package ch.eisenring.app.client;

import ch.eisenring.app.client.background.ActivityManager;
import ch.eisenring.app.client.background.BackgroundActivity;
import ch.eisenring.app.client.background.BackgroundActivityManager;
import ch.eisenring.app.shared.SoftwareComponent;
import ch.eisenring.app.shared.network.RPCHandler;
import ch.eisenring.app.shared.network.RPCManager;
import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.collections.Map;
import ch.eisenring.core.observable.Observer;
import ch.eisenring.core.observable.ObserverNotificationPolicy;
import ch.eisenring.core.resource.DataFileSource;
import ch.eisenring.core.usersettings.PreferencesSource;
import ch.eisenring.core.usersettings.UserSettings;
import ch.eisenring.gui.window.FeedbackHandler;
import ch.eisenring.network.StreamableConnection;
import ch.eisenring.network.PacketCallback;
import ch.eisenring.network.PacketSink;
import ch.eisenring.network.packet.AbstractPacket;

/**
 * Applications need to implement this interface
 */
public interface AppComponent extends SoftwareComponent, FeedbackHandler, PacketSink, PreferencesSource, ActivityManager {

	/**
	 * Returns true if the component is running as client 
	 */
	public default boolean isClient() {
		return getCore().isClient();
	}

	/**
	 * Returns true if the component is running as sever
	 */
	public default boolean isServer() {
		return getCore().isServer();
	}

	/**
	 * Launches the component
	 */
	public void launch() throws Exception;
	
	/**
	 * Terminates the component
	 */
	public void shutdown() throws Exception;

	/**
	 * Gets the AppCore that is running the component.
	 * Only valid after component has been launched without
	 * exception and until shutdown is called.
	 */
	public AppCore getCore();

	/**
	 * Gets the user settings for this component
	 */
	public default UserSettings getUserSettings() {
		return getCore().getUserSettings();
	}
	
	/**
	 * Gets the FeedbackHandler for this component
	 */
	public default FeedbackHandler getFeedbackHandler() {
		return getCore().getFeedbackHandler();
	}

	/**
	 * Gets the data file source of this component.
	 * 
	 * Each component can provide its own source, but
	 * the default implementation gets the core's source.
	 */
	public DataFileSource getDataFileSource();

	/**
	 * Sends a packet for this component and processes
	 * the reply using the given RPCAction.
	 */
	public default void sendPacket(final RPCHandler rpcHandler, final AbstractPacket request) {
		final RPCManager rpcManager = getCore().getRPCManager();
		//final PacketSink sink = getConnection(true);
		rpcManager.sendRPC(this, request, this, rpcHandler);
	}

	public default void sendPacket(final AbstractPacket packet, final PacketCallback callback) {
		final PacketSink sink = getConnection(true);
		if (sink == null)
			return;
		sink.sendPacket(packet, callback);
	}

	/**
	 * Sends a packet for this component.
	 * By default the packet send is delegated to the AppCore
	 * that launched this component.
	 * If the component manages its own network connection,
	 * the component may implement the method itself.
	 */
	public default void sendPacket(final AbstractPacket packet, final boolean waitForSend) {
		final StreamableConnection sink = getConnection(true);
		if (sink == null)
			return;
		sink.sendPacket(packet, waitForSend);
	}

	/**
	 * Sends a packet for this component and waits for a reply
	 * to that packet. Only packets that support sequence numbers
	 * can be used for this call.
	 * The call returns null when a timeout condition occurs. 
	 */
	public default AbstractPacket sendAndWait(final AbstractPacket request) {
		final RPCManager rpcManager = getCore().getRPCManager();
		final PacketSink sink = getConnection(true);
		return rpcManager.sendAndWait(sink, request);
	}

	/**
	 * Sends a bunch of request packets and waits until all of
	 * them have either triggered replies or have timed out.
	 * 
	 * This results in a map of request -> reply, if a request timed
	 * out, the reply will be NULL.
	 */
	public default Map<? extends AbstractPacket, ? extends AbstractPacket> sendAndWait(final java.util.Collection<? extends AbstractPacket> requests) {
		final RPCManager rpcManager = getCore().getRPCManager();
		final PacketSink sink = getConnection(true);
		return rpcManager.sendAndWait(sink, requests);
	}

	@Override
	public default StreamableConnection getConnection() {
		return getConnection(true);
	}
	
	/**
	 * Gets the connection to the server.
	 * 
	 * The connection will be established, if there is no live connection
	 * and connectIfNotConnected is true.
	 * By default a component uses the connection provided by the
	 * core that launched it. The component may override the method
	 * to create its own connections if it need to connect to
	 * a specific server. 
	 */
	public default StreamableConnection getConnection(final boolean connectIfNotConnected) {
		final AppCore core = getCore();
		return core.getConnection(connectIfNotConnected);
	}

	/**
	 * Adds an observer to this components connection.
	 */
	public default void addConnectionObserver(final Observer<?> observer, final ObserverNotificationPolicy notificationPolicy) {
		getCore().addConnectionObserver(observer, notificationPolicy);
	}

	/**
	 * Removes an observer from this components connection.
	 */
	public default void remConnectionObserver(final Observer<?> observer) {
		getCore().remConnectionObserver(observer);
	}

	// --------------------------------------------------------------
	// ---
	// --- ActivityManager implementation
	// ---
	// --------------------------------------------------------------
	/**
	 * Gets the BackgroundActiviyManager for this component
	 * (This returns NULL if the component doesn't have an activity manager)
	 */
	public default BackgroundActivityManager getActivityManager() {
		return getCore().getActivityManager();
	}

	@Override
	public default void startActivity(final BackgroundActivity activity) {
		final ActivityManager manager = getActivityManager();
		if (manager != null)
			manager.startActivity(activity);
	}

	@Override
	public default void finishActivity(final BackgroundActivity activity, final ErrorMessage result) {
		final ActivityManager manager = getActivityManager();
		if (manager != null)
			manager.finishActivity(activity, result);
	}

	// --------------------------------------------------------------
	// ---
	// --- Feature implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public default Class<?> getFeatureType() {
		return AppComponent.class;
	}

}

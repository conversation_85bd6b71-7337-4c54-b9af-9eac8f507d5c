package ch.eisenring.app.client.feedback;

import static ch.eisenring.core.codetable.MessageClassCode.ERROR;
import static ch.eisenring.core.codetable.MessageClassCode.WARN;
import ch.eisenring.app.client.AppCore;
import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.codetable.MessageClassCode;
import ch.eisenring.gui.window.FeedbackHandler;

public final class AppFeedbackHandler implements FeedbackHandler {

	private final AppCore core;
	
	public AppFeedbackHandler(final AppCore core) {
		this.core = core;
	}

	@Override
	public void warn(final CharSequence warning) {
		final AppStatusMessage msg = new AppStatusMessage(warning, WARN);
		core.getAppStatusHistory().add(msg);
	}

	@Override
	public void error(final CharSequence error) {
		final AppStatusMessage msg = new AppStatusMessage(error, ERROR);
		core.getAppStatusHistory().add(msg);
	}

	@Override
	public void message(final CharSequence message, final MessageClassCode type) {
		final AppStatusMessage msg = new AppStatusMessage(message, type);
		core.getAppStatusHistory().add(msg);
	}

	@Override
	public void message(final ErrorMessage message) {
		final AppStatusMessage msg = new AppStatusMessage(message);
		core.getAppStatusHistory().add(msg);
	}

}

package ch.eisenring.app.client.platform;

import java.io.File;

import ch.eisenring.core.platform.Platform;

public abstract class WindowsConfiguration extends PlatformConfiguration {

	@Override
	protected void configureImpl(final Platform platform) throws Exception {
		configureRegistry(platform);
	}

	protected abstract void configureRegistry(final Platform platform) throws Exception;

	protected String getAppDir() {
		try {
			File appDir = new File(".."); 
			appDir = appDir.getCanonicalFile();
			return Platform.getPlatform().toPlatformSpecificPath(appDir.getAbsolutePath());
		} catch (final Exception e) {
			throw new RuntimeException(e.getMessage(), e);
		}
	}
	
}

package ch.heag.benchmarks.collections;

import ch.heag.benchmarks.BenchmarkTestObject;

/**
 * Testing element with bad hashCode distribution (4 possible hashCodes)
 */
public class ElementBad implements BenchmarkTestObject  {

	private final int value;
	
	public ElementBad(final int value) {
		this.value = value;
	}

	@Override
	public int hashCode() {
		return value & 0x8020;
	}

	@Override
	public boolean equals(final Object o) {
		return o instanceof ElementBad && ((ElementBad) o).value == value;
	}

	@Override
	public String toString() {
		return "ElementBad(" + value + ")";
	}

}

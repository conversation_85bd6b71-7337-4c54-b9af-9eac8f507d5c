package ch.heag.benchmarks.collections.set;

import ch.eisenring.core.iterators.Filter;
import ch.eisenring.core.util.sizeof.MemSize;
import ch.heag.benchmarks.BenchmarkTestObject;
import ch.heag.benchmarks.Benchmarkable;
import ch.heag.benchmarks.collections.CollectionTestData;

public abstract class AbstractSetBenchmarkable implements Benchmarkable {

	protected final SetProvider setProvider;
	protected final String testCaseName;
	protected final int testSize;
	protected final CollectionTestData testData;

	protected final static Filter<Object> MEMSIZE_FILTER = new Filter<Object>() {
		@Override
		public boolean accepts(final Object object) {
			if (object instanceof Integer)
				return false;
			return !(object instanceof BenchmarkTestObject);
		}
	};

	protected AbstractSetBenchmarkable(
			final String testName,
			final SetProvider setProvider,
			final int testSize,
			final CollectionTestData testData) {
		this.testCaseName = testName;
		this.setProvider = setProvider;
		this.testSize = testSize;
		this.testData = testData;
	}

	@Override
	public void prepareTest() {
		// NO-OP
	}

	@Override
	public void teardownTest() {
		// NO-OP
	}

	@Override
	public String getTestName() {
		return setProvider.getTestClassName() + "<" + testData.getDataName() + ">." + testCaseName;
	}
	
	@Override
	public String getTestCaseName() {
		return testCaseName;
	}

	@Override
	public String getTestClassName() {
		return setProvider.getTestClassName();
	}

	@Override
	public String getTestDataName() {
		return testData.getDataName();
	}

	@Override
	public int getSize() {
		return testSize;
	}

	@Override
	public Object getMemSizeObject() {
		return null;
	}

	@Override
	public long getMemSize(final Object object) {
		return MemSize.sizeOf(object, MEMSIZE_FILTER);
	}

	@Override
	public String getDescription() {
		return "No description";
	}

}

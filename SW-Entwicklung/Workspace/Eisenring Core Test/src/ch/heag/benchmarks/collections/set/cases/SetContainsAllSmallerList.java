package ch.heag.benchmarks.collections.set.cases;

import java.util.Set;

import ch.heag.benchmarks.AbstractBenchmark;
import ch.heag.benchmarks.collections.AbstractCollectionBenchmark;
import ch.heag.benchmarks.collections.set.AbstractSetBenchmarkable;
import ch.heag.benchmarks.collections.set.SetProvider;

public class SetContainsAllSmallerList extends AbstractSetBenchmarkable {

	private java.util.Collection<Object> sourceList;
	private Set<Object> theSet;
	
	public SetContainsAllSmallerList(SetProvider provider, int size) {
		super("Set("+(size+1)+").containsAll(List["+size+"])==true", provider, size, AbstractCollectionBenchmark.DATA_GOOD);
	}

	@Override
	public void prepareTest() {
		sourceList = new java.util.ArrayList<>(testSize + 1);
		for (int i=0; i<testSize; ++i) {
			Object e = testData.data[i];
			sourceList.add(e);
		}
		theSet = setProvider.createSet(sourceList.size() + 1);
		theSet.addAll(sourceList);
		theSet.add(null);
	}

	@Override
	public void teardownTest() {
		sourceList = null;
		theSet = null;
	}

	@SuppressWarnings("unused")
	@Override
	public int performTest(int iterationCount) {
		int result = 0;
		while (--iterationCount > 0) {
			AbstractBenchmark.checkTimeout();
			if (!theSet.containsAll(sourceList))
				throw new RuntimeException("containsAll mismatch, expected true, is false");
		}
		return result;
	}

	public Object getMemSizeObject() {
		return theSet;
	}

	@Override
	public String getDescription() {
		return "Prebuild and load Set, then measure containsAll(List)."
				+ "\nThe Set contains one entry that is not in the argument list.";
	}

}

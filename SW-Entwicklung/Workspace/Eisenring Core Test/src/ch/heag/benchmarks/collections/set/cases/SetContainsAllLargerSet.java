package ch.heag.benchmarks.collections.set.cases;

import java.util.Set;

import ch.heag.benchmarks.AbstractBenchmark;
import ch.heag.benchmarks.collections.AbstractCollectionBenchmark;
import ch.heag.benchmarks.collections.set.AbstractSetBenchmarkable;
import ch.heag.benchmarks.collections.set.SetProvider;

public class SetContainsAllLargerSet extends AbstractSetBenchmarkable {

	private java.util.Collection<Object> sourceSet;
	private Set<Object> theSet;
	
	public SetContainsAllLargerSet(SetProvider provider, int size) {
		super("Set("+size+").containsAll(Set["+(size+1)+"])==false", provider, size, AbstractCollectionBenchmark.DATA_GOOD);
	}

	@Override
	public void prepareTest() {
		sourceSet = new java.util.HashSet<>(testSize + 1);
		for (int i=0; i<testSize; ++i) {
			Object e = testData.data[i];
			sourceSet.add(e);
		}
		theSet = setProvider.createSet(sourceSet.size());
		theSet.addAll(sourceSet);
		sourceSet.add(null);
	}

	@Override
	public void teardownTest() {
		sourceSet = null;
		theSet = null;
	}

	@SuppressWarnings("unused")
	@Override
	public int performTest(int iterationCount) {
		int result = 0;
		while (--iterationCount > 0) {
			AbstractBenchmark.checkTimeout();
			if (theSet.containsAll(sourceSet))
				throw new RuntimeException("containsAll mismatch, expected false, is true");
		}
		return result;
	}

	public Object getMemSizeObject() {
		return theSet;
	}

	@Override
	public String getDescription() {
		return "Prebuild and load Set, then measure containsAll(Set).";
	}

}

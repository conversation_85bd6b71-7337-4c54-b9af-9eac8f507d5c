package ch.heag.benchmarks.collections.set.cases;

import java.util.Set;

import ch.heag.benchmarks.AbstractBenchmark;
import ch.heag.benchmarks.collections.CollectionTestData;
import ch.heag.benchmarks.collections.set.AbstractSetBenchmarkable;
import ch.heag.benchmarks.collections.set.SetProvider;

public class SetAddDistinctPresized extends AbstractSetBenchmarkable {

	public SetAddDistinctPresized(SetProvider provider, int size, CollectionTestData testData) {
		super("new("+size+").add(distinct, "+size+")", provider, size, testData);
	}

	@Override
	public int performTest(int iterationCount) {
		int result = 0;
		while (--iterationCount > 0) {
			AbstractBenchmark.checkTimeout();
			final Set<Object> set = createAndLoad();
			result += set.size();
		}
		return result;
	}

	private Set<Object> createAndLoad() {
		final Object[] testElements = testData.data;
		final Set<Object> set = setProvider.createSet(testSize);
		for (int i=0; i<testSize; ++i) {
			set.add(testElements[i]);
		}
		return set;
	}

	@Override
	public Object getMemSizeObject() {
		return createAndLoad();
	}

	@Override
	public String getDescription() {
		return "Creates Set with specified capacity and adds N distinct items using add(E)."
				+ "\nMeasures mainly the performance of add when capacity does not grow.";
	}

}

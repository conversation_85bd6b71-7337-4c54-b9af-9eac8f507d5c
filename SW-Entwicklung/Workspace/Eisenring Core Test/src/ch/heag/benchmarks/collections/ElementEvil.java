package ch.heag.benchmarks.collections;

import ch.heag.benchmarks.BenchmarkTestObject;

/**
 * Testing element with all elements having the same hashCode
 */
public class ElementEvil implements BenchmarkTestObject  {

	private final int value;
	
	public ElementEvil(final int value) {
		this.value = value;
	}

	@Override
	public int hashCode() {
		return 616;
	}

	@Override
	public boolean equals(final Object o) {
		return o instanceof ElementEvil && ((ElementEvil) o).value == value;
	}

	@Override
	public String toString() {
		return "ElementEvil(" + value + ")";
	}

}

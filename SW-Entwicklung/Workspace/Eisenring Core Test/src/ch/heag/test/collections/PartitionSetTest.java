//package ch.heag.test.collections;
//
//import java.util.Set;
//
//import junit.framework.TestSuite;
//
//import org.junit.runner.RunWith;
//import org.junit.runners.AllTests;
//
//import ch.eisenring.core.collections.impl.PartitionSet;
//
//import com.google.common.collect.testing.SetTestSuiteBuilder;
//import com.google.common.collect.testing.TestStringSetGenerator;
//import com.google.common.collect.testing.features.CollectionFeature;
//import com.google.common.collect.testing.features.CollectionSize;
//import com.google.common.collect.testing.features.SetFeature;
//
//@RunWith(AllTests.class)
//public class PartitionSetTest {
//
//	public static TestSuite suite() {
//		TestSuite test =  
//				SetTestSuiteBuilder.using(
//				// This class is responsible for creating the collection
//				// And providing data, which can be put into the collection
//				// Here we use a abstract generator which will create strings
//				// which will be put into the collection
//				new TestStringSetGenerator() {
//					@Override
//					public Set<String> create(String[] elements) {
//						// Fill here your collection with the given elements
//						final Set<String> set = new PartitionSet<>();
//						for (final String element : elements) {
//							set.add(element);
//						}
//						return set;
//					}
//				})
//				// The name of the test suite
//				.named("PartitionSet")
//				// Here we give a hit what features our collection supports
//				.withFeatures(SetFeature.GENERAL_PURPOSE,
//							CollectionFeature.ALLOWS_NULL_QUERIES,
//							CollectionFeature.ALLOWS_NULL_VALUES,
//							CollectionFeature.FAILS_FAST_ON_CONCURRENT_MODIFICATION,
//							CollectionFeature.NON_STANDARD_TOSTRING,
//							CollectionSize.ANY)
//				.createTestSuite();
//		return test;
//
//	}	
//	
//}

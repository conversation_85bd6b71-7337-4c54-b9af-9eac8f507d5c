package ch.heag.test.collections;

import java.util.Set;

import junit.framework.TestSuite;

import org.junit.runner.RunWith;
import org.junit.runners.AllTests;

import ch.eisenring.core.collections.impl.ArraySet;

import com.google.common.collect.testing.SetTestSuiteBuilder;
import com.google.common.collect.testing.features.CollectionFeature;
import com.google.common.collect.testing.features.CollectionSize;
import com.google.common.collect.testing.features.SetFeature;

@RunWith(AllTests.class)
public class ArraySetBadHashTest {

	public static TestSuite suite() {
		TestSuite test =  
				SetTestSuiteBuilder.using(
				// This class is responsible for creating the collection
				// And providing data, which can be put into the collection
				// Here we use a abstract generator which will create strings
				// which will be put into the collection
				new BadHashTestSetGenerator() {
					@Override
					public Set<Object> create(Object ... elements) {
						// Fill here your collection with the given elements
						final Set<Object> set = new ArraySet<>();
						for (final Object element : elements) {
							set.add(element);
						}
						return set;
					}
				})
				// The name of the test suite
				.named("ArraySetBadHash")
				// Here we give a hit what features our collection supports
				.withFeatures(SetFeature.GENERAL_PURPOSE,
								CollectionFeature.ALLOWS_NULL_VALUES,
								//CollectionFeature.SERIALIZABLE,
								CollectionFeature.FAILS_FAST_ON_CONCURRENT_MODIFICATION,
								CollectionFeature.NON_STANDARD_TOSTRING,
								CollectionSize.ANY)
				.createTestSuite();
		return test;

	}	
	
}

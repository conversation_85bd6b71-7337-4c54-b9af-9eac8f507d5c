package ch.heag.test.collections;

import org.junit.runner.RunWith;
import org.junit.runners.Suite;

@RunWith(Suite.class)
@Suite.SuiteClasses({
//	PartitionListTest.class,
//	PartitionSetBadHashTest.class,
//	PartitionSetTest.class,
//	HashSet2Test.class,
	
	ArrayLookupBadHashTest.class,
	AtomicArrayLookupBadHashTest.class,
	ArrayLookupTest.class,
	AtomicArrayLookupTest.class,
	
	ArraySetBadHashTest.class,
	AtomicArraySetBadHashTest.class,
	ArraySetTest.class,
	AtomicArraySetTest.class,
	
	PolySetTest.class
})
public class CollectionsTestSuite {

}

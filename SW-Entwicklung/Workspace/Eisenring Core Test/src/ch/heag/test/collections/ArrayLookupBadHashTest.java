package ch.heag.test.collections;

import java.util.Map;
import java.util.Map.Entry;

import junit.framework.TestSuite;

import org.junit.runner.RunWith;
import org.junit.runners.AllTests;

import ch.eisenring.core.collections.impl.ArrayLookup;

import com.google.common.collect.testing.MapTestSuiteBuilder;
import com.google.common.collect.testing.features.CollectionFeature;
import com.google.common.collect.testing.features.CollectionSize;
import com.google.common.collect.testing.features.MapFeature;

@RunWith(AllTests.class)
public class ArrayLookupBadHashTest {

	public static TestSuite suite() {
		TestSuite test =  
				MapTestSuiteBuilder.using(
				// This class is responsible for creating the collection
				// And providing data, which can be put into the collection
				// Here we use a abstract generator which will create strings
				// which will be put into the collection
				new BadHashTestMapGenerator() {
					@SuppressWarnings("unchecked")
					@Override
					public Map<Object, Object> create(Object ... entries) {
						final Map<Object, Object> map = new ArrayLookup<>();
						for (final Object object : entries) {
							final Entry<Object, Object> entry = (Entry<Object, Object>) object;
							map.put(entry.getKey(), entry.getValue());
						}
						return map;
					}
				})
				// The name of the test suite
				.named("ArrayLookupBadHash")
				// Here we give a hit what features our collection supports
				.withFeatures(MapFeature.ALLOWS_NULL_KEYS,
						MapFeature.ALLOWS_NULL_VALUES,
						MapFeature.ALLOWS_NULL_QUERIES,
						MapFeature.GENERAL_PURPOSE,
						MapFeature.SUPPORTS_PUT,
						MapFeature.SUPPORTS_REMOVE,
						MapFeature.FAILS_FAST_ON_CONCURRENT_MODIFICATION,
						CollectionFeature.NON_STANDARD_TOSTRING,
						CollectionSize.ANY)
				.createTestSuite();
		return test;

	}	
	
}

package ch.heag.vmsupport;

import java.lang.reflect.Field;

/**
 * Interface for accessing a field (the exact method of access depends
 * on the implementation selected by the factory).
 */
public interface FieldAccessor {

	/**
	 * Gets the type of the underlying field
	 */
	public abstract Class<?> getType();

	/**
	 * Gets the field accessed by this accessor
	 */
	public abstract Field getField();

	// --------------------------------------------------------------
	// ---
	// --- API
	// ---
	// --------------------------------------------------------------
	/**
	 * Gets the field value, if the field is a primitive the
	 * value is boxed in its java.lang wrapper type.
	 */
	public Object getAutoBoxing(final Object object) {
		switch (type) {
			case TYPE_OBJECT: return get(object);
			case TYPE_BOOLEAN: return getBoolean(object) ? Boolean.TRUE : Boolean.FALSE;
			case TYPE_BYTE: return Byte.valueOf(getByte(object));
			case TYPE_SHORT: return Short.valueOf(getShort(object));
			case TYPE_CHAR: return Character.valueOf(getChar(object));
			case TYPE_INT: return Integer.valueOf(getInt(object));
			case TYPE_LONG: return Long.valueOf(getLong(object));
			case TYPE_FLOAT: return Float.valueOf(getFloat(object));
			case TYPE_DOUBLE: return Double.valueOf(getDouble(object));
			default: throw new IllegalStateException("field type not supported");
		}
	}

	/**
	 * Sets the field value, if the field is a primitive the
	 * value is unboxed from the value object.
	 */
	public void setAutoBoxing(final Object object, final Object value) {
		switch (type) {
			case TYPE_OBJECT:
				set(object, value);
				return;
			case TYPE_BOOLEAN:
				setBoolean(object, ((Boolean) value).booleanValue());
				return;
			case TYPE_BYTE:
				setByte(object, ((Number) value).byteValue());
				return;
			case TYPE_SHORT:
				setShort(object, ((Number) value).shortValue());
				return;
			case TYPE_CHAR:
				setChar(object, ((Character) value).charValue());
				return;
			case TYPE_INT:
				setInt(object, ((Number) value).intValue());
				return;
			case TYPE_LONG:
				setLong(object, ((Long) value).longValue());
				return;
			case TYPE_FLOAT:
				setFloat(object, ((Number) value).floatValue());
				return;
			case TYPE_DOUBLE:
				setDouble(object, ((Number) value).doubleValue());
				return;
			default: throw new IllegalStateException("field type not supported");
		}
	}

	/**
	 * Gets the field value as object. This bypasses all type safety,
	 * when this is called on an accessor of a primitive field, the
	 * result is completely undetermined.
	 */
	public abstract Object get(final Object object);

	public abstract void set(final Object object, final Object value);

	public abstract boolean getBoolean(final Object object);

	public abstract void setBoolean(final Object object, final boolean value);

	public abstract char getChar(final Object object);

	public abstract void setChar(final Object object, final char value);
	
	public abstract byte getByte(final Object object);

	public abstract void setByte(final Object object, final byte value);

	public abstract short getShort(final Object object);

	public abstract void setShort(final Object object, final short value);

	public abstract int getInt(final Object object);

	public abstract void setInt(final Object object, final int value);

	public abstract long getLong(final Object object);

	public abstract void setLong(final Object object, final long value);

	public abstract float getFloat(final Object object);

	public abstract void setFloat(final Object object, final float value);

	public abstract double getDouble(final Object object);

	public abstract void setDouble(final Object object, final double value);
	
}

package ch.eisenring.email.server.network;

import ch.eisenring.email.server.MailServiceServer;
import ch.eisenring.network.ClassListPacketDispatcher;
import ch.eisenring.network.PacketDispatcher;

public final class MailServicePacketDispatcher {

	private MailServicePacketDispatcher() {
	}

	public static PacketDispatcher create(final MailServiceServer server) {
		return new ClassListPacketDispatcher(new Class<?>[] {
				EMailHandler.class
		}, new Class<?>[] { MailServiceServer.class }, new Object[] { server });
	}
	
}

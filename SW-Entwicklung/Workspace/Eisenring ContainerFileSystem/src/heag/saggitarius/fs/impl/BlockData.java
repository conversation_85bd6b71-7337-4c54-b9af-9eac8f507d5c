package heag.saggitarius.fs.impl;

import ch.eisenring.core.crypt.CryptUtil;
import heag.saggitarius.fs.FSException;
import heag.saggitarius.util.RawDataAccess;

class BlockData extends Block {
	
	protected final byte[] data;

	BlockData(final FSCore fs, final long lbn) {
		super(fs, lbn);
		this.data = new byte[fs.getBytesPerBlock()];
	}

	@Override
	public BlockType<?> getBlockType() {
		return BlockType.DATA;
	}

	@Override
	public void destroy() {
		super.destroy();
		CryptUtil.destroy(data);
	}

	// --------------------------------------------------------------
	// ---
	// --- Block private API
	// ---
	// --------------------------------------------------------------
	int getSizeOfUsed() {
		return fs.getBytesPerBlock();
	}

	@Override
	void toBuffer(byte[] buffer, int offset) throws FSException {
		super.toBuffer(buffer, offset);
		RawDataAccess.setBytes(buffer, offset, data);
	}

	@Override
	void fromBuffer(byte[] buffer, int offset) throws FSException {
		super.fromBuffer(buffer, offset);
		RawDataAccess.getBytes(buffer, offset, data);
	}

	@Override
	public void initBlock() throws FSException {
		super.initBlock();
		fs.unused.setBytes(data, 0, data.length);
	}

}

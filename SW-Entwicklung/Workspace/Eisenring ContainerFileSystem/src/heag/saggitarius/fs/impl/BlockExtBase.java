package heag.saggitarius.fs.impl;

import heag.saggitarius.fs.FSException;
import heag.saggitarius.util.RawDataAccess;

import java.util.Arrays;

import ch.eisenring.core.crypt.CryptUtil;

/**
 * The list block is used to extend the list of LBN's a file consists of,
 * if more LBN's than fit into the file entry block are needed.
 * 
 * Structure:
 * 0x0000	4		block type id
 * 0x0004	4		reserved (0)
 * 0x0008	8		not used by this block type (-1)
 * 0x0010	var		As many LBN's as fit into the block (varies with bytesPerBlock)
 * 					= (bytesPerBlock - 16) >> 3
 * 					with 2K blocks: 254
 * 					with 4K blocks: 510
 * 					with 8K blocks: 1022
 * 					with 16K blocks: 2046
 * 					with 32K blocks: 4094
 * 					with 64K blocks: 8190
 */
abstract class BlockExtBase extends BlockBase implements LBNList {

	final long[] lbnList;
	long parentLBN;

	protected BlockExtBase(final FSCore fs, final long lbn) {
		super(fs, lbn);
		lbnList = new long[fs.extNCount];
	}

	// --------------------------------------------------------------
	// ---
	// --- LBNList implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public int getLBNListSize() {
		return lbnList.length;
	}

	@Override
	public long getLBNList(final int index) {
		return lbnList[index];
	}

	@Override
	public void setLBNList(final int index, final long lbn) {
		updateDirty(lbnList[index], lbn);
		lbnList[index] = lbn;
	}

	// --------------------------------------------------------------
	// ---
	// --- Block specific API
	// ---
	// --------------------------------------------------------------
	public long getParentLBN() {
		return parentLBN;
	}

	public void setParentLBN(final long parentLBN) {
		updateDirty(this.parentLBN, parentLBN);
		this.parentLBN = parentLBN;
	}

	// --------------------------------------------------------------
	// ---
	// --- Block private API
	// ---
	// --------------------------------------------------------------
	@Override
	int getSizeOfUsed() {
		return fs.getBytesPerBlock();
	}

	@Override
	void toBuffer(final byte[] buffer, final int offset) throws FSException {
		super.toBuffer(buffer, offset);
		RawDataAccess.setLong(buffer, offset + FSConstants.BS_EX_PARENTLBN, parentLBN);
		fs.unused.setLong(buffer, offset + FSConstants.BS_EX_RESERVED8);
		RawDataAccess.setLongs(buffer, offset + FSConstants.BS_EX_LBNLIST, lbnList);
	}

	@Override
	void fromBuffer(byte[] buffer, int offset) throws FSException {
		super.fromBuffer(buffer, offset);
		parentLBN = RawDataAccess.getLong(buffer, offset + FSConstants.BS_EX_PARENTLBN);
		RawDataAccess.getLongs(buffer, offset + FSConstants.BS_EX_LBNLIST, lbnList);
	}

	@Override
	public void initBlock() throws FSException {
		super.initBlock();
		parentLBN = FSConstants.NULL_LBN;
		Arrays.fill(lbnList, FSConstants.NULL_LBN);
	}

	@Override
	public void destroy() {
		super.destroy();
		CryptUtil.destroy(lbnList);
		parentLBN = 0;
	}

}

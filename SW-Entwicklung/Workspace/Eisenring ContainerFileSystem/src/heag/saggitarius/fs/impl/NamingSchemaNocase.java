package heag.saggitarius.fs.impl;

import heag.saggitarius.fs.ErrorCode;
import heag.saggitarius.fs.FSException;


final class NamingSchemaNocase extends NamingSchema {

	static char toLower(final char c) {
		return c >= ASCII_TO_LOWER.length ? c : ASCII_TO_LOWER[c];
	}

	final static char[] ASCII_TO_LOWER = {
		0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F,
		0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C, 0x1D, 0x1E, 0x1F,
		0x20, 0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C, 0x2D, 0x2E, 0x2F,
		0x30, 0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37, 0x38, 0x39, 0x3A, 0x3B, 0x3C, 0x3D, 0x3E, 0x3F,
		0x40, 0x61, 0x62, 0x63, 0x64, 0x65, 0x66, 0x67, 0x68, 0x69, 0x6A, 0x6B, 0x6C, 0x6D, 0x6E, 0x6F,
		0x70, 0x71, 0x72, 0x73, 0x74, 0x75, 0x76, 0x77, 0x78, 0x79, 0x7A, 0x5B, 0x5C, 0x5D, 0x5E, 0x5F,
		0x60, 0x61, 0x62, 0x63, 0x64, 0x65, 0x66, 0x67, 0x68, 0x69, 0x6A, 0x6B, 0x6C, 0x6D, 0x6E, 0x6F,
		0x70, 0x71, 0x72, 0x73, 0x74, 0x75, 0x76, 0x77, 0x78, 0x79, 0x7A, 0x7B, 0x7C, 0x7D, 0x7E, 0x7F
	};

	private final FSCore fs;

	NamingSchemaNocase(final FSCore fs) {
		this.fs = fs;
	}

	@Override
	public int getNameHashRaw(final CharSequence objectName) {
		final int length = objectName.length();
		int hash = length;
		for (int i=0; i<length; ++i) {
			hash = (hash * 31) ^ toLower(objectName.charAt(i));
		}
		return hash;
	}

	@Override
	public int getNameHash(final CharSequence objectName) throws FSException {
		final int length = objectName.length();
		if (length > FSConstants.OBJECT_NAME_LIMIT)
			throw new FSException(ErrorCode.ERR_OBJECT_NAME_TOO_LONG);
		int hash = getNameHashRaw(objectName);
		for (int i=0; i<length; ++i) {
			hash = (hash * 31) ^ toLower(objectName.charAt(i));
		}
		if (hash < 0)
			hash ^= 0x8000_0001;
		hash %= fs.getHashTableSize();
		return hash;
	}

	@Override
	public boolean isValidObjectname(final CharSequence objectName) {
		if (objectName == null)
			return false;
		final int length = objectName.length();
		if (length <= 0)
			return false;
		if (length > FSConstants.OBJECT_NAME_LIMIT)
			return false;
		for (int i=0; i<length; ++i) {
			if (!isValidObjectnameCharacter(objectName.charAt(i)))
				return false;
		}
		return true;
	}

	boolean isValidObjectnameCharacter(final char character) {
		if (isPathSeparatorCharacter(character))
			return false;
		return true;
	}

	@Override
	public boolean isPathSeparatorCharacter(final char character) {
		return character == '/' || character == '\\';
	}

	@Override
	public boolean isSameObjectname(final CharSequence name1, final CharSequence name2) {
		if (name1 == null || name2 == null)
			return false;
		if (name1 == name2)
			return true;
		final int length = name1.length();
		if (name2.length() != length)
			return false;
		for (int i=0; i<length; ++i) {
			final char c1 = toLower(name1.charAt(i));
			final char c2 = toLower(name2.charAt(i));
			if (c1 != c2)
				return false;
		}
		return true;
	}

}

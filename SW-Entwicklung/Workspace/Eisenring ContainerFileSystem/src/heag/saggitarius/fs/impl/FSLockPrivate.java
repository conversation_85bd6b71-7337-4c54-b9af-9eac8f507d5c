package heag.saggitarius.fs.impl;

import heag.saggitarius.fs.ErrorCode;
import heag.saggitarius.fs.FSException;


final class FSLockPrivate implements FSConstants {

	/**
	 * File system instance this lock is located in
	 */
	final FSCore fs;

	/**
	 * Locks are internally chained
	 */
	FSLockPrivate succ;

	/**
	 * Contains the locking mode: SHARED / EXCLUSIVE
	 */
	int mode;

	/**
	 * Contains the type of the locked object TYPE_FILE or TYPE_DIRECTORY
	 */
	int objectType;

	/**
	 * The node locked by this lock (dir or file block)
	 */
	long lbn;

	FSLockPrivate(final FSCore fs, final long nodeLBN, final int objectType, final int mode) throws FSException {
		this.fs = fs;
		this.mode = mode;
		this.lbn = nodeLBN;
		this.objectType = objectType;
	}

	int getObjectType() {
		return objectType;
	}

	void ensureValid() throws FSException {
		if (!isValid())
			throw new FSException(ErrorCode.ERR_OBJECT_INVALID);
	}

	/**
	 * Gets the file system in which the lock exists 
	 */
	FSCore getFileSystem() {
		return fs;
	}

	/**
	 * Returns true if this lock is valid
	 */
	boolean isValid() {
		return mode == MODE_SHARED || mode == MODE_EXCLUSIVE;
	}

	/**
	 * Attempts to alter this locks mode. May fail with exception,
	 * leaving the lock unaltered.
	 * 
	 * Returns the previous mode if successful.
	 */
	int changeMode(final int mode) throws FSException {
		return fs.locks.changeMode(this, mode);
	}

	/**
	 * Gets the mode of this lock SHARED / EXCLUSIVE
	 */
	int getMode() {
		return mode;
	}

	BlockNode getBlock() throws FSException {
		ensureValid();
		return fs.getBlock(lbn, BlockType.NODE);
	}

	@SuppressWarnings("unchecked")
	<B extends BlockNode> B getBlock(final BlockType<B> blockType) throws FSException {
		final BlockNode block = getBlock();
		if (blockType.equals(block.getBlockType()))
			return (B) block;
		if (BlockType.DIRNODE.equals(blockType)) {
			throw new FSException(ErrorCode.ERR_NOT_A_DIRECTORY);
		} else if (BlockType.FILENODE.equals(blockType)) {
			throw new FSException(ErrorCode.ERR_NOT_A_FILE);
		}
		throw new FSException(ErrorCode.ERR_BLOCK_TYPE_MISMATCH);
	}	
}

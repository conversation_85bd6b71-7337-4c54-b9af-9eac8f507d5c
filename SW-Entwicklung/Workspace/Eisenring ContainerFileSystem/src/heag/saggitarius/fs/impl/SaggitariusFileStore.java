package heag.saggitarius.fs.impl;

import java.io.IOException;
import java.nio.file.FileStore;
import java.nio.file.attribute.FileAttributeView;
import java.nio.file.attribute.FileStoreAttributeView;

public class SaggitariusFileStore extends FileStore {

	private FSCore fs;

	SaggitariusFileStore(final FSCore fs) {
		this.fs = fs;
	}

	@Override
	public String name() {
		return "SaggitariusFileStore";
	}

	@Override
	public String type() {
		return "ContainerFileStore";
	}

	@Override
	public boolean isReadOnly() {
		return false;
	}

	@Override
	public long getTotalSpace() throws IOException {
		synchronized (fs.getLock()) {
			final BlockRoot root = fs.getRootBlock();
			final long count = root.getEndLBN() - root.getStartLBN();
			return count * fs.getBytesPerBlock();
		}
	}

	@Override
	public long getUsableSpace() throws IOException {
		return getTotalSpace();
	}

	@Override
	public long getUnallocatedSpace() throws IOException {
		return getTotalSpace();
	}

	@Override
	public boolean supportsFileAttributeView(final Class<? extends FileAttributeView> type) {
		// TODO Auto-generated method stub
		return false;
	}

	@Override
	public boolean supportsFileAttributeView(final String name) {
		// TODO Auto-generated method stub
		return false;
	}

	@Override
	public <V extends FileStoreAttributeView> V getFileStoreAttributeView(final Class<V> type) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public Object getAttribute(final String attribute) throws IOException {
		// TODO Auto-generated method stub
		return null;
	}

}

package heag.saggitarius.fs.impl;

import heag.saggitarius.fs.ErrorCode;
import heag.saggitarius.fs.FSException;
import heag.saggitarius.storage.BlockDevice;
import heag.saggitarius.storage.BlockSize;

import java.util.HashMap;
import java.util.Map;

/**
 * Holds the partition definition and other instance related structural information.
 */
abstract class FSStructure implements FSConstants {

	protected final Object fsLock = new Object();
	protected BlockSize blockSize;

	public final Object getLock() {
		return fsLock;
	}

	// --------------------------------------------------------------
	// ---
	// --- Describes the partition on which the file system exists
	// ---
	// --------------------------------------------------------------
	/** Number of bytes in each block */
	protected int bytesPerBlock;

	/** Number of bits in a bitmap block */
	protected int bitsPerBitmapBlock;

	/** Number of LBN's in a file node */
	protected int ext0Count;

	/** Number of LBN's in a list block */
	protected int extNCount;

	/** Max block count possible using only EXT0 */
	protected long maxExt0Count;
	
	/** Max block count possible using EXT0 - EXT1 */
	protected long maxExt1Count;

	/** Max block count possible using EXT0 - EXT2 */
	protected long maxExt2Count;

	/** Max block count possible using EXT0 - EXT3 */
	protected long maxExt3Count;

	/** Max block count possible using EXT0 - EXT4 */
	protected long maxExt4Count;

	/** Data block counts below LBNList type X blocks */
	protected Map<BlockType<?>, Long> lbnExtDataBlocks = new HashMap<>();

	/** Number of entries in name hash tables */
	protected int hashTableSize;

	/** The first LBN of the partition */
	protected long startLBN;
	
	/** The last LBN of the partition plus one (thats the first LBN after the partition) */
	protected long endLBN;
	
	/** The number of LBN's in the partition (this is equal to endLBN - startLBN) */
	protected long lbnCount;

	/** Block range to allocate control blocks from */
	protected long ctrlZoneStart, ctrlZoneEnd;

	/** Block range to allocate data blocks from */
	protected long dataZoneStart, dataZoneEnd;

	// --------------------------------------------------------------
	// ---
	// --- Structure initialization
	// ---
	// --------------------------------------------------------------
	/**
	 * Sets the block size and all derived variables
	 */
	protected void setBlockSize(final BlockSize blockSize) {
		this.blockSize = blockSize;
		this.bytesPerBlock = blockSize.getBytesPerBlock();
		// derived values
		this.bitsPerBitmapBlock = ((bytesPerBlock - FSConstants.BS_BM_BITMAP) >> 3) << 6;
		this.hashTableSize = FSConstants.getHashTableSize(bytesPerBlock);
		this.ext0Count = (bytesPerBlock - FSConstants.BS_FN_L0EXT) >> 3;
		this.extNCount = (bytesPerBlock - FSConstants.BS_EX_LBNLIST) >> 3;
		this.maxExt0Count = ext0Count;
		this.maxExt1Count = lpow(extNCount, 1) + maxExt0Count;
		this.maxExt2Count = lpow(extNCount, 2) + maxExt1Count;
		this.maxExt3Count = lpow(extNCount, 3) + maxExt2Count;
		this.maxExt4Count = lpow(extNCount, 4) + maxExt3Count;
		this.lbnExtDataBlocks.put(BlockType.FILENODE, Long.valueOf(ext0Count));
		this.lbnExtDataBlocks.put(BlockType.EXTLBN1, lpow(extNCount, 1));
		this.lbnExtDataBlocks.put(BlockType.EXTLBN2, lpow(extNCount, 2));
		this.lbnExtDataBlocks.put(BlockType.EXTLBN3, lpow(extNCount, 3));
		this.lbnExtDataBlocks.put(BlockType.EXTLBN4, lpow(extNCount, 4));
	}

	protected void initPartition(final long startLBN, final long endLBN) throws FSException {
		if (startLBN < 0)
			throw new FSException(ErrorCode.ERR_LBN_INVALID);
		if (endLBN <= startLBN)
			throw new FSException(ErrorCode.ERR_LBN_INVALID);
		final long deviceBlockCount;
		try {
			deviceBlockCount = getDevice().getBlockCount();
		} catch (final Exception e) {
			throw new FSException(ErrorCode.ERR_UNKNOWN, e);
		}
		if (endLBN > deviceBlockCount)
			throw new FSException(ErrorCode.ERR_LBN_INVALID);
		this.startLBN = startLBN;
		this.endLBN = endLBN;
		this.lbnCount = endLBN - startLBN;
		if (lbnCount < 16)
			throw new FSException(ErrorCode.ERR_LBN_INVALID);
		
		// derive default LBN numbers
		this.ctrlZoneStart = startLBN + 1;
		this.ctrlZoneEnd = (lbnCount >> 3) + startLBN;
		this.dataZoneStart = ctrlZoneEnd;
		this.dataZoneEnd = endLBN;
	}

	long getDataBlocksPerLBNListEntry(final BlockType<?> blockType) throws FSException {
		Long result = lbnExtDataBlocks.get(blockType);
		if (result != null)
			return result.longValue();
		throw new FSException(ErrorCode.ERR_INTERNAL);
	}

	// --------------------------------------------------------------
	// ---
	// --- Math helpers
	// ---
	// --------------------------------------------------------------
	static long lpow(final long value, int power) {
		if (power == 0)
			return 1L;
		long result = value;
		while (--power > 0) {
			result *= value;
		}
		return result;
	}

	/** 
	 * Divide + ceil
	 */
	static long ldiv(final long value, long divider) {
		return (value + divider - 1) / divider;
	}

	// --------------------------------------------------------------
	// ---
	// --- Structure property getters
	// ---
	// --------------------------------------------------------------
	/**
	 * Returns the size for naming hash tables. varies with block size.
	 */
	public final int getHashTableSize() {
		return hashTableSize;
	}

	/**
	 * Gets the block size of this instance
	 */
	public final BlockSize getBlockSize() {
		return blockSize;
	}

	/**
	 * Gets the block size of this instance
	 */
	public final int getBytesPerBlock() {
		return bytesPerBlock;
	}

	/**
	 * Gets the LBN where the root block is found
	 */
	public final long getRootLBN() throws FSException {
		return startLBN;
	}

	// --------------------------------------------------------------
	// ---
	// --- Structure utility methods
	// ---
	// --------------------------------------------------------------
	/**
	 * Rounds up data size to allocation size
	 * (that is to the next block boundary)
	 */
	public long roundDatasize(final long dataSize) throws FSException {
		return numberOfDataBlocksRequired(dataSize) * bytesPerBlock; 
	}

	/**
	 * Calculates the number of data blocks needed for data size.
	 */
	public long numberOfDataBlocksRequired(final long dataSize) throws FSException {
		return (dataSize + bytesPerBlock - 1) / bytesPerBlock; 
	}

	/**
	 * Calculates the number of ext blocks needed for data size.
	 */
	public long numberOfExtBlocksRequired(final long dataSize) throws FSException {
		long dataBlockCount = numberOfDataBlocksRequired(dataSize);
		if (dataBlockCount <= maxExt0Count) {
			return 0L;
		} else if (dataBlockCount <= maxExt1Count) {
			return 1L;
		} else if (dataBlockCount <= maxExt2Count) {
			// number of data blocks not fitting into ext1
			final long remCount = dataBlockCount - maxExt1Count;
			final long l1cnt = ldiv(remCount, extNCount);
			return l1cnt + 2L;
		} else if (dataBlockCount <= maxExt3Count) {
			// number of data blocks not fitting into ext2
			final long remCount = dataBlockCount - maxExt2Count;
			final long l1cnt = ldiv(remCount, extNCount);
			final long l2cnt = ldiv(l1cnt, extNCount);
			return l1cnt + l2cnt + extNCount + 2; 
		} else if (dataBlockCount <= maxExt4Count) {
			// number of data blocks not fitting into ext3
			final long remCount = dataBlockCount - maxExt2Count;
			final long l1cnt = ldiv(remCount, extNCount);
			final long l2cnt = ldiv(l1cnt, extNCount);
			final long l3cnt = ldiv(l2cnt, extNCount);
			return l1cnt + l2cnt + l3cnt + extNCount + 2; 
		}
		throw new FSException(ErrorCode.ERR_FILE_TOO_LARGE);
	}

	/**
	 * Determines the number of blocks needed for file size,
	 * including all necessary list blocks.
	 */
	long numberOfBlocksRequired(final long dataSize) throws FSException {
		return numberOfDataBlocksRequired(dataSize) + numberOfExtBlocksRequired(dataSize);
	}

	// --------------------------------------------------------------
	// ---
	// --- Template methods
	// ---
	// --------------------------------------------------------------
	public abstract BlockDevice getDevice();

}

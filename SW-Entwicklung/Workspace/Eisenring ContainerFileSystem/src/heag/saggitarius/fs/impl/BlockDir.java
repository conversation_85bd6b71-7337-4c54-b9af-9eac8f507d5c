package heag.saggitarius.fs.impl;

import static heag.saggitarius.fs.SaggitariusConstants.TYPE_DIRECTORY;
import heag.saggitarius.fs.FSException;
import heag.saggitarius.fs.ObjectInfo;
import heag.saggitarius.util.RawDataAccess;

import java.util.Arrays;

import ch.eisenring.core.crypt.CryptUtil;

final class BlockDir extends BlockNode {

	protected final long[] map;

	BlockDir(final FSCore fs, final long lbn) {
		super(fs, lbn);
		map = new long[FSConstants.getHashTableSize(fs.getBytesPerBlock())];
	}

	// --------------------------------------------------------------
	// ---
	// --- Block specific API
	// ---
	// --------------------------------------------------------------
	@Override
	public int getObjectType() {
		return TYPE_DIRECTORY;
	}

	@Override
	public ObjectInfo getObjectInfo() {
		return new ObjectInfoDirectory(this);
	}

	@Override
	public BlockType<?> getBlockType() {
		return BlockType.DIRNODE;
	}

	/**
	 * Gets the LBN at index from hash table
	 */
	public long getHashEntry(final int index) {
		return map[index];
	}

	/**
	 * Sets the LBN at index in hash table
	 */
	public void setHashEntry(final int index, final long lbn) {
		if (map[index] == lbn)
			return;
		map[index] = lbn;
		setDirty(true);
	}

	/**
	 * Gets the number of entries in hash table
	 */
	public int getHashTablesize() {
		return map.length;
	}

	/**
	 * Returns true if this directory is empty.
	 */ 
	public boolean isEmpty() {
		for (final long lbn : map) {
			if (lbn != FSConstants.NULL_LBN)
				return false;
		}
		return true;
	}

	// --------------------------------------------------------------
	// ---
	// --- Block private API
	// ---
	// --------------------------------------------------------------
	@Override
	int getSizeOfUsed() {
		return (fs.hashTableSize << 3) + FSConstants.BS_DN_SIZEOF;
	}

	@Override
	void toBuffer(final byte[] buffer, final int offset) throws FSException {
		super.toBuffer(buffer, offset);
		RawDataAccess.setLongs(buffer, offset + FSConstants.BS_DN_MAP, map);
	}

	@Override
	void fromBuffer(final byte[] buffer, final int offset) throws FSException {
		super.fromBuffer(buffer, offset);
		RawDataAccess.getLongs(buffer, offset + FSConstants.BS_DN_MAP, map);
	}

	@Override
	public void initBlock() throws FSException {
		super.initBlock();
		Arrays.fill(map, FSConstants.NULL_LBN);
	}

	@Override
	public void destroy() {
		super.destroy();
		CryptUtil.destroy(map);
	}

}

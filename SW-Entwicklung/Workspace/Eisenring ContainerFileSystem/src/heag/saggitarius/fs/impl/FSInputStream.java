package heag.saggitarius.fs.impl;

import heag.saggitarius.fs.ErrorCode;
import heag.saggitarius.fs.FSException;

import java.io.IOException;
import java.io.InputStream;

import ch.eisenring.core.io.StreamProperties;
import ch.eisenring.core.resource.ResourceFinalizer;

public final class FSInputStream extends InputStream implements StreamProperties {

	final FSCore fs;
	FSLockPrivate lock;
	long filePointer;
	long markPointer;

	// ResourceFinalizer for Java 1.8 and Java 21 compatibility
	private final ResourceFinalizer resourceFinalizer;

	FSInputStream(final FSLockPrivate lock) throws FSException {
		this.lock = lock;
		this.fs = lock.getFileSystem();
		this.resourceFinalizer = new ResourceFinalizer(this) {
			@Override
			protected void cleanup() {
				try {
					close();
				} catch (Exception e) {
					// Ignore cleanup failures during finalization
				}
			}
		};
	}

	private BlockFile getFileBlock() throws IOException {
		try {
			return fs.getBlock(lock.lbn, BlockType.FILENODE);
		} catch (final FSException e) {
			throw new IOException(e.getMessage(), e);
		}
	}

	private void ensureOpen() throws IOException {
		if (lock == null)
			throw new IOException("Stream closed");
		lock.ensureValid();
	}

	private long getFileSize() throws IOException {
		return getFileBlock().getFilesize();
	}

	@Override
	public int read() throws IOException {
		synchronized (fs.getLock()) {
			ensureOpen();
			// end of stream reached?
			final long fileSize = getFileSize();
			if (filePointer >= fileSize)
				return -1;
			final BlockData dataBlock = FSStreamHelper.getOrAllocate(getFileBlock(), filePointer, false);
			if (dataBlock == null)
				throw new FSException(ErrorCode.ERR_FILE_STRUCTURE_INVALID);
			final int blockOffset = (int) (filePointer % fs.getBytesPerBlock());
			++filePointer;
			return dataBlock.data[blockOffset] & 0xFF;
		}
	}

	@Override
	public int read(final byte[] buffer, int offset, int length) throws IOException {
		synchronized (fs.getLock()) {
			ensureOpen();
			// end of stream reached?
			final long fileSize = getFileSize();
			if (filePointer >= fileSize)
				return -1;
			final int bytesPerBlock = fs.getBytesPerBlock();
			final long oldFilePointer = filePointer;
			int blockOffset = (int) (filePointer % bytesPerBlock);
			while (length > 0) {
				final int fileRemLimit = (int) Math.min(fileSize - filePointer, Integer.MAX_VALUE);
				final int blockPortion =  Math.min(Math.min(bytesPerBlock - blockOffset, length), fileRemLimit);
				if (blockPortion <= 0)
					break;
				final BlockData dataBlock = FSStreamHelper.getOrAllocate(getFileBlock(), filePointer, false);
				if (dataBlock == null)
					throw new FSException(ErrorCode.ERR_FILE_STRUCTURE_INVALID);
				System.arraycopy(dataBlock.data, blockOffset, buffer, offset, blockPortion);
				filePointer += blockPortion;
				offset += blockPortion;
				length -= blockPortion;
				blockOffset = 0;
			}
			return (int) (filePointer - oldFilePointer);
		}
	}

	/**
	 * Skips the requested number of bytes
	 */
	@Override
	public long skip(final long offset) throws IOException {
		synchronized (fs.getLock()) {
			ensureOpen();
			if (offset == 0)
				return 0;
			long newFilePointer = filePointer + offset;
			// limit the new file pointer to valid range and check for long overflow
			if (offset < 0) {
				// seek backward
				if (newFilePointer > filePointer || newFilePointer < 0) {
					newFilePointer = 0;
				}
			} else if (offset > 0) {
				// seek forward
				if (newFilePointer < filePointer || newFilePointer > getFileSize()) {
					newFilePointer = getFileSize();
				}
			}	
			final long result = newFilePointer - filePointer; 
			filePointer = newFilePointer;
			return result;
		}
	}

	@Override
	public int available() throws IOException {
		synchronized (fs.getLock()) {
			ensureOpen();
			final long remaining = getFileSize() - filePointer;
			return (int) Math.min(remaining, Integer.MAX_VALUE);
		}
	}

	@Override
	public void close() throws IOException {
		synchronized (fs.getLock()) {
			final FSLockPrivate lock = this.lock;
			if (lock == null)
				return;
			try {
				fs.unlock(lock);
			} finally {
				this.lock = null;
			}
		}
	}

	@Override
	public boolean markSupported() {
		return true;
	}

	@Override
	public synchronized void mark(final int readlimit) {
		synchronized (fs.getLock()) {
			markPointer = filePointer;
		}
	}

	@Override
	public synchronized void reset() throws IOException {
		synchronized (fs.getLock()) {
			ensureOpen();
			filePointer = markPointer;
		}
	}

	// finalize() method removed - now using Cleaner for Java 21 compatibility

	// --------------------------------------------------------------
	// ---
	// --- StreamProperties implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public boolean isBuffered() {
		// although this stream is somewhat buffered, single byte reads are
		// pretty slow. So claim to be not buffered.
		return false;
	}

}

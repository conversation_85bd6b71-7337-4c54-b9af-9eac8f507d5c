package heag.saggitarius.fs.impl;

import heag.saggitarius.fs.FSException;

import java.io.IOException;
import java.io.OutputStream;

import ch.eisenring.core.io.StreamProperties;
import ch.eisenring.core.resource.ResourceFinalizer;

public final class FSOutputStream extends OutputStream implements StreamProperties {

	final FSLockPrivate lock;
	final FSCore fs;
	final long fileBlockLBN;

	// ResourceFinalizer for Java 1.8 and Java 21 compatibility
	private final ResourceFinalizer resourceFinalizer;

	FSOutputStream(final FSLockPrivate lock) throws FSException {
		this.lock = lock;
		this.fs = lock.getFileSystem();
		this.fileBlockLBN = lock.lbn;
		this.resourceFinalizer = new ResourceFinalizer(this) {
			@Override
			protected void cleanup() {
				try {
					close();
				} catch (Exception e) {
					// Ignore cleanup failures during finalization
				}
			}
		};
	}

	private BlockFile getFileBlock() throws FSException {
		return fs.getBlock(fileBlockLBN, BlockType.FILENODE);
	}

	@Override
	public void write(final byte[] buffer, int offset, int length) throws IOException {
		synchronized (fs.getLock()) {
			final int bytesPerBlock = fs.getBytesPerBlock();
			lock.ensureValid();
			final BlockFile fileBlock = getFileBlock();
			long fileSize = fileBlock.getFilesize();
			while (length > 0) {
				try {
					final BlockData dataBlock = FSStreamHelper.getOrAllocate(fileBlock, fileSize, true);
					final int blockOffset = (int) (fileSize % bytesPerBlock);
					final int chunkLen = Math.min(bytesPerBlock - blockOffset, length);
					System.arraycopy(buffer, offset, dataBlock.data, blockOffset, chunkLen);
					dataBlock.setDirty(true);
					fileSize += chunkLen;
					length -= chunkLen;
					offset += chunkLen;
					fileBlock.setFilesize(fileSize);
					fileBlock.setLastModified(System.currentTimeMillis());
				} catch (final FSException e) {
					fs.flush();
					throw e;
				}
			}
		}
	}
	
	@Override
	public void write(final int b) throws IOException {
		synchronized (fs.getLock()) {
			lock.ensureValid();
			final BlockFile fileBlock = getFileBlock();
			long fileSize = fileBlock.getFilesize();
			try {
				final BlockData dataBlock = FSStreamHelper.getOrAllocate(fileBlock, fileSize, true);
				final int blockOffset = (int) (fileSize % fs.getBytesPerBlock());
				dataBlock.data[blockOffset] = (byte) b;
				dataBlock.setDirty(true);
				++fileSize;
			} catch (final FSException e) {
				fs.flush();
				throw e;
			}
			fileBlock.setLastModified(System.currentTimeMillis());
			fileBlock.setFilesize(fileSize);
		}
	}

	@Override
	public void flush() throws IOException {
		synchronized (fs.getLock()) {
			if (lock.isValid()) {
				fs.flush();
			}
		}
	}
	
	@Override
	public void close() throws IOException {
		synchronized (fs.getLock()) {
			if (lock.isValid()) {
				fs.flushFully();
				fs.unlock(lock);
			}
		}
	}
	
	// finalize() method removed - now using Cleaner for Java 21 compatibility

	// --------------------------------------------------------------
	// ---
	// --- StreamProperties implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public boolean isBuffered() {
		// although this stream is somewhat buffered, single byte writes are
		// pretty slow. So claim to be not buffered.
		return false;
	}

}

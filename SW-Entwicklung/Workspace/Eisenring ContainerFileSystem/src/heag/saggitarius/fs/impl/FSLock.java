package heag.saggitarius.fs.impl;

import heag.saggitarius.fs.ErrorCode;
import heag.saggitarius.fs.FSException;
import heag.saggitarius.fs.ObjectInfo;

import java.lang.ref.Cleaner;
import java.util.ArrayList;
import java.util.List;

/**
 * Represents a lock on a file system object (file or directory).
 * 
 * Locks serve as handles to file system objects, ensuring the object
 * is protected from actions other than through the lock itself.
 * 
 * Note that any lock obtained must be released once no longer needed.
 * Failing to release locks can interfere with operations, especially
 * if the lock is an exclusive one.
 */
public class FSLock implements FSConstants {

	private static final Cleaner cleaner = Cleaner.create();

	final FSLockPrivate fsPrivate;

	// Cleanable registered with the cleaner
	private final Cleaner.Cleanable cleanable;

	FSLock(final FSLockPrivate fsPrivate) {
		this.fsPrivate = fsPrivate;
		this.cleanable = cleaner.register(this, new State(this));
	}

	void ensureValid() throws FSException {
		if (!isValid())
			throw new FSException(ErrorCode.ERR_OBJECT_INVALID);
	}

	/**
	 * Gets the file system in which the lock exists 
	 */
	public FSCore getFileSystem() {
		return fsPrivate.getFileSystem();
	}

	/**
	 * Returns true if this lock is valid
	 */
	public boolean isValid() {
		return fsPrivate.isValid();
	}

	/**
	 * Attempts to alter this locks mode. May fail with exception,
	 * leaving the lock unaltered.
	 * 
	 * Returns the previous mode if successful.
	 */
	public int changeMode(final int mode) throws FSException {
		return getFileSystem().locks.changeMode(fsPrivate, mode);
	}

	/**
	 * Gets the mode of this lock SHARED / EXCLUSIVE
	 */
	public int getMode() {
		return fsPrivate.getMode();
	}

	/**
	 * Gets the type of the object this lock represents (TYPE_FILE or TYPE_DIRECTORY)
	 */
	public int getObjectType() throws FSException {
		return fsPrivate.getObjectType();
	}
	
	public BlockNode getBlock() throws FSException {
		return fsPrivate.getBlock();
	}

	@SuppressWarnings("unchecked")
	public <B extends BlockNode> B getBlock(final BlockType<B> blockType) throws FSException {
		return fsPrivate.getBlock(blockType);
	}

	public ObjectInfo examine() throws FSException {
		return getBlock().getObjectInfo();
	}

	public ObjectInfo[] examineAll() throws FSException {
		final FSCore fs = fsPrivate.fs;
		synchronized (fs.getLock()) {
			ensureValid();
			final BlockDir directory;
			try {
				directory = getBlock(BlockType.DIRNODE);
			} catch (final ClassCastException e) {
				throw new FSException(ErrorCode.ERR_NOT_A_DIRECTORY);
			}
			final List<ObjectInfo> result = new ArrayList<>();
			for (int i=directory.getHashTablesize()-1; i>=0; --i) {
				long lbn = directory.getHashEntry(i);
				while (lbn != FSConstants.NULL_LBN) {
					final BlockNode node = fs.getBlock(lbn, BlockType.NODE);
					result.add(node.getObjectInfo());
					lbn = node.getNextLBN();
				}
			}
			return result.toArray(ObjectInfo.EMPTY_ARRAY);
		}
	}

	// finalize() method removed - now using Cleaner for Java 21 compatibility

	/**
	 * Cleanup logic that ensures leaked FSLocks get unlocked.
	 */
	private static final class State implements Runnable {
		private FSLockPrivate fsPrivate;

		State(FSLock lock) {
			this.fsPrivate = lock.fsPrivate;
		}

		@Override
		public void run() {
			if (fsPrivate != null) {
				try {
					switch (fsPrivate.getMode()) {
						default:
							break;
						case MODE_SHARED:
						case MODE_EXCLUSIVE:
							fsPrivate.fs.unlock(fsPrivate);
							break;
					}
				} catch (Exception e) {
					// Ignore cleanup failures during finalization
				}
				fsPrivate = null;
			}
		}
	}
}

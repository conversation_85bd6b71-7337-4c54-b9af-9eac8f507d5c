import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;


public final class BinaryFile {

	private final static int chunkSize = 32768;

	public final File file;
	public final byte[] binary;
	public final boolean success;
	public final String message;
		
	protected BinaryFile(final File file,
			             final byte[] binary,
			             final boolean success,
			             final String message) {
		this.file = file;
		this.binary = binary;
		this.success = success;
		this.message = message;
	}
		
	private byte[] buffer;
	private InputStream input;
	private ByteArrayOutputStream output;

	private BinaryFile(final File file) {
		this.file = file;
		this.binary = null;
		this.success = false;
		this.message = "internal error";
	}

	public static BinaryFile read(final String fileName) {
		return read(new File(fileName));
	}
	
	public static BinaryFile read(final File file) {
		final BinaryFile reader = new BinaryFile(file);
		return reader.read();
	}

	private void dispose() {
		buffer = null;
		if (output != null) {
			try {
				output.close();
			} catch (final IOException e) {
			}
		}
		output = null;
		if (input != null) {
			try {
				input.close();
			} catch (final IOException e) {
			}
		}
		input = null;
	}

	private BinaryFile error(final String message) {
		dispose();
		final String head = "File \"" + file.getAbsolutePath() + "\": ";
		return new BinaryFile(file, null, false, head+message);
	}

	private BinaryFile read() {
		if (!file.exists()) {
			return error("does not exist");
		} else if (!file.isFile()) {
			return error("not a file");
		}
		final long length = file.length();
		if (length < 0 || length > Integer.MAX_VALUE) {
			return error("too large ("+length+" byte)");
		}
		try {
			input = new FileInputStream(file);
		} catch (final FileNotFoundException e) {
			return error("not found");
		}
		try {
			output = new ByteArrayOutputStream((int) length);
		} catch (final OutOfMemoryError e) {
			return error("not enough memory ("+length+" byte)");
		}
		try {
			buffer = new byte[chunkSize];
		} catch (final OutOfMemoryError e) {
			return error("not enough memory ("+chunkSize+" byte)");
		}
		try {
			while (true) {
				final int read = input.read(buffer);
				if (read <= 0)
					break;
				output.write(buffer, 0, read);
			}
		} catch (final IOException e) {
			return error("IO-Error ("+e.getMessage()+")");
		}
		final byte[] bytes;
		try {
			bytes = output.toByteArray();
		} catch (final OutOfMemoryError e) {
			return error("not enough memory ("+length+" byte)");
		}
		dispose();
		return new BinaryFile(file, bytes, true, "Ok");
	}

}

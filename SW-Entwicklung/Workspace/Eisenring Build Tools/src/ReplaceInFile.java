import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;


public class ReplaceInFile {

	public static void main(String argv[]) {
//		argv = new String[] {
//			"C:\\SW-Entwicklung\\Workspace\\Eisenring Dispo Shared\\src\\ch\\eisenring\\dispo\\shared\\ISharedConstants.java",
//			"public final static String VERSION_STRING = \"", 
//			"\";",
//			"C:\\Eisenring Dispo\\Source\\Dispomodul.iss",
//			"AppVerName=Eisenring Dispo ",
//			".0\r\n",
//			"C:\\TEMP\\Test.iss"
//		};
		
		if (argv == null || argv.length != 7) {
			System.out.println("Usage: <VersionSourceFile> <Prefix> <Postfix> <ReplaceSourceFile> <Prefix> <Postfix> <DestinationFile>");
			System.exit(0);
		}

		// --- read version source and extract new version
		final BinaryFile newSource = BinaryFile.read(argv[0]);
		if (!newSource.success) {
			System.out.println(newSource.message);
			System.exit(10);
		}
		final byte[] newPre = paramHack(argv[1]).getBytes();
		final byte[] newPst = paramHack(argv[2]).getBytes();
		final byte[] newVer = extract(newSource.binary, newPre, newPst);
		if (newVer == null) {
			System.out.println("Could not find new version in "+newSource.file);
			System.exit(10);
		}
		
		// --- read replacement source and extract old version
		final BinaryFile oldSource = BinaryFile.read(argv[3]);
		if (!oldSource.success) {
			System.out.println(oldSource.message);
			System.exit(10);
		}
		final byte[] oldPre = paramHack(argv[4]).getBytes();
		final byte[] oldPst = paramHack(argv[5]).getBytes();
		final byte[] oldVer = extract(oldSource.binary, oldPre, oldPst);
		if (oldVer == null) {
			System.out.println("Could not find old version in "+newSource.file);
			System.exit(10);
		}
		
		// --- replace oldVer by newVer
		final byte[] oldVer2 = concat(oldPre, oldVer, oldPst);
		final byte[] newVer2 = concat(oldPre, newVer, oldPst);
		final byte[] replaced = replace(oldSource.binary, oldVer2, newVer2);
		
		// --- write to destination file
		final File dstFile = new File(argv[6]);
		FileOutputStream output = null;
		try {
			output = new FileOutputStream(dstFile);
			output.write(replaced);
		} catch (final IOException e) {
			System.out.println("File \""+dstFile+"\": "+e.getMessage());
			System.exit(10);
		} finally {
			if (output != null) {
				try {
					output.close();
				} catch (final IOException e) {
				}
			}
		}

		System.exit(0);
	}

	public final static byte[] replace(final byte[] haystack, final byte[] needle, final byte[] replacement) {
		final ByteArrayOutputStream output = new ByteArrayOutputStream(haystack.length);
		final int l = haystack.length;
		int i = 0;
		try {
			while (i < l) {
				if (equalsSlice(haystack, i, needle)) {
					output.write(replacement);
					i += needle.length;
				} else {
					output.write(haystack[i++]);
				}
			}
		} catch (final IOException e) {
			// can't happen ...
		}
		return output.toByteArray();
	}

	public final static String paramHack(final String param) {
		String result = param;
		result = result.replace("\\n", "\n");
		result = result.replace("\\r", "\r");
		result = result.replace("\\t", "\t");
		result = result.replace("\\q", "\"");
		return result;
	}

	public final static boolean equalsSlice(final byte[] array, final int index, final byte[] slice) {
		try {
			for (int j=0; j<slice.length; ++j) {
				final byte b_slice = slice[j];
				final byte b_array = array[index + j];
				if (b_slice != b_array) {
					return false;
				}
			}
			return true;
		} catch (final ArrayIndexOutOfBoundsException e) {
			return false;
		}
	}

	public final static byte[] extract(final byte[] haystack, final byte[] prefix, final byte[] postfix) {
		int l = haystack.length;
		int start = -1;
		int end = -1;
		int i = 0;
		// find prefix
		while (i < l) {
			if (equalsSlice(haystack, i, prefix)) {
				start = i + prefix.length;
				break;
			}
			++i;
		}
		if (start < 0)
			return null;
		while (i < l) {
			if (equalsSlice(haystack, i, postfix)) {
				end = i;
				break;
			}
			++i;
		}
		if (end < 0)
			return null;
		l = end - start;
		final byte[] extract = new byte[l];
		System.arraycopy(haystack, start, extract, 0, l);
		return extract;
	}

	/**
	 * Appends the given arrays into one big array
	 */
	public static byte[] concat(final byte[] ... arrays) {
		if (arrays == null)
			return null;
		int length = 0;
		for (int i=arrays.length-1; i>=0; --i) {
			final byte[] array = arrays[i];
			length += array == null ? 0 : array.length;
		}
		final byte[] result = new byte[length];
		int offset = 0;
		for (int i=0; i<arrays.length; ++i) {
			final byte[] array = arrays[i];
			if (array == null)
				continue;
			length = array.length;
			System.arraycopy(array, 0, result, offset, length);
			offset += length;
		}
		return result;
	}
	
}

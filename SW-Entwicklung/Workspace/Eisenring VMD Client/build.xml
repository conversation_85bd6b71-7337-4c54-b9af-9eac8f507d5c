<project>

    <include file="${ant.home}/tasks/customtasks.xml"/>

    <property name="base.dir" value="../../../Eisenring Dispo"/>
    <property name="workspace.dir" value="../../../SW-Entwicklung/Workspace"/>
    <property name="lib.dir" value="${base.dir}/VMDClient/lib"/>
    <property name="build.dir" value="ant_build"/>
    <property name="classes.dir" value="${build.dir}/classes"/>
    <property name="jar.file" value="vmdclient.jar"/>
    <property name="update.dir" value="${base.dir}/Server/update/vmdclient"/>

    <path id="classpath">
        <fileset dir="${lib.dir}" includes="**/*.jar"/>
    </path>
    
    <target name="clean">
        <delete dir="${build.dir}"/>
        <delete file="${lib.dir}/${jar.file}"/>
    </target>

    <target name="compile" depends="clean">
        <mkdir dir="${classes.dir}"/>
        <javac srcdir="
        	${workspace.dir}/Eisenring Core/src;
        	${workspace.dir}/Eisenring Commons/src;
		${workspace.dir}/Eisenring Network/src;
        	${workspace.dir}/Eisenring GUI/src;
		${workspace.dir}/Eisenring JDBC/src;
		${workspace.dir}/Eisenring Model Shared/src;
		${workspace.dir}/Eisenring Model GUI;
		${workspace.dir}/Eisenring App Shared/src;
		${workspace.dir}/Eisenring App Client/src;
		${workspace.dir}/Eisenring User Shared/src;
		${workspace.dir}/Eisenring User Client/src;
		${workspace.dir}/Eisenring Logiware Shared/src;
		${workspace.dir}/Eisenring EMail Shared/src;
		${workspace.dir}/Eisenring EMail Client/src;
        	${workspace.dir}/Eisenring PRT Shared/src;
        	${workspace.dir}/Eisenring PRT Client/src;
        	${workspace.dir}/Eisenring VMD Shared/src;
        	${workspace.dir}/Eisenring VMD Client/src;
    		src"
        	destdir="${classes.dir}"
        	classpathref="classpath"
	        includeAntRuntime="no"
        	includeJavaRuntime="yes"
        	debug="true"
               encoding="UTF-8">
            <compilerarg value="-Xlint:deprecation"/>
        </javac>
      <touch datetime="10/10/2010 10:10 am">
        <fileset dir="${classes.dir}"/>
      </touch>
    </target>

    <target name="jar" depends="compile">
        <zip destfile="${lib.dir}/${jar.file}" level="9">
            <fileset includes="**/*" dir="${classes.dir}"/>
            <fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring Core/src"/>
            <fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring Commons/src"/>
            <fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring Network/src"/>
            <fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring GUI/src"/>
            <fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring JDBC/src"/>
            <fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring Model Shared/src"/>
            <fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring Model GUI/src"/>
            <fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring App Shared/src"/>
            <fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring App Client/src"/>
            <fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring EMail Shared/src"/>
            <fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring EMail Client/src"/>
            <fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring User Shared/src"/>
            <fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring User Client/src"/>
            <fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring Logiware Shared/src"/>
            <fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring PRT Shared/src"/>
            <fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring PRT Client/src"/>
            <fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring VMD Shared/src"/>
            <fileset excludes="**/*.java" dir="${workspace.dir}/Eisenring VMD Client/src"/>
            <fileset excludes="**/*.java" dir="src"/>
        </zip>
    </target>

    <target name="launch4j" depends="jar">
	<launch4j configFile="${base.dir}/Source/Launch4j/VMDClient_New.xml" outfile="${base.dir}/VMDClient/bin/Eisenring-VMD.exe"/>
    </target>

    <target name="build" depends="jar">
        <delete dir="${build.dir}"/>
    </target>

    <target name="release" depends="build">    
	<delete dir="${update.dir}"/>
	<mkdir dir="${update.dir}"/>
	<copy todir="${update.dir}">
	    <fileset dir="${base.dir}/vmdclient"/>
	</copy>
    </target>
       
</project>

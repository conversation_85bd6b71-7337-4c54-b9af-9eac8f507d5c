package ch.eisenring.vmd.client.gui.rule.editors;

import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.gui.components.HEAGCodeComboBox;
import ch.eisenring.gui.interfaces.ValueEditor;

@SuppressWarnings("serial")
public final class VMDParameterEditorCode implements VMDParameterEditor {

	private HEAGCodeComboBox<AbstractCode> comboBox;

	public VMDParameterEditorCode(final Class<AbstractCode> codeClass) {
		comboBox = new HEAGCodeComboBox<AbstractCode>(codeClass);
	}

	@Override
	public ValueEditor getEditorComponent() {
		return comboBox;
	}

}

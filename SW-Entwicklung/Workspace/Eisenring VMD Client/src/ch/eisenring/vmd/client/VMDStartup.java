package ch.eisenring.vmd.client;

import java.net.MalformedURLException;

import ch.eisenring.app.client.AppStartup;
import ch.eisenring.app.client.fileassoc.ProtocolURL;
import ch.eisenring.core.collections.Collection;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.platform.Platform;
import ch.eisenring.core.util.JavaVersionCode;
import ch.eisenring.vmd.shared.VMDConstants;

public class VMDStartup extends AppStartup {

	public static void main(final String[] argv) {
		Platform.setTempSubPath("HEAG_VMD");
		Thread.currentThread().setName(VMDStartup.class.getSimpleName());
		defaultStartupActions(argv);

//		final String[] argv2 = DMSCommandLine.processArgs(argv);
		VMDStartup.start(argv);
	}
	
	protected static void start(final String[] argv) {
		final VMDCore core = new VMDCore();
		
		// child classes may do single instance check here
		core.checkSingleInstance(argv);

		// handle the command line arguments
		final ArrayList<String> args = new ArrayList<String>();
		if (argv != null) {
			for (final String arg : argv) {
				try {
					new ProtocolURL(VMDConstants.URL_PROTOCOL, arg);
					// if it is a valid URL, it will be processed later...
					continue;
				} catch (final MalformedURLException e) {
					// not an URL argument
				}
				args.add(arg);
			}
		}

		try {
			core.launch(Collection.toArray(args, String.class));
		} catch (final Exception e) {
			throw new RuntimeException(e);
		}

		// attempt to connect to server
		final VMDClient client = core.getComponent(VMDClient.class, true);

		// prepare connection
		client.getConnection(true);
		
		// open GUI
		if (client.CHECK_WINDPW_OPENONSTARTUP.get(Boolean.TRUE)) {
			core.showMainWindow();
		}

		// process (application) arguments
//		new DoLater(new Runnable() {
//			@Override
//			public void run() {
//				ThreadCore.sleep(133);
//				// update tray icon
//				final PacketProblemCountRequest request = PacketProblemCountRequest.create(client.getLoginname());
//				client.sendPacket(request, false);
//				// handle command line/URL commands
//				final MainWindowManager manager = client.WINDOW_MANAGER.get();
//				manager.applyDefaultView();
//				final ProtocolURLHandler urlHandler = client.URL_HANDLER.get();
//				for (final String arg : argv) {
//					try {
//						final ProtocolURL url = urlHandler.createURL(arg);
//						urlHandler.processURL(url);
//					} catch (final Throwable t) {
//						// ignore url problems
//					}
//				}
//				// check if there are documents locked by this user
//				final DMSObjectLockSet lockSet = client.OBJECTLOCKSET.get();
//				final int count = lockSet.getAll(client.getLoginname()).size();
//				if (count > 0) {
//					final DMSMainWindow window = manager.getPrimaryWindow();
//					if (window != null) {
//						new ActionDocumentEditList(window).fire();
//					}
//				}
//			}
//		}, false,
//		new ClientConnectedCondition(client),
//		new UserContextAvailableCondition(client)).start();

//		WindowsAssociations.registerFileAssociations(true);
//		FileWalker.cleanSystemTemp(3);
		
		// check java version
		client.message(JavaVersionCode.getJavaVersionWarning());
	}

}

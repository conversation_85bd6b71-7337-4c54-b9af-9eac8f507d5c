package ch.eisenring.vmd.client.gui.rule;

import java.awt.GridBagLayout;

import ch.eisenring.gui.GridBagConstraints;
import ch.eisenring.gui.components.DecorationHeader;
import ch.eisenring.gui.components.HEAGPanel;
import ch.eisenring.gui.components.HEAGTabbedPane;
import ch.eisenring.vmd.client.VMDClient;
import ch.eisenring.vmd.client.resources.images.Images;
import ch.eisenring.vmd.shared.rule.api.VMDRule;

@SuppressWarnings("serial")
public class VMDRuleConfigPanel extends HEAGPanel {

	public final VMDClient client;
	public final VMDRule rule;
	public final VMDRuleDataPanel dataPanel;
	public final VMDRuleParameterPanel paramPanel;
	public final HEAGTabbedPane tabbedPane = new HEAGTabbedPane();
	
	public final DecorationHeader header = new DecorationHeader(
			DecorationHeader.LABEL, "Regelkonfiguration",
			DecorationHeader.ICON, Images.RULE,
			DecorationHeader.SIZE, DecorationHeader.MEDIUM,
			DecorationHeader.SEPARATOR, Boolean.TRUE);

	public VMDRuleConfigPanel(final VMDClient client, final VMDRule rule) {
		this.client = client;
		this.rule = rule;
		this.dataPanel = new VMDRuleDataPanel(client, rule);
		this.paramPanel = new VMDRuleParameterPanel(client, rule);
		initComponents();
		initLayout();
	}

	private void initComponents() {
		setLayout(new GridBagLayout());
	}

	private void initLayout() {
		tabbedPane.add(dataPanel, "Konfiguration");
		tabbedPane.add(paramPanel, "Parameter");
		
		add(header, GridBagConstraints.field(0, 0));
		add(tabbedPane, GridBagConstraints.area(0, 1));
	}

}

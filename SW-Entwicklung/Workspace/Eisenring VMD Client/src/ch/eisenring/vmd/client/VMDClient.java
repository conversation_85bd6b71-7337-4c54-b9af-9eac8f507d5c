package ch.eisenring.vmd.client;

import ch.eisenring.core.platform.PlatformPath;
import java.io.File;
import java.net.SocketAddress;

import ch.eisenring.app.client.AbstractAppComponent;
import ch.eisenring.app.client.AppCore;
import ch.eisenring.app.client.action.ShowVersionHistoryAction;
import ch.eisenring.app.client.util.ClientConnectedCondition;
import ch.eisenring.app.client.util.DoLater;
import ch.eisenring.app.shared.Configurable;
import ch.eisenring.app.shared.ServiceNotFoundException;
import ch.eisenring.core.application.Configuration;
import ch.eisenring.core.application.ConfigurationException;
import ch.eisenring.core.collections.Collection;
import ch.eisenring.core.collections.Set;
import ch.eisenring.core.collections.impl.ArraySet;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.feature.Feature;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.core.logging.Logger.LogLevel;
import ch.eisenring.core.observable.Observable;
import ch.eisenring.core.observable.Observer;
import ch.eisenring.core.platform.Platform;
import ch.eisenring.core.util.singleinstance.SingleInstanceListener;
import ch.eisenring.user.client.service.UserContextAvailableCondition;
import ch.eisenring.user.shared.service.USRService;
import ch.eisenring.user.shared.service.UserFacade;
import ch.eisenring.vmd.client.desktop.DesktopIntegration;
import ch.eisenring.vmd.client.desktop.VMDStatusIconDaemon;
import ch.eisenring.vmd.client.network.VMDPacketDispatcher;
import ch.eisenring.vmd.client.resources.images.VMDCodeIconAssociation;
import ch.eisenring.vmd.shared.network.PacketServerStatusReply;
import ch.eisenring.vmd.shared.rule.api.VMDRuleCache;
import ch.eisenring.vmd.shared.rule.api.VMDRuleDataCache;
import ch.eisenring.vmd.shared.rule.api.VMDRuleMetaCache;
import ch.eisenring.vmd.shared.util.UsernameUtil;

public class VMDClient extends AbstractAppComponent implements Configurable {

	// --------------------------------------------------------------
	// ---
	// --- Client private states
	// ---
	// --------------------------------------------------------------
	public final VMDStatusIconDaemon statusIconDaemon = new VMDStatusIconDaemon(this);
	
	public final DesktopIntegration desktopIntegration = new DesktopIntegration(this);
	String loginName = UsernameUtil.canonicalize(Platform.getPlatform().getLoginName());
	Long loginUserRowId = null;
	UserFacade loginUser = null;

	public final VMDRuleDataCache dataCache = new VMDClientRuleDataCache(this);
	public final VMDRuleMetaCache metaCache = new VMDClientRuleMetaCache(this);
	public final VMDRuleCache ruleCache = new VMDRuleCache(dataCache, metaCache);

	// --------------------------------------------------------------
	// ---
	// --- Client public / observable states
	// ---
	// --------------------------------------------------------------
	public final Observable<SocketAddress> SERVER_SOCKET_ADDRESS =
		Observable.create(true, "ServerSocketAddress", null);
	public final Observable<Boolean> CHECK_WINDOW_AUTOCLOSE = 
		Observable.create(true, "CheckWindowAutoClose", Boolean.TRUE);
	public final Observable<Boolean> CHECK_WINDPW_OPENONSTARTUP = 
		Observable.create(true, "CheckWindowOpenOnStartup", Boolean.TRUE);
	public final Observable<PacketServerStatusReply> SERVER_LAST_STATUS =
		Observable.create(true, "LastServerStatus", (PacketServerStatusReply) null);
	
	@SuppressWarnings("unchecked")
	public final Observable<String>[] HOLIDAY_REPLACEMENTS = new Observable[] {
		Observable.create(true, "HolidayReplacement1", (String) null),
		Observable.create(true, "HolidayReplacement2", (String) null),
		Observable.create(true, "HolidayReplacement3", (String) null),
		Observable.create(true, "HolidayReplacement4", (String) null),
		Observable.create(true, "HolidayReplacement5", (String) null),
	};

	// --------------------------------------------------------------
	// ---
	// --- Private states
	// ---
	// --------------------------------------------------------------
	private final Observer<Object> userServiceObserver = new Observer<Object>() {
		@Override
		public void observableChanged(final Observable<Object> observable) {
			try {
				final USRService userService = locateService(USRService.class);
				final UserFacade user = userService.getLoggedInUser();
				if (user != null && !user.isLoginAllowed()) {
					Logger.error("Benutzer ist gesperrt");
					getCore().shutdown(true);
				}
				loginUser = user;
			} catch (final ServiceNotFoundException e) {
				loginUser = null;
			}
			loginUserRowId = loginUser == null ? null : loginUser.getRowId();
		}
	};

	// --------------------------------------------------------------
	// ---
	// --- Feature management
	// ---
	// --------------------------------------------------------------
	private final Feature[] features = {
			new VMDPacketDispatcher(this),
			statusIconDaemon
	};

	@Override
	public Feature[] getFeatures() {
		return features;
	}

	// --------------------------------------------------------------
	// ---
	// --- Launch / Shutdown
	// ---
	// --------------------------------------------------------------
	@Override
	public void launch() throws Exception {
		super.launch();

		// make sure codes are properly configured with icons
		VMDCodeIconAssociation.init();
		
		final AppCore core = getCore();

		// bind user settings
		CHECK_WINDOW_AUTOCLOSE.bind(this);
		CHECK_WINDPW_OPENONSTARTUP.bind(this);
		for (final Observable<?> observable : HOLIDAY_REPLACEMENTS) {
			observable.bind(this);
		}

		desktopIntegration.attachTrayIcon();
		// connect the user system with the DMS permission system
		final USRService userService = locateService(USRService.class);
		userService.getPermissionObservable().addObserver(userServiceObserver);

		core.getSingleInstance().addListener(singleInstanceListener);
		
		askShowVersionHistory();
	}
	
	@Override
	public void shutdown() throws Exception {
		desktopIntegration.removeTrayIcon();

		// detach from user service
		try {
			final USRService userService = locateService(USRService.class);
			userService.getPermissionObservable().removeObserver(userServiceObserver);
		} catch (final ServiceNotFoundException e) {
			Logger.warn(e.getMessage());
		}

		super.shutdown();
	}

	// --------------------------------------------------------------
	// ---
	// --- Base class overrides
	// ---
	// --------------------------------------------------------------
	private final SingleInstanceListener singleInstanceListener = new SingleInstanceListener() {
		@Override
		public void otherInstanceStarted(final String[] argv, final File currentDir) {
			new DoLater(new Runnable() {
				@Override
				public void run() {
					if (argv == null || argv.length <= 0) {
						// open window
					} else {
						// handle URL
//						final ProtocolURLHandler urlHandler = URL_HANDLER.get();
//						urlHandler.processCmdLineArgs(argv);
					}
				}
			}, true, 
			new ClientConnectedCondition(VMDClient.this),
			new UserContextAvailableCondition(VMDClient.this)).start();
		}
	};

	// --------------------------------------------------------------
	// ---
	// --- Configuration
	// ---
	// --------------------------------------------------------------
	@Override
	public void getConfigurationFileNames(final Collection<String> fileNames) {
		final Platform platform = Platform.getPlatform();
		fileNames.add("VMD.cfg");
		fileNames.add(new File(platform.getPath(PlatformPath.SETTINGS), "VMDUser.cfg").getAbsolutePath());
	}

	@Override
	public void configure(final Configuration configuration) throws ConfigurationException {
		// server address & port
		SERVER_SOCKET_ADDRESS.set(configuration.getSocketAddress("VMDClient:Server", "VMDClient:Port"));
	}

	// --------------------------------------------------------------
	// ---
	// --- Local API
	// ---
	// --------------------------------------------------------------
	/**
	 * Gets all the users the current client is responsible for,
	 * that is the logged in user, as well as any users he plays
	 * temporary replacement for. 
	 */
	public Set<String> getResponsibleForNames() {
		final Set<String> result = new ArraySet<>(4);
		result.add(getLoginname());
		for (final Observable<String> observable : HOLIDAY_REPLACEMENTS) {
			final String username = observable.get();
			if (!Strings.isEmpty(username)) {
				result.add(username);
			}
		}
		return result;
	}
	
	/**
	 * Gets the name of the logged in user
	 */
	public String getLoginname() {
		final UserFacade loginUser = getLoginUser();
		final String result = loginUser == null ? loginName : loginUser.getLoginname();
		return UsernameUtil.canonicalize(result);
	}

	/**
	 * Gets the logged in user, NULL if no user logged in
	 */
	public UserFacade getLoginUser() {
		return loginUser;
	}
	/**
	 * Gets the logged in user's row id, NULL if no user logged in
	 */
	public Long getLoginUserRowId() {
		return loginUserRowId;
	}

	/**
	 * Generate user level error from exception
	 */
	public void showError(final Throwable t) {
		Logger.log(LogLevel.ERROR, t);
		error("Fehler: " + t.getMessage());
	}

	public void showError(final String errorMessage) {
		Logger.log(LogLevel.ERROR, errorMessage);
		error(errorMessage);
	}

	/**
	 * Show main window if it is currently hidden.
	 * If the window is already showing, it will pop to front.
	 */
	public void showMainWindow() {
		getCore().showMainWindow();
	}

	private void askShowVersionHistory() {
		askShowVersionHistory(this, new ShowVersionHistoryAction(this, "../help", "vmd-versionhistory.html"));
	}
	
}

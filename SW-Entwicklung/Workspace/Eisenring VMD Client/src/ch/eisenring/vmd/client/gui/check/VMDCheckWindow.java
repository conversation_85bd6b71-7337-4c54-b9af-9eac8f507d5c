package ch.eisenring.vmd.client.gui.check;

import java.awt.BorderLayout;
import java.awt.Dimension;

import javax.swing.WindowConstants;

import ch.eisenring.app.shared.network.RPCContext;
import ch.eisenring.app.shared.network.RPCHandler;
import ch.eisenring.app.shared.network.RPCHandlerEDT;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.tag.TagSet;
import ch.eisenring.gui.balloontip.BalloonTipManager;
import ch.eisenring.gui.model.StoreResults;
import ch.eisenring.gui.window.AbstractBaseWindow;
import ch.eisenring.gui.window.SACButtonPanel;
import ch.eisenring.gui.window.WindowTags;
import ch.eisenring.vmd.client.VMDClient;
import ch.eisenring.vmd.client.gui.AbstractVMDFrame;
import ch.eisenring.vmd.client.gui.VMDMainMenuBar;
import ch.eisenring.vmd.client.resources.images.Images;
import ch.eisenring.vmd.shared.model.pojo.VMDRuleResult;
import ch.eisenring.vmd.shared.network.PacketProjectValidationReply;
import ch.eisenring.vmd.shared.network.PacketProjectValidationRequest;

@SuppressWarnings("serial")
public class VMDCheckWindow extends AbstractVMDFrame {

	public final VMDCheckPanel checkPanel;
	public final SACButtonPanel buttonPanel = new SACButtonPanel();
	
	private final RPCHandler rpcHandler = new RPCHandlerEDT() {
		@Override
		public void timeoutOccured(final RPCContext rpcContext) {
			BalloonTipManager.showError(buttonPanel.getBalloonTipComponent(), 
					"Zeitüberschreitung der Serveranfrage");
		}
		@Override
		public void requestSent(final RPCContext rpcContext) {
			BalloonTipManager.clearAll(VMDCheckWindow.this);
		}
		@Override
		public void replyReceived(final RPCContext rpcContext) {
			final PacketProjectValidationReply reply = (PacketProjectValidationReply) rpcContext.getReply();
			if (reply.isValid()) {
				// close window and display results
				final List<VMDRuleResult> results = reply.getResults();
				// ignore non-open results
				for (int i=results.size()-1; i>=0; --i) {
					final VMDRuleResult result = results.get(i);
					if (!result.isOpen()) {
						results.remove(i);
					}
				}
				if (results.isEmpty()) {
					final StringMaker maker = StringMaker.obtain(512);
					maker.append("<html><u><b><font color=green>Exzellent:</font></b></u><br>");
					maker.append("Keine Probleme gefunden");
					BalloonTipManager.show(buttonPanel.getBalloonTipComponent(), maker.release());
				} else {
					setVisible(!client.CHECK_WINDOW_AUTOCLOSE.get(Boolean.TRUE));
					checkPanel.reset();
					final VMDCheckResultWindow resultWindow = new VMDCheckResultWindow(client);
					resultWindow.setResults(results);
					resultWindow.setVisible(true);
				}
			} else {
				// validation failed, display error message
				BalloonTipManager.show(buttonPanel.getBalloonTipComponent(), reply.getMessage());
			}
		}
	};

	// --------------------------------------------------------------
	// ---
	// --- Constructor
	// ---
	// --------------------------------------------------------------
	public VMDCheckWindow(final VMDClient client) {
		super(client, new TagSet(
				WindowTags.ICON, Images.VMD,
				WindowTags.TITLE, client.getCore().getCoreDescriptor().getWindowTitle(),
				WindowTags.MINIMUM_SIZE, new Dimension(256, 128),
				WindowTags.RESIZEABLE, WindowTags.RESIZEABLE_NO,
				WindowTags.POSITION_SETTINGS_ID, "VMDCheckWindow",
				WindowTags.LAYOUTMANAGER, new BorderLayout()
		));
		checkPanel = new VMDCheckPanel(this);
		setDefaultCloseOperation(WindowConstants.DO_NOTHING_ON_CLOSE);
		initComponents();
		initLayout();
		pack();
	}

	private void initComponents() {
		buttonPanel.configureSaveButton("Prüfen", "Das gewählte Projekt jetzt auf Probleme prüfen");
		buttonPanel.configureApplyButton(null, null);
		buttonPanel.configureCancelButton(null, null);
	}

	private void initLayout() {
		getContentPane().removeAll();
		setJMenuBar(VMDMainMenuBar.getMainMenuBar(this));
		add(checkPanel, BorderLayout.CENTER);
		add(buttonPanel, BorderLayout.SOUTH);
	}

	@Override
	public void storeView(final StoreResults results, final Object model) {
		super.storeView(results, model);
		final PacketProjectValidationRequest request = checkPanel.getRequest();
		client.sendPacket(rpcHandler, request);
	}

	// --------------------------------------------------------------
	// ---
	// --- Window implementation
	// ---
	// --------------------------------------------------------------
	@Override
	protected void onOk() {
		super.onOk();
		AbstractBaseWindow.Util.doApply(this);
	}

	@Override
	protected void onCancel() {
		super.onCancel();
		setVisible(false);
	}

}

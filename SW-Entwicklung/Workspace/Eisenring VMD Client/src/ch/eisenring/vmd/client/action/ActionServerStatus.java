package ch.eisenring.vmd.client.action;

import ch.eisenring.gui.window.AbstractBaseWindow;
import ch.eisenring.vmd.client.VMDClient;
import ch.eisenring.vmd.client.gui.status.VMDServerStatusWindow;
import ch.eisenring.vmd.client.resources.images.Images;

public class ActionServerStatus extends AbstractVMDAction {

	public ActionServerStatus(final VMDClient client) {
		super(client, Images.COG, "Server Status...");
	}

	@Override
	protected void performAction() {
		final VMDServerStatusWindow window = new VMDServerStatusWindow(client);
		AbstractBaseWindow.showWindow(window);
	}

}

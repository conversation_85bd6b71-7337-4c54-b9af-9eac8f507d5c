package ch.eisenring.vmd.client.action;

import ch.eisenring.vmd.client.VMDClient;
import ch.eisenring.vmd.client.resources.images.Images;

public class ActionCheckNow extends AbstractVMDAction {

	public ActionCheckNow(final VMDClient client) {
		super(client, Images.SPHERE_BLUE, "Sofortprüfung...", DEFAULT_ACTION_TRUE);
	}

	@Override
	protected void performAction() {
		showGUI(client);
	}

	public static void showGUI(final VMDClient client) {
		client.showMainWindow();
	}

}

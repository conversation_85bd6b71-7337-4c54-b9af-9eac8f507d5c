package ch.eisenring.vmd.client.action;

import ch.eisenring.app.client.action.AbstractAPPAction;
import ch.eisenring.vmd.client.VMDClient;
import ch.eisenring.vmd.client.gui.AbstractVMDWindow;

public abstract class AbstractVMDAction extends AbstractAPPAction<VMDClient> {

	protected AbstractVMDWindow window;

	protected AbstractVMDAction(final VMDClient client, final Object ... properties) {
		super(client, properties);
	}

	protected AbstractVMDAction(final AbstractVMDWindow window, final Object ...properties) {
		super(window.getClient(), properties);
		this.window = window;
	}

}

package ch.eisenring.vmd.client.gui.status;

import ch.eisenring.vmd.client.VMDClient;
import ch.eisenring.vmd.client.action.AbstractVMDAction;
import ch.eisenring.vmd.client.resources.images.Images;
import ch.eisenring.vmd.shared.network.PacketServerStatusRequest;

class ActionRefresh extends AbstractVMDAction {

	public ActionRefresh(final VMDClient client) {
		super(client, Images.REFRESH, "Aktualisieren");
	}

	@Override
	protected void performAction() {
		final PacketServerStatusRequest request = PacketServerStatusRequest.create();
		client.sendPacket(request, false);
	}

}

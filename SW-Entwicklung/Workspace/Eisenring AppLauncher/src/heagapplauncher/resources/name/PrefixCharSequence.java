package heagapplauncher.resources.name;

final class PrefixCharSequence extends CharSe<PERSON><PERSON><PERSON> {

	final CharSequence prefix;
	final byte[] bytes;
	final int offsetAndLength;

	public PrefixCharSequence(final CharSequence prefix, final byte[] bytes, final int offset, final int length) {
		this.prefix = prefix;
		this.bytes = bytes;
		this.offsetAndLength = offset | (length << LENGTH_SHIFT);
	}

	// --------------------------------------------------------------
	// ---
	// --- CharSequence implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public char charAt(final int index) {
		if (index < 0)
			throwIndexException(index);
		int l = prefix.length();
		if (index < l)
			return prefix.charAt(index);
		int i = index - l;
		if (i >= (offsetAndLength >>> LENGTH_SHIFT))
			throwIndexException(index);
		return (char) bytes[(offsetAndLength & OFFSET_MASK) + i];
	}

	@Override
	public int length() {
		return prefix.length() + (offsetAndLength >>> LENGTH_SHIFT);
	}

	int offset() {
		return offsetAndLength & OFFSET_MASK;
	}

}

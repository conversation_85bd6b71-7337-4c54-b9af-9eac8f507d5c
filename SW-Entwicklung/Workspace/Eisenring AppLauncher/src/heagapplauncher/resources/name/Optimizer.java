package heagapplauncher.resources.name;

import heagapplauncher.resources.ResourceNameAccessor;

import java.util.HashMap;
import java.util.Map;

public class Optimizer {

	private final static int TYPE_STRING = 0;
	private final static int TYPE_BYTE = 1;
	private final static int TYPE_CLASS = 2;

	static class Allocation {
		String string;
		int offset;
		int type;
		int length;
		
		public CharSequence createCharSequence(final CharSequence prefix, final byte[] bytes) {
			switch (type) {
				default:
				case TYPE_STRING:
					return prefix.length() > 0 ? prefix + string : string;
				case TYPE_BYTE:
					return new PrefixCharSequence(prefix, bytes, offset, length);
				case TYPE_CLASS:
					return new ClassCharSequence(prefix, bytes, offset, length);
			}
		}
	}

	private Map<String, Allocation> allocMap = new HashMap<>();
	
	private int byteIndex;
	private byte[] byteBuffer;

	Allocation allocate(final String string) {
		Allocation result = allocMap.get(string);
		if (result == null) {
			result = new Allocation();
			allocMap.put(string, result);
			result.string = string;
			result.length = string.length();
			result.offset = -1;
			result.type = TYPE_STRING;
			if (CharSequenceBase.isByteContent(string)) {
				result.offset = byteIndex;
				if (CharSequenceBase.isClassSuffix(string)) {
					result.length -= 6;
					result.type = TYPE_CLASS;
				} else {
					result.type = TYPE_BYTE;
				}
				byteIndex += result.length;
			}
		}
		if (result.type != TYPE_STRING && byteBuffer != null) {
			int j = result.offset;
			for (int i=0; i<result.length; ++i) {
				byteBuffer[j++] = (byte) string.charAt(i);
			}
		}
		return result;
	}

	void clear() {
		byteIndex = 0;
		byteBuffer = null;
		allocMap.clear();
	}

	public void optimize(final Object[] elements) {
		clear();
		int charLength = 0;
		int total = 0;
		for (int pass=0; pass<2; ++pass) {
			if (pass == 0) {
				byteBuffer = null;
			} else {
				byteBuffer = new byte[byteIndex];
			}
			byteIndex = 0;
			charLength = 0;
			total = 0;
			for (final Object element : elements) {
				final ResourceNameAccessor accessor = getAccessor(element);
				if (accessor == null)
					continue;
				final CharSequence rawQualified = accessor.getQualifiedName();
				total += rawQualified.length();
				if (!CharSequenceBase.isByteContent(rawQualified)) {
					charLength += rawQualified.length();
					continue;
				}
				final String qualified = rawQualified.toString();
				final String name = getName(qualified);
				final String prefix = getPrefix(qualified);
				Allocation ap = allocate(prefix);
				Allocation an = allocate(name);
				if (byteBuffer != null) {
					if (an.type != TYPE_STRING) {
						CharSequence csp = ap.createCharSequence("", byteBuffer);
						CharSequence csn = an.createCharSequence(csp, byteBuffer);
						accessor.setQualifiedName(csn);
//System.out.println(qualified + " ? " + csn);					
					}
				}
			}
		}
		System.out.println("bytes/characters/total: " + byteBuffer.length + "/" + charLength + "/" + total);
		clear();
	}
	
	static String getPrefix(final String qualifiedName) {
		final int i = qualifiedName.lastIndexOf('/');
		return i < 0 ? "" : qualifiedName.substring(0, i+1);
	}

	static String getName(final String qualifiedName) {
		final int i = qualifiedName.lastIndexOf('/');
		return i < 0 ? qualifiedName : qualifiedName.substring(i+1);
	}

	static ResourceNameAccessor getAccessor(final Object object) {
		return object instanceof ResourceNameAccessor ? (ResourceNameAccessor) object : null;
	}

}

package heagapplauncher.resources.zip;

import heagapplauncher.resources.ResourceBase;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;

final class ZIPResource extends ResourceBase<ZIPResourceProvider> {

	public ZIPResource(final ZIPResourceProvider provider, final CharSequence entryName) {
		super(provider, entryName);
	}

	// --------------------------------------------------------------
	// ---
	// --- ResourceEntry implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public long getSize() throws IOException {
		return provider.getByteArray(getQualifiedName()).length;
	}

	@Override
	public InputStream getInputStream() throws IOException {
		return new ByteArrayInputStream(getByteArray());
	}

	@Override
	public byte[] getByteArray() throws IOException {
		return provider.getByteArray(getQualifiedName());
	}

}

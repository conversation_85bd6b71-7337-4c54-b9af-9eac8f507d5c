package ch.eisenring.dms.shared.network;

import ch.eisenring.core.codetable.ErrorMessage;

public final class PacketGetObjectReply extends PacketObjectTransportBase {

	private PacketGetObjectReply() {
		super(CAPS_SEQUENCE_REPLY | CAPS_ERRORMESSAGE, PRIORITY_REPLY);
	}

	/**
	 * Creates a reply packet
	 */
	public static PacketGetObjectReply create(final PacketGetObjectRequest request,
								              final ErrorMessage error) {
		final PacketGetObjectReply packet = new PacketGetObjectReply();
		packet.setReplyId(request, error);
		return packet;
	}

}

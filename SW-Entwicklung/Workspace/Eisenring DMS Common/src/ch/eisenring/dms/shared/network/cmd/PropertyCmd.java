package ch.eisenring.dms.shared.network.cmd;

import java.io.IOException;

import ch.eisenring.core.io.StreamReader;
import ch.eisenring.core.io.StreamWriter;
import ch.eisenring.core.io.Streamable;
import ch.eisenring.dms.service.codetables.DMSPropertyCode;
import ch.eisenring.dms.shared.codetables.DMSPropertyCmdCode;

public final class PropertyCmd implements Streamable {

	protected long objectRowId;
	protected DMSPropertyCmdCode cmdCode;
	protected DMSPropertyCode propertyCode;
	protected String propertyValue;

	public PropertyCmd(final DMSPropertyCmdCode cmdCode,
			           final long objectRowId,
			           final DMSPropertyCode propertyCode,
			           final String propertyValue) {
		this();
		this.cmdCode = cmdCode;
		this.objectRowId = objectRowId;
		this.propertyCode = propertyCode;
		this.propertyValue = propertyValue;
	}

	// --------------------------------------------------------------
	// ---
	// --- Getters
	// ---
	// --------------------------------------------------------------
	public DMSPropertyCmdCode getCmdCode() {
		return cmdCode;
	}

	public long getObjectRowId() {
		return objectRowId;
	}

	public DMSPropertyCode getPropertyCode() {
		return propertyCode;
	}

	public String getPropertyValue() {
		return propertyValue;
	}

	// --------------------------------------------------------------
	// ---
	// --- Streamable implementation
	// ---
	// --------------------------------------------------------------
	private PropertyCmd() {
		// default constructor for Streamable
	}

	@Override
	public void read(final StreamReader reader) throws IOException {
		objectRowId = reader.readLong();
		cmdCode = reader.readCode(DMSPropertyCmdCode.class);
		propertyCode = reader.readCode(DMSPropertyCode.class);
		propertyValue = reader.readString();
	}
	
	@Override
	public void write(final StreamWriter writer) throws IOException {
		writer.writeLong(objectRowId);
		writer.writeCode(cmdCode);
		writer.writeCode(propertyCode);
		writer.writeString(propertyValue);
	}

}
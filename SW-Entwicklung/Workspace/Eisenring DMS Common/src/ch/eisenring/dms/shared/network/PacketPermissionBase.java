package ch.eisenring.dms.shared.network;

import java.io.IOException;
import java.util.Iterator;

import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.Set;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.collections.impl.HashSet;
import ch.eisenring.core.io.StreamReader;
import ch.eisenring.core.io.StreamUtil;
import ch.eisenring.core.io.StreamWriter;
import ch.eisenring.dms.shared.model.data.PermissionDescriptor;

public abstract class PacketPermissionBase extends AbstractDMSPacket {

	protected String user;
	protected final Set<PermissionDescriptor> descriptors = new HashSet<>();

	PacketPermissionBase() {
		super(CAPS_SEQUENCE_REQUEST | CAPS_SEQUENCE_REPLY | CAPS_ERRORMESSAGE, PRIORITY_DEFAULT);
	}

	PacketPermissionBase(final int caps, final int priority) {
		super(caps, priority);
	}

	// --------------------------------------------------------------
	// ---
	// --- Basic Setters
	// ---
	// --------------------------------------------------------------
	protected void setUser(final String user) {
		this.user = user;
	}

	protected void addPermissions(final PermissionDescriptor ... descriptors) {
		if (descriptors != null) {
			for (final PermissionDescriptor descriptor : descriptors) {
				this.descriptors.add(descriptor);
			}
		}
	}

	// --------------------------------------------------------------
	// ---
	// --- Basic Getters
	// ---
	// --------------------------------------------------------------
	public String getUser() {
		return user;
	}

	public Iterator<PermissionDescriptor> getPermissionIterator() {
		return new ArrayList<>(descriptors).iterator();
	}

	public List<PermissionDescriptor> getPermissions() {
		return new ArrayList<>(descriptors);
	}

	// --------------------------------------------------------------
	// ---
	// --- Streamable
	// ---
	// --------------------------------------------------------------
	@Override
	protected void readImpl(final StreamReader reader) throws IOException {
		super.readImpl(reader);
		user = reader.readString();
		StreamUtil.readCollection(reader, descriptors);
	}

	@Override
	protected void writeImpl(final StreamWriter writer) throws IOException {
		super.writeImpl(writer);
		writer.writeString(user);
		StreamUtil.writeCollection(writer, descriptors);
	}

}

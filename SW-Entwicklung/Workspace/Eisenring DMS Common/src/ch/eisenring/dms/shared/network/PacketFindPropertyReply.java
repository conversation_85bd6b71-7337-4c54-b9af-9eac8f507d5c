package ch.eisenring.dms.shared.network;

import ch.eisenring.dms.shared.model.DAOResult;

/**
 * Result packet for find by property value
 */
public final class PacketFindPropertyReply extends AbstractDAOReplyPacket {

	private PacketFindPropertyReply() {
		super(CAPS_SEQUENCE_REPLY | CAPS_ERRORMESSAGE, PRIORITY_REPLY);
	}

	public static PacketFindPropertyReply create(final PacketFindPropertyRequest request,
												 final DAOResult result) {
		final PacketFindPropertyReply packet = new PacketFindPropertyReply();
		packet.setResult(request, result);
		return packet;
	}

	// --------------------------------------------------------------
	// ---
	// --- Packet implementation
	// ---
	// --------------------------------------------------------------

	// no packet specific implementation
		
}

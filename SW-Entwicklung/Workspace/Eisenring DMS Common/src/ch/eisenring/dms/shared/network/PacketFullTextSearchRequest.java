package ch.eisenring.dms.shared.network;

import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.alterableview.AlterableView;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.datatypes.date.DateGranularityCode;
import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.core.io.AutoStreamed;
import ch.eisenring.dms.service.codetables.DMSDocumentType;
import ch.eisenring.dms.service.codetables.DMSObjectStatus;
import ch.eisenring.dms.service.codetables.DMSObjectType;

public final class PacketFullTextSearchRequest extends AbstractDAORequestPacket {

	/**
	 * The search text as entered by the user
	 */
	@AutoStreamed
	private String searchCriteria;

	/**
	 * Row id of the folder to be searched. If null search entire DMS
	 */
	@AutoStreamed
	private Long parentFolderId;
	
	/**
	 * Flag if child folders should be searched
	 */
	@AutoStreamed
	private boolean searchChildFolders = true;
	
	/**
	 * List of document types to be considered, if empty: any
	 */
	@AutoStreamed
	private final List<DMSDocumentType> documentTypes = new ArrayList<>();
	
	/**
	 * Modified Date upper bound, if null none
	 */
	@AutoStreamed
	private long lastModifiedUpper;
	
	/**
	 * Modified Date lower bound, if null none
	 */
	@AutoStreamed
	private long lastModifiedLower;

	/**
	 * Document size lower bound, if null none
	 */
	@AutoStreamed
	private Long documentSizeLower;
	
	/**
	 * Document size upper bound, if null none
	 */
	@AutoStreamed
	private Long documentSizeUpper;

	/**
	 * Controls the object status. If not null,
	 * only objects in a status equal or less
	 * to this code are found. 
	 */
	@AutoStreamed
	private DMSObjectStatus objectStatus;

	/**
	 * Controls the type of object to be searched.
	 * If null, any type of object will be searched,
	 * otherwise only the specified type.
	 */
	@AutoStreamed
	private DMSObjectType objectType;

	private PacketFullTextSearchRequest() {
	}

	/**
	 * Creates a full text search request
	 */
	public static PacketFullTextSearchRequest create(final Long parentFolderId,
			final String searchCriteria, final String user) {
		final PacketFullTextSearchRequest packet = new PacketFullTextSearchRequest();
		packet.parentFolderId = parentFolderId;
		packet.searchCriteria = searchCriteria;
		packet.lastModifiedLower = DateGranularityCode.DAY.round(System.currentTimeMillis(), -30);
		packet.lastModifiedUpper = TimestampUtil.NULL_TIMESTAMP;
		packet.setObjectType(DMSObjectType.FILE);
		packet.setUser(user);
		return packet;
	}

	// --------------------------------------------------------------
	// ---
	// --- Getters & Setters
	// ---
	// --------------------------------------------------------------
	public String getSearchCriteria() {
		return searchCriteria;
	}

	public void setSearchCriteria(final String searchCriteria) {
		this.searchCriteria = searchCriteria;
	}

	public Long getParentFolderId() {
		return parentFolderId;
	}

	public void setParentFolderId(final Long parentFolderId) {
		this.parentFolderId = parentFolderId;
	}

	public boolean getSearchChildFolders() {
		return searchChildFolders;
	}

	public void setSearchChildFolders(final boolean searchChildFolders) {
		this.searchChildFolders = searchChildFolders;
	}

	/**
	 * Removes any restriction to document types
	 */
	public void clearDocumentTypes() {
		documentTypes.clear();
	}

	/**
	 * Gets the document types the search should be restricted to.
	 * If the list is empty it means any document type.
	 */
	public List<DMSDocumentType> getDocumentTypes() {
		return AlterableView.of(documentTypes);
	}

	/**
	 * Adds a document type to be searched for
	 */
	public void addDocumentType(final DMSDocumentType documentType) {
		if (documentType != null && !documentTypes.contains(documentType))
			documentTypes.add(documentType);
	}

	/**
	 * Gets the last modified date, upper bound.
	 * Null if no upper bound shall be applied.
	 */
	public long getLastModifiedUpper() {
		return lastModifiedUpper;
	}
	
	public void setLastModifiedUpper(final long lastModifiedUpper) {
		this.lastModifiedUpper = lastModifiedUpper;
	}

	/**
	 * Gets the last modified date, lower bound.
	 * Null if no lower bound shall be applied.
	 */
	public long getLastModifiedLower() {
		return lastModifiedLower;
	}
	
	public void setLastModifiedLower(final long lastModifiedLower) {
		this.lastModifiedLower = lastModifiedLower;
	}

	/**
	 * Gets the upper bound for this document size.
	 * Null if no upper bound shall be applied.
	 */
	public Long getDocumentSizeUpper() {
		return documentSizeUpper;
	}

	public void setDocumentSizeUpper(final Long documentSizeUpper) {
		this.documentSizeUpper = documentSizeUpper;
	}

	/**
	 * Gets the lower bound for this document size.
	 * Null if no lower bound shall be applied.
	 */
	public Long getDocumentSizeLower() {
		return documentSizeLower;
	}

	public void setDocumentSizeLower(final Long documentSizeLower) {
		this.documentSizeLower = documentSizeLower;
	}

	/**
	 * Gets the object status the search should be restricted to.
	 * Null means no status restriction.
	 */
	public DMSObjectStatus getObjectStatus() {
		return objectStatus;
	}

	public void setObjectStatus(final DMSObjectStatus objectStatus) {
		this.objectStatus = objectStatus;
	}

	/**
	 * Gets the object type the search should be restricted to.
	 * Null means no type restriction.
	 */
	public DMSObjectType getObjectType() {
		return objectType;
	}

	public void setObjectType(final DMSObjectType objectType) {
		this.objectType = objectType;
	}
	
}

package ch.eisenring.dms.shared.network;

import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.dms.shared.network.cmd.ObjectMessageSet;

public final class PacketSetObjectIconReply extends PacketObjectMessagesBase {

	private PacketSetObjectIconReply() {
		super(CAPS_SEQUENCE_REPLY | CAPS_ERRORMESSAGE, PRIORITY_REPLY);
	}
	
	public static PacketSetObjectIconReply create(final PacketSetObjectIconRequest request,
			                                      final ObjectMessageSet changeMessages) {
		final PacketSetObjectIconReply packet = new PacketSetObjectIconReply();
		packet.setReplyId(request, ErrorMessage.OK);
		packet.messages = changeMessages;
		return packet;
	}

	public static PacketSetObjectIconReply create(final PacketSetObjectIconRequest request, final ErrorMessage error) {
		final PacketSetObjectIconReply packet = new PacketSetObjectIconReply();
		packet.setReplyId(request, error);
		return packet;
	}

}

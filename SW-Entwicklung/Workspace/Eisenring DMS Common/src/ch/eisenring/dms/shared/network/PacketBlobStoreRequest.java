package ch.eisenring.dms.shared.network;

import ch.eisenring.core.io.AutoStreamed;
import ch.eisenring.dms.shared.model.record.DMSBlob;

/**
 * Store a blob
 */
public final class PacketBlobStoreRequest extends AbstractDMSPacket {

	@AutoStreamed
	private DMSBlob blob;

	private PacketBlobStoreRequest() {
		super(CAPS_SEQUENCE_REQUEST, PRIORITY_REQUEST);
	}

	// --------------------------------------------------------------
	// ---
	// --- Factory methods
	// ---
	// --------------------------------------------------------------
	public static PacketBlobStoreRequest create(final DMSBlob blob) {
		final PacketBlobStoreRequest packet = new PacketBlobStoreRequest();
		packet.blob = blob;
		return packet;
	}

	// --------------------------------------------------------------
	// ---
	// --- Packet specific API
	// ---
	// --------------------------------------------------------------
	public DMSBlob getBlob() {
		return blob;
	}

}

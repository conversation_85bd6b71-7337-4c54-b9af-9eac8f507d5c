package ch.eisenring.dms.shared.cache.binary;

import ch.eisenring.core.collections.List;
import ch.eisenring.dms.shared.model.api.DMSBinary;

/**
 * Abstract cache for object versions.
 */
public interface DMSBinaryCache {

	/**
	 * Gets the specified binary by primary key.
	 * Return NULL if no such binary is in cache.
	 */
	public DMSBinary getBinary(final long binaryRowId);

	/**
	 * Returns all the binaries cached for object.
	 * If there are no cached binaries, returns NULL (not an empty list!)
	 */
	public List<DMSBinary> getBinaries(final long objectRowId);

	/**
	 * Adds the given binary to the cache.
	 * This may cause the eviction of other cached binaries.
	 */
	public void addBinary(final DMSBinary binary);

	/**
	 * Orders the cache to forget all cached items.
	 */
	public void flush();

	/**
	 * Sets the cache size
	 */
	public void setCachesize(final long cacheSize);

	/**
	 * Dummy cache implementation
	 */
	public DMSBinaryCache DUMMY = new DMSBinaryCacheDummy();

	/**
	 * To be called periodically to maintain cache
	 */
	public void tick();

}

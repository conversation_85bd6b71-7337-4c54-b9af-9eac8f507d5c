package ch.eisenring.dms.shared.network;

import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.io.AutoStreamed;
import ch.eisenring.logiware.code.pseudo.AVORTeamCode;
import ch.eisenring.logiware.code.soft.LWKuechenTypCode;
import ch.eisenring.logiware.code.soft.LWKundenberaterCode;
import ch.eisenring.logiware.code.soft.LWMarketingCode;
import ch.eisenring.logiware.code.soft.LWObjektBetreuerCode;
import ch.eisenring.logiware.model.LWSubjektInfo;

public final class PacketLWProjectInfoReply extends PacketLWProjectInfoBase {

	@AutoStreamed
	protected LWKundenberaterCode kundenberater;

    @AutoStreamed
    protected AVORTeamCode kundenberater2;
	
	@AutoStreamed
	protected LWObjektBetreuerCode objektbetreuer;

	@AutoStreamed
	protected LWSubjektInfo subjektVerkaeufer;

	@AutoStreamed
	protected LWSubjektInfo subjektAG;

	@AutoStreamed
	protected LWSubjektInfo subjektA;

	@AutoStreamed
	protected LWSubjektInfo subjektB;

	@AutoStreamed
	protected LWSubjektInfo subjektI;

	@AutoStreamed
	protected LWSubjektInfo subjektK;

	@AutoStreamed
	protected LWMarketingCode marketingCode;

	@AutoStreamed
	protected String projectDescription;

	@AutoStreamed
	protected Integer idSubjektAG;

	@AutoStreamed
	protected Integer idSubjektA;

	@AutoStreamed
	protected Integer idSubjektB;

	@AutoStreamed
	protected Integer idSubjektI;

	@AutoStreamed
	protected Integer idSubjektK;
	
	@AutoStreamed
	protected Integer idSubjektVerkaeufer;

	@AutoStreamed
	protected String addressOverrideB;

	@AutoStreamed
	protected String anzahlKuechen;

	@AutoStreamed
	protected String prjBezPLZ;
	
	@AutoStreamed
	protected String prjBezOrt;

	@AutoStreamed
	protected String prjBezStrasse;

	// --------------------------------------------------------------
	// ---
	// --- Constructor
	// ---
	// --------------------------------------------------------------
	private PacketLWProjectInfoReply() {
		super(CAPS_SEQUENCE_REPLY | CAPS_ERRORMESSAGE, PRIORITY_REPLY);
	}

	// --------------------------------------------------------------
	// ---
	// --- Factory
	// ---
	// --------------------------------------------------------------
	public static PacketLWProjectInfoReply create(final PacketLWProjectInfoRequest request, final ErrorMessage error) {
		final PacketLWProjectInfoReply packet = new PacketLWProjectInfoReply();
		packet.setReplyId(request, error);
		packet.projectNumber = request.projectNumber;
		packet.flags = request.flags;
		return packet;
	}

	// --------------------------------------------------------------
	// ---
	// --- Packet specific API
	// ---
	// --------------------------------------------------------------
	public LWKundenberaterCode getKundenberater() {
		return kundenberater;
	}

	public void setKundenberater(final LWKundenberaterCode kundenberater) {
		this.kundenberater = kundenberater;
	}

    public AVORTeamCode getKundenberater2() {
        return kundenberater2;
    }

    public void setKundenberater2(final AVORTeamCode kundenberater) {
        this.kundenberater2 = kundenberater;
    }

	public LWObjektBetreuerCode getObjektbetreuer() {
		return objektbetreuer;
	}

	public void setObjektbetreuer(final LWObjektBetreuerCode objektbetreuer) {
		this.objektbetreuer = objektbetreuer;
	}

	public LWSubjektInfo getSubjektAG() {
		return subjektAG;
	}

	public void setSubjektAG(final LWSubjektInfo subjekt) {
		this.subjektAG = subjekt;
	}

	public LWSubjektInfo getSubjektA() {
		return subjektA;
	}

	public void setSubjektA(final LWSubjektInfo subjekt) {
		this.subjektA = subjekt;
	}

	public LWSubjektInfo getSubjektB() {
		return subjektB;
	}

	public void setSubjektB(final LWSubjektInfo subjekt) {
		this.subjektB = subjekt;
	}

	public LWSubjektInfo getSubjektI() {
		return subjektI;
	}

	public void setSubjektI(final LWSubjektInfo subjekt) {
		this.subjektI = subjekt;
	}

	public LWSubjektInfo getSubjektK() {
		return subjektK;
	}

	public void setSubjektK(final LWSubjektInfo subjekt) {
		this.subjektK = subjekt;
	}
	
	public String getProjectDescription() {
		return projectDescription;
	}

	public void setProjectDescription(final String description) {
		this.projectDescription = description;
	}

	public LWSubjektInfo getSubjektVerkaeufer() {
		return subjektVerkaeufer;
	}

	public void setSubjektVerkaeufer(final LWSubjektInfo subjekt) {
		this.subjektVerkaeufer = subjekt;
	}

	public LWMarketingCode getMarketing() {
		return marketingCode;
	}

	public void setMarketing(final LWMarketingCode marketingCode) {
		this.marketingCode = marketingCode;
	}

	public Integer getIdSubjektVerkauefer() {
		return idSubjektVerkaeufer;
	}

	public void setIdSubjektVerkaeufer(final Integer idSubjekt) {
		this.idSubjektVerkaeufer = idSubjekt;
	}

	public Integer getIdSubjektAG() {
		return idSubjektAG;
	}

	public void setIdSubjektAG(final Integer idSubjekt) {
		this.idSubjektAG = idSubjekt;
	}

	public Integer getIdSubjektA() {
		return idSubjektA;
	}

	public void setIdSubjektA(final Integer idSubjekt) {
		this.idSubjektA = idSubjekt;
	}

	public Integer getIdSubjektB() {
		return idSubjektB;
	}

	public void setIdSubjektB(final Integer idSubjekt) {
		this.idSubjektB = idSubjekt;
	}

	public Integer getIdSubjektI() {
		return idSubjektI;
	}

	public void setIdSubjektI(final Integer idSubjekt) {
		this.idSubjektI = idSubjekt;
	}

	public Integer getIdSubjektK() {
		return idSubjektK;
	}

	public void setIdSubjektK(final Integer idSubjekt) {
		this.idSubjektK = idSubjekt;
	}

	public String getAddressOverrideB() {
		return addressOverrideB;
	}

	public void setAddressOverrideB(final String addressOverrideB) {
		this.addressOverrideB = addressOverrideB;
	}

	public String getAnzahlKuechen() {
		return anzahlKuechen;
	}

	public void setAnzahlKuechen(final String anzahlKuechen) {
		this.anzahlKuechen = anzahlKuechen;
	}

	public String getProjektPLZ() {
		return prjBezPLZ;
	}

	public void setProjektPLZ(final String prjBezPLZ) {
		this.prjBezPLZ = prjBezPLZ;
	}
	
	public String getProjektOrt() {
		return prjBezOrt;
	}

	public void setProjektOrt(final String prjBezOrt) {
		this.prjBezOrt = prjBezOrt;
	}

	public String getProjektStrasse() {
		return prjBezStrasse;
	}

	public void setProjektStrasse(final String prjBezStrasse) {
		this.prjBezStrasse = prjBezStrasse;
	}

}

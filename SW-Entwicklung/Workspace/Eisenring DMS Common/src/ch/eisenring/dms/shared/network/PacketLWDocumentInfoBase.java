package ch.eisenring.dms.shared.network;

import java.io.IOException;

import ch.eisenring.core.io.StreamReader;
import ch.eisenring.core.io.StreamWriter;

public abstract class PacketLWDocumentInfoBase extends AbstractDMSPacket {

	protected int documentId;

	PacketLWDocumentInfoBase(final int caps, final int priority) {
		super(caps, priority);
	}

	// --------------------------------------------------------------
	// ---
	// --- Packet specific API
	// ---
	// --------------------------------------------------------------
	/**
	 * Gets the document number to be queried
	 */
	public final int getDocumentId() {
		return documentId;
	}
	
	// --------------------------------------------------------------
	// ---
	// --- Base class overrides
	// ---
	// --------------------------------------------------------------
	@Override
	protected void readImpl(final StreamReader reader) throws IOException {
		super.readImpl(reader);
		documentId = reader.readInt();
	}

	@Override
	protected void writeImpl(final StreamWriter writer) throws IOException {
		super.writeImpl(writer);
		writer.writeInt(documentId);
	}

}

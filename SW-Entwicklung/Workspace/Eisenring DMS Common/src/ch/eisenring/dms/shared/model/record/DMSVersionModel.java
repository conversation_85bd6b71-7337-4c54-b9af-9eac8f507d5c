package ch.eisenring.dms.shared.model.record;

import static ch.eisenring.dms.shared.model.metaold.DMSVersionMetaModel.ATR_BINARYHASH;
import static ch.eisenring.dms.shared.model.metaold.DMSVersionMetaModel.ATR_BYTESIZE;
import static ch.eisenring.dms.shared.model.metaold.DMSVersionMetaModel.ATR_DCMCODE;
import static ch.eisenring.dms.shared.model.metaold.DMSVersionMetaModel.ATR_FILEEXTENSION;
import static ch.eisenring.dms.shared.model.metaold.DMSVersionMetaModel.ATR_MADEON;
import static ch.eisenring.dms.shared.model.metaold.DMSVersionMetaModel.ATR_OBJECT_ROWID;
import static ch.eisenring.dms.shared.model.metaold.DMSVersionMetaModel.ATR_RAWSIZE;
import static ch.eisenring.dms.shared.model.metaold.DMSVersionMetaModel.ATR_STATUSCODE;

import ch.eisenring.dms.service.codetables.DMSBinaryStatus;
import ch.eisenring.dms.shared.codetables.DMSCompressionCode;
import ch.eisenring.dms.shared.model.api.DMSVersion;
import ch.eisenring.dms.shared.model.metaold.DMSVersionMetaModel;
import ch.eisenring.model.MetaClass;
import ch.eisenring.model.PrimaryKey;
import ch.eisenring.model.TransactionContext;

public class DMSVersionModel extends DMSModel implements DMSVersion {

	public final static MetaClass METACLASS = DMSVersionMetaModel.METACLASS;

	/**
	 * Constructor is called via reflection
	 */
	protected DMSVersionModel(final TransactionContext context, final PrimaryKey pk) {
		super(context, pk);
	}
	
	// --------------------------------------------------------------
	// ---
	// --- Attribute Getters / Setters
	// ---
	// --------------------------------------------------------------
	public long getObjectRowId() {
		return (Long) ATR_OBJECT_ROWID.getMember(this);
	}

	public void setObjectRowId(final long objectRowId) {
		ATR_OBJECT_ROWID.setMember(this, objectRowId);
	}

	public DMSBinaryStatus getStatusCode() {
		return (DMSBinaryStatus) ATR_STATUSCODE.getMember(this);
	}

	public void setStatusCode(final DMSBinaryStatus statusCode) {
		ATR_STATUSCODE.setMember(this, statusCode);
	}

	public long getBytesize() {
		return (Long) ATR_BYTESIZE.getMember(this);
	}

	public long getRawsize() {
		return (Long) ATR_RAWSIZE.getMember(this);
	}

	@Override
	public long getMadeOn() {
		return (Long) ATR_MADEON.getMember(this);
	}

	public void setMadeOn(final long timestamp) {
		ATR_MADEON.setMember(this, timestamp);
	}

	public String getBinaryhash() {
		return (String) ATR_BINARYHASH.getMember(this);
	}

	// there is no setter for the binary hash, it will
	// be set automatically when the binary data is updated.

	public DMSCompressionCode getDCMCode() {
		return (DMSCompressionCode) ATR_DCMCODE.getMember(this);
	}

	// there is no setter for the compression code, it will
	// be set automatically when the binary data is updated.

	public String getFileExtension() {
		return (String) ATR_FILEEXTENSION.getMember(this);
	}

	public void setFileExtension(final String extension) {
		ATR_FILEEXTENSION.setMember(this, extension);
	}
	
}

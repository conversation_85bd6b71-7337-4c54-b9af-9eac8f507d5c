package ch.eisenring.dms.shared.network.brq;

import ch.eisenring.core.collections.Collection;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.dms.shared.model.api.DMSBinary;


/**
 * Request object specifying the active version of an object
 */
public final class BinaryRequestActive extends BinaryRequest {

	protected BinaryRequestActive() {
		// default constructor for reflection
	}

	public BinaryRequestActive(final long objectRowId) {
		super(objectRowId);
	}

	public BinaryRequestActive(final long objectRowId, final Collection<DMSBinary> cachedBinaries) {
		super(objectRowId, cachedBinaries);
	}
	
	@Override
	public String toString() {
		return Strings.concat("Aktive Version von Objekt #", getRowId());
	}

}

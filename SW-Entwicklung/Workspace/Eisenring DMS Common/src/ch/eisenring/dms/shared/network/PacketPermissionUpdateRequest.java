package ch.eisenring.dms.shared.network;

import ch.eisenring.dms.shared.model.data.PermissionDescriptor;

/**
 * Packet sent to update permission descriptor(s)
 */
public final class PacketPermissionUpdateRequest extends PacketPermissionBase {

	private PacketPermissionUpdateRequest() {
	}

	public static PacketPermissionUpdateRequest create(final String user,
													   final PermissionDescriptor ... descriptors) {
		final PacketPermissionUpdateRequest packet = new PacketPermissionUpdateRequest();
		packet.setUser(user);
		packet.addPermissions(descriptors);
		return packet;
	}

}

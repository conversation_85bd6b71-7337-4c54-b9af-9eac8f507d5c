package ch.eisenring.dms.shared.formular;

import ch.eisenring.core.codetype.StaticCode;

public final class DMSFormularCode extends StaticCode {

	public final static DMSFormularCode NULL = new DMSFormularCode(0, null, "", "");
	public final static DMSFormularCode LEISTUNGSBEWERTUNG = new DMSFormularCode(4, "Leistungsbewertung", "H04");
	public final static DMSFormularCode BEMUSTERUNGSPROTOKOLL_SIR_MIET = new DMSFormularCode(12, "Bemusterungsprotokoll Mietobjekt", "H01");
	// identical to BEMUSTERUNGSPROTOKOLL_SIR_MIET, but slightly different print variation
	public final static DMSFormularCode BEMUSTERUNGSPROTOKOLL_SIR_ET = new DMSFormularCode(112, "Bemusterungsprotokoll Einzel/Typen", "H01");
	public final static DMSFormularCode PME_AUSSCHREIBUNG = new DMSFormularCode(10000, "Angebot & Ausschreibung", "");
	public final static DMSFormularCode PME_FEHLERMELDUNG = new DMSFormularCode(10001, "PME Meldung", "");
	public static final DMSFormularCode PME_NEUER_LIEFERANT = new DMSFormularCode(10002, "Erfassung neuer Lieferant", "");

	private final String barcodePrefix;

	protected DMSFormularCode(final int id, final String text,
		final String barcodePrefix) {
		super(id, Integer.valueOf(id), text, text);
		this.barcodePrefix = barcodePrefix;
	}

	protected DMSFormularCode(final int id, final Object key, final String text,
		final String barcodePrefix) {
		super(id, key, text, text);
		this.barcodePrefix = barcodePrefix;
	}

	public String getBarcodePrefix() {
		return barcodePrefix;
	}

}

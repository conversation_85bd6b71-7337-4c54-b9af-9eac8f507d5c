package ch.eisenring.dms.shared.network;

import java.io.IOException;

import ch.eisenring.core.io.StreamReader;
import ch.eisenring.core.io.StreamWriter;

public final class PacketGetPropertiesRequest extends AbstractDMSPacket {

	protected transient long objectRowId;

	PacketGetPropertiesRequest() {
		super(CAPS_SEQUENCE_REQUEST, PRIORITY_REQUEST);
	}

	public static PacketGetPropertiesRequest create(final long objectRowId) {
		final PacketGetPropertiesRequest packet = new PacketGetPropertiesRequest();
		packet.objectRowId = objectRowId;
		return packet;
	}

	// --------------------------------------------------------------
	// ---
	// --- API
	// ---
	// --------------------------------------------------------------
	public long getObjectRowId() {
		return objectRowId;
	}

	// --------------------------------------------------------------
	// ---
	// --- Packet implementation
	// ---
	// --------------------------------------------------------------
	@Override
	protected void readImpl(final StreamReader reader) throws IOException {
		super.readImpl(reader);
		objectRowId = reader.readLong();
	}

	@Override
	protected void writeImpl(final StreamWriter writer) throws IOException {
		super.writeImpl(writer);
		writer.writeLong(objectRowId);
	}

}

package ch.eisenring.dms.shared.network;

import ch.eisenring.dms.shared.model.DAOResult;

public final class PacketRenameObjectReply extends AbstractDAOReplyPacket {

	private PacketRenameObjectReply() {
	}

	public static PacketRenameObjectReply create(final PacketRenameObjectRequest request,
			                                     final DAOResult result) {
		final PacketRenameObjectReply packet = new PacketRenameObjectReply();
		packet.setResult(request, result);
		return packet;
	}

}

package ch.eisenring.dms.shared.model.metaold;

import ch.eisenring.core.util.VarArgs;
import ch.eisenring.dms.shared.model.record.DMSSearch;
import ch.eisenring.model.MetaAttribute;
import ch.eisenring.model.MetaClass;
import ch.eisenring.model.MetaClassMember;
import ch.eisenring.model.MetaProperty;
import ch.eisenring.model.engine.jdbc.ColumnSpecifier;
import ch.eisenring.model.factory.AttributeFactory;
import ch.eisenring.model.primarykey.LongPrimaryKey;

public interface DMSSearchMetaModel extends DMSObjectMetaModel {

	MetaAttribute ATR_SEARCHWORDS = AttributeFactory.cleanString(
			"Searchwords", null, 65536,
			ColumnSpecifier.get(TABLE, "Searchwords"));

	MetaClass METACLASS = new MetaClass(
			"DMSSearch", DMSSearch.class,
			LongPrimaryKey.class,
			DMSObjectMetaModel.METACLASS,
			// Attributes
			new VarArgs<MetaProperty>(MetaProperty.class).flat(
			ColumnSpecifier.get(TABLE, "rowId"),
			TABLE,
			MetaClassMember.discoverMetaClassMembers(DMSSearchMetaModel.class)
			));

}

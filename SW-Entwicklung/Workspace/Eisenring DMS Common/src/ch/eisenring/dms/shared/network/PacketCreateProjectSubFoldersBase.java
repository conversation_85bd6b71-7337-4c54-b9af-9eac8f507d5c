package ch.eisenring.dms.shared.network;

import java.io.IOException;

import ch.eisenring.core.collections.Set;
import ch.eisenring.core.collections.alterableview.AlterableView;
import ch.eisenring.core.collections.impl.HashSet;
import ch.eisenring.core.io.StreamReader;
import ch.eisenring.core.io.StreamUtil;
import ch.eisenring.core.io.StreamWriter;
import ch.eisenring.logiware.code.pseudo.AbwicklungsartCode;

abstract class PacketCreateProjectSubFoldersBase extends AbstractDMSPacket {

	protected String user;
	protected String basisNummer;
	protected final Set<AbwicklungsartCode> abwicklungsArten = new HashSet<>();
	
	protected PacketCreateProjectSubFoldersBase(final int caps, final int priority) {
		super(caps, priority);
	}

	// --------------------------------------------------------------
	// ---
	// --- API
	// ---
	// --------------------------------------------------------------
	public final String getUser() {
		return user;
	}

	public final String getBasisNummer() {
		return basisNummer;
	}

	public final Set<AbwicklungsartCode> getAbwicklungsArten() {
		return AlterableView.of(abwicklungsArten);
	}

	// --------------------------------------------------------------
	// ---
	// --- Streamable
	// ---
	// --------------------------------------------------------------
	@Override
	protected void readImpl(final StreamReader reader) throws IOException {
		super.readImpl(reader);
		user = reader.readString();
		basisNummer = reader.readString();
		StreamUtil.readCollection(reader, abwicklungsArten);
	}

	@Override
	protected void writeImpl(final StreamWriter writer) throws IOException {
		super.writeImpl(writer);
		writer.writeString(user);
		writer.writeString(basisNummer);
		StreamUtil.writeCollection(writer, abwicklungsArten);
	}

}

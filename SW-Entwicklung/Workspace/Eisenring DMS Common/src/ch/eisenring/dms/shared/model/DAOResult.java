package ch.eisenring.dms.shared.model;

import java.io.IOException;
import java.io.InputStream;

import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.io.StreamFormat;
import ch.eisenring.core.io.StreamReader;
import ch.eisenring.core.io.StreamWriter;
import ch.eisenring.core.io.Streamable;
import ch.eisenring.core.io.binary.BinaryHolder;
import ch.eisenring.core.io.binary.BinaryHolderUtil;
import ch.eisenring.core.io.memory.MemoryOutputStream;
import ch.eisenring.core.util.OperationResult;
import ch.eisenring.model.Model;
import ch.eisenring.model.TransactionContext;
import ch.eisenring.model.engine.stream.StreamTransactionSink;
import ch.eisenring.model.engine.stream.StreamTransactionSource;

public final class DAOResult implements Streamable {

	private BinaryHolder serializedContext;
	private ErrorMessage errorMessage;

	/**
	 * Creates a normal result object (success) 
	 */
	public DAOResult(final TransactionContext context) {
		setContext(context);
	}

	/**
	 * Creates an error result object from exception
	 */
	public DAOResult(final Throwable throwable) {
		this(throwable.getMessage());
	}

	/**
	 * Creates an error result object using message
	 */
	public DAOResult(final String message) {
		this(new ErrorMessage(Strings.concat("Fehler: ", message)));
	}

	/**
	 * Creates a result from ErrorMessage
	 */
	public DAOResult(final ErrorMessage errorMessage) {
		this.errorMessage = errorMessage;
	}

	/**
	 * Returns true if the result is from a successful operation
	 */
	public boolean isValid() {
		final ErrorMessage errorMessage = this.errorMessage;
		return errorMessage == null ? false : errorMessage.isSuccess();
	}

	/**
	 * Gets the error message
	 */
	public ErrorMessage getMessage() {
		return errorMessage;
	}

	/**
	 * Gets the context from this result
	 */
	public TransactionContext getContext() throws IOException {
		final BinaryHolder serializedContext = this.serializedContext;
		if (serializedContext == null || serializedContext.size() <= 0)
			return null;
		final TransactionContext context = new TransactionContext();
		final InputStream input = serializedContext.getInputStream();
		final StreamReader reader = StreamFormat.createReader(input);
		final StreamTransactionSource source = new StreamTransactionSource(reader);
		context.load(source);
		context.setUnchanged(false);
		return context;
	}
	
	/**
	 * Extracts the context and finds all objects of the given class.
	 */
	public <T extends Model> OperationResult<List<T>> getModels(final Class<T> modelClass) {
		if (!isValid()) {
			return new OperationResult<List<T>>(getMessage());
		} else {
			try {
				final TransactionContext context = getContext();
				final List<T> list = context.getModels(modelClass);
				return new OperationResult<List<T>>(getMessage(), (Throwable) null, list);
			} catch (final Throwable e) {
				return new OperationResult<List<T>>(new ErrorMessage(e));
			}
		}
	}
	
	protected void setContext(final TransactionContext context) {
		if (context == null) {
			serializedContext = null;
			errorMessage = new ErrorMessage("Fehler: Context == NULL");
			return;
		}
		try {
			final MemoryOutputStream output = new MemoryOutputStream();
			final StreamWriter writer = StreamFormat.SERIAL.createWriter(output);
			final StreamTransactionSink sink = new StreamTransactionSink(writer, true);
			context.store(sink);
			writer.close();
			serializedContext = BinaryHolderUtil.create(output, true);
			errorMessage = ErrorMessage.OK;
		} catch (final IOException e) {
			serializedContext = null;
			errorMessage = new ErrorMessage(Strings.concat("Fehler: ", e.getMessage()));
		}
	}
	
	public int getSizeHint() {
		return BinaryHolderUtil.size(serializedContext, 1 << 20) + 128;
	}

	// --------------------------------------------------------------
	// ---
	// --- Streamable implementation
	// ---
	// --------------------------------------------------------------
	DAOResult() {
		// default constructor for Streamable
	}

	@Override
	public void read(final StreamReader reader) throws IOException {
		errorMessage = (ErrorMessage) reader.readObject();
		serializedContext = reader.readBinaryHolder();
	}

	@Override
	public void write(final StreamWriter writer) throws IOException {
		writer.writeObject(errorMessage);
		writer.writeBinaryHolder(serializedContext);
	}

}

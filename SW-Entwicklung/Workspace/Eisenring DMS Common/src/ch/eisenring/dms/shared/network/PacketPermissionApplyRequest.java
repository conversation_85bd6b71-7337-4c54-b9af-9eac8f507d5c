package ch.eisenring.dms.shared.network;

import ch.eisenring.core.io.AutoStreamed;

public final class PacketPermissionApplyRequest extends AbstractDAORequestPacket {

	@AutoStreamed
	private Long permissionRowId;
	
	private PacketPermissionApplyRequest() {
	}

	public static PacketPermissionApplyRequest create(final Long objectId,
													  final String user,
													  final Long permissionRowId) {
		final PacketPermissionApplyRequest packet = new PacketPermissionApplyRequest();
		packet.addRowId(objectId);
		packet.setUser(user);
		packet.permissionRowId = permissionRowId;
		return packet;
	}

	public Long getPermissionRowId() {
		return permissionRowId;
	}

	// --------------------------------------------------------------
	// ---
	// --- Packet implementation
	// ---
	// --------------------------------------------------------------

	// no packet specific implementation

}

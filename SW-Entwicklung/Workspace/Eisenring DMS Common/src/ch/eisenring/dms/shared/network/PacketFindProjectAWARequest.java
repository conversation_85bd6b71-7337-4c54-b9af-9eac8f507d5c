package ch.eisenring.dms.shared.network;

import ch.eisenring.core.io.AutoStreamed;

/**
 * Requests a list of "AbwicklungArten" that exist for project number
 * (in Logiware ERP)
 */
public class PacketFindProjectAWARequest extends AbstractDMSPacket {

	@AutoStreamed
	private String projectnumber;

	private PacketFindProjectAWARequest() {
		super(CAPS_SEQUENCE_REQUEST, PRIORITY_REQUEST + 1);
	}

	// --------------------------------------------------------------
	// ---
	// --- Factory methods
	// ---
	// --------------------------------------------------------------
	public static PacketFindProjectAWARequest create(final String projectnumber) {
		final PacketFindProjectAWARequest packet = new PacketFindProjectAWARequest();
		packet.projectnumber = projectnumber;
		return packet;
	}

	// --------------------------------------------------------------
	// ---
	// --- Packet specific API
	// ---
	// --------------------------------------------------------------
	public String getProjectnumber() {
		return projectnumber;
	}

}

package ch.eisenring.dms.shared.model.meta;

import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.core.util.VarArgs;
import ch.eisenring.dms.service.codetables.DMSBinaryStatus;
import ch.eisenring.dms.shared.DMSConstants;
import ch.eisenring.dms.shared.codetables.DMSCompressionCode;
import ch.eisenring.dms.shared.model.pojo.DMSVersionPojo;
import ch.eisenring.model.MetaAttribute;
import ch.eisenring.model.MetaClass;
import ch.eisenring.model.MetaClassMember;
import ch.eisenring.model.MetaProperty;
import ch.eisenring.model.PrimaryKeyOrdinal;
import ch.eisenring.model.engine.jdbc.ColumnSpecifier;
import ch.eisenring.model.engine.jdbc.TableSpecifier;
import ch.eisenring.model.factory.AttributeFactory;
import ch.eisenring.model.mapping.POJOField;
import ch.eisenring.model.primarykey.LongPrimaryKey;

public interface DMSVersionMeta {

	TableSpecifier TABLE = TableSpecifier.get(DMSConstants.DMS_DATABASE, "DMSBinary");
	
	ColumnSpecifier PKCOL = ColumnSpecifier.get(TABLE, "rowId");

	MetaAttribute ATR_ROWID = AttributeFactory.pLong(
			"rowId", 0, 
			POJOField.get(DMSVersionPojo.class, "rowId"),
			PKCOL, PrimaryKeyOrdinal.O1);

	MetaAttribute ATR_OBJECT_ROWID = AttributeFactory.pLong(
			"Object_rowId", 0, 
			POJOField.get(DMSVersionPojo.class, "objectRowId"),
			ColumnSpecifier.get(TABLE, "Object_rowId"));

	MetaAttribute ATR_STATUSCODE = AttributeFactory.code(
			"Statuscode", DMSBinaryStatus.class, DMSBinaryStatus.ACTIVE,
			POJOField.get(DMSVersionPojo.class, "statusCode"),
			ColumnSpecifier.get(TABLE, "Statuscode"));

	MetaAttribute ATR_BYTESIZE = AttributeFactory.pLong(
			"Bytesize", 0, 0, Long.MAX_VALUE,
			POJOField.get(DMSVersionPojo.class, "byteSize"),
			ColumnSpecifier.get(TABLE, "Bytesize"));

	MetaAttribute ATR_RAWSIZE = AttributeFactory.pLong(
			"Rawsize", 0, 0, Long.MAX_VALUE,
			POJOField.get(DMSVersionPojo.class, "rawSize"),
			ColumnSpecifier.get(TABLE, "Rawsize"));

	MetaAttribute ATR_DCMCODE = AttributeFactory.code(
			"DCMCode", DMSCompressionCode.class, DMSCompressionCode.NONE0,
			POJOField.get(DMSVersionPojo.class, "dcmCode"),
			ColumnSpecifier.get(TABLE, "DCMCode"));
	
	MetaAttribute ATR_MADEON = AttributeFactory.datetime(
			"MadeOn", TimestampUtil.NULL_TIMESTAMP,
			POJOField.get(DMSVersionPojo.class, "madeOn"),
			ColumnSpecifier.get(TABLE, "MadeOn"));

	MetaAttribute ATR_BINARYHASH = AttributeFactory.string(
			"Binaryhash", null, 80,
			POJOField.get(DMSVersionPojo.class, "binaryHash"),
			ColumnSpecifier.get(TABLE, "Binaryhash"));

	MetaAttribute ATR_FILEEXTENSION = AttributeFactory.string(
			"FileExtension", null, 16,
			POJOField.get(DMSVersionPojo.class, "fileExtension"),
			ColumnSpecifier.get(TABLE, "FileExtension"));

	MetaClass METACLASS = new MetaClass(
			"DMSVersion",
			LongPrimaryKey.class,
			// Attributes
			new VarArgs<MetaProperty>(MetaProperty.class).flat(
			TABLE, PKCOL,
			MetaClassMember.discoverMetaClassMembers(DMSVersionMeta.class)
			));

}

package ch.eisenring.dms.shared.network;

import ch.eisenring.core.io.binary.BinaryHolder;

public final class PacketCreateVersionRequest extends PacketCreateObjectRequest {

	private PacketCreateVersionRequest() {
		super(CAPS_SEQUENCE_REQUEST, PRIORITY_REQUEST + 1);
	}
	
	public static PacketCreateVersionRequest create(final Long objectRowId,
												    final String user,
												    final String objectname,
												    final BinaryHolder binary) {
		final PacketCreateVersionRequest packet = new PacketCreateVersionRequest();
		packet.binary = binary; // we assume the thing won't be disposed
		packet.addRowId(objectRowId);
		packet.setUser(user);
		packet.objectname = objectname;
		return packet;

	}

	@Override
	public boolean isCreateRequest() {
		return false;
	}

}

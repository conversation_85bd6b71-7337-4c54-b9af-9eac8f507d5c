package ch.eisenring.dms.shared.formular;

import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.print.shared.resource.ReportResource;

public class FormularLeistungsbewertungSource extends FormularReportSource {

	public FormularLeistungsbewertungSource(final ReportResource reportResource) {
		super(reportResource);
		final List<Object> list = new ArrayList<>(1);
		list.add(new Object());
		setModelList(list);
	}

	@Override
	public String getBarcode() {
		final StringMaker barcode = StringMaker.obtain();
		barcode.append(getFormularCode().getBarcodePrefix());
		barcode.append(getParam("Projectnumber"));
		return barcode.release(); 
	}

	@Override
	public DMSFormularCode getFormularCode() {
		return DMSFormularCode.LEISTUNGSBEWERTUNG;
	}

	// --------------------------------------------------------------
	// ---
	// --- Setters / Getters
	// ---
	// --------------------------------------------------------------
	public void setProjektnummer(final String projektnummer) {
		putParam("Projectnumber", projektnummer);
	}

	public void setObjektbezeichnung(final String objektbezeichnung) {
		putParam("Objectname", objektbezeichnung);
	}

	public void setAddress(final String address) {
		putParam("Address", address);
	}

	public void setVerkaeufer(final String verkaeufer) {
		putParam("Verkaeufer", verkaeufer);
	}

	public void setKundenberater(final String kundenberater) {
		putParam("Kundenberater", kundenberater);
	}

	public void setObjektbetreuer(final String objektbetreuer) {
		putParam("Objektbetreuer", objektbetreuer);
	}

}

package ch.eisenring.dms.shared.model.record;

import static ch.eisenring.dms.shared.model.metaold.DMSSearchMetaModel.ATR_SEARCHWORDS;
import ch.eisenring.dms.shared.model.metaold.DMSSearchMetaModel;
import ch.eisenring.model.MetaClass;
import ch.eisenring.model.PrimaryKey;
import ch.eisenring.model.TransactionContext;

/**
 * Inherits everything from DMSObject and adds the
 * search index field.
 */
public final class DMSSearch extends DMSObjectModel {

	public final static MetaClass METACLASS = DMSSearchMetaModel.METACLASS;
	
	/**
	 * Constructor is called via reflection
	 */
	protected DMSSearch(final TransactionContext context, final PrimaryKey pk) {
		super(context, pk);
	}

	// --------------------------------------------------------------
	// ---
	// --- Derived <PERSON><PERSON> & Setters
	// ---
	// --------------------------------------------------------------
	
	// none yet
	
	// --------------------------------------------------------------
	// ---
	// --- Attribute Getters & Setters
	// ---
	// --------------------------------------------------------------
	public String getSearchwords() {
		return (String) ATR_SEARCHWORDS.getMember(this);
	}
	
	public void setSearchwords(final String searchwords) {
		ATR_SEARCHWORDS.setMember(this, searchwords);
	}

}

package ch.eisenring.dms.shared.network;

import ch.eisenring.dms.shared.model.data.PermissionDescriptor;

/**
 * Packet sent to create new permission descriptor(s)
 */
public final class PacketPermissionInsertRequest extends PacketPermissionBase {

	private PacketPermissionInsertRequest() {
	}

	public static PacketPermissionInsertRequest create(final String user,
													   final PermissionDescriptor ... descriptors) {
		final PacketPermissionInsertRequest packet = new PacketPermissionInsertRequest();
		packet.setUser(user);
		packet.addPermissions(descriptors);
		return packet;
	}

}

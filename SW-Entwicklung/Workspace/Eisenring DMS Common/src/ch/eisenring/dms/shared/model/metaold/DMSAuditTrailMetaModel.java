package ch.eisenring.dms.shared.model.metaold;

import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.core.util.VarArgs;
import ch.eisenring.dms.shared.DMSConstants;
import ch.eisenring.dms.shared.codetables.DMSAuditCode;
import ch.eisenring.dms.shared.model.record.DMSAuditTrailModel;
import ch.eisenring.model.MetaAttribute;
import ch.eisenring.model.MetaClass;
import ch.eisenring.model.MetaClassMember;
import ch.eisenring.model.MetaProperty;
import ch.eisenring.model.engine.jdbc.ColumnSpecifier;
import ch.eisenring.model.engine.jdbc.TableSpecifier;
import ch.eisenring.model.factory.AttributeFactory;
import ch.eisenring.model.primarykey.LongPrimaryKey;

public interface DMSAuditTrailMetaModel {

	TableSpecifier TABLE = TableSpecifier.get(DMSConstants.DMS_DATABASE, "DMSAuditTrail");
	
	MetaAttribute ATR_OBJECT_ROWID = AttributeFactory.wLong(
			"Object_rowId", null, Long.MIN_VALUE, Long.MAX_VALUE,
			ColumnSpecifier.get(TABLE, "Object_rowId"));

	MetaAttribute ATR_BINARY_ROWID = AttributeFactory.wLong(
			"Binary_rowId", null, Long.MIN_VALUE, Long.MAX_VALUE,
			ColumnSpecifier.get(TABLE, "Binary_rowId"));
	
	MetaAttribute ATR_AUDITCODE = AttributeFactory.code(
			"Auditcode", DMSAuditCode.class, null,
			ColumnSpecifier.get(TABLE, "Auditcode"));
	
	MetaAttribute ATR_MADEON = AttributeFactory.datetime(
			"MadeOn", TimestampUtil.NULL_TIMESTAMP,
			ColumnSpecifier.get(TABLE, "MadeOn"));
	
	MetaAttribute ATR_MADEBY = AttributeFactory.cleanString(
			"MadeBy", null, 60, 
			ColumnSpecifier.get(TABLE, "MadeBy"));

	MetaAttribute ATR_VALUE_OLD = AttributeFactory.string(
			"ValueOld", null, 250,
			ColumnSpecifier.get(TABLE, "ValueOld"));

	MetaAttribute ATR_VALUE_NEW = AttributeFactory.string(
			"ValueNew", null, 250,
			ColumnSpecifier.get(TABLE, "ValueNew"));

	MetaClass METACLASS = new MetaClass(
			"DMSAuditTrail", DMSAuditTrailModel.class,
			LongPrimaryKey.class,
			null,
			// Attributes
			new VarArgs<MetaProperty>(MetaProperty.class).flat(
			ColumnSpecifier.get(TABLE, "rowId"),
			TABLE,
			MetaClassMember.discoverMetaClassMembers(DMSAuditTrailMetaModel.class)
			));

}

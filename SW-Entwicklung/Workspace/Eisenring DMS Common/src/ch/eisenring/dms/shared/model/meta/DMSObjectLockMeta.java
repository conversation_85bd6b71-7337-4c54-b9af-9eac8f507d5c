package ch.eisenring.dms.shared.model.meta;

import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.core.util.VarArgs;
import ch.eisenring.dms.shared.DMSConstants;
import ch.eisenring.dms.shared.locking.DMSObjectLock;
import ch.eisenring.model.MetaAttribute;
import ch.eisenring.model.MetaClass;
import ch.eisenring.model.MetaClassMember;
import ch.eisenring.model.MetaProperty;
import ch.eisenring.model.PrimaryKeyOrdinal;
import ch.eisenring.model.engine.jdbc.ColumnSpecifier;
import ch.eisenring.model.engine.jdbc.TableSpecifier;
import ch.eisenring.model.factory.AttributeFactory;
import ch.eisenring.model.mapping.POJOField;
import ch.eisenring.model.primarykey.LongPrimaryKey;

public interface DMSObjectLockMeta {

	TableSpecifier TABLE = TableSpecifier.get(DMSConstants.DMS_DATABASE, "DMSObjectLock");
	Class<DMSObjectLock> POJOCLASS = DMSObjectLock.class;
	ColumnSpecifier PKCOL = ColumnSpecifier.get(TABLE, "Object_rowId");

	MetaAttribute ATR_OBJECTROWID = AttributeFactory.pLong(
			PKCOL.getColumnName(), LongPrimaryKey.AUTO_ASSIGN,
			POJOField.get(POJOCLASS, "objectRowId"),
			PKCOL, PrimaryKeyOrdinal.O1);

	MetaAttribute ATR_TIMESTAMP = AttributeFactory.datetime(
			"Timestamp", TimestampUtil.NULL_TIMESTAMP,
			POJOField.get(POJOCLASS, "timestamp"),
			ColumnSpecifier.get(TABLE, "LockTime"));

	MetaAttribute ATR_USER = AttributeFactory.cleanString(
			"User", null, 60,
			POJOField.get(POJOCLASS, "user"),
			ColumnSpecifier.get(TABLE, "LockUser"));

	MetaAttribute ATR_HOST = AttributeFactory.cleanString(
			"Host", null, 256,
			POJOField.get(POJOCLASS, "host"),
			ColumnSpecifier.get(TABLE, "LockHost"));

	MetaAttribute ATR_Path = AttributeFactory.cleanString(
			"Path", null, 1024,
			POJOField.get(POJOCLASS, "path"),
			ColumnSpecifier.get(TABLE, "LockPath"));

	MetaClass METACLASS = new MetaClass(
			"DMSObjectLock", LongPrimaryKey.class,
			// Attributes
			new VarArgs<MetaProperty>(MetaProperty.class).flat(
			TABLE, PKCOL,
			MetaClassMember.discoverMetaClassMembers(DMSObjectLockMeta.class)
			));

}

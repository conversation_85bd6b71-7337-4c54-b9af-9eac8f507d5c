package ch.eisenring.dms.shared.network;

import java.io.IOException;

import ch.eisenring.core.io.StreamReader;
import ch.eisenring.core.io.StreamWriter;
import ch.eisenring.core.io.binary.BinaryHolder;
import ch.eisenring.dms.shared.codetables.DMSCompressionPolicy;

abstract class PacketCreateObjectRequest extends AbstractDAORequestPacket {

	protected DMSCompressionPolicy compressionPolicy = DMSCompressionPolicy.DEFAULT;
	protected String objectname;
	protected BinaryHolder binary;

	protected PacketCreateObjectRequest(final int caps, final int priority) {
		super(caps, priority);
	}

	// --------------------------------------------------------------
	// ---
	// --- API
	// ---
	// --------------------------------------------------------------
	public final DMSCompressionPolicy getCompressionPolicy() {
		return compressionPolicy;
	}

	public final void setCompressionPolicy(final DMSCompressionPolicy compressionPolicy) {
		this.compressionPolicy = compressionPolicy == null
				? DMSCompressionPolicy.DEFAULT : compressionPolicy;
	}

	public final String getObjectname() {
		return objectname;
	}

	public final BinaryHolder getBinary() {
		return binary;
	}

	// --------------------------------------------------------------
	// ---
	// --- Streamable
	// ---
	// --------------------------------------------------------------
	@Override
	protected void readImpl(final StreamReader reader) throws IOException {
		super.readImpl(reader);
		objectname = reader.readString();
		compressionPolicy = reader.readCode(DMSCompressionPolicy.class);
		binary = reader.readBinaryHolder();
	}

	@Override
	protected void writeImpl(final StreamWriter writer) throws IOException {
		super.writeImpl(writer);
		writer.writeString(objectname);
		writer.writeCode(compressionPolicy);
		writer.writeBinaryHolder(binary);
	}

}

package ch.eisenring.dms.shared.network;

import ch.eisenring.core.codetable.ErrorMessage;

/**
 * Packet sent in reply to PermissionInsertRequest
 */
public final class PacketPermissionUpdateReply extends PacketPermissionBase {

	private PacketPermissionUpdateReply() {
	}

	public static PacketPermissionUpdateReply create(final PacketPermissionUpdateRequest request,
			                                         final ErrorMessage message) {
		final PacketPermissionUpdateReply packet = new PacketPermissionUpdateReply();
		packet.setReplyId(request, message);
		packet.setUser(request.getUser());
		return packet;
	}
			                                  
}

package ch.eisenring.dms.shared.formular;

import java.awt.Point;

import ch.eisenring.core.barcode.Code128;
import ch.eisenring.core.collections.Map;
import ch.eisenring.core.collections.impl.HashMap;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.print.shared.model.AbstractReportSource;
import ch.eisenring.print.shared.resource.ReportResource;

public abstract class FormularReportSource extends AbstractReportSource {

	private final ReportResource reportResource;
	private final Map<String, String> parameterMap = new HashMap<>();
	
	protected FormularReportSource(final ReportResource reportResource) {
		this.reportResource = reportResource;
	}

	protected void putParam(final String key, final String value) {
		final String putVal = value == null ? "" : value;
		parameterMap.put(key, putVal);
	}

	protected String getParam(final String key) {
		final String result = parameterMap.get(key);
		return result == null ? "" : result;
	}

	public final ReportResource getReportResource() {
		return reportResource;
	}

	@Override
	public void populateParameters(final Map<String, Object> map) {
		super.populateParameters(map);
		map.putAll(parameterMap);
		final String barcode = getBarcode();
		if (Strings.isEmpty(barcode)) {
			map.put("Barcode", "");
			map.put("Clearcode", "");
		} else {
			map.put("Barcode", Code128.getInstance().encode(barcode));
			map.put("Clearcode", barcode);
		}
	}

	public abstract DMSFormularCode getFormularCode();

	public abstract String getBarcode();

	/**
	 * Dummy for report sources that can be mapped to a spreadsheet
	 */
	public Map<Point, String> getTableSheetMapping() {
		return new HashMap<>();
	}

}

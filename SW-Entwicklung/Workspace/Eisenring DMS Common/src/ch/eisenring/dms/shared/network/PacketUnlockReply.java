package ch.eisenring.dms.shared.network;

import ch.eisenring.core.codetable.ErrorMessage;

/**
 * Packet sent in response to unlocking an object
 */
public final class PacketUnlockReply extends AbstractDMSPacket {

	public static PacketUnlockReply create(final PacketUnlockRequest request,
			                               final ErrorMessage message) {
		final PacketUnlockReply packet = new PacketUnlockReply();
		packet.setReplyId(request, message);
		return packet;
	}

	PacketUnlockReply() {
		super(CAPS_SEQUENCE_REPLY | CAPS_ERRORMESSAGE, PRIORITY_REPLY);
	}

}

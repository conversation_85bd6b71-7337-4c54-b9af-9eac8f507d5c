package ch.eisenring.dms.shared.cache.object;

import java.io.IOException;

import ch.eisenring.core.io.StreamReader;
import ch.eisenring.core.io.StreamWriter;
import ch.eisenring.dms.service.StringCaches;
import ch.eisenring.dms.service.api.DMSObject;
import ch.eisenring.dms.service.codetables.DMSObjectType;

public final class DMSObjectDataDocument extends DMSObjectData implements Cloneable {

	/**
	 * Size of the current version in bytes
	 */
	private long bytesize;

	/**
	 * Cached file extension
	 */
	private String fileExtension;
	
	@Override
	public DMSObjectType getType() {
		return DMSObjectType.FILE;
	}

	@Override
	public Long getPermissionRowId() {
		// documents never have a permission
		return null;
	}

	@Override
	public String getFileExtension() {
		return fileExtension;
	}

	@Override
	public long getBytesize() {
		return bytesize;
	}

	@Override
	public String getParticipants() {
		// documents have no participants
		return null;
	}

	@Override
	public void setChildren(final DMSObject[] children) {
		// ignore, documents have no children
	}

	@Override
	public DMSObject[] getChildren() {
		// documents have no children
		return DMSObject.EMPTY_ARRAY;
	}

	@Override
	public void update(final DMSObject object) {
		super.update(object);
		this.bytesize = object.getBytesize();
		this.fileExtension = object.getFileExtension();
	}

	// --------------------------------------------------------------
	// ---
	// --- Streamable implementation
	// ---
	// --------------------------------------------------------------
	public DMSObjectDataDocument() {
		// default constructor for Streamable
	}

	@Override
	public void read(final StreamReader reader) throws IOException {
		super.read(reader);
		this.fileExtension = reader.readString(StringCaches.FILEEXT);
		this.bytesize = reader.readLong();
	}
	
	@Override
	public void write(final StreamWriter writer) throws IOException {
		super.write(writer);
		writer.writeString(fileExtension, StringCaches.FILEEXT);
		writer.writeLong(bytesize);
	}

	// --------------------------------------------------------------
	// ---
	// --- Object overrides
	// ---
	// --------------------------------------------------------------
	@Override
	public DMSObjectDataDocument clone() throws CloneNotSupportedException {
		final DMSObjectDataDocument result = (DMSObjectDataDocument) super.clone();
		// no fields that need to be invalidated for now
		return result;
	}

}

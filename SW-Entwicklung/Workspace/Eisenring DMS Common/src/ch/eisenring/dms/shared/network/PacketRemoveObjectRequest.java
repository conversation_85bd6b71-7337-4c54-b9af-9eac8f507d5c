package ch.eisenring.dms.shared.network;

public final class PacketRemoveObjectRequest extends AbstractDAORequestPacket {

	private PacketRemoveObjectRequest() {
	}

	public static PacketRemoveObjectRequest create(final long objectRowId, final String user) {
		final PacketRemoveObjectRequest packet = new PacketRemoveObjectRequest();
		packet.setUser(user);
		packet.addRowId(objectRowId);
		return packet;
	}

	// --------------------------------------------------------------
	// ---
	// --- Packet implementation
	// ---
	// --------------------------------------------------------------

	// no packet specific implementation
	
}

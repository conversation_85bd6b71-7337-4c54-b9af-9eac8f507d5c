package ch.eisenring.dms.shared.network;

import ch.eisenring.core.io.StreamReader;
import ch.eisenring.core.io.StreamWriter;

import java.io.IOException;

/**
 * Packet sent to request analysis of Logiware/DMS project structure.
 */
public final class PacketProjectStructureQueryRequest extends AbstractDMSPacket {

	private String projektnummer;
	
	private PacketProjectStructureQueryRequest() {
		super(CAPS_SEQUENCE_REQUEST, PRIORITY_REQUEST);
	}

	public static PacketProjectStructureQueryRequest create(final String projektnummer) {
		final PacketProjectStructureQueryRequest packet = new PacketProjectStructureQueryRequest();
		packet.projektnummer = projektnummer;
		return packet;
	}

	// --------------------------------------------------------------
	// ---
	// --- Packet API
	// ---
	// --------------------------------------------------------------
	/**
	 * Projektnummer to be analyzed
	 */
	public String getProjektnummer() {
		return projektnummer;
	}

	// --------------------------------------------------------------
	// ---
	// --- Streamable
	// ---
	// --------------------------------------------------------------
	@Override
	protected void readImpl(final StreamReader reader) throws IOException {
		super.readImpl(reader);
		projektnummer = reader.readString();
	}

	@Override
	protected void writeImpl(final StreamWriter writer) throws IOException {
		super.writeImpl(writer);
		writer.writeString(projektnummer);
	}

}

package ch.eisenring.dms.shared.network;

import ch.eisenring.core.io.AutoStreamed;

public final class PacketSetParticipantsRequest extends AbstractDAORequestPacket {

	@AutoStreamed
	private String participants;
	
	private PacketSetParticipantsRequest() {
	}

	public static PacketSetParticipantsRequest create(final Long objectId,
													  final String user,
													  final String participants) {
		final PacketSetParticipantsRequest packet = new PacketSetParticipantsRequest();
		packet.addRowId(objectId);
		packet.setUser(user);
		packet.participants = participants;
		return packet;
	}

	public String getParticipants() {
		return participants;
	}

	// --------------------------------------------------------------
	// ---
	// --- Packet implementation
	// ---
	// --------------------------------------------------------------

	// no packet specific implementation

}

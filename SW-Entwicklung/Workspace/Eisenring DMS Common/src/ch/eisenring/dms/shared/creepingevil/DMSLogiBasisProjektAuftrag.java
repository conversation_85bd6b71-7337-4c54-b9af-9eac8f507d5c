package ch.eisenring.dms.shared.creepingevil;

import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.io.AutoStreamable;
import ch.eisenring.core.io.AutoStreamed;

public class DMSLogiBasisProjektAuftrag implements AutoStreamable {

	@AutoStreamed
	public String projektnummer;
	
	@AutoStreamed
	public String bauherr;
	
	@AutoStreamed
	public String baufuehrerName;
	
	@AutoStreamed
	public String baufuehrerEMail;

	@AutoStreamed
	public String baufuehrerPhone;

	public String getObjektbezeichnung() {
		return Strings.toSingleLine(bauherr);
	}

}

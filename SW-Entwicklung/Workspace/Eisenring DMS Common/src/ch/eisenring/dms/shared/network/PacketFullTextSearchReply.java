package ch.eisenring.dms.shared.network;

import ch.eisenring.dms.shared.model.DAOResult;

public final class PacketFullTextSearchReply extends AbstractDAOReplyPacket {

	private PacketFullTextSearchReply() {
	}

	public static PacketFullTextSearchReply create(final PacketFullTextSearchRequest request,
			                                       final DAOResult result) {
		final PacketFullTextSearchReply packet = new PacketFullTextSearchReply();
		packet.setResult(request, result);
		return packet;
	}

}

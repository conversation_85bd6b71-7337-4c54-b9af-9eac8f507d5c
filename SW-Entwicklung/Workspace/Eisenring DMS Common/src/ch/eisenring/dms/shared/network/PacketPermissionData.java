package ch.eisenring.dms.shared.network;

import java.io.IOException;

import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.alterableview.AlterableView;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.io.StreamReader;
import ch.eisenring.core.io.StreamUtil;
import ch.eisenring.core.io.StreamWriter;
import ch.eisenring.dms.shared.model.data.PermissionDescriptor;

/**
 * Base class for packets that carry permission data.
 * 
 * Packets of this type transport any number of permission descriptors
 * (that includes zero).
 */
public abstract class PacketPermissionData extends AbstractDMSPacket {

	private final List<PermissionDescriptor> permissionDescriptors = new ArrayList<>();
	private final List<Long> deletedDescriptors = new ArrayList<>();
	
	protected PacketPermissionData(final int caps, final int priority) {
		super(caps, priority);
	}

	public List<PermissionDescriptor> getPermissions() {
		return AlterableView.of(permissionDescriptors);
	}

	public List<Long> getDeletions() {
		return AlterableView.of(deletedDescriptors);
	}

	// --------------------------------------------------------------
	// ---
	// --- Streamable
	// ---
	// --------------------------------------------------------------
	@Override
	protected void readImpl(final StreamReader reader) throws IOException {
		super.readImpl(reader);
		StreamUtil.readCollection(reader, permissionDescriptors);
		StreamUtil.readCollection(reader, deletedDescriptors);
	}

	@Override
	protected void writeImpl(final StreamWriter writer) throws IOException {
		super.writeImpl(writer);
		StreamUtil.writeCollection(writer, permissionDescriptors);
		StreamUtil.writeCollection(writer, deletedDescriptors);
	}

}

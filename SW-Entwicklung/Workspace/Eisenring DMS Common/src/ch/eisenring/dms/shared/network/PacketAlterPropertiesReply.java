package ch.eisenring.dms.shared.network;

import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.dms.shared.network.cmd.ObjectMessageSet;

public final class PacketAlterPropertiesReply extends PacketObjectMessagesBase {

	private PacketAlterPropertiesReply() {
		super(CAPS_SEQUENCE_REPLY | CAPS_ERRORMESSAGE, PRIORITY_REPLY);
	}
	
	public static PacketAlterPropertiesReply create(
			final PacketAlterPropertiesRequest request,
			final ObjectMessageSet changeMessages) {
		final PacketAlterPropertiesReply packet = create(request, ErrorMessage.OK);
		packet.messages = changeMessages;
		return packet;
	}

	public static PacketAlterPropertiesReply create(
			final PacketAlterPropertiesRequest request,
			final ErrorMessage error) {
		final PacketAlterPropertiesReply packet = new PacketAlterPropertiesReply();
		packet.setReplyId(request, error);
		return packet;
	}
	
}

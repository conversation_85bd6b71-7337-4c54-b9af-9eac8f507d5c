package ch.eisenring.dms.shared.network;

import java.io.IOException;
import java.util.Comparator;

import ch.eisenring.core.collections.Collection;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.Set;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.collections.impl.HashSet;
import ch.eisenring.core.io.StreamReader;
import ch.eisenring.core.io.StreamUtil;
import ch.eisenring.core.io.StreamWriter;

public abstract class AbstractDAORequestPacket extends AbstractDMSPacket {

	public final static Comparator<AbstractDAORequestPacket> REQUEST_ORDERING =
		new Comparator<AbstractDAORequestPacket>() {
			public int compare(final AbstractDAORequestPacket r1, final AbstractDAORequestPacket r2) {
				final boolean b1 = r1.isCreateRequest();
				final boolean b2 = r2.isCreateRequest();
				if (b1 && !b2)
					return 1;
				if (!b1 && b2)
					return -1;
				final int p1 = r1.getPacketPriority();
				final int p2 = r2.getPacketPriority();
				return p1<p2 ? -1 : (p1==p2 ? 0 : 1);				
			}
		};

	protected final Set<Long> rowIdList = new HashSet<>();
	protected String user;
	
	AbstractDAORequestPacket(final int caps, final int priority) {
		super(caps, priority);
	}

	protected AbstractDAORequestPacket() {
		super(CAPS_SEQUENCE_REQUEST, PRIORITY_REQUEST);
	}

	protected AbstractDAORequestPacket(final Long rowId, final String user) {
		this();
		if (rowId != null)
			rowIdList.add(rowId);
		this.user = user;
	}
	
	public final Long getRowId() {
		return rowIdList.isEmpty() ? null : rowIdList.iterator().next();
	}

	public final void addRowId(final Long rowId) {
		if (rowId != null && !rowIdList.contains(rowId))
			rowIdList.add(rowId);
	}

	public final void addRowIds(final Collection<Long> rowIds) {
		if (rowIds != null) {
			for (final Long rowId : rowIds) {
				addRowId(rowId);
			}
		}
	}

	public final void removeRowId(final Long rowId) {
		rowIdList.remove(rowId);
	}

	public final int getRowIdCount() {
		return rowIdList.size();
	}

	public final List<Long> getRowIdList() {
		return new ArrayList<>(rowIdList);
	}

	public final String getUser() {
		return user;
	}

	protected final void setUser(final String user) {
		this.user = user;
	}

	/**
	 * Returns if this is a request to create a new object.
	 * Default implementation is return false.
	 */
	public boolean isCreateRequest() {
		return false;
	}

	// --------------------------------------------------------------
	// ---
	// --- Streamable implementation
	// ---
	// --------------------------------------------------------------
	@Override
	protected void readImpl(final StreamReader reader) throws IOException {
		super.readImpl(reader);
		user = reader.readString();
		StreamUtil.readCollection(reader, rowIdList);
	}

	@Override
	protected void writeImpl(final StreamWriter writer) throws IOException {
		super.writeImpl(writer);
		writer.writeString(user);
		StreamUtil.writeCollection(writer, rowIdList);
	}

}

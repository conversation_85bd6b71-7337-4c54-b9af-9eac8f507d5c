package ch.eisenring.dms.shared.network;

import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.io.AutoStreamed;
import ch.eisenring.dms.shared.model.record.DMSBlob;

/**
 * Returns a blob
 */
public final class PacketBlobLoadReply extends AbstractDMSPacket {

	@AutoStreamed
	private DMSBlob blob;

	private PacketBlobLoadReply() {
		super(CAPS_SEQUENCE_REPLY | CAPS_ERRORMESSAGE, PRIORITY_REPLY);
	}

	// --------------------------------------------------------------
	// ---
	// --- Factory methods
	// ---
	// --------------------------------------------------------------
	public static PacketBlobLoadReply create(final PacketBlobLoadRequest request, final ErrorMessage error) {
		final PacketBlobLoadReply packet = new PacketBlobLoadReply();
		packet.setReplyId(request, error);
		return packet;
	}

	public static PacketBlobLoadReply create(final PacketBlobLoadRequest request, final DMSBlob blob) {
		final PacketBlobLoadReply packet = create(request, ErrorMessage.OK);
		packet.blob = blob;
		return packet;
	}

	// --------------------------------------------------------------
	// ---
	// --- Packet specific API
	// ---
	// --------------------------------------------------------------
	public DMSBlob getBlob() {
		return blob;
	}

}

package ch.eisenring.dms.shared.network.brq;

import java.io.IOException;

import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.io.StreamReader;
import ch.eisenring.core.io.StreamWriter;
import ch.eisenring.dms.shared.cache.binary.DMSBinaryCache;
import ch.eisenring.dms.shared.model.api.DMSBinary;

public final class BinaryReplyError extends BinaryReply {

	/**
	 * RowId that was originally requested (object or binary)
	 */
	private long rowId;

	/**
	 * The error message
	 */
	private ErrorMessage error;

	public BinaryReplyError(final BinaryRequest request, final ErrorMessage error) {
		this.rowId = request.rowId;
		this.error = error;
	}

	@Override
	public boolean isReplyFor(final BinaryRequest request) {
		return this.rowId == request.rowId;
	}

	@Override
	public ErrorMessage getError() {
		return error;
	}

	@Override
	public DMSBinary getBinary(final DMSBinaryCache cache) throws UnsupportedOperationException {
		throw new UnsupportedOperationException("Invalid operation");
	}

	// --------------------------------------------------------------
	// ---
	// --- Streamable implementation
	// ---
	// --------------------------------------------------------------
	protected BinaryReplyError() {
		// constructor for reflection
	}

	@Override
	public void read(final StreamReader reader) throws IOException {
		rowId = reader.readLong();
		error = (ErrorMessage) reader.readObject();
	}

	@Override
	public void write(final StreamWriter writer) throws IOException {
		writer.writeLong(rowId);
		writer.writeObject(error);
	}
	
}

package ch.eisenring.dms.shared.network;

import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.io.AutoStreamed;

public class PacketReplicateTemplateReply extends AbstractDMSPacket {

	@AutoStreamed
	private Long createdFolderRowId;
	
	private PacketReplicateTemplateReply() {
		super(CAPS_SEQUENCE_REPLY | CAPS_ERRORMESSAGE, PRIORITY_REPLY);
	}

	// --------------------------------------------------------------
	// ---
	// --- Factory methods
	// ---
	// --------------------------------------------------------------
	public static PacketReplicateTemplateReply create(
			final PacketReplicateTemplateRequest request,
			final Long createdFolderRowId,
			final ErrorMessage message) {
		final PacketReplicateTemplateReply packet = new PacketReplicateTemplateReply();
		packet.setReplyId(request, message);
		packet.createdFolderRowId = createdFolderRowId;
		return packet;
	}

	// --------------------------------------------------------------
	// ---
	// --- Packet specific API
	// ---
	// --------------------------------------------------------------
	public Long getCreatedFolderRowId() {
		return createdFolderRowId;
	}

}

package ch.eisenring.dms.shared.network;

import ch.eisenring.logiware.code.pseudo.AbwicklungsartCode;

public final class PacketCreateProjectSubFoldersRequest extends PacketCreateProjectSubFoldersBase {

	private PacketCreateProjectSubFoldersRequest() {
		super(CAPS_SEQUENCE_REQUEST, PRIORITY_REQUEST);
	}

	// --------------------------------------------------------------
	// ---
	// --- Factory methods
	// ---
	// --------------------------------------------------------------
	public static PacketCreateProjectSubFoldersRequest create(
			final String basisNummer,
			final String user,
			final AbwicklungsartCode ... abwicklungsArten) {
		final PacketCreateProjectSubFoldersRequest packet = new PacketCreateProjectSubFoldersRequest();
		packet.user = user;
		packet.basisNummer = basisNummer;
		for (final AbwicklungsartCode awa : abwicklungsArten) {
			packet.abwicklungsArten.add(awa);
		}
		return packet;
	}

	// --------------------------------------------------------------
	// ---
	// --- API
	// ---
	// --------------------------------------------------------------

	// none

	// --------------------------------------------------------------
	// ---
	// --- Streamable
	// ---
	// --------------------------------------------------------------

	// none

}

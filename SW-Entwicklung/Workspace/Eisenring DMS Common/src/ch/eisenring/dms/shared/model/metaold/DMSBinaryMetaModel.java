package ch.eisenring.dms.shared.model.metaold;

import ch.eisenring.core.util.VarArgs;
import ch.eisenring.dms.shared.model.meta.DMSBinaryMeta;
import ch.eisenring.dms.shared.model.record.DMSBinaryModel;
import ch.eisenring.model.MetaAttribute;
import ch.eisenring.model.MetaClass;
import ch.eisenring.model.MetaClassMember;
import ch.eisenring.model.MetaProperty;
import ch.eisenring.model.engine.jdbc.ColumnSpecifier;
import ch.eisenring.model.factory.AttributeFactory;
import ch.eisenring.model.primarykey.LongPrimaryKey;

public interface DMSBinaryMetaModel extends DMSVersionMetaModel {

	MetaAttribute ATR_PLAINTEXT = AttributeFactory.clone(DMSBinaryMeta.ATR_PLAINTEXT);
	MetaAttribute ATR_BINARYDATA = AttributeFactory.clone(DMSBinaryMeta.ATR_BINARYDATA);
	MetaAttribute ATR_PLAINVERSION = AttributeFactory.clone(DMSBinaryMeta.ATR_PLAINVERSION);

	MetaClass METACLASS = new MetaClass(
			"DMSBinary", DMSBinaryModel.class,
			LongPrimaryKey.class,
			DMSVersionMetaModel.METACLASS,
			// Attributes
			new VarArgs<MetaProperty>(MetaProperty.class).flat(
			ColumnSpecifier.get(TABLE, "rowId"),
			TABLE,
			MetaClassMember.discoverMetaClassMembers(DMSBinaryMetaModel.class)
			));

}

package ch.eisenring.dms.shared.network;

import java.util.Collection;

import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.alterableview.AlterableView;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.io.AutoStreamed;
import ch.eisenring.dms.service.codetables.DMSDocumentType;

public final class PacketFindDocumentByTypeReply extends AbstractDMSPacket {

	@AutoStreamed
	private long folderRowId;
	
	@AutoStreamed
	private DMSDocumentType documentType;
	
	@AutoStreamed
	private final List<Long> documentRowIds = new ArrayList<>();

	private PacketFindDocumentByTypeReply() {
		super(CAPS_SEQUENCE_REPLY | CAPS_ERRORMESSAGE, PRIORITY_REPLY);
	}

	// --------------------------------------------------------------
	// ---
	// --- Factory methods
	// ---
	// --------------------------------------------------------------
	public static PacketFindDocumentByTypeReply create(final PacketFindDocumentByTypeRequest request, final ErrorMessage error) {
		final PacketFindDocumentByTypeReply packet = new PacketFindDocumentByTypeReply();
		packet.setReplyId(request, error);
		packet.folderRowId = request.getFolderRowId();
		packet.documentType = request.getDocumentType();
		return packet;
	}

	public static PacketFindDocumentByTypeReply create(final PacketFindDocumentByTypeRequest request, final Collection<Long> documentRowIds) {
		final PacketFindDocumentByTypeReply packet = create(request, ErrorMessage.OK);
		packet.documentRowIds.addAll(documentRowIds);
		return packet;
	}
	
	// --------------------------------------------------------------
	// ---
	// --- Packet specific API
	// ---
	// --------------------------------------------------------------
	public long getFolderRowId() {
		return folderRowId;
	}

	public DMSDocumentType getDocumentType() {
		return documentType;
	}

	public List<Long> getDocumentRowIds() {
		return AlterableView.of(documentRowIds);
	}

}

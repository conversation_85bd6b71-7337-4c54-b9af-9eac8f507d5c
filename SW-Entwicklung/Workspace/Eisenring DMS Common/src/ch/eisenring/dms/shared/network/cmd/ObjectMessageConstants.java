package ch.eisenring.dms.shared.network.cmd;

public interface ObjectMessageConstants {

	/**
	 * Flag signaling that the objects attribute values have changed
	 */
	public int ATTRIBUTES = 0x01;
	
	/**
	 * Flag signaling that the objects children have changed
	 */
	public int CHILDREN = 0x02;

	/**
	 * Flag signaling that the object has been removed.
	 * Note: a REMOVED message requires also a CHILDREN message for the parent folder!
	 */
	public int REMOVED = 0x04;

	/**
	 * Flag signaling that the objects properties have changed
	 */
	public int PROPERTIES = 0x08;
	
	/**
	 * Mask of all allowed flag bits (that doesn't mean it makes sense
	 * to send them all at once).
	 */
	public int ALL = 0x0F;

}

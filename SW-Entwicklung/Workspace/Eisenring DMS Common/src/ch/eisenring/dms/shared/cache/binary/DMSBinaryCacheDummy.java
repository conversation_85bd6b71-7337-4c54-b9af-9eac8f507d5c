package ch.eisenring.dms.shared.cache.binary;

import ch.eisenring.core.collections.List;
import ch.eisenring.dms.shared.model.api.DMSBinary;

/**
 * Dummy implementation of DMSBinaryCache (this implementation
 * provides a cache with zero capacity, it simply discards
 * added entries and always replies with "cache miss" to queries)
 */
public final class DMSBinaryCacheDummy implements DMSBinaryCache {

	@Override
	public void addBinary(final DMSBinary binary) {
		// ignore
	}

	@Override
	public DMSBinary getBinary(final long binaryRowId) {
		return null;
	}

	@Override
	public List<DMSBinary> getBinaries(final long objectRowId) {
		return null;
	}

	@Override
	public void flush() {
		// nothing to do here
	}

	@Override
	public void setCachesize(final long cacheSize) {
		// ignore
	}

	@Override
	public void tick() {
		// ignore
	}

}

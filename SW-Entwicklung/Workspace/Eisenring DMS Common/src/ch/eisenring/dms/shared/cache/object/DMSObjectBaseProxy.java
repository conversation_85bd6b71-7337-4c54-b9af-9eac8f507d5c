package ch.eisenring.dms.shared.cache.object;

import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.core.io.binary.BinaryHolder;
import ch.eisenring.dms.service.api.DMSObject;
import ch.eisenring.dms.service.codetables.DMSIconCode;
import ch.eisenring.dms.service.codetables.DMSObjectStatus;
import ch.eisenring.dms.service.codetables.DMSObjectType;

/**
 * Generic proxy for DMSObject (must be specialized for client/server)
 * 
 * Note: It doesn't make sense to make the proxy implement Streamable,
 * because the concrete child class will never exist on the other side.
 */
public abstract class DMSObjectBaseProxy implements DMSObject {

	final static long DEFAULT_LASTCHANGED_TIMESTAMP = TimestampUtil.toTimestamp("2000-01-01");

	/**
	 * Primary key of this object
	 */
	private long pkValue;

	/**
	 * The data holder of this proxy
	 */
	private volatile DMSObjectData dataObject;

	/**
	 * Constructor. Only to be called by the cache.
	 * Instances of ObjectProxy must be obtained only
	 * through the caches calls.
	 */
	protected DMSObjectBaseProxy(final long pkValue) {
		this.pkValue = pkValue;
	}

	// --------------------------------------------------------------
	// ---
	// --- Parent/Child management
	// ---
	// --------------------------------------------------------------
	/**
	 * Updates this proxy with the data from the given object.
	 * If the object parameter is NULL, this will invalidate all
	 * attributes and the child array of this proxy.
	 */
	protected DMSObjectData update(final DMSObject object) {
		if (object == null) {
			// forget the data object
			dataObject = null;
			return null;
		} else {
			DMSObjectData dataObject = this.dataObject;
			if (dataObject == null) {
				// create new data object
				this.dataObject = dataObject = DMSObjectData.create(object);
			} else {
				// recycle existing data object
				dataObject.update(object);
			}
			return dataObject;
		}
	}

	/**
	 * Invalidate the cached child array of the object
	 */
	public final void invalidateChildren() {
		// bypass the normal getDataObject() here, if there is currently
		// no data object attached, there are also no children to invalidate.
		final DMSObjectData dataObject = this.dataObject;
		if (dataObject != null)
			dataObject.setChildren(null);
	}

	/**
	 * Invalidate the cached attributes of the object.
	 * This automatically invalidates also the children.
	 */
	public final void invalidateAttributes() {
		dataObject = null;
	}

	/**
	 * Gets the ProxyDataObject for this proxy.
	 * Be careful with it, its a reference to the live object.
	 */
	protected DMSObjectData getDataObject(final boolean loadIfNotPresent) {
		return dataObject;
	}

	/**
	 * Sets the data object for this proxy. Caller must take extreme
	 * care that the data object is of correct type and content.
	 */
	protected void setDataObject(final DMSObjectData dataObject) {
		this.dataObject = dataObject;
	}

	// --------------------------------------------------------------
	// ---
	// --- KeywordHolder implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public String getKeywords() {
		final DMSObjectData object = getDataObject(true);
		return object == null ? null : object.getKeywords();
	}
	
	// --------------------------------------------------------------
	// ---
	// --- DMSObjectInterface implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public final long getPKValue() {
		return pkValue;
	}

	@Override
	public final Long getParentRowId() {
		final DMSObjectData object = getDataObject(true);
		return object == null ? null : object.getParentRowId();
	}

	@Override
	public final Long getPermissionRowId() {
		final DMSObjectData object = getDataObject(true);
		return object == null ? null : object.getPermissionRowId();
	}

	@Override
	public final DMSObjectType getType() {
		final DMSObjectData object = getDataObject(true);
		return object == null ? DMSObjectType.FOLDER : object.getType();
	}

	@Override
	public final DMSObjectStatus getObjectStatus() {
		final DMSObjectData object = getDataObject(true);
		return object == null ? DMSObjectStatus.ACTIVE : object.getObjectStatus();
	}

	@Override
	public final String getObjectname() {
		final DMSObjectData object = getDataObject(true);
		return object == null ? "???" : object.getObjectname();
	}
	
	@Override
	public final DMSIconCode getIconCode() {
		final DMSObjectData object = getDataObject(true);
		return object == null ? DMSIconCode.DEFAULT : object.getIconCode();
	}
	
	@Override
	public final BinaryHolder getThumbnail() {
		final DMSObjectData object = getDataObject(true);
		return object == null ? null : object.getThumbnail();
	}

	@Override
	public final String getFileExtension() {
		final DMSObjectData object = getDataObject(true);
		return object == null ? null : object.getFileExtension();
	}

	@Override
	public final String getParticipants() {
		final DMSObjectData object = getDataObject(true);
		return object == null ? null : object.getParticipants();
	}

	@Override
	public final long getBytesize() {
		final DMSObjectData object = getDataObject(true);
		return object == null ? 0 : object.getBytesize();
	}

	@Override
	public long getLastChanged() {
		final DMSObjectData object = getDataObject(true);
		return object == null ? DEFAULT_LASTCHANGED_TIMESTAMP : object.getLastChanged();
	}

	// --------------------------------------------------------------
	// ---
	// --- Object Overrides
	// ---
	// --------------------------------------------------------------
	@Override
	public final String toString() {
		return getObjectname();
	}

	@Override
	public final int hashCode() {
		return ((int) (pkValue >> 32)) ^ ((int) pkValue);
	}

	@Override
	public final boolean equals(final Object o) {
		return (o instanceof DMSObjectBaseProxy) && ((DMSObjectBaseProxy) o).pkValue == pkValue;
	}

}

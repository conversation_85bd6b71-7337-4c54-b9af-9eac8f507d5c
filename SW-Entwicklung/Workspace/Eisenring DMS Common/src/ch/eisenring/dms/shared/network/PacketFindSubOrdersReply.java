package ch.eisenring.dms.shared.network;

import java.io.IOException;

import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.collections.Collection;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.alterableview.AlterableView;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.io.StreamReader;
import ch.eisenring.core.io.StreamUtil;
import ch.eisenring.core.io.StreamWriter;

public class PacketFindSubOrdersReply extends AbstractDMSPacket {

	private String projectNumber;
	private final List<String> subNumbers = new ArrayList<>();
	
	private PacketFindSubOrdersReply() {
		super(CAPS_SEQUENCE_REPLY | CAPS_ERRORMESSAGE, PRIORITY_REPLY);
	}

	public static PacketFindSubOrdersReply create(
			final PacketFindSubOrdersRequest request,
			final ErrorMessage error) {
		final PacketFindSubOrdersReply packet = new PacketFindSubOrdersReply();
		packet.setReplyId(request, error);
		packet.projectNumber = request.getProjectNumber();
		return packet;
	}

	public static PacketFindSubOrdersReply create(
			final PacketFindSubOrdersRequest request,
			final Collection<String> subNumbers) {
		final PacketFindSubOrdersReply packet = new PacketFindSubOrdersReply();
		packet.setReplyId(request, ErrorMessage.OK);
		for (final String subNumber : subNumbers) {
			if (!Strings.isEmpty(subNumber) && !packet.subNumbers.contains(subNumber))
				packet.subNumbers.add(subNumber);
		}
		packet.projectNumber = request.getProjectNumber();
		return packet;
	}

	// --------------------------------------------------------------
	// ---
	// --- User API
	// ---
	// --------------------------------------------------------------
	public String getProjectNumber() {
		return projectNumber;
	}

	public List<String> getSubNumbers() {
		return AlterableView.of(subNumbers);
	}

	// --------------------------------------------------------------
	// ---
	// --- Streamable
	// ---
	// --------------------------------------------------------------
	@Override
	protected void readImpl(final StreamReader reader) throws IOException {
		super.readImpl(reader);
		projectNumber = reader.readString();
		StreamUtil.readCollection(reader, subNumbers);
	}

	@Override
	protected void writeImpl(final StreamWriter writer) throws IOException {
		super.writeImpl(writer);
		writer.writeString(projectNumber);
		StreamUtil.writeCollection(writer, subNumbers);
	}
	
}


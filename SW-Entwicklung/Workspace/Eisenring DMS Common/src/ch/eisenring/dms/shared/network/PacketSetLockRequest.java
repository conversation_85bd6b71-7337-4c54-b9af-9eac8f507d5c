//package ch.eisenring.dms.shared.network;
//
//public class PacketSetLockRequest extends AbstractDAORequestPacket {
//
//	private PacketSetLockRequest() {
//	}
//	
//	public static PacketSetLockRequest create(final Long objectRowId,
//			                                  final String user) {
//		final PacketSetLockRequest packet = new PacketSetLockRequest();
//		packet.setUser(user);
//		packet.addRowId(objectRowId);
//		return packet;
//	}
//
//	// --------------------------------------------------------------
//	// ---
//	// --- Packet implementation
//	// ---
//	// --------------------------------------------------------------
//
//	// no packet specific implementation
//	
//}

package ch.eisenring.dms.shared.network;

import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.dms.service.api.DMSObject;

public final class PacketGetChildObjectsReply extends PacketObjectTransportBase {

	PacketGetChildObjectsReply() {
		super(CAPS_SEQUENCE_REPLY | CAPS_ERRORMESSAGE, PRIORITY_REPLY);
	}

	/**
	 * Creates a reply packet
	 */
	public static PacketGetChildObjectsReply create(final PacketGetChildObjectsRequest request,
										            final java.util.Collection<DMSObject> objects) {
		final PacketGetChildObjectsReply packet = new PacketGetChildObjectsReply();
		packet.setReplyId(request, ErrorMessage.OK);
		packet.addObjects(objects);
		return packet;
	}

	/**
	 * Creates a reply packet
	 */
	public static PacketGetChildObjectsReply create(final PacketGetChildObjectsRequest request,
										            final ErrorMessage error) {
		final PacketGetChildObjectsReply packet = new PacketGetChildObjectsReply();
		packet.setReplyId(request, error);
		return packet;
	}

}

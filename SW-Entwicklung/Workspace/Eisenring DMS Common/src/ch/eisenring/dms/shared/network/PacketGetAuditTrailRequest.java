package ch.eisenring.dms.shared.network;

public final class PacketGetAuditTrailRequest extends AbstractDAORequestPacket {

	private PacketGetAuditTrailRequest() {
	}

	public static PacketGetAuditTrailRequest create(final Long objectRowId, final String user) {
		final PacketGetAuditTrailRequest packet = new PacketGetAuditTrailRequest();
		packet.setUser(user);
		packet.addRowId(objectRowId);
		return packet;
	}

	// --------------------------------------------------------------
	// ---
	// --- Packet implementation
	// ---
	// --------------------------------------------------------------

	// no packet specific implementation

}

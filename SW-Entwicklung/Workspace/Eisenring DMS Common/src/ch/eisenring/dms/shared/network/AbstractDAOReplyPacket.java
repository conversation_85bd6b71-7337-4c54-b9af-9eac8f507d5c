package ch.eisenring.dms.shared.network;

import java.io.IOException;

import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.io.StreamReader;
import ch.eisenring.core.io.StreamWriter;
import ch.eisenring.core.util.OperationResult;
import ch.eisenring.dms.shared.model.DAOResult;
import ch.eisenring.model.Model;

public abstract class AbstractDAOReplyPacket extends AbstractDMSPacket {

	private transient DAOResult result;

	AbstractDAOReplyPacket(final int caps, final int priority) {
		super(caps, priority);
	}

	protected AbstractDAOReplyPacket() {
		super(CAPS_SEQUENCE_REPLY | CAPS_ERRORMESSAGE, PRIORITY_REPLY);
	}
	
	protected void setResult(final AbstractDAORequestPacket request,
							 final DAOResult result) {
		this.result = result;
		final ErrorMessage message = result == null ? ErrorMessage.ERROR : result.getMessage();
		setReplyId(request, message);
	}

	public DAOResult getResult() {
		return result;
	}
	
	/**
	 * Extracts the context and finds all objects of the given class.
	 */
	public <T extends Model> OperationResult<List<T>> getModels(final Class<T> modelClass) {
		return result.getModels(modelClass);
	}

	/**
	 * Returns first model or null if error
	 */
	public <T extends Model> T getFirstModel(final Class<T> modelClass) {
		final OperationResult<List<T>> result = getModels(modelClass);
		if (result.isSuccess()) {
			final List<T> list = result.getResult();
			return list == null || list.isEmpty() ? null : list.get(0);
		}
		return null;
	}

	// --------------------------------------------------------------
	// ---
	// --- Streamable implementation
	// ---
	// --------------------------------------------------------------
	@Override
	protected void readImpl(final StreamReader reader) throws IOException {
		super.readImpl(reader);
		result = (DAOResult) reader.readObject();
	}

	@Override
	protected void writeImpl(final StreamWriter writer) throws IOException {
		super.writeImpl(writer);
		writer.writeObject(result);
	}

}

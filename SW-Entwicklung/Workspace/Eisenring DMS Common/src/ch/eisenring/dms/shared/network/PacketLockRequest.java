package ch.eisenring.dms.shared.network;

import ch.eisenring.dms.shared.locking.DMSObjectLock;

/**
 * Packet used to request a lock from the server
 */
public final class PacketLockRequest extends PacketLockBase {

	PacketLockRequest() {
		super(CAPS_SEQUENCE_REQUEST, PRIORITY_REQUEST);
	}

	// --------------------------------------------------------------
	// ---
	// --- Factory methods
	// ---
	// --------------------------------------------------------------
	/**
	 * Requests the specified lock from the server
	 */
	public static PacketLockRequest create(final DMSObjectLock lock) {
		if (lock == null)
			throw new NullPointerException();
		final PacketLockRequest packet = new PacketLockRequest();
		packet.addLock(lock);
		return packet;
	}
	
	/**
	 * Creates a lock request for the specified object.
	 * Host and path may be NULL, if the lock is only a lock and not a check-out.
	 */
	@Deprecated
	public static PacketLockRequest create(final long objectRowId,
			final String lockUser, final String lockHost, final String lockPath) {
		final DMSObjectLock lock = DMSObjectLock.create(objectRowId, 
				lockUser, System.currentTimeMillis(), lockHost, lockPath);
		return create(lock);
	}

	public DMSObjectLock getLock() {
		return getLockIterator().next();
	}

	// --------------------------------------------------------------
	// ---
	// --- Packet implementation
	// ---
	// --------------------------------------------------------------

	// no packet specific implementation

}

package ch.eisenring.dms.shared.model.meta;

import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.core.util.VarArgs;
import ch.eisenring.dms.service.StringCaches;
import ch.eisenring.dms.service.codetables.DMSIconCode;
import ch.eisenring.dms.service.codetables.DMSObjectStatus;
import ch.eisenring.dms.service.codetables.DMSObjectType;
import ch.eisenring.dms.shared.DMSConstants;
import ch.eisenring.dms.shared.codetables.DMSDirtyFlags;
import ch.eisenring.dms.shared.model.pojo.DMSObjectPojo;
import ch.eisenring.model.MetaAttribute;
import ch.eisenring.model.MetaClass;
import ch.eisenring.model.MetaClassMember;
import ch.eisenring.model.MetaProperty;
import ch.eisenring.model.PrimaryKeyOrdinal;
import ch.eisenring.model.engine.jdbc.ColumnSpecifier;
import ch.eisenring.model.engine.jdbc.TableSpecifier;
import ch.eisenring.model.factory.AttributeFactory;
import ch.eisenring.model.mapping.POJOField;
import ch.eisenring.model.primarykey.LongPrimaryKey;

public interface DMSObjectMeta {

	TableSpecifier TABLE = TableSpecifier.get(DMSConstants.DMS_DATABASE, "DMSObject");

	ColumnSpecifier PKCOL = ColumnSpecifier.get(TABLE, "rowId");
	MetaAttribute ATR_ROWID = AttributeFactory.pLong(
			"rowId", 0, Long.MIN_VALUE, Long.MAX_VALUE,
			POJOField.get(DMSObjectPojo.class, "rowId"),
			PKCOL, PrimaryKeyOrdinal.O1);

	MetaAttribute ATR_PARENT_ROWID = AttributeFactory.wLong(
			"Parent_rowId", null, Long.MIN_VALUE, Long.MAX_VALUE,
			POJOField.get(DMSObjectPojo.class, "parentRowId"),
			ColumnSpecifier.get(TABLE, "Parent_rowId"));

	MetaAttribute ATR_PERMISSION_ROWID = AttributeFactory.wLong(
			"Permission_rowId", null, Long.MIN_VALUE, Long.MAX_VALUE,
			POJOField.get(DMSObjectPojo.class, "permissionRowId"),
			ColumnSpecifier.get(TABLE, "Permission_rowId"));

	MetaAttribute ATR_TYPECODE = AttributeFactory.code(
			"Typecode", DMSObjectType.class, null,
			POJOField.get(DMSObjectPojo.class, "objectType"),
			ColumnSpecifier.get(TABLE, "Typecode"));

	MetaAttribute ATR_STATUSCODE = AttributeFactory.code(
			"Statuscode", DMSObjectStatus.class, DMSObjectStatus.ACTIVE,
			POJOField.get(DMSObjectPojo.class, "objectStatus"),
			ColumnSpecifier.get(TABLE, "Statuscode"));

	MetaAttribute ATR_ICONCODE = AttributeFactory.code(
			"Iconcode", DMSIconCode.class, DMSIconCode.DEFAULT,
			POJOField.get(DMSObjectPojo.class, "iconCode"),
			ColumnSpecifier.get(TABLE, "Iconcode"));

	MetaAttribute ATR_DIRTYCODE = AttributeFactory.pInt(
			"Dirtycode", DMSDirtyFlags.ALL,
			Integer.MIN_VALUE, Integer.MAX_VALUE,
			ColumnSpecifier.get(TABLE, "Dirtycode"));

	MetaAttribute ATR_OBJECTNAME = AttributeFactory.cleanString(
			"Objectname", null, 250, StringCaches.OBJECTNAME,
			POJOField.get(DMSObjectPojo.class, "objectName"),
			ColumnSpecifier.get(TABLE, "Objectname"));
	
	MetaAttribute ATR_KEYWORDS = AttributeFactory.cleanString(
			"Keywords", null, 2000,
			POJOField.get(DMSObjectPojo.class, "keyWords"),
			ColumnSpecifier.get(TABLE, "Keywords"));

	MetaAttribute ATR_THUMBNAIL = AttributeFactory.binaryHolder(
			"Thumbnail",
			POJOField.get(DMSObjectPojo.class, "thumbnail"),
			ColumnSpecifier.get(TABLE, "Thumbnail"));

	MetaAttribute ATR_LASTCHANGED = AttributeFactory.datetime(
			"LastChanged", TimestampUtil.NULL_TIMESTAMP,
			POJOField.get(DMSObjectPojo.class, "lastChanged"),
			ColumnSpecifier.get(TABLE, "LastChanged"));

	MetaAttribute ATR_BYTESIZE = AttributeFactory.wLong(
			"Bytesize", 0L, Long.MIN_VALUE, Long.MAX_VALUE,
			POJOField.get(DMSObjectPojo.class, "byteSize"),
			ColumnSpecifier.get(TABLE, "Bytesize"));

	MetaAttribute ATR_FILEEXTENSION = AttributeFactory.cleanString(
			"FileExtension", null, 16, StringCaches.FILEEXT,
			POJOField.get(DMSObjectPojo.class, "fileExtension"),
			ColumnSpecifier.get(TABLE, "FileExtension"));

	MetaAttribute ATR_PARTICIPANTS = AttributeFactory.cleanString(
			"Participants", null, 250, StringCaches.PARTICIPANTS,
			POJOField.get(DMSObjectPojo.class, "participants"),
			ColumnSpecifier.get(TABLE, "Participants"));

	MetaClass METACLASS = new MetaClass(
			"DMSObject",
			LongPrimaryKey.class,
			// Attributes
			new VarArgs<MetaProperty>(MetaProperty.class).flat(
			TABLE, PKCOL,
			MetaClassMember.discoverMetaClassMembers(DMSObjectMeta.class)
			));

}

package ch.eisenring.dms.shared.network;

import ch.eisenring.core.io.AutoStreamed;
import ch.eisenring.core.io.binary.BinaryHolder;
import ch.eisenring.dms.service.codetables.DMSIconCode;

public final class PacketSetObjectIconRequest extends AbstractDAORequestPacket {

	@AutoStreamed
	private DMSIconCode iconCode;

	@AutoStreamed
	private BinaryHolder thumbnail;
	
	private PacketSetObjectIconRequest() {
	}

	public static PacketSetObjectIconRequest create(final Long objectId,
													final String user,
													final DMSIconCode iconCode,
													final BinaryHolder thumbnail) {
		final PacketSetObjectIconRequest packet = new PacketSetObjectIconRequest();
		packet.addRowId(objectId);
		packet.setUser(user);
		packet.iconCode = iconCode;
		packet.thumbnail = thumbnail;
		return packet;
	}

	public DMSIconCode getIconCode() {
		return iconCode;
	}

	public BinaryHolder getThumbnail() {
		return thumbnail;
	}

	// --------------------------------------------------------------
	// ---
	// --- Packet implementation
	// ---
	// --------------------------------------------------------------

	// no packet specific implementation

}

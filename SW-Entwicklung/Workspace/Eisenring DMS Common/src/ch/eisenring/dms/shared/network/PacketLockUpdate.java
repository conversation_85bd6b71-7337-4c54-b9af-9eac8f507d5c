package ch.eisenring.dms.shared.network;

/**
 * Packet sent to all clients when the lock inventory changes.
 */
public final class PacketLockUpdate extends PacketLockBase {

	private PacketLockUpdate() {
		super(CAPS_NO_SEQUENCE_SUPPORT | CAPS_BROADCAST | CAPS_SILENT, PRIORITY_NOTIFY);
	}
	
	public static PacketLockUpdate create() {
		final PacketLockUpdate packet = new PacketLockUpdate();
		
		return packet;
	}

	// --------------------------------------------------------------
	// ---
	// --- Packet implementation
	// ---
	// --------------------------------------------------------------

	// no packet specific implementation

}

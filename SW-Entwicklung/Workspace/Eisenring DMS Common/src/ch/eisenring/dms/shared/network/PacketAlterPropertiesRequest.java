package ch.eisenring.dms.shared.network;

import java.io.IOException;
import java.util.Iterator;

import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.io.StreamReader;
import ch.eisenring.core.io.StreamWriter;
import ch.eisenring.dms.service.codetables.DMSPropertyCode;
import ch.eisenring.dms.shared.codetables.DMSPropertyCmdCode;
import ch.eisenring.dms.shared.network.cmd.PropertyCmd;

/**
 * Packet used to alter properties (can contain multiple commands
 * for completely unrelated properties)
 */
public final class PacketAlterPropertiesRequest extends AbstractDAORequestPacket {

	private final ArrayList<PropertyCmd> cmdList = new ArrayList<>();
	
	private PacketAlterPropertiesRequest() {
	}
	
	public static PacketAlterPropertiesRequest create(final String user) {
		final PacketAlterPropertiesRequest packet = new PacketAlterPropertiesRequest();
		packet.setUser(user);
		return packet;
	}

	public void addCommand(final DMSPropertyCmdCode cmdCode,
			               final long objectRowId,
			               final DMSPropertyCode propertyCode,
			               final String propertyValue) {
		final PropertyCmd cmd = new PropertyCmd(cmdCode, objectRowId, propertyCode, propertyValue);
		cmdList.add(cmd);
	}

	public void addCommand(final DMSPropertyCmdCode cmdCode,
						   final long objectRowId,
						   final DMSPropertyCode propertyCode,
						   final AbstractCode propertyValueCode) {
		String propertyValue = null;
		if (propertyValueCode != null) {
			final Object key = AbstractCode.getKey(propertyValueCode, null);
			propertyValue = Strings.toString(key);
		}
		addCommand(cmdCode, objectRowId, propertyCode, propertyValue);
	}

	public Iterator<PropertyCmd> getCommandIterator() {
		return cmdList.iterator();
	}
	
	// --------------------------------------------------------------
	// ---
	// --- Packet implementation
	// ---
	// --------------------------------------------------------------
	@Override
	protected void readImpl(final StreamReader reader) throws IOException {
		super.readImpl(reader);
		final int count = reader.readInt();
		cmdList.clear();
		cmdList.ensureCapacity(count);
		for (int i=0; i<count; ++i) {
			final PropertyCmd cmd = (PropertyCmd) reader.readObject();
			cmdList.add(cmd);
		}
	}

	@Override
	protected void writeImpl(final StreamWriter writer) throws IOException {
		super.writeImpl(writer);
		final int count = cmdList.size();
		writer.writeInt(count);
		for (int i=0; i<count; ++i) {
			final PropertyCmd cmd = cmdList.get(i);
			writer.writeObject(cmd);
		}
	}

}

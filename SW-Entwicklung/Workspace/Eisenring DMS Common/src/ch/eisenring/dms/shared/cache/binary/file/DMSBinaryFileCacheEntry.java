package ch.eisenring.dms.shared.cache.binary.file;

import java.io.File;

import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.dms.shared.model.api.DMSBinary;

public final class DMSBinaryFileCacheEntry {

	protected long objectRowId;
	protected long binaryRowId;
	protected long rawSize;
	protected long lastAccess;

	public DMSBinaryFileCacheEntry(final DMSBinary binary) {
		this.objectRowId = binary.getObjectRowId();
		this.binaryRowId = binary.getPKValue();
		this.rawSize = binary.getRawsize();
		this.lastAccess = Long.MIN_VALUE;
	}

	public long getObjectRowId() {
		return objectRowId;
	}

	public long getBinaryRowId() {
		return binaryRowId;
	}

	public long getWeight() {
		return rawSize;
	}

	public long getLastAccess() {
		return lastAccess;
	}

	public File getCacheDir(final File cachePath) {
		int hash = ((int) (binaryRowId >> 32)) ^ ((int) binaryRowId);
		hash = ((hash >> 16) ^ hash) & 0xFFFF;
		hash = (((hash * 73) >> 8) ^ hash) & 0x1FF;
		final StringMaker b = StringMaker.obtain();
		b.append(Strings.getHEXDigit((hash >> 12) & 0xF));
		b.append(Strings.getHEXDigit((hash >>  8) & 0xF));
		b.append(Strings.getHEXDigit( hash        & 0xF));
		final String subPath = b.release(); 
		return new File(cachePath, subPath);
	}

	public File getBinFile(final File cachePath) {
		return null;
	}

	public File getDatFile(final File cachePath) {
		return null;
	}

}

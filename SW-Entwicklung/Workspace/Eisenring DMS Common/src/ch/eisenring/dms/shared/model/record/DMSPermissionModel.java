package ch.eisenring.dms.shared.model.record;

import ch.eisenring.dms.shared.model.metaold.DMSPermissionMetaModel;
import ch.eisenring.model.MetaClass;
import ch.eisenring.model.Model;
import ch.eisenring.model.PrimaryKey;
import ch.eisenring.model.TransactionContext;

/**
 * Persistance mapping class for PermissionDescriptor.
 *
 * As such, the class is only a skeleton that is practically
 * not instanciated to be used by application code. 
 */
public class DMSPermissionModel extends Model {

	public final static MetaClass METACLASS = DMSPermissionMetaModel.METACLASS;

	/**
	 * Constructor is called via reflection
	 */
	protected DMSPermissionModel(final TransactionContext context, final PrimaryKey pk) {
		super(context, pk);
	}

	// --------------------------------------------------------------
	// ---
	// --- Class is not really implemented because it is used
	// --- only for persistance mapping.
	// ---
	// --------------------------------------------------------------

}

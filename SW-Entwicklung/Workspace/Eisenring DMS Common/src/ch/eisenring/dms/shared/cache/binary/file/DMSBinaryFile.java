package ch.eisenring.dms.shared.cache.binary.file;

import java.io.IOException;

import ch.eisenring.core.io.binary.BinaryHolder;
import ch.eisenring.dms.service.codetables.DMSBinaryStatus;
import ch.eisenring.dms.shared.codetables.DMSCompressionCode;
import ch.eisenring.dms.shared.model.api.DMSBinary;

/**
 * File-based implementation of DMSBinaryInterface.
 * For use by disk based cache.
 */
public final class DMSBinaryFile implements DMSBinary {

	private int plainversion;
	private long objectRowId;
	private long binaryRowId;
	private long byteSize;
	private long rawSize;
	private String fileExtension;
	private String binaryHash;
	private String plaintext;
	private long madeOn;
	private DMSBinaryStatus statusCode;
	private DMSCompressionCode compressionCode;
	private BinaryHolder rawData;

	// --------------------------------------------------------------
	// ---
	// --- DMSBinaryInterface implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public long getPKValue() {
		return binaryRowId;
	}

	@Override
	public long getObjectRowId() {
		return objectRowId;
	}

	@Override
	public DMSBinaryStatus getStatusCode() {
		return statusCode;
	}

	@Override
	public long getBytesize() {
		return byteSize;
	}

	@Override
	public long getRawsize() {
		return rawSize;
	}

	@Override
	public DMSCompressionCode getDCMCode() {
		return compressionCode;
	}

	@Override
	public long getMadeOn() {
		return madeOn;
	}

	@Override
	public String getBinaryhash() {
		return binaryHash;
	}

	@Override
	public String getFileExtension() {
		return fileExtension;
	}
	
	@Override
	public BinaryHolder getRawData() {
		return rawData;
	}
	
	@Override
	public int getPlainversion() {
		return plainversion;
	}

	@Override
	public String getPlaintext() {
		return plaintext;
	}
	
	@Override
	public BinaryHolder getBinaryData() {
		try {
			final DMSCompressionCode compressionCode = getDCMCode();
			return compressionCode.decompress(getRawData());
		} catch (final IOException e) {
			throw new RuntimeException(e.getMessage(), e);
		}		
	}

}

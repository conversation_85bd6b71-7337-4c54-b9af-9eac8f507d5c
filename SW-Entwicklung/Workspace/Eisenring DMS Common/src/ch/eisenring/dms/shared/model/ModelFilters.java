package ch.eisenring.dms.shared.model;

import ch.eisenring.core.iterators.Filter;
import ch.eisenring.dms.shared.model.metaold.DMSObjectMetaModel;
import ch.eisenring.dms.shared.model.record.DMSObjectModel;
import ch.eisenring.model.Model;

public interface ModelFilters {

	Filter<Model> ROOT_OBJECTS = new Filter<Model>() {
		@Override
		public boolean accepts(final Model model) {
			return model instanceof DMSObjectModel && DMSObjectMetaModel.ATR_PARENT_ROWID.getMember(model) == null;
		}
	};
	
}

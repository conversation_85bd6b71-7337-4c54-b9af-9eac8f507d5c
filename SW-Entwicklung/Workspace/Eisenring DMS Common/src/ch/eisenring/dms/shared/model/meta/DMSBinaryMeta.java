package ch.eisenring.dms.shared.model.meta;

import ch.eisenring.core.util.VarArgs;
import ch.eisenring.dms.shared.DMSConstants;
import ch.eisenring.dms.shared.model.pojo.DMSBinaryPojo;
import ch.eisenring.model.MetaAttribute;
import ch.eisenring.model.MetaClass;
import ch.eisenring.model.MetaClassMember;
import ch.eisenring.model.MetaProperty;
import ch.eisenring.model.Model;
import ch.eisenring.model.engine.jdbc.ColumnSpecifier;
import ch.eisenring.model.factory.AttributeFactory;
import ch.eisenring.model.mapping.POJOField;
import ch.eisenring.model.primarykey.LongPrimaryKey;

public interface DMSBinaryMeta extends DMSVersionMeta {

	MetaAttribute ATR_PLAINTEXT = AttributeFactory.cleanString(
			"Plaintext", null, 65536,
			POJOField.get(DMSBinaryPojo.class, "plainText"),
			ColumnSpecifier.get(TABLE, "Plaintext"));

	MetaAttribute ATR_BINARYDATA = AttributeFactory.binaryHolder(
			"Binarydata", 
			POJOField.get(DMSBinaryPojo.class, "binaryData"),
			ColumnSpecifier.get(TABLE, "Binarydata"));

	MetaAttribute ATR_PLAINVERSION = AttributeFactory.pInt(
			"Plainversion", DMSConstants.PLAINTEXT_VERSION_INVALID, Integer.MIN_VALUE, Integer.MAX_VALUE,
			POJOField.get(DMSBinaryPojo.class, "plainVersion"),
			ColumnSpecifier.get(TABLE, "Plainversion"));

	MetaClass METACLASS = new MetaClass(
			"DMSBinary", (Class<Model>) null,
			LongPrimaryKey.class,
			DMSVersionMeta.METACLASS,
			// Attributes
			new VarArgs<MetaProperty>(MetaProperty.class).flat(
			ColumnSpecifier.get(TABLE, "rowId"),
			TABLE,
			MetaClassMember.discoverMetaClassMembers(DMSBinaryMeta.class)
			));

}

package ch.eisenring.dms.shared.network;

import ch.eisenring.dms.shared.network.cmd.ObjectMessageSet;

/**
 * This packet is sent to all clients to inform them of
 * changes made by other clients.
 */
public final class PacketObjectMessages extends PacketObjectMessagesBase {

	private PacketObjectMessages() {
		super(CAPS_BROADCAST | CAPS_SILENT, PRIORITY_NOTIFY);
	}
	
	public static PacketObjectMessages create(final ObjectMessageSet messages) {
		final PacketObjectMessages packet = new PacketObjectMessages();
		packet.messages = messages;
		return packet;
	}

}

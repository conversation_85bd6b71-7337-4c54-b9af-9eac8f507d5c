package ch.eisenring.dms.shared.network;

import ch.eisenring.dms.shared.model.data.PermissionDescriptor;

/**
 * Packet sent to delete permission descriptor(s)
 */
public final class PacketPermissionDeleteRequest extends PacketPermissionBase {

	private PacketPermissionDeleteRequest() {
	}

	public static PacketPermissionDeleteRequest create(final String user,
													   final PermissionDescriptor ... descriptors) {
		final PacketPermissionDeleteRequest packet = new PacketPermissionDeleteRequest();
		packet.setUser(user);
		packet.addPermissions(descriptors);
		return packet;
	}

}

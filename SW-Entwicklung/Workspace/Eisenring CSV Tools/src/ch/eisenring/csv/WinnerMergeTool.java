package ch.eisenring.csv;

import java.awt.Component;
import java.io.File;
import java.io.PrintStream;

import javax.swing.JFileChooser;
import javax.swing.UIManager;

import ch.eisenring.csv.gui.MergeToolFrame;
import ch.eisenring.csv.reader.CSVFile;
import ch.eisenring.csv.reader.CSVReader;
import ch.eisenring.csv.reader.CSVRow;


public class WinnerMergeTool {

	private final JFileChooser fileChooser = new JFileChooser();
	
	public static void main(final String[] argv) {
		try {
			UIManager.setLookAndFeel(UIManager.getSystemLookAndFeelClassName());
		} catch (Throwable t) {
		}

		WinnerMergeTool instance = new WinnerMergeTool();
		instance.interactiveMerge();
	}

	public void interactiveMerge() {
		final MergeToolFrame gui = new MergeToolFrame();
		try {
			gui.setVisible(true);
			
			// --- select and read source
			gui.addMessage("Erste Datei (mit Preisen) auswählen...");
			final File srcFile = requestLoad(gui);
			if (srcFile == null) {
				throw new RuntimeException("Abbruch durch Benutzer!");
			} else {
				gui.addMessage("Quell-Datei 1: \""+srcFile.getCanonicalPath()+"\"");
			}
			gui.addMessage("Datei 1 wird eingelesen...");
			final CSVFile srcCSVFile = CSVReader.readCSV(srcFile, ';');
			if (srcCSVFile == null) {
				gui.addMessage("Fehler beim lesen der Datei!");
			} else {
				gui.addMessage("Fertig! ("+srcCSVFile.getRowCount()+" Zeilen)");
			}
			
			// --- select and read destination
			gui.addMessage("Zweite Datei (mit aktueller Artikelliste) auswählen...");
			final File dstFile = requestLoad(gui);
			if (dstFile == null) {
				throw new RuntimeException("Abbruch durch Benutzer!");
			}
			gui.addMessage("Quell-Datei 2: \""+dstFile.getCanonicalPath()+"\"");
			gui.addMessage("Datei 2 wird eingelesen...");
			final CSVFile dstCSVFile = CSVReader.readCSV(dstFile, ';');
			if (srcCSVFile == null) {
				gui.addMessage("Fehler beim lesen der Datei!");
			} else {
				gui.addMessage("Fertig! ("+dstCSVFile.getRowCount()+" Zeilen)");
			}
			
			// --- merge the files
			gui.addMessage("Die Daten werden zusammengeführt...");
			mergeCSVFiles(srcCSVFile, dstCSVFile,
					new String[] { "!Artikel-Nr.", "Code" },
					new String[] { "Montagezeit", "Assembly" });
			gui.addMessage("Fertig!");
			
			// --- save new file
			gui.addMessage("Zieldatei für Zusammengeführte Daten auswählen...");
			final File newFile = requestSave(gui);
			if (newFile == null) {
				throw new RuntimeException("Abbruch durch Benutzer!");
			}
			gui.addMessage("Daten werden gespeichert unter \""+newFile.getCanonicalPath()+"\"...");
			final String data = dstCSVFile.toString();
			PrintStream printStream = null;
			try {
				printStream = new PrintStream(newFile);
				printStream.print(data);
				printStream.flush();
			} finally {
				if (printStream != null) {
					try {
						printStream.close();
					} catch (Throwable t) {}
				}
			}
			gui.addMessage("Speichern erfolgreich!");
		} catch (Throwable t) {
			gui.addMessage("Unerwarteter Fehler: "+t.getMessage());
		} finally {
			gui.addMessage("Sie können dieses Fenster jetzt schliessen.");
		}
	}
	
	public void mergeCSVFiles(CSVFile srcFile, CSVFile dstFile, String[] keyNames, String[] valNames) {
		int srcKeyIndex = -1;
		int dstKeyIndex = -1;
		int srcValIndex = -1;
		int dstValIndex = -1;
		for (int i=0; i<keyNames.length; ++i) {
			if (srcKeyIndex < 0)
				srcKeyIndex = srcFile.getColumnIndex(keyNames[i]);
			if (dstKeyIndex < 0)
				dstKeyIndex = dstFile.getColumnIndex(keyNames[i]);
		}
		for (int i=0; i<valNames.length; ++i) {
			if (srcValIndex < 0)
				srcValIndex = srcFile.getColumnIndex(valNames[i]);
			if (dstValIndex < 0)
				dstValIndex = dstFile.getColumnIndex(valNames[i]);
		}
		// --- merge all keys from src to dst
		for (int dstIndex = 1; dstIndex < dstFile.getRowCount(); ++dstIndex) {
			final CSVRow dstRow = dstFile.getRow(dstIndex);
			final String key = dstRow.getColumn(dstKeyIndex);
			if (key != null) {
				final CSVRow srcRow = srcFile.findRowByKey(srcKeyIndex, key);
				String val;
				if (srcRow != null) {
					val = srcRow.getColumn(srcValIndex);
				} else {
					val = "Aktualisieren";
				}
				dstRow.setColumn(dstValIndex, val);
			}
		}
		// --- check for new lines in dstFile
		for (int dstIndex = 1; dstIndex < dstFile.getRowCount(); ++dstIndex) {
			final CSVRow dstRow = dstFile.getRow(dstIndex);
			final String key = dstRow.getColumn(dstKeyIndex);
			if (key != null) {
				final CSVRow srcRow = srcFile.findRowByKey(srcKeyIndex, key);
				if (srcRow == null) {
					dstRow.setColumn(dstValIndex, "Aktualisieren");
				}
			}
		}
	}

	public File requestLoad(Component parent) {
		fileChooser.setMultiSelectionEnabled(false);
		final int result = fileChooser.showOpenDialog(parent);
		if (JFileChooser.APPROVE_OPTION==result) {
			// user has choosen file(s)
			return fileChooser.getSelectedFile();
		}
		return null;
	}

	public File requestSave(Component parent) {
		fileChooser.setMultiSelectionEnabled(false);
		final int result = fileChooser.showSaveDialog(parent);
		if (JFileChooser.APPROVE_OPTION==result) {
			// user has choosen file(s)
			return fileChooser.getSelectedFile();
		}
		return null;
	}

}

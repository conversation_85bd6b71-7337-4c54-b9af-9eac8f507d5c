package ch.eisenring.dispo.client.gui.dnd;

import java.awt.Component;
import java.awt.Container;
import java.awt.Point;
import java.awt.Rectangle;
import java.awt.event.KeyAdapter;
import java.awt.event.KeyEvent;
import java.awt.event.KeyListener;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.awt.event.MouseListener;
import java.awt.event.MouseMotionAdapter;
import java.awt.event.MouseMotionListener;
import java.util.Iterator;

import javax.swing.JViewport;
import javax.swing.SwingUtilities;

import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.core.logging.Logger.LogLevel;
import ch.eisenring.core.observable.Observable;
import ch.eisenring.core.observable.Observer;
import ch.eisenring.core.observable.ObserverNotificationPolicy;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.client.gui.components.OverlayedFrame;
import ch.eisenring.dispo.client.gui.grid.BlockElement;
import ch.eisenring.dispo.client.gui.grid.Grid;
import ch.eisenring.dispo.client.gui.grid.GridElement;
import ch.eisenring.dispo.shared.model.ModelContext;
import ch.eisenring.gui.util.CursorUtil;

/**
 * Main handler for drag'n'drop.
 */
public final class DnDMainHandler extends DnDBase {

	private final Observer<Object> observer = new Observer<Object>() {
		@Override
		public void observableChanged(final Observable<Object> observeable) {
			boolean waitingForModel = client.WAITING_FOR_MODEL.get();
			setEnabled(!waitingForModel);
		}
	};
	
	/** Key listener, used to detect ESC key (cancel drag and drop) */
	private final KeyListener keyListener = new KeyAdapter() {
		@Override
		public void keyReleased(final KeyEvent event) {
			// any key event cancels drag'n'drop!
			//if (KeyEvent.VK_ESCAPE==event.getKeyCode())
			final int keyCode = event.getKeyCode();
			switch (keyCode) {
				case KeyEvent.VK_CONTROL:
				case KeyEvent.VK_SHIFT:
				case KeyEvent.VK_ALT:
					if (lastMouseEvent != null) {
						// qualifier keys do not cancel drag'n'drop
						MouseEvent e = new MouseEvent(
								lastMouseEvent.getComponent(),
								lastMouseEvent.getID(),
								System.currentTimeMillis(),
								event.getModifiers(),
								lastMouseEvent.getX(),
								lastMouseEvent.getY(),
								0, false);
						activeHandler.mouseMoved(e);
					} else {
						activeHandler.cancelDrag();
					}
					break;
				default:
					activeHandler.cancelDrag();
					break;
			}
		}

		@Override
		public void keyPressed(final KeyEvent event) {
			keyReleased(event);
		}
	};
	
	/** Motion listener */
	private final MouseMotionListener motionListener = new MouseMotionAdapter() {
		@Override
		public void mouseMoved(final MouseEvent event) {
			lastMouseEvent = event;
			if (isEnabled()) {
				final ModelContext context = client.getCurrentModel();
				if (null!=context)
					context.obtain();
				try {
					try {
						event.consume();
						activeHandler.mouseMoved(event);
					} catch (Exception e) {
						activeHandler.cancelDrag();
						Logger.error(e);
					}
				} finally {
					if (null!=context)
						context.release();
				}
			}
		}
		@Override
		public void mouseDragged(final MouseEvent event) {
			lastMouseEvent = event;
			if (isEnabled()) {
				event.consume();
				activeHandler.mouseMoved(event);
			}
		}
	};
	
	/** Mouse click listener */
	private final MouseListener mouseListener = new MouseAdapter() {
		@Override
		public void mousePressed(final MouseEvent event) {
			lastMouseEvent = event;
			if (event.isConsumed())
				return;
			if (isEnabled()) {
				final ModelContext context = getClient().getCurrentModel();
				if (context != null)
					context.obtain();
				try {
					if (contextMenu.gestureTriggerDetected(event)) {
						// nothing
//					} else if (doubleClick.gestureTriggerDetected(event)) {
						// nothing 
					} else if (MouseEvent.BUTTON1==event.getButton()) {
						if (GestureBase.isShifted(event)) {
							event.consume();
							final BlockElement block = getGridElementForSelect(event);
							if (block != null) {
								client.DND_SELECTION.get().toggle(block.getModel());
								block.getGrid().repaint();
							}
						} else {
							client.DND_SELECTION.get().clear();
							try {
								event.consume();
								activeHandler.mousePressed(event);
							} catch (final Exception e) {
								activeHandler.cancelDrag();
								Logger.log(LogLevel.ERROR, e);
							}
						}
					} else {
						activeHandler.cancelDrag();
					}
				} finally {
					if (context != null)
						context.release();
				}
			}
		}

		@Override
		public void mouseReleased(final MouseEvent event) {
			lastMouseEvent = event;
			if (isEnabled()) {
				final ModelContext context = getClient().getCurrentModel();
				if (null!=context)
					context.obtain();
				try {
					if (contextMenu.gestureTriggerDetected(event)) {
						// nothing
					} else if (doubleClick.gestureTriggerDetected(event)) {
						// nothing
					} else if (MouseEvent.BUTTON1==event.getButton()) {
						if (!event.isConsumed()) {
							event.consume();
							activeHandler.mouseReleased(event);
						}
					} else {
						activeHandler.cancelDrag();
					}
				} finally {
					if (null!=context)
						context.release();
				}
			}
		}
	};
	
	protected DnDStateBase activeHandler;

	/** Handler instances for each state */
	protected final DnDStateBase idleHandler = new DnDStateIdle(this);
	protected final DnDStateBase resizeWestHandler = new DnDStateResizeWest(this);
	protected final DnDStateBase resizeEastHandler = new DnDStateResizeEast(this);
	protected final DnDStateBase dragHandler = new DnDStateDrag(this);
	
	/** All supported gestures */
	private final GestureBase doubleClick = new GestureDoubleClick(this);
	private final GestureBase contextMenu = new GestureContextMenu(this);

	/** List of components the mainHandler is currently attached to */
	private final List<Component> componentList = new ArrayList<Component>();
	
	/** List of Grid objects the mainHandler currently controls */
	private final List<Grid> gridList = new ArrayList<Grid>();
	
	/** Frame the mainHandler is currently attached to */
	private OverlayedFrame frame;
	
	/** Currently active cursor type */
	private Object activeCursorId = CursorUtil.CURSOR_DEFAULT;
	
	/** Flag if the handler should really work */
	private boolean enabled;
	
	/** Last mouse event we processed */
	protected MouseEvent lastMouseEvent;
	
	public DnDMainHandler(final Client client) {
		super(client);
		setActiveHandler(idleHandler);
		client.WAITING_FOR_MODEL.addObserver(observer, ObserverNotificationPolicy.EDT_ASYNCHRONOUS);
	}

	public final Client getClient() {
		return this.client;
	}

	/**
	 * Enables/disables the handler
	 */
	protected final void setEnabled(final boolean enabled) {
		if (this.enabled!=enabled) {
			this.enabled = enabled;
			if (this.enabled) {
				// when enabling...
			} else {
				// when disabling...
				activeHandler.cancelDrag();
			}
			setActiveHandler(idleHandler);
			setCursor(idleHandler.getActiveCursor());
		}
	}

	/**
	 * Returns true if the handler is activated.
	 */
	protected final boolean isEnabled() {
		boolean waitingForModel = client.WAITING_FOR_MODEL.get();
		return enabled && !waitingForModel;
	}

	/**
	 * Attaches the mainHandler to the given Frame and all its child components.
	 *  
	 * For the mainHandler to work properly, this must be called after the frame
	 * has been populated with all its components, and when a component is
	 * added or removed, attach must be called again.
	 */
	public void attach(OverlayedFrame frame) {
		detach();
		this.frame = frame;
		if (null!=frame) {
			attachComponent(frame);
			attachComponent(frame.getGlassPane());
		}
		setEnabled(null!=frame);
	}
	private void attachComponent(Component component) {
		if (null==component)
			return;
		componentList.add(component);
		if (component instanceof Grid)
			gridList.add((Grid) component);
		component.addKeyListener(keyListener);
		component.addMouseListener(mouseListener);
		component.addMouseMotionListener(motionListener);
		if (component instanceof Container) {
			final Component[] components = ((Container) component).getComponents();
			for (int i=components.length-1; i>=0; --i)
				attachComponent(components[i]);
		}
	}

	/**
	 * Detaches the mainHandler from all components it currently listens to.
	 */
	public void detach() {
		setEnabled(false);
		for (int i=componentList.size()-1; i>=0; --i) {
			final Component component = componentList.get(i);
			component.removeKeyListener(keyListener);
			component.removeMouseListener(mouseListener);
			component.removeMouseMotionListener(motionListener);
		}
		componentList.clear();
		gridList.clear();
		frame = null;
	}

	/**
	 * Sets the active state handler
	 */
	protected void setActiveHandler(DnDStateBase newState) {
		if (null==newState) {
			newState = idleHandler;
		}
		if (activeHandler!=newState) {
			activeHandler = newState;
			setCursor(activeHandler.getActiveCursor());
		}
	}

	/**
	 * Cancel the drag and drop operation currently underway.
	 */
	@Override
	public void cancelDrag() {
		activeHandler.cancelDrag();
	}

	/**
	 * Cancel drag if working with the specified element.
	 */
	@Override
	public void cancelDrag(final GridElement element) {
		if (element != null) {
			activeHandler.cancelDrag(element);
			// also cancel for all child elements...
			final Iterator<GridElement> i = element.newChildIterator();
			while (i.hasNext())
				cancelDrag(i.next());
		}
	}
	
	/**
	 * Sets the current mouse cursor
	 */
	@Override
	protected final void setCursor(Object cursorId) {
		// if currently disabled, ignore the supplied cursor
		// and use the default cursor instead!
		if (!isEnabled())
			cursorId = idleHandler.getActiveCursor();
		if (null==cursorId)
			cursorId = activeHandler.getActiveCursor();
		if (!cursorId.equals(this.activeCursorId)) {
			this.activeCursorId = cursorId;
			CursorUtil.setCursor(this.frame, this.activeCursorId);
		}
	}

	/**
	 * Gets the grid that caused the mouse event, or null if
	 * the event originated from any other component.
	 * 
	 * If the event originated from a grid, the grids screen
	 * coordinates are automatically updated.
	 */
	@Override
	protected final Grid getGrid(MouseEvent event) {
		final int mx = event.getXOnScreen();
		final int my = event.getYOnScreen();
		for (int i=gridList.size()-1; i>=0; --i) {
			final Grid grid = gridList.get(i);
			final Component gridComponent = (Component) grid;
			if (!gridComponent.isVisible() || !gridComponent.isShowing() || !gridComponent.isEnabled())
				continue;
			grid.updateScreenPosition();
			int x = grid.getXOnScreen();
			int y = grid.getYOnScreen();
			int w = grid.getWidth();
			int h = grid.getHeight();
			// if the grid is inside a scrollpane make sure
			// only the visible rect is used  
			final Container container = gridComponent.getParent();
			if (container instanceof JViewport) {
				final JViewport viewport = (JViewport) container;
				final Rectangle rect = viewport.getVisibleRect();
				final Point point = new Point(rect.x, rect.y);
				SwingUtilities.convertPointToScreen(point, viewport);
				x = point.x;
				y = point.y;
				w = rect.width;
				h = rect.height;
			}
			if (mx<x || my<y)
				continue;
			if (mx>x+w || my>y+h)
				continue;
			return grid;
		}
		return null;
	}
	
	public OverlayedFrame getFrame() {
		return this.frame;
	}

}

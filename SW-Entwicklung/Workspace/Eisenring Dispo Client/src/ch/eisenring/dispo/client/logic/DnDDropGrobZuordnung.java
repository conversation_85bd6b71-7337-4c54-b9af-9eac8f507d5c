package ch.eisenring.dispo.client.logic;

import static ch.eisenring.dispo.shared.codetables.EmploymentRole.MITARBEITER_CHAUFEUR;
import static ch.eisenring.dispo.shared.codetables.ZuordnungCode.ZUORDNUNG_FAHRAUFTRAG;
import static ch.eisenring.dispo.shared.codetables.ZuordnungCode.ZUORDNUNG_MONTAGE;
import static ch.eisenring.dispo.shared.codetables.ZuordnungCode.ZUORDNUNG_OVERFLOW;
import static ch.eisenring.dispo.shared.codetables.ZuordnungCode.ZUORDNUNG_ZUSATZ;
import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.client.gui.dnd.DnDEvent;
import ch.eisenring.dispo.client.gui.dnd.DnDHintDrop;
import ch.eisenring.dispo.client.gui.grid.DSPGridLayout;
import ch.eisenring.dispo.client.gui.grid.GridElement;
import ch.eisenring.dispo.shared.model.AbstractMitarbeiter;
import ch.eisenring.dispo.shared.model.AbstractModel;
import ch.eisenring.dispo.shared.model.Auftrag;
import ch.eisenring.dispo.shared.model.FeinZuordnung;
import ch.eisenring.dispo.shared.model.GrobZuordnung;
import ch.eisenring.dispo.shared.model.Mitarbeiter;
import ch.eisenring.dispo.shared.model.OverflowAuftrag;
import ch.eisenring.logiware.code.soft.LWDisponentSirCode;

/**
 * Handles drop of GrobZuordnung of any type
 */
public final class DnDDropGrobZuordnung extends DnDHintDrop {

	public final static DnDDropGrobZuordnung INSTANCE = new DnDDropGrobZuordnung();
	
	private DnDDropGrobZuordnung() {
	}

	/**
	 * GrobZuordnung supports the following drop modes:
	 * 1.) Move (drop on source line)
	 * 2.) Copy (drop on source line, while holding CTRL)
	 * 3.) Morph Copy (drop on Employee)
	 */
	@Override
	public GridElement getDropDelegate(final Client client, final DnDEvent event) {
		// depending on where the element is dropped, a copy is required
		final GridElement thisElement = event.getDroppedElement();
		final GridElement targetElement = event.getTargetDropDelegate();
		final AbstractModel targetModel = targetElement.getModel();
		final DSPGridLayout layout = targetElement.getLayout();
		
		if (targetModel instanceof Mitarbeiter) {
			// create FeinZuordnung from dropped GrobZuordnung
			final Mitarbeiter mitarbeiter = (Mitarbeiter) targetModel;
			final GrobZuordnung source = (GrobZuordnung) thisElement.getModel();
			final Auftrag auftrag = source.getAuftrag();
			final FeinZuordnung copy = new FeinZuordnung(mitarbeiter.getContext());
			copy.setCreatedBy(client.getCurrentUserName());
			copy.setLabel(source.getLabel());
			copy.setMitarbeiter(mitarbeiter);
			copy.setAuftrag(auftrag);
			copy.setAuftragNummer(source.getAuftragNummer());
			copy.setAuftragGSE(source.getAuftragGSE());

			// --- assign disponent, if no disponent yet assigned
			if (auftrag != null && AbstractCode.isNull(auftrag.getDisponent())) {
				// set disponent on auftrag
				LWDisponentSirCode disponent = client.getCurrentDisponent();
				if (disponent != null && !LWDisponentSirCode.NULL.equals(disponent)) {
					auftrag.setDisponent(disponent);
				}
			}
			
			// --- determine type of FeinPlanung
			if (MITARBEITER_CHAUFEUR.equals(mitarbeiter.getEmploymentRole())) {
				copy.setType(ZUORDNUNG_FAHRAUFTRAG);
			} else if (auftrag == null) {
				copy.setType(ZUORDNUNG_OVERFLOW);
			} else {
				boolean zusatz = auftrag.getZuordnungCount(ZUORDNUNG_MONTAGE) > 0;
				zusatz ^= event.isALTDown();
				// create Fein- or Zusatzplanung
				if (zusatz) {
					copy.setType(ZUORDNUNG_ZUSATZ);
				} else {
					copy.setType(ZUORDNUNG_MONTAGE);
					auftrag.setChefMonteur(mitarbeiter);
				}
			}
			copy.setRange(layout.getCellBeginTimestamp(event.getDropCellStart()),
					layout.getCellEndTimestamp(event.getDropCellEnd()));
			return new FeinZuordnungBlock(copy);
		} else if (isCopyDrop(event) &&
				(targetModel instanceof Auftrag || targetModel instanceof OverflowAuftrag)) {
			// drag-copy GrobZuordnung
			final GrobZuordnung source = (GrobZuordnung) thisElement.getModel();
			final GrobZuordnung copy = new GrobZuordnung(targetModel.getContext());
			copy.setCreatedBy(client.getCurrentUserName());
			copy.setAuftragNummer(source.getAuftragNummer());
			copy.setAuftragGSE(source.getAuftragGSE());
			copy.setBemerkungen(source.getBemerkungen());
			copy.setColor(source.getColor());
			copy.setLabel(source.getLabel());
			copy.setParent(source.getParent());
			copy.setRange(layout.getCellBeginTimestamp(event.getDropCellStart()),
					layout.getCellEndTimestamp(event.getDropCellEnd()));
			return new GrobZuordnungBlock(copy);
		}
		return thisElement;
	}

	@Override
	public boolean isCopyDrop(final DnDEvent event) {
		final GridElement targetElement = event.getTargetDropDelegate();
		final AbstractModel targetModel = targetElement.getModel();
		if (targetModel instanceof AbstractMitarbeiter) {
			// Morph copy to FeinZuordnung
			return true;
		} else if (event.isCTRLDown()) {
			// Simple copy
			return true;
		}
		return false;
	}

}

package ch.eisenring.dispo.client.action;

import ch.eisenring.core.observable.Observable;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.client.gui.ObjektbetreuerFilter;

public final class ActionNoFilterObjektbetreuer extends AbstractDSPAction {

	private final Observable<String> source;
	
	public ActionNoFilterObjektbetreuer(final Client client,
			final Observable<String> labelObservable,
			final Observable<String> sourceObservable) {
		super(client, labelObservable.get());
		this.source = sourceObservable;
	}

	@Override
	protected void performAction() {
		final String settings = ObjektbetreuerFilter.get(source);
		client.OBJEKTBETREUER_FILTER_SETTINGS.set(settings);
	}

}

package ch.eisenring.dispo.client.gui.employee;

import heag.dsp.pub.codetables.BewertungKategorieCode;
import heag.dsp.pub.model.DSPMontageBewertung;

import java.awt.Font;
import java.awt.GridBagLayout;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.util.Calendar;
import java.util.Date;

import javax.swing.JComponent;

import org.jfree.chart.ChartFactory;
import org.jfree.chart.ChartPanel;
import org.jfree.chart.JFreeChart;
import org.jfree.chart.axis.CategoryAxis;
import org.jfree.chart.axis.ValueAxis;
import org.jfree.chart.plot.CategoryPlot;
import org.jfree.chart.plot.PlotOrientation;
import org.jfree.data.category.CategoryDataset;
import org.jfree.data.category.DefaultCategoryDataset;

import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.collections.Collection;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.datatypes.date.HEAGCalendar;
import ch.eisenring.core.datatypes.date.Period;
import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.dispo.client.resource.imgnew.Images;
import ch.eisenring.dispo.shared.model.AbstractMitarbeiter;
import ch.eisenring.gui.components.HEAGCodeComboBox;
import ch.eisenring.gui.components.HEAGDateField;
import ch.eisenring.gui.components.HEAGFloatingButton;
import ch.eisenring.gui.components.HEAGLabel;
import ch.eisenring.gui.components.HEAGPanel;
import ch.eisenring.gui.util.LayoutUtil;
import ch.eisenring.logiware.code.soft.LWMonteurSirCode;

@SuppressWarnings("serial")
public class BewertungGraphPanel extends HEAGPanel {

	public final static double NORMAL_SCALE = 100D;
	
	protected List<DSPMontageBewertung> bewertungen = new ArrayList<>();

	protected final HEAGFloatingButton btnRefresh = new HEAGFloatingButton(Images.REFRESH, 16);
	protected final HEAGDateField datFrom = new HEAGDateField();
	protected final HEAGDateField datUpto = new HEAGDateField();
	protected final HEAGCodeComboBox<BewertungKategorieCode> cmbCategory =
		new HEAGCodeComboBox<BewertungKategorieCode>();
	
	{ // initialize GUI components
		@SuppressWarnings("unchecked")
		final List<BewertungKategorieCode> l = (List) AbstractCode.getInstances(BewertungKategorieCode.class, AbstractCode.Order.Id);
		cmbCategory.populate(Collection.toArray(l, BewertungKategorieCode.class), true);
		final HEAGCalendar c = HEAGCalendar.obtainNow();
		c.set(Calendar.MONTH, 0);
		c.set(Calendar.DAY_OF_MONTH, 1);
		c.set(Calendar.HOUR_OF_DAY, 0);
		c.set(Calendar.MINUTE, 0);
		c.set(Calendar.SECOND, 0);
		c.set(Calendar.MILLISECOND, 0);
		datFrom.setValue(c.getTime());
		c.set(Calendar.MONTH, 11);
		c.set(Calendar.DAY_OF_MONTH, 31);
		c.set(Calendar.HOUR_OF_DAY, 23);
		c.set(Calendar.MINUTE, 59);
		c.set(Calendar.SECOND, 59);
		c.set(Calendar.MILLISECOND, 0);
		datUpto.setValue(c.getTime());
		c.release();
	}
	
	protected LWMonteurSirCode monteur;
	protected JFreeChart chart;

	protected final ActionListener refreshListener = new ActionListener() {
		@Override
		public void actionPerformed(final ActionEvent event) {
			updateChart();
		}
	};

	public BewertungGraphPanel() {
		initComponents();
		initLayout(null);
	}

	public void setData(
			final LWMonteurSirCode monteur,
			final java.util.Collection<DSPMontageBewertung> bewertungen) {
		this.bewertungen.clear();
		this.bewertungen.addAll(bewertungen);
		updateChart();
	}

	private void initComponents() {
		btnRefresh.setToolTipText("Grafik aktualisieren");
		btnRefresh.addActionListener(refreshListener);
	}

	private void initLayout(final CategoryDataset dataSet) {
		removeAll();
		setLayout(new GridBagLayout());
		final LayoutUtil l = new LayoutUtil(8);
		
		add(new HEAGLabel("Bereich"), l.label());
		add(cmbCategory, l.fixed(1));
		add(new HEAGPanel(), l.field(1));
		add(new HEAGLabel("Zeitraum von"), l.label());
		add(datFrom, l.fixed(1));
		add(new HEAGLabel("bis"), l.label());
		add(datUpto, l.fixed(1));
		add(btnRefresh, l.button());

		final JComponent chartComponent;
		if (dataSet == null) {
			chartComponent = new HEAGPanel();
		} else {
			String title = AbstractCode.getLongText(monteur, "???") + " (" +
				TimestampUtil.DATE10.format(datFrom.getTimestamp()) + " - " +
				TimestampUtil.DATE10.format(datUpto.getTimestamp()) +
				" )\n(Ausgewertet am: " +
				TimestampUtil.DATE10.format(System.currentTimeMillis()) + ")";
			chart = ChartFactory.createBarChart(
					title, // title
					null, // X
					null, // Y
					dataSet,
					PlotOrientation.VERTICAL, 
					true, 
					true,
					false); 
	
			final CategoryPlot plot = chart.getCategoryPlot();
			final CategoryAxis xAxis = plot.getDomainAxis();
			final ValueAxis yAxis = plot.getRangeAxis();
			xAxis.setTickLabelFont(new Font("sans serif", 0, 10));
			yAxis.setRange(0D,  NORMAL_SCALE);
			//axis.setCategoryLabelPositions(CategoryLabelPositions.UP_90);
			chartComponent = new ChartPanel(chart);
			
		}
		
		add(chartComponent, l.panel(8, 10, 10));
	}

	protected void updateChart() {
		updateChart(
				cmbCategory.getSelectedCode(),
				datFrom.getDate(), datUpto.getDate());
	}

	@Override
	public void updateView(final Object model) {
		super.updateView(model);
		if (model instanceof AbstractMitarbeiter) {
			monteur = ((AbstractMitarbeiter) model).getMonteurSirCode();
		}
		updateChart();
	}

	private void updateChart(final BewertungKategorieCode category,
            final Date dFrom, final Date dUpto) {
		if (AbstractCode.isNull(category) || dFrom == null || dUpto == null) {
			initLayout(null);
			return;
		}
		// initialize accumulators
		final List<BewertungCategoryAccumulator> accumulators = new ArrayList<BewertungCategoryAccumulator>(5);
		if (BewertungKategorieCode.ALL.equals(category)) {
			accumulators.add(new BewertungCategoryAccumulator(BewertungKategorieCode.ARBEIT));
			accumulators.add(new BewertungCategoryAccumulator(BewertungKategorieCode.SAUBERKEIT));
			accumulators.add(new BewertungCategoryAccumulator(BewertungKategorieCode.AUFTRITT));
			accumulators.add(new BewertungCategoryAccumulator(BewertungKategorieCode.ADMINISTRATION));
			accumulators.add(new BewertungCategoryAccumulator(BewertungKategorieCode.BAULEITING));
		} else {
			accumulators.add(new BewertungCategoryAccumulator(category));
		}
		
		// filter bewertungen
		final Period period = Period.create(TimestampUtil.toTimestamp(dFrom), TimestampUtil.toTimestamp(dUpto));
		for (final DSPMontageBewertung bewertung : bewertungen) {
			final long timestamp = bewertung.getTimestamp();
			if (!period.intersects(timestamp))
				continue;
			for (final BewertungCategoryAccumulator accumulator : accumulators) {
				accumulator.add(bewertung);
			}
		}

		// create and populate data set
		final DefaultCategoryDataset dataSet = new DefaultCategoryDataset();
		for (final BewertungCategoryAccumulator accumulator : accumulators) {
			accumulator.addToDatasetNormalized(dataSet, NORMAL_SCALE);
		}
		
		initLayout(dataSet);
		validate();
	}
	
}

package ch.eisenring.dispo.client.gui.components.modelpopup;

import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.dispo.shared.model.Auftrag;

public class AuftragInfoText extends ModelInfoTextBase {

	protected AuftragInfoText(final InfoPopupRequest request) {
		super(request);
	}
	
	@Override
	protected void buildInfoText(final StringMaker builder) {
		final Auftrag auftrag = (Auftrag) model;
		appendAuftragInfo(builder, auftrag);
	}

}

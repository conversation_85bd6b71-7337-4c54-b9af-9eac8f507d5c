package ch.eisenring.dispo.client.remotecontrol;

import ch.eisenring.dispo.client.Client;

public class RemoteControlHandler {

	private final static Object LOCK = new Object();
	private final Client client;
	
	public RemoteControlHandler(final Client client) {
		this.client = client;
	}
	
	/**
	 * Checks if an open command for the given dialog is pending.
	 * If the dialog id matches, an object id is returned and
	 * the pending command is cleared.
	 * If the dialog id doesn't match null is returned and nothing
	 * is changed.
	 */
	public Long getAutoOpenObjectId(final String dialogId) {
		synchronized (LOCK) {
			final Object dlgId = client.STATE_AUTO_OPEN_DIALOG.get();
			if (dlgId == null || !dlgId.equals(dialogId)) {
				return null;
			}
			final Object objectId = client.STATE_AUTO_OPEN_OBJECT.get();
			if (objectId instanceof Long) {
				clearAutoOpenCommands();
				return (Long) objectId;
			}
			return null;
		}
	}

	public Long peekAutoOpenObjectId(final String dialogId) {
		synchronized (LOCK) {
			final Object dlgId = client.STATE_AUTO_OPEN_DIALOG.get();
			if (dlgId == null || !dlgId.equals(dialogId)) {
				return null;
			}
			final Object objectId = client.STATE_AUTO_OPEN_OBJECT.get();
			return (objectId instanceof Long) ? (Long) objectId : null;
		}
	}

	/**
	 * Add an auto open command
	 */
	public void addAutoOpenCommand(final String dialogId, final Long objectId) {
		synchronized (LOCK) {
			if (objectId == null || dialogId == null) {
				clearAutoOpenCommands();
			} else {
				client.STATE_AUTO_OPEN_OBJECT.set(objectId);
				client.STATE_AUTO_OPEN_DIALOG.set(dialogId);
			}
		}
	}

	/**
	 * Clears any pending auto open commands
	 */
	public void clearAutoOpenCommands() {
		synchronized (LOCK) {
			client.STATE_AUTO_OPEN_OBJECT.set((Long) null);
			client.STATE_AUTO_OPEN_DIALOG.set((String) null);
		}
	}

}

package ch.eisenring.dispo.client.gui;

import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.dispo.shared.model.AbstractModel;
import ch.eisenring.dispo.shared.model.Auftrag;
import ch.eisenring.dispo.shared.model.GrobZuordnung;

public abstract class AutoOpenEditHint {

	protected AutoOpenEditHint() {
	}
	
	public static AutoOpenEditHint createByAuftragNummer(int auftragNummer) {
		return new AutoOpenByAuftragNr(auftragNummer);
	}

	public static AutoOpenEditHint createByObjectId(Long objectId) {
		return new AutoOpenByObjectId(objectId);
	}

	public abstract boolean matches(final AbstractModel model);

}

final class AutoOpenByAuftragNr extends AutoOpenEditHint {
	
	private final int auftragNummer;
	
	public AutoOpenByAuftragNr(final int auftragNummer) {
		this.auftragNummer = auftragNummer;
	}

	@Override
	public String toString() {
		return Strings.concat("Auftrag #", auftragNummer);
	}

	@Override
	public boolean matches(final AbstractModel model) {
		if (!(model instanceof GrobZuordnung))
			return false;
		final GrobZuordnung z = (GrobZuordnung) model;
		final Auftrag a = z.getAuftrag();
		return a != null && a.getAuftragnummer() == this.auftragNummer;
	}

}

final class AutoOpenByObjectId extends AutoOpenEditHint {
	
	private final Long objectId;
	
	public AutoOpenByObjectId(final Long objectId) {
		this.objectId = objectId;
	}
	
	@Override
	public String toString() {
		return Strings.concat("Object #", objectId);
	}

	@Override
	public boolean matches(final AbstractModel model) {
		return model != null && this.objectId.equals(model.getId());
	}

}
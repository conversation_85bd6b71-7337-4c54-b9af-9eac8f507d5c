package ch.eisenring.dispo.client.gui.abruf;

import java.io.IOException;
import java.io.InputStream;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;

import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.Map;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.collections.impl.HashMap;
import ch.eisenring.core.csv.CSVFile;
import ch.eisenring.core.csv.CSVLine;
import ch.eisenring.core.csv.CSVReader;
import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.io.Streams;
import ch.eisenring.core.io.binary.BinaryHolder;
import ch.eisenring.core.io.binary.BinaryHolderUtil;
import ch.eisenring.core.io.memory.MemoryOutputStream;
import ch.eisenring.core.util.file.FileImage;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.shared.codetables.AbrufModus;
import ch.eisenring.dispo.shared.lw.LWBestellInfo;
import ch.eisenring.dispo.shared.network.packets.PacketAbrufInfoBase.AbrufProjektInfo;
import ch.eisenring.dispo.shared.network.packets.PacketAbrufInfoReply;
import ch.eisenring.dispo.shared.network.packets.PacketAbrufInfoRequest;
import ch.eisenring.logiware.util.ProjektBezeichnung;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

class Terminierung {

	static class Recipients {
		public final String to;
		public final String cc;
		
		public Recipients(final String to, final String cc) {
			this.to = to;
			this.cc = cc;
		}

		@Override
		public int hashCode() {
			return Strings.hashCode(to) ^ Strings.hashCode(cc);
		}
	
		@Override
		public boolean equals(final Object o) {
			if (!(o instanceof Recipients))
				return false;
			final Recipients r = (Recipients) o;
			return Strings.equals(r.to, to) && Strings.equals(r.cc, cc);
		}
	}

	public long callForTimestamp = TimestampUtil.NULL_TIMESTAMP;
	public List<LWBestellInfo> bestellInfos = new ArrayList<>();
	public Recipients recipients;
	public String contact;
	public String contactMobile;
	public String gpsKoordinaten;
	public String lieferantenbezeichnung;
	public String time;
	public String constraint;
	
	public static List<Terminierung> splitByRecipients(final Client client, final List<LWBestellInfo> bestellInfos, final long callForTimestamp) {
		final Map<Recipients, Terminierung> result = new HashMap<>();
		final CSVFile dataFile = getDataFile(client);
		for (final LWBestellInfo bestellInfo : bestellInfos) {
			final Recipients recipients = determineRecipients(dataFile, bestellInfo);
			Terminierung terminierung = result.get(recipients);
			if (terminierung == null) {
				terminierung = new Terminierung();
				terminierung.recipients = recipients;
				terminierung.lieferantenbezeichnung = bestellInfo.getLieferantenBezeichnung();
//				terminierung.contact = bestellInfo.
				if (TimestampUtil.isNull(callForTimestamp)) {
					terminierung.callForTimestamp = bestellInfo.getWunschDatum();
				} else {
					terminierung.callForTimestamp = callForTimestamp;
				}
				result.put(terminierung.recipients, terminierung);
			}
			terminierung.bestellInfos.add(bestellInfo);
		}
		
		// fetch additional data
		final PacketAbrufInfoRequest request = PacketAbrufInfoRequest.create();
		final List<Terminierung> terminierungen = new ArrayList<>(result.values());
		for (final Terminierung terminierung : terminierungen) {
			final LWBestellInfo bestellInfo = terminierung.bestellInfos.get(0);
			request.addProjektKey(bestellInfo.getProjektKey());
		}
		final PacketAbrufInfoReply reply = (PacketAbrufInfoReply) client.sendAndWait(request);
		if (!reply.isValid())
			throw new RuntimeException(reply.getMessage().getText());
		for (final Terminierung terminierung : terminierungen) {
			final LWBestellInfo bestellInfo = terminierung.bestellInfos.get(0);
			final AbrufProjektInfo info = reply.getAbrufInfo(bestellInfo.getProjektKey());
			terminierung.contact = info.monteurName;
			terminierung.gpsKoordinaten = info.gpsKoordinaten;
			terminierung.contactMobile = info.monteurNatel;
		}
		return terminierungen;
	}

	private static CSVFile getDataFile(final Client client) {
		try {
			final InputStream input = client.getDataFileSource().getAsStream("dsp/misc-data/Lieferanten-Email.csv");
			final CSVFile csvFile = CSVReader.readCSV(input, ';');
			return csvFile;
		} catch (final Exception e) {
			throw new RuntimeException(e.getMessage(), e);
		}
	}

	public static Recipients determineRecipients(final CSVFile dataFile, final LWBestellInfo bestellInfo) {
		String resultTo = null;
		String resultCc = null;
		// determine email addresses
		for (int i=dataFile.getLineCount()-1; i>0; --i) {
			final CSVLine csvLine = dataFile.getLine(i);
			final String lieferant = Strings.clean(csvLine.getColumn(0));
			if (lieferant != null && Strings.contains(bestellInfo.getLieferantenBezeichnung(), lieferant)) {
				final String to = Strings.clean(csvLine.getColumn(1));
				if (to != null)
					resultTo = to;
				final String cc = Strings.clean(csvLine.getColumn(2));
				if (cc != null)
					resultCc = cc;
			}
		}
		return new Recipients(resultTo, resultCc);
	}

	public FileImage createXLSX(final Client client, final AbrufModus modus) throws IOException {
		Workbook workbook = null;
		{
			final BinaryHolder template = client.getDataFile("dsp/misc-data/Abrufliste-Template.xlsx", false);
			if (template == null)
				throw new IOException("Template not available");
			InputStream templateInput = null;
			try {
				templateInput = template.getInputStream();
				workbook = new XSSFWorkbook(templateInput);
			} finally {
				Streams.closeSilent(templateInput);
			}
		}
		final Sheet sheet = workbook.getSheetAt(0);
		int rowIndex = 0;
		for (final LWBestellInfo bestellInfo : bestellInfos) {
			// locate or create row in XLS
			++rowIndex;
			Row row = sheet.getRow(rowIndex);
			if (row == null)
				row = sheet.createRow(rowIndex);
			// fill the row with data
			fillRow(client, row, bestellInfo);
		}
		// create new XLS
		final MemoryOutputStream memOut = new MemoryOutputStream();
		try {
			workbook.write(memOut);
			memOut.close();
			final BinaryHolder newBinary = BinaryHolderUtil.create(memOut, true);
			return FileImage.create(modus.getXLSName(), newBinary);
		} finally {
			Streams.closeSilent(memOut);
		}		
	}

	private void fillRow(final Client client, final Row row, final LWBestellInfo bestellInfo) {
		final ProjektBezeichnung bezeichnung = new ProjektBezeichnung(bestellInfo.getProjektBezeichnung());
		getCell(row, 0).setCellValue(bestellInfo.getBestellNummer());
		getCell(row, 2).setCellValue("Hans Eisenring AG");
		getCell(row, 3).setCellValue(
				bestellInfo.getProjektNummer() + "_" + bezeichnung.getOrt() + " "
				+ bezeichnung.getPLZ() + " " + bezeichnung.getStrasse());
		//getCell(row, 5).setCellValue("Alter Liefertermin");
		getCell(row, 6).setCellValue(TimestampUtil.DATE10.format(callForTimestamp));

		final String time = Strings.clean(this.time);
		getCell(row, 9).setCellValue(time == null ? "-" : time);
		
		getCell(row, 11).setCellValue(bezeichnung.getStrasse());
		getCell(row, 12).setCellValue(bezeichnung.getPLZ());
		getCell(row, 13).setCellValue(bezeichnung.getOrt());
		getCell(row, 14).setCellValue(Strings.trim(gpsKoordinaten));
		getCell(row, 15).setCellValue(Strings.trim(contact));
		getCell(row, 16).setCellValue(Strings.trim(contactMobile));
		getCell(row, 17).setCellValue(Strings.trim(constraint));
	}

	private static Cell getCell(final Row row, final int cellIndex) {
		Cell cell = row.getCell(cellIndex);
		if (cell == null)
			cell = row.createCell(cellIndex);
		return cell;
	}

}

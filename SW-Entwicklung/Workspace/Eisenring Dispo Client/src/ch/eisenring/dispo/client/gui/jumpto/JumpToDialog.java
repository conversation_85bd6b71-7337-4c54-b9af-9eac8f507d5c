package ch.eisenring.dispo.client.gui.jumpto;

import static ch.eisenring.dispo.client.DSPClientConstants.LUS_POS_JUMPTOWINDOW;

import java.awt.BorderLayout;
import java.awt.Dialog;

import ch.eisenring.core.tag.TagSet;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.client.gui.components.dialog.AbstractDSPDialog;
import ch.eisenring.dispo.client.help.HelpUtil;
import ch.eisenring.gui.resource.images.Images;
import ch.eisenring.gui.window.WindowTags;

@SuppressWarnings("serial")
public class JumpToDialog extends AbstractDSPDialog {

	private final JumpToPanel panel = new JumpToPanel(this);
	private final HelpUtil helpUtil;
	
	public JumpToDialog(final Client client) {
		super(client, new TagSet(
				WindowTags.POSITION_SETTINGS_ID, LUS_POS_JUMPTOWINDOW,
				WindowTags.LAYOUTMANAGER, new BorderLayout(),
				WindowTags.ICON, Images.CALENDAR,
				WindowTags.TITLE, "Sprung zu..."));
		this.helpUtil = new HelpUtil(client, "SprungZu", this);
		initLayout();
		pack();
		setResizable(false);
		setModalityType(Dialog.ModalityType.APPLICATION_MODAL);
	}

	private void initLayout() {
		getContentPane().removeAll();
		add(panel, BorderLayout.CENTER);
		helpUtil.attachListener(this);
	}

}

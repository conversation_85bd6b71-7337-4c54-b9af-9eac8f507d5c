package ch.eisenring.dispo.client.gui.auftrag;

import java.awt.GridBagLayout;

import javax.swing.JScrollPane;
import javax.swing.ScrollPaneConstants;

import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.shared.model.Auftrag;
import ch.eisenring.gui.GridBagConstraints;
import ch.eisenring.gui.components.HEAGTable;

@SuppressWarnings("serial")
public final class AusstandPanel extends AuftragDetailPanel {

	private final JScrollPane scrollPane = new JScrollPane();
	private final PositionTableModel tableModel = new PositionTableModel(true);
	private final HEAGTable table = new HEAGTable(tableModel);

	public AusstandPanel(final Client client) {
		super(client);
		initComponents();
		initLayout();
	}

	private void initComponents() {
		setLayout(new GridBagLayout());
		scrollPane.setVerticalScrollBarPolicy(ScrollPaneConstants.VERTICAL_SCROLLBAR_ALWAYS);
		scrollPane.setHorizontalScrollBarPolicy(ScrollPaneConstants.HORIZONTAL_SCROLLBAR_AS_NEEDED);
		scrollPane.setViewportView(table);
		tableModel.applyLayout(table);
	}

	private void initLayout() {
		removeAll();
		add(scrollPane, GridBagConstraints.area(0, 0));
	}

	// --------------------------------------------------------------
	// ---
	// --- ModelView implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public void updateView(final Object model) {
		super.updateView(model);
		if (model instanceof Auftrag) {
			final Auftrag auftrag = (Auftrag) model;
			tableModel.setAuftrag(auftrag);
		}
	}

	// --------------------------------------------------------------
	// ---
	// --- HEAGPanelLazy implementation
	// ---
	// --------------------------------------------------------------
	protected boolean isChangedImpl() {
		// panel is read only
		return false;
	}

	@Override
	protected void updateEditableImpl() {
		// nothing (panel is read only)
	}

	@Override
	protected void showingFirstTimeImpl() {
		// nothing
	}

}

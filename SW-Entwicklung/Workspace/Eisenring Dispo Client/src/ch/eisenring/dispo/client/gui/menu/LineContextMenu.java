package ch.eisenring.dispo.client.gui.menu;

import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.datatypes.date.DateGranularityCode;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.resource.ImageResource;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.client.action.ActionBestaetigungMontageResourcen;
import ch.eisenring.dispo.client.action.ActionEmployeeFlag;
import ch.eisenring.dispo.client.action.ActionHighlightMitarbeiter;
import ch.eisenring.dispo.client.action.ActionMeldungAuftragsscheinTag;
import ch.eisenring.dispo.client.action.ActionMeldungAuftragsscheinWoche;
import ch.eisenring.dispo.client.codetables.EmployeeFlagCode;
import ch.eisenring.dispo.client.gui.grid.LineElement;
import ch.eisenring.dispo.shared.model.AbstractMitarbeiter;
import ch.eisenring.dispo.shared.model.AbstractModel;
import ch.eisenring.dispo.shared.model.MasterMitarbeiter;
import ch.eisenring.dispo.shared.model.Mitarbeiter;
import ch.eisenring.dispo.shared.model.ModelContext;
import ch.eisenring.dispo.shared.model.VirtualMitarbeiter;
import ch.eisenring.email.client.resource.images.Images;
import ch.eisenring.gui.action.AbstractAction;
import ch.eisenring.gui.action.ActionNoAction;
import ch.eisenring.gui.menu.Menu;
import ch.eisenring.gui.menu.MenuBuilder;
import ch.eisenring.tapi.client.ActionPhoneDial;

@SuppressWarnings("serial")
public class LineContextMenu extends ContextMenuBase {

	private final LineElement lineElement;
	
	public LineContextMenu(final Client client, final LineElement lineElement) {
		super(client);
		this.lineElement = lineElement;
		initItems();
	}

	private void initItems() {
		final AbstractModel model = lineElement.getModel();
		if (model instanceof Mitarbeiter) {
			final Mitarbeiter mitarbeiter = (Mitarbeiter) model;
			final MasterMitarbeiter master = getMasterMitarbeiter(mitarbeiter);
			if (master != null) {
				final String phoneMobile = master.getPhoneMobile();
				final String actionName = Strings.concat(master.getEmploymentRole().toString(), " anrufen");
				add(MenuBuilder.createItem(new ActionPhoneDial(actionName, phoneMobile)));
				add(getAuftragsscheinMenu(master, mitarbeiter, false));
				add(getAuftragsscheinMenu(master, mitarbeiter, true));
				add(MenuBuilder.createItem(new ActionBestaetigungMontageResourcen(client, master)));
			}
			getFlagMenu(client, mitarbeiter).addTo(this, "Markierung");
			add(MenuBuilder.createItem(new ActionHighlightMitarbeiter(client, mitarbeiter)));
		}
	}

	public Menu getAuftragsscheinMenu(final MasterMitarbeiter master,
									  final AbstractMitarbeiter mitarbeiter,
									  final boolean withDocuments) {
		final String menuName = withDocuments
				? "Tagesplan mailen (mit Dokumenten)"
				: "Wochenplan mailen (nur Planung)";
		final ImageResource icon = withDocuments ? Images.EMAIL_WITH_ATTACHMENT : Images.EMAIL;
		final AbstractAction menuAction = new ActionNoAction(menuName, icon, true); 
		final Menu result = new Menu(menuAction);
		long day = mitarbeiter.getContext().getBegin();
		for (int i=0; i<6; ++i) {
			final ActionMeldungAuftragsscheinTag action = new ActionMeldungAuftragsscheinTag(client, master, mitarbeiter, day, withDocuments);
			result.add(MenuBuilder.createItem(action));
			day = DateGranularityCode.DAY.round(day, 1);
		}
		if (!withDocuments) {
			result.addSeparator();
			result.add(MenuBuilder.createItem(new ActionMeldungAuftragsscheinWoche(client, master, mitarbeiter)));
		}
		return result;
	}

	public static MenuBuilder getFlagMenu(final Client client, final Mitarbeiter mitarbeiter) {
		final MenuBuilder b = new MenuBuilder();
		final List<EmployeeFlagCode> flags = AbstractCode.getInstances(
				EmployeeFlagCode.class, EmployeeFlagCode.Order.Color);
		for (final EmployeeFlagCode flag : flags) {
			b.add(new ActionEmployeeFlag(client, mitarbeiter, flag));
		}
//		
//		b.add(new ActionEmployeeFlag(client, mitarbeiter, 0x00));
//		b.add(new ActionEmployeeFlag(client, mitarbeiter, 0x0A));
//		b.add(new ActionEmployeeFlag(client, mitarbeiter, 0x05));
//		b.add(new ActionEmployeeFlag(client, mitarbeiter, 0x07));
//		b.add(new ActionEmployeeFlag(client, mitarbeiter, 0x06));
//		b.add(new ActionEmployeeFlag(client, mitarbeiter, 0x02));
//		b.add(new ActionEmployeeFlag(client, mitarbeiter, 0x08));
//		b.add(new ActionEmployeeFlag(client, mitarbeiter, 0x04));
//		b.add(new ActionEmployeeFlag(client, mitarbeiter, 0x09));
//		b.add(new ActionEmployeeFlag(client, mitarbeiter, 0x03));
//		b.add(new ActionEmployeeFlag(client, mitarbeiter, 0x01));
		return b;
	}

	public MasterMitarbeiter getMasterMitarbeiter(final AbstractMitarbeiter employee) {
		if (employee instanceof MasterMitarbeiter) {
			return (MasterMitarbeiter) employee;
		} else if (employee instanceof VirtualMitarbeiter) {
			return null;
		} else if (employee != null) {
			final ModelContext global = client.getContextCache().getGlobalContext();
			if (global != null) {
				final MasterMitarbeiter master = global.getMitarbeiter(employee.getPresentoId(), MasterMitarbeiter.class);
				return master;
			}
		}
		return null;
	}

}

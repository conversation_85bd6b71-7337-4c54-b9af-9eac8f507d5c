package ch.eisenring.dispo.client.gui.endmontage;

import java.awt.Dimension;

import ch.eisenring.core.tag.TagSet;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.client.gui.components.dialog.AbstractDSPDialog;
import ch.eisenring.dispo.shared.network.packets.PacketDMSEndmontageChoiceReply;
import ch.eisenring.gui.BorderLayout;
import ch.eisenring.gui.window.AbstractBaseWindow;
import ch.eisenring.gui.window.DialogTags;
import ch.eisenring.gui.window.WindowTags;
import heag.huo.client.resources.images.Images;

@SuppressWarnings("serial")
public final class EndmontageDossierDialog extends AbstractDSPDialog {

	final EndmontageDossierPanel dossierPanel;

	public EndmontageDossierDialog(final Client client, final PacketDMSEndmontageChoiceReply reply) {
		super(client, new TagSet(
				WindowTags.MINIMUM_SIZE, new Dimension(512, 384),
				WindowTags.TITLE, "Endmontagedossier drucken",
				WindowTags.ICON, Images.OPENFOLDER,
				WindowTags.POSITION_SETTINGS_ID, "EndmontageDossierPrintWindow",
				WindowTags.RESIZEABLE, WindowTags.RESIZEABLE_YES,
				WindowTags.LAYOUTMANAGER, new BorderLayout(),
				DialogTags.DIALOG_OWNER, client.getMainWindow()
			));
		this.dossierPanel = new EndmontageDossierPanel(client, reply);
		initComponents();
		initLayout();
	}
	
	private void initComponents() {
	}

	private void initLayout() {
		add(dossierPanel, BorderLayout.CENTER);
		pack();
	}

	public static void showDialog(final Client client, final PacketDMSEndmontageChoiceReply reply) {
		final EndmontageDossierDialog dialog = new EndmontageDossierDialog(client, reply);
		AbstractBaseWindow.showWindow(dialog);
	}

}

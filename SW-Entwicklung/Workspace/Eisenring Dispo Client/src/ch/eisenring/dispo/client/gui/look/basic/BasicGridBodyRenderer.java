package ch.eisenring.dispo.client.gui.look.basic;

import java.awt.Graphics2D;

import ch.eisenring.dispo.client.gui.grid.DSPGridLayout;
import ch.eisenring.dispo.client.gui.look.AbstractRenderer;
import ch.eisenring.dispo.client.gui.look.GridColorSet;
import ch.eisenring.dispo.client.gui.look.ILook;

public final class BasicGridBodyRenderer extends AbstractRenderer {

	protected BasicGridBodyRenderer(ILook look) {
		super(look);
	}

	/**
	 * Renders the complete grid 
	 */
	public void paintGrid(Graphics2D graphics, DSPGridLayout layout) {
		paintBackground(graphics, layout);
		paintSubColumns(graphics, layout);
		paintColumns(graphics, layout);
		paintLines(graphics, layout);
	}

	/**
	 * Renders the grid background
	 */
	public void paintBackground(Graphics2D graphics, DSPGridLayout layout) {
		final GridColorSet colorSet = layout.getColorSet();
		graphics.setPaintMode();
		graphics.setColor(colorSet.getColorBackground());
		graphics.fillRect(0, 0, layout.getWidth(), layout.getHeight());
	}

	/**
	 * Renders the column sub-raster
	 */
	public void paintSubColumns(Graphics2D graphics, DSPGridLayout layout) {
		final GridColorSet colorSet = layout.getColorSet();
		final int columnCount = layout.getColumnCount();
		final int cellCount = layout.getCellCountPerColumn();
		final int cellWidth = layout.getCellWidth();
		final int labelColumn = layout.getLabelColumn();
		final int h = layout.getHeight();
		graphics.setPaintMode();
		graphics.setColor(colorSet.getColorGridMinor());
		for (int columnIndex=0; columnIndex<columnCount; ++columnIndex) {
			if (labelColumn==columnIndex)
				continue;
			int x0 = layout.getColumnMinX(columnIndex);
			int x1 = x0 + cellWidth - 1;
			for (int cellIndex=0; cellIndex<cellCount; ++cellIndex) {
				graphics.drawLine(x0, 0, x0, h);
				graphics.drawLine(x1, 0, x1, h);
				x0 += cellWidth;
				x1 += cellWidth;
			}
		}
	}

	/**
	 * Renders the column raster
	 */
	public void paintColumns(final Graphics2D graphics, final DSPGridLayout layout) {
		final GridColorSet colorSet = layout.getColorSet();
		final int columnCount = layout.getColumnCount();
		final int h = layout.getHeight();
		graphics.setPaintMode();
		graphics.setColor(colorSet.getColorGridMajor());
		for (int columnIndex=0; columnIndex < columnCount; ++columnIndex) {
			final int x0 = layout.getColumnMinX(columnIndex);
			final int x1 = x0 + layout.getColumnWidth(columnIndex) - 1;
			graphics.drawLine(x0, 0, x0, h);
			graphics.drawLine(x1, 0, x1, h);
		}
	}
	
	/**
	 * Renders the line raster
	 */
	public void paintLines(Graphics2D graphics, DSPGridLayout layout) {
		final GridColorSet colorSet = layout.getColorSet();
		final int lineHeight = layout.getLineHeight();
		final int w = layout.getWidth();
		final int h = layout.getHeight();
		graphics.setPaintMode();
		graphics.setColor(colorSet.getColorGridMajor());
		int y = -1;
		while (y <= h) {
			graphics.fillRect(0, y, w, 2);
			y += lineHeight;
		}
	}

}

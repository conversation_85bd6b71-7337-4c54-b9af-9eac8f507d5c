package ch.eisenring.dispo.client.gui.components;

import java.awt.Color;
import java.awt.Font;
import java.awt.Graphics;
import java.awt.Graphics2D;
import java.awt.Point;
import java.awt.RenderingHints;

import javax.swing.BorderFactory;
import javax.swing.JPanel;
import javax.swing.SwingUtilities;
import javax.swing.border.Border;

import ch.eisenring.core.resource.ImageResource;
import ch.eisenring.core.util.GFXUtil;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.client.gui.grid.DSPGridLayout;
import ch.eisenring.dispo.client.gui.grid.Grid;
import ch.eisenring.dispo.client.gui.grid.GridElement;
import ch.eisenring.dispo.client.gui.grid.LineElement;
import ch.eisenring.dispo.client.gui.grid.highlight.ElementHighlight;
import ch.eisenring.dispo.client.gui.look.ILook;
import ch.eisenring.gui.util.GUIUtil;

@SuppressWarnings("serial")
public abstract class ImageGrid extends JPanel implements Grid {

	protected final Border border = BorderFactory.createEtchedBorder(); //new JButton().getBorder();
	protected final Client client;
	protected ImageResource icon;
	protected int iconSize = 32;
	protected String label;
	private int xOnScreen, yOnScreen;

	public ImageGrid(final Client client, final ImageResource icon) {
		this.client = client;
		this.icon = icon;
		setBorder(border);
	}
	
	@Override
	public void setBorder(final Border border) {
		super.setBorder(border);
		if (icon != null)
			GUIUtil.setComponentSizeForImage(this, icon.getIcon(iconSize));
	}

	protected final void setImage(final ImageResource icon) {
		this.icon = icon;
		repaint();
	}

	@Override
	protected void paintComponent(final Graphics g) {
		super.paintComponent(g);
		final Graphics2D g2d = (Graphics2D) g;
		if (icon != null) {
			// paint the image on top
			GFXUtil.iconBox(g2d, icon, iconSize, GFXUtil.CENTER, this);
		}
		if (label != null) {
			final ILook look = client.GRIDLOOK.get();
			if (look != null) {
				Font font = look.getDefaultFont();
				font = font.deriveFont(9F);
				final int h = font.getSize() + 6;
				final int y = getHeight() - h;
				g2d.setFont(font);
				g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
				look.text(g2d, label, 0, y, getWidth(), h, ILook.JUSTIFY_CENTER);
			}
		}
	}

	@Override
	public void updateScreenPosition() {
		final Point p = new Point(0, 0);
		SwingUtilities.convertPointToScreen(p, this);
		this.xOnScreen = p.x;
		this.yOnScreen = p.y;
	}

	@Override
	public int getXOnScreen() {
		return this.xOnScreen;
	}
	
	@Override
	public int getYOnScreen() {
		return this.yOnScreen;
	}
	
	@Override
	public void forceRepaint() {
		// dummy
	}

	@Override
	public int getLineIndex(LineElement element) {
		return -1;
	}

	@Override
	public void addGridElement(GridElement element) {
		// dummy
	}
	
	@Override
	public void removeGridElement(GridElement element) {
		// dummy
	}

	@Override
	public DSPGridLayout getGridLayout() {
		return null;
	}
	
	@Override
	public GridElement getElementForModelId(final Long modelId) {
		// not supported
		return null;
	}

	@Override
	public void requestPeriodicRepaint() {
		// no effect on this type
	}
	
	@Override
	public boolean needsPeriodicRepaint() {
		return false;
	}
	
	@Override
	public boolean isHighlightMode() {
		return false;
	}

	@Override
	public Color getHightlightingColor(GridElement model) {
		return ElementHighlight.UNRELATED;
	}

}

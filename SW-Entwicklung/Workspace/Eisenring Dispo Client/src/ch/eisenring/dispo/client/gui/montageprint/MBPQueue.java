package ch.eisenring.dispo.client.gui.montageprint;

import java.util.Iterator;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;

import ch.eisenring.app.shared.network.RPCContext;
import ch.eisenring.app.shared.network.RPCHandler;
import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.collections.Collection;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.core.observable.Observable;
import ch.eisenring.core.threading.ThreadCore;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.client.action.ActionMBPQueueDialog;
import ch.eisenring.dispo.shared.montageprint.MBPQueueStatusCode;
import ch.eisenring.dispo.shared.network.packets.PacketMBPPrintReply;
import ch.eisenring.dispo.shared.network.packets.PacketMBPPrintRequest;
import ch.eisenring.dispo.shared.pojo.mbp.MBPAuftrag;
import ch.eisenring.print.shared.model.PrintServiceIdentifier;

public class MBPQueue {

	/**
	 * Number of jobs to be printed concurrently.
	 */
	public final static int MAX_CONCURRENCY = 2;
	
	/**
	 * Controls how many jobs are enqueued (max.) before printing
	 * is artificially stalled.
	 */
	public final static int MAX_QUEUED_JOBS = 3;

	protected final Client client;

	protected final Object lock = new Object();
	
	protected final List<MBPAuftrag> elements = new ArrayList<>();

	/**
	 * Flag if daemon is running
	 */
	protected final AtomicBoolean running = new AtomicBoolean();
	
	/**
	 * Queue status
	 */
	protected final AtomicReference<MBPQueueStatusCode> status =
			new AtomicReference<MBPQueueStatusCode>(MBPQueueStatusCode.STOPPED);

	/**
	 * Number of concurrent active print jobs
	 */
	protected final AtomicInteger concurrency = new AtomicInteger(0);

	/**
	 * Printer used by the queue
	 */
	protected final AtomicReference<PrintServiceIdentifier> printer = new AtomicReference<PrintServiceIdentifier>();

	public MBPQueue(final Client client) {
		this.client = client;
	}

	protected Observable<?> getObservable() {
		return client.MBP_QUEUE;
	}

	// --------------------------------------------------------------
	// ---
	// --- Controls queue activity
	// ---
	// --------------------------------------------------------------
	public void setActive(final boolean active) {
		boolean altered = false;
		while (true) {
			final MBPQueueStatusCode expect = status.get();
			if (expect.isActive() == active)
				break;
			final MBPQueueStatusCode update = active ? MBPQueueStatusCode.IDLE : MBPQueueStatusCode.STOPPED;
			if (status.compareAndSet(expect, update)) {
				altered = true;
				break;
			}
		}
		if (active)
			startDaemon();
		if (altered)
			getObservable().fireValueChange();
	}
	
	protected void updateStatus(final MBPQueueStatusCode update) {
		boolean altered = false;
		while (true) {
			final MBPQueueStatusCode expect = status.get();
			if (expect.isActive() != update.isActive()) {
				break;
			} else if (status.compareAndSet(expect, update)) {
				altered = !expect.equals(update);
				break;
			}
		}
		if (altered)
			getObservable().fireValueChange();
	}

	public boolean isActive() {
		final MBPQueueStatusCode result = this.status.get();
		return result.isActive();
	}

	public MBPQueueStatusCode getStatus() {
		return status.get();
	}

	public PrintServiceIdentifier getPrinter() {
		return printer.get();
	}

	public void setPrinter(final PrintServiceIdentifier printer) {
		this.printer.set(printer);
	}

	public int getPendingCount() {
		int count = 0;
		for (final MBPAuftrag auftrag : getContents()) {
			switch (auftrag.getPrintStatus()) {
				case MBPAuftrag.PRINT_STATUS_PENDING:
				case MBPAuftrag.PRINT_STATUS_PRINTING:
					++count;
					break;
			}
		}
		return count;
	}

	// --------------------------------------------------------------
	// ---
	// --- Controls queue activity
	// ---
	// --------------------------------------------------------------
	private void startDaemon() {
		if (!running.compareAndSet(false, true))
			return;
		// start daemon thread
		final Runnable runnable = new Runnable() {
			@Override
			public void run() {
				daemonLoop();
			}
		};
		client.getCore().getThreadPool().start(runnable, "Batchdruck-Daemon", Thread.NORM_PRIORITY + 2);
	}
	
	protected void daemonLoop() {
		while (running.get()) {
			final MBPQueueStatusCode status = this.status.get();
			final boolean active = status.isActive();
			ThreadCore.sleep(333);
			// queue active?
			if (!active)
				continue;
			// maximum number of jobs active?
			if (concurrency.get() >= MAX_CONCURRENCY) {
				updateStatus(MBPQueueStatusCode.PRINTING);
				continue;
			}
			// printer available?
			final PrintServiceIdentifier printService = getPrinter();
			if (printService == null ||
				printService.getQueuedJobCount() > MAX_QUEUED_JOBS) {
				updateStatus(MBPQueueStatusCode.STALLED);
				continue;
			}
			// is a job available?
			final MBPAuftrag auftrag = getNextPending();
			if (auftrag == null) {
				updateStatus(MBPQueueStatusCode.IDLE);
				continue;
			}
			updateStatus(MBPQueueStatusCode.PRINTING);
			final RPCHandler rpcHandler = new RPCHandler() {
				@Override
				public void requestSent(final RPCContext rpcContext) {
					// don't care
				}
				@Override
				public void timeoutOccured(final RPCContext rpcContext) {
					setPrintStatus(auftrag, MBPAuftrag.PRINT_STATUS_FAILURE);
					concurrency.decrementAndGet();
					getObservable().fireValueChange();
				}
				@Override
				public void replyReceived(final RPCContext rpcContext) {
					try {
						final PacketMBPPrintReply reply = (PacketMBPPrintReply) rpcContext.getReply();
						if (!reply.isValid()) {
							client.message(reply.getMessage());
							setPrintStatus(auftrag, MBPAuftrag.PRINT_STATUS_FAILURE);
						} else {
							setPrintStatus(auftrag, MBPAuftrag.PRINT_STATUS_SUCCESS);
						}
					} catch (final Exception e) {
						Logger.error(e);
						client.message(new ErrorMessage(e));
						setPrintStatus(auftrag, MBPAuftrag.PRINT_STATUS_FAILURE);
					} finally {
						concurrency.decrementAndGet();
						getObservable().fireValueChange();
					}
				}
			};
			concurrency.incrementAndGet();
			final PacketMBPPrintRequest request = PacketMBPPrintRequest.create(auftrag, printer.get());
			client.sendPacket(rpcHandler, request);
		}
	}
	
	// --------------------------------------------------------------
	// ---
	// --- Queue content management
	// ---
	// --------------------------------------------------------------
	/**
	 * Add objects to the queue
	 */
	public void add(final Collection<MBPAuftrag> jobs) {
		if (jobs == null || jobs.isEmpty())
			return;
		boolean changed = false;
		synchronized (lock) {
			for (final MBPAuftrag job : jobs) {
				if (job == null || elements.contains(job))
					continue;
				elements.add(job);
				changed = true;
			}
		}
		// publish queue content changes
		if (changed) {
			getObservable().fireValueChange();
			// pop open the queue dialog
			final ActionMBPQueueDialog action = new ActionMBPQueueDialog(client);
			action.fire();
		}
	}

	/**
	 * Take first pending object from queue, returns NULL if queue is empty.
	 */
	public MBPAuftrag getNextPending() {
		MBPAuftrag result = null;
		synchronized (lock) {
			for (final MBPAuftrag auftrag : elements) {
				if (auftrag.getPrintStatus() == MBPAuftrag.PRINT_STATUS_PENDING) {
					result = auftrag;
					break;
				}
			}
		}
		setPrintStatus(result, MBPAuftrag.PRINT_STATUS_PRINTING);
		return result;
	}

	/**
	 * Sets the print status of a job
	 */
	public void setPrintStatus(final MBPAuftrag auftrag, final int printStatus) {
		if (auftrag == null)
			return;
		if (auftrag.getPrintStatus() == printStatus)
			return;
		auftrag.setPrintStatus(printStatus);
		getObservable().fireValueChange();
	}

	/**
	 * Removes all jobs that are complete
	 */
	public void cleanUp() {
		boolean result = false;
		synchronized (lock) {
			final Iterator<MBPAuftrag> i = elements.iterator();
			while (i.hasNext()) {
				final MBPAuftrag auftrag = i.next();
				switch (auftrag.getPrintStatus()) {
					case MBPAuftrag.PRINT_STATUS_FAILURE:
					case MBPAuftrag.PRINT_STATUS_SUCCESS:
						i.remove();
						result = true;
						break;
				}
			}
		}
		if (result)
			getObservable().fireValueChange();
	}

	/**
	 * Gets current queue contents (a copy of)
	 */
	public List<MBPAuftrag> getContents() {
		final List<MBPAuftrag> result;
		synchronized (lock) {
			result = new ArrayList<MBPAuftrag>(elements);
		}
		return result;
	}

}

package ch.eisenring.dispo.client.gui.montageprint;

import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.dispo.shared.network.packets.PacketMBPSearchReply;
import ch.eisenring.dispo.shared.pojo.mbp.MBPAuftrag;
import ch.eisenring.gui.components.HEAGListTableModel;
import ch.eisenring.gui.components.HEAGTable;
import ch.eisenring.gui.components.HEAGTableLayout;
import ch.eisenring.gui.components.SortKey;

@SuppressWarnings("serial")
public class MBPAuftragTableModel extends HEAGListTableModel<MBPAuftrag> {

	public final static HEAGTableLayout LAYOUT1;
	static {
		final HEAGTableLayout l = new HEAGTableLayout("MBPAuftragTableLayout", 2, new SortKey(0));
		l.addColumn("Kom-Nr.", 56, MBPAuftrag.Order.Projektnummer);
		l.addColumn("Auftr-Nr.", 64, MBPAuftrag.Order.Auftragnummer);
		l.addColumn("Basis", 56, MBPAuftrag.Order.Basisnummer);
		l.addColumn("GSE", 40, HEAGTable.Renderer.Centered, MBPAuftrag.Order.GSEAuftrag);
		l.addColumn("AWA", 40, HEAGTable.Renderer.Centered, MBPAuftrag.Order.Abwicklungsart);
		l.addColumn("Wu-Dat.", 64, HEAGTable.Renderer.Centered, MBPAuftrag.Order.Wunschdatum);
		l.addColumn("Kundenberater", 64, 32, 512, MBPAuftrag.Order.Kundenberater);
		l.addColumn("Bezeichnung", 128, 32, 512, MBPAuftrag.Order.Projektbezeichnung);
		LAYOUT1 = l;
	}

	public final static HEAGTableLayout LAYOUT2;
	static {
		final HEAGTableLayout l = new HEAGTableLayout("MBPAuftragTableLayout", 2, new SortKey(0));
		l.addColumn("Kom-Nr.", 56, MBPAuftrag.Order.Projektnummer);
		l.addColumn("Auftr-Nr.", 64, MBPAuftrag.Order.Auftragnummer);
		l.addColumn("Basis", 56, MBPAuftrag.Order.Basisnummer);
		l.addColumn("GSE", 40, HEAGTable.Renderer.Centered, MBPAuftrag.Order.GSEAuftrag);
		l.addColumn("AWA", 40, HEAGTable.Renderer.Centered, MBPAuftrag.Order.Abwicklungsart);
		l.addColumn("Wu-Dat.", 64, HEAGTable.Renderer.Centered, MBPAuftrag.Order.Wunschdatum);
		l.addColumn("Kundenberater", 64, 32, 512, MBPAuftrag.Order.Kundenberater);
		l.addColumn("Bezeichnung", 128, 32, 512, MBPAuftrag.Order.Projektbezeichnung);
		l.addColumn("Druckstatus", 128, 32, 512);
		LAYOUT2 = l;
	}

	public MBPAuftragTableModel(final boolean showStatus) {
		super(showStatus ? LAYOUT2 : LAYOUT1);
	}

	@Override
	public Object getColumnValue(final MBPAuftrag rowObject, final int columnIndex) {
		if (rowObject == null)
			return null;
		switch (columnIndex) {
			default: return "???";
			case 0: return rowObject.getProjektKey().getProjektnummer();
			case 1: return rowObject.getAuftragKey().getAuftragnummer();
			case 2: return rowObject.getBasisKey().getProjektnummer();
			case 3: return AbstractCode.getKey(rowObject.getAuftragKey().getGSE(), "");
			case 4: return AbstractCode.getKey(rowObject.getAbwicklungsArt(), "");
			case 5: return TimestampUtil.DATE10.format(rowObject.getWunschDatum());
			case 6: return rowObject.getKundenBerater();
			case 7: return rowObject.getProjektbezeichnung();
			case 8: {
				switch (rowObject.getPrintStatus()) {
					case MBPAuftrag.PRINT_STATUS_PENDING:
						return "Wartet";
					case MBPAuftrag.PRINT_STATUS_PRINTING:
						return "Druckt";
					case MBPAuftrag.PRINT_STATUS_SUCCESS:
						return "Ok";
					case MBPAuftrag.PRINT_STATUS_FAILURE:
						return "Fehler";
					default:
						return "<Unbekannt>";
				}
			}
		}
	}

	/**
	 * Sets the objects to be displayed
	 */
	public void setContent(final PacketMBPSearchReply packet) {
		final List<MBPAuftrag> contents = packet.isValid()
				? packet.getAuftragList() : null;
		setContent(contents);
	}

}

package ch.eisenring.dispo.client.gui.print;

import static ch.eisenring.dispo.shared.codetables.EmploymentRole.MITARBEITER_CHAUFEUR;
import static ch.eisenring.dispo.shared.codetables.EmploymentRole.MITARBEITER_MONTEUR;

import java.awt.Dimension;
import java.awt.GridBagLayout;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.util.Collections;

import javax.swing.JButton;
import javax.swing.JScrollPane;
import javax.swing.ScrollPaneConstants;

import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.iterators.Filter;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.client.gui.components.ModelCheckBox;
import ch.eisenring.dispo.shared.codetables.EmploymentRole;
import ch.eisenring.dispo.shared.model.AbstractMitarbeiter;
import ch.eisenring.dispo.shared.model.AbstractModel;
import ch.eisenring.dispo.shared.model.ModelContext;
import ch.eisenring.gui.components.HEAGPanel;
import ch.eisenring.gui.components.ScrollablePanel;
import ch.eisenring.gui.components.Separator;
import ch.eisenring.gui.util.GUIUtil;
import ch.eisenring.gui.util.LayoutUtil;
import ch.eisenring.logiware.code.soft.LWDisponentSirCode;
import ch.eisenring.user.shared.service.UserFacade;

@SuppressWarnings("serial")
public class EmployeeSelectPanel extends HEAGPanel {

	public final Client client;
	private final List<AbstractMitarbeiter> monteurList = new ArrayList<AbstractMitarbeiter>();
	private final List<AbstractMitarbeiter> chauffeurList = new ArrayList<AbstractMitarbeiter>();
	private final List<ModelCheckBox<AbstractMitarbeiter>> checkList = new ArrayList<ModelCheckBox<AbstractMitarbeiter>>();
	private final ScrollablePanel panel = new ScrollablePanel() {
		@Override
		public boolean getScrollableTracksViewportWidth() {
			return true;
		}
	};
	private final JScrollPane scroll = new JScrollPane();
	private final JButton btnAll = new JButton("Alle");
	private final JButton btnNone = new JButton("Keine");
	private final JButton btnMine = new JButton("Meine");
	private final JButton btnMont = new JButton("Monteure");
	private final JButton btnChauf = new JButton("Chauffeure");
	
	private final ActionListener mineListener = new ActionListener() {
		@Override
		public void actionPerformed(ActionEvent e) {
			for (int i=checkList.size()-1; i>=0; --i) {
				final ModelCheckBox<AbstractMitarbeiter> c = checkList.get(i);
				final AbstractMitarbeiter m = c.getModelObject();
				final boolean s = client.isDisponierbar(m); 
				c.setSelected(s);
			}
		}
	};
	
	private final ActionListener allListener = new ActionListener() {
		@Override
		public void actionPerformed(ActionEvent e) {
			for (int i=checkList.size()-1; i>=0; --i)
				checkList.get(i).setSelected(true);
		}
	};
	
	private final ActionListener noneListener = new ActionListener() {
		@Override
		public void actionPerformed(ActionEvent e) {
			for (int i=checkList.size()-1; i>=0; --i)
				checkList.get(i).setSelected(false);
		}
	};

	protected void selectByType(EmploymentRole type) {
		for (int i=checkList.size()-1; i>=0; --i) {
			final ModelCheckBox<AbstractMitarbeiter> c = checkList.get(i);
			final AbstractMitarbeiter m = c.getModelObject();
			final EmploymentRole t = m.getEmploymentRole();
			c.setSelected(type.equals(t));
		}
	}

	private final ActionListener montListener = new ActionListener() {
		@Override
		public void actionPerformed(ActionEvent e) {
			selectByType(MITARBEITER_MONTEUR);
		}
	};

	private final ActionListener chaufListener = new ActionListener() {
		@Override
		public void actionPerformed(ActionEvent e) {
			selectByType(MITARBEITER_CHAUFEUR);
		}
	};

	public EmployeeSelectPanel(final Client client) {
		this.client = client;
		scroll.setPreferredSize(new Dimension(200, 300));
		btnAll.addActionListener(allListener);
		btnNone.addActionListener(noneListener);
		btnMont.addActionListener(montListener);
		btnChauf.addActionListener(chaufListener);
		btnMine.addActionListener(mineListener);
		initLayout();
	}

	private void initLayout() {
		initPanelLayout();
		final LayoutUtil l = new LayoutUtil(6);
		removeAll();
		setLayout(new GridBagLayout());
		GUIUtil.makeSameSize(btnAll, btnNone, btnMont, btnChauf, btnMine);
		
		add(new HEAGPanel(), l.field(1));
		add(btnAll, l.button());
		add(btnNone, l.button());
		add(btnMine, l.button());
		add(btnChauf, l.button());
		add(btnMont, l.button());
		add(scroll, l.area());
	}

	private void initPanelLayout() {
		scroll.setVerticalScrollBarPolicy(ScrollPaneConstants.VERTICAL_SCROLLBAR_ALWAYS);
		scroll.setHorizontalScrollBarPolicy(ScrollPaneConstants.HORIZONTAL_SCROLLBAR_AS_NEEDED);
		scroll.setViewportView(panel);
		
		final LayoutUtil l = new LayoutUtil(2);
		panel.removeAll();
		panel.setLayout(new GridBagLayout());

		checkList.clear();
		panel.add(Separator.create("Chauffeure"), l.separator());
		for (int i=0; i<chauffeurList.size(); ++i) {
			final AbstractMitarbeiter m = chauffeurList.get(i);
			final ModelCheckBox<AbstractMitarbeiter> c = new ModelCheckBox<AbstractMitarbeiter>(m);
			checkList.add(c);
			panel.add(c, l.field(1));
		}
		l.nextLine();
		panel.add(Separator.create("Monteure"), l.separator());
		for (int i=0; i<monteurList.size(); ++i) {
			final AbstractMitarbeiter m = monteurList.get(i);
			final ModelCheckBox<AbstractMitarbeiter> c = new ModelCheckBox<AbstractMitarbeiter>(m);
			checkList.add(c);
			panel.add(c, l.field(1));
		}
	}

	@SuppressWarnings("unchecked")
	public void setContext(final ModelContext context) {
		final Filter<AbstractModel> filter = new Filter<AbstractModel>() {
			@Override
			public boolean accepts(AbstractModel model) {
				if (model instanceof AbstractMitarbeiter) {
					final AbstractMitarbeiter m = (AbstractMitarbeiter) model;
					final EmploymentRole t = m.getEmploymentRole();
					if (MITARBEITER_MONTEUR.equals(t))
						return true;
					if (MITARBEITER_CHAUFEUR.equals(t))
						return true;
				}
				return false;
			}
		};
		final List<AbstractMitarbeiter> list = (List) context.getModelList(null, filter);
		monteurList.clear();
		chauffeurList.clear();
		// only accept employees which currently have the role "Monteur/Chauffeur"
		for (int i=list.size()-1; i>=0; --i) {
			final AbstractMitarbeiter m = list.get(i);
			final EmploymentRole t = m.getEmploymentRole();
			if (MITARBEITER_MONTEUR.equals(t)) {
				monteurList.add(m);
			}
			if (MITARBEITER_CHAUFEUR.equals(t)) {
				chauffeurList.add(m);
			}
		}
		Collections.sort(monteurList, AbstractMitarbeiter.Order.FullName);
		Collections.sort(chauffeurList, AbstractMitarbeiter.Order.FullName);
		initLayout();
	}
	
	public List<AbstractMitarbeiter> getSelectedMitarbeiter() {
		final List<AbstractMitarbeiter> list = new ArrayList<AbstractMitarbeiter>();
		for (int i=0; i<checkList.size(); ++i) {
			final ModelCheckBox<AbstractMitarbeiter> c = checkList.get(i);
			if (c.isSelected()) {
				final AbstractMitarbeiter m = c.getModelObject();
				list.add(m);
			}
		}
		return list;
	}

	protected UserFacade getCurrentUser() {
		return client.getCurrentUser();
	}
	
	protected LWDisponentSirCode getCurrentDisponent() {
		return client.getCurrentDisponent();
	}

}

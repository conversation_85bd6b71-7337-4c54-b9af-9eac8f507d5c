package ch.eisenring.dispo.client.gui.report.endmontagen;

import java.awt.GridBagLayout;
import java.util.Calendar;

import ch.eisenring.core.collections.Set;
import ch.eisenring.core.datatypes.date.HEAGCalendar;
import ch.eisenring.core.datatypes.date.Period;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.client.resource.imgnew.Images;
import ch.eisenring.dispo.shared.network.packets.PacketReportKOKRequest;
import ch.eisenring.gui.GridBagConstraints;
import ch.eisenring.gui.components.DecorationHeader;
import ch.eisenring.gui.components.HEAGLabel;
import ch.eisenring.gui.components.HEAGPanel;
import ch.eisenring.gui.components.HEAGPeriodField;
import ch.eisenring.gui.components.code.HEAGCodeSelectionField;
import ch.eisenring.gui.model.ValidationResults;
import ch.eisenring.logiware.code.pseudo.AbwicklungsartCode;
import ch.eisenring.logiware.code.soft.LWTerminCD;

@SuppressWarnings("serial")
public class ReportKOKPanel extends HEAGPanel {

	protected final HEAGPeriodField periodControl = new HEAGPeriodField();
	protected final HEAGCodeSelectionField<LWTerminCD> selGruppe;
	protected final HEAGCodeSelectionField<AbwicklungsartCode> selAWA;

	public ReportKOKPanel(final Client client) {
		this.selGruppe = new HEAGCodeSelectionField<>(200, null, client, LWTerminCD.class);
		this.selAWA = new HEAGCodeSelectionField<AbwicklungsartCode>(200, null, client, AbwicklungsartCode.class);
		initComponents();
		initLayout();
	}
	
	private void initComponents() {
		setLayout(new GridBagLayout());
		final HEAGCalendar c = HEAGCalendar.obtainNow();
		long begin, end;
		c.set(Calendar.MONTH, 0);
		c.set(Calendar.DAY_OF_MONTH, 1);
		c.set(Calendar.HOUR_OF_DAY, 0);
		c.set(Calendar.MINUTE, 0);
		c.set(Calendar.SECOND, 0);
		c.set(Calendar.MILLISECOND, 0);
		begin = c.getTimeInMillis();
		c.set(Calendar.MONTH, 11);
		c.set(Calendar.DAY_OF_MONTH, 31);
		c.add(Calendar.DAY_OF_YEAR, 1);
		end = c.getTimeInMillis();
		c.release();
		periodControl.setValue(Period.create(begin, end));
		selGruppe.setSelection(Set.asReadonlySet(LWTerminCD.C101));
		selAWA.setSelection(Set.asReadonlySet(AbwicklungsartCode.AWA001, AbwicklungsartCode.AWA135));
	}

	private void initLayout() {
		removeAll();
		
		int y = 0;
		DecorationHeader header = new DecorationHeader(
				DecorationHeader.ICON, Images.LINEGRAPH,
				DecorationHeader.SIZE, DecorationHeader.MEDIUM,
				DecorationHeader.LABEL, "Verschiebungen KOK",
				DecorationHeader.SEPARATOR, Boolean.TRUE);
		add(header, GridBagConstraints.decorator(0, y));

		++y;
		add(new HEAGLabel("Auswertungszeitraum"), GridBagConstraints.label(0, y));
		add(periodControl, GridBagConstraints.field(1, y).gridWidthRemainder());
		++y;
		add(new HEAGLabel("Termingruppe"), GridBagConstraints.label(0, y));
		add(selGruppe, GridBagConstraints.field(1, y).gridWidthRemainder());
		++y;
		add(new HEAGLabel("Abwicklungsarten"), GridBagConstraints.label(0, y));
		add(selAWA, GridBagConstraints.field(1, y).gridWidthRemainder());
	}

	public PacketReportKOKRequest getRequest() {
		final Period period = (Period) periodControl.getValue();
		final Set<LWTerminCD> gruppen = selGruppe.getSelection();
		final Set<AbwicklungsartCode> awa = selAWA.getSelection();
		return PacketReportKOKRequest.create(period, gruppen, awa);
	}

	@Override
	public void validateView(final ValidationResults results, final Object model) {
		super.validateView(results, model);
		final Set<AbwicklungsartCode> awa = selAWA.getSelection();
		if (awa.isEmpty())
			results.add("Eine oder mehrere Abwicklungsarten wählen", selAWA);
		final Set<LWTerminCD> gruppen = selGruppe.getSelection();
		if (gruppen.isEmpty())
			results.add("Eine oder mehrere Gruppen wählen", selGruppe);

	}

}

package ch.eisenring.dispo.client.gui.menu;

import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.client.action.ActionElementBase;
import ch.eisenring.dispo.client.gui.grid.BlockElement;
import ch.eisenring.dispo.client.gui.grid.GridElement;
import ch.eisenring.dispo.shared.model.AbstractModel;
import ch.eisenring.dispo.shared.model.AbstractZuordnung;
import ch.eisenring.dispo.shared.model.Auftrag;
import ch.eisenring.logiware.LWProjektKey;
import ch.eisenring.logiware.code.soft.LWGranit2Code;
import heag.huo.client.gui.haus.HausUebersichtWindow;
import heag.huo.client.resources.images.Images;

public class ActionHausUebersicht extends ActionElementBase {

	public ActionHausUebersicht(final Client client, final GridElement gridElement) {
		super(client, gridElement, Images.HOUSE, "Hausübersicht...");
		isEnabled();
	}

	@Override
	protected boolean isEnabledImpl() {
		return super.isEnabledImpl() && getBasisNr(getBlockElement()) != null;
	}

	private String getBasisNr(final BlockElement element) {
		if (element == null)
			return null;
		final AbstractModel model = element.getModel();
		final Auftrag auftrag;
		if (model instanceof Auftrag) {
			auftrag = (Auftrag) model;
		} else if (model instanceof AbstractZuordnung) {
			auftrag =((AbstractZuordnung) model).getAuftrag();
		} else {
			auftrag = null;
		}
		if (auftrag == null)
			return null;
		final LWGranit2Code objArt = auftrag.getGranitschluessel2();
		if (AbstractCode.isNull(objArt) || !objArt.isObjekt())
			return null;
		String basisNr = auftrag.getBasisnummer();
		if (Strings.isEmpty(basisNr))
			return null;
		try {
			basisNr = LWProjektKey.getCanonicalBasisobjektNummer(basisNr);
		} catch (final IllegalArgumentException e) {
			return null;
		}
		return basisNr;
	}

	@Override
	protected void performBlockAction(final BlockElement blockElement) {
		final String basisNr = getBasisNr(blockElement);
		if (basisNr == null)
			return;
		HausUebersichtWindow.doIt(client, basisNr);
	}

}

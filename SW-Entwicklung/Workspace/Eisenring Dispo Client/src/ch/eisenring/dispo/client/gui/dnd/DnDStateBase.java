package ch.eisenring.dispo.client.gui.dnd;

import java.awt.event.MouseEvent;

import ch.eisenring.dispo.client.gui.components.FrameOverlay;
import ch.eisenring.dispo.client.gui.components.OverlayedFrame;
import ch.eisenring.dispo.client.gui.grid.BlockElement;
import ch.eisenring.dispo.client.gui.grid.Grid;
import ch.eisenring.dispo.client.gui.grid.GridElement;

public abstract class DnDStateBase extends DnDBase {

	// the grid element currently manipulated by this handler
	private BlockElement gridElement;

	public DnDStateBase(final DnDMainHandler mainHandler) {
		super(mainHandler);
	}

	/**
	 * Called when the mouse moved
	 */
	protected abstract void mouseMoved(final MouseEvent event);

	/**
	 * Called when the left mouse button is pressed
	 */
	protected void mousePressed(final MouseEvent event) {
		final BlockElement gridElement = getGridElementForAction(event, getActivatingZone());
		if (null==gridElement) {
			cancelDrag();
		} else {
			this.gridElement = gridElement;
			beginState(event, gridElement);
		}
	}
	
	/**
	 * Called when the left mouse button was pressed,
	 * and the element under the mouse coordinates was 
	 * suitable for the operation.
	 */
	protected abstract void beginState(final MouseEvent event, final BlockElement gridElement);
	
	/**
	 * Called when the left mouse button is released
	 */
	protected abstract void mouseReleased(final MouseEvent event);
	
	/**
	 * Gets the cursor type to be used when this handler is active 
	 */
	protected abstract Object getActiveCursor();

	/**
	 * Returns control to the idle state handler.
	 * This method should be called from mouseReleased(),
	 * no matter if the operation succeeded or failed.
	 */
	protected void terminateState() {
		gridElement = null;
		setActiveHandler(null);
	}

	/**
	 * Gets the zone that activates this handler
	 */
	protected abstract int getActivatingZone();
	
	@Override
	protected void cancelDrag() {
		terminateState();
	}
	
	protected final void cancelDrag(final GridElement element) {
		if (element.equals(getGridElement()))
			cancelDrag();
	}

	/**
	 * Gets the grid that cause the mouse event, or null if
	 * the event originated from any other component.
	 * 
	 * If the event originated from a grid, the grids screen
	 * coordinates are automatically updated.
	 */
	protected final Grid getGrid(final MouseEvent event) {
		return mainHandler.getGrid(event);
	}

	/**
	 * Sets the active mouse cursor
	 */
	@Override
	protected final void setCursor(final Object cursorId) {
		mainHandler.setCursor(cursorId);
	}
	
	/**
	 * Sets the active mainHandler
	 */
	@Override
	protected final void setActiveHandler(final DnDStateBase activeState) {
		mainHandler.setActiveHandler(activeState);
	}

	/**
	 * Gets the currently manipulated grid element
	 */
	protected final BlockElement getGridElement() {
		return this.gridElement;
	}
	
	/**
	 * Sets the currently manipulated grid element
	 */
	protected void setGridElement(final BlockElement gridElement) {
		this.gridElement = gridElement;
	}

	/**
	 * Gets the state handler responsible for zone
	 */
	protected final DnDStateBase getHandlerForZone(final int zoneId) {
		if (mainHandler.dragHandler.getActivatingZone()==zoneId)
			return mainHandler.dragHandler;
		if (mainHandler.resizeEastHandler.getActivatingZone()==zoneId)
			return mainHandler.resizeEastHandler;
		if (mainHandler.resizeWestHandler.getActivatingZone()==zoneId)
			return mainHandler.resizeWestHandler;
		return mainHandler.idleHandler;
	}
	
	/**
	 * Gets the overlay component
	 */
	protected FrameOverlay getOverlay() {
		final OverlayedFrame frame = mainHandler.getFrame();
		return null==frame ? null : frame.getOverlay();
	}
	
}

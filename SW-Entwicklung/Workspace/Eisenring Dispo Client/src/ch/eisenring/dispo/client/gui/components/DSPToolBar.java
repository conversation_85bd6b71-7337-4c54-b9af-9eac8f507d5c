package ch.eisenring.dispo.client.gui.components;

import static ch.eisenring.dispo.shared.codetables.AnsichtCode.ABRECHNUNG;
import static ch.eisenring.dispo.shared.codetables.AnsichtCode.FAHRPLANUNG;
import static ch.eisenring.dispo.shared.codetables.AnsichtCode.FEINPLANUNG;
import static ch.eisenring.dispo.shared.codetables.AnsichtCode.GROBPLANUNG;
import static ch.eisenring.dispo.shared.codetables.ZuordnungCode.ZUORDNUNG_ABLADEN;
import static ch.eisenring.dispo.shared.codetables.ZuordnungCode.ZUORDNUNG_ABWESEND;
import static ch.eisenring.dispo.shared.codetables.ZuordnungCode.ZUORDNUNG_FAHRAUFTRAG;
import static ch.eisenring.dispo.shared.codetables.ZuordnungCode.ZUORDNUNG_SUPERVISOR;

import java.awt.GridBagLayout;
import java.util.Collections;

import javax.swing.JComponent;
import javax.swing.JPanel;

import ch.eisenring.core.collections.Collection;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.observable.Observable;
import ch.eisenring.core.observable.Observer;
import ch.eisenring.core.observable.ObserverNotificationPolicy;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.client.gui.components.button.KWCurrImageButton;
import ch.eisenring.dispo.client.gui.components.button.KWNextImageButton;
import ch.eisenring.dispo.client.gui.components.button.KWPrevImageButton;
import ch.eisenring.dispo.client.gui.components.combo.AnsichtTypeCombo;
import ch.eisenring.dispo.client.gui.components.combo.DSPAbwicklungsartCombo;
import ch.eisenring.dispo.client.gui.components.combo.FahrzeugCombo;
import ch.eisenring.dispo.client.gui.components.combo.FahrzeugCombo.Fahrzeug;
import ch.eisenring.dispo.client.gui.components.combo.MitarbeiterFavoritesCombo;
import ch.eisenring.dispo.client.gui.kok.KOKSetzenButton;
import ch.eisenring.dispo.client.gui.toolbar.BasisNummerFilter;
import ch.eisenring.dispo.client.gui.toolbar.DisponentFilter;
import ch.eisenring.dispo.client.resource.imgnew.Images;
import ch.eisenring.dispo.shared.codetables.AnsichtCode;
import ch.eisenring.dispo.shared.codetables.ColorCode;
import ch.eisenring.gui.GridBagConstraints;
import ch.eisenring.gui.components.HEAGPanel;
import ch.eisenring.gui.util.GUIUtil;
import ch.eisenring.gui.util.LayoutUtil;

@SuppressWarnings("serial")
public final class DSPToolBar extends HEAGPanel {
	
	private final Client client;
	private final JComponent ressourcenPlanung;
	private final JComponent fahrAuftrag;
	private final FahrauftragNeuDnD[] fzgDrag;
	private final JComponent supervisorDrag;
	private final JComponent abladenDrag;
	private final JComponent sabwesendDrag;
	private final JComponent clipboard;
	private final JComponent deleteDrop;
	private final JComponent kwPrev;
	private final JComponent kwCurr;
	private final JComponent kwNext;
	private final JComponent refresh;
	private final JComponent filter;
	private final JComponent kokSetzen;
	private final JComponent cmbAbwicklungsArt;
	private final JComponent ansichtCombo;
	private final JComponent favoritesCombo = new MitarbeiterFavoritesCombo();
	private final JComponent disponentIdentity;
	private final JComponent disponentFilter;
	private final JComponent basisNummerFilter;
	
	private final JPanel buttonPanel;
	private final JPanel comboPanel;
	
	private final Observer<Object> observer = new Observer<Object>() {
		@Override
		public void observableChanged(final Observable<Object> observeable) {
			final AnsichtCode ansicht = client.ANSICHT.get(); 
			ansichtChanged(ansicht);
		}
	};
	
	public DSPToolBar(final Client client) {
		this.client = client;
		this.fzgDrag = getFahrzeuge(client);
		this.kwPrev = new KWPrevImageButton(client);
		this.kwCurr = new KWCurrImageButton(client);
		this.kwNext = new KWNextImageButton(client);
		this.filter = new FilterSettingsButton(client);
		this.refresh = new RefreshButton(client);
		this.cmbAbwicklungsArt = new DSPAbwicklungsartCombo(client, false);
		this.clipboard = new ClipboardDnD(client);
		this.deleteDrop = new TrashcanDnD(client);
		this.disponentIdentity = new DisponentIdentity(client);
		this.ressourcenPlanung = new FahrauftragNeuDnD(client, ZUORDNUNG_FAHRAUFTRAG,
				Images.EMPLOYEE, ColorCode.COLOR_4011, null, "Ressourcenplanung");
		this.fahrAuftrag = new FahrauftragNeuDnD(client, ZUORDNUNG_FAHRAUFTRAG,
				Images.FA_ALLGEMEIN, ColorCode.COLOR_DEFAULT, null, "Fahrauftrag");
		this.supervisorDrag = new FahrauftragNeuDnD(client, ZUORDNUNG_SUPERVISOR,
				Images.SUPERVISOR, ColorCode.COLOR_DEFAULT, null, "Begleitung");
		this.abladenDrag = new FahrauftragNeuDnD(client, ZUORDNUNG_ABLADEN,
				Images.FA_ABLADEN, ColorCode.COLOR_DEFAULT, null, "Abladen");
		this.sabwesendDrag = new FahrauftragNeuDnD(client, ZUORDNUNG_ABWESEND,
				Images.FA_ABWESEND, ColorCode.COLOR_DEFAULT, null, "S/Abwesend");
		this.kokSetzen = new KOKSetzenButton(client);
		this.disponentFilter = new DisponentFilter(client);
		this.ansichtCombo = new AnsichtTypeCombo(client);
		this.basisNummerFilter = new BasisNummerFilter(client);
		this.buttonPanel = getButtonPanel();
		this.comboPanel = getComboPanel();
		initLayout();
		client.ANSICHT.addObserver(observer, ObserverNotificationPolicy.EDT_ASYNCHRONOUS);
	}
	
	private void initLayout() {
		final LayoutUtil l = new LayoutUtil(4);
		removeAll();
		setLayout(new GridBagLayout());
		favoritesCombo.setFocusable(false);
		disponentIdentity.setFocusable(false);

		add(buttonPanel, l.panel(1, 0, 0));
		add(comboPanel, l.panel(1, 0, 0));
		add(new HEAGPanel(), l.panel(1, 0, 1));
		add(refresh, l.button());
	}
	
	private HEAGPanel getComboPanel() {
		final LayoutUtil l = new LayoutUtil(6);
		final HEAGPanel panel = new HEAGPanel();
		//GUIUtil.makeSameSize(auftragsArtCombo, ansichtCombo, favoritesCombo, disponentIdentity);
		GUIUtil.makeSameSize(favoritesCombo, disponentIdentity);
		GUIUtil.makeSameSize(disponentFilter, basisNummerFilter);
		panel.setLayout(new GridBagLayout());
		panel.add(ansichtCombo, l.button());
		panel.add(cmbAbwicklungsArt, l.button());
		panel.add(favoritesCombo, l.button());
		if (!client.isKioskMode()) {
			panel.add(disponentIdentity, l.button());
			panel.add(disponentFilter, l.button());
			panel.add(basisNummerFilter, l.button());
		}
		return panel;
	}
	
	private HEAGPanel getButtonPanel() {
		final HEAGPanel panel = new HEAGPanel();
		{
			final ArrayList<JComponent> buttons = new ArrayList<>();
			Collections.addAll(buttons, kwPrev, kwCurr, kwNext, filter, fahrAuftrag, ressourcenPlanung);
			Collections.addAll(buttons, supervisorDrag, deleteDrop, clipboard, refresh, sabwesendDrag, abladenDrag);
			Collections.addAll(buttons, fzgDrag);
			GUIUtil.makeSameSize(buttons);
		}

		int x = -1;
		panel.setLayout(new GridBagLayout());

		panel.add(kwPrev, GridBagConstraints.button(++x, 0));
		panel.add(kwCurr, GridBagConstraints.button(++x, 0));
		panel.add(kwNext, GridBagConstraints.button(++x, 0));
		if (!client.isKioskMode()) {
			panel.add(filter, GridBagConstraints.button(++x, 0));
			panel.add(ressourcenPlanung, GridBagConstraints.button(++x, 0));
			panel.add(fahrAuftrag, GridBagConstraints.button(++x, 0));
			panel.add(supervisorDrag, GridBagConstraints.button(++x, 0));
			panel.add(abladenDrag, GridBagConstraints.button(++x, 0));
			panel.add(sabwesendDrag, GridBagConstraints.button(++x, 0));
			panel.add(deleteDrop, GridBagConstraints.button(++x, 0));
			panel.add(clipboard, GridBagConstraints.button(++x, 0));
		}

		for (FahrauftragNeuDnD dnd : fzgDrag) {
			panel.add(dnd, GridBagConstraints.button(++x, 0));
		}

		if (!client.isKioskMode()) {
			panel.add(kokSetzen, GridBagConstraints.button(++x, 0));
		}
		return panel;
	}

	protected void ansichtChanged(final AnsichtCode ansicht) {
		final boolean isFahrPlanung = FAHRPLANUNG.equals(ansicht);
		final boolean isFeinPlanung = FEINPLANUNG.equals(ansicht);
		final boolean isGrobPlanung = GROBPLANUNG.equals(ansicht);
		final boolean isAbrechnung = ABRECHNUNG.equals(ansicht);
		for (final FahrauftragNeuDnD dnd : fzgDrag) {
			dnd.setVisible(isFahrPlanung);
		}
		basisNummerFilter.setVisible(isFeinPlanung || isGrobPlanung);
		disponentFilter.setVisible(!isFahrPlanung);
		deleteDrop.setVisible(!isAbrechnung);
		supervisorDrag.setVisible(isFeinPlanung);
		abladenDrag.setVisible(isFeinPlanung);
		sabwesendDrag.setVisible(isFeinPlanung);
		fahrAuftrag.setVisible(isFahrPlanung);
		ressourcenPlanung.setVisible(false); // obsolete 20.04.2016
		buttonPanel.validate();
	}
	
	public static FahrauftragNeuDnD[] getFahrzeuge(final Client client) {
		final ArrayList<FahrauftragNeuDnD> result = new ArrayList<>();
		if (!client.isKioskMode()) {
		int index = 0;
			for (final Fahrzeug fahrzeug : FahrzeugCombo.getFahrzeuge(FahrzeugCombo.TYPE_FAHRZEUG)) {
				if (++index > 5) {
					break;
				}
				final FahrauftragNeuDnD dnd = new FahrauftragNeuDnD(client, ZUORDNUNG_FAHRAUFTRAG, fahrzeug.icon,
						ColorCode.COLOR_DEFAULT, fahrzeug, fahrzeug.itemLabel);
				result.add(dnd);
			}
		}
		return Collection.toArray(result, FahrauftragNeuDnD.class);
	}

}

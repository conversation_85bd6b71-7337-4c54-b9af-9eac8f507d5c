package ch.eisenring.dispo.client.action;

import static ch.eisenring.dispo.shared.codetables.ZuordnungCode.ZUORDNUNG_FAHRAUFTRAG;
import static ch.eisenring.dispo.shared.codetables.ZuordnungCode.ZUORDNUNG_MONTAGE;
import static ch.eisenring.dispo.shared.codetables.ZuordnungCode.ZUORDNUNG_SUPERVISOR;
import static ch.eisenring.dispo.shared.codetables.ZuordnungCode.ZUORDNUNG_ZUSATZ;

import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.collections.Map;
import ch.eisenring.core.collections.impl.HashMap;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.resource.ImageResource;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.client.gui.grid.BlockElement;
import ch.eisenring.dispo.client.gui.grid.GridElement;
import ch.eisenring.dispo.client.resource.imgnew.Images;
import ch.eisenring.dispo.shared.codetables.ZuordnungCode;
import ch.eisenring.dispo.shared.model.AbstractZuordnung;
import ch.eisenring.dispo.shared.model.FeinZuordnung;

@SuppressWarnings("serial")
public final class ActionElementMorph extends ActionElementBase {

	private final static Map<ZuordnungCode, ZuordnungCode> MORPH_MAP; static {
		final Map<ZuordnungCode, ZuordnungCode> l = new HashMap<>();
		l.put(ZUORDNUNG_MONTAGE, ZUORDNUNG_ZUSATZ);
		l.put(ZUORDNUNG_ZUSATZ, ZUORDNUNG_MONTAGE);
		l.put(ZUORDNUNG_FAHRAUFTRAG, ZUORDNUNG_SUPERVISOR);
		l.put(ZUORDNUNG_SUPERVISOR, ZUORDNUNG_FAHRAUFTRAG);
		MORPH_MAP = l;
	}

	private final static Map<ZuordnungCode, ImageResource> ICON_MAP; static {
		final Map<ZuordnungCode, ImageResource> l = new HashMap<>();
		l.put(ZUORDNUNG_MONTAGE, Images.ZUSATZPLANUNG);
		l.put(ZUORDNUNG_ZUSATZ, Images.FEINPLANUNG);
		l.put(ZUORDNUNG_FAHRAUFTRAG, Images.SUPERVISOR);
		l.put(ZUORDNUNG_SUPERVISOR, Images.FAHRPLANUNG);
		ICON_MAP = l;
	}

	public ActionElementMorph(final Client client, final GridElement gridElement) {
		super(client, gridElement,
				getIconResource(gridElement), getLabelText(gridElement));
	}

	@Override
	public void performBlockAction(final BlockElement blockElement) {
		final FeinZuordnung zuordnung = (FeinZuordnung) getModel();
		final ZuordnungCode newType = MORPH_MAP.get(zuordnung.getType());
		if (newType != null) {
			zuordnung.setType(newType);
		}
	}

	private static ImageResource getIconResource(final GridElement gridElement) {
		final Object model = gridElement == null ? null : gridElement.getModel();
		final AbstractZuordnung zuordnung = (AbstractZuordnung) model;
		final ZuordnungCode type = zuordnung.getType();
		return ICON_MAP.get(type);
	}

	private static String getLabelText(final GridElement gridElement) {
		final Object model = gridElement == null ? null : gridElement.getModel();
		final AbstractZuordnung zuordung = (AbstractZuordnung) model;
		final ZuordnungCode newType = MORPH_MAP.get(zuordung.getType());
		return Strings.concat("In ", AbstractCode.getLongText(newType, "???"), " umwandeln");
	}

	@Override
	protected boolean isEnabledImpl() {
		if (!super.isEnabledImpl())
			return false;
		final AbstractZuordnung zuordnung = (AbstractZuordnung) getModel();
		if (zuordnung == null || !isMorphableType(zuordnung.getType())) {
			return false;
		}
		final BlockElement block = getBlockElement();
		return block.isEditable(); 
	}

	public final static boolean isMorphableType(final ZuordnungCode type) {
		return MORPH_MAP.get(type) != null;
	}

	public final static boolean isMorphable(final AbstractZuordnung zuordnung) {
		if (zuordnung instanceof FeinZuordnung) {
			return isMorphableType(((FeinZuordnung) zuordnung).getType());
		}
		return false;
	}

}

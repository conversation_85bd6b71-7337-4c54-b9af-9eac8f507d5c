package ch.eisenring.dispo.client.logic;

import ch.eisenring.dispo.client.gui.dnd.DnDHintDrag;
import ch.eisenring.dispo.client.gui.grid.GridElement;

public class DnDDragAllowed  extends DnDHintDrag {

	public final static DnDDragAllowed INSTANCE = new DnDDragAllowed();
		
	private DnDDragAllowed() {
	}

	@Override
	public boolean isDraggable(GridElement thisElement) {
		// allow drag operation unconditionally
		return true;
	}

}

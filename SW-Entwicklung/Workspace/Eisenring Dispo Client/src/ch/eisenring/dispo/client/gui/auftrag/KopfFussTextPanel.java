package ch.eisenring.dispo.client.gui.auftrag;

import java.awt.GridBagLayout;

import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.shared.model.Auftrag;
import ch.eisenring.gui.GridBagConstraints;
import ch.eisenring.gui.components.HEAGLabel;
import ch.eisenring.gui.components.HEAGTextArea;
import ch.eisenring.gui.util.ConversionUtil;

@SuppressWarnings("serial")
public final class KopfFussTextPanel extends AuftragDetailPanel {

	private final HEAGTextArea txtKopf = new HEAGTextArea(new AuftragBinding(50000) {
		@Override
		protected Object getValue(final Auftrag auftrag) {
			return auftrag.getKopfText();
		}
		@Override
		protected void setValue(final Auftrag auftrag, final Object value) {
			auftrag.setKopfText(ConversionUtil.convert(value, Strings.NULL));
		}
	});
	private final HEAGTextArea txtFuss = new HEAGTextArea(new AuftragBinding(50000) {
		@Override
		protected Object getValue(final Auftrag auftrag) {
			return auftrag.getFussText();
		}
		@Override
		protected void setValue(final Auftrag auftrag, final Object value) {
			auftrag.setFussText(ConversionUtil.convert(value, Strings.NULL));
		}
	});

	public KopfFussTextPanel(final Client client) {
		super(client);
		initComponents();
		initLayout();
	}
	
	private void initComponents() {
		setLayout(new GridBagLayout());
	}

	private void initLayout() {
		add(new HEAGLabel("<HTML>Kopftext<BR>(MS)"), GridBagConstraints.label(0, 0));
		add(txtKopf, GridBagConstraints.area(1, 0));

		add(new HEAGLabel("Fusstext"), GridBagConstraints.label(0, 1));
		add(txtFuss, GridBagConstraints.area(1, 1));
	}

	@Override
	public void setEditable(final boolean editable) {
		super.setEditable(editable);
		updateEditableImpl();
	}

	// --------------------------------------------------------------
	// ---
	// --- HEAGPanelLazy implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public void evaluateChanged(final Object model) {
		// this panel is read only, never evaluate changed
	}

	@Override
	public final boolean isViewChanged() {
		// this panel is read-only (never changed)
		return false;
	}

	@Override
	protected void updateEditableImpl() {
		txtKopf.setEditable(false);
		txtFuss.setEditable(false);
	}

	@Override
	protected void showingFirstTimeImpl() {
		// nothing
	}

}

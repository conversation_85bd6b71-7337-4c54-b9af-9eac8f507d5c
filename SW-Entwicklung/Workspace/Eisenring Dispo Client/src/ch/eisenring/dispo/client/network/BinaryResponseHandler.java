package ch.eisenring.dispo.client.network;

import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.shared.network.packets.PacketBinaryResponse;
import ch.eisenring.network.PacketSink;
import ch.eisenring.network.implementation.PacketDispatchMode;
import ch.eisenring.network.packet.AbstractPacket;

public final class BinaryResponseHandler extends AbstractDSPPacketHandler {

	BinaryResponseHandler(final Client client) {
		super(client, PacketBinaryResponse.class, PacketDispatchMode.SYNCHRONOUS);
	}
	
	@Override
	public void handle(final AbstractPacket abstractPacket, final PacketSink sink) {
		// NO-OP
	}

}

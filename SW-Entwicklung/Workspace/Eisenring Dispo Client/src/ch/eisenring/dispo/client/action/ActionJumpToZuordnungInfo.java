package ch.eisenring.dispo.client.action;

import java.awt.Component;

import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.client.gui.auftrag.PlanungInfoHelper;
import ch.eisenring.dispo.shared.service.ZuordnungInfoEntry;

public final class ActionJumpToZuordnungInfo extends AbstractDSPAction {

	private final ZuordnungInfoEntry zuordnung;
	private final Component component;
	
	public ActionJumpToZuordnungInfo(final Client client,
			                         final ZuordnungInfoEntry zuordnung,
			                         final Component component) {
		super(client, (zuordnung == null ? "" : zuordnung.toString()));
		this.zuordnung = zuordnung;
		this.component = component;
	}

	@Override
	protected void performAction() {
		if (component != null) {
			component.setVisible(false);
		}
		PlanungInfoHelper.jumpToZuordnung(client, zuordnung);
	}
	
	@Override
	protected boolean isEnabledImpl() {
		return super.isEnabledImpl() && zuordnung != null;
	}

}

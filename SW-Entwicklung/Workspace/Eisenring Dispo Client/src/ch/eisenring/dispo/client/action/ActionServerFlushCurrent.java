package ch.eisenring.dispo.client.action;

import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.client.util.KWUtil;
import ch.eisenring.dispo.shared.codetables.DSPRightCode;
import ch.eisenring.dispo.shared.network.packets.PacketServerFlushOne;

public final class ActionServerFlushCurrent extends AbstractDSPAction {
 
	public ActionServerFlushCurrent(final Client client) {
		super(client, "_Aktuelle Woche neu laden");
		addPermissionObserver();
	}

	@Override
	protected void performAction() {
		if (!askConfirmation(client, "Sind Sie sicher das Sie die aktuelle Kalenderwoche\nneu laden wollen? (Die Aktion betrifft alle Benutzer!)")) {
			return;
		}
		PacketServerFlushOne packet = PacketServerFlushOne.create();
		long date = TimestampUtil.toTimestamp(client.STATE_KWDATE.get());
		date = KWUtil.getKW(date, 0); 
		packet.addContext(date);
		client.sendPacket(packet);
	}

	@Override
	protected boolean isEnabledImpl() {
		return DSPRightCode.RELOAD_ONE.isPermitted() ||
		       DSPRightCode.RELOAD_ALL.isPermitted();
	}

}

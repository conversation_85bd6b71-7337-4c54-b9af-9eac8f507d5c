package ch.eisenring.dispo.client.action;

import java.awt.event.InputEvent;
import java.awt.event.KeyEvent;

import javax.swing.KeyStroke;

import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.client.gui.jumpto.JumpToDialog;
import ch.eisenring.dispo.client.resource.imgnew.Images;
import ch.eisenring.gui.util.GUIUtil;

public final class ActionJumpTo extends AbstractDSPAction {

	public ActionJumpTo(final Client client) {
		super(client, KeyStroke.getKeyStroke(KeyEvent.VK_G, InputEvent.CTRL_DOWN_MASK),
				Images.CALENDAR, "Sprun_g zu Datum...");
	}

	@Override
	protected void performAction() {
		GUIUtil.invokeLater(new Runnable() {
			@Override
			public void run() {
				JumpToDialog dialog = new JumpToDialog(client);
				dialog.setVisible(true);
			}
		});
	}

}

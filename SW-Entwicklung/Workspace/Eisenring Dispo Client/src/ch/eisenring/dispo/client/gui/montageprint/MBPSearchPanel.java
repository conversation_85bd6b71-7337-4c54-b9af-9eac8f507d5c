package ch.eisenring.dispo.client.gui.montageprint;

import java.awt.GridBagLayout;
import java.util.Date;

import ch.eisenring.core.datatypes.date.DateUtil;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.shared.network.packets.PacketMBPSearchReply;
import ch.eisenring.dispo.shared.network.packets.PacketMBPSearchRequest;
import ch.eisenring.gui.GridBagConstraints;
import ch.eisenring.gui.action.AbstractAction;
import ch.eisenring.gui.components.HEAGButton;
import ch.eisenring.gui.components.HEAGCodeComboBox;
import ch.eisenring.gui.components.HEAGDateField;
import ch.eisenring.gui.components.HEAGLabel;
import ch.eisenring.gui.components.HEAGPanel;
import ch.eisenring.gui.components.HEAGTextField;
import ch.eisenring.gui.components.Separator;
import ch.eisenring.gui.model.StoreResults;
import ch.eisenring.gui.model.ValidationResults;
import ch.eisenring.gui.util.GUIUtil;
import ch.eisenring.logiware.code.soft.GSECode;
import ch.eisenring.logiware.code.soft.LWDisponentSirCode;
import ch.eisenring.logiware.code.soft.LWKundenberaterCode;

@SuppressWarnings("serial")
public class MBPSearchPanel extends HEAGPanel {

	protected final Client client;
	protected final MBPResultPanel resultPanel;
	
	protected final AbstractAction searchAction = new AbstractAction(
			"Suchen", AbstractAction.DEFAULT_ACTION_TRUE) {
		protected void performAction() {
			final MBPSelectDialog dialog = (MBPSelectDialog) GUIUtil.getWindowAncestor(MBPSearchPanel.this);
			dialog.onApply();
		}
	};

	protected final HEAGCodeComboBox<LWKundenberaterCode> cmbKundenberater =
			new HEAGCodeComboBox<LWKundenberaterCode>(LWKundenberaterCode.class);
	protected final HEAGCodeComboBox<LWDisponentSirCode> cmbDisponent =
			new HEAGCodeComboBox<LWDisponentSirCode>(LWDisponentSirCode.class);
	protected final HEAGCodeComboBox<GSECode> cmbGSE = new HEAGCodeComboBox<>(
			GSECode.NULL, GSECode.GSE_2501, GSECode.GSE_2502, GSECode.GSE_2503);
	protected final HEAGDateField datKW = new HEAGDateField();
	protected final HEAGTextField txtBasis = new HEAGTextField(10);
	protected final HEAGButton btnSearch = new HEAGButton(searchAction);
	
	public MBPSearchPanel(final Client client, final MBPResultPanel resultPanel) {
		this.client = client;
		this.resultPanel = resultPanel;
		initComponents();
		initLayout();
	}

	private void initComponents() {
		setLayout(new GridBagLayout());
		GUIUtil.setMinSizeForText(txtBasis, 10);
		datKW.setDate(new Date());
	}

	private void initLayout() {
		removeAll();
		int y = 0;
		
		add(new HEAGLabel("KW"), GridBagConstraints.label(0, y));
		add(datKW, GridBagConstraints.fixed(1, y));
		
		++y;
		add(new HEAGLabel("GSE"), GridBagConstraints.label(0, y));
		add(cmbGSE, GridBagConstraints.fixed(1, y));
		
		++y;
		add(new HEAGLabel("Basis"), GridBagConstraints.label(0, y));
		add(txtBasis, GridBagConstraints.fixed(1, y));
		
		++y;
		add(new HEAGLabel("Kundenberater"), GridBagConstraints.label(0, y));
		add(cmbKundenberater, GridBagConstraints.fixed(1, y));
		
		++y;
		add(new HEAGLabel("Disponent"), GridBagConstraints.label(0, y));
		add(cmbDisponent, GridBagConstraints.fixed(1, y));

		++y;
		add(Separator.create(), GridBagConstraints.separator(0, y));

		++y;
		add(btnSearch, GridBagConstraints.button(0, y, GridBagConstraints.EAST).gridWidthRemainder().insetBottom(0));
	}

	public PacketMBPSearchRequest getSearchRequest() {
		final Date date = datKW.getDate();
		Date dateFrom = date == null ? null : DateUtil.findMonday(date, 0);
		Date dateUpto = date == null ? null : DateUtil.findMonday(date, 1);
		final PacketMBPSearchRequest request = PacketMBPSearchRequest.create(
				dateFrom, dateUpto,
				cmbGSE.getSelectedCode(),
				cmbKundenberater.getSelectedCode(),
				cmbDisponent.getSelectedCode(),
				txtBasis.getText());
		return request;
	}

	@Override
	public void validateView(final ValidationResults results, final Object model) {
		super.validateView(results, model);
		if (datKW.getDate() == null) {
			results.add("KW ist ein Pflichtfeld", datKW);
		}
	}
	
	@Override
	public void storeView(final StoreResults results, final Object model) {
		super.storeView(results, model);
		if (!results.isSuccess())
			return;
		final PacketMBPSearchRequest request = getSearchRequest();
		final PacketMBPSearchReply reply = (PacketMBPSearchReply) client.sendAndWait(request);
		if (!reply.isValid()) {
			results.add(reply.getMessage());
		} else {
			resultPanel.update(reply);
		}
	}

}

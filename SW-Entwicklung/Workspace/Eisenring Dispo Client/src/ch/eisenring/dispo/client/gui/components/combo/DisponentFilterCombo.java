package ch.eisenring.dispo.client.gui.components.combo;

import java.awt.event.ItemEvent;
import java.awt.event.ItemListener;

import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.observable.Observable;
import ch.eisenring.core.observable.Observer;
import ch.eisenring.core.observable.ObserverNotificationPolicy;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.gui.components.HEAGCodeComboBox;
import ch.eisenring.logiware.code.soft.LWDisponentSirCode;

@SuppressWarnings("serial")
public class DisponentFilterCombo extends HEAGCodeComboBox<LWDisponentSirCode> {

	private final Observable<LWDisponentSirCode> observable;
	
	private final ItemListener itemListener = new ItemListener() {
		@Override
		public void itemStateChanged(final ItemEvent event) {
			final LWDisponentSirCode code = getSelectedCode();
			observable.set(code);
		}
	};

	private final Observer<LWDisponentSirCode> selectionObserver = new Observer<LWDisponentSirCode>() {
		@Override
		public void observableChanged(final Observable<LWDisponentSirCode> observable) {
			final LWDisponentSirCode code = observable.get();
			setSelectedCode(code);
		}
	};

	private final Observer<Object> updateObserver = new Observer<Object>() {
		@Override
		public void observableChanged(final Observable<Object> observable) {
			final List<LWDisponentSirCode> codes = AbstractCode.getInstances(LWDisponentSirCode.class);
			populate(codes, false);
		}
	};
	
	public DisponentFilterCombo(final Client client) {
		super(LWDisponentSirCode.class);
		this.observable = client.GROBPLANUNG_DISPONENT_FILTER;
		setFocusable(false);
		addItemListener(itemListener);
		observable.addObserver(selectionObserver, ObserverNotificationPolicy.EDT_ASYNCHRONOUS);
		client.WAITING_FOR_MODEL.addObserver(updateObserver);
		setToolTipText("<html>Grobplanungen filtern nach Disponent");
	}

}

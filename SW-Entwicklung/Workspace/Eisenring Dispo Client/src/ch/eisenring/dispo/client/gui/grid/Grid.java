package ch.eisenring.dispo.client.gui.grid;

import java.awt.Color;

import ch.eisenring.dispo.shared.model.AbstractModel;

public interface Grid {

	public static Object LOCK = new Object();

	public abstract DSPGridLayout getGridLayout();

	public abstract int getLineIndex(LineElement element);
	
	public abstract void addGridElement(GridElement element);
	
	public abstract void removeGridElement(GridElement element);

	public abstract void updateScreenPosition();

	public abstract int getXOnScreen();
	
	public abstract int getYOnScreen();
	
	public abstract int getWidth();
	
	public abstract int getHeight();
	
	public abstract void forceRepaint();

	public abstract GridElement getElementUnder(int x, int y);
	
	public default GridElement getElementForModel(final AbstractModel model) {
		return model == null ? null : getElementForModelId(model.getId());
	}

	public abstract GridElement getElementForModelId(final Long modelId);

	public abstract void requestPeriodicRepaint();
	
	public abstract void repaint();

	public abstract boolean needsPeriodicRepaint();
	
	/**
	 * Returns true if the grid should currently paint in highlighting mode.
	 */
	public abstract boolean isHighlightMode();

	/**
	 * Gets the color for element in highlighting mode
	 */
	public abstract Color getHightlightingColor(final GridElement model);

}

package ch.eisenring.dispo.client.gui.dnd;

import java.awt.event.MouseEvent;

import ch.eisenring.dispo.client.gui.grid.BlockElement;
import ch.eisenring.dispo.client.gui.grid.LineElement;
import ch.eisenring.dispo.client.gui.menu.BlockContextMenu;
import ch.eisenring.dispo.client.gui.menu.LineContextMenu;

public final class GestureContextMenu extends GestureBase {

	protected GestureContextMenu(final DnDMainHandler mainHandler) {
		super(mainHandler);
	}

	@Override
	protected boolean isGestureTrigger(final MouseEvent event) {
		return event.isPopupTrigger();
	}

	@Override
	protected void gestureDetected(final MouseEvent event, final BlockElement block) {
		final BlockContextMenu menu = new BlockContextMenu(getClient(), block);
		menu.show(event);
	}
	
	protected void gestureDetected(final MouseEvent event, final LineElement line) {
		final LineContextMenu menu = new LineContextMenu(mainHandler.client, line);
		menu.show(event);
	}

}

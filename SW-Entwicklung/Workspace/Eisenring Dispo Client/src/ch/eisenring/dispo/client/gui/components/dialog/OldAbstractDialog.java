package ch.eisenring.dispo.client.gui.components.dialog;

import java.awt.Component;
import java.awt.Container;

import ch.eisenring.core.observable.Observable;
import ch.eisenring.core.observable.Observer;
import ch.eisenring.core.observable.ObserverNotificationPolicy;
import ch.eisenring.core.resource.ImageResource;
import ch.eisenring.core.tag.TagSet;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.client.remotecontrol.IAutoOpenReceiver;
import ch.eisenring.dispo.client.remotecontrol.RemoteControlHandler;
import ch.eisenring.gui.window.AbstractBaseDialog;
import ch.eisenring.gui.window.DialogTags;
import ch.eisenring.gui.window.FeedbackHandler;
import ch.eisenring.gui.window.WindowTags;

@SuppressWarnings("serial")
public abstract class OldAbstractDialog extends AbstractBaseDialog {

	protected final Client client;

	protected final Observer<Object> autoOpenObserver = new Observer<Object>() {
		public void observableChanged(final Observable<Object> observable) {
			processAutoOpenCommands(OldAbstractDialog.this, null);
		}
	};

	protected OldAbstractDialog(final Client client,
			                    final String localUserSettingsId,
			                    final ImageResource icon) {
		super(new TagSet(
				DialogTags.DIALOG_OWNER, client.getMainWindow(),
				WindowTags.ICON, icon,
				WindowTags.USERSETTINGS, client.getUserSettings()));
		this.client = client;
		client.STATE_AUTO_OPEN_DIALOG.addObserver(autoOpenObserver, ObserverNotificationPolicy.EDT_ASYNCHRONOUS);
	}

	public final Client getClient() {
		return client;
	}
	
	@Override
	public void setVisible(final boolean visible) {
		super.setVisible(visible);
		if (!isVisible())
			client.getDnDHandler().cancelDrag();
	}

	/**
	 * Called when any auto open command is issued.
	 * Child classes that act as command receivers need to
	 * overwrite this method and check if a command for them
	 * is pending.
	 */
	protected final void processAutoOpenCommands(final Component component, final RemoteControlHandler handler) {
		final RemoteControlHandler h = handler == null ? new RemoteControlHandler(client) : handler;
		if (component instanceof IAutoOpenReceiver) {
			((IAutoOpenReceiver) component).processAutoOpenCommands(h);
		}
		if (component instanceof Container) {
			final Component[] components = ((Container) component).getComponents();
			for (int i=components.length-1; i>=0; --i) {
				processAutoOpenCommands(components[i], h);
			}
		}
	}

	/**
	 * Called when the dialog is about to become visible
	 */
	protected void onOpen() {
		super.onOpen();
		processAutoOpenCommands(this, null);
	}

	/**
	 * Called after the dialog has been closed using the "OK" gesture
	 * (usually a button in the dialog)
	 * (Precondition: isContentValid() returned true)
	 */
	protected void onOk() {
		client.getDnDHandler().cancelDrag();
		super.onOk();
	}
	
	/**
	 * Called after the dialog has been closed using the "CANCEL" gesture
	 * (close widget, cancel button or ESC key)
	 * (Precondition: none)
	 */
	@Override
	protected void onCancel() {
		client.getDnDHandler().cancelDrag();
		super.onCancel();
	}

	/**
	 * Called when the "APPLY" gesture has been triggered.
	 * (Precondition: isContentValid() returned true)
	 */
	@Override
	protected void onApply() {
		client.getDnDHandler().cancelDrag();
		super.onApply();
	}

	@Override
	public FeedbackHandler getFeedbackHandler() {
		return client.getFeedbackHandler();
	}

}

package ch.eisenring.dispo.client.util;

import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.shared.model.BinaryModel;
import ch.eisenring.dispo.shared.network.packets.PacketBinaryRequest;
import ch.eisenring.dispo.shared.network.packets.PacketBinaryResponse;

public class BinaryUtil {

	private final Client client;

	public BinaryUtil(final Client client) {
		this.client = client;
	}

	/**
	 * Synchronous load of a single binary object from the server 
	 */
	public BinaryModel getBinaryModel(final Long binaryId) {
		if (binaryId == null || binaryId == 0)
			return null;
		final PacketBinaryRequest request = PacketBinaryRequest.create(binaryId);
		final PacketBinaryResponse reply = (PacketBinaryResponse) client.sendAndWait(request);
		if (reply == null) {
			// timeout or other serious problem
			client.error("Zeitüberschreitung der Serveranfrage. Keine Antwort vom Server.");
			return null;
		} else if (reply.isValid()) {
			return reply.getBinaries().get(0);
		} else {
			// error message in reply
			client.message(reply.getMessage());
			return null;
		}
	}

	/**
	 * Synchronous load of a list of binary objects from the server 
	 */
	public List<BinaryModel> getBinaryModels(final List<Long> binaryIds) {
		if (binaryIds.size() <= 0)
			return new ArrayList<BinaryModel>();
		final PacketBinaryRequest request = PacketBinaryRequest.create(binaryIds);
		final PacketBinaryResponse reply = (PacketBinaryResponse) client.sendAndWait(request);
		if (reply == null) {
			// timeout or other serious problem
			client.error("Zeitüberschreitung der Serveranfrage. Keine Antwort vom Server.");
			return List.emptyReadOnly(BinaryModel.class);
		} else if (reply.isValid()) {
			return reply.getBinaries();
		} else {
			// error message in reply
			client.message(reply.getMessage());
			return List.emptyReadOnly(BinaryModel.class);
		}
	}

}

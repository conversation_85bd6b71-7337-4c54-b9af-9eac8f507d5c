package ch.eisenring.dispo.client.gui.montageprint;

import java.awt.GridBagLayout;

import javax.swing.JScrollPane;
import javax.swing.ScrollPaneConstants;

import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.observable.Observable;
import ch.eisenring.core.observable.Observer;
import ch.eisenring.core.observable.ObserverNotificationPolicy;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.shared.montageprint.MBPQueueStatusCode;
import ch.eisenring.dispo.shared.pojo.mbp.MBPAuftrag;
import ch.eisenring.gui.GridBagConstraints;
import ch.eisenring.gui.action.AbstractAction;
import ch.eisenring.gui.balloontip.BalloonTipManager;
import ch.eisenring.gui.components.HEAGButton;
import ch.eisenring.gui.components.HEAGCodeComboBox;
import ch.eisenring.gui.components.HEAGIntegerField;
import ch.eisenring.gui.components.HEAGLabel;
import ch.eisenring.gui.components.HEAGPanel;
import ch.eisenring.gui.components.HEAGTable;
import ch.eisenring.gui.resource.images.Images;
import ch.eisenring.print.client.gui.PrintServiceCombo;
import ch.eisenring.print.shared.model.PrintServiceIdentifier;

@SuppressWarnings("serial")
public class MBPQueuePanel extends HEAGPanel {

	protected final Client client;
	protected final PrintServiceCombo cmbPrinter;
	protected final MBPAuftragTableModel tableModel = new MBPAuftragTableModel(true);
	protected final HEAGTable table = new HEAGTable();
	protected final JScrollPane scrollPane = new JScrollPane();

	protected final HEAGButton btnStart;
	protected final HEAGButton btnStop;
	protected final HEAGButton btnClean;
	protected final HEAGIntegerField intJobCount = new HEAGIntegerField(5, true);
	protected final HEAGCodeComboBox<MBPQueueStatusCode> cmbStatus =
			new HEAGCodeComboBox<MBPQueueStatusCode>(MBPQueueStatusCode.class);

	protected final AbstractAction actionClean = new AbstractAction(
			Images.REFRESH, "Aufräumen", "Entfernt die abgeschlossenen Druckaufträge aus der Liste") {
		@Override
		protected void performAction() {
			final MBPQueue queue = client.MBP_QUEUE.get();
			queue.cleanUp();
		}
	};

	protected final AbstractAction actionStart = new AbstractAction(
			ch.eisenring.print.shared.resource.images.Images.PRINTER, "Start") {
		@Override
		protected void performAction() {
			final PrintServiceIdentifier printService = cmbPrinter.getSelectedService();
			String error = null;
			try {
				final MBPQueue queue = client.MBP_QUEUE.get();
				queue.setPrinter(printService);
				queue.setActive(true);
			} catch (final Exception e) {
				error = "Fehler: " + e.getMessage();
			}
			if (error != null) {
				final ErrorMessage message = new ErrorMessage(error);
				BalloonTipManager.show(btnStart, message);
			}
		}
	};
			
	protected final AbstractAction actionStop = new AbstractAction(
			Images.DELETE, "Stop") {
		@Override
		protected void performAction() {
			final MBPQueue queue = client.MBP_QUEUE.get();
			queue.setActive(false);
		}
	};

	protected final Observer<?> observer = new Observer<Object>() {
		@Override
		public void observableChanged(final Observable<Object> observable) {
			final MBPQueue queue = client.MBP_QUEUE.get();
			final boolean active = queue.isActive();
			actionStart.setEnabled(!active);
			actionStop.setEnabled(active);
			cmbPrinter.setEnabled(!active);
			cmbStatus.setSelectedCode(queue.getStatus());
			intJobCount.setValue(queue.getPendingCount());
			updateTableModel();
		}
	};

	public MBPQueuePanel(final Client client) {
		this.client = client;
		// load the printers available to the server!
		this.cmbPrinter = PrintServiceCombo.createRemote(client);
		this.btnClean = new HEAGButton(actionClean);
		this.btnStart = new HEAGButton(actionStart);
		this.btnStop = new HEAGButton(actionStop);
		initComponents();
		initLayout();
		client.MBP_QUEUE.addObserver(observer, ObserverNotificationPolicy.EDT_ASYNCHRONOUS);
		observer.observableChanged(null);
	}

	private void initComponents() {
		setLayout(new GridBagLayout());
		table.setModel(tableModel);
		scrollPane.setVerticalScrollBarPolicy(ScrollPaneConstants.VERTICAL_SCROLLBAR_ALWAYS);
		scrollPane.setHorizontalScrollBarPolicy(ScrollPaneConstants.HORIZONTAL_SCROLLBAR_AS_NEEDED);
		scrollPane.setViewportView(table);
		tableModel.getLayout().applyLayout(table);
		intJobCount.setEnabled(false);
		cmbStatus.setEnabled(false);
	}

	private void initLayout() {
		removeAll();
		
		add(btnStart, GridBagConstraints.button(0, 0));
		add(btnStop, GridBagConstraints.button(1, 0));
		add(new HEAGLabel("Drucker"), GridBagConstraints.label(2, 0));
		add(cmbPrinter, GridBagConstraints.fixed(3, 0));
		add(new HEAGLabel("#", false), GridBagConstraints.label(4, 0).weightX(1));
		add(intJobCount, GridBagConstraints.fixed(5, 0));
		add(cmbStatus, GridBagConstraints.fixed(6, 0));
		add(btnClean, GridBagConstraints.fixed(7, 0));
		
		add(scrollPane, GridBagConstraints.area(0, 1).gridWidthRemainder());
	}

	protected void updateTableModel() {
		final MBPQueue queue = client.MBP_QUEUE.get();
		final List<MBPAuftrag> contents = queue.getContents();
		tableModel.setContent(contents);
	}

}

package ch.eisenring.dispo.client.gui.print;

import java.awt.BorderLayout;
import java.awt.Container;
import java.awt.Dialog;

import ch.eisenring.core.resource.ImageResource;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.client.gui.components.dialog.OldAbstractDialog;
import ch.eisenring.gui.components.HEAGPanel;
import ch.eisenring.gui.util.GUIUtil;
import ch.eisenring.print.client.gui.PrintButtonPanel;
import ch.eisenring.print.shared.model.PrintServiceIdentifier;

@SuppressWarnings("serial")
public abstract class AbstractPrintDialog extends OldAbstractDialog {

	private final Object LOCK = new Object();
	private final PrintButtonPanel buttonPanel;
	private final HEAGPanel contentPanel = new HEAGPanel();
	
	protected AbstractPrintDialog(final Client client, final String localUserSettingsId, final ImageResource icon) {
		super(client, localUserSettingsId, icon);
		this.buttonPanel = new PrintButtonPanel(client);
		setModalityType(Dialog.ModalityType.APPLICATION_MODAL);
		initLayout();
		pack();
		setMinimumSize(getPreferredSize());
	}	

	private void initLayout() {
		final Container c = super.getContentPane();
		c.removeAll();
		c.setLayout(new BorderLayout());
		c.add(contentPanel, BorderLayout.CENTER);
		c.add(buttonPanel, BorderLayout.SOUTH);
	}

	public final Object getLock() {
		return LOCK;
	}
	
	@Override
	public Container getContentPane() {
		return contentPanel;
	}

	/**
	 * Gets the printer selected in the dialog
	 */
	protected PrintServiceIdentifier getSelectedPrinter() {
		return buttonPanel.getSelectedPrinter();
	}

	/**
	 * Gets the number of copies to be printed
	 */
	protected int getNumCopies() {
		return buttonPanel.getNumCopies();
	}

	@Override
	protected void onOk() {
		final Runnable task = new Runnable() {
			@Override
			public void run() {
				doPrintJob();
			}
		};
		client.getThreadPool().start(task, "PrintJob");
		super.onOk();
	}

	/**
	 * This method is called (in a separate Thread),
	 * to take care of the printing.
	 * 
	 * This should make use of openProgress(),
	 * incrementProgress() and closeProgress()!
	 */
	protected abstract void doPrintJob();
	
	protected final void invokeLater(Runnable r) {
		if (GUIUtil.isEventDispatchThread()) {
			r.run();
		} else {
			GUIUtil.invokeLater(r);
			Thread.yield();
		}
	}

}

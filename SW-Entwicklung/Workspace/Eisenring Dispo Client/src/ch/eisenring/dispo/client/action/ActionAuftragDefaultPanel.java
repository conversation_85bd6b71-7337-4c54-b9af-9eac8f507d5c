package ch.eisenring.dispo.client.action;

import ch.eisenring.dispo.client.Client;

public class ActionAuftragDefaultPanel extends AbstractDSPAction {

	private final String panelName;
	
	public ActionAuftragDefaultPanel(final Client client, final String panelName) {
		super(client, panelName);
		this.panelName = panelName;
		addObservable(client.DEFAULT_AUFTRAG_PANEL);
	}
	
	@Override
	protected void performAction() {
		client.DEFAULT_AUFTRAG_PANEL.set(panelName);
	}
	
	@Override
	protected boolean isCheckedImpl() {
		final Object value = client.DEFAULT_AUFTRAG_PANEL.get();
		return panelName.equals(value);
	}

}

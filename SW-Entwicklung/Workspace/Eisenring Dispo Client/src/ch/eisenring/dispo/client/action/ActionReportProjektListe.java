package ch.eisenring.dispo.client.action;

import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.client.gui.report.projektliste.ProjektListeWindow;
import ch.eisenring.dispo.client.resource.imgnew.Images;

public class ActionReportProjektListe extends AbstractDSPAction {

	public ActionReportProjektListe(final Client client) {
		super(client, Images.XLS, "Projektliste...");
	}

	@Override
	protected void performAction() {
		ProjektListeWindow.showDialog(client);
	}

}

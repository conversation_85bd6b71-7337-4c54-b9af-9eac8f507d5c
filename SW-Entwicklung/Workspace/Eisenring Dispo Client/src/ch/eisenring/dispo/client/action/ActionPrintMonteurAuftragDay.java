package ch.eisenring.dispo.client.action;

import java.awt.event.InputEvent;
import java.awt.event.KeyEvent;

import javax.swing.KeyStroke;

import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.client.gui.print.MonteurTagAuftragDruckDialog;
import ch.eisenring.dispo.shared.codetables.DSPRightCode;
import ch.eisenring.gui.util.GUIUtil;
import ch.eisenring.print.shared.resource.images.Images;

public class ActionPrintMonteurAuftragDay extends AbstractDSPAction {

	public ActionPrintMonteurAuftragDay(final Client client) {
		super(client, KeyStroke.getKeyStroke(KeyEvent.VK_P, InputEvent.CTRL_DOWN_MASK),
				Images.PRINTER, "Tagespläne Drucken...");
		addObservable(client.WAITING_FOR_MODEL);
		addObservable(client.CURRENT_MODEL);
		addPermissionObserver();
	}

	@Override
	protected void performAction() {
		GUIUtil.invokeLater(new Runnable() {
			@Override
			public void run() {
				MonteurTagAuftragDruckDialog dialog = new MonteurTagAuftragDruckDialog(client);
				dialog.setVisible(true);
			}
		});
	}

	@Override
	protected boolean isEnabledImpl() {
		boolean enabled = super.isEnabledImpl();
		enabled &= DSPRightCode.PRINTAUFTRAGSSCHEINE.isPermitted();
		enabled &= null != client.getCurrentModel();
		enabled &= !Boolean.TRUE.equals(client.WAITING_FOR_MODEL.get());
		return enabled;
	}

}

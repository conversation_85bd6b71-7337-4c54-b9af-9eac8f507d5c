package ch.eisenring.dispo.client.action;

import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.client.gui.auftrag.ZuordnungDialog;
import ch.eisenring.dispo.client.gui.grid.BlockElement;
import ch.eisenring.dispo.client.gui.grid.GridElement;
import ch.eisenring.dispo.client.resource.imgnew.Images;
import ch.eisenring.dispo.shared.model.AbstractZuordnung;
import ch.eisenring.gui.util.GUIUtil;

public final class ActionElementEdit extends ActionElementBase {

	public ActionElementEdit(final Client client, final GridElement gridElement) {
		super(client, gridElement, Images.EDIT, "Details _bearbeiten...");
		setDefault(true);
	}

	@Override
	protected void performBlockAction(final BlockElement blockElement) {
		final Runnable r = new Runnable() {
			@Override
			public void run() {
				final AbstractZuordnung zuordnung = (AbstractZuordnung) blockElement.getModel();
				ZuordnungDialog.showDialog(client, zuordnung, !blockElement.isDialogEditable());
				blockElement.forceRepaint();
			}
		};
		GUIUtil.invokeLater(r);
	}

}

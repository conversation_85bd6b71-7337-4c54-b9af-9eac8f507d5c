package ch.eisenring.dispo.client.gui.dnd;

import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.Set;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.collections.impl.HashSet;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.shared.model.AbstractModel;
import ch.eisenring.dispo.shared.model.ModelContext;

public final class DnDSelection {

	public final Client client;
	private final List<Long> selected = new ArrayList<>();

	public DnDSelection(final Client client) {
		this.client = client;
	}

	public void clear() {
		if (!selected.isEmpty()) {
			selected.clear();
			client.getMainWindow().repaint();
		}
	}

	public boolean isSelected(final AbstractModel model) {
		if (model == null)
			return false;
		return selected.contains(model.getId());
	}

	public void add(final AbstractModel model) {
		if (model == null)
			return;
		final Long id = model.getId();
		if (!selected.contains(id)) {
			selected.add(id);
			dump();
		}
	}

	public void addAll(final DnDSelection selection) {
		if (selection == null)
			return;
		for (final Long modelId : selection.selected) {
			if (!selected.contains(modelId)) {
				selected.add(modelId);
			}
		}
	}

	public void remove(final AbstractModel model) {
		if (model == null)
			return;
		selected.remove(model.getId());
		dump();
	}

	public void toggle(final AbstractModel model) {
		if (model == null)
			return;
		final Long id = model.getId();
		if (selected.contains(id)) {
			selected.remove(id);
		} else {
			selected.add(id);
		}
		dump();
	}

	public boolean isEmpty() {
		return selected.isEmpty();
	}
	
	public int size() {
		return selected.size();
	}

	protected void dump() {
	}

	/**
	 * Gets all Models selected in context
	 */
	public List<AbstractModel> getSelected(final ModelContext context) {
		final List<AbstractModel> result = new ArrayList<AbstractModel>();
		if (context != null) {
			for (final Long modelId : selected) {
				final AbstractModel model = context.getModel(modelId); 
				if (model != null)
					result.add(model);
			}
		}
		return result;
	}

	/**
	 * Gets the Id's of the selected models
	 */
	public Set<Long> getSelectedModelIds() {
		return new HashSet<>(selected);
	}

}

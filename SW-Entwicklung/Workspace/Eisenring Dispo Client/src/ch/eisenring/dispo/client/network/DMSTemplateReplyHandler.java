package ch.eisenring.dispo.client.network;

import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.shared.network.packets.PacketDMSTemplateReply;
import ch.eisenring.network.PacketSink;
import ch.eisenring.network.packet.AbstractPacket;

class DMSTemplateReply<PERSON><PERSON>ler extends AbstractDSPPacketHandler {

	public DMSTemplateReplyHandler(final Client client) {
		super(client, PacketDMSTemplateReply.class);
	}

	@Override
	public void handle(final AbstractPacket packet, final PacketSink sink) {
		// only for testing
		handle((PacketDMSTemplateReply) packet);
	}

	private void handle(final PacketDMSTemplateReply packet) {
		// ignore (the packet must be handled by RPC)
	}

}

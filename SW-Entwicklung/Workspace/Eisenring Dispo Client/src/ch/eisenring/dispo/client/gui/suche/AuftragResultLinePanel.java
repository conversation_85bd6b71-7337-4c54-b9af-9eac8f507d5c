package ch.eisenring.dispo.client.gui.suche;

import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;

import javax.swing.JButton;
import javax.swing.JPanel;

import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.shared.service.SearchResultEntryAuftrag;
import ch.eisenring.gui.components.HEAGDateField;
import ch.eisenring.gui.components.HEAGIntegerField;
import ch.eisenring.gui.components.HEAGLabel;
import ch.eisenring.gui.components.HEAGPanel;
import ch.eisenring.gui.components.HEAGTextField;
import ch.eisenring.gui.components.Separator;
import ch.eisenring.gui.util.GUIUtil;
import ch.eisenring.gui.util.LayoutUtil;

@SuppressWarnings("serial")
public class AuftragResultLinePanel extends HEAGPanel {

	private final Client client;
	private final HEAGTextField txtPrjNr = new HEAGTextField(255);
	private final HEAGTextField txtAbwArt = new HEAGTextField(255);
	private final HEAGIntegerField intAufNr = new HEAGIntegerField(8, false);
	private final HEAGDateField datWunsch = new HEAGDateField();
	private final JButton btnSelect = new JButton("Öffnen");
	private final SearchResultEntryAuftrag entry;

	private final ActionListener actionListener = new ActionListener() {
		@Override
		public void actionPerformed(final ActionEvent e) {
			final Runnable r = new Runnable() {
				@Override
				public void run() {
					AuftragSuchePanel.jumpToResult(client, entry);
				}
			};
			GUIUtil.invokeLater(r);
		}
	};

	public AuftragResultLinePanel(final Client client, final SearchResultEntryAuftrag entry) {
		this.client = client;
		this.entry = entry;
		btnSelect.addActionListener(actionListener);
		txtPrjNr.setFocusable(false);
		txtAbwArt.setFocusable(false);
		intAufNr.setFocusable(false);
		datWunsch.setFocusable(false);
		updateView();
		setEditable(false);
	}
	
	public static void addColumnHeaders(JPanel panel, LayoutUtil l) {
		panel.add(new HEAGPanel(), l.button());
		panel.add(new HEAGLabel("Projekt"), l.field(1));
		panel.add(new HEAGLabel("Auftrag-Nr."), l.field(1));
		panel.add(new HEAGLabel("Abwicklungsart"), l.field(1));
		panel.add(new HEAGLabel("Wunschdatum"), l.field(1));
	}

	public void add2Panel(JPanel panel, LayoutUtil l) {
		panel.add(Separator.create(), l.separator());
		panel.add(l.getHSpacer(), l.separator());
		panel.add(btnSelect, l.button());
		panel.add(txtPrjNr, l.field(1));
		panel.add(intAufNr, l.field(1));
		panel.add(txtAbwArt, l.field(1));
		panel.add(datWunsch, l.field());
	}
	
	public void updateView() {
		txtPrjNr.setText(entry.getProjektNummer());
		txtAbwArt.setText(entry.getAbwicklungsArt().toString());
		intAufNr.setValue(entry.getAuftragsNummer());
		datWunsch.setDate(entry.getWunschDatum());
	}
	
	public void setEditable(boolean editable) {
		txtPrjNr.setEditable(editable);
		txtAbwArt.setEditable(editable);
		intAufNr.setEditable(editable);
		datWunsch.setEditable(editable);
	}
	
	

}

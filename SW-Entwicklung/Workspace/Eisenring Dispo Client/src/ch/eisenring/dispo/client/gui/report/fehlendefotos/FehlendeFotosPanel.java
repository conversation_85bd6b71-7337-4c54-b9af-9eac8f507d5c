package ch.eisenring.dispo.client.gui.report.fehlendefotos;

import java.awt.GridBagLayout;
import java.util.Calendar;
import java.util.Date;

import ch.eisenring.core.datatypes.date.HEAGCalendar;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.client.resource.imgnew.Images;
import ch.eisenring.dispo.shared.network.packets.PacketFehlendeFotosRequest;
import ch.eisenring.gui.GridBagConstraints;
import ch.eisenring.gui.components.DecorationHeader;
import ch.eisenring.gui.components.HEAGDateField;
import ch.eisenring.gui.components.HEAGLabel;
import ch.eisenring.gui.components.HEAGPanel;
import ch.eisenring.gui.model.ValidationResults;
import ch.eisenring.logiware.code.pseudo.AbwicklungsartCode;

@SuppressWarnings("serial")
public class FehlendeFotosPanel extends HEAGPanel {

	protected final HEAGDateField datFrom = new HEAGDateField();
	protected final HEAGDateField datUpto = new HEAGDateField();

	public FehlendeFotosPanel(final Client client) {
		initComponents();
		initLayout();
	}
	
	private void initComponents() {
		setLayout(new GridBagLayout());
		final HEAGCalendar c = HEAGCalendar.obtainNow();
		c.add(Calendar.DAY_OF_YEAR, -3);
		c.set(Calendar.HOUR_OF_DAY, 0);
		c.set(Calendar.MINUTE, 0);
		c.set(Calendar.SECOND, 0);
		c.set(Calendar.MILLISECOND, 0);
		datUpto.setDate(c.getTime());
		c.add(Calendar.DAY_OF_YEAR, -30);
		datFrom.setDate(c.getTime());
		c.release();
	}

	private void initLayout() {
		removeAll();
		
		int y = 0;
		DecorationHeader header = new DecorationHeader(
				DecorationHeader.ICON, Images.PHOTO,
				DecorationHeader.SIZE, DecorationHeader.MEDIUM,
				DecorationHeader.LABEL, "Fehlende Fotos",
				DecorationHeader.SEPARATOR, Boolean.TRUE);
		add(header, GridBagConstraints.decorator(0, y));
	
		++y;
		add(new HEAGLabel("von"), GridBagConstraints.label(0, y));
		add(datFrom, GridBagConstraints.fixed(1, y));
		add(new HEAGLabel("   bis"), GridBagConstraints.label(2, y));
		add(datUpto, GridBagConstraints.fixed(3, y));
	}

	public PacketFehlendeFotosRequest getRequest() {
		final Date from = datFrom.getDate();
		final Date upto = datUpto.getDate();
		return PacketFehlendeFotosRequest.create(from, upto, AbwicklungsartCode.AWA135);
	}

	@Override
	public void validateView(final ValidationResults results, final Object model) {
		super.validateView(results, model);
		final Date from = datFrom.getDate();
		if (from == null)
			results.add("Kein Datum ausgewählt.", datFrom);
		final Date upto = datUpto.getDate();
		if (upto == null)
			results.add("Kein Datum ausgewählt.", datUpto);
		if (from != null && upto != null && upto.compareTo(from) <= 0)
			results.add("Muss hinter 'von' liegen", datUpto);
	}

}

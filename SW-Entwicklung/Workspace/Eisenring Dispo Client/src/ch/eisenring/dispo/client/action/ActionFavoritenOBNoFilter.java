package ch.eisenring.dispo.client.action;

import ch.eisenring.dispo.client.Client;

public final class ActionFavoritenOBNoFilter extends AbstractDSPAction {

	public ActionFavoritenOBNoFilter(final Client client) {
		super(client, "Kein Filter");
		addObservable(client.OBJEKTBETREUER_FILTER_BYPASS);
	}

	@Override
	protected boolean isCheckedImpl() {
		return Boolean.TRUE.equals(client.OBJEKTBETREUER_FILTER_BYPASS.get());
	}

	@Override
	protected void performAction() {
		client.OBJEKTBETREUER_FILTER_BYPASS.set(Boolean.TRUE);
	}

}

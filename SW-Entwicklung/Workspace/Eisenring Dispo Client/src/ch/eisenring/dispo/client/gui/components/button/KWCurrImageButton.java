package ch.eisenring.dispo.client.gui.components.button;

import java.awt.Font;
import java.awt.Graphics;
import java.awt.Graphics2D;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.awt.event.MouseListener;
import java.util.Calendar;

import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.Map;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.collections.impl.HashMap;
import ch.eisenring.core.datatypes.date.HEAGCalendar;
import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.core.observable.Observable;
import ch.eisenring.core.observable.Observer;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.client.action.ActionKWGoto;
import ch.eisenring.dispo.client.gui.look.ILook;
import ch.eisenring.dispo.client.resource.imgnew.Images;
import ch.eisenring.dispo.shared.model.Auftrag;
import ch.eisenring.dispo.shared.model.ModelContext;
import ch.eisenring.gui.menu.MenuBuilder;
import ch.eisenring.logiware.code.pseudo.AbwicklungsartCode;

@SuppressWarnings("serial")
public final class KWCurrImageButton extends ImageButton {

	static class CodeCount implements Comparable<CodeCount> {
		public AbstractCode code;
		public int count;

		@Override
		public int compareTo(final CodeCount o) {
			return AbstractCode.Order.ToString.compare(code, o.code);
		}
	}

	protected final Observer<Object> observer = new Observer<Object>() {
		@Override
		public void observableChanged(final Observable<Object> observeable) {
			updateToolTip();
		}
	};

	private final Client client;
	private String kwText = "00";
	
	private final MouseListener mouseListener = new MouseAdapter() {
		@Override
		public void mousePressed(final MouseEvent event) {
			this.mouseReleased(event);
		}
		
		@Override
		public void mouseReleased(final MouseEvent event) {
			if (!event.isConsumed() && event.isPopupTrigger()) {
				final MenuBuilder popup = ActionKWGoto.getKWGotoCurr10Popup(client, TimestampUtil.NULL_TIMESTAMP);
				popup.showPopupMenu(event);
			}
		}
		
		@Override
		public void mouseEntered(final MouseEvent event) {
			updateToolTip();
		}
	};
	
	public KWCurrImageButton(final Client client) {
		super(client, Images.KWCURR.getIcon(32), null);
		this.client = client;
		client.STATE_KWDATE.addObserver(observer);
		setFocusable(false);
		setEnabled(false);
		addMouseListener(mouseListener);
	}

	@SuppressWarnings("unchecked")
	protected void updateToolTip() {
		try {
			final HEAGCalendar c = HEAGCalendar.obtain(client.STATE_KWDATE.get());
			final StringMaker b = StringMaker.obtain();
			b.append("<html>");
			b.append("Kalenderwoche ");
			b.append(c.get(Calendar.WEEK_OF_YEAR));
			b.append(" (");
			b.append(c.get(Calendar.DAY_OF_MONTH));
			b.append(".");
			b.append(1+c.get(Calendar.MONTH));
			b.append(".");
			b.append(c.get(Calendar.YEAR));
			b.append(" - ");
			c.add(Calendar.DAY_OF_YEAR, 4);
			b.append(c.get(Calendar.DAY_OF_MONTH));
			b.append(".");
			b.append(c.get(Calendar.MONTH) + 1);
			b.append(".");
			b.append(c.get(Calendar.YEAR));
			b.append(")");

			final ModelContext context = client.getCurrentModel();
			if (context != null) {
				final List<Auftrag> list = (List) context.getModelList(Auftrag.class);
				final Map<AbwicklungsartCode, CodeCount> map = new HashMap<>();
				for (int i=list.size()-1; i>=0; --i) {
					final Auftrag auftrag = list.get(i);
					final AbwicklungsartCode abwArt = auftrag.getAbwicklungsart();
					if (abwArt == null) {
						client.warn("Der Auftragnummer " + auftrag.getAuftragnummer() + " ist keine Abwicklungsart zugewiesen");
					}
					CodeCount count = map.get(abwArt);
					if (count == null) {
						count = new CodeCount();
						count.code = abwArt;
						map.put(abwArt, count);
					}
					count.count++;
				}
				final List<CodeCount> counts = new ArrayList<CodeCount>(map.values());
				if (!counts.isEmpty()) {
					b.append("<br><br><u>Auftragsvolumen:</u>");
					for (final CodeCount count : counts) {
						b.append("<br>");
						b.append(count.count);
						b.append(' ');
						b.append(AbstractCode.getLongText(count.code, "NULL"));
					}
				}
			}
	
			setToolTipText(b.release());
		
			String kwNew = Strings.toString(c.get(Calendar.WEEK_OF_YEAR));
			if (!kwNew.equals(kwText)) {
				kwText = kwNew;
				repaint();
			}
			c.release();
		} catch (final Throwable t) {
			Logger.error(t);
			setToolTipText("Fehler: " + t.getMessage());
		}
	}
	
	@Override
	protected void paintComponent(Graphics g) {
		super.paintComponent(g);
		final ILook look = client.GRIDLOOK.get();
		final Graphics2D g2d = (Graphics2D) g;
		final Font font = g2d.getFont();
		final Font derived = font.deriveFont(Font.BOLD, (int) (font.getSize()/0.666D));
		g2d.setFont(derived);
		look.text(g2d, kwText, 0, 0, getWidth(), getHeight(), ILook.JUSTIFY_CENTER);
	}
	
	@Override
	protected boolean isVisuallyEnabled() {
		return true;
	}

}

package ch.eisenring.dispo.client.gui.abruf;

import java.awt.GridBagLayout;

import javax.swing.JButton;
import javax.swing.JScrollPane;
import javax.swing.ListSelectionModel;
import javax.swing.ScrollPaneConstants;
import javax.swing.event.ListSelectionEvent;
import javax.swing.event.ListSelectionListener;

import ch.eisenring.core.collections.Collection;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.client.resource.imgnew.Images;
import ch.eisenring.dispo.shared.codetables.AbrufModus;
import ch.eisenring.dispo.shared.network.packets.PacketBestellInfosRequest;
import ch.eisenring.gui.action.AbstractAction;
import ch.eisenring.gui.components.HEAGButton;
import ch.eisenring.gui.components.HEAGList;
import ch.eisenring.gui.components.HEAGPanel;
import ch.eisenring.gui.interfaces.DefaultButtonProvider;
import ch.eisenring.gui.util.GUIUtil;
import ch.eisenring.gui.util.LayoutUtil;

@SuppressWarnings("serial")
class SelectOrdersPanel extends HEAGPanel implements DefaultButtonProvider {

	private final Client client;
	private final HEAGList<Object> listControl;
	private final SelectOrdersListModel listModel;
	private final JScrollPane scrollPane;
	private final long callForDate;
	private final AbrufModus modus;
	
	private final ListSelectionListener selectionListener = new ListSelectionListener() {
		@Override
		public void valueChanged(final ListSelectionEvent event) {
			updateButtons();
		}
	};
	
	private final AbstractAction actionContinue = new AbstractAction("Verschieben", Images.OK_GREEN) {
		@Override
		protected void performAction() {
			performSend();
		}
	};

	private final AbstractAction actionCancel = new AbstractAction("Abbrechen", Images.CANCEL) {
		@Override
		protected void performAction() {
			GUIUtil.hideWindow(SelectOrdersPanel.this);
		}
	};

	private final HEAGButton btnContinue = new HEAGButton(actionContinue);
	private final HEAGButton btnCancel = new HEAGButton(actionCancel);
	
	public SelectOrdersPanel(final Client client,
			                 final Collection<String> projektnummern,
			                 final long callForDate,
			                 final AbrufModus modus) {
		this.client = client;
		this.modus = modus;
		this.callForDate = callForDate;
		this.scrollPane = new JScrollPane();
		this.listModel = new SelectOrdersListModel();
		this.listControl = new HEAGList<Object>();
		listModel.setContent(projektnummern);
		initComponents();
		initLayout();
	}
	
	private void initComponents() {
		scrollPane.setVerticalScrollBarPolicy(ScrollPaneConstants.VERTICAL_SCROLLBAR_ALWAYS);
		scrollPane.setHorizontalScrollBarPolicy(ScrollPaneConstants.HORIZONTAL_SCROLLBAR_NEVER);
		scrollPane.setViewportView(listControl);
		listControl.setModel(listModel);
		listControl.getSelectionModel().setSelectionMode(ListSelectionModel.MULTIPLE_INTERVAL_SELECTION);
		listControl.getSelectionModel().addListSelectionListener(selectionListener);
		btnContinue.setEnabled(false);
		btnContinue.setText(modus.getLongText());
		GUIUtil.makeSameSize(btnContinue, btnCancel);
		setLayout(new GridBagLayout());
	}

	private void initLayout() {
		removeAll();
		final LayoutUtil l = new LayoutUtil(3);
		add(scrollPane, l.area(10, 10, 3));
		add(new HEAGPanel(), l.field(1));
		add(btnContinue, l.button());
		add(btnCancel, l.button());
	}

	@Override
	public JButton getDefaultButton() {
		return btnContinue;
	}

	public void updateButtons() {
		final List<String> selection = getSelection();
		final boolean enabled = selection != null && !selection.isEmpty();
		btnContinue.setEnabled(enabled);
	}

	public void selectAll() {
		GUIUtil.invokeLater(new Runnable() {
			@Override
			public void run() {
				int size = listModel.getSize();
				if (size > 0)
				listControl.setSelectionInterval(0, size - 1);
			}
		});
	}

	public List<String> getSelection() {
		final List<Object> selection = listControl.getSelectedValuesList();
		final List<String> result = new ArrayList<String>(selection.size());
		for (final Object object : selection)
			result.add(object.toString());
		return result;
	}

	/**
	 * Opens and prepares the send mail dialog
	 */
	public void performSend() {
		final List<String> selection = getSelection();
		if (selection == null || selection.isEmpty())
			return;
		final PacketBestellInfosRequest request = PacketBestellInfosRequest.create(callForDate, modus);
		request.addProjektnummern(selection);
		client.sendPacket(request);
		GUIUtil.hideWindow(this);
	}

}

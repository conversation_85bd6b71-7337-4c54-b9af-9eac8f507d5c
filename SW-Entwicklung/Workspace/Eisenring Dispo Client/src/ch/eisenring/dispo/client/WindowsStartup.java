package ch.eisenring.dispo.client;

import ch.eisenring.app.client.platform.WindowsConfiguration;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.platform.Platform;
import ch.eisenring.core.platform.windows.WinRegistry;
import ch.eisenring.core.platform.windows.WinRegistryCompat;

import static ch.eisenring.core.platform.windows.WinRegistryCompat.*;

final class WindowsStartup extends WindowsConfiguration {

	@Override
	protected void configureRegistry(final Platform platform) throws Exception {
		final int hkey = WinRegistry.HKEY_CURRENT_USER;
		final String appDir = getAppDir();

		final String path = "Software\\Classes\\Dispomodul";
		final String iconPath = Strings.concat(path, "\\DefaultIcon");
		final String commandPath = Strings.concat(path, "\\shell\\open\\command");

		WinRegistryCompat.createKey(hkey, path);
		WinRegistryCompat.writeStringValue(hkey, path, "URL Protocol", "");
		WinRegistryCompat.writeStringValue(hkey, path, STANDARD, "URL:Dispomodul Protocol");

		WinRegistryCompat.createKey(hkey, iconPath);
		WinRegistryCompat.writeStringValue(hkey, iconPath, STANDARD, Strings.concat(
				appDir, "\\bin\\DispoLinkStarter.exe,0"));

		WinRegistryCompat.createKey(hkey, commandPath);
		WinRegistryCompat.writeStringValue(hkey, commandPath, STANDARD, Strings.concat(
				"\"", appDir, "\\bin\\DispoLinkStarter.exe\" \"%1\""));
	}

}

package ch.eisenring.dispo.client.logic;

import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.dispo.client.gui.dnd.DnDHintAccept;
import ch.eisenring.dispo.client.gui.dnd.DnDHintDrag;
import ch.eisenring.dispo.client.gui.dnd.DnDHintDrop;
import ch.eisenring.dispo.client.gui.grid.LineElement;
import ch.eisenring.dispo.shared.model.AbstractModel;
import ch.eisenring.dispo.shared.model.Auftrag;

public final class GrobPlanungLine extends LineElement {

	public GrobPlanungLine(AbstractModel model) {
		super(model);
	}

	@Override
	public String getLabel() {
		final AbstractModel model = getModel();
		if (model instanceof Auftrag) {
			final Auftrag auftrag = (Auftrag) model;
			return Strings.concat(auftrag.getProjektnummer(), "   ", Auftrag.getOrtAndPLZ(auftrag.getProjektbezeichnung()));
		}
		return super.getLabel();
	}

	@Override
	public DnDHintAccept getAcceptHint() {
		return DnDAcceptGrobPlanung.INSTANCE;
	}

	@Override
	public DnDHintDrag getDragHint() {
		return DnDDragDenied.INSTANCE;
	}

	@Override
	public DnDHintDrop getDropHint() {
		return DnDDropDenied.INSTANCE;
	}

	@Override
	public boolean isEditable() {
		return false;
	}

}

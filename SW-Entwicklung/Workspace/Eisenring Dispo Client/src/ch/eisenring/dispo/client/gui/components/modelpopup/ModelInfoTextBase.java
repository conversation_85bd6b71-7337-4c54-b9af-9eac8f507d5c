package ch.eisenring.dispo.client.gui.components.modelpopup;

import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.datatypes.date.Period;
import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.core.datatypes.date.format.HEAGDateFormat;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.datatypes.strings.format.api.Formats;
import ch.eisenring.core.iterators.Filter;
import ch.eisenring.dispo.shared.codetables.DSPGranitMontageSCCode;
import ch.eisenring.dispo.shared.codetables.DSPMaterialSCCode;
import ch.eisenring.dispo.shared.codetables.DSPRightCode;
import ch.eisenring.dispo.shared.lw.LWBestellInfo;
import ch.eisenring.dispo.shared.model.AbstractModel;
import ch.eisenring.dispo.shared.model.Auftrag;
import ch.eisenring.dispo.shared.model.FeinZuordnung;
import ch.eisenring.dispo.shared.model.GrobZuordnung;
import ch.eisenring.dispo.shared.network.packets.PacketInfoPopupReply;
import ch.eisenring.dispo.shared.util.AuftragUtil;
import ch.eisenring.logiware.code.pseudo.AbwicklungsartCode;
import ch.eisenring.logiware.code.soft.GSECode;
import ch.eisenring.lw.api.LWAuftragPositionData;
import ch.eisenring.lw.api.LWEtappeData;

public abstract class ModelInfoTextBase {

	protected final static HEAGDateFormat DATE_FORMAT = HEAGDateFormat.get("dd.MM.yyyy");

	@SuppressWarnings("unused")
	private int tableRowCount;
	protected final InfoPopupRequest request;
	protected final AbstractModel model;
	
	protected ModelInfoTextBase(final InfoPopupRequest request) {
		this.request = request;
		this.model = request.getBlockElement().getModel();
	}

	public static ModelInfoTextBase create(final InfoPopupRequest request) {
		final AbstractModel model = request.getBlockElement().getModel();
		if (model instanceof FeinZuordnung)
			return new FeinZuordnungInfoText(request);
		else if (model instanceof GrobZuordnung)
			return new GrobZuordnungInfoText(request);
		else if (model instanceof Auftrag) 
			return new AuftragInfoText(request);
		else
			return new NullInfoText();
	}
	
	@Override
	public String toString() {
		final StringMaker builder = StringMaker.obtain(512);
		builder.setLength(0);
		tableRowCount = 0;
		builder.append("<HTML><TABLE BORDER=0 CELLSPACING=0 CELLPADDING=0>");
		if (model == null) {
			appendTableLine(builder, "&LT;NULL&GT;", "&LT;NULL&GT;");
		} else {
			buildInfoText(builder);
		}
		builder.append("</TABLE>");
		return builder.release();
	}

	protected abstract void buildInfoText(final StringMaker builder);

	protected final void appendTableLine(final StringMaker builder, final Object label, final Object item) {
		appendTableLine(builder, false, label, item);
	}

	protected final void appendTableLine(final StringMaker builder, final boolean isHTML, final Object label, final Object item) {
		builder.append("<TR><TD VALIGN=TOP><B>");
		if (Strings.isEmpty(label)) {
			builder.append("&nbsp;");
		} else if (isHTML) {
			builder.append(label);
		} else {
			Strings.toHTMLEntities(Strings.toCharSequence(label), builder);
		}
		builder.append(':');
		builder.append("&nbsp;&nbsp;&nbsp;</B></TD><TD>");
		if (item == null) {
			builder.append("&nbsp;");
		} else if (isHTML) {
			builder.append(item);
		} else {
			Strings.toHTMLEntities(Strings.toCharSequence(item), builder);
		}
		builder.append("</TD></TR>");
		++tableRowCount;
	}

	protected final void appendAuftragInfo(final StringMaker builder, final Auftrag auftrag) {
		if (auftrag == null)
			return;
		final StringMaker maker = StringMaker.obtain();
		// add info for Auftrag
		maker.setLength(0);
		maker.append(auftrag.getProjektnummer());
		maker.append(" / ");
		maker.append(auftrag.getAuftragnummer());
		maker.append(" (");
		maker.append(auftrag.getAbwicklungsart());
		maker.append(")");
		appendTableLine(builder, "Projekt/Auftrag", maker);
		{ // Wunschdatum / Zeit und ggf. Etappe
			final LWEtappeData etappe = getEtappe();
			final String caption = etappe == null
					? "Wunschdatum / Zeit" : "Wunschdatum / Zeit (Etappe)";
			maker.setLength(0);
			maker.append("<B>");
			maker.append(auftrag.getWunschdatum(), TimestampUtil.DATE10);
			maker.append("</B>&nbsp;");
			maker.append(auftrag.getTerminZeit(), Formats.TRIM);
			if (etappe != null) {
				maker.append("&nbsp;&nbsp;&nbsp;(");
				Period.Util.appendToDatePeriod(maker, etappe);
				maker.append(")");
			}
			appendTableLine(builder, true, caption, maker);
		}
		// Bauherr
		appendTableLine(builder, "Bauherr", auftrag.getProjektbezeichnung());
		// Vertreter
		appendTableLine(builder, "Verkäufer", auftrag.getVerkaeufer());
		appendTableLine(builder, "Kundenberater", auftrag.getKundenberater());
		appendTableLine(builder, "Projektleiter KB", auftrag.getObjektbetreuer());
		//appendTableLine("AVOR-Team", auftrag.getAVORTeam());
		appendTableLine(builder, "Disponent", auftrag.getDisponent());
		appendTableLine(builder, true,"<U>Ausführender Monteur</U>",
				Strings.toHTMLEntities(Strings.toString(auftrag.getChefMonteur())));
		appendTableLine(builder, true,"<I>Küche (135) montiert von</I>",
				Strings.toHTMLEntities(AbstractCode.getLongText(auftrag.getMonteur135er(), "-")));
		// Ausführung
		appendTableLine(builder, "Ausführung", auftrag.getAusfuehrung());
		appendTableLine(builder, "Abdeckung", auftrag.getAbdeckung());
		// special case: Granitmonage durch Sirnach SC
		if (auftrag.getGSEAuftrag().equals(GSECode.GSE_2503)
				&& DSPGranitMontageSCCode.GRANITMONTAGE_JA.equals(auftrag.getGranitmontageSCCode())) {
			final String message = DSPMaterialSCCode.MATERIAL_VORHANDEN.equals(auftrag.getMaterialSCCode())
					? "Material an Lager SIRS" : "Material fehlt";
			appendTableLine(builder, "Ausstand Abdeckung", message);
		}
		// Granitmontage durch Sirnach
		appendTableLine(builder, "Granitmontage durch Sirnach", 
				AbstractCode.getLongText(auftrag.getGranitmontage(), ""));
		// Bemerkungen
		appendTableLine(builder, "Kopftext", auftrag.getKopfText());
		if (DSPRightCode.KUECHENBEWERTUNG_ANSEHEN.isPermitted()) {
			appendTableLine(builder, "Bewertung", 
					 AuftragUtil.formatKuechenBewertungPercent(auftrag.getKuechenBewertungPercent()));
			appendTableLine(builder, "Bewertung Punktwert", 
					 AuftragUtil.formatKuechenBewertungPercent(auftrag.getPunktwertPercent()));
		}
		appendTableLine(builder, "Bemerkungen (Auftrag)", auftrag.getBemerkungen());

		// BestellInfos
		builder.append("<TR><TD COLSPAN=2 VALIGN=TOP>");
		builder.append(getBestellInfo(auftrag));
		builder.append("</TD></TR>");
		appendPositionen(builder, "<TR><TD COLSPAN=2 VALIGN=TOP>", "</TD></TR>");
		maker.release();
	}

	protected final String getBestellInfo(final Auftrag auftrag) {
		final PacketInfoPopupReply reply = request.reply;
		if (reply == null)
			return "";
		final StringMaker b = StringMaker.obtain(512);
		final List<LWBestellInfo> infos = reply.getBestellInfos();
		if (infos.isEmpty())
			return "";
		b.append("<HR><TABLE>"
			   + "<TR><TD COLSPAN=7><B>Bestell-Info:&nbsp;&nbsp;&nbsp;</B></TD></TR>"
			   + "<TR>"
			   + "<TD><B>Belegnr:&nbsp;&nbsp;&nbsp;</B></TD>"
			   + "<TD><B>Lieferant:&nbsp;&nbsp;&nbsp;</B></TD>"
			   + "<TD><B>Bestellnr:&nbsp;&nbsp;&nbsp;</B></TD>"
			   + "<TD><B>Wunschd.:&nbsp;&nbsp;&nbsp;</B></TD>"
			   + "<TD><B>KOK:&nbsp;&nbsp;&nbsp;</B></TD>"
			   + "<TD><B>Baustelle?&nbsp;&nbsp;&nbsp;</B></TD>"
			   + "<TD><B>Lieferort<br />(Ident-Nr)</B></TD>"
			   + "</TR>");
		// Bestellungen
		appendBestellInfoTableSection(b, infos, AbwicklungsartCode.AWA500);
		// Nachbestellungen
		b.append("<TR><TD COLSPAN=7><HR></TD></TR>");
		appendBestellInfoTableSection(b, infos, AbwicklungsartCode.AWA505);
		b.append("</TABLE>");
		return b.release();
	}

	private void appendBestellInfoTableSection(final StringMaker target, final List<LWBestellInfo> infos, final AbwicklungsartCode abwArt) {
		final List<? extends LWBestellInfo> bestellInfos = getBestellInfos(infos, abwArt);
		if (bestellInfos.isEmpty())
			return;
		bestellInfos.sort(LWBestellInfo.Order.Default);
		for (final LWBestellInfo info : bestellInfos) {
			final String belegNr = Strings.trim(info.getBelegNummer());
			final String lieferant = Strings.trim(info.getLieferantenBezeichnung());
			final String bestellNr = Strings.trim(info.getBestellNummer());
			final String baustelle = Strings.trim(info.getBaustellenLieferungText());
			final String lieferSubjektId = Strings.trim(info.getLieferSubjektId());

			target.append("<TR><TD>");
			Strings.toHTMLEntities(belegNr, target);
			target.append("&nbsp;&nbsp;&nbsp;</TD><TD NOWRAP>");
			Strings.toHTMLEntities(lieferant, target);
			target.append("&nbsp;&nbsp;&nbsp;</TD><TD NOWRAP>");
			Strings.toHTMLEntities(bestellNr, target);
			target.append("&nbsp;&nbsp;&nbsp;</TD><TD NOWRAP>");
			target.append(info.getWunschDatum(), DATE_FORMAT);
			target.append("&nbsp;&nbsp;&nbsp;</TD><TD NOWRAP>");
			{
				final long kokDatum = info.getKOKDatum();
				if (TimestampUtil.isNull(kokDatum))
					target.append('-');
				else
					target.append(kokDatum, DATE_FORMAT);
			}
			target.append("&nbsp;&nbsp;&nbsp;</TD><TD NOWRAP>");
			Strings.toHTMLEntities(baustelle, target);
			target.append("&nbsp;&nbsp;&nbsp;</TD><TD NOWRAP>");
			Strings.toHTMLEntities(lieferSubjektId, target);
			target.append("</TD></TR>");
		}
	}

	protected void appendPositionen(final StringMaker builder, final String beginTag, final String endTag) {
		final List<LWAuftragPositionData> positionen = request.getPositionen();
		if (positionen == null || positionen.isEmpty())
			return;
		positionen.sort(LWAuftragPositionData.Order.PositionsNummer);
		builder.append(beginTag);
		builder.append("<HR><TABLE>"
				   + "<TR><TD COLSPAN=3><B>Auftrag Positionen:&nbsp;&nbsp;&nbsp;</B></TD></TR>"
				   + "<TR>"
				   + "<TD><B>Pos-Nr.:&nbsp;&nbsp;&nbsp;</B></TD>"
				   + "<TD><B>Prod-Nr.:&nbsp;&nbsp;&nbsp;</B></TD>"
				   + "<TD><B>Bezeichnung:&nbsp;&nbsp;&nbsp;</B></TD>"
				   + "<TD><B>Menge:&nbsp;&nbsp;&nbsp;</B></TD>"
				   + "</TR>");
		for (final LWAuftragPositionData position : positionen) {
			builder.append("<TR><TD>");
			Strings.toHTMLEntities(position.getPositionsNummerText(), builder);
			builder.append("&nbsp;&nbsp;&nbsp;</TD><TD NOWRAP>");
			Strings.toHTMLEntities(position.getProduktNummer(), builder);
			builder.append("&nbsp;&nbsp;&nbsp;</TD><TD NOWRAP>");
			Strings.toHTMLEntities(Strings.limit(position.getProduktBezeichnung(), 60), builder);
			builder.append("&nbsp;&nbsp;&nbsp;</TD><TD NOWRAP>");
			Strings.toHTMLEntities(position.getBestellMengeText(), builder);
			builder.append("&nbsp;&nbsp;&nbsp;</TD></TR>");
		}
		builder.append("</TABLE>");
		builder.append(endTag);
	}

	private List<LWBestellInfo> getBestellInfos(final java.util.Collection<LWBestellInfo> infos, final AbwicklungsartCode awa) {
		final Filter<LWBestellInfo> filter = new Filter<LWBestellInfo>() {
			@Override
			public boolean accepts(final LWBestellInfo info) {
				return info != null && awa.equals(info.getAbwicklungsart());
			}
		};
		return filter.filter(infos);
	}

	private LWEtappeData getEtappe() {
		final InfoPopupRequest request = this.request;
		if (request == null)
			return null;
		final PacketInfoPopupReply reply = request.reply;
		return reply == null ? null : reply.getEtappe();
	}

}

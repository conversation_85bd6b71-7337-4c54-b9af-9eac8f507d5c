package ch.eisenring.dispo.client.network;

import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.shared.network.packets.PacketInfoReply;
import ch.eisenring.network.PacketSink;
import ch.eisenring.network.implementation.PacketDispatchMode;
import ch.eisenring.network.packet.AbstractPacket;

class InfoReplyHandler extends AbstractDSPPacketHandler {

	public InfoReplyHandler(final Client client) {
		super(client, PacketInfoReply.class, PacketDispatchMode.ASYNCHRONOUS);
	}

	@Override
	public void handle(final AbstractPacket abstractPacket, PacketSink sink) {
		// packet is supposed to be handled through an RPCHandler
	}

}

package ch.eisenring.dispo.client.gui.endmontage;

import java.awt.GridBagLayout;
import java.awt.event.MouseEvent;

import javax.swing.ScrollPaneConstants;

import ch.eisenring.app.shared.network.RPCContext;
import ch.eisenring.app.shared.network.RPCHandler;
import ch.eisenring.app.shared.network.RPCHandlerEDT;
import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.client.action.AbstractDSPAction;
import ch.eisenring.dispo.client.action.ActionJumpToDMS;
import ch.eisenring.dispo.shared.endmontageprint.EndmontagePrintDocument;
import ch.eisenring.dispo.shared.network.packets.PacketDMSBatchPrintReply;
import ch.eisenring.dispo.shared.network.packets.PacketDMSBatchPrintRequest;
import ch.eisenring.dispo.shared.network.packets.PacketDMSEndmontageChoiceReply;
import ch.eisenring.dms.service.api.DMSObjectIdentity;
import ch.eisenring.gui.GridBagConstraints;
import ch.eisenring.gui.balloontip.BalloonTipManager;
import ch.eisenring.gui.components.DecorationHeader;
import ch.eisenring.gui.components.HEAGButton;
import ch.eisenring.gui.components.HEAGLabel;
import ch.eisenring.gui.components.HEAGPanel;
import ch.eisenring.gui.components.HEAGScrollPane;
import ch.eisenring.gui.components.HEAGTable;
import ch.eisenring.gui.components.Spacer;
import ch.eisenring.gui.menu.MenuBuilder;
import ch.eisenring.gui.util.GUIUtil;
import ch.eisenring.print.client.gui.PrintServiceCombo;
import ch.eisenring.print.shared.model.PrintServiceIdentifier;
import ch.eisenring.print.shared.resource.images.Images;

@SuppressWarnings("serial")
final class EndmontageDossierPanel extends HEAGPanel {

	public final Client client;
	final PacketDMSEndmontageChoiceReply reply;
	final AbstractDSPAction actionPrint;
	
	final DecorationHeader header;
	final PrintServiceCombo cmbPrinter;
	final HEAGButton btnPrint;
	final HEAGButton btnDMS;

	final EndmontageDossierTableModel tableModel = new EndmontageDossierTableModel();
	final HEAGTable table = new HEAGTable(tableModel) {
		@Override
		protected void rowSingleClicked(final int rowIndex, final Object rowObject, final MouseEvent event) {
			if (rowObject instanceof EndmontagePrintDocument) {
				final EndmontagePrintDocument document = (EndmontagePrintDocument) rowObject;
				document.setSelected(!document.isSelected());
				repaint();
			}
		}
		@Override
		protected MenuBuilder getRowContextMenu(final int rowIndex, final Object rowObject, final MouseEvent event) {
			MenuBuilder result = null;
			if (rowObject instanceof EndmontagePrintDocument) {
				result = new MenuBuilder();
				final DMSObjectIdentity id = ((EndmontagePrintDocument) rowObject).getObject();
				result.add(new ActionShowDocument(client, id));
			}
			return result;
		}
	};

	final HEAGScrollPane scrollPane = new HEAGScrollPane(table);
	
	public EndmontageDossierPanel(final Client client, final PacketDMSEndmontageChoiceReply reply) {
		super(new GridBagLayout());
		this.client = client;
		this.reply = reply;

		// load the printers available to the server!
		this.cmbPrinter = PrintServiceCombo.createRemote(client);

		// print action
		actionPrint = new AbstractDSPAction(client, Images.PRINTER, "Drucken") {
			@Override
			protected void performAction() {
				doPrint();
			}
		};		
		this.btnPrint = new HEAGButton(actionPrint);
		this.btnDMS = new HEAGButton(new ActionJumpToDMS(client, reply.getProjektKey()));
		this.header = new DecorationHeader(
				DecorationHeader.ICON, Images.PRINTER,
				DecorationHeader.SIZE, DecorationHeader.MEDIUM,
				DecorationHeader.LABEL, Strings.concat("Endmontagedossier ", reply.getProjektKey().getProjektnummer())
			);
		initComponents();
		initLayout();
	}
	
	private void initComponents() {
		tableModel.applyLayout(table);
		scrollPane.setVerticalScrollBarPolicy(ScrollPaneConstants.VERTICAL_SCROLLBAR_ALWAYS);
		scrollPane.setHorizontalScrollBarPolicy(ScrollPaneConstants.HORIZONTAL_SCROLLBAR_AS_NEEDED);
		tableModel.setContent(reply.getDocuments());
	}
	
	private void initLayout() {
		removeAll();
		int y = 0;
		add(header, GridBagConstraints.field(0, y).gridWidthRemainder());
		++y;
		add(btnPrint, GridBagConstraints.button(0, y));
		add(new HEAGLabel("Ausgabe auf Drucker"), GridBagConstraints.label(1, y));
		add(cmbPrinter, GridBagConstraints.fixed(2, y));
		add(new Spacer(), GridBagConstraints.spacer(3, y).weight(1, 0));
		add(btnDMS, GridBagConstraints.button(4, y));
		++y;
		add(scrollPane, GridBagConstraints.area(0, y).gridWidthRemainder().weight(1, 1));
	}

	private PacketDMSBatchPrintRequest getPrintRequest() {
		final PrintServiceIdentifier printer = cmbPrinter.getSelectedService();
		final String name = Strings.concat("Endmontagedossier_", reply.getProjektKey().getProjektnummer());
		final List<DMSObjectIdentity> documentIds = new ArrayList<>();
		for (int i=0; i<tableModel.getRowCount(); ++i) {
			final EndmontagePrintDocument document = tableModel.getRow(i);
			if (document.isSelected())
				documentIds.add(document.getObject());
		}
		return PacketDMSBatchPrintRequest.create(printer, name, documentIds);
	}

	private RPCHandler printRPC = new RPCHandlerEDT() {
		@Override
		public void timeoutOccured(final RPCContext rpcContext) {
			client.message(ErrorMessage.TIMEOUT);
			GUIUtil.getWindowAncestor(EndmontageDossierPanel.this).setEnabled(true);
		}
		
		@Override
		public void replyReceived(final RPCContext rpcContext) {
			GUIUtil.getWindowAncestor(EndmontageDossierPanel.this).setEnabled(true);
			final PacketDMSBatchPrintReply reply = (PacketDMSBatchPrintReply) rpcContext.getReply();
			if (!reply.isValid())
				BalloonTipManager.show(btnPrint, reply.getMessage());
			else
				GUIUtil.hideWindow(EndmontageDossierPanel.this);
		}
	};

	private void doPrint() {
		final PacketDMSBatchPrintRequest request = getPrintRequest();
		if (request.getObjectIds().isEmpty()) {
			BalloonTipManager.show(btnPrint, "Keine Dokumente zum Drucken ausgewählt");
			return;
		}
		client.sendPacket(printRPC, request);
		GUIUtil.getWindowAncestor(this).setEnabled(false);
	}

}

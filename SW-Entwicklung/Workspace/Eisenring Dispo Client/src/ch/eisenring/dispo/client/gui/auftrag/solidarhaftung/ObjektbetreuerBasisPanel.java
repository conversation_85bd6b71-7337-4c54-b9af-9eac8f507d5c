package ch.eisenring.dispo.client.gui.auftrag.solidarhaftung;

import java.awt.GridBagLayout;

import javax.swing.BorderFactory;
import javax.swing.JTextField;

import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.client.gui.auftrag.AuftragDetailPanel;
import ch.eisenring.dispo.client.gui.auftrag.abrechnung.YesNoBinding;
import ch.eisenring.dispo.client.gui.auftrag.basisprojekt.BasisProjektHolder;
import ch.eisenring.dispo.client.gui.auftrag.basisprojekt.BasisProjektView;
import ch.eisenring.dispo.service.model.DSPBasisProjekt;
import ch.eisenring.dispo.shared.codetables.DSPRightCode;
import ch.eisenring.dispo.shared.metamodel.DSPBasisProjektMeta;
import ch.eisenring.gui.GridBagConstraints;
import ch.eisenring.gui.components.CompoundLinePanel;
import ch.eisenring.gui.components.HEAGCheckBox;
import ch.eisenring.gui.components.HEAGCodeComboBox;
import ch.eisenring.gui.components.HEAGFloatingButton;
import ch.eisenring.gui.components.HEAGLabel;
import ch.eisenring.gui.components.HEAGTextArea;
import ch.eisenring.gui.components.HEAGTextField;
import ch.eisenring.gui.components.Separator;
import ch.eisenring.gui.interfaces.ValueEditor;
import ch.eisenring.gui.model.StoreResults;
import ch.eisenring.gui.util.GUIUtil;
import ch.eisenring.logiware.code.soft.LWMonteurSirCode;
import ch.eisenring.logiware.gui.LWProjektKeyField;
import ch.eisenring.model.gui.POJOBinding;

@SuppressWarnings("serial")
class ObjektbetreuerBasisPanel extends AuftragDetailPanel implements BasisProjektView {

	protected final LWProjektKeyField keyBasis = new LWProjektKeyField();
	protected final HEAGCheckBox chkObjektuebergabe = new HEAGCheckBox("Objektübergabe", new YesNoBinding(DSPBasisProjektMeta.ATR_OBJEKTUEBERGABE));
	protected final HEAGTextField txtAnzahlKuechen = new HEAGTextField(new POJOBinding(DSPBasisProjektMeta.ATR_ANZAHLKUECHEN) {
		protected Object modelValueToEditorValue(final ValueEditor editor, final Object modelValue) {
			return Strings.toString(modelValue);
		}
		protected Object editorValueToModelValue(final Object model, final Object editorValue) {
			return Strings.toString(editorValue);
		}
	});
	protected final HEAGCheckBox chkWerkvertrag = new HEAGCheckBox("Werkvertrag im DMS abgelegt", new YesNoBinding(DSPBasisProjektMeta.ATR_WERKVERTRAG));
	protected final HEAGCodeComboBox<LWMonteurSirCode> cmbBauleitenderMonteur = new HEAGCodeComboBox<LWMonteurSirCode>(LWMonteurSirCode.class, new POJOBinding(DSPBasisProjektMeta.ATR_BAULEITENDER_MONTEUR));
	protected final HEAGTextArea txtCommentOB = new HEAGTextArea(new POJOBinding(DSPBasisProjektMeta.ATR_COMMENT_OB));
	protected final HEAGTextArea txtCommentWkv = new HEAGTextArea(new POJOBinding(DSPBasisProjektMeta.ATR_COMMENT_WERKVERTRAG));
	protected final HEAGTextField txtEmailSolidarhaftung = new HEAGTextField(
			new POJOBinding(DSPBasisProjektMeta.ATR_EMAILSOLIDARHAFTUNG) {
		protected Object modelValueToEditorValue(final ValueEditor editor, final Object modelValue) {
			return Strings.toString(modelValue);
		}
		protected Object editorValueToModelValue(final Object model, final Object editorValue) {
			return Strings.toString(editorValue);
		}
	});
	protected final HEAGCheckBox chkDeklaration = new HEAGCheckBox("Deklaration Solidarhaftung versendet und im DMS abgelegt",
			new YesNoBinding(DSPBasisProjektMeta.ATR_DEKLARATION_SOLIDARHAFTUNG));
	
	protected final ActionSolidarhaftungSelect actionMail;
	protected final HEAGFloatingButton btnMail;
	
	BasisProjektHolder basisProjektHolder;

	public void setBasisProjektHolder(final BasisProjektHolder basisProjektHolder) {
		this.basisProjektHolder = basisProjektHolder;
	}

	public ObjektbetreuerBasisPanel(final Client client) {
		super(client);
		this.actionMail = new ActionSolidarhaftungSelect(client, this);
		this.btnMail = new HEAGFloatingButton(actionMail);
		initComponents();
		initLayout();
	}

	private void initComponents() {
		setBorder(BorderFactory.createTitledBorder("Kommission Basis"));
		setLayout(new GridBagLayout());
		GUIUtil.setMinSizeForText(txtAnzahlKuechen, 8);
		txtAnzahlKuechen.setHorizontalAlignment(JTextField.RIGHT);
	}

	private void initLayout() {
		int y = -1;

		++y;
		add(new HEAGLabel("Basisprojekt"),  GridBagConstraints.label(0, y));
		add(keyBasis, GridBagConstraints.field(1, y).gridWidthRemainder());

		++y;
		add(new HEAGLabel("Objektübergabe"), GridBagConstraints.label(0, y));
		add(chkObjektuebergabe, GridBagConstraints.field(1, y));
		add(new HEAGLabel("Anzahl Küchen"), GridBagConstraints.label(2, y, GridBagConstraints.EAST));
		add(txtAnzahlKuechen, GridBagConstraints.fixed(3, y));
		
		++y;
		add(new HEAGLabel("Bauleitender Monteur"), GridBagConstraints.label(0, y));
		add(cmbBauleitenderMonteur, GridBagConstraints.field(1, y).gridWidthRemainder());
		
		++y;
		add(new HEAGLabel("Bemerkungen OB"), GridBagConstraints.label(0, y));
		add(txtCommentOB, GridBagConstraints.area(1, y).gridWidthRemainder());

		++y;
		add(new HEAGLabel("<html>Bemerkungen<br>Werkvertrag"), GridBagConstraints.label(0, y));
		add(txtCommentWkv, GridBagConstraints.area(1, y).gridWidthRemainder());
		
		++y;
		add(Separator.create(), GridBagConstraints.separator(0, y));

		++y;
		add(new HEAGLabel("Werkvertrag"), GridBagConstraints.label(0, y));
		add(chkWerkvertrag, GridBagConstraints.field(1, y).gridWidthRemainder());

		++y;
		add(Separator.create(), GridBagConstraints.separator(0, y));

		++y;
		add(new HEAGLabel("Solidarhaftung"), GridBagConstraints.label(0, y).gridHeight(2));
		add(chkDeklaration, GridBagConstraints.field(1, y).gridWidthRemainder());
		++y; {
			final CompoundLinePanel line = new CompoundLinePanel();
			line.addLabel(new HEAGLabel("Empfänger Mail"));
			line.addField(txtEmailSolidarhaftung, 1);
			line.addButton(btnMail);
			add(line, GridBagConstraints.field(1, y).gridWidthRemainder());
		}
	}

	// --------------------------------------------------------------
	// ---
	// --- ModelView implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public void updateView(final Object model) {
		basisProjektHolder.updateView(model);
		super.updateView(model);
		keyBasis.updateView(basisProjektHolder.getBasisKey());
	}
	
	@Override
	public void updateModel(final Object model) {
		super.updateModel(basisProjektHolder.getBasisProjekt());
	}

	@Override
	public void storeView(final StoreResults results, final Object model) {
		super.storeView(results, basisProjektHolder.getBasisProjekt());
		basisProjektHolder.store(results);
	}

	@Override
	public void evaluateChanged(final Object model) {
		super.evaluateChanged(basisProjektHolder.getBasisProjekt());
	}

	// --------------------------------------------------------------
	// ---
	// --- HEAGPanelLazy implementation
	// ---
	// --------------------------------------------------------------
	@Override
	protected void showingFirstTimeImpl() {
		basisProjektHolder.showingFirstTimeImpl();
	}

	@Override
	public void updateEditableImpl() {
		final DSPBasisProjekt basisProjekt = basisProjektHolder.getBasisProjekt();
		{
			final boolean editable = basisProjekt != null && isEditable()
					&& DSPRightCode.REITER_OB_EDIT.isPermitted();
			chkObjektuebergabe.setEditable(editable);
			txtAnzahlKuechen.setEditable(false);
			chkWerkvertrag.setEditable(false);
			cmbBauleitenderMonteur.setEditable(editable);
			txtCommentOB.setEditable(editable);
			txtEmailSolidarhaftung.setEditable(editable);
			chkDeklaration.setEditable(false);
		} {	// field controlled separately
			final boolean editable = basisProjekt != null && isEditable()
					&& DSPRightCode.GL_AUSWERTUNG.isPermitted();
			txtCommentWkv.setEditable(editable);
		}
	}

}

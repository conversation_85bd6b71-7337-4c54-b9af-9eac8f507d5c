package ch.eisenring.dispo.client.network;

import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.shared.network.NetworkStats;
import ch.eisenring.dispo.shared.network.packets.PacketStatsRequest;
import ch.eisenring.dispo.shared.network.packets.PacketStatsResponse;
import ch.eisenring.network.StreamableConnection;
import ch.eisenring.network.PacketSink;
import ch.eisenring.network.PingStatistics;
import ch.eisenring.network.implementation.PacketDispatchMode;
import ch.eisenring.network.packet.AbstractPacket;

public final class StatsRequestHandler extends AbstractDSPPacketHandler {

	StatsRequestHandler(final Client client) {
		super(client, PacketStatsRequest.class, PacketDispatchMode.ASYNCHRONOUS);
	}

	@Override
	public void handle(final AbstractPacket abstractPacket, final PacketSink sink) {
		final NetworkStats stats = getClientStats(client);
		// send statistics
		sink.sendPacket(PacketStatsResponse.create(stats), false);
	}

	public static NetworkStats getClientStats(final Client client) {
		// get the client statistcis
		final NetworkStats clientStats = new NetworkStats();
		clientStats.update();
		clientStats.update(client.getContextCache());
		clientStats.update(client.getCore().getCoreStatistics());
		
		// get ping stats
		final StreamableConnection connection = client.getConnection(false);
		final PingStatistics ping = connection == null ? (PingStatistics) null : connection.getPingStatistics();
		clientStats.copy(ping);
		
		return clientStats;
	}

}

package ch.eisenring.dispo.client.gui.employee;

import ch.eisenring.dispo.shared.model.MasterMitarbeiter;
import ch.eisenring.gui.ManualBinding;
import ch.eisenring.gui.components.HEAGPanel;

@SuppressWarnings("serial")
public class EmployeeBasePanel extends HEAGPanel {

	public static abstract class EmployeeBinding extends ManualBinding {

		protected EmployeeBinding() {
			this(1);
		}

		public EmployeeBinding(final int length) {
			super(length);
		}

		@Override
		protected boolean isValidModel(final Object model) {
			return model instanceof MasterMitarbeiter;
		}
	}

}

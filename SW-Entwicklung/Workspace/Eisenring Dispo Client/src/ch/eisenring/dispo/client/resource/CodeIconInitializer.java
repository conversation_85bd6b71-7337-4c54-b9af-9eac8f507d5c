package ch.eisenring.dispo.client.resource;

import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.dispo.client.resource.imgnew.Images;
import ch.eisenring.dispo.shared.codetables.VerschiebenCode;

/**
 * Attaches icon resources to static codes.
 */
public final class CodeIconInitializer {

	private CodeIconInitializer() {
	}

	public static void initCodeIcons() {
		VerschiebenCode.PRIO1.setData(AbstractCode.KEY_ICON, Images.TRIANGLE_RED);
		VerschiebenCode.PRIO2.setData(AbstractCode.KEY_ICON, Images.TRIANGLE_YELLOW);
		VerschiebenCode.PRIO3.setData(AbstractCode.KEY_ICON, Images.TRIANGLE_GREEN);
		VerschiebenCode.NULL.setData(AbstractCode.KEY_ICON, Images.TRANSPARENT);
	}

}

package ch.eisenring.dispo.client.logic;

import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.client.gui.dnd.DnDEvent;
import ch.eisenring.dispo.client.gui.dnd.DnDHintAccept;
import ch.eisenring.dispo.client.gui.grid.BlockElement;
import ch.eisenring.dispo.client.gui.grid.Grid;
import ch.eisenring.dispo.client.gui.grid.LineElement;
import ch.eisenring.dispo.shared.codetables.ZuordnungCode;
import ch.eisenring.dispo.shared.model.AbstractModel;
import ch.eisenring.dispo.shared.model.Auftrag;
import ch.eisenring.dispo.shared.model.FeinZuordnung;
import ch.eisenring.dispo.shared.model.GrobZuordnung;
import ch.eisenring.dispo.shared.model.Mitarbeiter;
import ch.eisenring.logiware.code.soft.LWMonteurSirCode;

public abstract class DnDAcceptLine extends DnDHintAccept {
	
	public DnDAcceptLine(final Client client) {
		super(client);
	}

	@Override
	public void performDrop(DnDEvent event) {
		final LineElement targetLine = (LineElement) event.getTargetDropDelegate();
		final BlockElement sourceElement = (BlockElement) event.getDroppedElement();
		final Grid sourceGrid = sourceElement.getGrid();
		final Grid targetGrid = targetLine.getGrid();

		final BlockElement droppedElement = (BlockElement) sourceElement.getDropHint().getDropDelegate(client, event);
		if (null==droppedElement)
			return;

		final AbstractModel dropModel = droppedElement.getModel();
		final AbstractModel targetModel = targetLine.getModel();
		if (dropModel instanceof FeinZuordnung) {
			final FeinZuordnung z = (FeinZuordnung) dropModel;
			final Mitarbeiter mitarbeiter = (Mitarbeiter) targetModel;
			z.setMitarbeiter(mitarbeiter);
			do {
				if (!ZuordnungCode.ZUORDNUNG_MONTAGE.equals(z.getType()))
					break;
				if (mitarbeiter == null || !mitarbeiter.isMonteur())
					break;
				final LWMonteurSirCode code = mitarbeiter.getMonteurSirCode();
				if (AbstractCode.isNull(code))
					break;
				final Auftrag auftrag = z.getAuftrag();
				if (auftrag == null)
					break;
				auftrag.setChefMonteur(mitarbeiter);
			} while (false);
		} else if (dropModel instanceof GrobZuordnung) {
			final GrobZuordnung z = (GrobZuordnung) dropModel;
			z.setParent(targetModel);
		}
		
		targetLine.addBlock(droppedElement);
		droppedElement.setCellIndex(event.getDropCellStart());
		droppedElement.setCellSpan(event.getDropCellSpan());
		droppedElement.updateModel();
		droppedElement.calculateBounds();

		// repaint source and target grids
		sourceGrid.forceRepaint();
		if (sourceGrid!=targetGrid)
			targetGrid.forceRepaint();
		
	}

}

package ch.eisenring.dispo.client.gui.look.basic;

import java.awt.Font;
import java.awt.Graphics2D;
import java.awt.image.BufferedImage;

import ch.eisenring.core.observable.Observable;
import ch.eisenring.core.observable.Observer;
import ch.eisenring.core.observable.ObserverNotificationPolicy;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.client.gui.grid.BlockElement;
import ch.eisenring.dispo.client.gui.grid.GridBase;
import ch.eisenring.dispo.client.gui.grid.DSPGridLayout;
import ch.eisenring.dispo.client.gui.grid.LineElement;
import ch.eisenring.dispo.client.gui.look.ILook;

public final class BasicLook implements ILook {

	private final BasicTextRenderer textRenderer = new BasicTextRenderer(this);
	private final BasicGridHeaderRenderer headerRenderer = new BasicGridHeaderRenderer(this);
	private final BasicGridBodyRenderer bodyRenderer = new BasicGridBodyRenderer(this);
	private final BasicLineElementRenderer lineRenderer;
	private final BasicBlockElementRenderer blockRenderer;
	private final BasicOverlapRenderer overlapRenderer = new BasicOverlapRenderer(this);
	
	private final Observer<Integer> observer = new Observer<Integer>() {
		@Override
		public void observableChanged(final Observable<Integer> observeable) {
			int size = observeable.get(Integer.valueOf(12));
			Font font = new BufferedImage(1, 1, BufferedImage.TYPE_INT_RGB).createGraphics().getFont();
			if (size > 0)
				font = font.deriveFont((float) size);
			defaultFont = font;
		}
	};

	private Font defaultFont;

	public BasicLook(final Client client, final float fontSize) {
		this.lineRenderer = new BasicLineElementRenderer(client, this);
		this.blockRenderer = new BasicBlockElementRenderer(client, this);
		this.defaultFont = new BufferedImage(1, 1, BufferedImage.TYPE_INT_RGB)
				.createGraphics().getFont().deriveFont(fontSize);
		if (client != null) {
			client.FONT_SIZE.addObserver(observer, ObserverNotificationPolicy.SYNCHRONOUS);
		}
	}

	@Override
	public void text(Graphics2D graphics, String text, int x, int y, int w, int h, int justification) {
		textRenderer.text(graphics, text, x, y, w, h, justification);
	}

	@Override
	public void paintGridHeader(Graphics2D graphics, DSPGridLayout layout, GridBase grid) {
		headerRenderer.paintHeader(graphics, layout, grid);
	}

	@Override
	public void paintGridBody(Graphics2D graphics, DSPGridLayout layout) {
		bodyRenderer.paintGrid(graphics, layout);
	}
	
	@Override
	public void paintBlockElement(Graphics2D graphics, BlockElement element) {
		blockRenderer.paintElement(graphics, element);
	}
	
	@Override
	public void paintLineElement(Graphics2D graphics, LineElement element) {
		lineRenderer.paintElement(graphics, element);
	}

	@Override
	public void paintBlockOverlap(Graphics2D graphics, int x, int y, int w, int h) {
		overlapRenderer.paintOverlap(graphics, x, y, w, h);
	}

	public Font getDefaultFont() {
		return defaultFont;
	}

}

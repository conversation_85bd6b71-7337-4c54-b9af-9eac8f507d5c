package ch.eisenring.dispo.client.network;

import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.shared.network.packets.PacketSearchExtReply;
import ch.eisenring.network.PacketSink;
import ch.eisenring.network.implementation.PacketDispatchMode;
import ch.eisenring.network.packet.AbstractPacket;

final class SearchExtReplyHandler extends AbstractDSPPacketHandler {

	SearchExtReplyHandler(final Client client) {
		super(client, PacketSearchExtReply.class, PacketDispatchMode.ASYNCHRONOUS);
	}

	@Override
	public void handle(final AbstractPacket abstractPacket, final PacketSink sink) {
		// this packet should be handled by RPC
	}

}

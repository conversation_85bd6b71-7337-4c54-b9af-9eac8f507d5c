package ch.eisenring.dispo.client.gui.report.endmontagen;

import java.awt.BorderLayout;
import java.awt.Dimension;

import ch.eisenring.core.tag.TagSet;
import ch.eisenring.core.usersettings.UserSettings;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.client.resource.imgnew.Images;
import ch.eisenring.dispo.shared.network.packets.PacketReportEndmontagenReply;
import ch.eisenring.gui.window.AbstractBaseFrame;
import ch.eisenring.gui.window.FeedbackHandler;
import ch.eisenring.gui.window.WindowTags;

@SuppressWarnings("serial")
public class ReportEndmontagenResultDialog extends AbstractBaseFrame {

	protected Client client;
	protected ReportEndmontagenResultPanel panel;
	
	public ReportEndmontagenResultDialog(final Client client,
			final PacketReportEndmontagenReply packet) {
		super(new TagSet(
				WindowTags.ICON, Images.LINEGRAPH,
				WindowTags.TITLE, "Endmontagen Auswertung",
				WindowTags.MINIMUM_SIZE, new Dimension(800, 600)
				));
		this.client = client;
		this.panel = new ReportEndmontagenResultPanel(client, packet);
		initComponents();
		initLayout();
		pack();
	}

	private void initComponents() {
		setLayout(new BorderLayout());
	}

	private void initLayout() {
		add(panel, BorderLayout.CENTER);
	}

	@Override
	public UserSettings getUserSettings() {
		return client.getUserSettings();
	}

	@Override
	public FeedbackHandler getFeedbackHandler() {
		return client.getFeedbackHandler();
	}

	@Override
	protected void onCancel() {
		super.onCancel();
		setVisible(false);
	}

	@Override
	protected void onClose() {
		super.onClose();
		setVisible(false);
	}

}

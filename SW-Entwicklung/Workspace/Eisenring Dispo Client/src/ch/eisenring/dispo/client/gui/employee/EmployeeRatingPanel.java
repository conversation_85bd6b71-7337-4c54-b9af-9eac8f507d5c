package ch.eisenring.dispo.client.gui.employee;

import java.awt.GridBagLayout;
import java.awt.event.MouseEvent;

import javax.swing.JScrollPane;
import javax.swing.ScrollPaneConstants;

import ch.eisenring.app.client.gui.HEAGPanelLazy;
import ch.eisenring.app.shared.network.RPCContext;
import ch.eisenring.app.shared.network.RPCHandler;
import ch.eisenring.app.shared.network.RPCHandlerEDT;
import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.collections.List;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.shared.codetables.DSPRightCode;
import ch.eisenring.dispo.shared.model.MasterMitarbeiter;
import ch.eisenring.dispo.shared.network.packets.PacketMontageBewertungGetReply;
import ch.eisenring.dispo.shared.network.packets.PacketMontageBewertungGetRequest;
import ch.eisenring.gui.balloontip.BalloonTipManager;
import ch.eisenring.gui.components.HEAGTable;
import ch.eisenring.gui.util.LayoutUtil;
import ch.eisenring.logiware.code.soft.LWMonteurSirCode;
import heag.dsp.pub.model.DSPMontageBewertung;

@SuppressWarnings("serial")
public class EmployeeRatingPanel extends HEAGPanelLazy<Client> {

	protected LWMonteurSirCode monteur;
	protected final JScrollPane scrollPane = new JScrollPane();
	protected final MontageBewertungTableModel tableModel = new MontageBewertungTableModel();
	protected final HEAGTable table = new HEAGTable(tableModel) {
		@Override
		protected void rowDoubleClicked(final int rowIndex, final Object rowObject, final MouseEvent event) {
			super.rowDoubleClicked(rowIndex, rowObject, event);
			final MontageBewertungTableModel tableModel = (MontageBewertungTableModel) table.getModel();
			final DSPMontageBewertung bewertung = tableModel.getRow(rowIndex);
			if (bewertung != null) {
				BewertungInputDialog.showExistingBewertung(client, bewertung);
			}
		}
	};

	private final BewertungGraphPanel panelGraph = new BewertungGraphPanel();

	public EmployeeRatingPanel(final Client client) {
		super(client);
		initComponents();
		initLayout();
	}

	private void initComponents() {
		scrollPane.setHorizontalScrollBarPolicy(ScrollPaneConstants.HORIZONTAL_SCROLLBAR_AS_NEEDED);
		scrollPane.setVerticalScrollBarPolicy(ScrollPaneConstants.VERTICAL_SCROLLBAR_ALWAYS);
		scrollPane.setViewportView(table);
	}

	private void initLayout() {
		removeAll();
		setLayout(new GridBagLayout());
		final LayoutUtil l = new LayoutUtil(5);
		add(panelGraph, l.area(16));
		
		add(scrollPane, l.area(8));
	}

	@Override
	protected void showingFirstTimeImpl() {
		final PacketMontageBewertungGetRequest request = PacketMontageBewertungGetRequest.create(monteur);
		client.sendPacket(rpcHandler, request);
	}

	private RPCHandler rpcHandler = new RPCHandlerEDT() {
		@Override
		public void timeoutOccured(final RPCContext rpcContext) {
			BalloonTipManager.show(EmployeeRatingPanel.this, new ErrorMessage("Netzwerk-Zeitüberschreitung"));
		}
		
		@Override
		public void replyReceived(final RPCContext rpcContext) {
			final PacketMontageBewertungGetReply reply = (PacketMontageBewertungGetReply) rpcContext.getReply();
			if (!reply.isValid()) {
				BalloonTipManager.show(EmployeeRatingPanel.this, reply.getMessage());
			} else {
				final List<DSPMontageBewertung> bewertungen = reply.getBewertungen(); 
				tableModel.setContent(bewertungen);
				tableModel.applyLayout(table);
				panelGraph.setData(monteur, bewertungen);
			}
		}
	};

	@Override
	public void setEditable(final boolean editable) {
		super.setEditable(editable);
		final boolean edit2 = editable && DSPRightCode.MITARBEITER_BEARBEITEN.isPermitted();
		table.setEnabled(edit2);
	}

	@Override
	public void updateView(final Object model) {
		super.updateView(model);
		if (model instanceof MasterMitarbeiter) {
			final MasterMitarbeiter employee = (MasterMitarbeiter) model;
			monteur = employee.getMonteurSirCode();
		}
	}
	
	@Override
	public void updateModel(final Object model) {
		super.updateModel(model);
	}

}

package ch.eisenring.dispo.client.help;

import static ch.eisenring.dispo.client.DSPClientConstants.HELP_PATH;

import java.awt.Component;
import java.awt.Container;
import java.awt.event.KeyAdapter;
import java.awt.event.KeyEvent;

import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.core.platform.Platform;
import ch.eisenring.core.threading.ThreadCore;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.gui.util.CursorUtil;

public class HelpUtil extends KeyAdapter {

	protected final Client client;
	private final String helpId;
	private final Component component;

	public HelpUtil(final Client client, final String helpId, final Component component) {
		this.client = client;
		this.helpId = helpId;
		this.component = component;
	}
	
	public void attachListener(Component component) {
		if (null!=component) {
			component.addKeyListener(this);
			if (component instanceof Container) {
				Component[] childs = ((Container) component).getComponents();
				for (int i=childs.length-1; i>=0; --i) {
					attachListener(childs[i]);
				}
			}
		}
	}
	
	public void removeListener(Component component) {
		if (null!=component) {
			component.removeKeyListener(this);
			if (component instanceof Container) {
				Component[] childs = ((Container) component).getComponents();
				for (int i=childs.length-1; i>=0; --i) {
					removeListener(childs[i]);
				}
			}
		}
	}

	@Override
	public void keyReleased(KeyEvent e) {
		isHelpEvent(e);
	}

	public boolean isHelpEvent(KeyEvent keyEvent) {
		if (!isHelpTrigger(keyEvent))
			return false;
		keyEvent.consume();
		showHelp(helpId, component);
		return true;
	}

	public static boolean isHelpTrigger(final KeyEvent keyEvent) {
		if (KeyEvent.VK_F1!=keyEvent.getKeyCode())
			return false;
		if (0!=keyEvent.getModifiersEx())
			return false;
		if (keyEvent.isConsumed())
			return false;
		return true;
	}

	public void showHelp(final String helpId, final Component component) {
		CursorUtil.setCursorAll(component, true);
		long time = System.currentTimeMillis();
		String path = HELP_PATH.get();
		if (Strings.isEmpty(path))
			return;
		path = Strings.concat(path, helpId, ".html");
		Logger.debug(Strings.concat("starting help viewer for: ", path));
		final Platform platform = Platform.getPlatform();
		platform.openFile(path);

		// make sure busy-cursor was shown at least 800 ms
		time = System.currentTimeMillis()-time;
		if (time<800) {
			time = 800-time;
			ThreadCore.sleep((int) time);
		}
		CursorUtil.setCursorAll(component, false);
	}

}

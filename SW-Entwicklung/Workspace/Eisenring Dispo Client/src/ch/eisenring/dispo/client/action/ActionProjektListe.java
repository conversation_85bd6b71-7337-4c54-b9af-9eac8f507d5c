package ch.eisenring.dispo.client.action;

import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.client.gui.report.projektliste.ProjektListeWindow;
import ch.eisenring.dispo.client.resource.imgnew.Images;
import ch.eisenring.gui.window.AbstractBaseDialog;

public class ActionProjektListe extends AbstractDSPAction {

	public ActionProjektListe(final Client client) {
		super(client, Images.COG, "Projektliste");
	}

	@Override
	protected void performAction() {
		final ProjektListeWindow window = new ProjektListeWindow(client);
		AbstractBaseDialog.showWindow(window);
	}

}

package ch.eisenring.dispo.client.favorites;

import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.shared.model.AbstractMitarbeiter;

public final class EmployeeFavoritesAll extends EmployeeFavorites {

	EmployeeFavoritesAll() {
		super(0, null, "<PERSON><PERSON>", "Alle Mitarbeiter");
	}

	@Override
	public boolean isEditable() {
		return false;
	}

	@Override
	public boolean isFavorite(final Client client, final AbstractMitarbeiter mitarbeiter) {
		return true;
	}
	
	@Override
	public void addMitarbeiter(final AbstractMitarbeiter mitarbeiter) {
		// no-op
	}
	
	@Override
	public void removeMitarbeiter(final AbstractMitarbeiter mitarbeiter) {
		// no-op
	}

}

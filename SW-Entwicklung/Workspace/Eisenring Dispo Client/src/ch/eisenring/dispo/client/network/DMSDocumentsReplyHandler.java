package ch.eisenring.dispo.client.network;

import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.shared.network.packets.PacketDMSDocumentsReply;
import ch.eisenring.network.PacketSink;
import ch.eisenring.network.packet.AbstractPacket;

class DMSDocuments<PERSON><PERSON>ly<PERSON><PERSON><PERSON> extends AbstractDSPPacketHandler {

	DMSDocumentsReplyHandler(final Client client) {
		super(client, PacketDMSDocumentsReply.class);
	}

	@Override
	public void handle(final AbstractPacket packet, final PacketSink sink) {
		// ignore (packet must be handled by RPC)
	}

}

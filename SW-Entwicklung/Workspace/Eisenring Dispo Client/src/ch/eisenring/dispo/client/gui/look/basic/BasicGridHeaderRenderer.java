package ch.eisenring.dispo.client.gui.look.basic;

import java.awt.Font;
import java.awt.Graphics2D;

import ch.eisenring.dispo.client.gui.grid.GridBase;
import ch.eisenring.dispo.client.gui.grid.DSPGridLayout;
import ch.eisenring.dispo.client.gui.look.AbstractRenderer;
import ch.eisenring.dispo.client.gui.look.GridColorSet;
import ch.eisenring.dispo.client.gui.look.ILook;

public final class BasicGridHeaderRenderer extends AbstractRenderer {

	protected BasicGridHeaderRenderer(ILook look) {
		super(look);
	}

	/**
	 * Renders the complete header
	 */
	public void paintHeader(Graphics2D graphics, DSPGridLayout layout, GridBase grid) {
		paintBackground(graphics, layout);
		paintColumnBorders(graphics, layout);
		paintTitles(graphics, layout);
	}

	/**
	 * Renders the header background
	 */
	public void paintBackground(Graphics2D graphics, DSPGridLayout layout) {
		final GridColorSet colorSet = layout.getColorSet();
		graphics.setPaintMode();
		graphics.setColor(colorSet.getColorHeaderBackground());
		graphics.fillRect(0, 0, layout.getWidth(), layout.getHeaderHeight());
	}

	/**
	 * Renders the column borders
	 */
	public void paintColumnBorders(Graphics2D graphics, DSPGridLayout layout) {
		final GridColorSet colorSet = layout.getColorSet();
		final int columnCount = layout.getColumnCount();
		graphics.setPaintMode();
		graphics.setColor(colorSet.getColorGridMajor());
		for (int columnIndex=0; columnIndex<columnCount; ++columnIndex) {
			final int x = layout.getColumnMinX(columnIndex);
			final int y = 0;
			final int w = layout.getColumnWidth(columnIndex) - 1;
			final int h = layout.getHeaderHeight() - 1;
			graphics.drawRect(x, y, w, h);
		}
	}

	/**
	 * Renders the header titles
	 */
	public void paintTitles(Graphics2D graphics, DSPGridLayout layout) {
		final GridColorSet colorSet = layout.getColorSet();
		final int labelColumn = layout.getLabelColumn();
		final int columnCount = layout.getColumnCount();
		final Font font = getLook().getDefaultFont();
		final Font bold = font.deriveFont(Font.BOLD);
		graphics.setFont(bold);
		graphics.setPaintMode();
		graphics.setColor(colorSet.getColorHeaderText());
		for (int columnIndex=0; columnIndex<columnCount; ++columnIndex) {
			final int justification = labelColumn==columnIndex ? ILook.JUSTIFY_LEFT : ILook.JUSTIFY_CENTER;
			final int x = layout.getColumnMinX(columnIndex) + 1;
			final int y = 1;
			final int w = layout.getColumnWidth(columnIndex) - 2;
			final int h = layout.getHeaderHeight() - 2;
			final String title = layout.getColumnTitle(columnIndex);
			getLook().text(graphics, title, x, y, w, h, justification);
		}
		graphics.setFont(font);
	}
	
}

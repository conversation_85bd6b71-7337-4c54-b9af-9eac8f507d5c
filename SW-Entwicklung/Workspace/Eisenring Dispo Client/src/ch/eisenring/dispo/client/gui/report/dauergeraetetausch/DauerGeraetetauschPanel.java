package ch.eisenring.dispo.client.gui.report.dauergeraetetausch;

import java.awt.GridBagLayout;
import java.util.Calendar;

import ch.eisenring.core.datatypes.date.HEAGCalendar;
import ch.eisenring.core.datatypes.date.Period;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.shared.network.packets.PacketDauerGeraeteTauschRequest;
import ch.eisenring.gui.GridBagConstraints;
import ch.eisenring.gui.components.HEAGPanel;
import ch.eisenring.gui.components.HEAGPeriodField;
import ch.eisenring.gui.model.ValidationResults;

@SuppressWarnings("serial")
public class DauerGeraetetauschPanel extends HEAGPanel {

	protected final Client client;
	protected final HEAGPeriodField periodField = new HEAGPeriodField();

	public DauerGeraetetauschPanel(final Client client) {
		this.client = client;
		initComponents();
		initLayout();
	}

	private void initComponents() {
		setLayout(new GridBagLayout());
		final HEAGCalendar c = HEAGCalendar.obtainNow();
		c.set(Calendar.MILLISECOND, 0);
		c.set(Calendar.SECOND, 0);
		c.set(Calendar.MINUTE, 0);
		c.set(Calendar.HOUR_OF_DAY, 0);
		c.set(Calendar.DAY_OF_MONTH, 1);
		final long begin = c.getTimeInMillis();
		c.add(Calendar.MONTH, 1);
		final long end = c.getTimeInMillis();
		periodField.setValue(Period.create(begin, end));
		c.release();
	}

	private void initLayout() {
		removeAll();
		add(periodField, GridBagConstraints.fixed(0, 0).gridWidthRemainder());
	}

	PacketDauerGeraeteTauschRequest getRequest() {

		final Period period = (Period) periodField.getValue();
		final PacketDauerGeraeteTauschRequest packet = PacketDauerGeraeteTauschRequest.create(period);
		return packet;
	}

	// --------------------------------------------------------------
	// ---
	// --- View implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public void validateView(final ValidationResults results, final Object model) {
		super.validateView(results, model);
//		final Date from = datFrom.getDate();
//		final Date upto = datUpto.getDate(); 
//		if (from == null)
//			results.add("Von-Datum fehlt", datFrom);
//		if (upto == null)
//			results.add("Bis-Datum fehlt", datUpto);
//		if (from != null && upto != null && from.after(upto))
//			results.add("Das Von-Datum darf nicht hinter dem Bis-Datum liegen", datFrom);
	}

}

package ch.eisenring.dispo.client.gui.monteurteam;

import java.awt.Component;
import java.awt.Dimension;

import ch.eisenring.core.tag.TagSet;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.client.gui.components.dialog.AbstractDSPDialog;
import ch.eisenring.dispo.client.resource.imgnew.Images;
import ch.eisenring.gui.BorderLayout;
import ch.eisenring.gui.window.AbstractBaseDialog;
import ch.eisenring.gui.window.DialogTags;
import ch.eisenring.gui.window.WindowTags;

@SuppressWarnings("serial")
public class MonteurTeamMaintainDialog extends AbstractDSPDialog {

	private final MonteurTeamMaintainPanel panel;

	public MonteurTeamMaintainDialog(final Client client, final Component parent) {
		super(client, new TagSet(
				DialogTags.MODALITY, DialogTags.MODALITY_MODAL,
				DialogTags.DIALOG_OWNER, parent,
				WindowTags.MINIMUM_SIZE, new Dimension(300, 400),
				WindowTags.RESIZEABLE, WindowTags.RESIZEABLE_YES,
				WindowTags.TITLE, "Monteur-Teams verwalten",
				WindowTags.ICON, Images.MONTEUR_TEAM,
				WindowTags.LAYOUTMANAGER, new BorderLayout(0, 0),
				WindowTags.POSITION_SETTINGS_ID, "MonteurTeamListWindow"
			));
		panel = new MonteurTeamMaintainPanel(this);
		initComponents();
		initLayout();
		pack();
	}

	private void initComponents() {
		
	}

	private void initLayout() {
		add(panel, BorderLayout.CENTER);
	}

	public static void showDialog(final Client client, final Component parent) {
		final AbstractBaseDialog dialog = new MonteurTeamMaintainDialog(client, parent);
		AbstractBaseDialog.showWindow(dialog);
	}

	void updateTeamList() {
		panel.updateTeamList();
	}

}

package ch.eisenring.dispo.client.gui.auftrag.solidarhaftung;

import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.client.action.AbstractDSPAction;
import ch.eisenring.email.client.resource.images.Images;
import ch.eisenring.gui.window.AbstractBaseWindow;

class ActionSolidarhaftungSelect extends AbstractDSPAction {

	private final ObjektbetreuerBasisPanel basisPanel;

	public ActionSolidarhaftungSelect(final Client client, final ObjektbetreuerBasisPanel basisPanel) {
		super(client, "", "Öffnet den Dialog zum Zusammenstellen und Versenden der Deklaration Solidarhaftung",
				Images.EMAIL_WITH_ATTACHMENT);
		this.basisPanel = basisPanel;
	}

	@Override
	protected boolean isEnabledImpl() {
		return getMailTo() != null && super.isEnabledImpl();
	}

	@Override
	protected void performAction() {
		final String emailAddress = getMailTo();
		if (Strings.isEmpty(emailAddress))
			return;
		
		final SolidarhaftungData data = new SolidarhaftungData();
		data.client = client;
		data.basisPanel = basisPanel;
		data.auftrag = basisPanel.basisProjektHolder.getAuftrag();

		SolidarHaftungSelectDialog dialog = new SolidarHaftungSelectDialog(data);
		AbstractBaseWindow.showWindow(dialog);
	}

	private String getMailTo() {
		if (basisPanel == null)
			return null;
		String result = basisPanel.txtEmailSolidarhaftung.getText();
		if (Strings.isEmpty(result) && basisPanel.basisProjektHolder.getAuftrag() != null)
			result = basisPanel.basisProjektHolder.getAuftrag().getEMailBaufuehrer();
		return Strings.isEmpty(result) ? null : result;
	}

}

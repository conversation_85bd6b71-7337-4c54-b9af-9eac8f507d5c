package ch.eisenring.dispo.client.gui.look;

import java.awt.Color;
import java.awt.Component;

import ch.eisenring.gui.util.ColorRGB;

public final class GridColorSet {

	public final static Color COLOR_DEFAULT_BG = new Color(0xFFAAAAAA);

	private final Color foreground;
	private final Color background;
	private final Color disabledOverlay;
	private final Color gridMajor;
	private final Color gridMinor;
	private final Color headerBackground;
	private final Color textDisabled;
	
	public GridColorSet(final Component component) {
		Color bg, fg;
		fg = component.getForeground();
		bg = component.getBackground();
		this.foreground = fg == null ? Color.BLACK : fg; 
		this.background = bg == null ? COLOR_DEFAULT_BG : bg;
		
		this.disabledOverlay = ColorRGB.alpha(background, 0xBB000000);
		
		this.gridMajor = ColorRGB.brightness(background, 0.75F);

		this.gridMinor = ColorRGB.brightness(background, 0.93F);
		
		this.headerBackground = ColorRGB.brightness(background, 0.90F);
		
		this.textDisabled = ColorRGB.alpha(foreground, 0x8800000);
	}

	/**
	 * Gets the text color
	 */
	public Color getColorText() {
		return foreground;
	}
	
	/**
	 * Gets the text color
	 */
	public Color getColorText(final boolean enabled) {
		return enabled ? foreground : textDisabled;
	}

	/**
	 * Gets the background color
	 */
	public Color getColorBackground() {
		return background;
	}
	
	/**
	 * Gets the header text color
	 */
	public Color getColorHeaderText() {
		return foreground;
	}

	/**
	 * Gets the header background color
	 */
	public Color getColorHeaderBackground() {
		return headerBackground;
	}

	/**
	 * Gets the color for primary grid raster (major columns)
	 */
	public Color getColorGridMajor() {
		return gridMajor;
	}
	
	/**
	 * Gets the color for secondary grid raster (minor columns)
	 */
	public Color getColorGridMinor() {
		return gridMinor;
	}

	/**
	 * Gets the overlay color for the disabled look.
	 * This is a color with alpha that is intended to
	 * be rendered on top of the entire component.
	 */
	public Color getColorDisabledOverlay() {
		return disabledOverlay;
	}
	
}

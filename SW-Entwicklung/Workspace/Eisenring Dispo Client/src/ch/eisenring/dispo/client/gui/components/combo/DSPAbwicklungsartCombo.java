package ch.eisenring.dispo.client.gui.components.combo;

import static ch.eisenring.dispo.shared.codetables.AnsichtCode.FAHRPLANUNG;

import java.awt.event.ItemEvent;
import java.awt.event.ItemListener;

import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.observable.Observable;
import ch.eisenring.core.observable.Observer;
import ch.eisenring.core.observable.ObserverNotificationPolicy;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.shared.codetables.DSPAbwicklungsArt;

@SuppressWarnings("serial")
public class DSPAbwicklungsartCombo extends AbstractTypeCombo<DSPAbwicklungsArt> {
	
	private final Observer<Object> observer = new Observer<Object>() {
		@Override
		public void observableChanged(final Observable<Object> observeable) {
			final AbstractCode abwArt = client.STATE_ABWICKLUNGSART.get();
			setSelectedItem(abwArt);
			final Object ansicht = client.ANSICHT.get();
			final boolean visible = !FAHRPLANUNG.equals(ansicht);
			setVisible(visible);
		}
	};

	private final ItemListener itemListener = new ItemListener() {
		@Override
		public void itemStateChanged(final ItemEvent event) {
			final DSPAbwicklungsArt item = getSelectedCode();
			if (item != null)
				client.STATE_ABWICKLUNGSART.set(item);
		}
	};
	
	protected final Client client;

	public DSPAbwicklungsartCombo(final Client client, final boolean showAll) {
		super(DSPAbwicklungsArt.class);
		this.client = client;
		setFocusable(false);
		setToolTipText("In der Grobplanung anzuzeigende Abwicklungsart");
		addItemListener(itemListener);
		client.ANSICHT.addObserver(observer, ObserverNotificationPolicy.EDT_ASYNCHRONOUS);
		client.STATE_ABWICKLUNGSART.addObserver(observer, ObserverNotificationPolicy.EDT_ASYNCHRONOUS);
	}

}

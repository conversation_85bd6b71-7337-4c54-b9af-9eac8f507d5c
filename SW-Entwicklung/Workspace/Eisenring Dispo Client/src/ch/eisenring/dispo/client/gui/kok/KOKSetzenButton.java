package ch.eisenring.dispo.client.gui.kok;

import static ch.eisenring.dispo.client.gui.kok.KOKSetzenAction.ACTION_FOR_REAL;
import static ch.eisenring.dispo.client.gui.kok.KOKSetzenAction.ACTION_IGNORE;

import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.awt.event.MouseListener;
import java.util.Collection;

import javax.swing.JPopupMenu;

import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.observable.Observable;
import ch.eisenring.core.observable.Observer;
import ch.eisenring.core.observable.ObserverNotificationPolicy;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.client.gui.components.button.ImageButton;
import ch.eisenring.dispo.client.resource.imgnew.Images;
import ch.eisenring.dispo.shared.codetables.DSPAbwicklungsArt;
import ch.eisenring.gui.action.AbstractAction;
import ch.eisenring.gui.menu.MenuBuilder;

@SuppressWarnings("serial")
public class KOKSetzenButton extends ImageButton {

	private final Observer<Object> observer = new Observer<Object>() {
		@Override
		public void observableChanged(final Observable<Object> observeable) {
			final AbstractAction action = getApplicationAction();
			if (action != null) {
				final boolean enabled = getApplicationAction().isEnabled();
				setVisible(enabled);
				setEnabled(enabled);
			}
		}
	};

	private final MouseListener mouseListener = new MouseAdapter() {
		@Override
		public void mouseClicked(final MouseEvent event) {
			if (!event.isConsumed()) {
				event.consume();
				final JPopupMenu popup = new JPopupMenu();
				final Collection<DSPAbwicklungsArt> types = AbstractCode.getInstances(DSPAbwicklungsArt.class);
				for (final DSPAbwicklungsArt type : types) {
					if (!DSPAbwicklungsArt.SHOWALL.equals(type))
						popup.add(MenuBuilder.createItem(new KOKSetzenAction(client, type, ACTION_FOR_REAL)));
				}
				popup.addSeparator();
				popup.add(MenuBuilder.createItem(new KOKSetzenAction(client, DSPAbwicklungsArt.SHOWALL, ACTION_FOR_REAL)));
				popup.show(KOKSetzenButton.this, 0, 0);
			}
		}
	};

	protected final Client client;

	public KOKSetzenButton(final Client client) {
		super(client, Images.KOK.getIcon(32), new KOKSetzenAction(client, DSPAbwicklungsArt.SHOWALL, ACTION_IGNORE));
		this.client = client;
		client.WAITING_FOR_MODEL.addObserver(observer, ObserverNotificationPolicy.EDT_ASYNCHRONOUS);
		client.CURRENT_MODEL.addObserver(observer, ObserverNotificationPolicy.EDT_ASYNCHRONOUS);
		client.ANSICHT.addObserver(observer, ObserverNotificationPolicy.EDT_ASYNCHRONOUS);
		setFocusable(false);
		setToolTipText("Feinplanung abschliessen");
		addMouseListener(mouseListener);
	}

}

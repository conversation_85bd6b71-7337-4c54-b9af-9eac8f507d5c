package ch.eisenring.dispo.client.gui.report.endmontagen;

import java.awt.GridBagLayout;
import java.util.Calendar;

import ch.eisenring.core.datatypes.date.HEAGCalendar;
import ch.eisenring.core.datatypes.date.Period;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.client.resource.imgnew.Images;
import ch.eisenring.dispo.shared.network.packets.PacketReportEndmontagen2Request;
import ch.eisenring.gui.GridBagConstraints;
import ch.eisenring.gui.components.DecorationHeader;
import ch.eisenring.gui.components.HEAGLabel;
import ch.eisenring.gui.components.HEAGPanel;
import ch.eisenring.gui.components.HEAGPeriodField;

@SuppressWarnings("serial")
public class ReportEndmontagen2Panel extends HEAGPanel {

	protected final HEAGPeriodField periodControl = new HEAGPeriodField();

	public ReportEndmontagen2Panel(final Client client) {
		initComponents();
		initLayout();
	}
	
	private void initComponents() {
		setLayout(new GridBagLayout());
		final HEAGCalendar c = HEAGCalendar.obtainNow();
		long begin, end;
		c.set(Calendar.MONTH, 0);
		c.set(Calendar.DAY_OF_MONTH, 1);
		c.set(Calendar.HOUR_OF_DAY, 0);
		c.set(Calendar.MINUTE, 0);
		c.set(Calendar.SECOND, 0);
		c.set(Calendar.MILLISECOND, 0);
		begin = c.getTimeInMillis();
		c.set(Calendar.MONTH, 11);
		c.set(Calendar.DAY_OF_MONTH, 31);
		c.add(Calendar.DAY_OF_YEAR, 1);
		end = c.getTimeInMillis();
		c.release();
		periodControl.setValue(Period.create(begin, end));
	}

	private void initLayout() {
		removeAll();
		
		int y = 0;
		DecorationHeader header = new DecorationHeader(
				DecorationHeader.ICON, Images.LINEGRAPH,
				DecorationHeader.SIZE, DecorationHeader.MEDIUM,
				DecorationHeader.LABEL, "Endmontagen pro Küche",
				DecorationHeader.SEPARATOR, Boolean.TRUE);
		add(header, GridBagConstraints.decorator(0, y));

		++y;
		add(new HEAGLabel("Auswertungszeitraum"), GridBagConstraints.label(0, y));
		add(periodControl, GridBagConstraints.field(1, y).gridWidthRemainder());
	}

	public PacketReportEndmontagen2Request getRequest() {
		final Period period = (Period) periodControl.getValue();
		return PacketReportEndmontagen2Request.create(period);
	}

}

package ch.eisenring.dispo.client.resource;

import ch.eisenring.core.resource.ResourceLoader;

public final class Template<PERSON>oader extends ResourceLoader {

	public final static String BASEPATH = "dsp/export-templates/";
	public final static String AUFTRAG_INFOSEITE = BASEPATH + "Auftrag-Infoseite.html";

	public final static String AUFTRAG_DOKUMENTE_BLOCK = BASEPATH + "Auftrag-Dokumente.html";
	public final static String AUFTRAG_DOKUMENTE_LINE = BASEPATH + "Auftrag-Dokumente-Line.html";
	public final static String AUFTRAG_KOMMUNIKATION_BLOCK = BASEPATH + "Auftrag-EMails.html";
	public final static String AUFTRAG_KOMMUNIKATION_LINE = BASEPATH + "Auftrag-Dokumente-Line.html";
	public final static String AUFTRAG_BILDER_BLOCK = BASEPATH + "Auftrag-Images.html";
	public final static String AUFTRAG_BILDER_LINE = BASEPATH + "Auftrag-Dokumente-Line.html";

}

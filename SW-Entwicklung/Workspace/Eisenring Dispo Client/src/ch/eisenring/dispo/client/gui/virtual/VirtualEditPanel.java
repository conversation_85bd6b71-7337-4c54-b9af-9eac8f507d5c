package ch.eisenring.dispo.client.gui.virtual;

import java.awt.GridBagLayout;

import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.client.ClientContextCache;
import ch.eisenring.dispo.client.gui.components.combo.CustomComboBoxes;
import ch.eisenring.dispo.shared.codetables.EmploymentRole;
import ch.eisenring.dispo.shared.codetables.SortHintCode;
import ch.eisenring.dispo.shared.model.AbstractModel;
import ch.eisenring.dispo.shared.model.ModelContext;
import ch.eisenring.dispo.shared.model.VirtualMitarbeiter;
import ch.eisenring.gui.ManualBinding;
import ch.eisenring.gui.components.HEAGCheckBox;
import ch.eisenring.gui.components.HEAGCodeComboBox;
import ch.eisenring.gui.components.HEAGIntegerField;
import ch.eisenring.gui.components.HEAGLabel;
import ch.eisenring.gui.components.HEAGPanel;
import ch.eisenring.gui.components.HEAGTextField;
import ch.eisenring.gui.model.ValidationResults;
import ch.eisenring.gui.util.ConversionUtil;
import ch.eisenring.gui.util.LayoutUtil;
import ch.eisenring.gui.window.AbstractBaseWindow;
import ch.eisenring.logiware.code.soft.GSECode;

@SuppressWarnings("serial")
public class VirtualEditPanel extends HEAGPanel {

	static abstract class Binding extends ManualBinding {
		Binding() {
			super();
		}
		Binding(final int length) {
			super(length);
		}
		@Override
		protected boolean isValidModel(final Object model) {
			return model instanceof VirtualMitarbeiter;
		}
	}
	
	protected final Client client;
	private final boolean isCreate;
	private final HEAGTextField txtVorname = new HEAGTextField(new Binding(30) {
		@Override
		protected Object getModelValueImpl(final Object model) {
			return ((VirtualMitarbeiter) model).getFirstName();
		}
		@Override
		protected void setModelValueImpl(final Object model, final Object value) {
			((VirtualMitarbeiter) model).setFirstName(ConversionUtil.convert(value, (String) null));
		}
	});
	private final HEAGTextField txtNachname = new HEAGTextField(new Binding(30) {
		@Override
		protected Object getModelValueImpl(final Object model) {
			return ((VirtualMitarbeiter) model).getLastName();
		}
		@Override
		protected void setModelValueImpl(final Object model, final Object value) {
			((VirtualMitarbeiter) model).setLastName(ConversionUtil.convert(value, (String) null));
		}
	});
	private final HEAGCodeComboBox<GSECode> cmbGSE = CustomComboBoxes.createGSECombo(new Binding() {
		@Override
		protected Object getModelValueImpl(final Object model) {
			return ((VirtualMitarbeiter) model).getGSE(null);
		}
		@Override
		protected void setModelValueImpl(final Object model, final Object value) {
			((VirtualMitarbeiter) model).setGSE(ConversionUtil.convert(value, GSECode.class));
		}
	}, GSECode.GSE_2501);
	private final HEAGCheckBox ckbHigh = new HEAGCheckBox("Hervorgehoben", new Binding() {
		@Override
		protected Object getModelValueImpl(final Object model) {
			return Boolean.valueOf(((VirtualMitarbeiter) model).getHighlight() != 0);
		}
		@Override
		protected void setModelValueImpl(final Object model, final Object value) {
			final boolean b = ConversionUtil.convert(value, false);
			((VirtualMitarbeiter) model).setHighlight(b ? 1 : 0);
		}
	});
	private final HEAGCodeComboBox<SortHintCode> cmbSort = CustomComboBoxes.createSortHintCombo();
	private final HEAGIntegerField intWeekCount = new HEAGIntegerField(2, false);
	private VirtualMitarbeiter mitarbeiter;
	
	public VirtualEditPanel(final Client client, final boolean isCreate) {
		this(client, (VirtualMitarbeiter) null, isCreate);
	}

	public VirtualEditPanel(final Client client, final VirtualMitarbeiter mitarbeiter, final boolean isCreate) {
		this.client = client;
		this.mitarbeiter = mitarbeiter;
		this.isCreate = isCreate;
		initComponents();
		initLayout();
		updateView(mitarbeiter);
	}

	@SuppressWarnings("unchecked")
	private void initComponents() {
		intWeekCount.setValue(1);
	}

	private void initLayout() {
		final LayoutUtil l = new LayoutUtil(4);
		removeAll();
		setLayout(new GridBagLayout());
		add(new HEAGLabel("Vorname"), l.label());
		add(txtVorname, l.field());

		add(new HEAGLabel("Nachname"), l.label());
		add(txtNachname, l.field());

		add(new HEAGLabel("Optionen"), l.label());
		add(ckbHigh, l.field(1));
		add(cmbGSE, l.field(1));
		add(cmbSort, l.fixed());
		
		if (isCreate) {
			add(new HEAGLabel("Wochen"), l.label());
			add(intWeekCount, l.label(false, 2));
			add(new HEAGLabel("(1 bis max. 60)", false), l.label());
		}
	}

	public AbstractModel getModel() {
		return mitarbeiter;
	}

	public void setModel(AbstractModel model) {
		if (model instanceof VirtualMitarbeiter)
			mitarbeiter = (VirtualMitarbeiter) model;
		updateView(mitarbeiter);
	}

	// --------------------------------------------------------------
	// ---
	// --- ModelViewContainer implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public void updateView(final Object model) {
		super.updateView(model);
		if (model instanceof VirtualMitarbeiter) {
			final VirtualMitarbeiter mitarbeiter = (VirtualMitarbeiter) model;
			cmbSort.setSelectedItem(mitarbeiter.getSortHint());
		}
	}

	@Override
	public void updateModel(final Object model) {
		super.updateModel(model);
		if (model instanceof VirtualMitarbeiter) {
			final VirtualMitarbeiter mitarbeiter = (VirtualMitarbeiter) model;
			mitarbeiter.setShortName(null);
			mitarbeiter.setSortHint(((SortHintCode) cmbSort.getSelectedCode()).getSortHint());
		}
	}
	
	@Override
	public boolean isEditable() {
		return txtVorname.isEditable();
	}
	
	@Override
	public void setEditable(final boolean editable) {
		txtVorname.setEditable(editable);
		txtNachname.setEditable(editable);
		ckbHigh.setEnabled(editable);
		cmbSort.setEnabled(editable);
	}
	
	@Override
	public void validateView(final ValidationResults results, final Object model) {
		super.validateView(results, model);
		String v = txtVorname.getText();
		String n = txtNachname.getText();
		if (null==v)
			results.add("Vorname darf nicht leer sein", txtVorname);
		if (null==n)
			results.add("Nachname darf nicht leer sein", txtNachname);
		int weekCount = ConversionUtil.convert(intWeekCount, 0);
		if (weekCount<1 || weekCount>60)
			results.add("Anzahl Wochen muss zwischen 1 und 60 liegen", intWeekCount);
		if (getCurrentContext() == null)
			results.add("Context == null", null);
	}

	/***************************************************************
	 * Content control
	 ***************************************************************/
	public void createMitarbeiter(final EmploymentRole type) {
		final ModelContext firstContext = getCurrentContext();
		if (firstContext == null)
			return;
		if (!AbstractBaseWindow.Util.isContentValid(this, null))
			return;
		final Runnable r = new Runnable() {
			@Override
			public void run() {
				ModelContext context = null;
				int weekCount = ConversionUtil.convert(intWeekCount, 0);
				Long pId = null;
				ProgressDialog progress = new ProgressDialog(client, "Erstelle...");
				progress.open(weekCount);
				do { 
					if (null==context) {
						context = firstContext;
					} else {
						context = getNextContext(context);
					}
					context.obtain();
					try {
						final VirtualMitarbeiter mitarbeiter = new VirtualMitarbeiter(context);
						mitarbeiter.setEmploymentRole(type);
						updateModel(mitarbeiter);
						// copy the fake PRESENTO id to all subsequent weeks
						if (null==pId) {
							pId = mitarbeiter.getPresentoId();
						} else {
							mitarbeiter.setPresentoId(pId);
						}
						client.sendModelChanges(context);
					} finally {
						context.release();
					}
					progress.incrementProgress();
				} while (--weekCount>0);
				progress.close();
			}
		};
		client.getThreadPool().start(r, "TemporaryEmployeeGUI");
	}

	public ModelContext getCurrentContext() {
		if (Boolean.TRUE.equals(client.WAITING_FOR_MODEL.get()))
			return null;
		return client.getCurrentModel();
	}		

	public ModelContext getNextContext(final ModelContext context) {
		final ClientContextCache contextCache = client.getContextCache();
		final long date = TimestampUtil.findMonday(context.getBegin(), 1);
		return contextCache.getOrLoadContext(date);
	}

}

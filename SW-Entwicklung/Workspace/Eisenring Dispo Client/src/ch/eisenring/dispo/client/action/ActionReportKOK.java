package ch.eisenring.dispo.client.action;

import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.client.gui.report.endmontagen.ReportKOKDialog;
import ch.eisenring.dispo.client.resource.imgnew.Images;
import ch.eisenring.dispo.shared.codetables.DSPRightCode;
import ch.eisenring.gui.window.AbstractBaseDialog;

@SuppressWarnings("serial")
public class ActionReportKOK extends AbstractDSPAction {

	public ActionReportKOK(final Client client) {
		super(client, "Verschiebungen KOK...", Images.LINEGRAPH);
		addPermissionObserver();
	}

	@Override
	protected boolean isEnabledImpl() {
		return DSPRightCode.GL_AUSWERTUNG.isPermitted();
	}

	@Override
	protected void performAction() {
		final AbstractBaseDialog dialog = new ReportKOKDialog(client);
		AbstractBaseDialog.showWindow(dialog);
	}

}

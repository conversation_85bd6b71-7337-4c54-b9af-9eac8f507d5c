package ch.eisenring.dispo.client.action;

import java.awt.Component;

import javax.swing.JOptionPane;

import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.client.gui.grid.BlockElement;
import ch.eisenring.dispo.client.gui.grid.Grid;
import ch.eisenring.dispo.client.gui.grid.GridElement;
import ch.eisenring.dispo.client.gui.grid.LineElement;
import ch.eisenring.dispo.shared.codetables.DSPRightCode;
import ch.eisenring.dispo.shared.model.AbstractMitarbeiter;
import ch.eisenring.dispo.shared.model.AbstractModel;
import ch.eisenring.dispo.shared.model.AbstractZuordnung;
import ch.eisenring.dispo.shared.model.Auftrag;
import ch.eisenring.dispo.shared.model.FeinZuordnung;
import ch.eisenring.dispo.shared.model.GrobZuordnung;
import ch.eisenring.dispo.shared.model.ModelContext;
import ch.eisenring.gui.resource.images.Images;
import ch.eisenring.logiware.code.soft.GSECode;

public final class ActionElementDelete extends ActionElementBase {

	public ActionElementDelete(final Client client, GridElement gridElement) {
		super(client, gridElement, Images.DELETE, "_Löschen");
	}

	@Override
	protected void performBlockAction(BlockElement blockElement) {
		performDelete(client, blockElement);
	}

	@Override
	protected boolean isEnabledImpl() {
		return super.isEnabledImpl() && canDelete(client, getBlockElement());
	}

	public static boolean canDelete(final Client client, GridElement element) {
		return element.isEditable() && canDelete(client, element.getModel());
	}

	public static boolean canDelete(final Client client, final AbstractModel model) {
		boolean result = true;
		if (model instanceof AbstractZuordnung) {
			result &= canDelete(client, (AbstractZuordnung) model);
		} else {
			result = false;
		}
		return result;
	}

	public static boolean canDelete(final Client client, final AbstractZuordnung zuordnung) {
		if (zuordnung instanceof GrobZuordnung) {
			// GrobZuordnung can only be deleted if there is at least
			// one other peer element in the line
			final Auftrag auftrag = zuordnung.getAuftrag();
			if (auftrag == null)
				return false;
			return auftrag.getGrobZuordnungenRelation().list().size() > 1;
		} else if (zuordnung instanceof FeinZuordnung) {
			final FeinZuordnung z = (FeinZuordnung) zuordnung;
			final AbstractMitarbeiter m = z.getMitarbeiter();
			if (m == null)
				return false;
			if (m.isChauffeur()) {
				return DSPRightCode.DISPOSITION_CHAFFEUR.isPermitted();
			} else if (m.isMonteur()) {
				final ModelContext globalContext = client.getContextCache().getGlobalContext();
				final GSECode gse = m.getGSE(globalContext);
				if (GSECode.GSE_2501.equals(gse)) {
					return DSPRightCode.DISPOSITION_MONTEUR2501.isPermitted();
				} else if (GSECode.GSE_2503.equals(gse)) {
					return DSPRightCode.DISPOSITION_MONTEUR2503.isPermitted();
				} else {
					// other GSE's are not permitted anyway
					return false;
				}
			} 
		}
		// should not get here
		return false;
	}

	public static void performDelete(final Client client, final GridElement element) {
		performDelete(client, element, true);
	}

	public static void performDelete(final Client client, 
			                         final GridElement element,
			                         final boolean askConfirmation) {
		if (!canDelete(client, element))
			return;
		if (askConfirmation) {
			if (!client.NO_ASK_CONFIRMATION.get()) {
				Component c = client.getMainWindow();
				
				int option = JOptionPane.showOptionDialog(c,
						"Wollen Sie das Element wirklich löschen?",
						"Sicherheitsabfrage", JOptionPane.YES_NO_OPTION,
						JOptionPane.WARNING_MESSAGE, null, 
						new String[] { "Ja", "Nein" }, "Nein");
	
				// cancel the action if user choose anything but YES
				if (JOptionPane.YES_OPTION != option)
					return;
			}
		}
		
		// remove the grid element
		final BlockElement block = (BlockElement) element;
		final Grid grid = block.getGrid();
		final AbstractModel model = block.getModel();
		final LineElement line = block.getLine();

		if (null!=line)
			line.removeBlock(block);
		if (null!=model) {
			final ModelContext context = model.getContext();
			context.obtain();
			try {
				// mark the model as deleted
				model.delete();
			} finally {
				context.release();
			}
		}
		// make sure the grid is repainted after removing the element
		grid.forceRepaint();
	}

}

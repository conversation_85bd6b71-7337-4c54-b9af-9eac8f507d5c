package ch.eisenring.dispo.client.gui.print;

import java.awt.GridBagLayout;

import ch.eisenring.gui.components.HEAGLabel;
import ch.eisenring.gui.components.HEAGCheckBox;
import ch.eisenring.gui.components.HEAGPanel;
import ch.eisenring.gui.util.GUIUtil;
import ch.eisenring.gui.util.LayoutUtil;

@SuppressWarnings("serial")
public class WeekDayPanel extends HEAGPanel {

	private final HEAGLabel lblTxt = new HEAGLabel("Zu druckende Tage");
	private final HEAGCheckBox ckbMon = new HEAGCheckBox("Montag");
	private final HEAGCheckBox ckbTue = new HEAGCheckBox("Dienstag");
	private final HEAGCheckBox ckbWed = new HEAGCheckBox("Mittwoch");
	private final HEAGCheckBox ckbThu = new HEAGCheckBox("Donnerstag");
	private final HEAGCheckBox ckbFri = new HEAGCheckBox("Freitag");
	private final HEAGCheckBox ckbSat = new HEAGCheckBox("Samstag");
	private final HEAGCheckBox ckbSun = new HEAGCheckBox("Sonntag");

	public WeekDayPanel() {
		ckbSun.setVisible(false);
		initLayout();
	}

	private void initLayout() {
		final LayoutUtil l = new LayoutUtil(8);
		GUIUtil.makeSameSize(ckbMon, ckbTue, ckbWed, ckbThu, ckbFri, ckbSat, ckbSun);
		removeAll();
		setLayout(new GridBagLayout());
		
		add(lblTxt, l.field(1));
		add(ckbMon, l.button());
		add(ckbTue, l.button());
		add(ckbWed, l.button());
		add(ckbThu, l.button());
		add(ckbFri, l.button());
		add(ckbSat, l.button());
		add(ckbSun, l.button());
	}
	
	public int getSelectedDayMask() {
		return (ckbMon.isSelected() ? 0x01 : 0) |
			   (ckbTue.isSelected() ? 0x02 : 0) |
			   (ckbWed.isSelected() ? 0x04 : 0) |
			   (ckbThu.isSelected() ? 0x08 : 0) |
			   (ckbFri.isSelected() ? 0x10 : 0) |
			   (ckbSat.isSelected() ? 0x20 : 0) |
			   (ckbSun.isSelected() ? 0x40 : 0);
	}
	
	public int getSelectedDayCount() {
		return Integer.bitCount(getSelectedDayMask());
	}

}

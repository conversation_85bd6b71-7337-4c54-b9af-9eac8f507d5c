package ch.eisenring.dispo.client.gui.auftrag;

import static ch.eisenring.dispo.shared.codetables.ZuordnungCode.ZUORDNUNG_OVERFLOW;

import java.awt.GridBagLayout;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;

import javax.swing.JButton;

import ch.eisenring.core.collections.List;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.iterators.Filter;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.client.gui.components.dialog.OldAbstractDialog;
import ch.eisenring.dispo.client.gui.suche.AuftragSucheDialog;
import ch.eisenring.dispo.shared.codetables.DSPAbwicklungsArt;
import ch.eisenring.dispo.shared.codetables.DSPRightCode;
import ch.eisenring.dispo.shared.model.AbstractModel;
import ch.eisenring.dispo.shared.model.AbstractZuordnung;
import ch.eisenring.dispo.shared.model.FeinZuordnung;
import ch.eisenring.dispo.shared.model.GrobZuordnung;
import ch.eisenring.dispo.shared.model.ModelContext;
import ch.eisenring.gui.ManualBinding;
import ch.eisenring.gui.components.HEAGIntegerField;
import ch.eisenring.gui.components.HEAGLabel;
import ch.eisenring.gui.components.HEAGPanel;
import ch.eisenring.gui.dialogs.OkCancelDialog;
import ch.eisenring.gui.util.GUIUtil;
import ch.eisenring.gui.util.LayoutUtil;

@SuppressWarnings("serial")
public class OverflowLinkPanel extends AuftragDetailPanel {

	private final JButton btnJump = new JButton("Auftrag Anzeigen...");
	private final JButton btnDelete = new JButton(Strings.concat(DSPAbwicklungsArt.OVERFLOW, " löschen..."));
	private final HEAGLabel lblAuftrag = new HEAGLabel("Quell-Auftrag:");
	private AbstractZuordnung zuordnung;
	private final HEAGIntegerField intAuftragNr = new HEAGIntegerField(new ManualBinding(7) {
		protected Object getModelValueImpl(final Object model) {
			return ((AbstractZuordnung) model).getAuftragNummer();
		}
		protected void setModelValueImpl(final Object model, final Object value) {
			// can't set value
		}
		protected boolean isValidModel(final Object model) {
			return model instanceof AbstractZuordnung;
		}
	}, 7, false);

	private final ActionListener jumpListener = new ActionListener() {
		@Override
		public void actionPerformed(final ActionEvent event) {
			GUIUtil.hideWindow(OverflowLinkPanel.this);
			AuftragSucheDialog suche = new AuftragSucheDialog(client);
			suche.performSearch(null, zuordnung.getAuftragNummer());
		}
	};

	private final ActionListener deleteListener = new ActionListener() {
		@Override
		public void actionPerformed(ActionEvent e) {
			final String text = Strings.concat("<html>Achtung!<br><br>"
						+ "Wollen Sie wirklich alle ", DSPAbwicklungsArt.OVERFLOW, " in dieser<br>"
						+ "Kalenderwoche für Auftrag Nr. ", zuordnung.getAuftragNummer(), " löschen?");
			boolean doIt = OkCancelDialog.doDialog(text, "Ja", "Nein\n", "Löschen bestätigen");
			if (doIt) {
				GUIUtil.hideWindow(OverflowLinkPanel.this);
				deleteOverflow(client, zuordnung.getAuftragNummer(), zuordnung.getContext());
			}
		}
	};
	
	public OverflowLinkPanel(OldAbstractDialog dialog) {
		super(dialog.getClient());
		initLayout();
	}
	
	private void initLayout() {
		final LayoutUtil l = new LayoutUtil(5);
		removeAll();

		intAuftragNr.setEditable(false);
		btnJump.addActionListener(jumpListener);
		btnDelete.addActionListener(deleteListener);

		setLayout(new GridBagLayout());
		add(lblAuftrag, l.label());
		add(intAuftragNr, l.fixed(1));
		add(btnJump, l.button());
		add(btnDelete, l.button());
		add(new HEAGPanel(), l.field());
	}
	
	// --------------------------------------------------------------
	// ---
	// ---
	// ---
	// --------------------------------------------------------------
	@Override
	public void evaluateChanged(final Object model) {
		// this panel is never changed, never evaluate
	}

	@Override
	public boolean isViewChanged() {
		// this panel is never changed
		return false;
	}

	@Override
	protected void showingFirstTimeImpl() {
		showingAgainImpl();
	}

	@Override
	protected void updateEditableImpl() {
		// nothing
	}

	@Override
	public boolean isEditable() {
		return true;
	}
	
	@Override
	public void setEditable(final boolean editable) {
		// no-op
	}
	
	@Override
	public void updateView(final Object model) {
		super.updateView(model);
		if (model instanceof AbstractZuordnung) {
			zuordnung = (AbstractZuordnung) model;
		}
		btnJump.setEnabled(zuordnung != null);
		btnDelete.setEnabled(DSPRightCode.GROBPLANUNG.isPermitted());
		
		// delete is only enabled if the panel is displayed
		// for a real overflow...
		boolean v = false; 
		if (zuordnung instanceof FeinZuordnung) {
			FeinZuordnung f = (FeinZuordnung) zuordnung;
			v = ZUORDNUNG_OVERFLOW.equals(f.getType());
		} else if (zuordnung instanceof GrobZuordnung) {
			GrobZuordnung g = (GrobZuordnung) zuordnung;
			if (0!=g.getAuftragNummer() && null==g.getAuftrag()) {
				v = true;
			}
		}
		btnDelete.setVisible(v);
	}
	
	// --------------------------------------------------------------
	// ---
	// --- Overflow deletion
	// ---
	// --------------------------------------------------------------
	@SuppressWarnings("unchecked")
	public static void deleteOverflow(final Client client, final int auftragNr, final ModelContext context) {
		if (context == null)
			return;
		if (auftragNr <= 0)
			return;
		context.obtain();
		try {
			// Find all FeinPlanung/FolgeDisposition
			List<AbstractModel> l = (List) context.getModelList(FeinZuordnung.class, new Filter<AbstractModel>() {
				@Override
				public boolean accepts(AbstractModel model) {
					final FeinZuordnung z = (FeinZuordnung) model;
					return auftragNr==z.getAuftragNummer()
							&& ZUORDNUNG_OVERFLOW.equals(z.getType()); 
				}
			}); 
			// Find all GrobPlanung/FolgeDisposition
			l.addAll(context.getModelList(GrobZuordnung.class, new Filter<AbstractModel>() {
				@Override
				public boolean accepts(AbstractModel model) {
					final GrobZuordnung z = (GrobZuordnung) model;
					return auftragNr==z.getAuftragNummer()
							&& null!=z.getOverflow();
				}
			}));
			// perform the necessary deletes
			for (int i=l.size()-1; i>=0; --i) {
				AbstractModel model = l.get(i);
				if (model instanceof GrobZuordnung)
					((GrobZuordnung) model).getOverflow().delete();
				model.delete();
			}
			client.sendModelChanges(context);
		} finally {
			context.release();
		}
	}
	
}

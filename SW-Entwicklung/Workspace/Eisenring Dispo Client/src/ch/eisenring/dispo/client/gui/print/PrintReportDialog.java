package ch.eisenring.dispo.client.gui.print;

import static ch.eisenring.dispo.client.DSPClientConstants.LUS_POS_FOTOBERICHTPRINT;

import java.awt.Dimension;

import ch.eisenring.core.tag.TagSet;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.gui.util.GUIUtil;
import ch.eisenring.print.client.job.ClientPrintReportJob;
import ch.eisenring.print.shared.job.AbstractJob;
import ch.eisenring.print.shared.job.PrintTags;
import ch.eisenring.print.shared.model.PRTReportDataSource;
import ch.eisenring.print.shared.resource.ReportResource;
import ch.eisenring.print.shared.resource.images.Images;

@SuppressWarnings("serial")
public abstract class PrintReportDialog extends AbstractPrintDialog {

	protected final ReportResource reportResource;
	
	public PrintReportDialog(final Client client, final ReportResource reportResource) {
		super(client, LUS_POS_FOTOBERICHTPRINT, Images.PRINTER);
		this.reportResource = reportResource;
		setTitle(reportResource + " Drucken");
		setMinimumSize(GUIUtil.max(getMinimumSize(), new Dimension(200, 40)));
		pack();
		setResizable(false);
	}
	
	protected abstract PRTReportDataSource createReportSource();
	
	@Override
	protected void doPrintJob() {
		final AbstractJob job = new ClientPrintReportJob<Client>(
					client, reportResource, createReportSource(),
					new TagSet(PrintTags.PRINTER, getSelectedPrinter(),
							   PrintTags.NUM_COPIES, getNumCopies()));
		client.addJob(job);
	}

}

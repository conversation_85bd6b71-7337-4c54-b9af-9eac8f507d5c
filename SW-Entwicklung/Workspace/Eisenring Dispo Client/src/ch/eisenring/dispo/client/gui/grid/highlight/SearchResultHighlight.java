package ch.eisenring.dispo.client.gui.grid.highlight;

import java.awt.Color;
import java.util.Collection;

import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.client.gui.grid.BlockElement;
import ch.eisenring.dispo.shared.model.AbstractModel;
import ch.eisenring.dispo.shared.model.AbstractZuordnung;
import ch.eisenring.dispo.shared.model.Auftrag;
import ch.eisenring.dispo.shared.service.SearchResultEntryAuftrag;

public class SearchResultHighlight extends ElementHighlight {

	private final int[] auftragNummern;
	
	public SearchResultHighlight(final Client client,
			final Collection<SearchResultEntryAuftrag> results) {
		super(client);
		auftragNummern = new int[results.size()];
		int i = 0;
		for (SearchResultEntryAuftrag result : results) {
			auftragNummern[i++] = result.getAuftragsNummer();
		}
	}

	@Override
	public Color getColor(final BlockElement blockElement) {
		final AbstractModel model = blockElement.getModel();
		if (model instanceof AbstractZuordnung) {
			final AbstractZuordnung zuordnung = (AbstractZuordnung) model;
			final Auftrag auftrag = zuordnung.getAuftrag();
			if (auftrag != null && contains(auftrag.getAuftragnummer())) {
				return RELATED;
			} else if (contains(zuordnung.getAuftragNummer())) {
				return RELATED;
			}
		}
		return UNRELATED;
	}

	private boolean contains(final int auftragNummer) {
		for (int i=auftragNummern.length-1; i>=0; --i) {
			if (auftragNummer == auftragNummern[i])
				return true;
		}
		return false;
	}

	// --------------------------------------------------------------
	// ---
	// --- Object overrides
	// ---
	// --------------------------------------------------------------
	@Override
	public int hashCode() {
		return -1;
	}

	@Override
	public boolean equals(final Object o) {
		if (!(o instanceof SearchResultHighlight))
			return false;
		return this == o;
	}

}

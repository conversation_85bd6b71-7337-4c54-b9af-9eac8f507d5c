package ch.eisenring.dispo.client.action;

import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.shared.codetables.DSPRightCode;
import ch.eisenring.dispo.shared.network.packets.PacketServerFlushAll;

public final class ActionServerFlushAll extends AbstractDSPAction {
 
	public ActionServerFlushAll(final Client client) {
		super(client, "Alle _Wochen neu laden");
		addPermissionObserver();
	}

	@Override
	protected void performAction() {
		if (!askConfirmation(client, "Sind Sie sicher das Si<PERSON> alle Kalenderwochen\nneu laden wollen? (Die Aktion betrifft alle Benutzer!)")) {
			return;
		}
		client.sendPacket(PacketServerFlushAll.create());
	}

	@Override
	protected boolean isEnabledImpl() {
		return DSPRightCode.RELOAD_ALL.isPermitted();
	}

}

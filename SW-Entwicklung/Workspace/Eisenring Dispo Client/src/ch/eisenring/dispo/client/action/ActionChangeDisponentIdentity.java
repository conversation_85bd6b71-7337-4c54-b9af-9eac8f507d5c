package ch.eisenring.dispo.client.action;

import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.shared.codetables.DSPRightCode;
import ch.eisenring.logiware.code.soft.LWDisponentSirCode;

public final class ActionChangeDisponentIdentity extends AbstractDSPAction {

	protected final LWDisponentSirCode identity;

	public ActionChangeDisponentIdentity(final Client client, final LWDisponentSirCode identity) {
		super(client, AbstractCode.getLongText(identity, "???"));
		this.identity = identity;
		addPermissionObserver();
	}

	@Override
	protected void performAction() {
		client.DISPONENT_IDENTITY.set(identity);
	}
	
	@Override
	protected boolean isEnabledImpl() {
		return DSPRightCode.DISPOSITION_EDIT.isPermitted();
	}

}

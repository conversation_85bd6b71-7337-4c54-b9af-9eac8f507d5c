package ch.eisenring.dispo.client.gui.auftrag.solidarhaftung;

import java.awt.GridBagLayout;

import javax.swing.JScrollPane;
import javax.swing.ListSelectionModel;
import javax.swing.ScrollPaneConstants;

import ch.eisenring.core.collections.List;
import ch.eisenring.gui.GridBagConstraints;
import ch.eisenring.gui.components.HEAGCheckBox;
import ch.eisenring.gui.components.HEAGPanel;
import ch.eisenring.gui.components.HEAGTable;
import ch.eisenring.gui.model.ValidationResults;

@SuppressWarnings("serial")
public class SolidarhaftungSelectPanel extends HEAGPanel {

	final SolidarhaftungSelectTableModel tableModel;
	final HEAGTable table = new HEAGTable();
	final JScrollPane scrollPane = new JScrollPane();
	final HEAGCheckBox chkOpenDMS = new HEAGCheckBox("Ablageordner für \"Deklaration Solidarhaftung\" nach Mailversand öffnen");
	final SolidarhaftungData data;

	public SolidarhaftungSelectPanel(final SolidarhaftungData data) {
		this.data = data;
		this.tableModel = new SolidarhaftungSelectTableModel(data.client);
		initComponents();
		initLayout();
	}

	private void initComponents() {
		setLayout(new GridBagLayout());
		table.setModel(tableModel);
		scrollPane.setVerticalScrollBarPolicy(ScrollPaneConstants.VERTICAL_SCROLLBAR_ALWAYS);
		scrollPane.setHorizontalScrollBarPolicy(ScrollPaneConstants.HORIZONTAL_SCROLLBAR_AS_NEEDED);
		scrollPane.setViewportView(table);
		tableModel.applyLayout(table);
		table.setSelectionMode(ListSelectionModel.MULTIPLE_INTERVAL_SELECTION);
		chkOpenDMS.setSelected(data.openFolderInDMS);
	}

	private void initLayout() {
		add(scrollPane, GridBagConstraints.area(0, 0));
		add(chkOpenDMS, GridBagConstraints.field(0, 1));
	}

	public List<SolidarhaftungSelectInfo> getSelection() {
		return tableModel.getSelection(table);
	}

	@Override
	public void validateView(final ValidationResults results, final Object model) {
		super.validateView(results, model);
		if (getSelection().isEmpty()) {
			results.add("Keine Subunternehmer aufgewählt", table);
		}
	}

	@Override
	public void updateModel(final Object model) {
		super.updateModel(model);
		data.openFolderInDMS = chkOpenDMS.isSelected();
		final List<SolidarhaftungSelectInfo> infos = getSelection();
		data.documents.clear();
		for (final SolidarhaftungSelectInfo info : infos) {
			data.documents.addAll(info.lohnBedingungen);
			data.documents.addAll(info.arbeitsBedingungen);
		}
	}

}

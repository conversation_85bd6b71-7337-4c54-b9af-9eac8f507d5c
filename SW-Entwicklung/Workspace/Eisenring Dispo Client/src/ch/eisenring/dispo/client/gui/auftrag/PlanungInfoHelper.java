package ch.eisenring.dispo.client.gui.auftrag;

import java.awt.Component;
import java.util.Iterator;

import javax.swing.JPopupMenu;

import ch.eisenring.core.collections.List;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.client.action.ActionJumpToZuordnungInfo;
import ch.eisenring.dispo.client.gui.AutoOpenEditHint;
import ch.eisenring.dispo.shared.model.AbstractZuordnung;
import ch.eisenring.dispo.shared.model.Auftrag;
import ch.eisenring.dispo.shared.network.packets.PacketInfoReply;
import ch.eisenring.dispo.shared.network.packets.PacketInfoRequest;
import ch.eisenring.dispo.shared.service.ZuordnungInfoEntry;
import ch.eisenring.gui.menu.MenuBuilder;

public class PlanungInfoHelper {

	public static PacketInfoRequest createRequest(final AbstractZuordnung zuordnung) {
		PacketInfoRequest result = null;
		if (zuordnung != null) {
			int auftragNummer = zuordnung.getAuftragNummer();
			final Auftrag auftrag = zuordnung.getAuftrag();
			if (auftrag != null) {
				auftragNummer = auftrag.getAuftragnummer();
			}
			if (auftragNummer != 0) {
				result = PacketInfoRequest.create(auftragNummer);
			}
		}
		return result;
	}

	public static JPopupMenu createPopupMenu(final Client client, final PacketInfoReply response, final Component component) {
		final MenuBuilder b = new MenuBuilder();
		final List<ZuordnungInfoEntry> list = response.getSearchResults();
		final Iterator<ZuordnungInfoEntry> iterator = list.iterator();
		while (iterator.hasNext()) {
			final ZuordnungInfoEntry info = iterator.next();
			final ActionJumpToZuordnungInfo action = new ActionJumpToZuordnungInfo(client, info, component);
			b.add(action);
		}
		return b.isEmpty() ? null : b.populate(null);
	}
	
	public static void jumpToZuordnung(final Client client, final ZuordnungInfoEntry zuordnung) {
		if (zuordnung != null) {
	 		final AutoOpenEditHint autoEditHint = AutoOpenEditHint.createByObjectId(zuordnung.getOID());
		 	client.STATE_AUTOEDIT.set(autoEditHint); 
		 	client.getContextCache().setCurrentContext(zuordnung.getBegin());
		}
	}

}

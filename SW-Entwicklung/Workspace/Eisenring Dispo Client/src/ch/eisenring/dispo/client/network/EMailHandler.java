package ch.eisenring.dispo.client.network;

import ch.eisenring.dispo.client.Client;
import ch.eisenring.email.network.PacketEMail;
import ch.eisenring.network.PacketSink;
import ch.eisenring.network.implementation.PacketDispatchMode;
import ch.eisenring.network.packet.AbstractPacket;

public class E<PERSON><PERSON><PERSON><PERSON><PERSON> extends AbstractDSPPacketHandler {

	public EMailHandler(final Client client) {
		super(client, PacketEMail.class, PacketDispatchMode.SYNCHRONOUS);
	}
	
	@Override
	public void handle(final AbstractPacket abstractPacket, final PacketSink sink) {
		// the client deals with this packet only through specifically registered listeners
	}
	
}

package ch.eisenring.dispo.client.gui.toolbar;

import java.awt.GridBagLayout;

import javax.swing.event.DocumentEvent;
import javax.swing.event.DocumentListener;

import ch.eisenring.dispo.client.Client;
import ch.eisenring.gui.GUIConstants;
import ch.eisenring.gui.GridBagConstraints;
import ch.eisenring.gui.components.HEAGLabel;
import ch.eisenring.gui.components.HEAGPanel;
import ch.eisenring.gui.components.HEAGTextField;
import ch.eisenring.gui.util.GUIUtil;

@SuppressWarnings("serial")
public class BasisNummerFilter extends HEAGPanel {

	final Client client;
	
	final HEAGLabel lblBasisNummer = new HEAGLabel("Basisnummer");
	final HEAGTextField txtBasisNummer = new HEAGTextField(16);
	
	private DocumentListener docListener = new DocumentListener() {
		@Override
		public void changedUpdate(final DocumentEvent event) {
			filterChanged(event);
		}
		@Override
		public void insertUpdate(final DocumentEvent event) {
			filterChanged(event);
		}
		@Override
		public void removeUpdate(DocumentEvent event) {
			filterChanged(event);
		}
	};

	public BasisNummerFilter(final Client client) {
		super(new GridBagLayout());
		this.client = client;
		initComponents();
		initLayout();
	}

	private void initComponents() {
		GUIUtil.setSizeForText(txtBasisNummer, "0000000");
		txtBasisNummer.setToolTipText("<html>Grobplanung filtern nach Basis");
		txtBasisNummer.getDocument().addDocumentListener(docListener);
	}

	private void initLayout() {
		add(lblBasisNummer, GridBagConstraints.field(0, 0).insets(0, 0, GUIConstants.VSPACE, 0));
		add(txtBasisNummer, GridBagConstraints.field(0, 1).insets(0));
	}

	void filterChanged(final DocumentEvent event) {
		final String text = txtBasisNummer.getText();
		client.BASISNUMMER_FILTER_CRITERION.set(text);
	}

}

package ch.eisenring.dispo.client.gui.jumpto;

import java.awt.GridBagLayout;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.KeyAdapter;
import java.awt.event.KeyEvent;
import java.awt.event.KeyListener;

import javax.swing.JButton;

import ch.eisenring.core.datatypes.date.HEAGCalendar;
import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.client.gui.components.dialog.AbstractDSPDialog;
import ch.eisenring.gui.components.HEAGDateField;
import ch.eisenring.gui.components.HEAGKWField;
import ch.eisenring.gui.components.HEAGKWField.KWInputResult;
import ch.eisenring.gui.components.HEAGLabel;
import ch.eisenring.gui.components.HEAGPanel;
import ch.eisenring.gui.util.GUIUtil;
import ch.eisenring.gui.util.LayoutUtil;

@SuppressWarnings("serial")
public class JumpToPanel extends HEAGPanel {

	private final AbstractDSPDialog dialog;
	
	private final HEAGLabel lblDate = new HEAGLabel("Datum");
	private final HEAGLabel lblKW = new HEAGLabel("Kalenderwoche");

	private final HEAGDateField dateDate = new HEAGDateField();
	private final HEAGKWField kwField = new HEAGKWField();

	private final JButton btnJumpDate = new JButton("Go");
	private final JButton btnJumpKW = new JButton("Go");

	private final KeyListener keyDateListener = new KeyAdapter() {
		@Override
		public void keyPressed(KeyEvent e) {
			if (KeyEvent.VK_ENTER==e.getKeyCode())
				jumpToDate();
		}
	};
	
	private final KeyListener keyKWListener = new KeyAdapter() {
		@Override
		public void keyPressed(KeyEvent e) {
			if (KeyEvent.VK_ENTER==e.getKeyCode())
				jumpToKW();
		}
	};

	private final ActionListener jumpDateListener = new ActionListener() {
		@Override
		public void actionPerformed(ActionEvent e) {
			jumpToDate();
		}
	};
	
	private final ActionListener jumpKWListener = new ActionListener() {
		@Override
		public void actionPerformed(ActionEvent e) {
			jumpToKW();
		}
	};
	
	public JumpToPanel(final AbstractDSPDialog dialog) {
		this.dialog = dialog;
		initLayout();
		initDefaults();
	}
	
	private void initLayout() {
		btnJumpDate.addActionListener(jumpDateListener);
		btnJumpKW.addActionListener(jumpKWListener);
		btnJumpDate.setFocusable(false);
		btnJumpKW.setFocusable(false);
		GUIUtil.addKeyListener(dateDate, keyDateListener);
		GUIUtil.addKeyListener(kwField, keyKWListener);
		final LayoutUtil l = new LayoutUtil(3);
		GUIUtil.makeSameSize(btnJumpDate, btnJumpKW);
		removeAll();
		setLayout(new GridBagLayout());
		
		add(lblDate, l.label());
		add(dateDate, l.field(1));
		add(btnJumpDate, l.fixed());

		add(lblKW, l.label());
		add(kwField, l.field(1));
		add(btnJumpKW, l.fixed());
	}
	
	private void initDefaults() {
		final HEAGCalendar c = HEAGCalendar.obtainNow();
		dateDate.setDate(c.getTime());
		kwField.setDateRange(TimestampUtil.MIN_KWTIMESTAMP, TimestampUtil.MAX_KWTIMESTAMP);
		kwField.setDate(c.getTimeInMillis());
		c.release();
	}

	public Client getClient() {
		return dialog.getClient();
	}

	protected void jumpToDate() {
		long date = dateDate.getTimestamp();
		//Date date = dateDate.getDate();
		if (TimestampUtil.isNull(date)) {
			dateDate.requestFocusInWindow();
			dialog.getFeedbackHandler().warn("Das angegebene Datum ist ungültig");
		} else {
			date = TimestampUtil.findMonday(date, 0);
			if (date < TimestampUtil.MIN_KWTIMESTAMP) {
				dateDate.setValue(TimestampUtil.MIN_KWTIMESTAMP);
				dateDate.requestFocusInWindow();
				dialog.getFeedbackHandler().warn("Das angegebene Datum liegt zu weit in der Vergangenheit (min. 01.01.2008)");
			} else if (date > TimestampUtil.MAX_KWTIMESTAMP) {
				dateDate.setValue(TimestampUtil.MAX_KWTIMESTAMP);
				dateDate.requestFocusInWindow();
				dialog.getFeedbackHandler().warn("Das angegebene Datum liegt zu weit in der Zukunft (max. 31.12.2099)");
			} else {
				// request change of current model context
				getClient().getContextCache().setCurrentContext(date);
				dialog.setVisible(false);
			}
		}
	}

	protected void jumpToKW() {
		final KWInputResult result = kwField.getInput(true);
		if (result.isValid()) {
			getClient().getContextCache().setCurrentContext(result.date);
			dialog.setVisible(false);
		} else {
			dialog.getFeedbackHandler().error(result.error);
		}
	}

}

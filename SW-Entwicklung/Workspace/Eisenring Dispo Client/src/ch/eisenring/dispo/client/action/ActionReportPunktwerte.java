package ch.eisenring.dispo.client.action;

import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.client.gui.report.endmontagen.ReportPunktwerteDialog;
import ch.eisenring.dispo.client.resource.imgnew.Images;
import ch.eisenring.dispo.shared.codetables.DSPRightCode;
import ch.eisenring.gui.window.AbstractBaseWindow;

@SuppressWarnings("serial")
public class ActionReportPunktwerte extends AbstractDSPAction {

	public ActionReportPunktwerte(final Client client) {
		super(client, "Auswertung Punktwerte...", Images.LINEGRAPH);
		addPermissionObserver();
	}

	@Override
	protected boolean isEnabledImpl() {
		return DSPRightCode.GL_AUSWERTUNG.isPermitted();
	}
	
	@Override
	protected void performAction() {
		final ReportPunktwerteDialog dialog = new ReportPunktwerteDialog(client);
		AbstractBaseWindow.showWindow(dialog);
	}

}

package ch.eisenring.dispo.client.network;

import static ch.eisenring.network.implementation.ConnectionStatusCode.CON_ERROR;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.client.DSPServerConnection;
import ch.eisenring.network.PacketSink;
import ch.eisenring.network.implementation.PacketDispatchMode;
import ch.eisenring.network.packet.AbstractPacket;
import ch.eisenring.network.packet.PacketConnectionRejected;

public final class ConnectionRejectedHandler extends AbstractDSPPacketHandler {

	ConnectionRejectedHandler(final Client client) {
		super(client, PacketConnectionRejected.class, PacketDispatchMode.SYNCHRONOUS);
	}

	@Override
	public void handle(final AbstractPacket abstractPacket, final PacketSink sink) {
		client.error("Der Server hat die Verbindung abgelehnt");
		((DSPServerConnection) sink).terminate(CON_ERROR, "Der Server hat die Verbinding abgelehnt", null);
	}

}

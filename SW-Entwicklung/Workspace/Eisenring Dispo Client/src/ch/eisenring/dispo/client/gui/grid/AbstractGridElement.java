package ch.eisenring.dispo.client.gui.grid;

import static ch.eisenring.dispo.client.DSPClientConstants.APPEARANCE_NORMAL;
import static ch.eisenring.dispo.shared.codetables.ZuordnungCode.ZUORDNUNG_ABLADEN;
import static ch.eisenring.dispo.shared.codetables.ZuordnungCode.ZUORDNUNG_ABWESEND;
import static ch.eisenring.dispo.shared.codetables.ZuordnungCode.ZUORDNUNG_SUPERVISOR;
import static ch.eisenring.dispo.shared.codetables.ZuordnungCode.ZUORDNUNG_ZUSATZ;

import java.awt.Rectangle;
import java.awt.event.MouseEvent;
import java.util.Iterator;

import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.collections.Map;
import ch.eisenring.core.collections.impl.HashMap;
import ch.eisenring.core.iterators.EmptyIterator;
import ch.eisenring.dispo.client.gui.dnd.DnDHelper;
import ch.eisenring.dispo.shared.codetables.DSPGranitMontageSCCode;
import ch.eisenring.dispo.shared.codetables.DSPMaterialSCCode;
import ch.eisenring.dispo.shared.codetables.VerschiebenCode;
import ch.eisenring.dispo.shared.codetables.ZuordnungCode;
import ch.eisenring.dispo.shared.model.AbstractMitarbeiter;
import ch.eisenring.dispo.shared.model.AbstractModel;
import ch.eisenring.dispo.shared.model.Auftrag;
import ch.eisenring.logiware.code.soft.GSECode;
import ch.eisenring.logiware.code.soft.LWGranit1Code;
import ch.eisenring.logiware.code.soft.LWMarketingCode;

public abstract class AbstractGridElement implements GridElement {

	protected final static Iterator<GridElement> EMPTY_CHILD_ITERATOR = 
			EmptyIterator.get(GridElement.class);

	private final static int RESIZE_WIDTH_MIN = 1;
	private final static int RESIZE_WIDTH_MAX = 5;
	
	private final Rectangle bounds = new Rectangle();
	private final AbstractModel model;
	private int cellIndex;
	private int cellSpan;
	private int appearance = APPEARANCE_NORMAL;
	
	protected AbstractGridElement(final AbstractModel model) {
		this(model, 0, 0);
	}
	
	protected AbstractGridElement(final AbstractModel model, int cellIndex, int cellSpan) {
		this.model = model;
		setCellIndex(cellIndex);
		setCellSpan(cellSpan);
	}
	
	/**
	 * Calculates this elements pixel bounds.
	 */
	public abstract void calculateBounds();

	/**
	 * Sets the bounds of this element.
	 * Should be called by the implementation of calculateBounds().
	 */
	protected void setBounds(int x, int y, int width, int height) {
		bounds.setBounds(x, y, width, height);
	}

	/**
	 * Gets the bounds of this element.
	 */
	public final Rectangle getBounds() {
		return new Rectangle(this.bounds);
	}

	@Override
	public int getX() {
		return this.bounds.x;
	}
	
	@Override
	public int getY() {
		return this.bounds.y;
	}

	@Override
	public int getXOnScreen() {
		final Grid grid = getGrid();
		return this.bounds.x + (null==grid ? 0 : grid.getXOnScreen());
	}
	
	@Override
	public int getYOnScreen() {
		final Grid grid = getGrid();
		return this.bounds.y + (null==grid ? 0 : grid.getYOnScreen());
	}
	
	@Override
	public int getWidth() {
		return this.bounds.width;
	}
	
	@Override
	public int getHeight() {
		return this.bounds.height;
	}

	@Override
	public final int getCellIndex() {
		return this.cellIndex;
	}
	
	@Override
	public final void setCellIndex(int columnIndex) {
		this.cellIndex = columnIndex;
	}
	
	@Override
	public int getEndIndex() {
		return this.cellIndex + this.cellSpan - 1;
	}
	
	@Override
	public final int getCellSpan() {
		return cellSpan;
	}
	
	@Override
	public final void setCellSpan(int cellSpan) {
		this.cellSpan = cellSpan;
	}

	public final void setCellRange(int beginIndex, int endIndex) {
		if (endIndex<beginIndex) {
			cellIndex = endIndex;
			cellSpan = (1+beginIndex-endIndex);
		} else {
			cellIndex = beginIndex;
			cellSpan = (1+endIndex-beginIndex);
		}
	}

	@Override
	public DSPGridLayout getLayout() {
		final Grid grid = getGrid();
		return null==grid ? null : grid.getGridLayout();
	}
	
	@Override
	public String getLabel() {
		return this.model.getLabel();
	}
	
	@Override
	public AbstractModel getModel() {
		return this.model;
	}

	@Override
	public void setAppearance(int appearance) {
		this.appearance = appearance;
	}
	
	@Override
	public int getAppearance() {
		return this.appearance;
	}

	@Override
	public GridElement getElementUnder(int x, int y) {
		final Grid grid = getGrid();
		final int x0 = this.bounds.x + (null==grid ? 0 : grid.getXOnScreen());
		final int y0 = this.bounds.y + (null==grid ? 0 : grid.getYOnScreen());
		final int x1 = this.bounds.width + x0 - 1;
		final int y1 = this.bounds.height + y0 - 1;
		return x>=x0 && x<=x1 && y>=y0 && y<=y1 ? this : null;
	}
	
	@Override
	public int getZoneId(final int x, final int y) {
		final Grid grid = getGrid();

		// bounds of element
		final int x0 = this.bounds.x + (null==grid ? 0 : grid.getXOnScreen());
		final int y0 = this.bounds.y + (null==grid ? 0 : grid.getYOnScreen());
		final int x1 = this.bounds.width + x0 - 1;
		final int y1 = this.bounds.height + y0 - 1;

		if (x<x0 || x>x1 || y<y0 || y>y1)
			return DnDHelper.ZONE_OUTSIDE;
		
		// determine the width of the resize area
		int resizeWidth = this.bounds.width/3;
		if (resizeWidth<RESIZE_WIDTH_MIN)
			resizeWidth = RESIZE_WIDTH_MIN;
		else if (resizeWidth>RESIZE_WIDTH_MAX)
			resizeWidth = RESIZE_WIDTH_MAX;
		
		if (x<x0+resizeWidth)
			return DnDHelper.ZONE_RESIZE_WEST;
		
		if (x>x1-resizeWidth)
			return DnDHelper.ZONE_RESIZE_EAST;
		
		return DnDHelper.ZONE_DRAGBODY;
	}

	@Override
	public void forceRepaint() {
		final Grid grid = getGrid();
		if (null!=grid)
			grid.forceRepaint();
	}

	@Override
	public int getDragX(MouseEvent event) {
		return event.getXOnScreen()-getXOnScreen();
	}
	
	@Override
	public int getDragY(MouseEvent event) {
		return event.getYOnScreen()-getYOnScreen();
	}
	
	@Override
	public boolean acceptsDrop(GridElement element, int x, int y) {
		return getAcceptHint().isAcceptable(this, element);
	}

	@Override
	public int getIconFlags() {
		return 0;
	}

	private final static Map<ZuordnungCode, Integer> TYPE_ICON_FLAGS;
	static {
		final Map<ZuordnungCode, Integer> m = new HashMap<>();
		m.put(ZUORDNUNG_ZUSATZ, ICON_ZUSATZ);
		m.put(ZUORDNUNG_SUPERVISOR, ICON_SUPERVISOR);
		m.put(ZUORDNUNG_ABLADEN, ICON_ABLADEN);
		m.put(ZUORDNUNG_ABWESEND, ICON_ABWESEND);
		TYPE_ICON_FLAGS = m;
	}

	public int getIconFlags(final Auftrag auftrag, final AbstractMitarbeiter monteur, final ZuordnungCode type) {
		int result; {
			final Integer typeFlags = TYPE_ICON_FLAGS.get(type);
			result = typeFlags == null ? 0 : typeFlags.intValue();
		}

		if (auftrag == null)
			return result;
			
		if (ZUORDNUNG_ZUSATZ.equals(type)) {
			// ZusatzPlanung zeigt nicht die anderen icons vom Auftrag
		} else {
			result |= ICON_OK;

			// check for VIP icon
			final LWMarketingCode marketing = auftrag.getMarketing();
			if (AbstractCode.equals(LWMarketingCode.VIP, marketing))
				result |= ICON_VIP;

			// check for UmbauBewohnt
			final LWGranit1Code granit = auftrag.getGranitschluessel1();
			if (granit != null && granit.isBewohnt())
				result |= ICON_BEWOHNT;

			// SpezialKüche?
			if (auftrag.isSpezialKueche())
				result |= ICON_SPEZKUECHE;
			
			// Ausstand detection
			{ 
				final GSECode gse = auftrag.getGSEAuftrag();
				int ausstandBau = auftrag.getAusstandCountBaustelle();
				int ausstandTotal = auftrag.getAusstandCount();
				// Spezialfall Service Center
				if (GSECode.GSE_2503.equals(gse)) {
					if (DSPGranitMontageSCCode.GRANITMONTAGE_JA.equals(auftrag.getGranitmontageSCCode())) {
						// Wenn Material nicht an Lager, als ausstehende Position betrachten
						if (!DSPMaterialSCCode.MATERIAL_VORHANDEN.equals(auftrag.getMaterialSCCode())) {
							++ausstandBau;
							++ausstandTotal;
						}
					}
				}
				if (ausstandTotal > 0)
					result |= ausstandTotal > ausstandBau ? ICON_PROBLEM : ICON_WARN;
			}

			final VerschiebenCode verschiebenCode = auftrag.getVerschiebenCode();
			if (AbstractCode.isNull(verschiebenCode)) {
				// no icon
			} else if (VerschiebenCode.PRIO1.equals(verschiebenCode)){
				result |= ICON_VPRIO1;
			} else if (VerschiebenCode.PRIO2.equals(verschiebenCode)){
				result |= ICON_VPRIO2;
			} else if (VerschiebenCode.PRIO3.equals(verschiebenCode)){
				result |= ICON_VPRIO3;
			}
			
			if (auftrag.getEndmontageAnzahl() >= 3 && !GSECode.GSE_2503.equals(auftrag.getGSEAuftrag())) {
				result |= ICON_HIGHVOLTAGE;
			}
		}
		return result;
	}

}

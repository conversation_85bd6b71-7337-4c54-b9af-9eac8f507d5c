package ch.eisenring.dispo.client.gui.dnd;

import static ch.eisenring.dispo.client.DSPClientConstants.APPEARANCE_GHOSTED;
import static ch.eisenring.dispo.client.DSPClientConstants.APPEARANCE_NORMAL;

import java.awt.event.MouseEvent;
import java.util.Iterator;

import javax.swing.JComponent;

import ch.eisenring.dispo.client.gui.components.FrameOverlay;
import ch.eisenring.dispo.client.gui.components.OverlayedFrame;
import ch.eisenring.dispo.client.gui.grid.BlockElement;
import ch.eisenring.dispo.client.gui.grid.DSPGridLayout;
import ch.eisenring.dispo.client.gui.grid.GridElement;
import ch.eisenring.dispo.client.gui.grid.LineElement;
import ch.eisenring.dispo.shared.model.ModelContext;
import ch.eisenring.gui.util.CursorUtil;

public final class DnDStateDrag extends DnDStateBase {

	// drag image used by this handler
	private DnDImage dragImage;
	
	// offset in pixels where the cursor grabs the image
	private int grabX, grabY;
	
	protected DnDStateDrag(final DnDMainHandler masterHandler) {
		super(masterHandler);
	}

	@Override
	public int getActivatingZone() {
		return DnDHelper.ZONE_DRAGBODY;
	}

	@Override
	public Object getActiveCursor() {
		return CursorUtil.CURSOR_HAND;
	}
	
	@Override
	protected void beginState(MouseEvent event, BlockElement gridElement) {
		// determine grab position of the element
		grabX = gridElement.getDragX(event);
		grabY = gridElement.getDragY(event);
		setCursor(getActiveCursor());
		dragImage = new DnDImage(gridElement);
		dragImage.setLocation(-32767, -32767);
		setOverlay(dragImage, event.getXOnScreen(), event.getYOnScreen());
		gridElement.setAppearance(APPEARANCE_GHOSTED);
		gridElement.forceRepaint();
	}

	@Override
	protected void terminateState() {
		final GridElement gridElement = getGridElement();
		if (null!=gridElement) {
			gridElement.setAppearance(APPEARANCE_NORMAL);
			gridElement.forceRepaint();
		}
		setOverlay(null, 0, 0);
		dragImage = null;
		super.terminateState();
	}

	@Override
	public void cancelDrag() {
		super.cancelDrag();
	}
	
	@Override
	public void mouseMoved(final MouseEvent event) {
		final DnDEvent dropEvent = new DnDEvent(getGridElement(), event.getModifiers());
		final boolean canDropHere = calculateDropLocation(dropEvent, event);
		setOverlay(dragImage, event.getXOnScreen(), event.getYOnScreen());
		if (canDropHere) {
			boolean isCopy = dropEvent.getDroppedElement().getDropHint().isCopyDrop(dropEvent);
			isCopy &= !dropEvent.getTargetDropDelegate().getAcceptHint().suppressCopy();
			setCursor(isCopy ? CursorUtil.CURSOR_DROP_COPY : CursorUtil.CURSOR_DROP_MOVE);
		} else {
			setCursor(CursorUtil.CURSOR_DROP_DENIED);
		}
	}
	
	@Override
	public void mouseReleased(final MouseEvent event) {
		final DnDEvent dropEvent = new DnDEvent(getGridElement(), event.getModifiers());
		final boolean canDropHere = calculateDropLocation(dropEvent, event);
		if (canDropHere) {
			final GridElement targetElement = dropEvent.getTargetElement();
			targetElement.getAcceptHint().performDrop(dropEvent);
			final ModelContext context = client.getCurrentModel();
			client.sendModelChanges(context);
			terminateState();
		} else {
			cancelDrag();
		}
	}

	/**
	 * Sets the overlay on screen 
	 */
	protected void setOverlay(final JComponent component, int x, int y) {
		final OverlayedFrame frame = mainHandler.getFrame();
		if (null==frame)
			return;
		final FrameOverlay overlay = frame.getOverlay();
		if (null==component) {
			overlay.setOverlayComponent(null);
		} else {
			final int lx = x-grabX;
			final int ly = y-grabY;
			overlay.setOverlayComponent(component);
			overlay.setOverlayPositionOnScreen(lx, ly);
		}
	}

	/**
	 * Calculate the position where the dropped element will be located.
	 * 
	 * Returns true if a valid position was found.
	 * Returns false, if drop was obstructed or rejected.
	 */
	private boolean calculateDropLocation(final DnDEvent dropEvent, final MouseEvent event) {
		final GridElement draggedElement = dropEvent.getDroppedElement();
		GridElement target = getGridElement(event);
		if (null==target)
			return false;
		if (!target.acceptsDrop(draggedElement, event.getXOnScreen(), event.getYOnScreen()))
			return false;
		// block element cannot be the target!
		while (target instanceof BlockElement)
			target = ((BlockElement) target).getLine(); 
		if (null==target)
			return false;
		final LineElement line = (LineElement) target;
		final DSPGridLayout lineLayout = line.getLayout();
		if (null==draggedElement)
			return false;
		// calculate the location and size within the line
		int cellSpan, cellIndex;
		if (null==lineLayout) {
			cellSpan = 12;
			cellIndex = 0;
		} else {
			int pixelIndex = event.getXOnScreen()-grabX-line.getXOnScreen()
					-lineLayout.getColumnMinX(lineLayout.getFirstColumn())
					+(lineLayout.getCellWidth()>>1);
			cellSpan = draggedElement.getCellSpan();
			cellIndex = pixelIndex / lineLayout.getCellWidth();
		}
		if (cellIndex<0)
			cellIndex = 0;
		// make the calculated span fit with whatever is in the line
		boolean obstructed;
		do {
			obstructed = false;
			final Iterator<GridElement> i = line.newChildIterator(true);
			while (i.hasNext()) {
				final GridElement childElement = i.next();
				// the element can not block itself
				if (childElement==draggedElement)
					continue;
				final int minIndex = childElement.getCellIndex();
				final int maxIndex = childElement.getEndIndex();
				if (minIndex<cellIndex && maxIndex>cellIndex) {
					obstructed = true;
					cellIndex = maxIndex+1;
				}
			}
		} while (obstructed);
		// make sure the cell span doesn't extend into another element
		final Iterator<GridElement> i = line.newChildIterator(true);
		while (i.hasNext()) {
			final GridElement childElement = i.next();
			// ignore self if it is not a copy operation
			if (childElement==draggedElement && !dropEvent.isCTRLDown())
				continue;
			final int minIndex = childElement.getCellIndex();
			final int maxIndex = childElement.getEndIndex();
			if (maxIndex<cellIndex)
				continue;
			while (minIndex<cellIndex+cellSpan)
				--cellSpan;
		}
		// if there is nothing left...
		if (cellSpan<1)
			return false;

		if (null!=lineLayout && cellSpan+cellIndex>=lineLayout.getCellCountMax())
			cellSpan = lineLayout.getCellCountMax()-cellIndex;
		
		dropEvent.setDropCellStart(cellIndex);
		dropEvent.setDropCellSpan(cellSpan);
		dropEvent.setTargetElement(target);
		return true;
	}

}

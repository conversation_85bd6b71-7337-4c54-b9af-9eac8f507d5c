package ch.eisenring.dispo.client.gui.print;

import static ch.eisenring.dispo.client.DSPClientConstants.LUS_POS_MONTEURAUFTRAG;

import java.awt.Dimension;
import java.awt.GridBagLayout;

import ch.eisenring.core.collections.List;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.client.help.HelpUtil;
import ch.eisenring.dispo.client.printing.PrintAuftragsscheineTagJob;
import ch.eisenring.dispo.shared.model.AbstractMitarbeiter;
import ch.eisenring.dispo.shared.model.ModelContext;
import ch.eisenring.dispo.shared.resource.reports.Reports;
import ch.eisenring.gui.components.Separator;
import ch.eisenring.gui.util.GUIUtil;
import ch.eisenring.gui.util.LayoutUtil;
import ch.eisenring.print.shared.resource.images.Images;

@SuppressWarnings("serial")
public class MonteurTagAuftragDruckDialog extends AbstractPrintDialog {

	private final EmployeeSelectPanel pnlMonteur;
	private final WeekDayPanel pnlWeekDay = new WeekDayPanel();
	private final ModelContext context;
	private final HelpUtil helpUtil;
	
	public MonteurTagAuftragDruckDialog(final Client client) {
		super(client, LUS_POS_MONTEURAUFTRAG, Images.PRINTER);
		this.pnlMonteur = new EmployeeSelectPanel(client);
		this.helpUtil = new HelpUtil(client, "Drucken-MonteurTag", this);
		setTitle("Auftragsscheine Drucken (Einzelne Tage)");
		initLayout();
		context = client.getCurrentModel();
		pnlMonteur.setContext(context);
		setMinimumSize(GUIUtil.max(getMinimumSize(), new Dimension(520, 420)));
		pack();
	}
	
	private void initLayout() {
		final LayoutUtil l = new LayoutUtil(1);
		getContentPane().removeAll();
		setLayout(new GridBagLayout());
		add(pnlWeekDay, l.panel(1, 0));
		add(Separator.create(), l.separator());
		add(pnlMonteur, l.panel(1, 3));
		helpUtil.attachListener(this);
	}

	protected void doPrintJob() {
		final List<AbstractMitarbeiter> list = pnlMonteur.getSelectedMitarbeiter();
		final int dayCount = pnlWeekDay.getSelectedDayCount();
		final int dayMask = pnlWeekDay.getSelectedDayMask();
		if (list.isEmpty())
			return;
		if (dayCount<1)
			return;
		final PrintAuftragsscheineTagJob job = new PrintAuftragsscheineTagJob(
				client,
				Reports.REPORT_MONTEUR_TAG_AUFTRAG,
				list, dayMask, getSelectedPrinter(), getNumCopies());
		client.addJob(job);
	}

}

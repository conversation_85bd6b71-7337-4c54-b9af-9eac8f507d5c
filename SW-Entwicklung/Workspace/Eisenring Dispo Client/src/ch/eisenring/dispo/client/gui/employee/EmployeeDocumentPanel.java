package ch.eisenring.dispo.client.gui.employee;

import java.awt.GridBagLayout;
import java.awt.datatransfer.DataFlavor;
import java.awt.datatransfer.Transferable;
import java.awt.dnd.DropTargetDropEvent;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.MouseEvent;
import java.io.File;
import java.io.FileOutputStream;
import java.util.Date;

import javax.swing.JButton;
import javax.swing.JComponent;
import javax.swing.JFileChooser;
import javax.swing.JScrollPane;
import javax.swing.ListSelectionModel;
import javax.swing.ScrollPaneConstants;
import javax.swing.TransferHandler;
import javax.swing.event.ListSelectionEvent;
import javax.swing.event.ListSelectionListener;

import ch.eisenring.app.shared.network.RPCContext;
import ch.eisenring.app.shared.network.RPCHandler;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.io.Streams;
import ch.eisenring.core.io.binary.BinaryHolder;
import ch.eisenring.core.io.binary.BinaryHolderUtil;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.core.logging.Logger.LogLevel;
import ch.eisenring.core.platform.Platform;
import ch.eisenring.core.platform.PlatformPath;
import ch.eisenring.core.tag.TagSet;
import ch.eisenring.core.util.OperationResult;
import ch.eisenring.core.util.file.FileUtil;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.client.gui.IModelPanel;
import ch.eisenring.dispo.client.network.RPCAdapter;
import ch.eisenring.dispo.client.resource.EMailTemplates;
import ch.eisenring.dispo.client.util.BinaryUtil;
import ch.eisenring.dispo.shared.model.AbstractDocument;
import ch.eisenring.dispo.shared.model.AbstractModel;
import ch.eisenring.dispo.shared.model.BinaryModel;
import ch.eisenring.dispo.shared.model.DSPContextCache;
import ch.eisenring.dispo.shared.model.EmployeeDocument;
import ch.eisenring.dispo.shared.model.MasterMitarbeiter;
import ch.eisenring.dispo.shared.model.ModelContext;
import ch.eisenring.dispo.shared.model.ModelReference;
import ch.eisenring.dispo.shared.network.packets.PacketBinaryRequest;
import ch.eisenring.dispo.shared.network.packets.PacketBinaryResponse;
import ch.eisenring.email.EMailAttachment;
import ch.eisenring.email.EMailTemplate;
import ch.eisenring.email.EMailTemplateResource;
import ch.eisenring.email.client.MailServiceTags;
import ch.eisenring.email.client.action.ActionEMailer;
import ch.eisenring.gui.FileDrop;
import ch.eisenring.gui.FileDropListener;
import ch.eisenring.gui.components.HEAGPanel;
import ch.eisenring.gui.components.HEAGTable;
import ch.eisenring.gui.util.CursorUtil;
import ch.eisenring.gui.util.GUIUtil;
import ch.eisenring.gui.util.LayoutUtil;

@SuppressWarnings("serial")
public final class EmployeeDocumentPanel extends HEAGPanel implements IModelPanel {

	protected final HEAGTable table = new HEAGTable() {{
			getSelectionModel().setSelectionMode(ListSelectionModel.MULTIPLE_INTERVAL_SELECTION);
			setDragEnabled(true);
			setTransferHandler(new TransferHandler() {
				@Override
				public int getSourceActions(final JComponent source) {
					return COPY;
				}

				@Override
				protected Transferable createTransferable(final JComponent source) {
					final List<EmployeeDocument> selection = getTableSelection();
					return new DocumentTransferable(client, selection);
				}

				@Override
				protected void exportDone(final JComponent source, final Transferable data, final int action) {
					super.exportDone(source, data, action);
				}
			});
		}

		@Override
		protected void rowDoubleClicked(final int rowIndex, final Object rowObject, final MouseEvent event) {
			if (selectedElement != null) {
				final List<EmployeeDocument> selection = List.asList(selectedElement);
				CursorUtil.setWindowBlock(EmployeeDocumentPanel.this, true);
				loadBinaries(selection);
			}
		}
	};

	protected final RPCHandler rpcHandler = new RPCAdapter() {
		@Override
		public void replyReceived(final RPCContext rpcContext) {
			final Client client = (Client) rpcContext.getComponent();
			final PacketBinaryResponse response = (PacketBinaryResponse) rpcContext.getReply();
			final BinaryViewer viewer = new BinaryViewer(client);
			final ModelContext context = getEmployee().getContext();
			if (mailAttachmentCount == 0) {
				// show documents
				viewer.viewBinary(response, context);
				CursorUtil.setWindowBlock(EmployeeDocumentPanel.this, false);
			} else {
				// turn documents into attachments
				final List<BinaryModel> binaryList = response.getBinaries();
				for (final BinaryModel binary : binaryList) {
					--mailAttachmentCount;
					final AbstractDocument document = viewer.findDocumentForBinary(binary, context);
					if (document instanceof EmployeeDocument) {
						final EmployeeDocument empDoc = (EmployeeDocument) document;
						final BinaryHolder holder = BinaryHolderUtil.create(binary.getBinary());
						final EMailAttachment attachment = new EMailAttachment(holder, empDoc.getFileName());
						mailAttachments.add(attachment);
						empDoc.getContext().obtain();
						try {
							empDoc.setMailDate(new Date());
							client.sendModelChanges(empDoc.getContext());
						} finally {
							empDoc.getContext().release();
						}
					}
				}
				if (mailAttachmentCount == 0) {
					CursorUtil.setWindowBlock(EmployeeDocumentPanel.this, false);
					updateView();
					final TagSet args = new TagSet();
					final EMailTemplateResource resource = EMailTemplates.EMAIL_MONTAGEKALKULATION;
					final EMailTemplate template;
					template = resource.getTemplate(client.getDataFileSource());
					template.addModel(getEmployee());
					template.addModel(client.getCurrentUser());		
					args.add(MailServiceTags.MAIL_TEMPLATE, template);
					args.add(MailServiceTags.DIALOG_TITLE, resource.getDialogTitle());
					args.add(MailServiceTags.MAIL_ATTACHMENTS, mailAttachments);
					ActionEMailer.showMailerDialog(args, client.getCore());
					mailAttachments = null;
				}
			}
		}
	};
	
	protected final JScrollPane scrollPane = new JScrollPane();
	protected final JButton btnAdd = new JButton("Hinzufügen...");
	protected final JButton btnDelete = new JButton("Löschen");
	protected final JButton btnMail = new JButton("Senden...");
	protected EmployeeDocument selectedElement;
	protected EmployeeDocumentTableModel tableModel;
	protected boolean editable;
	protected ModelReference<MasterMitarbeiter> employeeRef;
	protected List<EMailAttachment> mailAttachments;
	protected int mailAttachmentCount;
	protected final Client client;

	protected final ListSelectionListener selectionListener = new ListSelectionListener() {
		@Override
		public void valueChanged(ListSelectionEvent e) {
			final int rowIndex = table.getSelectedRow();
			setSelectedElement(tableModel.getRow(rowIndex));
		}
	};
	
	protected final ActionListener addListener = new ActionListener() {
		@Override
		public void actionPerformed(ActionEvent e) {
			final JFileChooser fileChooser = FileDrop.getDropFileChooser();
			fileChooser.setMultiSelectionEnabled(true);
			final int result = fileChooser.showOpenDialog(EmployeeDocumentPanel.this);
			if (JFileChooser.APPROVE_OPTION==result) {
				// user has choosen file(s)
				addFiles(fileChooser.getSelectedFiles());
			}
		}
	};
	
	protected final ActionListener deleteListener = new ActionListener() {
		@Override
		public void actionPerformed(ActionEvent e) {
			// delete the selected document (if any)
			// the document entity on the server side
			// will take care of removing the associated binary
			final List<EmployeeDocument> selection = getTableSelection();
			if (selection.size() > 0) {
				// ensure context is locked!
				final ModelContext context = selection.get(0).getContext();
				if (context != null) context.obtain();
				try {
					for (int i=0; i<selection.size(); ++i) {
						final EmployeeDocument document = selection.get(i);
						document.delete();
					}
					sendModelChanges();
				} finally {
					if (context != null) context.release();
				}
			}
			table.clearSelection();
		}
	};

	protected final ActionListener mailListener = new ActionListener() {
		@Override
		public void actionPerformed(final ActionEvent event) {
			// load the selected files
			final List<EmployeeDocument> documentList = getTableSelection();
			mailAttachments = new ArrayList<EMailAttachment>();
			mailAttachmentCount = documentList.size();
			loadBinaries(documentList);
		}
	};
	
	public EmployeeDocumentPanel(final Client client) {
		this.client = client;
		// attach all the required listeners
		new FileDrop(scrollPane, scrollPane.getBorder(), new FileDropListener() {
			@Override
			public void filesDropped(File[] files, final DropTargetDropEvent event) {
				addFiles(files);
			}
	    });
		initComponents();
		initLayout();
		updateEditable();
	}

	protected final void initComponents() {
		btnAdd.addActionListener(addListener);
		btnDelete.addActionListener(deleteListener);
		btnMail.addActionListener(mailListener);

		GUIUtil.makeSameSize(btnAdd, btnDelete, btnMail);

		// set up table scroll pane
		scrollPane.setVerticalScrollBarPolicy(ScrollPaneConstants.VERTICAL_SCROLLBAR_ALWAYS);
		scrollPane.setHorizontalScrollBarPolicy(ScrollPaneConstants.HORIZONTAL_SCROLLBAR_AS_NEEDED);
		scrollPane.setViewportView(table);
	}

	protected final void initLayout() {
		

		final LayoutUtil l = new LayoutUtil(5);
		setLayout(new GridBagLayout());
		add(btnAdd, l.fixed(1));
		add(btnMail, l.fixed(1));
		add(btnDelete, l.fixed(1));
		add(new HEAGPanel(), l.field());

		add(scrollPane, l.area());
	}	
	
	protected EmployeeDocumentTableModel createTableModel(final MasterMitarbeiter employee) {
		return new EmployeeDocumentTableModel(employee);
	}

	public MasterMitarbeiter getEmployee() {
		return employeeRef.getModel();
	}
	
	/****************************************************************
	 * Add/Remove documents
	 ****************************************************************/
	public void addFile(String fileName) {
		try {
			final File file = new File(fileName); 
			final MasterMitarbeiter employee = getEmployee();
			final ModelContext context = employee.getContext();
			final OperationResult<byte[]> result = FileUtil.readBinaryFile(file);
			if (result.isFailure()) {
				client.error(result.getMessage().getText());
				return;
			}
			byte[] rawData = result.getResult();
			if (rawData.length > (32 << 20)) {
				client.error("Die Datei ist zu gross!");
				return;
			}
			// create the binary and the document that refers to it
			// in the same context. this ensures they are both stored
			// in the same database transaction.
			//
			// NOTE: BinaryModel uses evictOnTransactionBoundary!!!
			final BinaryModel binary = new BinaryModel(context, null);
			binary.setBinary(rawData);
			final EmployeeDocument document = new EmployeeDocument(context);
			document.setEmployee(employee);
			document.setBinaryId(binary.getId());
			document.setByteSize((int) rawData.length);
			document.setFileName(file.getName());
		} catch (final Exception e) {
			Logger.log(LogLevel.ERROR, "error creating document:");
			Logger.log(LogLevel.ERROR, e);
		}
	}

	protected void setSelectedElement(final EmployeeDocument document) {
		this.selectedElement = document;
		updateEditable();
	}

	protected final void sendModelChanges() {
		// force the client to send the model back to the server NOW
		final MasterMitarbeiter employee = getEmployee();
		client.sendModelChanges(employee.getContext());
		setModel(employee);
	}

	/****************************************************************
	 * IModelPanel interface
	 ****************************************************************/
	@Override
	@SuppressWarnings("unchecked")
	public final void setModel(final AbstractModel model) {
		if (model instanceof MasterMitarbeiter) {
			table.getSelectionModel().removeListSelectionListener(selectionListener);
			setSelectedElement(null);
			final DSPContextCache cache = client.getContextCache();
			employeeRef = new ModelReference<MasterMitarbeiter>((MasterMitarbeiter) model, cache);
			tableModel = createTableModel(getEmployee());
			tableModel.setEditable(isEditable());
			table.setModel(tableModel);
			table.getSelectionModel().addListSelectionListener(selectionListener);
			tableModel.applyLayout(table);
		}
		updateEditable();
		updateView();
	}
	
	@Override
	public final void updateModel() {
		// nothing here
	}
	
	@Override
	public void updateView() {
		final MasterMitarbeiter employee = getEmployee();
		tableModel.setEmployee(employee);
		tableModel.applyLayout(table);
		updateEditable();
	}
	
	@Override
	public void setEditable(boolean editable) {
		this.editable = editable;
		updateEditable();
	}

	private void updateEditable() {
		// --- update delete document button
		btnAdd.setEnabled(editable);
		boolean deleteEnabled = false;
		final List<EmployeeDocument> selection = getTableSelection();
		if (editable) {
			deleteEnabled = selection !=  null &&
			                selection.size() == 1;
		}
		btnDelete.setEnabled(deleteEnabled);
		btnMail.setEnabled(!selection.isEmpty());
	}

	@Override
	public final boolean isEditable() {
		return editable;
	}

	/****************************************************************
	 * Selection handling
	 ****************************************************************/
	public List<EmployeeDocument> getTableSelection() {
		final List<EmployeeDocument> result = new ArrayList<EmployeeDocument>();
		final ListSelectionModel selectionModel = table.getSelectionModel();
		final int min = selectionModel.getMinSelectionIndex();
		final int max = selectionModel.getMaxSelectionIndex();
		if (min >= 0 && max >= 0) {
			for (int i=min; i<=max; ++i) {
				if (selectionModel.isSelectedIndex(i)) {
					final EmployeeDocument document = tableModel.getRow(i);
					if (document != null) {
						result.add(document);
					}
				}
			}
		}
		return result;
	}

	protected final void loadBinaries(final List<EmployeeDocument> documents) {
		if (documents != null && documents.size() > 0) {
			final List<Long> binaryIds = new ArrayList<Long>();
			for (int i=0; i<documents.size(); ++i) {
				final EmployeeDocument document = documents.get(i);
				binaryIds.add(document.getBinaryId());
			}
			if (binaryIds.size() > 0) {
				final PacketBinaryRequest request = PacketBinaryRequest.create(binaryIds);
				client.sendPacket(rpcHandler, request);		
			} else {
				CursorUtil.setWindowBlock(this, false);
			}
		}
	}

	/****************************************************************
	 * Add/Remove documents
	 ****************************************************************/
	/**
	 * Creates a bunch of file attachments from file array 
	 */
	protected final void addFiles(final File[] files) {
		CursorUtil.setWindowBlock(this, true);
		GUIUtil.invokeLater(new Runnable() {
			@Override
			public void run() {
				if (editable) {
					setEnabled(false);
					if (null!=files) {
						for (int i=0; i<files.length; ++i) {
							final File file = files[i];
							if (file.isFile() && file.exists()) {
								addFile(file.getAbsolutePath());
							}
						}
						sendModelChanges();
					}
					setEnabled(true);
				} else {
					client.error("Es fehlt Ihnen die Berechtigung hier eine Datei anzuhängen");
				}
				CursorUtil.setWindowBlock(EmployeeDocumentPanel.this, false);
			}
		});
	}

}

class DocumentTransferable implements Transferable {

	protected final Client client;
	protected final List<EmployeeDocument> documents;
	protected List<File> transferData;
	
	public DocumentTransferable(final Client client, final List<EmployeeDocument> documents) {
		this.client = client;
		this.documents = documents;
	}

	public Object getTransferData(final DataFlavor flavor) {
		if (transferData != null) {
			return transferData;
		}
		// --- load the document(s)
		//     (this is hacky as hell, since this is called on the EDT)
		final BinaryUtil util = new BinaryUtil(client);
		final List<File> list = new ArrayList<File>();
		final List<Long> binaryIds = new ArrayList<Long>();
		for (int i=0; i<documents.size(); ++i) {
			final EmployeeDocument document = documents.get(i);
			binaryIds.add(document.getBinaryId());
		}
		FileOutputStream out = null;
		try {
			final List<BinaryModel> binaries = util.getBinaryModels(binaryIds);
			for (int i=0; i<binaries.size(); ++i) {
				final BinaryModel binary = binaries.get(i);
				final AbstractDocument document = findDocumentForBinary(documents, binary);
				final File tempFile = new File(Platform.getPlatform().getPath(PlatformPath.APP_TEMP),
						 FileUtil.sanitizeFilename(document.getFileName(), false));
				out = new FileOutputStream(tempFile);
				out.write(binary.getBinary());
				list.add(tempFile);
				out.close();
				tempFile.deleteOnExit();
			}
		} catch (Exception e) {
			client.error(Strings.concat("Problem: ", e.getMessage()));
		} finally {
			Streams.closeSilent(out);
		}
		transferData = list;
		return list;
	}

	public static EmployeeDocument findDocumentForBinary(final List<EmployeeDocument> documents, final BinaryModel binary) {
		final long binaryId = binary.getId();
		for (int i=0; i<documents.size(); ++i) {
			final EmployeeDocument document = documents.get(i);
			if (binaryId == document.getBinaryId()) {
				return document;
			}
		}
		return null;
	}

	public DataFlavor[] getTransferDataFlavors() {
		return new DataFlavor[]{ DataFlavor.javaFileListFlavor };
	}

	public boolean isDataFlavorSupported(DataFlavor flavor) {
		return DataFlavor.javaFileListFlavor.equals(flavor);
	}

}

 
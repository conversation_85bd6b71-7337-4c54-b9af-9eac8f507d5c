package ch.eisenring.dispo.client.logic;

import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.client.gui.dnd.DnDHintDrag;
import ch.eisenring.dispo.client.gui.grid.GridElement;
import ch.eisenring.dispo.shared.modelapi.Zuordnung;

public final class DnDDragGrobZuordnung extends DnDHintDrag {

	public final static DnDDragGrobZuordnung INSTANCE = new DnDDragGrobZuordnung();
	
	private DnDDragGrobZuordnung() {
	}

	@Override
	public boolean isDraggable(final GridElement thisElement) {
		final Zuordnung zuordnung = (Zuordnung) thisElement.getModel();
		final String userName = Client.getInstance().getCurrentUserName();
		return zuordnung.isEditableFor(userName);
	}
	
	@Override
	public boolean isSelectable(final GridElement thisElement) {
		final Zuordnung zuordnung = (Zuordnung) thisElement.getModel();
		return zuordnung != null;
	}

}

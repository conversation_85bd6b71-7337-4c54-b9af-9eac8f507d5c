package ch.eisenring.dispo.client.action;

import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.client.resource.EMailTemplates;
import ch.eisenring.dispo.shared.model.Auftrag;

public class ActionMeldungFormNotifySC extends ActionMeldungAbstract {

	public ActionMeldungFormNotifySC(final Client client, final Auftrag auftrag, Object ... models) {
		super(client, EMailTemplates.EMAIL_FORM_NOTIFY_SC, 
				join(auftrag, auftrag.getChefMonteur(), models));
	}
	
}

package ch.eisenring.dispo.client.gui.employee;

import heag.dsp.pub.codetables.BewertungKategorieCode;
import heag.dsp.pub.model.DSPMontageBewertung;

import org.jfree.data.category.DefaultCategoryDataset;

import ch.eisenring.core.codetable.RatingCode;
import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.Lookup;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.collections.impl.HashMap;
import ch.eisenring.core.datatypes.primitives.Primitives;

/**
 * Helper class for counting MitarbeiterBewertungModel
 */
public class BewertungCategoryAccumulator {

	/**
	 * Codes accumulated by this accumulator
	 */
	private final static List<RatingCode> ACCUMULATED_CODES; static {
		final List<RatingCode> l = new ArrayList<>(4);
		l.add(RatingCode.C00);
		l.add(RatingCode.C05);
		l.add(RatingCode.C10);
		l.add(RatingCode.C15);
		ACCUMULATED_CODES = l;
	}
	
	/**
	 * Category counted by this accumulator
	 */
	private final BewertungKategorieCode category;

	private final Lookup<RatingCode, Integer> map = new HashMap<>();
	private int numberOfValues;
	
	public BewertungCategoryAccumulator(final BewertungKategorieCode category) {
		this.category = category;
	}

	public void add(final DSPMontageBewertung bewertung) {
		final RatingCode code = bewertung.getBewertung(category);
		if (ACCUMULATED_CODES.contains(code)) {
			Integer count = map.get(code);
			if (count == null) {
				count = Primitives.INT0;
			}
			count = Integer.valueOf(count.intValue() + 1);
			map.put(code, count);
			++numberOfValues;
		}
	}

	public void addToDataset(final DefaultCategoryDataset dataSet) {
		final String category = AbstractCode.getLongText(this.category, "???");
		for (final RatingCode code : ACCUMULATED_CODES) {
			Integer value = map.get(code);
			dataSet.addValue(value == null
					? Double.NaN : value.doubleValue(),
					category, code.getShortText());
		}
	}

	public void addToDatasetNormalized(final DefaultCategoryDataset dataSet, final double scaleFactor) {
		final String category = AbstractCode.getLongText(this.category, "???");
		for (final RatingCode code : ACCUMULATED_CODES) {
			final Integer value = map.get(code);
			final double doubleValue;
			if (value == null) {
				doubleValue = Double.NaN;
			} else {
				doubleValue = (value.doubleValue() * scaleFactor) / numberOfValues;
			}
			dataSet.addValue(doubleValue, category, code.getShortText());
		}
	}

	// --------------------------------------------------------------
	// ---
	// ---
	// ---
	// --------------------------------------------------------------
}

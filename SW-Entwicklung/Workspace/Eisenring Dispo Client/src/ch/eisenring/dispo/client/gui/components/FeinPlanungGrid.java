package ch.eisenring.dispo.client.gui.components;

import static ch.eisenring.dispo.client.DSPClientConstants.SHOWN_EMPLOYEES;
import static ch.eisenring.dispo.shared.codetables.EmploymentRole.MITARBEITER_MONTEUR;

import java.util.Collections;
import java.util.Comparator;
import java.util.Iterator;

import ch.eisenring.core.collections.List;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.client.favorites.EmployeeFavorites;
import ch.eisenring.dispo.client.gui.grid.BlockElement;
import ch.eisenring.dispo.client.gui.grid.LineElement;
import ch.eisenring.dispo.client.logic.FeinPlanungLine;
import ch.eisenring.dispo.client.logic.FeinZuordnungBlock;
import ch.eisenring.dispo.shared.codetables.AnsichtCode;
import ch.eisenring.dispo.shared.model.AbstractMitarbeiter;
import ch.eisenring.dispo.shared.model.FeinZuordnung;
import ch.eisenring.dispo.shared.model.ModelContext;

/**
 * Raster class for Disposition 
 */
@SuppressWarnings("serial")
public class FeinPlanungGrid extends MitarbeiterGrid {

	public FeinPlanungGrid(final Client client) {
		super(client);
		getGridLayout().setLabelTitle("Mitarbeiter");
	}

	@Override
	protected void newModelSet(final ModelContext context) {
		context.obtain();
		try {
			final EmployeeFavorites favorites = SHOWN_EMPLOYEES.get();
			final List<AbstractMitarbeiter> mitarbeiterList = getMitarbeiter(context);
			for (int i=mitarbeiterList.size()-1; i>=0; --i) {
				final AbstractMitarbeiter mitarbeiter = mitarbeiterList.get(i);
				if (MITARBEITER_MONTEUR.equals(mitarbeiter.getEmploymentRole()))
					if (mitarbeiter.getEmployed())
						if (favorites.isFavorite(client, mitarbeiter))
							continue;
				mitarbeiterList.remove(i);
			}
			// sort list
			final Comparator<AbstractMitarbeiter> ordering;
			if (AnsichtCode.ABRECHNUNG.equals(client.ANSICHT.get())) {
				ordering = AbstractMitarbeiter.Order.AbrechnungDisplayOrder;
			} else {
				ordering = AbstractMitarbeiter.Order.StandardDisplayOrder;
			}
			Collections.sort(mitarbeiterList, ordering);
			
			for (int i=0, j=mitarbeiterList.size(); i<j; ++i)
				addMonteur(mitarbeiterList.get(i));
		} finally {
			context.release();
		}
	}
	
	private void addMonteur(AbstractMitarbeiter mitarbeiter) {
		final ModelContext context = mitarbeiter.getContext();
		final LineElement line = new FeinPlanungLine(mitarbeiter);
		addGridElement(line);
		// block elements for employee
		final Iterator<FeinZuordnung> j = mitarbeiter.newFeinZuordnungIterator();
		while (j.hasNext()) {
			final FeinZuordnung zu = j.next();
			final BlockElement block = new FeinZuordnungBlock(zu);
			line.addBlock(block);
			block.setDateRange(context, zu);
		}
	}

}

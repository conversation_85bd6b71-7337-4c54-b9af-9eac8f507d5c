package ch.eisenring.dispo.client.gui.components.combo;

import java.awt.Image;

import ch.eisenring.core.collections.Collection;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.csv.CSVFile;
import ch.eisenring.core.csv.CSVLine;
import ch.eisenring.core.csv.CSVReader;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.resource.ImageResource;
import ch.eisenring.core.resource.image.ImageImageResource;
import ch.eisenring.dispo.client.resource.ImageLoader;
import ch.eisenring.dispo.client.resource.imgnew.Images;
import ch.eisenring.gui.components.EditableStringCombo;

@SuppressWarnings("serial")
public class FahrzeugCombo extends EditableStringCombo {

	private final static Object LOCK = new Object();

	public final static String TYPE_FAHRZEUG = "T";
	public final static String TYPE_ANHAENGER = "A";

	private final static ArrayList<Fahrzeug> LIST = new ArrayList<Fahrzeug>();
	private static boolean dataRead;
	
	public static class Fahrzeug {
		public final String typ;
		public final String bezeichnung;
		public final String itemLabel;
		public final String comboLabel;
		public final ImageResource icon;

		public Fahrzeug(final String typ, final String number, final String bezeichnung, final String label, final String farbe) {
			if (TYPE_FAHRZEUG.equalsIgnoreCase(typ)) {
				this.typ = TYPE_FAHRZEUG;
			} else if (TYPE_ANHAENGER.equalsIgnoreCase(typ)) {
				this.typ = TYPE_ANHAENGER;
			} else {
				this.typ = "";
			}
			this.bezeichnung = Strings.trim(bezeichnung);
			this.itemLabel = Strings.trim(label);
			this.comboLabel = Strings.trim(Strings.concat(number, " ", bezeichnung)); 

			final Image image = ImageLoader.getImage(Strings.concat("images/lorry_", farbe, ".png")); 
			if (image == null) {
				icon = Images.FA_ALLGEMEIN;
			} else {
				icon = new ImageImageResource(image);
			}
		}
		
		public boolean isOfType(final String type) {
			return type == null ? false : type.equalsIgnoreCase(this.typ);
		}
	}

	private static boolean loadFahrzeuge(final String fileName) {
		synchronized (LOCK) {
			final CSVFile csvFile = CSVReader.readCSV(fileName, ';');
			if (csvFile != null) {
				int rowIndex = 0;
				while (++rowIndex < csvFile.getLineCount()) {
					final CSVLine line = csvFile.getLine(rowIndex);
					if (line.getColumnCount() < 5)
						continue;
					final Fahrzeug fahrzeug = new Fahrzeug(
							line.getColumn(0), line.getColumn(1),
							line.getColumn(2), line.getColumn(3), line.getColumn(4));
					if (null != Strings.clean(fahrzeug.typ)) {
						LIST.add(fahrzeug);
					}
				}
				return true;
			}
			return false;
		}
	}

	public static Collection<Fahrzeug> getFahrzeuge(final String type) {
		final ArrayList<Fahrzeug> result = new ArrayList<Fahrzeug>();
		synchronized (LOCK) {
			if (!dataRead) {
				dataRead = loadFahrzeuge("../data/data-files/Fahrzeuge.csv");
			}
			for (Fahrzeug fahrzeug : LIST) {
				if (fahrzeug.isOfType(type)) {
					result.add(fahrzeug);
				}
			}
		}
		return result;
	}
	
	public static Collection<String> getFahrzeugComboLabels(final String type) {
		final ArrayList<String> result = new ArrayList<String>();
		result.add(NULL_ENTRY);
		for (Fahrzeug fahrzeug : getFahrzeuge(type)) {
			result.add(fahrzeug.comboLabel);
		}
		return result;
	}
	
	public FahrzeugCombo() {
		this(TYPE_FAHRZEUG);
	}
	
	protected FahrzeugCombo(final String type) {
		super(getFahrzeugComboLabels(type));
	}
	
}

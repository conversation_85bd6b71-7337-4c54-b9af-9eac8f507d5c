package ch.eisenring.dispo.client.gui.report.mt;

import java.awt.GridBagLayout;
import java.util.Calendar;
import java.util.Date;

import ch.eisenring.core.datatypes.date.HEAGCalendar;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.shared.network.packets.PacketMTAuswertungRequest;
import ch.eisenring.gui.GridBagConstraints;
import ch.eisenring.gui.components.HEAGDateField;
import ch.eisenring.gui.components.HEAGLabel;
import ch.eisenring.gui.components.HEAGPanel;
import ch.eisenring.gui.model.StoreResults;
import ch.eisenring.gui.model.ValidationResults;

@SuppressWarnings("serial")
public class MTAuswertungPanel extends HEAGPanel {

	protected final Client client;
	protected final HEAGDateField datFrom = new HEAGDateField();
	protected final HEAGDateField datUpto = new HEAGDateField();

	public MTAuswertungPanel(final Client client) {
		this.client = client;
		initComponents();
		initLayout();
	}

	private void initComponents() {
		setLayout(new GridBagLayout());
		final HEAGCalendar c = HEAGCalendar.obtainNow();
		c.set(Calendar.MILLISECOND, 0);
		c.set(Calendar.SECOND, 0);
		c.set(Calendar.MINUTE, 0);
		c.set(Calendar.HOUR_OF_DAY, 0);
		c.set(Calendar.DAY_OF_MONTH, 1);
		datFrom.setDate(c.getTime());
		c.add(Calendar.MONTH, 1);
		c.add(Calendar.DAY_OF_MONTH, -1);
		datUpto.setDate(c.getTime());
		c.release();
	}

	private void initLayout() {
		removeAll();

		add(new HEAGLabel("Von"), GridBagConstraints.label(0, 0));
		add(datFrom, GridBagConstraints.fixed(1, 0));
		add(new HEAGLabel("   bis"), GridBagConstraints.label(2, 0));
		add(datUpto, GridBagConstraints.fixed(3, 0));
	}

	private PacketMTAuswertungRequest getRequest() {
		final Date dateFrom = datFrom.getDate();
		final Date dateUpto = datUpto.getDate();
		final PacketMTAuswertungRequest packet = PacketMTAuswertungRequest.create(dateFrom, dateUpto);
		return packet;
	}

	// --------------------------------------------------------------
	// ---
	// --- View implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public void validateView(final ValidationResults results, final Object model) {
		super.validateView(results, model);
		final Date from = datFrom.getDate();
		final Date upto = datUpto.getDate(); 
		if (from == null)
			results.add("Von-Datum fehlt", datFrom);
		if (upto == null)
			results.add("Bis-Datum fehlt", datUpto);
		if (from != null && upto != null && from.after(upto))
			results.add("Das Von-Datum darf nicht hinter dem Bis-Datum liegen", datFrom);
	}

	@Override
	public void storeView(final StoreResults results, final Object model) {
		super.storeView(results, model);
		if (!results.isSuccess())
			return;
		final PacketMTAuswertungRequest request = getRequest();
		client.sendPacket(request, false); 
	}

}

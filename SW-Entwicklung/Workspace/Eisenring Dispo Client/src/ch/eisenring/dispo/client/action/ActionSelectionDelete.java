package ch.eisenring.dispo.client.action;

import java.awt.Component;

import javax.swing.JOptionPane;

import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.Set;
import ch.eisenring.core.collections.impl.HashSet;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.client.gui.dnd.DnDSelection;
import ch.eisenring.dispo.client.gui.grid.BlockElement;
import ch.eisenring.dispo.client.gui.grid.Grid;
import ch.eisenring.dispo.client.gui.grid.GridElement;
import ch.eisenring.dispo.client.gui.grid.LineElement;
import ch.eisenring.dispo.shared.model.AbstractModel;
import ch.eisenring.dispo.shared.model.ModelContext;
import ch.eisenring.gui.resource.images.Images;

public final class ActionSelectionDelete extends ActionSelectionBase {

	public ActionSelectionDelete(final Client client, final DnDSelection selection) {
		super(client, selection, Images.DELETE, "_Löschen");
	}

	@Override
	protected void performSelectionAction(final DnDSelection selection) {
		if (!canDelete(client, getSelection()))
			return;

		if (!client.NO_ASK_CONFIRMATION.get()) {
			Component c = client.getMainWindow();
			
			int option = JOptionPane.showOptionDialog(c,
					"Wollen Sie die ausgewählten Elemente wirklich löschen?",
					"Sicherheitsabfrage", JOptionPane.YES_NO_OPTION,
					JOptionPane.WARNING_MESSAGE, null, 
					new String[] { "Ja", "Nein" }, "Nein");

			// cancel the action if user choose anything but YES
			if (JOptionPane.YES_OPTION != option)
				return;
		}
		
		// remove the grid element(s)
		final Set<Grid> alteredGrids = new HashSet<>();
		final List<GridElement> elements = getSelectedElements(client, selection);
		for (final GridElement element : elements) {
			final AbstractModel model = element.getModel();
			final BlockElement block = (BlockElement) element;
			final LineElement line = block.getLine();
			if (line == null || model == null)
				continue;
			final ModelContext context = model.getContext();
			context.obtain();
			try {
				// mark the model as deleted
				model.delete();
				line.removeBlock(block);
			} finally {
				context.release();
			}
			alteredGrids.add(element.getGrid());
		}
		// make sure the grids are repainted after removing the elements
		for (final Grid grid : alteredGrids) {
			grid.forceRepaint();
		}
	}

	@Override
	protected boolean isEnabledImpl() {
		return super.isEnabledImpl() && canDelete(client, getSelection());
	}

	public static boolean canDelete(final Client client, final DnDSelection selection) {
		if (selection == null || selection.isEmpty())
			return false;
		boolean result = true;
		final List<GridElement> elements = getSelectedElements(client, selection);
		for (final GridElement element : elements) {
			result &= ActionElementDelete.canDelete(client, element);
		}
		return result;
	}

}

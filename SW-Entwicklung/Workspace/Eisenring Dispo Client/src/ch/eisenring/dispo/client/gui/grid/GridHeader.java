package ch.eisenring.dispo.client.gui.grid;

import java.awt.Dimension;
import java.awt.Graphics2D;

import ch.eisenring.dispo.client.gui.components.CanvasBase;

@SuppressWarnings("serial")
public class GridHeader extends CanvasBase {
	
	private final GridBase grid;
	
	public GridHeader(GridBase dispoRaster) {
		this.grid = dispoRaster;
		setWidth(500);
	}

	public void setWidth(int width) {
		final DSPGridLayout layout = getGridLayout();
		final int height = layout == null ? 1 : layout.getHeaderHeight();
		final Dimension d = new Dimension(width, height);
		setMinimumSize(d);
		setPreferredSize(d);
		setMaximumSize(d);
		setSize(d);
	}

	@Override
	protected void renderContent(final Graphics2D g) {
		if (g == null)
			return;
		final DSPGridLayout layout = getGridLayout();
		layout.getLook().paintGridHeader(g, layout, grid);
	}

	public DSPGridLayout getGridLayout() {
		return this.grid.getGridLayout();
	}

}

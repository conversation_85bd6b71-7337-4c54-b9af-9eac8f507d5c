package ch.eisenring.dispo.client.gui.employee;

import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.client.action.AbstractDSPAction;
import ch.eisenring.dispo.client.gui.IModelPanel;
import ch.eisenring.dispo.shared.model.AbstractModel;
import ch.eisenring.dispo.shared.model.MasterMitarbeiter;
import ch.eisenring.dispo.shared.model.ModelContext;
import ch.eisenring.gui.components.HEAGCheckBox;
import ch.eisenring.gui.components.HEAGPanel;
import ch.eisenring.gui.components.HEAGTable;
import ch.eisenring.gui.util.GUIUtil;
import ch.eisenring.gui.util.LayoutUtil;

import javax.swing.*;
import javax.swing.event.DocumentEvent;
import javax.swing.event.DocumentListener;
import javax.swing.event.ListSelectionEvent;
import javax.swing.event.ListSelectionListener;
import javax.swing.table.TableRowSorter;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.MouseEvent;

@SuppressWarnings("serial")
public class EmployeeVerwaltungPanel extends HEAGPanel implements IModelPanel {

	private EmployeeTableModel tableModel;

	private final HEAGTable table = new HEAGTable() {
		protected void rowDoubleClicked(final int rowIndex, final Object rowObject, final MouseEvent event) {
			final MasterMitarbeiter employee = tableModel.getRow(table.convertRowIndexToModel(rowIndex));
			setSelectedEmployee(employee);
			editActionListener.actionPerformed(null);
		}
	};

	private final ListSelectionListener selectionListener = new ListSelectionListener() {
		@Override
		public void valueChanged(final ListSelectionEvent event) {
			setSelectedEmployee(tableModel.getRow(table.convertRowIndexToModel(table.getSelectedRow())));
		}
	};

	private final ActionListener editActionListener = new ActionListener() {
		@Override
		public void actionPerformed(final ActionEvent event) {
			if (selectedEmployee != null && selectedEmployee.canEdit()) {
				doEditDialog(selectedEmployee);
			}
		}
	};

	private final ActionListener addActionListener = new ActionListener() {
		@Override
		public void actionPerformed(final ActionEvent event) {
			doEditDialog(null);
		}
	};

	private final ActionListener showActionListener = new ActionListener() {
		@Override
		public void actionPerformed(final ActionEvent event) {
			GUIUtil.invokeLater(() -> setContext(context));
		}
	};

	private final ActionListener deleteActionListener = new ActionListener() {
		@Override
		public void actionPerformed(final ActionEvent event) {
			if (selectedEmployee != null && selectedEmployee.canDelete()) {
				if (AbstractDSPAction.askConfirmation(client, "Diesen Mitarbeiter wirklich löschen?")) {
					final ModelContext context = selectedEmployee.getContext();
					context.obtain();
					try {
						selectedEmployee.delete();
						client.sendModelChanges(context);
					} finally {
						context.release();
					}
					setContext(context);
				}
			}
		}
	};

	private final JButton btnAdd = new JButton("Neu...");
	private final JButton btnEdit = new JButton("Bearbeiten...");
	private final JButton btnDelete = new JButton("Löschen...");
	private final HEAGCheckBox ckbShowInternal = new HEAGCheckBox("Interne Mitarbeiter anzeigen");
	private final JTextField searchFilter = new JTextField();

	private MasterMitarbeiter selectedEmployee;
	private ModelContext context;
	public final Client client;

	public EmployeeVerwaltungPanel(final Client client) {
		this.client = client;
		initComponents();
		initLayout();
		setSelectedEmployee(null);
	}

	private void initComponents() {
		btnAdd.setToolTipText("Öffnet den Dialog zur Eingabe eines neuen Mitarbeiters");
		btnEdit.setToolTipText("<html>Öffnet den gerade ausgewählten Mitarbeiter zum Bearbeiten<br>(Interne Mitarbeiter aus Presento können nicht bearbeitet werden)");
		btnDelete.setToolTipText("<html>Löscht den ausgewählten Mitarbeiter<br>" +
				"<br>" +
				"Man kann nur Mitarbeiter löschen, die nicht <i>Intern (aus Presento importiert)</i><br>" +
				"sind, und der Mitarbeiter darf keine Beschäftigungsperioden haben (die Liste<br>" +
				"im Reiter Ein-/Austritt muss komplett leer sein)");
		btnAdd.addActionListener(addActionListener);
		btnEdit.addActionListener(editActionListener);
		btnDelete.addActionListener(deleteActionListener);
		ckbShowInternal.addActionListener(showActionListener);

		table.setAutoCreateRowSorter(true);
		searchFilter.getDocument().addDocumentListener(new DocumentListener() {
			@Override
			public void insertUpdate(DocumentEvent e) {
				filterAction();
			}

			@Override
			public void changedUpdate(DocumentEvent e) {
				filterAction();
			}

			@Override
			public void removeUpdate(DocumentEvent e) {
				filterAction();
			}
		});
	}

	private void filterAction() {
		if (table.getRowSorter() instanceof TableRowSorter) {
			String regex = new StringBuilder().append("(?i)").append(searchFilter.getText()
					.replaceAll("[eéè]", "[eéè]") //
					.replaceAll("[ijy]", "[ijy]") //
					.replaceAll("[oôò]", "[oôò]") //
					.replaceAll("\\s+", ".*") //
			).toString();
			((TableRowSorter) table.getRowSorter()).setRowFilter(RowFilter.regexFilter(regex));
		}
	}

	private void initLayout() {
		GUIUtil.makeSameSize(btnAdd, btnEdit, btnDelete);
		final LayoutUtil l = new LayoutUtil(6);
		removeAll();
		setLayout(new GridBagLayout());
		add(btnAdd, l.button());
		add(btnEdit, l.button());
		add(btnDelete, l.button());
		add(ckbShowInternal, l.label());
		add(new JLabel("Suchen:"), l.label());
		add(searchFilter, l.field(1));
		add(createScrollPane(table), l.area());
	}

	private JScrollPane createScrollPane(Component component) {
		return new JScrollPane(component, JScrollPane.VERTICAL_SCROLLBAR_ALWAYS, JScrollPane.HORIZONTAL_SCROLLBAR_AS_NEEDED);
	}

	public void setContext(final ModelContext context) {
		table.getSelectionModel().removeListSelectionListener(selectionListener);
		setSelectedEmployee(null);
		this.context = context;
		tableModel = new EmployeeTableModel(context, ckbShowInternal.isSelected());
		table.setModel(tableModel);
		tableModel.applyLayout(table);
		table.getSelectionModel().addListSelectionListener(selectionListener);
		filterAction();
	}

	protected void setSelectedEmployee(final MasterMitarbeiter employee) {
		this.selectedEmployee = employee;
		btnEdit.setEnabled((employee != null) && employee.canEdit());
		btnDelete.setEnabled((employee != null) && employee.canDelete());
	}

	/**
	 * Open edit dialog for Employee
	 */
	protected void doEditDialog(MasterMitarbeiter employee) {
		final EmployeeEditDialog dialog = new EmployeeEditDialog(client, this);
		boolean isNew = false;
		if (employee == null) {
			employee = MasterMitarbeiter.create(context);
			isNew = true;
		}
		dialog.doDialog(employee, isNew);
	}

	/****************************************************************
	 * IModelPanel implementation
	 ****************************************************************/
	@Override
	public boolean isEditable() {
		return true;
	}

	@Override
	public void setEditable(boolean editable) {
		// NO-OP
	}

	@Override
	public void setModel(AbstractModel model) {
		// NO-OP
	}

	@Override
	public void updateModel() {
		// NO-OP
	}

	@Override
	public void updateView() {
		setContext(context);
	}

}

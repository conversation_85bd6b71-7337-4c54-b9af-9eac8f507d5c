package ch.eisenring.dispo.client.action;

import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.client.gui.gu.GUUebersichtDialog;
import ch.eisenring.dispo.client.resource.imgnew.Images;

public class ActionGUUebersicht extends AbstractDSPAction {

	public ActionGUUebersicht(final Client client) {
		super(client, Images.XLS, "GU-Übersicht...");
	}

	@Override
	protected void performAction() {
		GUUebersichtDialog.showDialog(client);
	}

}

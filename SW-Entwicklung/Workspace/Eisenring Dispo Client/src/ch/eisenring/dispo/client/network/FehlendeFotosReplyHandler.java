package ch.eisenring.dispo.client.network;

import ch.eisenring.core.util.file.FileImage;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.shared.network.packets.PacketFehlendeFotosReply;

final class FehlendeFotosReplyHandler extends AbstractShowFileHandler<PacketFehlendeFotosReply> {

	FehlendeFotosReplyHandler(final Client client) {
		super(client, PacketFehlendeFotosReply.class);
	}

	@Override
	protected FileImage getFileImage(final PacketFehlendeFotosReply packet) {
		return packet.getFileImage();
	}

}

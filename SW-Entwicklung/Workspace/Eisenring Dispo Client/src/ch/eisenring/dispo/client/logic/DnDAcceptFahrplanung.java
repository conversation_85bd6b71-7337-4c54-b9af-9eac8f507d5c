package ch.eisenring.dispo.client.logic;

import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.client.gui.grid.GridElement;
import ch.eisenring.dispo.shared.model.AbstractModel;
import ch.eisenring.dispo.shared.model.FeinZuordnung;
import ch.eisenring.dispo.shared.model.GrobZuordnung;
import ch.eisenring.dispo.shared.model.Mitarbeiter;

/**
 * Drop accept hint for FahrPlanung line elements. 
 */
public final class DnDAcceptFahrplanung extends DnDAcceptLine {

	public final static DnDAcceptFahrplanung INSTANCE = new DnDAcceptFahrplanung(Client.getInstance());
	
	private DnDAcceptFahrplanung(final Client client) {
		super(client);
	}

	@Override
	public boolean isAcceptable(GridElement thisElement, GridElement dropElement) {
		// FahrPlanung accepts both FeinZuordnung and GrobZuordnung
		// as well as new FahrAuftrag elements as a drop.
		boolean result = false;
		final AbstractModel thisModel = thisElement.getModel();
		final AbstractModel dropModel = dropElement.getModel();
		if (thisModel instanceof Mitarbeiter) {
			final Mitarbeiter mitarbeiter = (Mitarbeiter) thisModel;
			if (dropModel instanceof FeinZuordnung) {
				result = true;
			} else if (dropModel instanceof GrobZuordnung) {
				result = true;
			} else if (dropElement instanceof FahrAuftragNeuBlock) {
				result = true;
			} else {
				result = true;
			}
			result &= client.isDisponierbar(mitarbeiter);
		}
		return result;
	}

}

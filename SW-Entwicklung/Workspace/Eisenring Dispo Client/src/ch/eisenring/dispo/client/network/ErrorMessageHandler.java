package ch.eisenring.dispo.client.network;

import ch.eisenring.dispo.client.Client;
import ch.eisenring.gui.util.GUIUtil;
import ch.eisenring.network.PacketSink;
import ch.eisenring.network.implementation.PacketDispatchMode;
import ch.eisenring.network.packet.AbstractPacket;
import ch.eisenring.network.packet.PacketErrorMessage;

public final class ErrorMessageHandler extends AbstractDSPPacketHandler {

	ErrorMessageHandler(final Client client) {
		super(client, PacketErrorMessage.class, PacketDispatchMode.EDT_ASYNCHRONOUS);
	}

	@Override
	public void handle(final AbstractPacket abstractPacket, final PacketSink sink) {
		GUIUtil.invokeLater(new Runnable() {
			@Override
			public void run() {
				final PacketErrorMessage packet = (PacketErrorMessage) abstractPacket;
				client.message(packet.getMessage());
			}
		});
	}

}

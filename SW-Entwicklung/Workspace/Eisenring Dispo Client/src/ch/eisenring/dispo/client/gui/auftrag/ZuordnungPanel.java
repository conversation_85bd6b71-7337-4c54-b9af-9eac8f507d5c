package ch.eisenring.dispo.client.gui.auftrag;

import static ch.eisenring.dispo.shared.codetables.AbrufModus.ABRUFEN;
import static ch.eisenring.dispo.shared.codetables.AbrufModus.VERSCHIEBEN;
import static ch.eisenring.dispo.shared.codetables.EmploymentRole.MITARBEITER_CHAUFEUR;
import static ch.eisenring.dispo.shared.codetables.ZuordnungCode.ZUORDNUNG_FAHRAUFTRAG;

import java.awt.GridBagLayout;
import java.awt.Insets;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.InputEvent;

import javax.swing.JPopupMenu;

import ch.eisenring.app.client.fileassoc.ProtocolURL;
import ch.eisenring.app.shared.network.RPCContext;
import ch.eisenring.app.shared.network.RPCHandler;
import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.client.gui.abruf.SelectOrdersDialog;
import ch.eisenring.dispo.client.gui.abruf.SelectOrdersListModel;
import ch.eisenring.dispo.client.gui.components.combo.AnhaengerCombo;
import ch.eisenring.dispo.client.gui.components.combo.CustomComboBoxes;
import ch.eisenring.dispo.client.gui.components.combo.FahrzeugCombo;
import ch.eisenring.dispo.client.gui.components.dialog.AbstractDSPDialog;
import ch.eisenring.dispo.client.gui.components.dialog.OldAbstractDialog;
import ch.eisenring.dispo.client.network.RPCAdapter;
import ch.eisenring.dispo.client.resource.imgnew.Images;
import ch.eisenring.dispo.shared.codetables.ColorCode;
import ch.eisenring.dispo.shared.codetables.DSPRightCode;
import ch.eisenring.dispo.shared.model.AbstractModel;
import ch.eisenring.dispo.shared.model.AbstractZuordnung;
import ch.eisenring.dispo.shared.model.Auftrag;
import ch.eisenring.dispo.shared.model.DSPContextCache;
import ch.eisenring.dispo.shared.model.FeinZuordnung;
import ch.eisenring.dispo.shared.model.ModelReference;
import ch.eisenring.dispo.shared.network.packets.PacketInfoReply;
import ch.eisenring.dispo.shared.network.packets.PacketInfoRequest;
import ch.eisenring.gui.GUIConstants;
import ch.eisenring.gui.GridBagConstraints;
import ch.eisenring.gui.action.AbstractAction;
import ch.eisenring.gui.balloontip.BalloonTipManager;
import ch.eisenring.gui.components.EditableStringCombo;
import ch.eisenring.gui.components.HEAGButton;
import ch.eisenring.gui.components.HEAGCodeComboBox;
import ch.eisenring.gui.components.HEAGFloatingButton;
import ch.eisenring.gui.components.HEAGLabel;
import ch.eisenring.gui.components.HEAGPanel;
import ch.eisenring.gui.components.HEAGTextArea;
import ch.eisenring.gui.components.HEAGTextField;
import ch.eisenring.logiware.LWProjektKey;

@SuppressWarnings("serial")
public final class ZuordnungPanel extends HEAGPanel {

	private final ActionListener makeVisibleListener = new ActionListener() {
		@Override
		public void actionPerformed(final ActionEvent event) {
			if (0!=(InputEvent.ALT_DOWN_MASK & event.getModifiersEx())) {
			} else {
				final ZuordnungLocator locator = new ZuordnungLocator(client);
				locator.makeVisible(getZuordnung());
			}
		}
	};
	
	private final AbstractAction actionAbruf = new AbstractAction(ABRUFEN.getIcon(),
			Strings.concat(ABRUFEN, "...")) {
		@Override
		protected void performAction() {
			final String raw = txtPrjNr.getText();
			final List<String> projektnummern = SelectOrdersListModel.splitProjektnummern(raw);
			final SelectOrdersDialog dialog = new SelectOrdersDialog(client,
					projektnummern, getZuordnung().getBegin(), ABRUFEN);
			AbstractDSPDialog.showWindow(dialog);
		}
	};

	private final AbstractAction actionVerschieben = new AbstractAction(VERSCHIEBEN.getIcon(),
			Strings.concat(VERSCHIEBEN, "...")) {
		@Override
		protected void performAction() {
			final String raw = txtPrjNr.getText();
			final List<String> projektnummern = SelectOrdersListModel.splitProjektnummern(raw);
			final SelectOrdersDialog dialog = new SelectOrdersDialog(client,
					projektnummern, getZuordnung().getBegin(), VERSCHIEBEN);
			AbstractDSPDialog.showWindow(dialog);
		}
	};

	private final ActionListener planungInfoListener = new ActionListener() {
		@Override
		public void actionPerformed(final ActionEvent event) {
			final PacketInfoRequest request = PlanungInfoHelper.createRequest(getZuordnung());
			client.sendPacket(rpcHandler, request);
		}
	};

	private final ActionListener dmsListener = new ActionListener() {
		public void actionPerformed(final ActionEvent event) {
			try {
				final AbstractZuordnung zuordnung = getZuordnung();
				final Auftrag auftrag = zuordnung == null ? null : zuordnung.getAuftrag();
				if (auftrag != null) {
					final String projektnummer = LWProjektKey.getSignificantProjektnummer(auftrag.getProjektnummer());
					final String url = Strings.concat("EisenringDMS://Goto/Projektnummer/", projektnummer);
					ProtocolURL.openURL(client, url);
				}
			} catch (final Exception e) {
				final ErrorMessage message = new ErrorMessage(e);
				BalloonTipManager.show(btnDMS, message);
			}
		}
	};
	
	private final RPCHandler rpcHandler = new RPCAdapter() {
		@Override
		public void timeoutOccured(final RPCContext rpcContext) {
			BalloonTipManager.showError(btnPlanungInfo, "Zeitüberschreitung der Serveranfrage");
		}
		@Override
		public void replyReceived(final RPCContext rpcContext) {
			final Client client = (Client) rpcContext.getComponent();
			final PacketInfoReply response = (PacketInfoReply) rpcContext.getReply();
			final JPopupMenu popup = PlanungInfoHelper.createPopupMenu(client, response, currentInstance.dialog);
			if (popup != null) {
				popup.show(btnPlanungInfo, 0, 0);
			}
		}
	};

	private final HEAGLabel lblName = new HEAGLabel("Anzeige als");
	private final HEAGLabel lblBemerkungen = new HEAGLabel("<HTML>Bemerkungen:<BR>(nur Dispo)", false);
	private final HEAGLabel lblPrjNr = new HEAGLabel("<HTML>Projekt-Nummern:<BR>(auszuliefernde)", false);
	private final HEAGLabel lblFahrzeug = new HEAGLabel("Fahrzeug/Anhänger");
	private final HEAGTextField txtName = new HEAGTextField(30);
	private final HEAGTextArea txtBemerkungen = new HEAGTextArea(250);
	private final HEAGTextArea txtPrjNr = new HEAGTextArea(250);
	private final HEAGCodeComboBox<ColorCode> cmbColor = CustomComboBoxes.createColorComboBox();
	private final EditableStringCombo cmbFahrzeug = new FahrzeugCombo();
	private final EditableStringCombo cmbAnhaenger = new AnhaengerCombo();
	private final HEAGPanel pnlFahrzeug;
	private final HEAGFloatingButton btnMakeVisible = new HEAGFloatingButton(Images.JUMPTO, 16);
	private final HEAGFloatingButton btnPlanungInfo = new HEAGFloatingButton(Images.ABOUT, 16);
	private final HEAGFloatingButton btnCreator = new HEAGFloatingButton(Images.CREATOR, 16, false);
	private final HEAGFloatingButton btnDMS = new HEAGFloatingButton(ch.eisenring.commons.resource.images.Images.DMS, 16);
	private final HEAGButton btnAbruf = new HEAGButton(actionAbruf);
	private final HEAGButton btnVerschieben = new HEAGButton(actionVerschieben);
	
	protected final Client client;
	private final OldAbstractDialog dialog;
	private static ZuordnungPanel currentInstance;
	private ModelReference<AbstractZuordnung> zuordnungRef;
	private boolean editable = true;
	
	public ZuordnungPanel(final OldAbstractDialog dialog) {
		this.client = dialog.getClient();
		this.dialog = dialog;
		pnlFahrzeug = getFahrzeugPanel();
		initComponents();
		initLayout();
	}

	private void initComponents() {
		setLayout(new GridBagLayout());
		btnMakeVisible.setToolTipText("<html>Das zugehörige Planungselement in den sichtbaren Anzeigebereich rücken<br>(Verändert die aktuelle Planungsansicht, falls notwendig)");
		btnMakeVisible.addActionListener(makeVisibleListener);
		btnMakeVisible.setFocusable(false);
		btnPlanungInfo.setToolTipText("<html>Alle Planungen zu diesem Auftrag anzeigen<br>(Grob-, Fein- und Folgeplanungen in allen Wochen)");
		btnPlanungInfo.addActionListener(planungInfoListener);
		btnPlanungInfo.setFocusable(false);
		btnPlanungInfo.setVisible(false);
		btnDMS.setToolTipText("<html>Projektordner im DMS öffnen");
		btnDMS.addActionListener(dmsListener);
	}

	private void initLayout() {
		int y = -1;

		++y;
		add(lblName, GridBagConstraints.label(0, y));
		add(txtName, GridBagConstraints.field(1, y));
		add(cmbColor, GridBagConstraints.fixed(2, y));
		add(btnDMS, GridBagConstraints.button(3, y));
		add(btnCreator, GridBagConstraints.button(5, y));
		add(btnPlanungInfo, GridBagConstraints.button(6, y));
		add(btnMakeVisible, GridBagConstraints.button(7, y));
		// Fahrzeug / Anhänger
		++y;
		add(lblFahrzeug, GridBagConstraints.label(0, y));
		add(pnlFahrzeug, GridBagConstraints.field(1, y).gridWidthRemainder());
		
		++y;
		add(lblPrjNr, GridBagConstraints.label(0, y));
		add(txtPrjNr, GridBagConstraints.area(1, y).gridSize(GridBagConstraints.REMAINDER, 3));
		add(btnAbruf, GridBagConstraints.button(0, y + 1, GridBagConstraints.HORIZONTAL, GridBagConstraints.NORTH).weight(0, 0));
		add(btnVerschieben, GridBagConstraints.button(0, y + 2, GridBagConstraints.HORIZONTAL, GridBagConstraints.SOUTH).weight(0, 0));

		y += 3;
		add(lblBemerkungen, GridBagConstraints.label(0, y));
		add(txtBemerkungen, GridBagConstraints.area(1, y).gridWidthRemainder());
	}

	private HEAGPanel getFahrzeugPanel() {
		final HEAGPanel p = new HEAGPanel();
		p.setLayout(new GridBagLayout());
		GridBagConstraints gc;
		gc = new GridBagConstraints();
		gc.gridx = 0;
		gc.weightx = 1;
		gc.insets = new Insets(0, 0, 0, GUIConstants.HSPACE);
		gc.fill = GridBagConstraints.HORIZONTAL;
		p.add(cmbFahrzeug, gc);
		gc = new GridBagConstraints();
		gc.gridx = 1;
		gc.weightx = 1;
		gc.insets = new Insets(0, 0, 0, 0);
		gc.fill = GridBagConstraints.HORIZONTAL;
		p.add(cmbAnhaenger, gc);
		return p;
	}

	protected AbstractZuordnung getZuordnung() {
		return zuordnungRef == null ? null : zuordnungRef.getModel();
	}

	private static FeinZuordnung getFahrZuordnung(final AbstractZuordnung zuordnung) {
		if (zuordnung instanceof FeinZuordnung) {
			final FeinZuordnung fein = (FeinZuordnung) zuordnung;
			if (ZUORDNUNG_FAHRAUFTRAG.equals(fein.getType()) &&
				MITARBEITER_CHAUFEUR.equals(fein.getMitarbeiter().getEmploymentRole()))
				return fein;
		}
		return null;
	}
	
	/****************************************************************
	 * IModelPanel interface implementation
	 ****************************************************************/
	public void setModel(final AbstractModel model) {
		if (model instanceof AbstractZuordnung) {
			final AbstractZuordnung zuordnung = (AbstractZuordnung) model;
			final DSPContextCache cache = client.getContextCache();
			this.zuordnungRef = new ModelReference<AbstractZuordnung>(zuordnung, cache);
		}
		updateView(model);
	}

	@Override
	public void updateView(final Object model) {
		super.updateView(model);
		updateButtons(model);
		if (model instanceof AbstractZuordnung) {
			final AbstractZuordnung zuordnung = (AbstractZuordnung) model;
			txtBemerkungen.setText(zuordnung.getBemerkungen());
			txtName.setText(zuordnung.getLabel());
			final FeinZuordnung fz = getFahrZuordnung(zuordnung);
			boolean showExtraPrjNr = false;
			boolean showFahrzeug = fz != null;
			if (fz != null) {
				cmbFahrzeug.setSelectedString(fz.getFahrzeug());
				cmbAnhaenger.setSelectedString(fz.getAnhaenger());
				showExtraPrjNr = fz.getAuftrag() == null;
				if (showExtraPrjNr) {
					txtPrjNr.setText(fz.getPrjNrText());
				}
			}

			lblPrjNr.setVisible(showExtraPrjNr);
			txtPrjNr.setVisible(showExtraPrjNr);
			btnAbruf.setVisible(showExtraPrjNr);
			btnVerschieben.setVisible(showExtraPrjNr);
			
			lblFahrzeug.setVisible(showFahrzeug);
			pnlFahrzeug.setVisible(showFahrzeug);
			cmbColor.setVisible(zuordnung instanceof FeinZuordnung);
			cmbColor.setSelectedItem(zuordnung.getColor());
			// --- decide if "PlanungInfo" is available
			boolean enablePlanungInfo = (zuordnung.getAuftrag() != null) ||
			                            (zuordnung.getAuftragNummer() != 0);
			final String createdBy = zuordnung.getCreatedBy();
			if (Strings.isEmpty(createdBy)) {
				btnCreator.setToolTipText("Ersteller unbekannt");
			} else {
				btnCreator.setToolTipText(Strings.concat("Erstellt von: ", createdBy));
			}
			btnPlanungInfo.setVisible(enablePlanungInfo);
			currentInstance = enablePlanungInfo ? this : null;
		}
	}
	
	@Override
	public void updateModel(final Object model) {
		if (model instanceof AbstractZuordnung) {
			final AbstractZuordnung zuordnung = (AbstractZuordnung) model;
			if (editable) {
				zuordnung.setBemerkungen(txtBemerkungen.getText());
				zuordnung.setLabel(txtName.getText());
				zuordnung.setColor((ColorCode) cmbColor.getSelectedCode());
				final FeinZuordnung fz = getFahrZuordnung(zuordnung);
				if (fz != null) {
					fz.setPrjNrText(txtPrjNr.getText());
					fz.setFahrzeug(cmbFahrzeug.getSelectedString());
					fz.setAnhaenger(cmbAnhaenger.getSelectedString());
				}
			}
		}
		updateButtons(model);
	}
	
	@Override
	public void setEditable(final boolean editable) {
		this.editable = editable;
		final boolean e = DSPRightCode.BEMERKUNGEN_EDIT.isPermitted();
		txtBemerkungen.setEditable(e);
		txtName.setEditable(e);
		txtPrjNr.setEditable(e);
		cmbColor.setEnabled(e);
		cmbFahrzeug.setEnabled(e);
		cmbAnhaenger.setEnabled(e);
	}
	
	@Override
	public boolean isEditable() {
		return editable;
	}

	private void updateButtons(final Object model) {
		boolean enableDMS = false;
		if (model instanceof AbstractZuordnung) {
			final AbstractZuordnung zuordnung = (AbstractZuordnung) model;
			enableDMS = zuordnung.getAuftrag() != null;
		}
		btnDMS.setEnabled(enableDMS);
	}

}

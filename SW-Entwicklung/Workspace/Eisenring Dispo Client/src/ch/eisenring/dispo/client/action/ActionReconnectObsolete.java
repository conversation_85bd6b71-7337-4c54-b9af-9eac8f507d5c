package ch.eisenring.dispo.client.action;

import static ch.eisenring.dispo.shared.codetables.EmploymentRole.MITARBEITER_CHAUFEUR;
import static ch.eisenring.dispo.shared.codetables.EmploymentRole.MITARBEITER_MONTEUR;
import static ch.eisenring.dispo.shared.codetables.ZuordnungCode.ZUORDNUNG_FAHRAUFTRAG;
import static ch.eisenring.dispo.shared.codetables.ZuordnungCode.ZUORDNUNG_MONTAGE;
import static ch.eisenring.dispo.shared.codetables.ZuordnungCode.ZUORDNUNG_OBSOLETE;

import java.awt.event.InputEvent;
import java.awt.event.KeyEvent;

import javax.swing.KeyStroke;

import ch.eisenring.core.collections.List;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.iterators.Filter;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.shared.model.AbstractMitarbeiter;
import ch.eisenring.dispo.shared.model.AbstractModel;
import ch.eisenring.dispo.shared.model.Auftrag;
import ch.eisenring.dispo.shared.model.FeinZuordnung;
import ch.eisenring.dispo.shared.model.ModelContext;

public class ActionReconnectObsolete extends AbstractDSPAction {

	public ActionReconnectObsolete(final Client client) {
		super(client, KeyStroke.getKeyStroke(KeyEvent.VK_U, InputEvent.CTRL_DOWN_MASK | InputEvent.SHIFT_DOWN_MASK),
				"Ungültige Feinplanungen neu verbinden");
		addObservable(client.CURRENT_MODEL);
	}

	@Override
	protected void performAction() {
		final ModelContext context = client.getCurrentModel();
		if (null==context)
			return;
		context.obtain();
		try {
			if (isEnabledImpl()) {
				int reconnectCount = 0;
				// get list of all FeinZuordnung in status ZUORDNUNG_OBSOLETE
				final List<FeinZuordnung> obsoleteList = getObsoleteFeinZuordnungen(context);
				for (int i=obsoleteList.size()-1; i>=0; --i) {
					final FeinZuordnung feinZuordnung = obsoleteList.get(i);
					if (reconnect(feinZuordnung))
						++reconnectCount;
				}
				// display number of reconnected objects
				client.sendModelChanges(context);
				client.warn(Strings.concat("Es wurden ", reconnectCount, " ungültige Feinplanungen wieder mit ihren Aufträgen verbunden"));
			}
		} finally {
			context.release();
		}
	}

	@Override
	protected boolean isEnabledImpl() {
		final ModelContext context = client.getCurrentModel(); 
		if (null==context) {
			return false;
		}
		final List<FeinZuordnung> list = getObsoleteFeinZuordnungen(context);
		if (list.isEmpty())
			return false;

		// This feature is not controlled by any right, everyone can do it
		return true;
	}

	/**
	 * Gets a list of all obsolete FeinZuordnung in context
	 */
	@SuppressWarnings("unchecked")
	public static List<FeinZuordnung> getObsoleteFeinZuordnungen(ModelContext context) {
		final List result = context.getModelList(FeinZuordnung.class, new Filter<AbstractModel>() {
			@Override
			public boolean accepts(AbstractModel model) {
				if (model instanceof FeinZuordnung) {
					final FeinZuordnung z = (FeinZuordnung) model;
					return ZUORDNUNG_OBSOLETE.equals(z.getType());
				}
				return false;
			}
		});
		return result;
	}
	
	/**
	 * Reconnect FeinZuordnung
	 */
	public static boolean reconnect(final FeinZuordnung feinZuordnung) {
		boolean result = false;
		if (null!=feinZuordnung) {
			final ModelContext context = feinZuordnung.getContext();
			final int auftragNummer = feinZuordnung.getAuftragNummer();
			final Auftrag auftrag = context.getAuftragByAuftragsNummer(auftragNummer);
			final AbstractMitarbeiter mitarbeiter = feinZuordnung.getMitarbeiter();
			// if Auftrag not in this week, we can't reconnect...
			if (auftrag != null && mitarbeiter != null) {
				// depending on the type of Mitarbeiter the FeinPlanung must be changed to different types
				if (MITARBEITER_CHAUFEUR.equals(mitarbeiter.getEmploymentRole())) {
					feinZuordnung.setAuftrag(auftrag);
					feinZuordnung.setType(ZUORDNUNG_FAHRAUFTRAG);
					result = true;
				} else if (MITARBEITER_MONTEUR.equals(mitarbeiter.getEmploymentRole())) {
					feinZuordnung.setAuftrag(auftrag);
					feinZuordnung.setType(ZUORDNUNG_MONTAGE);
					result = true;
				}
			}
		}
		return result;
	}
	
	

}

package ch.eisenring.dispo.client.resource.colors;

import java.awt.Color;

import ch.eisenring.gui.util.ColorRGB;

public final class BlockColorSet {

	private final static float SATURATION_FACTOR = 0.33F;

	public final Color LIGHT;
	public final Color DARK;
	public final Color LIGHT_LOWSAT;
	public final Color DARK_LOWSAT;
	
	public BlockColorSet(final int rgb) {
		LIGHT = new Color(rgb);
		DARK = LIGHT;
		LIGHT_LOWSAT = new Color(ColorRGB.saturation(rgb, SATURATION_FACTOR));
		DARK_LOWSAT = LIGHT_LOWSAT;
	}

	public BlockColorSet(final int light, final int dark) {
		this.LIGHT = new Color(light);
		this.DARK = new Color(dark);
		this.LIGHT_LOWSAT = new Color(ColorRGB.saturation(light, SATURATION_FACTOR));
		this.DARK_LOWSAT = new Color(ColorRGB.saturation(dark, SATURATION_FACTOR));
	}

	public Color getColor(final boolean dark, final boolean saturation) {
		if (dark) {
			return saturation ? DARK_LOWSAT : DARK;
		} else {
			return saturation ? LIGHT_LOWSAT : LIGHT;
		}
	}

	public Color getColor(final boolean dark) {
		return dark ? DARK : LIGHT;
	}

}

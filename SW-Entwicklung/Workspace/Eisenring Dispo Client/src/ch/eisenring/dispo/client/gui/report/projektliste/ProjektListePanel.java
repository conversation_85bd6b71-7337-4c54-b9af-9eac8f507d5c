package ch.eisenring.dispo.client.gui.report.projektliste;

import java.awt.GridBagLayout;

import ch.eisenring.core.datatypes.date.DateGranularityCode;
import ch.eisenring.core.datatypes.date.Period;
import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.client.resource.imgnew.Images;
import ch.eisenring.dispo.shared.network.packets.PacketSCProjektListRequest;
import ch.eisenring.gui.GridBagConstraints;
import ch.eisenring.gui.components.DecorationHeader;
import ch.eisenring.gui.components.HEAGDateField;
import ch.eisenring.gui.components.HEAGLabel;
import ch.eisenring.gui.components.HEAGPanel;
import ch.eisenring.gui.model.ValidationResults;

@SuppressWarnings("serial")
public class ProjektListePanel extends HEAGPanel {

	protected final HEAGDateField datBegin = new HEAGDateField();
	protected final HEAGDateField datEnd = new HEAGDateField();

	public ProjektListePanel(final Client client) {
		initComponents();
		initLayout();
	}

	private void initComponents() {
		setLayout(new GridBagLayout());
		long begin = DateGranularityCode.QUARTER.round(System.currentTimeMillis(), -2);
		long end = DateGranularityCode.QUARTER.round(begin, 1);
		end = DateGranularityCode.DAY.round(end, -1);
		datBegin.setDate(begin);
		datEnd.setDate(end);
	}

	private void initLayout() {
		removeAll();
		
		int y = 0;
		DecorationHeader header = new DecorationHeader(
				DecorationHeader.ICON, Images.XLS,
				DecorationHeader.SIZE, DecorationHeader.MEDIUM,
				DecorationHeader.LABEL, "Projektliste",
				DecorationHeader.SEPARATOR, Boolean.TRUE);
		add(header, GridBagConstraints.decorator(0, y));

		++y;
		add(new HEAGLabel("von"), GridBagConstraints.label(0, y));
		add(datBegin, GridBagConstraints.fixed(1, y));
		add(new HEAGLabel("   bis"), GridBagConstraints.label(2, y));
		add(datEnd, GridBagConstraints.fixed(3, y));
	}

	public Period getPeriod() {
		long begin = DateGranularityCode.DAY.round(datBegin.getTimestamp(), 0);
		long end = DateGranularityCode.DAY.round(datEnd.getTimestamp(), 1);
		return Period.create(begin, end);
	}

	public PacketSCProjektListRequest getRequest() {
		final Period period = getPeriod();
		return PacketSCProjektListRequest.create(period);
	}

	@Override
	public void validateView(final ValidationResults results, final Object model) {
		super.validateView(results, model);
		final Period period = getPeriod();
		if (TimestampUtil.isNull(period.getBegin()))
			results.add("Kein Datum ausgewählt.", datBegin);
		if (TimestampUtil.isNull(period.getEnd()))
			results.add("Kein Datum ausgewählt.", datEnd);
		if (!TimestampUtil.isNull(period.getBegin()) && !TimestampUtil.isNull(period.getEnd())) {
			if (period.getEnd() < period.getBegin())
				results.add("Muss hinter 'von' liegen", datEnd);
		}
	}	

}

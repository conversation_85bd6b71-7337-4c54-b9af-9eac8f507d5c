package ch.eisenring.dispo.client.gui.monteurteam;

import java.awt.GridBagLayout;
import java.awt.event.MouseEvent;

import javax.swing.event.ListSelectionEvent;
import javax.swing.event.ListSelectionListener;

import ch.eisenring.core.collections.List;
import ch.eisenring.core.feature.FeatureLookup;
import ch.eisenring.dispo.client.action.AbstractDSPAction;
import ch.eisenring.dispo.client.cache.DSPMonteurTeamCache;
import ch.eisenring.dispo.client.resource.imgnew.Images;
import ch.eisenring.dispo.shared.pojo.DSPMonteurTeam;
import ch.eisenring.gui.GridBagConstraints;
import ch.eisenring.gui.action.AbstractAction;
import ch.eisenring.gui.components.HEAGButton;
import ch.eisenring.gui.components.HEAGPanel;
import ch.eisenring.gui.components.HEAGScrollPane;
import ch.eisenring.gui.components.HEAGTable;
import ch.eisenring.gui.util.GUIUtil;

@SuppressWarnings("serial")
public class MonteurTeamMaintainPanel extends HEAGPanel {

	final MonteurTeamMaintainDialog dialog;

	private final HEAGButton btnNew;
	private final HEAGButton btnEdit;
	private final MonteurTeamTableModel tableModel = new MonteurTeamTableModel();
	private final HEAGTable table = new HEAGTable(tableModel) {
		@Override
		protected void rowDoubleClicked(final int rowIndex,
				final Object rowObject, final MouseEvent event) {
			final DSPMonteurTeam team = tableModel.getRow(rowIndex);
			if (team != null)
				actionEdit.fire();
		}
	};
	private final HEAGScrollPane scrollPane = new HEAGScrollPane(table);

	final AbstractAction actionNew;
	final AbstractAction actionEdit;
	
	final ListSelectionListener selectionListener = new ListSelectionListener() {
		@Override
		public void valueChanged(final ListSelectionEvent event) {
			actionEdit.isEnabled();
		}
	};

	public MonteurTeamMaintainPanel(final MonteurTeamMaintainDialog dialog) {
		super(new GridBagLayout());
		this.dialog = dialog;
		this.actionNew = new AbstractDSPAction(dialog.getClient(), Images.ADD, "Neu...") {
			@Override
			protected void performAction() {
				final DSPMonteurTeam team = DSPMonteurTeam.create();
				MonteurTeamEditDialog.showDialog(client, dialog, team);				
			}
		};
		this.actionEdit = new AbstractDSPAction(dialog.getClient(), Images.EDIT, "Bearbeiten...") {
			@Override
			protected void performAction() {
				final DSPMonteurTeam team = getSelectedTeam();
				if (team != null)
					MonteurTeamEditDialog.showDialog(client, dialog, team);
			}
			
			@Override
			protected boolean isEnabledImpl() {
				return getSelectedTeam() != null;
			}
		};
		this.btnNew = new HEAGButton(actionNew);
		this.btnEdit = new HEAGButton(actionEdit);
		initComponents();
		initLayout();
		updateTeamList();
	}

	private void initComponents() {
		GUIUtil.makeSameSize(btnNew, btnEdit);
		tableModel.applyLayout(table);
		table.getSelectionModel().addListSelectionListener(selectionListener);
		actionEdit.isEnabled();
	}

	private void initLayout() {
		add(btnNew, GridBagConstraints.button(0, 0));
		add(btnEdit, GridBagConstraints.button(1, 0));
		add(scrollPane, GridBagConstraints.area(0, 1).gridWidth(3));
	}

	DSPMonteurTeam getSelectedTeam() {
		final List<DSPMonteurTeam> selection = tableModel.getSelection(table);
		return selection == null || selection.isEmpty() ? null : selection.get(0);
	}

	void updateTeamList() {
		final FeatureLookup lookup = dialog.getClient().getFeatureLookup();
		final DSPMonteurTeamCache cache = lookup.getFeature(DSPMonteurTeamCache.class);
		cache.flush();
		final List<DSPMonteurTeam> teams = cache.getTeams();
		tableModel.setContent(teams);
		actionEdit.isEnabled();
	}
	
}

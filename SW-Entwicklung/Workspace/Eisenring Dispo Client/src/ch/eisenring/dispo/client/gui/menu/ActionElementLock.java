package ch.eisenring.dispo.client.gui.menu;

import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.resource.ImageResource;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.client.action.ActionElementBase;
import ch.eisenring.dispo.client.gui.grid.BlockElement;
import ch.eisenring.dispo.client.resource.imgnew.Images;
import ch.eisenring.dispo.shared.model.AbstractModel;
import ch.eisenring.dispo.shared.modelapi.Zuordnung;

public class ActionElementLock extends ActionElementBase {

	static class State {
		public final String label;
		public final ImageResource icon;
		public final boolean enabled;
	
		public State(final String label, final ImageResource icon, final boolean enabled) {
			this.label = label;
			this.icon = icon;
			this.enabled = enabled;
		}
	}

	private final State state;

	public ActionElementLock(final Client client, final BlockElement blockElement) {
		this(client, blockElement, getState(client, blockElement));
	}

	private ActionElementLock(final Client client, final BlockElement blockElement, final State state) {
		super(client, blockElement, state.icon, state.label);
		this.state = state;
		isEnabled();
	}

	private static State getState(final Client client, final BlockElement blockElement) {
		String label;
		ImageResource icon;
		boolean enabled;
		final AbstractModel model = blockElement.getModel();
		if (model instanceof Zuordnung) {
			icon = Images.STOP;
			final Zuordnung z = (Zuordnung) model;
			final String lockedBy = z.getLockedBy();
			if (Strings.isEmpty(lockedBy)) {
				label = "Planung Sperren";
				enabled = true;
			} else if (Strings.equalsIgnoreCase(lockedBy, client.getCurrentUserName())) {
				label = "Planung Freigeben";
				enabled = true;
			} else {
				label = Strings.concat("Gesperrt von ", lockedBy);
				enabled = false;
			}
		} else {
			label = "Planung ungültig";
			icon = Images.QUESTION;
			enabled = false;
		}
		return new State(label, icon, enabled);
	}

	@Override
	protected boolean isEnabledImpl() {
		return state.enabled && super.isEnabledImpl() && getBlockElement().isEditable();
	}

	@Override
	protected void performBlockAction(final BlockElement blockElement) {
		final AbstractModel model = getModel();
		if (model instanceof Zuordnung) {
			final Zuordnung z = (Zuordnung) model;
			final String lockedBy = z.getLockedBy();
			if (Strings.isEmpty(lockedBy)) {
				z.setLockedBy(client.getCurrentUserName());
			} else {
				z.setLockedBy(null);
			}
		}
	}

}

package ch.eisenring.dispo.client.gui.auftrag.solidarhaftung;

import static ch.eisenring.email.client.resource.images.Images.EMAIL;

import java.awt.GridBagLayout;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;

import javax.swing.BorderFactory;

import ch.eisenring.app.shared.network.RPCContext;
import ch.eisenring.app.shared.network.RPCHandler;
import ch.eisenring.app.shared.network.RPCHandlerEDT;
import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.client.action.ActionOpenProjectDocument;
import ch.eisenring.dispo.client.gui.auftrag.ActionEMailPopup;
import ch.eisenring.dispo.client.gui.auftrag.AuftragDetailPanel;
import ch.eisenring.dispo.client.gui.auftrag.abrechnung.YesNoBinding;
import ch.eisenring.dispo.shared.metamodel.DSPProjektMetaClass;
import ch.eisenring.dispo.shared.model.Auftrag;
import ch.eisenring.dispo.shared.network.packets.PacketProjektGetReply;
import ch.eisenring.dispo.shared.network.packets.PacketProjektGetRequest;
import ch.eisenring.dispo.shared.pojo.DSPProjekt;
import ch.eisenring.dms.service.codetables.DMSDocumentType;
import ch.eisenring.gui.GridBagConstraints;
import ch.eisenring.gui.balloontip.BalloonTipManager;
import ch.eisenring.gui.components.HEAGButton;
import ch.eisenring.gui.components.HEAGCheckBox;
import ch.eisenring.gui.components.HEAGLabel;
import ch.eisenring.gui.components.HEAGPanel;
import ch.eisenring.logiware.LWProjektKey;
import ch.eisenring.logiware.gui.LWProjektKeyField;

@SuppressWarnings("serial")
class ObjektbetreuerProjektPanel extends AuftragDetailPanel {

	protected final LWProjektKeyField keyProjekt = new LWProjektKeyField();
	protected final HEAGCheckBox chkKuecheGemessen = new HEAGCheckBox("Küche gemessen", new YesNoBinding(DSPProjektMetaClass.ATR_KUECHE_GEMESSEN));
	protected final HEAGCheckBox chkKuecheAbgenommen = new HEAGCheckBox("Küche abgenommen", new YesNoBinding(DSPProjektMetaClass.ATR_KUECHE_ABGENOMMEN));
	protected final HEAGButton btnMail = new HEAGButton("Meldung...", EMAIL.getIcon(16));
	protected final ActionListener mailActionListener = new ActionListener() {
		@Override
		public void actionPerformed(final ActionEvent event) {
			ActionEMailPopup.showEMailPopup(client, getAuftrag(), btnMail);
		}
	};

	protected final HEAGButton btnStatusrapport = new HEAGButton("Statusrapport anzeigen");
	protected final ActionListener strActionListener = new ActionListener() {
		@Override
		public void actionPerformed(final ActionEvent event) {
			ActionOpenProjectDocument.openDocument(getAuftrag().getProjektnummer(), DMSDocumentType.STATUSRAPPORT);
		}
	};
	
	protected LWProjektKey projektKey;
	protected DSPProjekt projekt;
	
	public ObjektbetreuerProjektPanel(final Client client) {
		super(client);
		btnMail.addActionListener(mailActionListener);
		btnStatusrapport.addActionListener(strActionListener);
		initComponents();
		initLayout();
	}

	private void initComponents() {
		setBorder(BorderFactory.createTitledBorder("Kommission"));
		setLayout(new GridBagLayout());
	}

	private void initLayout() {
		int y = -1;
		
		add(new HEAGLabel("Projektauftrag"), GridBagConstraints.label(0, ++y));
		add(keyProjekt, GridBagConstraints.field(1, y));
		
		add(chkKuecheGemessen, GridBagConstraints.field(1, ++y));

		add(chkKuecheAbgenommen, GridBagConstraints.field(1, ++y));

		add(btnMail, GridBagConstraints.field(1, ++y));

		add(btnStatusrapport, GridBagConstraints.field(1, ++y));

		add(new HEAGPanel(), GridBagConstraints.area(1, ++y));
	}

	private final RPCHandler rpcHandler = new RPCHandlerEDT() {
		@Override
		public void requestSent(final RPCContext rpcContext) {
			// nothing
		}
		@Override
		public void timeoutOccured(final RPCContext rpcContext) {
			errorOccured(new ErrorMessage("Zeitüberschreitung der Serveranfrage"));
		}
		@Override
		public void replyReceived(final RPCContext rpcContext) {
			final PacketProjektGetReply reply = (PacketProjektGetReply) rpcContext.getReply();
			final ErrorMessage message = reply.getMessage();
			if (message.isSuccess()) {
				projekt = reply.getProjekt(projektKey);
				updateView(projekt);
				updateEditableImpl();
			} else {
				errorOccured(message);
			}
		};
		private void errorOccured(final ErrorMessage message) {
			client.message(message);
			BalloonTipManager.show(keyProjekt, message);
		}
	};

	// --------------------------------------------------------------
	// ---
	// --- ModelView implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public void updateView(final Object model) {
		super.updateView(model);
		if (model instanceof Auftrag) {
			final Auftrag auftrag = (Auftrag) model;
			// this panel is lazy, just remember the related project
			this.projektKey = LWProjektKey.get(auftrag.getProjektnummer(), auftrag.getGSEProjekt());
		} else if (model instanceof DSPProjekt) {
			keyProjekt.updateView(projektKey);
		}
	}

	@Override
	public void evaluateChanged(final Object model) {
		if (wasShown()) {
			super.evaluateChanged(projekt);
		}
	}

	@Override
	public boolean isViewChanged() {
		return wasShown() && super.isViewChanged();
	}
	// --------------------------------------------------------------
	// ---
	// --- HEAGPanelLazy implementation
	// ---
	// --------------------------------------------------------------
	@Override
	protected void showingFirstTimeImpl() {
		// fetch data from server
		final PacketProjektGetRequest request = PacketProjektGetRequest.create(projektKey);
		client.sendPacket(rpcHandler, request);
	}

	@Override
	protected void updateEditableImpl() {
		final boolean editable = false;
		chkKuecheGemessen.setEditable(editable);
		chkKuecheAbgenommen.setEditable(editable);
	}
	
}

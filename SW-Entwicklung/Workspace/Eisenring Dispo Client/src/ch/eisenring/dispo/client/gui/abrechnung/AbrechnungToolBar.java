package ch.eisenring.dispo.client.gui.abrechnung;

import java.awt.GridBagLayout;

import ch.eisenring.core.collections.List;
import ch.eisenring.core.observable.Observable;
import ch.eisenring.core.observable.Observer;
import ch.eisenring.core.resource.ImageResource;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.client.DSPClientConstants;
import ch.eisenring.dispo.client.gui.components.button.KWCurrImageButton;
import ch.eisenring.dispo.client.gui.components.button.KWNextImageButton;
import ch.eisenring.dispo.client.gui.components.button.KWPrevImageButton;
import ch.eisenring.dispo.client.gui.components.combo.MitarbeiterFavoritesCombo;
import ch.eisenring.dispo.client.resource.imgnew.Images;
import ch.eisenring.dispo.shared.transfer.AbrechnungProxy;
import ch.eisenring.gui.components.HEAGPanel;
import ch.eisenring.gui.components.IconButton;
import ch.eisenring.gui.util.GUIUtil;
import ch.eisenring.gui.util.LayoutUtil;

@SuppressWarnings("serial")
class AbrechnungToolBar extends HEAGPanel {

	private final Observer<Object> observer = new Observer<Object>() {
		@Override
		public void observableChanged(final Observable<Object> observable) {
			refreshTable();
		}
	};

	protected Observable<Boolean> PINNED = Observable.create(true, "PinnedResult", Boolean.FALSE);
	protected Observer<Boolean> pinnedObserver = new Observer<Boolean>() {
		@Override
		public void observableChanged(final Observable<Boolean> observable) {
			final Boolean pinned = Boolean.TRUE.equals(PINNED.get());
			final ImageResource imageResource;
			final String tooltip;
			if (pinned) {
				imageResource = Images.PIN;
				tooltip = "<html>Tabelleninhalt freigeben";
			} else {
				imageResource = Images.UNPIN;
				tooltip = "<html>Tabelleninhalt fixieren";
			}
			btnPin.setIcon(imageResource.getIcon(32));
			btnPin.setToolTipText(tooltip);
		}
	};

	protected final KWPrevImageButton btnPrev;
	protected final KWCurrImageButton btnCurr;
	protected final KWNextImageButton btnNext;
	protected final IconButton btnEMail;
	protected final IconButton btnRefresh;
	protected final IconButton btnPin;
	protected final MitarbeiterFavoritesCombo cmbMitarbeiter;
	protected final AbrechnungBasisReportPanel pnlReport;
	protected final AbrechnungWindow window;

	public AbrechnungToolBar(final AbrechnungWindow window) {
		final Client client = window.getClient();
		this.window = window;
		this.pnlReport = new AbrechnungBasisReportPanel(window);
		this.btnPrev = new KWPrevImageButton(client);
		this.btnCurr = new KWCurrImageButton(client);
		this.btnNext = new KWNextImageButton(client);
		this.btnEMail = new AbrechnungEMailButton(client, window);
		this.cmbMitarbeiter = new MitarbeiterFavoritesCombo();
		this.btnRefresh = new IconButton(Images.REFRESH.getIcon(32)) {{
				setFocusable(false);
			}
			@Override
			public void buttonClicked() {
				refreshTable();
			}
		};
		this.btnPin = new IconButton(Images.UNPIN.getIcon(32)) {{
				setFocusable(false);
			}
			public void buttonClicked() {
				final boolean pinned = !Boolean.TRUE.equals(PINNED.get());
				PINNED.set(Boolean.valueOf(pinned));
				refreshTable();
			}
		};
		initComponents();
		initLayout();
		PINNED.addObserver(pinnedObserver);
		DSPClientConstants.SHOWN_EMPLOYEES.addObserver(observer);
	}

	private void initComponents() {
		setLayout(new GridBagLayout());
		GUIUtil.makeSameSize(btnPrev, btnCurr, btnNext, btnRefresh, btnPin, btnEMail);
	}

	private void initLayout() {
		removeAll();
		final LayoutUtil l = new LayoutUtil(99);
		add(btnPrev, l.button());
		add(btnCurr, l.button());
		add(btnNext, l.button());
		add(btnRefresh, l.button());
		add(btnPin, l.button());
		add(btnEMail, l.button());
		add(cmbMitarbeiter, l.button());
		add(pnlReport, l.panel(1, 0, 0));
		add(new HEAGPanel(), l.field(1));
	}

	protected void refreshTable() {
		final boolean pinned = Boolean.TRUE.equals(PINNED.get());
		if (!pinned) {
			final AbrechnungTableModel tableModel = window.tablePanel.tableModel;
			final List<AbrechnungProxy> list = window.context.getAuftragList();
			tableModel.setContent(list);
		}
	}

}

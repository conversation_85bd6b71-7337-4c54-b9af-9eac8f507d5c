package ch.eisenring.dispo.client.gui.report.endmontagen;

import java.awt.GridBagLayout;
import java.util.Calendar;
import java.util.Date;

import ch.eisenring.core.datatypes.date.HEAGCalendar;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.client.resource.imgnew.Images;
import ch.eisenring.dispo.shared.network.packets.PacketReportEndmontagenRequest;
import ch.eisenring.gui.GridBagConstraints;
import ch.eisenring.gui.components.DecorationHeader;
import ch.eisenring.gui.components.HEAGCodeComboBox;
import ch.eisenring.gui.components.HEAGDateField;
import ch.eisenring.gui.components.HEAGIntegerField;
import ch.eisenring.gui.components.HEAGLabel;
import ch.eisenring.gui.components.HEAGPanel;
import ch.eisenring.gui.model.ValidationResults;
import ch.eisenring.gui.util.ConversionUtil;
import ch.eisenring.logiware.code.soft.LWKundenberaterCode;
import ch.eisenring.logiware.code.soft.LWObjektBetreuerCode;

@SuppressWarnings("serial")
public class ReportEndmontagenPanel extends HEAGPanel {

	protected final HEAGDateField datFrom = new HEAGDateField();
	protected final HEAGDateField datUpto = new HEAGDateField();

	protected final HEAGCodeComboBox<LWObjektBetreuerCode> cmbObjektbetreuer =
		new HEAGCodeComboBox<LWObjektBetreuerCode>(LWObjektBetreuerCode.class);
	protected final HEAGCodeComboBox<LWKundenberaterCode> cmbInnendienst =
		new HEAGCodeComboBox<LWKundenberaterCode>(LWKundenberaterCode.class);
	protected final HEAGIntegerField intVerkaeufer =
		new HEAGIntegerField(9, false);
	
	public ReportEndmontagenPanel(final Client client) {
		initComponents();
		initLayout();
	}
	
	private void initComponents() {
		setLayout(new GridBagLayout());
		final HEAGCalendar c = HEAGCalendar.obtainNow();
		c.set(Calendar.MONTH, 0);
		c.set(Calendar.DAY_OF_MONTH, 1);
		c.set(Calendar.HOUR_OF_DAY, 0);
		c.set(Calendar.MINUTE, 0);
		c.set(Calendar.SECOND, 0);
		c.set(Calendar.MILLISECOND, 0);
		datFrom.setDate(c.getTime());
		c.set(Calendar.MONTH, 11);
		c.set(Calendar.DAY_OF_MONTH, 31);
		datUpto.setDate(c.getTime());
		c.release();
	}

	private void initLayout() {
		removeAll();
		
		int y = 0;
		DecorationHeader header = new DecorationHeader(
				DecorationHeader.ICON, Images.LINEGRAPH,
				DecorationHeader.SIZE, DecorationHeader.MEDIUM,
				DecorationHeader.LABEL, "Auswertung Endmontagen",
				DecorationHeader.SEPARATOR, Boolean.TRUE);
		add(header, GridBagConstraints.decorator(0, y));

		++y;
		add(new HEAGLabel("von"), GridBagConstraints.label(0, y));
		add(datFrom, GridBagConstraints.fixed(1, y));
		add(new HEAGLabel("   bis"), GridBagConstraints.label(2, y));
		add(datUpto, GridBagConstraints.fixed(3, y));

		++y;
		add(new HEAGLabel("Innendienst"), GridBagConstraints.label(0, y));
		add(cmbInnendienst, GridBagConstraints.field(1, y).gridWidthRemainder());

		++y;
		add(new HEAGLabel("Objektbetreuer"), GridBagConstraints.label(0, y));
		add(cmbObjektbetreuer, GridBagConstraints.field(1, y).gridWidthRemainder());

		++y;
		add(new HEAGLabel("Verkäufer"), GridBagConstraints.label(0, y));
		add(intVerkaeufer, GridBagConstraints.field(1, y));
	}

	public PacketReportEndmontagenRequest getRequest() {
		final LWKundenberaterCode idCode = cmbInnendienst.getSelectedCode();
		final LWObjektBetreuerCode obCode = cmbObjektbetreuer.getSelectedCode();
		final Integer verkaeufer = ConversionUtil.convert(intVerkaeufer, Integer.class);
		final Date dateFrom = datFrom.getDate();
		final Date dateUpto = datUpto.getDate();
		return PacketReportEndmontagenRequest.create(dateFrom, dateUpto,
				obCode, idCode, verkaeufer);
	}

	@Override
	public void validateView(final ValidationResults results, final Object model) {
		super.validateView(results, model);
		final Date from = datFrom.getDate();
		if (from == null)
			results.add("Kein Datum ausgewählt.", datFrom);
		final Date upto = datUpto.getDate();
		if (upto == null)
			results.add("Kein Datum ausgewählt.", datUpto);
		if (from != null && upto != null && upto.compareTo(from) <= 0)
			results.add("Muss hinter 'von' liegen", datUpto);
	}

}

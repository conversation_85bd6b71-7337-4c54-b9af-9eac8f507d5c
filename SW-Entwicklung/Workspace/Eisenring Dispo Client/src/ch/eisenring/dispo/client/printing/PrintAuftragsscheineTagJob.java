package ch.eisenring.dispo.client.printing;

import net.sf.jasperreports.engine.JasperReport;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.datatypes.date.DateGranularityCode;
import ch.eisenring.core.tag.TagSet;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.shared.model.AbstractMitarbeiter;
import ch.eisenring.dispo.shared.model.ModelContext;
import ch.eisenring.dispo.shared.printing.MonteurTagAuftragSource;
import ch.eisenring.print.client.job.ClientPrintReportJob;
import ch.eisenring.print.shared.job.PrintTags;
import ch.eisenring.print.shared.model.PRTReportDataSource;
import ch.eisenring.print.shared.model.PrintServiceIdentifier;
import ch.eisenring.print.shared.resource.ReportResource;

public class PrintAuftragsscheineTagJob extends ClientPrintReportJob<Client> {

	private ReportResource reportResource;
	private final List<AbstractMitarbeiter> employeeList;
	private final int dayMask;
	
	public PrintAuftragsscheineTagJob(final Client client,
									  final ReportResource report,
								      final List<AbstractMitarbeiter> employeeList,
								      final int dayMask,
			                          final PrintServiceIdentifier printer,
			                          final int numCopies) {
		super(client, report, (PRTReportDataSource) null,
				new TagSet(PrintTags.PRINTER, printer,
						   PrintTags.NUM_COPIES, numCopies));
		this.reportResource = report;
		this.employeeList = employeeList;
		this.dayMask = dayMask;
	}

	@Override
	public void performJob() {
		final int numCopies = getNumCopies().getValue();
		final int dayCount = Integer.bitCount(dayMask);
		final double total = employeeList.size() * numCopies * dayCount;
			
		final List<JasperReport> reports = getReports();
		if (reports.isEmpty())
			return;

		final PrintServiceIdentifier printer = getPrinter();
		if (printer == null)
			return;

		int progress = 0;
		
		for (int pass=0; pass<numCopies; ++pass) {
			for (int i=0; i<employeeList.size(); ++i) {
				final AbstractMitarbeiter m = employeeList.get(i);
				for (int d=0; d<7; ++d) {
					if (0!=(dayMask&(1<<d))) { 
						final ModelContext context = m.getContext();
						final long dayDate = DateGranularityCode.DAY.round(context.getBegin(), d);
						final MonteurTagAuftragSource dataSource = new MonteurTagAuftragSource(m, dayDate);
						if (!dataSource.isEmpty()) {
							ClientPrintReportJob<Client> subJob = new ClientPrintReportJob<Client>(client, reportResource, dataSource,
									new TagSet(PrintTags.NUM_COPIES, 1,
											   PrintTags.PRINTER, printer));
							performSubJob(subJob);
						}
						++progress;
						setProgress(progress / total);
					}
				}
			}
		}
	}

}

package ch.eisenring.dispo.client.gui.look;

import java.awt.Font;
import java.awt.Graphics2D;

import ch.eisenring.dispo.client.DSPClientConstants;
import ch.eisenring.dispo.client.gui.grid.BlockElement;
import ch.eisenring.dispo.client.gui.grid.GridBase;
import ch.eisenring.dispo.client.gui.grid.DSPGridLayout;
import ch.eisenring.dispo.client.gui.grid.LineElement;

public interface ILook extends DSPClientConstants {

	// constants for renderers
	public final static int JUSTIFY_LEFT = -1;
	public final static int JUSTIFY_CENTER = 0;
	public final static int JUSTIFY_RIGHT = 1;
	
	public abstract void text(Graphics2D graphics, String text, int x, int y, int w, int h, int justification);

	public abstract void paintGridHeader(Graphics2D graphics, DSPGridLayout layout, GridBase grid);
	
	public abstract void paintGridBody(Graphics2D graphics, DSPGridLayout layout);

	public abstract void paintBlockElement(Graphics2D graphics, BlockElement element);
	
	public abstract void paintLineElement(Graphics2D graphics, LineElement element);
	
	public abstract void paintBlockOverlap(Graphics2D graphics, int x, int y, int w, int h);

	public abstract Font getDefaultFont();

}

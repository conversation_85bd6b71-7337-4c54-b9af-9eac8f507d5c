package ch.eisenring.dispo.client.gui.report.endmontagen;

import java.awt.GridBagLayout;

import ch.eisenring.core.collections.Set;
import ch.eisenring.core.datatypes.date.DateGranularityCode;
import ch.eisenring.core.datatypes.date.HEAGCalendar;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.client.resource.imgnew.Images;
import ch.eisenring.dispo.shared.network.packets.PacketReportVerschiebungenKOKRequest;
import ch.eisenring.gui.GridBagConstraints;
import ch.eisenring.gui.components.DecorationHeader;
import ch.eisenring.gui.components.HEAGDateField;
import ch.eisenring.gui.components.HEAGLabel;
import ch.eisenring.gui.components.HEAGPanel;
import ch.eisenring.gui.components.code.HEAGCodeSelectionField;
import ch.eisenring.gui.model.ValidationResults;
import ch.eisenring.logiware.code.pseudo.AbwicklungsartCode;
import ch.eisenring.logiware.code.soft.LWTerminCD;

@SuppressWarnings("serial")
public class ReportVerschiebungenKOKPanel extends HEAGPanel {

	protected final HEAGDateField dateControl = new HEAGDateField();
	protected final HEAGCodeSelectionField<LWTerminCD> selGruppe;
	protected final HEAGCodeSelectionField<AbwicklungsartCode> selAWA;

	public ReportVerschiebungenKOKPanel(final Client client) {
		this.selGruppe = new HEAGCodeSelectionField<>(200, null, client, LWTerminCD.class);
		this.selAWA = new HEAGCodeSelectionField<AbwicklungsartCode>(200, null, client, AbwicklungsartCode.class);
		initComponents();
		initLayout();
	}
	
	private void initComponents() {
		setLayout(new GridBagLayout());
		final HEAGCalendar c = HEAGCalendar.obtainNow();
		DateGranularityCode.KW.round(c, 0);
		dateControl.setDate(c.getTimeInMillis());
		c.release();
		selGruppe.setSelection(Set.asReadonlySet(LWTerminCD.C101));
		selAWA.setSelection(Set.asReadonlySet(AbwicklungsartCode.AWA001, AbwicklungsartCode.AWA135));
	}

	private void initLayout() {
		removeAll();
		
		int y = 0;
		DecorationHeader header = new DecorationHeader(
				DecorationHeader.ICON, Images.LINEGRAPH,
				DecorationHeader.SIZE, DecorationHeader.MEDIUM,
				DecorationHeader.LABEL, "Anzahl Verschiebungen KOK",
				DecorationHeader.SEPARATOR, Boolean.TRUE);
		add(header, GridBagConstraints.decorator(0, y));

		++y;
		add(new HEAGLabel("Auswertung-Stichtag"), GridBagConstraints.label(0, y));
		add(dateControl, GridBagConstraints.field(1, y).gridWidthRemainder());
		++y;
		add(new HEAGLabel("Termingruppe"), GridBagConstraints.label(0, y));
		add(selGruppe, GridBagConstraints.field(1, y).gridWidthRemainder());
		++y;
		add(new HEAGLabel("Abwicklungsarten"), GridBagConstraints.label(0, y));
		add(selAWA, GridBagConstraints.field(1, y).gridWidthRemainder());
	}

	public PacketReportVerschiebungenKOKRequest getRequest() {
		final long date = dateControl.getTimestamp();
		final Set<LWTerminCD> gruppen = selGruppe.getSelection();
		final Set<AbwicklungsartCode> awa = selAWA.getSelection();
		return PacketReportVerschiebungenKOKRequest.create(date, gruppen, awa);
	}

	@Override
	public void validateView(final ValidationResults results, final Object model) {
		super.validateView(results, model);
		final Set<AbwicklungsartCode> awa = selAWA.getSelection();
		if (awa.isEmpty())
			results.add("Eine oder mehrere Abwicklungsarten wählen", selAWA);
		final Set<LWTerminCD> gruppen = selGruppe.getSelection();
		if (gruppen.isEmpty())
			results.add("Eine oder mehrere Gruppen wählen", selGruppe);

	}

}

package ch.eisenring.dispo.client.logic;

import ch.eisenring.dispo.client.gui.dnd.DnDHintAccept;
import ch.eisenring.dispo.client.gui.dnd.DnDHintDrag;
import ch.eisenring.dispo.client.gui.dnd.DnDHintDrop;
import ch.eisenring.dispo.client.gui.grid.LineElement;
import ch.eisenring.dispo.shared.model.AbstractMitarbeiter;

public final class FeinPlanungLine extends LineElement {

	public FeinPlanungLine(final AbstractMitarbeiter monteur) {
		super(monteur);
	}

	@Override
	public DnDHintAccept getAcceptHint() {
		return DnDAcceptFeinPlanung.INSTANCE;
	}

	@Override
	public DnDHintDrag getDragHint() {
		return DnDDragDenied.INSTANCE;
	}

	@Override
	public DnDHintDrop getDropHint() {
		return DnDDropDenied.INSTANCE;
	}
	
	@Override
	public boolean isEditable() {
		return false;
	}

}

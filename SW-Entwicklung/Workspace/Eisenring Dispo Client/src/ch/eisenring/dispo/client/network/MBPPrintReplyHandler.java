package ch.eisenring.dispo.client.network;

import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.shared.network.packets.PacketMBPPrintReply;
import ch.eisenring.network.PacketSink;
import ch.eisenring.network.packet.AbstractPacket;

public class MB<PERSON>rintReplyHandler extends AbstractDSPPacketHandler {

	public MBPPrintReplyHandler(final Client client) {
		super(client, PacketMBPPrintReply.class);
	}

	@Override
	public void handle(final AbstractPacket packet, final PacketSink sink) {
		// nothing, must handle this via RPC
	}

}

package ch.eisenring.dispo.client.resource;

import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.dispo.shared.codetables.DSPDocumentCode;
import ch.eisenring.email.EMailTemplateResource;

public class DSPEMailTemplate extends EMailTemplateResource {

	protected final List<DSPDocumentCode> documentCodes = new ArrayList<DSPDocumentCode>();

	public DSPEMailTemplate(final String mailFilename,
			                final String blockFilename,
			                final String dialogTitle,
			                final String menuLabel,
			                final DSPDocumentCode ... documentCodes) {
		super(mailFilename, blockFilename, dialogTitle, menuLabel);
		if (documentCodes != null) {
			for (final DSPDocumentCode documentCode : documentCodes) {
				if (!AbstractCode.isNull(documentCode))
					this.documentCodes.add(documentCode);
			}
		}
		this.documentCodes.trimToSize();
	}

	public List<DSPDocumentCode> getDocumentCodes() {
		return new ArrayList<>(this.documentCodes);
	}

}

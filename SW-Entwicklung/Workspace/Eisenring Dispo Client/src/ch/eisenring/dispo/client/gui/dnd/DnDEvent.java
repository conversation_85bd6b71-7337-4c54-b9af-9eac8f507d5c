package ch.eisenring.dispo.client.gui.dnd;

import java.awt.event.InputEvent;

import ch.eisenring.dispo.client.gui.grid.GridElement;

public class DnDEvent {

	private final GridElement droppedElement;
	private GridElement targetElement;
	private int dropCellIndex = 0;
	private int dropCellSpan = 60;
	private final int modifiers;
	
	protected DnDEvent(final GridElement droppedElement, final int modifiers) {
		this.droppedElement = droppedElement;
		this.modifiers = modifiers;
	}
	
	public boolean isCTRLDown() {
		return (InputEvent.CTRL_DOWN_MASK & modifiers) != 0;
	}
	
	public boolean isSHIFTDown() {
		return (InputEvent.SHIFT_DOWN_MASK & modifiers) != 0;
	}
	
	public boolean isALTDown() {
		return (InputEvent.ALT_DOWN_MASK & modifiers) != 0;
	}

	/**
	 * Gets the cell span the dropped element should assume
	 */
	public int getDropCellSpan() {
		return this.dropCellSpan;
	}
	protected void setDropCellSpan(int dropCellSpan) {
		this.dropCellSpan = dropCellSpan;
	}
	
	
	/**
	 * Gets the cell index at which the dropped element 
	 * should be located
	 */
	public int getDropCellStart() {
		return this.dropCellIndex;
	}
	public int getDropCellEnd() {
		return dropCellIndex+dropCellSpan-1;
	}
	protected void setDropCellStart(int dropCellIndex) {
		this.dropCellIndex = dropCellIndex;
	}

	/**
	 * Gets the element which has been dropped
	 */
	public GridElement getDroppedElement() {
		return this.droppedElement;
	}

	/**
	 * Gets the element dropped onto
	 */
	public GridElement getTargetElement() {
		return this.targetElement;
	}
	protected void setTargetElement(GridElement targetElement) {
		this.targetElement = targetElement;
	}
	
	/**
	 * Gets the drop delegate of the target element
	 */
	public GridElement getTargetDropDelegate() {
		return targetElement.getAcceptHint().getDropDelegate(targetElement);
	}
	
}

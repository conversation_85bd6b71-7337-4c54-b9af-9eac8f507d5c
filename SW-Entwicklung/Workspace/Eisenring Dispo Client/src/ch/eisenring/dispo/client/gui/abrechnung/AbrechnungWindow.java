package ch.eisenring.dispo.client.gui.abrechnung;

import java.awt.Dimension;
import java.awt.GridBagLayout;

import ch.eisenring.app.client.gui.AbstractAPPFrame;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.tag.TagSet;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.client.resource.imgnew.Images;
import ch.eisenring.dispo.shared.transfer.AbrechnungProxy;
import ch.eisenring.gui.util.LayoutUtil;
import ch.eisenring.gui.window.WindowTags;

@SuppressWarnings("serial")
public class AbrechnungWindow extends AbstractAPPFrame<Client> {

	protected final AbrechnungContext context;
	protected final AbrechnungToolBar toolBar;
	protected final AbrechnungTablePanel tablePanel;

	public AbrechnungWindow(final Client client) {
		super(client, new TagSet(
				WindowTags.TITLE, "Abrechnungs-Übersicht",
				WindowTags.MINIMUM_SIZE, new Dimension(800, 680),
				WindowTags.POSITION_SETTINGS_ID, "AbrechnungWindow",
				WindowTags.ICON, Images.PIGGYBANK));
		this.context = new AbrechnungContext(client);
		this.tablePanel = new AbrechnungTablePanel(this);
		this.toolBar = new AbrechnungToolBar(this);
		initComponents();
		initLayout();
	}

	private void initComponents() {
		setLayout(new GridBagLayout());
	}
	
	private void initLayout() {
		getContentPane().removeAll();
		final LayoutUtil l = new LayoutUtil(1);
		add(toolBar, l.panel(1, 0, 10));
		add(tablePanel, l.area(10, 10, 1));
	}
	
	@Override
	protected void onOpen() {
		super.onOpen();
	}

	@Override
	protected void onCancel() {
		super.onCancel();
		setVisible(false);
	}

	@Override
	protected void onClose() {
		super.onClose();
		dispose();
	}
	
	public List<AbrechnungProxy> getSelection() {
		return tablePanel.getSelection();
	}

}

package ch.eisenring.dispo.client.gui.abruf;

import java.util.Collections;

import ch.eisenring.core.collections.Collection;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.gui.components.HEAGListModel;

@SuppressWarnings("serial")
public class SelectOrdersListModel extends HEAGListModel<Object> {

	private final List<String> stringList = new ArrayList<String>();

	@Override
	public int getSize() {
		return stringList.size();
	}

	@Override
	public Object getElementAt(final int index) {
		if (index < 0 || index >= stringList.size())
			return null;
		return stringList.get(index);
	}

	public void clear() {
		int rowCount = stringList.size();
		if (rowCount > 0) {
			stringList.clear();
			fireIntervalRemoved(this, 0, rowCount);
		}
	}

	public void setContent(final Collection<String> content) {
		clear();
		if (content != null && !content.isEmpty()) {
			for (final String item : content) {
				if (item != null)
					stringList.add(item);
			}
			Collections.sort(stringList, Strings.Order.NOCASE);
			int rowCount = stringList.size();
			if (rowCount > 0) {
				fireIntervalAdded(this, 0, rowCount);
			}
		}
	}

	private final static String ALLOWED_CHARS =
		"0123456789-" +
		"ABCDEFGHIJKLMNOPQRSTUVWXYZ" +
		"abcdefghijklmnopqrstuvwxyz";

	private final static String SEPERATOR_CHARS = ",;: \t\r\n";
	
	public static List<String> splitProjektnummern(final CharSequence projektnummern) {
		final List<String> result = new ArrayList<String>();
		if (projektnummern == null)
			return result;
		final StringMaker buffer = StringMaker.obtain(64);
		final int l = projektnummern.length();
		for (int i=0; i<l; ++i) {
			final char c = projektnummern.charAt(i);
			if (Strings.isWhitespace(c) || SEPERATOR_CHARS.indexOf(c) >= 0) {
				if (buffer.length() > 0) {
					result.add(buffer.toString());
					buffer.setLength(0);
				}
			} else if (ALLOWED_CHARS.indexOf(c) >= 0) {
				buffer.append(c);
			}
		}
		if (buffer.length() > 0) {
			result.add(buffer.toString());
		}
		buffer.releaseSilent();
		return result;
	}
}

package ch.eisenring.dispo.client.action;

import static ch.eisenring.dispo.shared.codetables.EmploymentRole.MITARBEITER_CHAUFEUR;
import static ch.eisenring.dispo.shared.codetables.EmploymentRole.MITARBEITER_MONTEUR;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.client.gui.virtual.VirtualEditDialog;
import ch.eisenring.dispo.shared.codetables.DSPRightCode;
import ch.eisenring.dispo.shared.model.VirtualMitarbeiter;

public class ActionVirtualMitarbeiterEdit extends AbstractDSPAction {

	private final VirtualMitarbeiter mitarbeiter;

	public ActionVirtualMitarbeiterEdit(final Client client, final VirtualMitarbeiter mitarbeiter) {
		super(client, "Temporären Mitarbeiter bearbeiten...");
		this.mitarbeiter = mitarbeiter;
		addPermissionObserver();
	}
	
	@Override
	protected void performAction() {
		final VirtualEditDialog dialog = new VirtualEditDialog(client, mitarbeiter);
		dialog.setVisible(true);
	}
	
	@Override
	protected boolean isEnabledImpl() {
		if (mitarbeiter != null) {
			if (MITARBEITER_MONTEUR.equals(mitarbeiter.getEmploymentRole())) {
				return DSPRightCode.VIRTUALMONTEUR.isPermitted();
			} else if (MITARBEITER_CHAUFEUR.equals(mitarbeiter.getEmploymentRole())) {
				return DSPRightCode.VIRTUALCHAUFFEUR.isPermitted();
			}
		}
		return false;
	}

}

package ch.eisenring.dispo.client.remotecontrol;

import java.net.MalformedURLException;

import ch.eisenring.app.client.fileassoc.ProtocolURL;
import ch.eisenring.app.client.fileassoc.ProtocolURLHandler;
import ch.eisenring.app.shared.network.RPCContext;
import ch.eisenring.app.shared.network.RPCHandler;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.client.gui.suche.AuftragSucheDialog;
import ch.eisenring.dispo.client.gui.suche.AuftragSuchePanel;
import ch.eisenring.dispo.client.network.RPCAdapter;
import ch.eisenring.dispo.shared.DSPConstants;
import ch.eisenring.dispo.shared.network.packets.PacketSearchRequest;

public class DSPProtocolURLHandler extends ProtocolURLHandler {

	protected final Client client;
	
	public DSPProtocolURLHandler(final Client client) {
		super(DSPConstants.URL_PROTOCOL);
		this.client = client;
	}
	
	public void processURL(final ProtocolURL url) {
		if (url != null) {
			final String[] args = url.getArguments();
			final String arg0 = getArgument(args, 0);
			if (arg0.equalsIgnoreCase("Open")) {
				try {
					handleOpen(args);
				} catch (final MalformedURLException e) {
					// too bad
				}
			}
		}
	}

	private void handleOpen(final String[] args) throws MalformedURLException {
		final String arg1 = getArgument(args, 1);
		if (arg1.equalsIgnoreCase("Auftrag")) {
			final int auftragNummer = getInt(args, 2);
			if (auftragNummer != 0) {
				AuftragSucheDialog suche = new AuftragSucheDialog(client);
				suche.performSearch(null, auftragNummer);
			}
		} else if (arg1.equalsIgnoreCase("Projekt")) {
			final String projektnummer = getArgument(args, 2);
			if (Strings.isEmpty(projektnummer))
				throw new MalformedURLException("Projektnummer fehlt");
			final PacketSearchRequest request = PacketSearchRequest.create(0, projektnummer);
			client.sendPacket(projektRPCHandler, request);
		} else if (arg1.equalsIgnoreCase("Form")) {
			final int auftragNummer = getInt(args, 2);
			final long objectId = getLong(args, 3);
			if (auftragNummer != 0 && objectId != 0) {
				new RemoteControlHandler(client).addAutoOpenCommand("Form", objectId);
				AuftragSucheDialog suche = new AuftragSucheDialog(client);
				suche.performSearch(null, auftragNummer);
			}
		}
	}

	private static int getInt(final String[] args, final int index) {
		try {
			return Strings.parseInt(getArgument(args, index));
		} catch (final NumberFormatException e) {
			return 0;
		}
	}
	
	private static long getLong(final String[] args, final int index) {
		try {
			return Strings.parseLong(getArgument(args, index));
		} catch (final NumberFormatException e) {
			return 0;
		}
	}

	// --------------------------------------------------------------
	// ---
	// --- Internal RPC Handlers
	// ---
	// --------------------------------------------------------------
	private final RPCHandler projektRPCHandler = new RPCAdapter() {
		@Override
		public void replyReceived(final RPCContext rpcContext) {
			AuftragSuchePanel.searchReplyReceived(rpcContext, null);
		}
	};

}

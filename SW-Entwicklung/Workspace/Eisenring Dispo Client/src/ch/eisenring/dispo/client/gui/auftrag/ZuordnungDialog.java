package ch.eisenring.dispo.client.gui.auftrag;

import static ch.eisenring.dispo.client.DSPClientConstants.LUS_POS_BLOCKWINDOW;

import java.awt.Dimension;
import java.awt.GridBagLayout;
import java.awt.event.KeyEvent;

import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.client.gui.components.dialog.AbstractModelDialog;
import ch.eisenring.dispo.client.gui.grid.highlight.ElementHighlight;
import ch.eisenring.dispo.client.gui.grid.highlight.ZuordnungElementHighlight;
import ch.eisenring.dispo.client.help.HelpUtil;
import ch.eisenring.dispo.client.resource.IconTypeAssociation;
import ch.eisenring.dispo.shared.codetables.DSPAbwicklungsArt;
import ch.eisenring.dispo.shared.codetables.ZuordnungCode;
import ch.eisenring.dispo.shared.model.AbstractModel;
import ch.eisenring.dispo.shared.model.AbstractZuordnung;
import ch.eisenring.dispo.shared.model.Auftrag;
import ch.eisenring.dispo.shared.model.FeinZuordnung;
import ch.eisenring.dispo.shared.model.GrobZuordnung;
import ch.eisenring.dispo.shared.model.ModelReference;
import ch.eisenring.gui.components.Separator;
import ch.eisenring.gui.interfaces.ModelView;
import ch.eisenring.gui.interfaces.ModelViewContainer;
import ch.eisenring.gui.model.StoreResults;
import ch.eisenring.gui.util.LayoutUtil;
import ch.eisenring.gui.window.SACButtonPanel;

@SuppressWarnings("serial")
public final class ZuordnungDialog extends AbstractModelDialog {

	private final SACButtonPanel buttonPanel = new SACButtonPanel();
	private final ZuordnungPanel zuordnungInfo = new ZuordnungPanel(this);
	private final AuftragPane auftragInfo;
	private final OverflowLinkPanel overflowLink;
	private final ElementHighlight highlight;
	
	private boolean viewOnly;
	
	private final HelpUtil helpUtil;

	private final ModelReference<AbstractZuordnung> zuordnungRef;
	
	protected ZuordnungDialog(final Client client, final AbstractZuordnung zuordnung, final boolean viewOnly) {
		super(client, LUS_POS_BLOCKWINDOW, IconTypeAssociation.getZuordnungDialogIcon(zuordnung));
		this.zuordnungRef = new ModelReference<AbstractZuordnung>(zuordnung, client.getContextCache());
		this.helpUtil = new HelpUtil(client, "Auftragdetail", this) {
			@Override
			public boolean isHelpEvent(KeyEvent keyEvent) {
				if (isHelpTrigger(keyEvent)) {
					String id = "Auftragdetails";
					String ext = null!=auftragInfo ? auftragInfo.getShowingTabHelpIdExt() : null;
					if (null!=ext) 
						id += ext;
					keyEvent.consume();
					showHelp(id, ZuordnungDialog.this);
				}
				return false;
			}
		};
		
		final Auftrag auftrag = getAuftrag();
		auftragInfo = null==auftrag ? null : new AuftragPane(this);

		overflowLink = 0==zuordnung.getNavigateToAuftragNummer() ? null : new OverflowLinkPanel(this);
		if (overflowLink != null)
			overflowLink.updateView(zuordnung);
		
		zuordnungInfo.setModel(getZuordnung());
		initComponents();
		initLayout();
		setMinimumSize(new Dimension(800, 600));
		pack();
		setViewOnly(viewOnly);
		highlight = new ZuordnungElementHighlight(client, getZuordnung());
	}

	private void initComponents() {
		updateTitle();
		setLayout(new GridBagLayout());
	}

	public void setViewOnly(boolean viewOnly) {
		this.viewOnly = viewOnly;
		zuordnungInfo.setEditable(!viewOnly);
	}

	private void initLayout() {
		final LayoutUtil l = new LayoutUtil(1);
		add(zuordnungInfo, l.panel(1, 2));
		if (null!=overflowLink) {
			add(Separator.create(), l.separator());
			add(overflowLink, l.panel(1, 0));
		}
		if (null!=auftragInfo) {
			add(auftragInfo, l.area(1, 3, 1));
		}
		buttonPanel.configureScreenShotButton(false);
		add(buttonPanel, l.panel());
		helpUtil.attachListener(this);
		updateView(getViewModel());
	}

	protected AbstractZuordnung getZuordnung() {
		return zuordnungRef == null ? null : zuordnungRef.getModel();
	}

	protected Auftrag getAuftrag() {
		final AbstractZuordnung zuordnung = getZuordnung();
		return zuordnung == null ? null : zuordnung.getAuftrag();
	}

	@Override
	public void updateModel(final Object model) {
		super.updateModel(model);
		final ModelView auftragInfo = this.auftragInfo;
		if (auftragInfo != null) {
			final Auftrag auftrag = model instanceof AbstractZuordnung
				? ((AbstractZuordnung) model).getAuftrag() : null;
			auftragInfo.updateModel(auftrag);
		}
	}

	@Override
	public void updateView(final Object model) {
		super.updateView(model);
		final ModelView auftragInfo = this.auftragInfo;
		if (auftragInfo != null) {
			final Auftrag auftrag = model instanceof AbstractZuordnung
				? ((AbstractZuordnung) model).getAuftrag() : null;
			auftragInfo.updateView(auftrag);
		}
		// haxx
		final AbstractZuordnung z = (AbstractZuordnung) model;
		final boolean editable = !viewOnly;
		zuordnungInfo.setEditable(editable);
		if (auftragInfo != null) {
			final ZuordnungCode type = z.getType();
			auftragInfo.setEditable(editable && type.isDetailEditable());
		}
	}

	@Override
	public void evaluateChanged(final Object model) {
		super.evaluateChanged(model);
		final ModelViewContainer auftragInfo = this.auftragInfo;
		if (auftragInfo != null) {
			final Auftrag auftrag = model instanceof AbstractZuordnung
				? ((AbstractZuordnung) model).getAuftrag() : null;
			auftragInfo.evaluateChanged(auftrag);
		}
	}

	@Override
	public void storeView(final StoreResults results, final Object model) {
		super.storeView(results, model);
		final ModelViewContainer auftragInfo = this.auftragInfo;
		if (auftragInfo != null) {
			final Auftrag auftrag = model instanceof AbstractZuordnung
				? ((AbstractZuordnung) model).getAuftrag() : null;
			auftragInfo.storeView(results, auftrag);
		}
		if (results.isSuccess()) {
			client.sendModelChanges(getZuordnung().getContext());
		}
	}
	
	@Override
	public Object getViewModel() {
		return getZuordnung();
	}
	
	public void modelChanged(final AbstractModel model) {
// 14.11.2011: Dialog nicht aktualisieren, er kommt zu häufig dazu das der Dialog
// aktualisiert wird und Benutzereingaben verworfen werden.
//		if (model.equals(getZuordnung()) || model.equals(getAuftrag())) {
//			updateView();
//		}
	}
	
	public void modelDeleted(final AbstractModel model) {
		if (model.equals(getZuordnung()) || model.equals(getAuftrag())) {
			setVisible(false);
		}
	}

	@Override
	protected void onClose() {
		super.onClose();
		ElementHighlight.remHighlight(highlight);
	}

	@Override
	protected void onOpen() {
		super.onOpen();
		ElementHighlight.addHighligt(highlight);
	}

	private void updateTitle() {
		final AbstractZuordnung z = getZuordnung();
		final String title;
		if (z instanceof GrobZuordnung) {
			if (0==z.getNavigateToAuftragNummer())
				title = Strings.concat("Grobplanung: ", z.getLabel());
			else
				title = Strings.concat(DSPAbwicklungsArt.OVERFLOW, ": ", z.getLabel());
		} else if (z instanceof FeinZuordnung) {
			final ZuordnungCode t = ((FeinZuordnung) z).getType();
			title = Strings.concat((t == null ? "<Fehler>" : t), ": ", z.getLabel()); 
		} else {
			title = Strings.concat("<Fehler>: ", z.getLabel());
		}
		setTitle(title);
	}

	public static void showDialog(final Client client, final AbstractZuordnung zuordnung, final boolean viewOnly) {
		final ZuordnungDialog dialog = new ZuordnungDialog(client, zuordnung, viewOnly);
		showWindow(dialog);
	}
	
}

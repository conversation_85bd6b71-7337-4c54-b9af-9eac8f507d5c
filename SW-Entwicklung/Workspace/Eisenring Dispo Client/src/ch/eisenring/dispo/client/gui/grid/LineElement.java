package ch.eisenring.dispo.client.gui.grid;

import java.awt.Graphics2D;
import java.util.Iterator;

import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.dispo.client.gui.look.ILook;
import ch.eisenring.dispo.shared.model.AbstractModel;

public abstract class LineElement extends AbstractGridElement {

	private final List<BlockElement> blockElements = new ArrayList<BlockElement>();
	private Grid grid;
	
	protected LineElement(final AbstractModel model) {
		super(model);
	}

	@Override
	public void calculateBounds() {
		final DSPGridLayout layout = getLayout();
		if (null!=layout) {
			int w = layout.getWidth() - 2;
			int h = layout.getLineHeight() - 2;
			int x = 1;
			int y = layout.getLineHeight() * getLineIndex() + 1;
			setBounds(x, y, w, h);
			// all block elements are also to recalculate their bounds
			for (int i=blockElements.size()-1; i>=0; --i)
				blockElements.get(i).calculateBounds();
		}
	}

	@Override
	public void paintElement(Graphics2D graphics) {
		final DSPGridLayout layout = getLayout();
		if (null!=layout) {
			layout.getLook().paintLineElement(graphics, this);
			
			// render non-blocking elements
			for (int i=blockElements.size()-1; i>=0; --i) {
				final BlockElement block = blockElements.get(i);
				if (!block.isBlocking()) 
					block.paintElement(graphics);
			}
			
			// render all blocking elements on top
			for (int i=blockElements.size()-1; i>=0; --i) {
				final BlockElement block = blockElements.get(i);
				if (block.isBlocking()) 
					block.paintElement(graphics);
			}

			// detect overlap regions between blocks,
			// draw a red border around overlapping zone
			final ILook look = getLayout().getLook();
			for (int i=blockElements.size()-1; i>=0; --i) {
				final BlockElement b1 = blockElements.get(i);
				final int x10 = b1.getX();
				final int x11 = x10+b1.getWidth()-1;
				for (int j=blockElements.size()-1; j>=0; --j) {
					if (j>=i)
						continue;
					final BlockElement b2 = blockElements.get(j);
					final int x20 = b2.getX();
					final int x21 = x20+b2.getWidth()-1;
					if (x10>x21 || x11<x20)
						continue;
					int x0 = x10<x20 ? x20 : x10;
					int x1 = x11<x21 ? x11 : x21;
					int y0 = b1.getY();
					int y1 = y0+b1.getHeight()-1;
					look.paintBlockOverlap(graphics, x0, y0, x1-x0, y1-y0);
				}
			}
		}
	}

	@Override
	public int getLineIndex() {
		final Grid grid = getGrid();
		return null==grid ? -1 : grid.getLineIndex(this);
	}
	
	@Override
	public Grid getGrid() {
		return this.grid;
	}
	
	@Override
	public void setGrid(Grid grid) {
		if (grid!=this.grid) {
			if (null!=this.grid) {
				this.grid.removeGridElement(this);
			}
			this.grid = grid;
			if (null!=this.grid) {
				this.grid.addGridElement(this);
			}
			calculateBounds();
		}
	}

	/**
	 * Adds a block to this line
	 */
	public void addBlock(BlockElement block) {
		if (null!=block && !blockElements.contains(block)) {
			if (null!=block.getLine())
				block.getLine().removeBlock(block);
			blockElements.add(block);
			block.setLine(this);
		}
	}
	
	/**
	 * Removes a block from this line
	 */
	public void removeBlock(BlockElement block) {
		if (blockElements.remove(block)) {
			block.setLine(null);
		}
	}

	@Override
	public GridElement getElementUnder(final int x, final int y) {
		GridElement element = null;
		if (null!=super.getElementUnder(x, y)) {
			for (int i=blockElements.size()-1; i>=0; --i) {
				element = blockElements.get(i);
				if (element.isBlocking()) {
					element = blockElements.get(i).getElementUnder(x, y);
					if (null!=element)
						break;
				} else {
					element = null;
				}
			}
			if (null==element)
				element = this;
		}
		return element;
	}

	@Override
	public boolean acceptsDrop(GridElement element, int x, int y) {
		// only accept drop on cells, not on the label column
		final DSPGridLayout layout = getLayout();
		final Grid grid = getGrid();
		if (null==layout || null==grid)
			return false;
		final int labelColumn = layout.getLabelColumn();
		final int labelX0 = grid.getXOnScreen() + layout.getColumnMinX(labelColumn);
		final int labelX1 = labelX0 + layout.getColumnWidth(labelColumn) - 1;
		if (x>=labelX0 && x<=labelX1)
			return false;
		return super.acceptsDrop(element, x, y);
	}
	
	@Override
	@SuppressWarnings("unchecked")
	public Iterator<GridElement> newChildIterator() {
		if (blockElements.isEmpty())
			return EMPTY_CHILD_ITERATOR;
		// semantics of generic types are just stupid...
		return (Iterator) blockElements.iterator();
	}
	
	@Override
	public Iterator<GridElement> newChildIterator(final boolean blocking) {
		if (blockElements.isEmpty())
			return EMPTY_CHILD_ITERATOR;
		final List<GridElement> list = new ArrayList<GridElement>(blockElements.size());
		for (int i=blockElements.size()-1; i>=0; --i) {
			final BlockElement block = blockElements.get(i);
			if (blocking==block.isBlocking())
				list.add(block);
		}
		return list.size()<=0 ? EMPTY_CHILD_ITERATOR : list.iterator();
	}
	
	@Override
	public GridElement getElementForModel(final AbstractModel model) {
		if (model == null)
			return null;
		if (model.equals(getModel()))
			return this;
		final List<BlockElement> elements = this.blockElements; 
		for (int i=elements.size()-1; i>=0; --i) {
			final GridElement element = elements.get(i).getElementForModel(model);
			if (element != null)
				return element;
		}
		return null;
	}
	
	@Override
	public GridElement getElementForModelId(final Long modelId) {
		if (modelId == null)
			return null;
		final AbstractModel model = getModel();
		if (model == null)
			return null;
		if (model.getId().equals(modelId))
			return this;
		final List<BlockElement> elements = this.blockElements; 
		for (int i=elements.size()-1; i>=0; --i) {
			final GridElement element = elements.get(i).getElementForModelId(modelId);
			if (element != null)
				return element;
		}
		return null;
	}
	
	@Override
	public boolean isBlocking() {
		return false;
	}

	/**
	 * Returns true if there are any elements past the
	 * visible area to the right.
	 */
	public boolean isRightExtending() {
		for (int i=blockElements.size()-1; i>=0; --i) {
			final BlockElement element = blockElements.get(i);
			if (element != null && element.isBlocking() && element.isPastVisibleArea()) {
				return true;
			}
		}
		return false;
	}
	
	/**
	 * Returns true if there are any elements past the
	 * visible area to the left
	 */
	public boolean isLeftExtending() {
		for (int i=blockElements.size()-1; i>=0; --i) {
			final BlockElement element = blockElements.get(i);
			if (element != null && element.isBlocking() && element.isBeforeVisibleArea()) {
				return true;
			}
		}
		return false;
	}

}
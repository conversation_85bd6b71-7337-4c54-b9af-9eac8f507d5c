package ch.eisenring.dispo.client.gui.report.objektuebersicht;

import java.awt.BorderLayout;
import java.awt.Container;

import ch.eisenring.app.client.background.RPCActivity;
import ch.eisenring.app.shared.network.RPCContext;
import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.tag.TagSet;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.client.gui.components.dialog.AbstractDSPDialog;
import ch.eisenring.dispo.client.network.AbstractShowFileHandler;
import ch.eisenring.dispo.client.resource.imgnew.Images;
import ch.eisenring.dispo.shared.network.packets.PacketObjektuebersichtIDReply;
import ch.eisenring.gui.model.StoreResults;
import ch.eisenring.gui.window.AbstractBaseWindow;
import ch.eisenring.gui.window.SACButtonPanel;
import ch.eisenring.gui.window.WindowTags;
import ch.eisenring.network.packet.AbstractPacket;

@SuppressWarnings("serial")
public class ObjektUebersichtIDDialog extends AbstractDSPDialog {

	protected final SACButtonPanel buttonPanel;
	protected final ObjektUebersichtIDPanel paramPanel;
	
	public ObjektUebersichtIDDialog(final Client client) {
		super(client, new TagSet(
				WindowTags.TITLE, "Objektübersicht ID",
				WindowTags.ICON, Images.XLS,
				WindowTags.RESIZEABLE, WindowTags.RESIZEABLE_NO
				));
		this.buttonPanel = new SACButtonPanel();
		this.paramPanel = new ObjektUebersichtIDPanel(client);
		initComponents();
		initLayout();
		pack();
	}

	private void initComponents() {
		buttonPanel.configureApplyButton((String) null, (String) null);
		buttonPanel.configureSaveButton("Auswerten", "Objektübersicht erstellen");
	}

	private void initLayout() {
		final Container c = getContentPane();
		c.removeAll();
		c.setLayout(new BorderLayout());
		c.add(paramPanel, BorderLayout.CENTER);
		c.add(buttonPanel, BorderLayout.SOUTH);
	}

	public static void showDialog(final Client client) {
		final ObjektUebersichtIDDialog dialog = new ObjektUebersichtIDDialog(client);
		AbstractBaseWindow.showWindow(dialog);
	}

	@Override
	public void storeView(final StoreResults results, final Object model) {
		super.storeView(results, model);
		final AbstractPacket request = paramPanel.getRequest();
		if (request == null)
			return;
		final RPCActivity activity = new RPCActivity(
				"Objektübersicht OB", Images.RATING) {
			@Override
			public void timeoutOccured(final RPCContext rpcContext) {
				client.finishActivity(this, ErrorMessage.TIMEOUT);
			}
			
			@Override
			public void requestSent(final RPCContext rpcContext) {
				client.startActivity(this);
			}
			
			@Override
			public void replyReceived(final RPCContext rpcContext) {
				final PacketObjektuebersichtIDReply reply = (PacketObjektuebersichtIDReply) rpcContext.getReply();
				if (reply.isValid()) {
					client.finishActivity(this);
					AbstractShowFileHandler.showFile(client, reply.getUebersicht());
				} else {
					client.finishActivity(this, reply.getMessage());
				}
			}
		};
		client.sendPacket(activity, request);
	}

}

package ch.eisenring.dispo.client.gui.auftrag.solidarhaftung;

import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.shared.transfer.DSPAuftragInterface;
import ch.eisenring.dms.service.proxy.DMSDocumentHandle;

public class SolidarhaftungData {

	Client client;
	ObjektbetreuerBasisPanel basisPanel;
	List<DMSDocumentHandle> documents = new ArrayList<>();
	DSPAuftragInterface auftrag;
	boolean openFolderInDMS = true;

	public String getMailTo() {
		if (basisPanel == null)
			return null;
		String result = basisPanel.txtEmailSolidarhaftung.getText();
		if (Strings.isEmpty(result) && basisPanel.basisProjektHolder.getAuftrag() != null)
			result = basisPanel.basisProjektHolder.getAuftrag().getEMailBaufuehrer();
		return Strings.isEmpty(result) ? null : result;
	}

}

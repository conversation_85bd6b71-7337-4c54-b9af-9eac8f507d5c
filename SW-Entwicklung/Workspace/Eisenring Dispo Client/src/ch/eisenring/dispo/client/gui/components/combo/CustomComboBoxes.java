package ch.eisenring.dispo.client.gui.components.combo;

import static ch.eisenring.dispo.shared.codetables.EmploymentRole.MITARBEITER_DUMMY;
import static ch.eisenring.dispo.shared.codetables.EmploymentRole.MITARBEITER_MASTER;
import static ch.eisenring.dispo.shared.codetables.SortHintCode.SORT_FIRST;
import static ch.eisenring.dispo.shared.codetables.SortHintCode.SORT_IN_ORDER;
import static ch.eisenring.dispo.shared.codetables.SortHintCode.SORT_LAST;
import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.collections.Collection;
import ch.eisenring.core.collections.List;
import ch.eisenring.dispo.shared.codetables.ColorCode;
import ch.eisenring.dispo.shared.codetables.EmploymentRole;
import ch.eisenring.dispo.shared.codetables.SortHintCode;
import ch.eisenring.gui.ValueEditorBinding;
import ch.eisenring.gui.components.HEAGCodeComboBox;
import ch.eisenring.logiware.code.soft.GSECode;

/**
 * Helper class to provide customized combobox instances
 */
public final class CustomComboBoxes {

	private CustomComboBoxes() {
	}

	public static HEAGCodeComboBox<ColorCode> createColorComboBox() {
		final HEAGCodeComboBox<ColorCode> combo = new HEAGCodeComboBox<ColorCode>(ColorCode.class);
		combo.setDisplayIcons(15);
		return combo;
	}

	public static HEAGCodeComboBox<EmploymentRole> createEmploymentRoleCombo(final boolean showAll) {
		final List<EmploymentRole> list = AbstractCode.getInstances(EmploymentRole.class); 
		if (!showAll) {
			list.remove(MITARBEITER_DUMMY);
			list.remove(MITARBEITER_MASTER);
		}
		final EmploymentRole[] array = Collection.toArray(list, EmploymentRole.class);
		final HEAGCodeComboBox<EmploymentRole> combo = new HEAGCodeComboBox<>(array);
		combo.setToolTipText("Aufgabenbereich");
		return combo;
	}

	@SuppressWarnings("serial")
	public static HEAGCodeComboBox<SortHintCode> createSortHintCombo() {
		final SortHintCode[] items = { SORT_FIRST, SORT_IN_ORDER, SORT_LAST };
		final HEAGCodeComboBox<SortHintCode> combo = new HEAGCodeComboBox<SortHintCode>(SortHintCode.class) {
			@Override
			public void setSelectedItem(final Object item) {
				// Make the comboBox select the proper item,
				// if passed the raw sort hint value.
				if (item instanceof Number) {
					super.setSelectedItem(SortHintCode.getBySortHint(((Number) item).intValue()));
				} else {
					super.setSelectedItem(item);
				}
			}
		};
		combo.populate(items, true);
		return combo;
	}

	public static HEAGCodeComboBox<GSECode> createGSECombo(final ValueEditorBinding binding, final GSECode defaultValue) {
		final HEAGCodeComboBox<GSECode> combo = new HEAGCodeComboBox<GSECode>(GSECode.class, binding);
		combo.populate(new GSECode[] { GSECode.NULL, GSECode.GSE_2501, GSECode.GSE_2502, GSECode.GSE_2503 }, true);
		if (defaultValue != null)
			combo.setSelectedCode(defaultValue);
		return combo;
	}

}


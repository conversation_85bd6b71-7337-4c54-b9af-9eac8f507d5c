package ch.eisenring.dispo.client.gui.dnd;

import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.client.gui.grid.GridElement;

/**
 * The drag'n'drop system relies on hints to decide how an element 
 * is treated by the system. Each element carries three hints
 * associated with it:
 *
 * - DnDHintDrop which describes how the element is treated when
 *   it is dropped onto another element.
 * - DndHintAccept which implements the execution of a drop
 *   onto the element.
 * - DnDHintDrag which describes if the element can be dragged. 
 */
public abstract class DnDHintDrop {

	/**
	 * This method is called when a drop operation is acceptable
	 * to retrieve the element to be really dropped.
	 * 
	 * Elements that need to be copied on drop must create the
	 * copy in this method.
	 */
	public abstract GridElement getDropDelegate(final Client client, final DnDEvent event);

	/**
	 * Returns true if a drop would require a copy of
	 * the dropped element.
	 */
	public abstract boolean isCopyDrop(DnDEvent event);
	
}

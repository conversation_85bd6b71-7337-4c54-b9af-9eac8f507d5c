package ch.eisenring.dispo.client.gui.menu;

import java.awt.event.MouseEvent;

import javax.swing.JPopupMenu;

import ch.eisenring.dispo.client.Client;

@SuppressWarnings("serial")
public class ContextMenuBase extends JPopupMenu {

	protected final Client client;
	protected final boolean isKioskMode;
	
	protected ContextMenuBase(final Client client) {
		this.client = client;
		this.isKioskMode = Boolean.TRUE.equals(client.isKioskMode());
	}

	public void show(MouseEvent event) {
		if (getComponentCount()>0) {
			show(event.getComponent(), event.getX(), event.getY());
		}
	}

}

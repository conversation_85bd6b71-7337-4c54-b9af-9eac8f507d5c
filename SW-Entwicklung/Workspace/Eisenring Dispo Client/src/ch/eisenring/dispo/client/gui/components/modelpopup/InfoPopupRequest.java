package ch.eisenring.dispo.client.gui.components.modelpopup;

import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.Set;
import ch.eisenring.dispo.client.gui.components.FrameOverlay;
import ch.eisenring.dispo.client.gui.grid.BlockElement;
import ch.eisenring.dispo.shared.DSPConstants;
import ch.eisenring.dispo.shared.model.AbstractModel;
import ch.eisenring.dispo.shared.model.AbstractZuordnung;
import ch.eisenring.dispo.shared.model.Auftrag;
import ch.eisenring.dispo.shared.network.packets.PacketInfoPopupBase;
import ch.eisenring.dispo.shared.network.packets.PacketInfoPopupReply;
import ch.eisenring.logiware.LWAuftragKey;
import ch.eisenring.logiware.LWProjektKey;
import ch.eisenring.logiware.code.pseudo.AbwicklungsartCode;
import ch.eisenring.logiware.code.soft.LWKuechenTypCode;
import ch.eisenring.lw.api.LWAuftragPositionData;

final class InfoPopupRequest {

	public final long dueTimestamp;
	public final FrameOverlay overlay;
	private final BlockElement blockElement;
	List<?> positionen;
	PacketInfoPopupReply reply;

	public final static Set<AbwicklungsartCode> AWA_BESTELLINFOS = 
			DSPConstants.ABWICKLUNGSARTEN_DISPO;

	public final static Set<AbwicklungsartCode> AWA_REMOTE = Set.asReadonlySet(
			AbwicklungsartCode.AWA134
		);

	public final static Set<LWKuechenTypCode> KTYP_REMOTE = Set.asReadonlySet(
			LWKuechenTypCode.WASCHTUERME,
			LWKuechenTypCode.WASCHKUECHEN
		);

	InfoPopupRequest(final FrameOverlay overlay, final BlockElement blockElement) {
		this.overlay = overlay;
		this.blockElement = blockElement;
		this.dueTimestamp = blockElement == null 
				? Long.MIN_VALUE : System.currentTimeMillis() + InfoPopupManager.RESPONSE_DELAY_MS;
	}

	public BlockElement getBlockElement() {
		return blockElement;
	}

	public Auftrag getAuftrag() {
		final BlockElement blockElement = getBlockElement();
		if (blockElement == null)
			return null;
		final AbstractModel model = blockElement.getModel();
		if (model == null)
			return null;
		return model instanceof AbstractZuordnung
				? ((AbstractZuordnung) model).getAuftrag() : null;
	}

	public LWAuftragKey getAuftragKey() {
		final Auftrag auftrag = getAuftrag();
		return auftrag == null ? null : auftrag.getAuftragKey();
	}

	public LWProjektKey getProjektKey() {
		final Auftrag auftrag = getAuftrag();
		return auftrag == null ? null : auftrag.getProjektKey();
	}

	public List<LWAuftragPositionData> getPositionen() {
		return reply == null ? null : reply.getPositionen();
	}

	/**
	 * Returns true if this is a request to hide the popup
	 */
	public boolean isHideRequest() {
		return blockElement == null;
	}

	/**
	 * Returns true if this request is due
	 */
	public boolean isDue() {
		if (isHideRequest())
			return true;
		return System.currentTimeMillis() >= dueTimestamp; 
	}

	/**
	 * Returns true if this request needs a remote action
	 */
	public int getRemoteMode() {
		if (isHideRequest())
			return PacketInfoPopupBase.MODE_LOCAL;
		final Auftrag auftrag = getAuftrag();
		if (auftrag == null)
			return PacketInfoPopupBase.MODE_LOCAL;
		// determine if a remote call needs to be made
		int mode = PacketInfoPopupBase.MODE_LOCAL;
		if (AWA_REMOTE.contains(auftrag.getAbwicklungsart())
				&& KTYP_REMOTE.contains(auftrag.getKuechenTyp()))
			mode |= PacketInfoPopupBase.MODE_POSITIONEN;
		if (getProjektKey() != null && AWA_BESTELLINFOS.contains(auftrag.getAbwicklungsart()))
			mode |= PacketInfoPopupBase.MODE_BESTELLINFOS | PacketInfoPopupBase.MODE_ETAPPIERUNG;
		return mode;
	}

	// --------------------------------------------------------------
	// ---
	// --- Object overrides
	// ---
	// --------------------------------------------------------------
	@Override
	public int hashCode() {
		return 0;
	}

	@Override
	public boolean equals(final Object o) {
		if (!(o instanceof InfoPopupRequest))
			return false;
		final InfoPopupRequest r = (InfoPopupRequest) o;
		return r.dueTimestamp == dueTimestamp && r.blockElement == blockElement;
	}
	
}

package ch.eisenring.dispo.client.gui.auftrag;

import java.awt.GridBagLayout;

import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.shared.model.Auftrag;
import ch.eisenring.gui.GridBagConstraints;
import ch.eisenring.gui.components.HEAGCodeComboBox;
import ch.eisenring.gui.components.HEAGLabel;
import ch.eisenring.gui.components.HEAGTextField;
import ch.eisenring.gui.components.Separator;
import ch.eisenring.gui.components.Spacer;
import ch.eisenring.gui.util.ConversionUtil;
import ch.eisenring.logiware.code.soft.LWGranit1Code;
import ch.eisenring.logiware.code.soft.LWGranit2Code;
import ch.eisenring.logiware.code.soft.LWGranit3Code;
import ch.eisenring.logiware.code.soft.LWGranit4Code;
import ch.eisenring.logiware.code.soft.LWGranit5Code;
import ch.eisenring.logiware.code.soft.LWGranit6Code;
import ch.eisenring.logiware.code.soft.LWGranit7Code;
import ch.eisenring.logiware.code.soft.LWGranit8Code;
import ch.eisenring.logiware.code.soft.LWGranit9Code;
import ch.eisenring.logiware.code.soft.LWGranitACode;
import ch.eisenring.logiware.code.soft.LWKuechenTypCode;

@SuppressWarnings("serial")
public final class GranitPanel extends AbstractAuftragPanel {

	@SuppressWarnings("unchecked")
	private final HEAGCodeComboBox<LWGranit1Code> cmb1 = new HEAGCodeComboBox<LWGranit1Code>(LWGranit1Code.class, new AuftragBinding() {
		@Override
		protected void setValue(final Auftrag auftrag, final Object value) {
			auftrag.setGranitschluessel1((LWGranit1Code) value);
		}
		@Override
		protected Object getValue(final Auftrag auftrag) {
			return auftrag.getGranitschluessel1();
		}
	});
	@SuppressWarnings("unchecked")
	private final HEAGCodeComboBox<LWGranit2Code> cmb2 = new HEAGCodeComboBox<LWGranit2Code>(LWGranit2Code.class, new AuftragBinding() {
		@Override
		protected void setValue(final Auftrag auftrag, final Object value) {
			auftrag.setGranitschluessel2((LWGranit2Code) value);
		}
		@Override
		protected Object getValue(final Auftrag auftrag) {
			return auftrag.getGranitschluessel2();
		}
	});
	@SuppressWarnings("unchecked")
	private final HEAGCodeComboBox<LWGranit3Code> cmb3 = new HEAGCodeComboBox<LWGranit3Code>(LWGranit3Code.class, new AuftragBinding() {
		@Override
		protected void setValue(final Auftrag auftrag, final Object value) {
			auftrag.setGranitschluessel3((LWGranit3Code) value);
		}
		@Override
		protected Object getValue(final Auftrag auftrag) {
			return auftrag.getGranitschluessel3();
		}
	});
	@SuppressWarnings("unchecked")
	private final HEAGCodeComboBox<LWGranit4Code> cmb4 = new HEAGCodeComboBox<LWGranit4Code>(LWGranit4Code.class, new AuftragBinding() {
		@Override
		protected void setValue(final Auftrag auftrag, final Object value) {
			auftrag.setGranitschluessel4((LWGranit4Code) value);
		}
		@Override
		protected Object getValue(final Auftrag auftrag) {
			return auftrag.getGranitschluessel4();
		}
	});
	@SuppressWarnings("unchecked")
	private final HEAGCodeComboBox<LWGranit5Code> cmb5 = new HEAGCodeComboBox<LWGranit5Code>(LWGranit5Code.class, new AuftragBinding() {
		@Override
		protected void setValue(final Auftrag auftrag, final Object value) {
			auftrag.setGranitschluessel5((LWGranit5Code) value);
		}
		@Override
		protected Object getValue(final Auftrag auftrag) {
			return auftrag.getGranitschluessel5();
		}
	});
	@SuppressWarnings("unchecked")
	private final HEAGCodeComboBox<LWGranit6Code> cmb6 = new HEAGCodeComboBox<LWGranit6Code>(LWGranit6Code.class, new AuftragBinding() {
		@Override
		protected void setValue(final Auftrag auftrag, final Object value) {
			auftrag.setGranitschluessel6((LWGranit6Code) value);
		}
		@Override
		protected Object getValue(final Auftrag auftrag) {
			return auftrag.getGranitschluessel6();
		}
	});
	@SuppressWarnings("unchecked")
	private final HEAGCodeComboBox<LWGranit7Code> cmb7 = new HEAGCodeComboBox<LWGranit7Code>(LWGranit7Code.class, new AuftragBinding() {
		@Override
		protected void setValue(final Auftrag auftrag, final Object value) {
			auftrag.setGranitschluessel7((LWGranit7Code) value);
		}
		@Override
		protected Object getValue(final Auftrag auftrag) {
			return auftrag.getGranitschluessel7();
		}
	});
	@SuppressWarnings("unchecked")
	private final HEAGCodeComboBox<LWGranit8Code> cmb8 = new HEAGCodeComboBox<LWGranit8Code>(LWGranit8Code.class, new AuftragBinding() {
		@Override
		protected void setValue(final Auftrag auftrag, final Object value) {
			auftrag.setGranitschluessel8((LWGranit8Code) value);
		}
		@Override
		protected Object getValue(final Auftrag auftrag) {
			return auftrag.getGranitschluessel8();
		}
	});
	@SuppressWarnings("unchecked")
	private final HEAGCodeComboBox<LWGranit9Code> cmb9 = new HEAGCodeComboBox<LWGranit9Code>(LWGranit9Code.class, new AuftragBinding() {
		@Override
		protected void setValue(final Auftrag auftrag, final Object value) {
			auftrag.setGranitschluessel9((LWGranit9Code) value);
		}
		@Override
		protected Object getValue(final Auftrag auftrag) {
			return auftrag.getGranitschluessel9();
		}
	});
	@SuppressWarnings("unchecked")
	private final HEAGCodeComboBox<LWGranitACode> cmbA = new HEAGCodeComboBox<LWGranitACode>(LWGranitACode.class, new AuftragBinding() {
		@Override
		protected void setValue(final Auftrag auftrag, final Object value) {
			auftrag.setGranitschluesselA((LWGranitACode) value);
		}
		@Override
		protected Object getValue(final Auftrag auftrag) {
			return auftrag.getGranitschluesselA();
		}
	});
	private final HEAGCodeComboBox<LWKuechenTypCode> cmbKuechenTyp = new HEAGCodeComboBox<LWKuechenTypCode>(LWKuechenTypCode.class, new AuftragBinding() {
		@Override
		protected void setValue(final Auftrag auftrag, final Object value) {
			auftrag.setKuechenTyp((LWKuechenTypCode) value);
		}
		@Override
		protected Object getValue(final Auftrag auftrag) {
			return auftrag.getKuechenTyp();
		}
	});
	private final HEAGTextField txtAbdeckung = new HEAGTextField(new AuftragBinding(40) {
		@Override
		protected void setValue(final Auftrag auftrag, final Object value) {
			auftrag.setAbdeckung(ConversionUtil.convert(value, Strings.NULL));
		}
		@Override
		protected Object getValue(final Auftrag auftrag) {
			return auftrag.getAbdeckung();
		}
	});
	private final HEAGTextField txtAusfuehrung = new HEAGTextField(new AuftragBinding(40) {
		@Override
		protected void setValue(final Auftrag auftrag, final Object value) {
			auftrag.setAusfuehrung(ConversionUtil.convert(value, Strings.NULL));
		}
		@Override
		protected Object getValue(final Auftrag auftrag) {
			return auftrag.getAusfuehrung();
		}
	});
	private final HEAGTextField txtInfoMasse = new HEAGTextField(new AuftragBinding(40) {
		@Override
		protected void setValue(final Auftrag auftrag, final Object value) {
			auftrag.setInfoMasse(ConversionUtil.convert(value, Strings.NULL));
		}
		@Override
		protected Object getValue(final Auftrag auftrag) {
			return auftrag.getInfoMasse();
		}
	});

	public GranitPanel(final Client client) {
		super(client);
		initComponents();
		initLayout();
	}

	private void initComponents() {
		setLayout(new GridBagLayout());
	}

	private void initLayout() {
		removeAll();
		
		int y = 0;
		add(new HEAGLabel("I", false), GridBagConstraints.button(0, y));
		add(new HEAGLabel("Bauart"), GridBagConstraints.label(1, y));
		add(cmb1, GridBagConstraints.field(2, y));
		
		++y;
		add(new HEAGLabel("II", false), GridBagConstraints.button(0, y));
		add(new HEAGLabel("Anzahl"), GridBagConstraints.label(1, y));
		add(cmb2, GridBagConstraints.field(2, y));
		
		++y;
		add(new HEAGLabel("III", false), GridBagConstraints.button(0, y));
		add(new HEAGLabel("Küchenform"), GridBagConstraints.label(1, y));
		add(cmb3, GridBagConstraints.field(2, y));

		++y;
		add(new HEAGLabel("IV", false), GridBagConstraints.button(0, y));
		add(new HEAGLabel("Insel"), GridBagConstraints.label(1, y));
		add(cmb4, GridBagConstraints.field(2, y));

		++y;
		add(new HEAGLabel("V", false), GridBagConstraints.button(0, y));
		add(new HEAGLabel("Ecke 45°"), GridBagConstraints.label(1, y));
		add(cmb5, GridBagConstraints.field(2, y));
		
		++y;
		add(new HEAGLabel("VI", false), GridBagConstraints.button(0, y));
		add(new HEAGLabel("Abd. / RW"), GridBagConstraints.label(1, y));
		add(cmb6, GridBagConstraints.field(2, y));
		
		++y;
		add(new HEAGLabel("VII", false), GridBagConstraints.button(0, y));
		add(new HEAGLabel("RW / Spez"), GridBagConstraints.label(1, y));
		add(cmb7, GridBagConstraints.field(2, y));
		
		++y;
		add(new HEAGLabel("VIII", false), GridBagConstraints.button(0, y));
		add(new HEAGLabel("Produktions"), GridBagConstraints.label(1, y));
		add(cmb8, GridBagConstraints.field(2, y));
		
		++y;
		add(new HEAGLabel("IX", false), GridBagConstraints.button(0, y));
		add(new HEAGLabel("Spülbecken"), GridBagConstraints.label(1, y));
		add(cmb9, GridBagConstraints.field(2, y));

		++y;
		add(new HEAGLabel("X", false), GridBagConstraints.button(0, y));
		add(new HEAGLabel("Bemusterung"), GridBagConstraints.label(1, y));
		add(cmbA, GridBagConstraints.field(2, y));

		++y;
		add(Separator.create(), GridBagConstraints.separator(0, y));

		++y;
		add(new HEAGLabel("Abdeckung"), GridBagConstraints.label(1, y));
		add(txtAbdeckung, GridBagConstraints.field(2, y));

		++y;
		add(new HEAGLabel("Ausführung"), GridBagConstraints.label(1, y));
		add(txtAusfuehrung, GridBagConstraints.field(2, y));

		++y;
		add(new HEAGLabel("Küchentyp"), GridBagConstraints.label(1, y));
		add(cmbKuechenTyp, GridBagConstraints.field(2, y));

		++y;
		add(new HEAGLabel("Info Masse"), GridBagConstraints.label(1, y));
		add(txtInfoMasse, GridBagConstraints.field(2, y));
		
		++y;
		add(new Spacer(), GridBagConstraints.panel(0, y).weightX(0));
	}

	@Override
	public void setEditable(final boolean editable) {
		super.setEditable(editable);
		// these are never mutable
		cmb1.setEnabled(false);
		cmb2.setEnabled(false);
		cmb3.setEnabled(false);
		cmb4.setEnabled(false);
		cmb5.setEnabled(false);
		cmb6.setEnabled(false);
		cmb7.setEnabled(false);
		cmb8.setEnabled(false);
		cmb9.setEnabled(false);
		cmbA.setEnabled(false);
		txtAusfuehrung.setEditable(false);
		txtAbdeckung.setEditable(false);
		cmbKuechenTyp.setEnabled(false);
		txtInfoMasse.setEditable(false);
	}

}

package ch.eisenring.dispo.client;

import ch.eisenring.core.observable.Observable;
import ch.eisenring.dispo.client.favorites.EmployeeFavorites;
import ch.eisenring.dispo.client.gui.grid.highlight.ElementHighlight;
import ch.eisenring.dispo.shared.DSPConstants;
import ch.eisenring.dispo.shared.model.AbstractModel;

public interface DSPClientConstants extends DSPConstants {

	// all states (should be moved into client class)
	public Observable<String> HELP_PATH =
		Observable.create(true, "HelpPath", "../help/");
	public Observable<AbstractModel> STATE_CLIPBOARD =
		Observable.create(true, "PlanungClipboard");
	public Observable<Boolean> SUPPRESS_BLINK =
		Observable.create(true, "SuppressBlink", Boolean.FALSE);
	public Observable<EmployeeFavorites> SHOWN_EMPLOYEES =
		Observable.create(true, "ShownEmployees", EmployeeFavorites.ALL);
	public Observable<ElementHighlight> STATE_GRID_HIGHLIGHTER =
		Observable.create(true, "GridHighlighter");

	// grid constants
	public final static int APPEARANCE_NORMAL = 1;
	public final static int APPEARANCE_GHOSTED = 2;
	public final static int APPEARANCE_HIDDEN = 3;
	
	// user setting id's
	public final static String LUS_POS_MAINWINDOW		= "MainWindow";
	public final static String LUS_POS_NETMON			= "NetworkMonitor";
	public final static String LUS_POS_USEREDIT			= "UserEdit";
	public final static String LUS_POS_ROLEEDIT			= "RoleEdit";
	public final static String LUS_POS_EMPLOYEEEDIT		= "EmployeeEdit";
	public final static String LUS_POS_BLOCKWINDOW		= "BlockWindow";
	public final static String LUS_POS_SEARCHWINDOW		= "SearchWindow";
	public final static String LUS_POS_POSITIONWINDOW	= "PositionWindow";
	public final static String LUS_POS_ATTACHMENTWINDOW	= "AttachmentWindow";
	public final static String LUS_POS_JUMPTOWINDOW		= "JumpToWindow";
	public final static String LUS_POS_USERLIST			= "UserListWindow";
	public final static String LUS_POS_ROLELIST			= "RoleListWindow";
	public final static String LUS_POS_EMPLOYEELIST		= "EmployeeListWindow";
	public final static String LUS_POS_PHONEDIAL		= "PhoneDialWindow";
	public final static String LUS_POS_MONTEURAUFTRAG	= "MonteurAuftragPrintWindow";
	public final static String LUS_POS_PRINTINGPROGRESS	= "PrintingProgressWindow";
	public final static String LUS_POS_OVERFLOWCREATE	= "OverflowCreateWindow";
	public final static String LUS_POS_FOTOBERICHTPRINT	= "FotoBerichtPrintWindow";
	public final static String LUS_POS_DISPOEVENTWINDOW	= "DispoEventWindow";
	public final static String LUS_POS_DISPOEVENTPRINT	= "DispoEventPrintWindow";
	public final static String LUS_POS_POIEXPORT		= "POIExportWindow";
	public final static String LUS_POS_EMAILDIALOG		= "EMailWindow";
	public final static String LUS_POS_FORMDIALOG		= "FormWindow";
	public final static String LUS_POS_STATISTICSWINDOW	= "StatisticsWindow";
	public final static String LUS_POIEXPORTPATH		= "POIExportPath";

}

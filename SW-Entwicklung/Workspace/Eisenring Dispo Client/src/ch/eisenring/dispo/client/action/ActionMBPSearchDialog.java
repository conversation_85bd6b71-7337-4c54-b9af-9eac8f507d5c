package ch.eisenring.dispo.client.action;

import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.client.gui.montageprint.MBPSelectDialog;
import ch.eisenring.dispo.client.resource.imgnew.Images;
import ch.eisenring.gui.window.AbstractBaseWindow;

public class ActionMBPSearchDialog extends AbstractDSPAction {

	public ActionMBPSearchDialog(final Client client) {
		super(client, "Montagedossier-Batchdruck Auswahl...", Images.SEARCH);
	}

	@Override
	protected boolean isEnabledImpl() {
		return super.isEnabledImpl();
	}

	@Override
	protected void performAction() {
		final MBPSelectDialog dialog = new MBPSelectDialog(client);
		AbstractBaseWindow.showWindow(dialog);
	}

}

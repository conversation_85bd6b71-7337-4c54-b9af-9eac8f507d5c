package ch.eisenring.dispo.client.gui.misc;

import static ch.eisenring.dispo.client.DSPClientConstants.LUS_POS_NETMON;

import java.awt.BorderLayout;
import java.util.concurrent.atomic.AtomicBoolean;

import ch.eisenring.core.logging.Logger;
import ch.eisenring.core.logging.Logger.LogLevel;
import ch.eisenring.core.tag.TagSet;
import ch.eisenring.core.threading.ThreadCore;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.client.gui.components.dialog.AbstractDSPDialog;
import ch.eisenring.dispo.client.network.StatsRequestHandler;
import ch.eisenring.dispo.shared.network.NetworkStats;
import ch.eisenring.dispo.shared.network.packets.PacketStatsRequest;
import ch.eisenring.gui.resource.images.Images;
import ch.eisenring.gui.util.GUIUtil;
import ch.eisenring.gui.window.DialogTags;
import ch.eisenring.gui.window.WindowTags;
import ch.eisenring.network.packet.PacketPing;

@SuppressWarnings("serial")
public final class NetworkMonitor extends AbstractDSPDialog {

	private final NetworkMonitorPanel clientPanel = new NetworkMonitorPanel("Client");
	private final NetworkMonitorPanel serverPanel = new NetworkMonitorPanel("Server");

	private final AtomicBoolean active = new AtomicBoolean(false);

	
	private final Runnable poller = new Runnable() {
		@Override
		public void run() {
			Logger.log(LogLevel.INFO, "NetworkMonitor poller started");
			while (active.get()) {
				client.sendPacket(PacketPing.create(), false);
				client.sendPacket(PacketStatsRequest.create(), false);
				ThreadCore.sleep(500);
				GUIUtil.invokeLater(new Runnable() {
					@Override
					public void run() {
						updateFields();
					}
				});
				ThreadCore.sleep(499);
			}
			Logger.log(LogLevel.INFO, "NetworkMonitor poller terminated");
		}
	};
	
	public NetworkMonitor(final Client client) {
		super(client, new TagSet(
				WindowTags.POSITION_SETTINGS_ID, LUS_POS_NETMON,
				WindowTags.ICON, Images.COG,
				WindowTags.TITLE, "Netzwerk-Monitor",
				WindowTags.RESIZEABLE, WindowTags.RESIZEABLE_NO,
				DialogTags.MODALITY, DialogTags.MODALITY_MODELESS
				));
		initLayout();
		pack();
		updateFields();
	}

	private void initLayout() {
		setLayout(new BorderLayout());
		add(clientPanel, BorderLayout.WEST);
		add(serverPanel, BorderLayout.EAST);
	}

	@Override
	protected void onOpen() {
		super.onOpen();
		active.set(true);
		client.getThreadPool().start(poller, "NetworkMonitor");
	}

	@Override
	protected void onClose() {
		super.onClose();
		active.set(false);
	}

	protected void updateFields() {
		final NetworkStats clientStats = StatsRequestHandler.getClientStats(client);
		final NetworkStats serverStats = client.SERVER_STATS.get();
		clientPanel.updateFields(clientStats);
		serverPanel.updateFields(serverStats);
	}
	
}

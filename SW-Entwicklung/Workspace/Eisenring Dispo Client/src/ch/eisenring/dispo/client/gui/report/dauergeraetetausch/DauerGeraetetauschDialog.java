package ch.eisenring.dispo.client.gui.report.dauergeraetetausch;

import java.awt.BorderLayout;
import java.awt.Dimension;

import ch.eisenring.app.client.background.RPCActivity;
import ch.eisenring.app.shared.network.RPCContext;
import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.tag.TagSet;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.client.gui.components.dialog.AbstractDSPDialog;
import ch.eisenring.dispo.client.network.AbstractShowFileHandler;
import ch.eisenring.dispo.client.resource.imgnew.Images;
import ch.eisenring.dispo.shared.network.packets.PacketDauerGeraeteTauschReply;
import ch.eisenring.gui.model.StoreResults;
import ch.eisenring.gui.window.SACButtonPanel;
import ch.eisenring.gui.window.WindowTags;
import ch.eisenring.network.packet.AbstractPacket;

@SuppressWarnings("serial")
public class DauerGeraetetauschDialog extends AbstractDSPDialog {

	protected final DauerGeraetetauschPanel parameterPanel;
	protected final SACButtonPanel buttonPanel = new SACButtonPanel();
	
	public DauerGeraetetauschDialog(final Client client) {
		super(client, new TagSet( 
			WindowTags.TITLE, "Dauer Gerätetausch",
			WindowTags.ICON, Images.QUESTION,
			WindowTags.MINIMUM_SIZE, new Dimension(320, 256)
		));
		this.parameterPanel = new DauerGeraetetauschPanel(client);
		initComponents();
		initLayout();
		pack();
	}

	private void initComponents() {
		setLayout(new BorderLayout());
		buttonPanel.configureApplyButton(null, null);
		buttonPanel.configureSaveButton("Auswerten", "Auswertung starten");
	}

	private void initLayout() {
		getContentPane().removeAll();
		add(parameterPanel, BorderLayout.CENTER);
		add(buttonPanel, BorderLayout.SOUTH);
	}

	@Override
	public void storeView(final StoreResults results, final Object model) {
		super.storeView(results, model);
		final AbstractPacket request = parameterPanel.getRequest();
		if (request == null)
			return;
		final RPCActivity activity = new RPCActivity(
				"Dauer Gerätetausch", Images.XLS) {
			@Override
			public void timeoutOccured(final RPCContext rpcContext) {
				client.finishActivity(this, ErrorMessage.TIMEOUT);
			}
			
			@Override
			public void requestSent(final RPCContext rpcContext) {
				client.startActivity(this);
			}
			
			@Override
			public void replyReceived(final RPCContext rpcContext) {
				final PacketDauerGeraeteTauschReply reply = (PacketDauerGeraeteTauschReply) rpcContext.getReply();
				if (reply.isValid()) {
					client.finishActivity(this);
					AbstractShowFileHandler.showFile(client, reply.getFile());
				} else {
					client.finishActivity(this, reply.getMessage());
				}
			}
		};
		client.sendPacket(activity, request);
	}
}

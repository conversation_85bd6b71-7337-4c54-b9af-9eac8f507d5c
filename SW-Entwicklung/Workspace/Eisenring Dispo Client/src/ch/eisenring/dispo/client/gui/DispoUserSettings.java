package ch.eisenring.dispo.client.gui;

import static ch.eisenring.dispo.shared.DSPConstants.VERSION_STRING;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;

import ch.eisenring.core.io.StreamFormat;
import ch.eisenring.core.io.StreamReader;
import ch.eisenring.core.io.StreamWriter;
import ch.eisenring.core.io.Streams;
import ch.eisenring.core.io.file.FileItem;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.core.logging.Logger.LogLevel;
import ch.eisenring.core.usersettings.GenericUserSettings;
import ch.eisenring.dispo.client.favorites.EmployeeFavorites;

public final class DispoUserSettings extends GenericUserSettings {

	private final static String SETTINGS = "Dispo-Usersettings.data";
	private final static String FAVORITES = "Dispo-Favorites.data";
	
	public DispoUserSettings() {
		super(VERSION_STRING, "Eisenring", SETTINGS);
	}

	/**
	 * Saves the data to file
	 */
	protected void write() {
		super.write();
		writeFavorites();
	}

	/**
	 * Reads the data from file
	 */
	public void read() {
		super.read();
		readFavorites();
	}

	private FileItem getFavoritesFile(final boolean createDirs) {
		FileItem file = getSettingsFile(createDirs);
		file = file.getParent();
		file = file.createChild(FAVORITES);
		return file;
	}

	/**
	 * Saves favorites
	 */
	public void writeFavorites() {
		final FileItem file = getFavoritesFile(true);
		OutputStream out = null;
		StreamWriter writer = null;
		try {
			// write the settings
			out = file.getOutputStream();
			writer = StreamFormat.SETTINGS.createWriter(out);
			EmployeeFavorites.ALL.write(writer);
			writer.close();
		} catch (final IOException e) {
			// ignore all failures
			Logger.log(LogLevel.ERROR, e);
		} finally {
			Streams.closeSilent(writer);
			Streams.closeSilent(out);
		}
	}

	/**
	 * Reads favorites
	 */
	public void readFavorites() {
		final FileItem file = getFavoritesFile(false);
		InputStream in = null;
		StreamReader reader = null;
		try {
			// read the settings
			in = file.getInputStream();
			reader = StreamFormat.createReader(in);
			EmployeeFavorites.ALL.read(reader);
			reader.close();
		} catch (final IOException e) {
			// ignore all failures
			Logger.log(LogLevel.ERROR, e);
		} finally {
			Streams.closeSilent(reader);
			Streams.closeSilent(in);
		}
	}

}

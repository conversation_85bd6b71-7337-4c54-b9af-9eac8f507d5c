package ch.eisenring.dispo.client.network;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;

import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.io.Streams;
import ch.eisenring.core.platform.Platform;
import ch.eisenring.core.platform.PlatformPath;
import ch.eisenring.core.util.file.FileImage;
import ch.eisenring.core.util.file.FileUtil;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.network.PacketSink;
import ch.eisenring.network.packet.AbstractPacket;

/**
 * Shows a file image received with packet 
 */
public abstract class AbstractShowFileHandler<T extends AbstractPacket> extends AbstractDSPPacketHandler {

	protected AbstractShowFileHandler(final Client client, final Class<T> packetClass) {
		super(client, packetClass);
	}

	@SuppressWarnings("unchecked")
	@Override
	public void handle(final AbstractPacket packet, final PacketSink sink) {
		handleDisplay((T) packet);
	}

	protected abstract FileImage getFileImage(final T packet);
	
	private void handleDisplay(final T packet) {
		if (!packet.isValid()) {
			client.message(packet.getMessage());
			return;
		}
		showFile(client, getFileImage(packet));
	}

	public static void showFile(final Client client, final FileImage fileImage) {
		final Platform platform = Platform.getPlatform();
		final String filename = FileUtil.sanitizeFilename(fileImage.getFilename());
		final File tempfile = new File(platform.getPath(PlatformPath.APP_TEMP), filename);
		OutputStream output = null;
		try {
			output = new FileOutputStream(tempfile);
			fileImage.getFiledata().writeTo(output);
		} catch (final IOException e) {
			client.message(new ErrorMessage(e));
			throw new RuntimeException(e);
		} finally {
			Streams.closeSilent(output);
		}
		tempfile.deleteOnExit();
		platform.openFile(tempfile.getAbsolutePath(), true);
	}

}

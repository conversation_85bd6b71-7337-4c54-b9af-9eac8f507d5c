package ch.eisenring.dispo.client.gui.employee;

import java.awt.Desktop;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;

import ch.eisenring.core.collections.List;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.io.Streams;
import ch.eisenring.core.iterators.Filter;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.core.platform.Platform;
import ch.eisenring.core.platform.PlatformPath;
import ch.eisenring.core.util.file.FileUtil;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.shared.model.AbstractDocument;
import ch.eisenring.dispo.shared.model.AbstractModel;
import ch.eisenring.dispo.shared.model.BinaryModel;
import ch.eisenring.dispo.shared.model.ModelContext;
import ch.eisenring.dispo.shared.network.packets.PacketBinaryResponse;

//@Deprecated
class BinaryViewer {

	private final Client client;
	protected boolean seperateProcess = true;
	protected boolean uniqeFileName = true;
	
	public BinaryViewer(final Client client) {
		this.client = client;
	}

	public void viewBinary(final PacketBinaryResponse packet) {
		final ModelContext context = client.getCurrentModel();
		viewBinary(packet, context);
	}

	public void viewBinary(final PacketBinaryResponse packet, final ModelContext context) {
		final List<BinaryModel> binaryList = packet.getBinaries();
		for (int i=0; i<binaryList.size(); ++i) {
			final BinaryModel binary = binaryList.get(i);
			final AbstractDocument document = findDocumentForBinary(binary, context);
			if (null!=document) {
				final String fileName = FileUtil.sanitizeFilename(document.getFileName(), uniqeFileName);
				final byte[] fileData = binary.getBinary();
				viewDocument(fileName, fileData);
			}
		}
	}
	
	@SuppressWarnings("unchecked")
	public AbstractDocument findDocumentForBinary(final BinaryModel binary, final ModelContext context) {
		final long binaryId = binary.getId();
		final List<AbstractDocument> list = (List) context.getModelList(null, new Filter<AbstractModel>() {
			@Override
			public boolean accepts(AbstractModel model) {
				if (model instanceof AbstractDocument) {
					final AbstractDocument ad = (AbstractDocument) model;
					if (ad.getBinaryId()==binaryId)
						return true;
				}
				return false;
			}
		});
		return list.isEmpty() ? null : list.get(0);
	}

	public String writeTempFile(String fileName, byte[] rawData) {
		// write data to temporary file
		final File file = new File(Platform.getPlatform().getPath(PlatformPath.APP_TEMP), fileName);
		FileOutputStream out = null;
		try {
			out = new FileOutputStream(file);
			out.write(rawData);
			out.close();
			file.deleteOnExit();
			return file.getAbsolutePath();
		} catch (IOException e) {
			Logger.error("error writing temporary file:");
			Logger.error(e);
			client.error(Strings.concat("Dateisystem-Fehler: ", e.getMessage()));
			return null;
		} finally {
			Streams.closeSilent(out);
		}
	}

	public void viewDocument(String fileName, byte[] rawData) {
		// write data to temporary file
		final String tempName = writeTempFile(fileName, rawData);
		if (tempName != null) {
			viewFile(tempName);
		}
	}

	public void viewFile(final String fileName) {
		doViewFile(fileName, true);
	}

	protected void doViewFile(final String fileName, final boolean async) {
		if (async) {
			final Runnable r = new Runnable() {
				@Override
				public void run() {
					doViewFile(fileName, false);
				}		
			};
			client.getThreadPool().start(r, "BinaryViewer.viewFile()");
			return;
		} else {
			final String nativeName; {
				final StringMaker nativeMaker = StringMaker.obtain();
				nativeMaker.append(fileName);
				nativeMaker.replace('/', File.separatorChar);
				nativeMaker.replace('\\', File.separatorChar);
				nativeName = nativeMaker.release();
			}
			boolean useCmdLineHack = true;
			if (Desktop.isDesktopSupported()) {
				try {
					final Desktop desktop = Desktop.getDesktop();
					if (desktop.isSupported(Desktop.Action.OPEN)) {
						final File file = new File(nativeName);
						desktop.open(file);
						useCmdLineHack = false;
						if (Logger.isDebugEnabled())
							Logger.debug(Strings.concat("opened file (", fileName, ") using java.awt.Desktop::open()"));
					}
				} catch (final Exception e) {
					Logger.warn(Strings.concat("java.awt.Desktop::open() failed: ", e.getMessage()));
					Logger.warn(e);
				}
			}
			if (useCmdLineHack) {
				Logger.warn("Using using ProcessBuilder hack as fallback to open file");
				try {
					// let the operating system deal with showing the file
					final String[] cmd = { "rundll32", "url.dll,FileProtocolHandler", nativeName };
					final ProcessBuilder pb = new ProcessBuilder(cmd);
					pb.start();
				} catch (final IOException e) {
					Logger.error(Strings.concat("error viewing file: ", fileName));
					Logger.error(e);
				}
			}
		}
	}

}

package ch.eisenring.dispo.client.gui.menu;

import ch.eisenring.app.shared.network.RPCContext;
import ch.eisenring.app.shared.network.RPCHandlerEDT;
import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.client.action.ActionElementBase;
import ch.eisenring.dispo.client.gui.endmontage.EndmontageDossierDialog;
import ch.eisenring.dispo.client.gui.grid.BlockElement;
import ch.eisenring.dispo.client.gui.grid.GridElement;
import ch.eisenring.dispo.shared.model.AbstractModel;
import ch.eisenring.dispo.shared.model.AbstractZuordnung;
import ch.eisenring.dispo.shared.model.Auftrag;
import ch.eisenring.dispo.shared.network.packets.PacketDMSEndmontageChoiceReply;
import ch.eisenring.dispo.shared.network.packets.PacketDMSEndmontageChoiceRequest;
import ch.eisenring.logiware.LWProjektKey;
import heag.huo.client.resources.images.Images;

public class ActionEndmontageDossier extends ActionElementBase {

	public ActionEndmontageDossier(final Client client, final GridElement gridElement) {
		super(client, gridElement, Images.OPENFOLDER, "Endmontage Dossier drucken...");
		isEnabled();
	}

	@Override
	protected boolean isEnabledImpl() {
		//return false;
		return super.isEnabledImpl() && getProjektKey(getBlockElement()) != null;
	}

	private static LWProjektKey getProjektKey(final BlockElement element) {
		if (element == null)
			return null;
		final AbstractModel model = element.getModel();
		final Auftrag auftrag;
		if (model instanceof Auftrag) {
			auftrag = (Auftrag) model;
		} else if (model instanceof AbstractZuordnung) {
			auftrag =((AbstractZuordnung) model).getAuftrag();
		} else {
			auftrag = null;
		}
		if (auftrag == null)
			return null;
		return auftrag.getProjektKey();
	}

	@Override
	protected void performBlockAction(final BlockElement blockElement) {
		final LWProjektKey projektKey = getProjektKey(blockElement);
		if (projektKey == null)
			return;
		// do it
		final PacketDMSEndmontageChoiceRequest request = PacketDMSEndmontageChoiceRequest.create(projektKey);
		client.sendPacket(new RPCHandler(client), request);
	}

	static class RPCHandler implements RPCHandlerEDT {
		final Client client;
		RPCHandler(final Client client) {
			this.client = client;
		}
	
		@Override
		public void timeoutOccured(final RPCContext rpcContext) {
			client.message(ErrorMessage.TIMEOUT);
		}
		
		@Override
		public void replyReceived(final RPCContext rpcContext) {
			final PacketDMSEndmontageChoiceReply reply = (PacketDMSEndmontageChoiceReply) rpcContext.getReply();
			if (reply.isValid()) {
				EndmontageDossierDialog.showDialog(client, reply);
			} else {
				client.message(reply.getMessage());
			}
		}
	}

}

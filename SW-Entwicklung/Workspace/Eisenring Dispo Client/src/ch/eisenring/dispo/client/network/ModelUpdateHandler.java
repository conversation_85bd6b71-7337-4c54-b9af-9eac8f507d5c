package ch.eisenring.dispo.client.network;

import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.client.ClientContextCache;
import ch.eisenring.dispo.shared.model.ModelContext;
import ch.eisenring.dispo.shared.network.packets.PacketContextVote;
import ch.eisenring.dispo.shared.network.packets.PacketModelExpunge;
import ch.eisenring.dispo.shared.network.packets.PacketModelUpdate;
import ch.eisenring.gui.util.GUIUtil;
import ch.eisenring.network.PacketSink;
import ch.eisenring.network.implementation.PacketDispatchMode;
import ch.eisenring.network.packet.AbstractPacket;

public final class ModelUpdateHandler extends AbstractDSPPacketHandler {

	ModelUpdateHandler(final Client client) {
		super(client, PacketModelUpdate.class, PacketDispatchMode.SYNCHRONOUS);
	}

	@Override
	public void handle(final AbstractPacket abstractPacket, final PacketSink sink) {
		final PacketModelUpdate packet = (PacketModelUpdate) abstractPacket;
		// let the context cache deal with the update
		final long date = packet.getDate();
		final ClientContextCache cache = client.getContextCache();
		final ModelContext context = cache.getContext(date);
		// if the client doesn't have this model, ignore the update!
		// (it means the change concerns a model this client doesn't have)
		if (context == null)
			return;
		
		// to avoid a deadlock with context synchronized access,
		// the remaining code must be executed on the AWT-thread.
		GUIUtil.invokeLater(new Runnable() {
			@Override
			public void run() {
				// perform the update
				context.obtain();
				try {
					final int version = packet.getVersion();
					if (version-1!=context.getVersion()) {
						// the update is for a different version -> model version conflict
						// resolve by invalidating the clients model 
						client.dispatch(PacketModelExpunge.create(date), sink);
						return;
					} else {
						// update the client model
						try {
							context.setVersion(version);
							packet.updateContext(context);
							client.modelUpdated(context);
							context.clearDeleted();
							// notify server that the data was fine for us
							sink.sendPacket(PacketContextVote.create(context.getBegin(), 1), false);
						} catch (final Exception e) {
							Logger.error(Strings.concat("error updating ", context, ": ", e.getMessage()));
							Logger.error(e);
							// notify server that we had a problem with this context
							sink.sendPacket(PacketContextVote.create(context.getBegin(), -3), false);
							// evict the context
							client.dispatch(PacketModelExpunge.create(date), sink);
						}
					}
				} finally {
					context.release();
				}
			}
		});
	}

}

package ch.eisenring.dispo.client.gui.report.endmontagen;

import java.awt.BorderLayout;

import javax.swing.JComponent;

import org.jfree.chart.ChartFactory;
import org.jfree.chart.ChartPanel;
import org.jfree.chart.JFreeChart;
import org.jfree.chart.plot.CategoryPlot;
import org.jfree.chart.plot.PlotOrientation;
import org.jfree.data.category.CategoryDataset;

import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.tag.TagSet;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.shared.network.packets.PacketReportAbschlussquotenReply;
import ch.eisenring.gui.components.HEAGPanel;
import ch.eisenring.statistics.shared.data.DataHolder;
import ch.eisenring.statistics.shared.util.DataSetBuilder;
import ch.eisenring.statistics.shared.util.RotateXAxisLabelsCustomizer;

@SuppressWarnings("serial")
public class ReportAbschlussquotenResultPanel extends HEAGPanel {

	protected final Client client;
	protected final PacketReportAbschlussquotenReply packet;
	protected JComponent chartPanel;

	public ReportAbschlussquotenResultPanel(final Client client,
			final PacketReportAbschlussquotenReply packet) {
		this.client = client;
		this.packet = packet;
		initComponents();
		initLayout();
	}

	private void initComponents() {
		final String g2 = AbstractCode.getLongText(packet.getGranit2(), "");
		final Integer verkaeufer = packet.getVerkaeufer();
		final StringMaker description = StringMaker.obtain(128);
		description.append("Abschlussquoten Offerten");
		if (!Strings.isEmpty(g2)) {
			description.append(' ');
			description.append(g2);
		}
		if (verkaeufer != null) {
			description.append(description.length() == 0 ? " " : ", ");
			description.append(verkaeufer);
		}
		setLayout(new BorderLayout());
		DataHolder dataHolder = new DataHolder(packet.getDataPoints());
		CategoryDataset dataSet = DataSetBuilder.getCategoryDataSet(dataHolder, 0, 99999);
		JFreeChart chart = ChartFactory.createLineChart(
			description.release(),
			AbstractCode.getLongText(packet.getGranularity(), "???"),
			"%",
			dataSet,
			PlotOrientation.VERTICAL,
			true, 
			true,
			false);
		new RotateXAxisLabelsCustomizer().customizePlot((CategoryPlot) chart.getPlot(), (TagSet) null);		
		chartPanel = new ChartPanel(chart);
	}

	private void initLayout() {
		add(chartPanel, BorderLayout.CENTER);
	}

}

package ch.eisenring.dispo.client.gui.kok;

import static ch.eisenring.dispo.shared.codetables.AnsichtCode.FEINPLANUNG;

import java.util.Collection;

import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.client.action.AbstractDSPAction;
import ch.eisenring.dispo.client.resource.imgnew.Images;
import ch.eisenring.dispo.shared.codetables.DSPAbwicklungsArt;
import ch.eisenring.dispo.shared.codetables.DSPRightCode;
import ch.eisenring.dispo.shared.logic.FeinPlanungAbschlussUtil;
import ch.eisenring.dispo.shared.model.Auftrag;
import ch.eisenring.dispo.shared.model.ModelContext;
import ch.eisenring.dispo.shared.network.packets.PacketFeinPlanungAbschluss;
import ch.eisenring.logiware.code.soft.LWDisponentSirCode;

public class KOKSetzenAction extends AbstractDSPAction {

	public final static int ACTION_FOR_REAL = 0xCAFEBABE;
	public final static int ACTION_IGNORE = 0;
	
	private final DSPAbwicklungsArt dspAbwicklungsArt;
	private final int behavior;
	
	public KOKSetzenAction(final Client client,
			               final DSPAbwicklungsArt abwicklungsArt,
			               final int behavior) {
		super(client, Images.KOK, getLabelText(abwicklungsArt));
		this.dspAbwicklungsArt = abwicklungsArt == null ? DSPAbwicklungsArt.SHOWALL : abwicklungsArt;
		this.behavior = behavior;
		addPermissionObserver();
	}

	private static String getLabelText(final AbstractCode abwicklungsArt) {
		if (DSPAbwicklungsArt.SHOWALL.equals(abwicklungsArt)) {
			return "Feinplanung für alle Abwicklungsarten abschliessen";
		} else {
			return Strings.concat("Feinplanung ", abwicklungsArt, " abschliessen");
		}
	}

	@Override
	protected void performAction() {
		if (behavior != ACTION_FOR_REAL) {
			return;
		}
		final ModelContext context = client.getCurrentModel();
		if (context == null)
			return;
		final LWDisponentSirCode disponent = client.getCurrentDisponent();
		if (disponent == null)
			return;
		if (DSPAbwicklungsArt.SHOWALL.equals(dspAbwicklungsArt)) {
			if (!askConfirmation(client, "Wirklich Feinplanung für alle Auftragsarten abschliessen?")) {
				return;
			}
		}
		PacketFeinPlanungAbschluss packet = PacketFeinPlanungAbschluss.create(disponent, context.getBegin(), dspAbwicklungsArt);
		client.sendPacket(packet);
	}
	
	@Override
	protected boolean isEnabledImpl() {
		if (!super.isEnabledImpl())
			return false;
		final LWDisponentSirCode disponent = client.getCurrentDisponent();
		boolean enabled = FEINPLANUNG.equals(client.ANSICHT.get());
		enabled &= (disponent != null && !LWDisponentSirCode.NULL.equals(disponent));
		enabled &= !Boolean.TRUE.equals(client.WAITING_FOR_MODEL.get());
		enabled &= DSPRightCode.DISPOSITION_EDIT.isPermitted();
		final ModelContext context = client.getCurrentModel();
		if (context == null) {
			enabled = false;
		} else {
			final Collection<Auftrag> auftragList = FeinPlanungAbschlussUtil.getAuftragList(context, disponent, dspAbwicklungsArt);
			if (auftragList == null || auftragList.isEmpty()) {
				enabled = false;
			}
		}
		return enabled;
	}

}

package ch.eisenring.dispo.client.gui.components.dialog;

import ch.eisenring.core.tag.Tags;
import ch.eisenring.dispo.client.Client;

@SuppressWarnings("serial")
public abstract class AbstractDSPDialog extends ch.eisenring.app.client.gui.AbstractDialog<Client> {

	protected AbstractDSPDialog(final Client client, final Tags tags) {
		super(client, tags);
	}

	@Override
	protected void onOk() {
		client.getDnDHandler().cancelDrag();
		super.onOk();
	}

	@Override
	protected void onApply() {
		client.getDnDHandler().cancelDrag();
		super.onApply();
	}

	@Override
	protected void onCancel() {
		client.getDnDHandler().cancelDrag();
		super.onCancel();
	}

}

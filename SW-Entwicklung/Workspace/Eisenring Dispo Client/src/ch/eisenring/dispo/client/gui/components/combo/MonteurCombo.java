package ch.eisenring.dispo.client.gui.components.combo;

import static ch.eisenring.dispo.shared.codetables.EmploymentRole.MITARBEITER_MONTEUR;

import java.util.Collections;

import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.dispo.shared.DSPConstants;
import ch.eisenring.dispo.shared.model.AbstractMitarbeiter;
import ch.eisenring.dispo.shared.model.Auftrag;
import ch.eisenring.dispo.shared.model.Mitarbeiter;
import ch.eisenring.dispo.shared.model.ModelContext;
import ch.eisenring.gui.ValueEditorBinding;
import ch.eisenring.gui.components.HEAGComboBox;

@SuppressWarnings("serial")
public final class MonteurCombo extends HEAGComboBox<Object> implements DSPConstants {

	private final List<Mitarbeiter> mitarbeiterList = new ArrayList<Mitarbeiter>();
	
	public MonteurCombo() {
		this(null);
	}

	public MonteurCombo(final ValueEditorBinding binding) {
		super(binding);
	}

	private void addMitarbeiter(final Mitarbeiter mitarbeiter) {
		if (mitarbeiter != null) {
			if (!mitarbeiterList.contains(mitarbeiter)) {
				mitarbeiterList.add(mitarbeiter);
			}
		}
	}

	@SuppressWarnings("unchecked")
	private void setContext(final ModelContext context) {
		final List<Mitarbeiter> mList = (List) context.getModelList(Mitarbeiter.class);
		mitarbeiterList.clear();
		mitarbeiterList.add(null);
		for (int i=mList.size()-1; i>=0; --i) {
			final Mitarbeiter mitarbeiter = mList.get(i);
			if (mitarbeiter.getPresentoId() == MONT_PRESENTO_ID) {
				// "MONT" ist der Platzhaltermonteur aus LOGI,
				// er soll immer erscheinen (LUTO 16.03.2010)
				addMitarbeiter(mitarbeiter);
			} else if (mitarbeiter.getLogiwareId() != null) {
				if (MITARBEITER_MONTEUR.equals(mitarbeiter.getEmploymentRole())) {
					if (mitarbeiter.getEmployed()) {
						addMitarbeiter(mitarbeiter);
					}
				}
			}
		}
		// Füge der ComboBox auch alle Mitarbeiter hinzu, die als Monteur
		// auf einen Auftrag gesetzt sind, auch wenn es nach PRESENTO gar kein
		// Monteur ist.
		final List<Auftrag> aList = (List) context.getModelList(Auftrag.class);
		for (int i=aList.size()-1; i>=0; --i) {
			final Auftrag auftrag = aList.get(i);
			final Mitarbeiter mitarbeiter = auftrag.getChefMonteur();
			if (mitarbeiter != null && !mitarbeiterList.contains(mitarbeiter)) {
				addMitarbeiter(mitarbeiter);
			}
		}
	}

	public void setMonteur(ModelContext context, Mitarbeiter monteur) {
		setContext(context);
		if (monteur != null)
			addMitarbeiter(monteur);
		updateComboItems();
		setValue(monteur);
	}
	
	public Mitarbeiter getMonteur() {
		return (Mitarbeiter) getValue();
//		final Object selected = getSelectedItem();
//		if (selected instanceof Mitarbeiter) {
//			return (Mitarbeiter) selected;
//		} else {
//			return null;
//		}
	}
	
	@Override
	public Class<?> getValueClass() {
		return AbstractMitarbeiter.class;
	}

	@Override
	public void setValue(final Object value) {
		if (value instanceof ModelContext) {
			setContext((ModelContext) value);
			updateComboItems();
			super.setValue(null);
		} else if (value instanceof Mitarbeiter) {
			final Mitarbeiter monteur = (Mitarbeiter) value;
			setContext(monteur.getContext());
			addMitarbeiter(monteur);
			updateComboItems();
			super.setValue(monteur);
		} else {
			super.setValue(null);
		}
	}

	@Override
	public Object getValue() {
		final Object selected = super.getValue();
		return selected instanceof Mitarbeiter ? selected : null;
	}

	@SuppressWarnings("unchecked")
	private void updateComboItems() {
		Object selectedItem = getSelectedItem();
		removeAllItems();
		Collections.sort(mitarbeiterList, AbstractMitarbeiter.Order.FullName);
		for (int i=0; i<mitarbeiterList.size(); ++i) {
			addItem(mitarbeiterList.get(i));
		}
		setSelectedItem(selectedItem);
	}

}

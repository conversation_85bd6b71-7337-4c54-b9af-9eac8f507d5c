package ch.eisenring.dispo.client.gui.suche;

import java.awt.GridBagLayout;

import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.shared.codetables.DSPExtSearchInCode;
import ch.eisenring.dispo.shared.codetables.DSPExtSearchRangeCode;
import ch.eisenring.dispo.shared.network.packets.PacketSearchExtReply;
import ch.eisenring.dispo.shared.network.packets.PacketSearchExtRequest;
import ch.eisenring.dispo.shared.service.ExtSearchResult;
import ch.eisenring.gui.components.HEAGCodeComboBox;
import ch.eisenring.gui.components.HEAGLabel;
import ch.eisenring.gui.components.HEAGPanel;
import ch.eisenring.gui.components.HEAGTextField;
import ch.eisenring.gui.model.ValidationResults;
import ch.eisenring.gui.util.GUIUtil;
import ch.eisenring.gui.util.LayoutUtil;
import ch.eisenring.gui.window.AbstractBaseWindow;

@SuppressWarnings("serial")
public class ExtSearchPanel extends HEAGPanel {

	protected final Client client;
	
	protected final HEAGCodeComboBox<DSPExtSearchInCode> cmbSearchIn;
	protected final HEAGCodeComboBox<DSPExtSearchRangeCode> cmbDateRange;
	protected final HEAGTextField txtSearch;

	public ExtSearchPanel(final Client client) {
		this.client = client;
		this.cmbSearchIn = new HEAGCodeComboBox<DSPExtSearchInCode>(DSPExtSearchInCode.class);
		this.cmbDateRange = new HEAGCodeComboBox<DSPExtSearchRangeCode>(DSPExtSearchRangeCode.class);
		this.txtSearch = new HEAGTextField(60);
		initComponents();
		initLayout();
	}

	private void initComponents() {
		setLayout(new GridBagLayout());
		GUIUtil.setMinSizeForText(txtSearch, 25);
		
		cmbSearchIn.setValue(client.EXTSEARCH_INCODE.get(DSPExtSearchInCode.FEINPLANUNG));
		cmbDateRange.setValue(client.EXTSEARCH_RANGECODE.get(DSPExtSearchRangeCode.CURRENT_KW));
	}

	private void initLayout() {
		removeAll();
		final LayoutUtil l = new LayoutUtil(2);
		add(new HEAGLabel("Suchen nach"), l.label());
		add(cmbSearchIn, l.field());
		add(new HEAGLabel("Suchzeitraum"), l.label());
		add(cmbDateRange, l.field());
		add(new HEAGLabel("Suchworte"), l.label());
		add(txtSearch, l.field());
	}

	public PacketSearchExtRequest getSearchRequest() {
		final DSPExtSearchInCode inCode = cmbSearchIn.getSelectedCode();
		final DSPExtSearchRangeCode rangeCode = cmbDateRange.getSelectedCode();
		client.EXTSEARCH_INCODE.set(inCode);
		client.EXTSEARCH_RANGECODE.set(rangeCode);
		final PacketSearchExtRequest packet = PacketSearchExtRequest.create(
				inCode, rangeCode, txtSearch.getText());
		return packet;
	}

	public ErrorMessage processSearchResult(final PacketSearchExtReply reply) {
		final ErrorMessage message = reply.getMessage();
		if (message.isSuccess()) {
			final List<ExtSearchResult> list = reply.getResults();
			final int size = list.size();
			switch (size) {
				case 0:	
					return new ErrorMessage("Keine Suchergebnisse");
				case 1:
				default:
					final ExtSearchResultDialog dialog = new ExtSearchResultDialog(client, list);
					AbstractBaseWindow.showWindow(dialog, true);
					return message;
			}
		}
		return message;
	}

	@Override
	public void validateView(final ValidationResults results, final Object model) {
		super.validateView(results, model);
		if (AbstractCode.isNull(cmbSearchIn.getSelectedCode())) {
			results.add("Pflichtfeld, Wert auswählen", cmbSearchIn);
		}
		if (AbstractCode.isNull(cmbDateRange.getSelectedCode())) {
			results.add("Pflichtfeld, Wert auswählen", cmbDateRange);
		}
		if (Strings.isEmpty(txtSearch.getText())) {
			results.add("Suchbegriff fehlt", txtSearch);
		}
	}

}

package ch.eisenring.dispo.client.action;

import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.client.gui.report.fehlendefotos.FehlendeFotosDialog;
import ch.eisenring.dispo.client.resource.imgnew.Images;

public class ActionReportFehlendeFotos extends AbstractDSPAction {

	public ActionReportFehlendeFotos(final Client client) {
		super(client, Images.PHOTO, "Fehlende Fotos...");
	}

	@Override
	protected void performAction() {
		FehlendeFotosDialog.showDialog(client);
	}

}

package ch.eisenring.dispo.client;

import ch.eisenring.dispo.shared.PeriodicCallback;

public class ClientHouse<PERSON>eeper extends PeriodicCallback {

	private final Client client;
	private int checkCount;
	
	public ClientHouseKeeper(Client client) {
		super(125L);
		this.client = client;
	}

	public Client getClient() {
		return client;
	}
	
	public void start() {
		startCallback();
	}
	
	public void stop() {
		stopCallback();
	}

	@Override
	protected void configureThread(Thread thread) {
		thread.setPriority(Thread.NORM_PRIORITY);
		thread.setName("ClientHouseKeeper");
		thread.setDaemon(true);
	}
	
	@Override
	protected void periodicCallback() {
		++checkCount;
		if ((checkCount & 1) == 0) {
			getClient().postTick();
		}
		if ((checkCount & 63) == 1) {
			getClient().getContextCache().checkTimeouts();
		}
	}

}

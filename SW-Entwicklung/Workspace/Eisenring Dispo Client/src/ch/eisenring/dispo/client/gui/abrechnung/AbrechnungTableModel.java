package ch.eisenring.dispo.client.gui.abrechnung;

import java.text.DecimalFormat;
import java.text.NumberFormat;

import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.dispo.service.codetables.YesNoCode;
import ch.eisenring.dispo.shared.codetables.AbrechnungCode;
import ch.eisenring.dispo.shared.transfer.AbrechnungProxy;
import ch.eisenring.dispo.shared.transfer.AbrechnungProxy.Order;
import ch.eisenring.dispo.shared.util.AuftragUtil;
import ch.eisenring.gui.components.HEAGListTableModel;
import ch.eisenring.gui.components.HEAGTable;
import ch.eisenring.gui.components.HEAGTableLayout;
import ch.eisenring.gui.components.SortKey;
import ch.eisenring.logiware.code.pseudo.AbwicklungsartCode;
import ch.eisenring.logiware.code.soft.LWGranit2Code;

@SuppressWarnings("serial")
class AbrechnungTableModel extends HEAGListTableModel<AbrechnungProxy> {

	private final static HEAGTableLayout LAYOUT; static {
		final HEAGTableLayout l = new HEAGTableLayout("AbrechnungTableLayout", 0, new SortKey(0), new SortKey(1));
		l.addColumn("Kom-Nr.", 56, Order.Projektnummer);
		l.addColumn("Auftr-Nr.", 56, Order.Auftragnummer);
		l.addColumn("AbwArt", 56, HEAGTable.Renderer.Centered, Order.Abwicklungsart);
		l.addColumn("Obj.-Art", 64, 32, 256, Order.Granit2);
		l.addColumn("Anz", 32, HEAGTable.Renderer.Centered, Order.ObjektKuechenAnzahl);
		l.addColumn("Mont.Abr.", 64, HEAGTable.Renderer.Centered, Order.MontageAbgerechnet);
		l.addColumn("Verr.", 48, HEAGTable.Renderer.Centered, Order.Abgerechnet);
		l.addColumn("Bezeichnung", 320, 32, 1024, Order.Projektbezeichnung);
		l.addColumn("Punktwert", 64, 32, 256, HEAGTable.Renderer.RightAligned, Order.Punktwert);
		l.addColumn("Basis", 56, Order.Basisnummer);
		l.addColumn("Ausführung", 192, 32, 1024, Order.Ausfuehrung);
		l.addColumn("Ausführung Front", 192, 32, 1024, Order.AusfuehrungFront);
		l.addColumn("Monteur", 128, 32, 1024, Order.Monteure);
		l.addColumn("Bewertung", 160, 32, 1024, Order.BewertungPercent);
		l.addColumn("Kü.-Ende", 72, HEAGTable.Renderer.Centered, Order.Abschlussdatum);
		l.addColumn("Abladen/h", 40, 32, 128, HEAGTable.Renderer.RightAligned, Order.Abladen);
		l.addColumn("Wu.-Dat.", 72, HEAGTable.Renderer.Centered, Order.Wunschdatum);
		l.addColumn("OB", 160, 32, 256, HEAGTable.Renderer.LeftAligned, Order.Objektbetreuer);
		LAYOUT = l;
	}

	public AbrechnungTableModel() {
		super(LAYOUT);
	}

	final DecimalFormat FORMAT_PUNKTWERT = new DecimalFormat("#0.00");

	@Override
	public Object getColumnValue(final AbrechnungProxy rowObject, final int columnIndex) {
		if (rowObject == null)
			return null;
		switch (columnIndex) {
			default: return "???";
			case 0:	return rowObject.getProjektnummer();
			case 1:	return Strings.toString(rowObject.getAuftragnummer());
			case 2: {
				final AbwicklungsartCode code = rowObject.getAbwicklungsart();
				final int id = AbstractCode.getId(code, 0);
				return Strings.toString(id);
			}
			case 3: {
				final LWGranit2Code code = rowObject.getGranitschluessel2();
				return AbstractCode.getLongText(code, "");
			}
			case 4: return rowObject.getObjektKuechenAnzahl();
			case 5: {
				final YesNoCode code = rowObject.getMontageAbgerechnet();
				return AbstractCode.getLongText(code, YesNoCode.NO.getLongText());
			}
			case 6: {
				final AbrechnungCode code = rowObject.getAbgerechnet();
				return AbstractCode.getLongText(code, AbrechnungCode.NO.getLongText());
			}
			case 7: return Strings.toSingleLine(rowObject.getProjektbezeichnung());
			case 8: return FORMAT_PUNKTWERT.format(rowObject.getPunktwert());
			case 9: return Strings.trim(rowObject.getBasisnummer());
			case 10: return Strings.trim(rowObject.getAusfuehrung());
			case 11: return Strings.trim(rowObject.getAusfuehrungFront());
			case 12: return rowObject.getMonteure();
			case 13: return AuftragUtil.formatKuechenBewertungPercent(rowObject.getKuechenBewertungPercent());
			case 14: return TimestampUtil.DATE10.format(rowObject.getAbschlussdatum());
			case 15:
				final NumberFormat format = new DecimalFormat("#0.0");
				return format.format(rowObject.getAbladen());
			case 16: return TimestampUtil.DATE10.format(rowObject.getWunschdatum());
			case 17: return AbstractCode.getLongText(rowObject.getObjektbetreuer(), "");
		}
	}

	public List<AbrechnungProxy> getContent() {
		return new ArrayList<>(modelList);
	}

}

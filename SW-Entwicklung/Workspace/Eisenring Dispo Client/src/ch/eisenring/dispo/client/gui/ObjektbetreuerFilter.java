package ch.eisenring.dispo.client.gui;

import java.util.Iterator;

import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.collections.Lookup;
import ch.eisenring.core.collections.impl.AtomicArrayLookup;
import ch.eisenring.core.datatypes.primitives.Primitives;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.StringMakerFriendly;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.iterators.Filter;
import ch.eisenring.core.observable.Observable;
import ch.eisenring.core.observable.Observer;
import ch.eisenring.core.observable.ObserverNotificationPolicy;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.logiware.code.soft.LWObjektBetreuerCode;

public final class ObjektbetreuerFilter implements Filter<LWObjektBetreuerCode>, StringMakerFriendly {

	private final static char SEPARATOR = '\0';

	private final static String DEFAULT_SETTING = "???";

	private final Lookup<String, Boolean> lookup = new AtomicArrayLookup<>();
	private final Client client;

	private final Observer<?> observer = new Observer<Object>() {
		@Override
		public void observableChanged(final Observable<Object> observable) {
			final String newValue = client.OBJEKTBETREUER_FILTER_SETTINGS.get();
			final String oldValue = ObjektbetreuerFilter.this.toString();
			if (!Strings.equals(newValue, oldValue)) {
				fromString(newValue);
			}
		}
	};

	public ObjektbetreuerFilter(final Client client) {
		this.client = client;
		client.OBJEKTBETREUER_FILTER_SETTINGS.addObserver(observer, ObserverNotificationPolicy.SYNCHRONOUS);
		client.OBJEKTBETREUER_FAVORITEN1.addObserver(observer, ObserverNotificationPolicy.SYNCHRONOUS);
	}

	public ObjektbetreuerFilter(final String value) {
		this.client = null;
		fromString(value);
	}
	
	private static String getObjektbetreuerKey(final LWObjektBetreuerCode objektbetreuer) {
		final String key = AbstractCode.getKey(objektbetreuer, (String) null);
		return key == null ? "???" : key; 
	}

	// --------------------------------------------------------------
	// ---
	// --- Utilitiy method for getting value
	// ---
	// --------------------------------------------------------------
	public static String get(final Observable<String> observable) {
		return observable.get(DEFAULT_SETTING);
	}

	// --------------------------------------------------------------
	// ---
	// --- Add / Remove
	// ---
	// --------------------------------------------------------------
	public void set(final LWObjektBetreuerCode objektbetreuer, final boolean accepted) {
		final String key = getObjektbetreuerKey(objektbetreuer);
		final Boolean newValue = Boolean.valueOf(accepted);
		final Boolean oldValue = lookup.put(key, newValue);
		if (client != null && !Primitives.equals(newValue, oldValue)) {
			client.OBJEKTBETREUER_FILTER_SETTINGS.set(toString());
		}
	}
	
	// --------------------------------------------------------------
	// ---
	// --- Filter implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public boolean accepts(final LWObjektBetreuerCode objektbetreuer) {
		if (client != null && Boolean.TRUE.equals(client.OBJEKTBETREUER_FILTER_BYPASS.get()))
			return true;
		final String key = getObjektbetreuerKey(objektbetreuer);
		Boolean result = lookup.get(key);
		return result == null ? false : result.booleanValue();
	}

	// --------------------------------------------------------------
	// ---
	// --- to/from String
	// ---
	// --------------------------------------------------------------
	@Override
	public void toStringMaker(final StringMaker target) {
		boolean first = true;
		final Iterator<String> keyItr = lookup.keyIterator();
		while (keyItr.hasNext()) {
			final String key = keyItr.next();
			final Boolean value = lookup.get(key);
			if (!Boolean.TRUE.equals(value))
				continue;
			if (first) {
				first = false;
			} else {
				target.append(SEPARATOR);
			}
			target.append(key);
		}
	}

	public String toString() {
		return StringMakerFriendly.toString(this);
	}

	public void fromString(final CharSequence charSeq) {
		lookup.clear();
		if (!Strings.isEmpty(charSeq)) {
			final String[] keys = Strings.explode(charSeq, SEPARATOR);
			for (final String key : keys) {
				lookup.put(key, Boolean.TRUE);
			}
		}
	}

}

package ch.eisenring.dispo.client.action;

import static ch.eisenring.dispo.shared.codetables.EmploymentRole.MITARBEITER_CHAUFEUR;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.client.gui.components.dialog.OldAbstractDialog;
import ch.eisenring.dispo.client.gui.virtual.VirtualCreateDialog;
import ch.eisenring.dispo.client.resource.imgnew.Images;
import ch.eisenring.dispo.shared.codetables.DSPRightCode;

public class ActionCreateVirtualChauffeur extends AbstractDSPAction {
	
	public ActionCreateVirtualChauffeur(final Client client) {
		super(client, "Chauffeur Platzhalter anlegen...", Images.EMPLOYEE_TEAL);
		addPermissionObserver();
	}
	
	@Override
	protected void performAction() {
		OldAbstractDialog dialog = new VirtualCreateDialog(client, MITARBEITER_CHAUFEUR);
		dialog.setVisible(true);
	}
	
	@Override
	protected boolean isEnabledImpl() {
		return DSPRightCode.VIRTUALCHAUFFEUR.isPermitted();
	}

}

package ch.eisenring.dispo.client.gui.monteurteam;

import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.dispo.shared.pojo.DSPMonteurTeam;
import ch.eisenring.gui.components.HEAGListTableModel;
import ch.eisenring.gui.components.HEAGTableLayout;
import ch.eisenring.gui.components.SortKey;

@SuppressWarnings("serial")
public class MonteurTeamTableModel extends HEAGListTableModel<DSPMonteurTeam> {

	private final static HEAGTableLayout LAYOUT;
	static {
		HEAGTableLayout l = new HEAGTableLayout("MonteurTeamTableLayout", 0,
				new SortKey(1));
		l.addColumn("GSE", 64, DSPMonteurTeam.Order.TeamGSE);
		l.addColumn("Team-Bezeichnung", 512, 64, 4096, DSPMonteurTeam.Order.TeamName);
		LAYOUT = l;
	}

	public MonteurTeamTableModel() {
		super(LAYOUT);
	}

	@Override
	public Object getColumnValue(final DSPMonteurTeam team, final int columnIndex) {
		if (team == null)
			return "???";
		switch (columnIndex) {
			default: return "???";
			case 0: return AbstractCode.getShortText(team.getTeamGSE(), "???");
			case 1: return team.getTeamName();
		}
	}

}

package ch.eisenring.dispo.client.gui.employee;

import java.awt.BorderLayout;

import javax.swing.JScrollPane;
import javax.swing.ListSelectionModel;
import javax.swing.ScrollPaneConstants;
import javax.swing.event.ListSelectionEvent;
import javax.swing.event.ListSelectionListener;

import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.dispo.client.gui.IModelPanel;
import ch.eisenring.dispo.shared.model.AbstractModel;
import ch.eisenring.dispo.shared.model.MasterMitarbeiter;
import ch.eisenring.dispo.shared.model.MitarbeiterEinAus;
import ch.eisenring.dispo.shared.model.MitarbeiterEmploymentRecord;
import ch.eisenring.gui.components.HEAGPanel;
import ch.eisenring.gui.components.HEAGTable;

@SuppressWarnings("serial")
public class EmployeeEinAusTable extends HEAGPanel implements IModelPanel {

	protected final JScrollPane scrollPane = new JScrollPane();
	protected EmployeeEinAusTableModel tableModel = new EmployeeEinAusTableModel((MasterMitarbeiter) null);
	protected final HEAGTable table = new HEAGTable();
	protected MasterMitarbeiter employee;
	protected MitarbeiterEmploymentRecord employment;

	public EmployeeEinAusTable() {
		initComponents();
		initLayout();
	}

	private void initComponents() {
		setLayout(new BorderLayout());
		scrollPane.setViewportView(table);
		scrollPane.setHorizontalScrollBarPolicy(ScrollPaneConstants.HORIZONTAL_SCROLLBAR_NEVER);
		scrollPane.setVerticalScrollBarPolicy(ScrollPaneConstants.VERTICAL_SCROLLBAR_ALWAYS);
	}

	private void initLayout() {
		removeAll();
		add(scrollPane, BorderLayout.CENTER);
	}

	public List<MitarbeiterEinAus> getSelection() {
		final int[] indices = table.getSelectedRows();
		final List<MitarbeiterEinAus> selection = new ArrayList<MitarbeiterEinAus>(indices.length);
		for (int i=0; i<indices.length; ++i) {
			final MitarbeiterEinAus einAus = tableModel.getRow(indices[i]);
			selection.add(einAus);
		}
		return selection;
	}
	
	// --------------------------------------------------------------
	// ---
	// --- Hooks intended for override
	// ---
	// --------------------------------------------------------------
	public void selectionChanged() {
	}

	// --------------------------------------------------------------
	// ---
	// --- IModelPanel implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public void setEditable(final boolean editable) {
		super.setEditable(editable);
	}

	@Override
	public void updateModel() {
		updateModel(employee);
	}

	@Override
	public void updateModel(final Object model) {
		super.updateModel(model);
		if (model instanceof MasterMitarbeiter) {
			employee = (MasterMitarbeiter) model;
			employee.setEmployment(new MitarbeiterEmploymentRecord(employment));
		}
	}

	@Override
	public void setModel(AbstractModel model) {
		updateView(model);
	}

	@Override
	public void updateView() {
		updateView(employee);
	}

	@Override
	public void updateView(final Object model) {
		super.updateView(model);
		if (model instanceof MasterMitarbeiter) {
			employee = (MasterMitarbeiter) model;
			if (employment == null) {
				employment = new MitarbeiterEmploymentRecord(employee.getEmployment());
			}
			tableModel = new EmployeeEinAusTableModel(employment);
			table.setModel(tableModel);
			table.getSelectionModel().addListSelectionListener(new ListSelectionListener() {
				@Override
				public void valueChanged(final ListSelectionEvent event) {
					selectionChanged();
				}
			});
			table.getSelectionModel().setSelectionMode(ListSelectionModel.MULTIPLE_INTERVAL_SELECTION);
			selectionChanged();
			tableModel.applyLayout(table);
			validate();
		}
	}

}

package ch.eisenring.dispo.client.action;

import java.awt.event.InputEvent;

import javax.swing.KeyStroke;

import ch.eisenring.dispo.client.Client;

public final class ActionSetGridDayCount extends AbstractDSPAction {

	private final int dayCount;
	
	public ActionSetGridDayCount(final Client client, final int dayCount) {
		super(client, KeyStroke.getKeyStroke('W', InputEvent.CTRL_DOWN_MASK),
				getLabelText(dayCount));
		this.dayCount = dayCount;
	}

	private static String getLabelText(final int dayCount) {
		switch (dayCount) {
			case 6:
				return "Samstag zeigen/planen";
			case 7:
				return "Sonntag zeigen/planen";
			default:
				return "???";
		}
	}

	public static int getCurrentDayCount(final Client client) {
		final int shownDays = client.STATE_SHOWNDAYS.get(Integer.valueOf(5));
		return shownDays;
	}
	
	public int getCurrentDayCount() {
		return getCurrentDayCount(client);
	}

	@Override
	protected void performAction() {
		final int currentDayCount = getCurrentDayCount();
		if (currentDayCount != dayCount) {
			client.STATE_SHOWNDAYS.set(Integer.valueOf(dayCount));
		} else {
			client.STATE_SHOWNDAYS.set(Integer.valueOf(5));
		}
	}

	@Override
	protected boolean isCheckedImpl() {
		return dayCount == getCurrentDayCount();
	}

}

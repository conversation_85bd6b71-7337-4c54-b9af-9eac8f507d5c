package ch.eisenring.dispo.client.action;

import ch.eisenring.core.collections.Set;
import ch.eisenring.core.collections.impl.ArraySet;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.client.resource.EMailTemplates;
import ch.eisenring.dispo.shared.codetables.AbrufModus;
import ch.eisenring.dispo.shared.network.packets.PacketBestellInfosRequest;
import ch.eisenring.dispo.shared.transfer.DSPAuftragInterface;

public final class ActionMeldungAbruf extends AbstractDSPAction {

	private final Object[] models;
	private final long callForDate;

	public ActionMeldungAbruf(final Client client, final Object[] models, final long callForDate) {
		super(client, EMailTemplates.EMAIL_GERAETE_ABRUFEN.getMenuLabel());
		this.callForDate = callForDate;
		this.models = models;
	}

	@Override
	protected void performAction() {
		final Set<String> selection = new ArraySet<>(models.length);
		for (final Object model : models) {
			if (model instanceof DSPAuftragInterface) {
				final DSPAuftragInterface auftrag = (DSPAuftragInterface) model;
				selection.add(auftrag.getProjektnummer());
			}
		}
		if (selection.isEmpty())
			return;
		final PacketBestellInfosRequest request = PacketBestellInfosRequest.create(callForDate, AbrufModus.ABRUFEN);
		request.addProjektnummern(selection);
		client.sendPacket(request);
	}
	
}

package ch.eisenring.dispo.client.action;

import static ch.eisenring.dispo.shared.codetables.ZuordnungCode.ZUORDNUNG_OBSOLETE;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.client.gui.grid.BlockElement;
import ch.eisenring.dispo.client.gui.grid.GridElement;
import ch.eisenring.dispo.shared.model.AbstractModel;
import ch.eisenring.dispo.shared.model.Auftrag;
import ch.eisenring.dispo.shared.model.FeinZuordnung;
import ch.eisenring.dispo.shared.model.ModelContext;

public final class ActionElementReconnect extends ActionElementBase {

	public ActionElementReconnect(final Client client, final GridElement gridElement) {
		super(client, gridElement,
				"Verbindung zum Auftrag _Wiederherstellen");
	}

	@Override
	protected void performBlockAction(BlockElement blockElement) {
		if (isEnabledImpl()) {
			ActionReconnectObsolete.reconnect(getFeinZuordnung(getBlockElement()));
		}
	}

	@Override
	protected boolean isEnabledImpl() {
		return super.isEnabledImpl() && 
			   canReconnect(getBlockElement());
	}

	public static FeinZuordnung getFeinZuordnung(BlockElement blockElement) {
		final AbstractModel model = blockElement.getModel();
		if (model instanceof FeinZuordnung)
			return (FeinZuordnung) model;
		return null;
	}

	public static boolean canReconnect(BlockElement blockElement) {
		return canReconnect(getFeinZuordnung(blockElement));
	}

	public static boolean canReconnect(FeinZuordnung zuordnung) {
		if (null==zuordnung)
			return false;
		if (!ZUORDNUNG_OBSOLETE.equals(zuordnung.getType()))
			return false;
		final ModelContext context = zuordnung.getContext();
		final int auftragNummer = zuordnung.getAuftragNummer();
		if (auftragNummer != 0)
			return false;
		final Auftrag auftrag = context.getAuftragByAuftragsNummer(auftragNummer);
		return null!=auftrag;
	}

}

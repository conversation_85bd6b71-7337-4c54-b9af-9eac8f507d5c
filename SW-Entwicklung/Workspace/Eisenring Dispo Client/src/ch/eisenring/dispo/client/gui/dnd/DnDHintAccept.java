package ch.eisenring.dispo.client.gui.dnd;

import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.client.gui.grid.GridElement;

/**
 * The drag'n'drop system relies on hints to decide how an element 
 * is treated by the system. Each element carries three hints
 * associated with it:
 *
 * - DnDHintDrop which describes how the element is treated when
 *   it is dropped onto another element.
 * - DndHintAccept which implements the execution of a drop
 *   onto the element.
 * - DnDHintDrag which describes if the element can be dragged. 
 */
public abstract class DnDHintAccept {

	protected final Client client;
	
	protected DnDHintAccept(final Client client) {
		this.client = client;
	}

	/**
	 * This method decides if the dropped element will be accepted
	 * as dropped element by this accept hint.
	 */
	public abstract boolean isAcceptable(GridElement thisElement, GridElement dropElement);

	/**
	 * This method can be overwritten if the element
	 * does not handle drops itself, but has a delegate
	 * element which handles the drop.
	 * 
	 * This method is called *after* accepts() has been called,
	 * when the drop really takes place. The target element
	 * can supply another element that receives the drop here.
	 * 
	 * Usually BlockElement uses this to delegate the drop
	 * to its LineElement.
	 */
	public GridElement getDropDelegate(GridElement thisElement) {
		return thisElement;
	}

	/**
	 * Performs the actual drop operation
	 */
	public abstract void performDrop(DnDEvent event);

	/**
	 * Determines if copy is suppressed on drop
	 */
	public boolean suppressCopy() {
		return false;
	}

}

package ch.eisenring.dispo.client.gui.abrechnung;

import java.awt.Component;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.awt.event.MouseListener;

import ch.eisenring.core.collections.Collection;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.client.action.ActionMeldungAbstract;
import ch.eisenring.dispo.client.resource.EMailTemplates;
import ch.eisenring.email.client.resource.images.Images;
import ch.eisenring.gui.components.IconButton;
import ch.eisenring.gui.menu.MenuBuilder;

@SuppressWarnings("serial")
class AbrechnungEMailButton extends IconButton {

	private final Client client;
	private final AbrechnungWindow window;
	
	private final MouseListener mouseListener = new MouseAdapter() {
		@Override
		public void mouseReleased(final MouseEvent event) {
			if (event.isConsumed())
				return;
			if (event.isPopupTrigger())
				showMenu(event);
		}
		@Override
		public void mousePressed(final MouseEvent event) {
			if (event.isConsumed())
				return;
			if (event.isPopupTrigger())
				showMenu(event);
		}
		@Override
		public void mouseClicked(final MouseEvent event) {
			if (event.isConsumed())
				return;
			if (event.getButton() != MouseEvent.BUTTON1)
				showMenu(event);
		}
	};

	public AbrechnungEMailButton(final Client client, final AbrechnungWindow window) {
		super(Images.EMAIL.getIcon(32));
		this.client = client;
		this.window = window;
		setFocusable(false);
		addMouseListener(mouseListener);
	}

	@Override
	public void buttonClicked() {
		showMenu(new MouseEvent(this, 0, System.currentTimeMillis(), 0, 0, 0, 1, true));
	}

	protected void showMenu(final MouseEvent event) {
		if (event.isConsumed())
			return;
		event.consume();
		final Collection<?> selection = window.getSelection();
		if (selection == null || selection.isEmpty())
			return;
		final MenuBuilder b = getMenu(client, selection, window);
		b.showPopupMenu(event);
	}
	
	public static MenuBuilder getMenu(final Client client, final Collection<?> models, final Component parentWindow) {
		final List<ActionMeldungAbstract> actions = getActions(client, models);
		final MenuBuilder b = new MenuBuilder();
		for (final ActionMeldungAbstract action : actions) {
			action.setParentWindow(parentWindow);
			b.add(action);
		}
		return b;
	}

	public static List<ActionMeldungAbstract> getActions(final Client client, final Collection<?> models) {
		final Object[] auftragArray = models.toArray(); 
		final List<ActionMeldungAbstract> result = new ArrayList<ActionMeldungAbstract>();
		result.add(new ActionMeldungAbstract(client, EMailTemplates.EMAIL_ABRUF_GLAS_MGT, auftragArray));
		result.add(new ActionMeldungAbstract(client, EMailTemplates.EMAIL_ABRUF_GLAS_KELLERGLAS, auftragArray));
		result.add(new ActionMeldungAbstract(client, EMailTemplates.EMAIL_ABRUF_GLAS_GLASVETIA, auftragArray));
		result.add(new ActionMeldungAbstract(client, EMailTemplates.EMAIL_ANZEIGE_ENDMONTAGE_MULTI, auftragArray));
		result.add(new ActionMeldungAbstract(client, EMailTemplates.EMAIL_ANZEIGE_ENDMONTAGE_STATUSRAPPORT, auftragArray));
		result.add(new ActionMeldungAbstract(client, EMailTemplates.EMAIL_ANZEIGE_MONTAGE, auftragArray));
		result.add(new ActionMeldungAbstract(client, EMailTemplates.EMAIL_ANZEIGE_MONTAGE_OB, auftragArray));
		return result;
	}

}

package ch.eisenring.dispo.client.gui.components.button;

import java.awt.Graphics;
import java.awt.Graphics2D;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.awt.event.MouseListener;
import java.util.Calendar;

import ch.eisenring.core.datatypes.date.HEAGCalendar;
import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.observable.Observable;
import ch.eisenring.core.observable.Observer;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.client.action.ActionKWGoto;
import ch.eisenring.dispo.client.action.ActionKWPrev;
import ch.eisenring.dispo.client.gui.look.ILook;
import ch.eisenring.gui.components.IconButton;
import ch.eisenring.gui.menu.MenuBuilder;
import ch.eisenring.gui.resource.images.Images;

@SuppressWarnings("serial")
public class KWPrevImageButton extends IconButton {
	
	private final Client client;
	private int kw;
	private int year;
	private final ActionKWPrev action;

	private final MouseListener mouseListener = new MouseAdapter() {
		@Override
		public void mousePressed(final MouseEvent event) {
			this.mouseReleased(event);
		}
		
		@Override
		public void mouseReleased(final MouseEvent event) {
			if (!event.isConsumed() && event.isPopupTrigger()) {
				final MenuBuilder popup = ActionKWGoto.getKWGotoPrev10Popup(client, TimestampUtil.NULL_TIMESTAMP);
				popup.showPopupMenu(event);
			}
		}
	};

	protected final Observer<Object> observer = new Observer<Object>() {
		@Override
		public void observableChanged(final Observable<Object> observeable) {
			updateState();
			updateToolTip();
			repaint();
		}
	};

	public void buttonClicked() {
		action.fire();
	}

	public KWPrevImageButton(final Client client) {
		super(Images.BACK.getIcon(32));
		this.client = client;
		this.action = new ActionKWPrev(client);
		client.STATE_KWDATE.addObserver(observer);
		setFocusable(false);
		addMouseListener(mouseListener);
	}

	protected void updateState() {
		setEnabled(action.isEnabled());
		final HEAGCalendar c = HEAGCalendar.obtain(client.STATE_KWDATE.get());
		c.add(Calendar.WEEK_OF_YEAR, -1);
		kw = c.get(Calendar.WEEK_OF_YEAR);
		year = c.get(Calendar.YEAR);
		c.release();
	}
	
	protected void updateToolTip() {
		final String tooltip = Strings.concat("Vorhergehende Kalenderwoche (", kw, "/", year, ")");
		setToolTipText(tooltip);
	}

	@Override
	protected void paintComponent(Graphics g) {
		super.paintComponent(g);
		final int x = 1;
		final int y = 1;
		final int w = 16;
		final int h = 16;
		final ILook look = client.GRIDLOOK.get();
		look.text((Graphics2D) g, Strings.toString(kw), x, y, w, h, ILook.JUSTIFY_CENTER);
	}

}
package ch.eisenring.dispo.client.gui.massmutation;

import java.awt.Container;
import java.awt.GridBagLayout;
import java.awt.event.FocusAdapter;
import java.awt.event.FocusEvent;

import javax.swing.JComponent;

import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.shared.codetables.AuftragPrintStatusCode;
import ch.eisenring.dispo.shared.model.AbstractZuordnung;
import ch.eisenring.dispo.shared.model.Auftrag;
import ch.eisenring.gui.GridBagConstraints;
import ch.eisenring.gui.components.DecorationHeader;
import ch.eisenring.gui.components.HEAGCheckBox;
import ch.eisenring.gui.components.HEAGCodeComboBox;
import ch.eisenring.gui.components.HEAGDateField;
import ch.eisenring.gui.components.HEAGLabel;
import ch.eisenring.gui.components.HEAGPanel;
import ch.eisenring.gui.components.Separator;
import ch.eisenring.gui.model.ValidationResults;
import ch.eisenring.gui.resource.images.Images;
import ch.eisenring.logiware.code.hard.LWPlanungStatusCode;
import ch.eisenring.logiware.code.soft.LWDienstSirCode;
import ch.eisenring.logiware.code.soft.LWDisponentSirCode;

@SuppressWarnings("serial")
public class DSPMassMutationPanel extends HEAGPanel {

	static class MMField<T extends JComponent> extends FocusAdapter {
		public final HEAGCheckBox checkbox = new HEAGCheckBox();
		public final HEAGLabel label;
		public final T component;
		
		public MMField(final T component, final String labelText) {
			this.label = new HEAGLabel(labelText);
			this.component = component;
			component.addFocusListener(this);
		}
		
		@Override
		public void focusGained(final FocusEvent event) {
			if (event.isTemporary())
				return;
			checkbox.setSelected(true);
		}

		public boolean isSelected() {
			return checkbox.isSelected();
		}

		public void addTo(final Container container, final int y) {
			container.add(checkbox, GridBagConstraints.fixed(0, y).anchor(GridBagConstraints.CENTER));
			container.add(label, GridBagConstraints.label(1, y));
			container.add(component, GridBagConstraints.fixed(2, y));
		}
	}

	final Client client;
	final MMField<HEAGDateField> datWunsch = new MMField<>(new HEAGDateField(), "Wunschdatum");
	final MMField<HEAGDateField> datAbschluss = new MMField<>(new HEAGDateField(), "Abschlussdatum");
	final MMField<HEAGDateField> datLieferung = new MMField<>(new HEAGDateField(), "Lieferdatum Küche");
	final MMField<HEAGCodeComboBox<LWPlanungStatusCode>> cmbStatus = new MMField<>(new HEAGCodeComboBox<>(LWPlanungStatusCode.class), "Planungstatus");
	final MMField<HEAGCodeComboBox<LWDisponentSirCode>> cmbDisponent = new MMField<>(new HEAGCodeComboBox<>(LWDisponentSirCode.class), "Disponent");
	final MMField<HEAGCheckBox> ckbGedruckt = new MMField<>(new HEAGCheckBox(), "Gedruckt");
	final MMField<HEAGCodeComboBox<LWDienstSirCode>> cmbDienstSir = new MMField<>(new HEAGCodeComboBox<>(LWDienstSirCode.class), "Dienstl. Sirnach");
	
	public DSPMassMutationPanel(final Client client) {
		this.client = client;
		initComponents();
		initLayout();
	}

	private void initComponents() {
		setLayout(new GridBagLayout());
		datWunsch.component.setValue(System.currentTimeMillis());
		datAbschluss.component.setValue(System.currentTimeMillis());
		datLieferung.component.setValue(TimestampUtil.NULL_TIMESTAMP_LONG);
		cmbStatus.component.setValue(LWPlanungStatusCode.GEPLANT);
		cmbDisponent.component.setValue(client.getCurrentDisponent());
	}

	private void initLayout() {
		int y = 0;
		
		add(new DecorationHeader(
				DecorationHeader.LABEL, "Mehrfachmutation",
				DecorationHeader.ICON, Images.EDIT,
				DecorationHeader.SIZE, DecorationHeader.MEDIUM,
				DecorationHeader.SEPARATOR, Boolean.TRUE), GridBagConstraints.decorator(0, y));

		++y;
		add(new HEAGLabel("Mutieren?", false), GridBagConstraints.label(0, y).anchor(GridBagConstraints.CENTER));
		add(new HEAGLabel("Feld / Eigenschaft", false), GridBagConstraints.label(1, y).anchor(GridBagConstraints.WEST));
		add(new HEAGLabel("zu Wert", false), GridBagConstraints.label(2, y).anchor(GridBagConstraints.WEST));
		
		++y;
		add(Separator.create(), GridBagConstraints.separator(0, y));

		datWunsch.addTo(this, ++y);
		datAbschluss.addTo(this, ++y);
		datLieferung.addTo(this, ++y);
		cmbStatus.addTo(this, ++y);
		cmbDisponent.addTo(this, ++y);
		ckbGedruckt.addTo(this, ++y);
		cmbDienstSir.addTo(this, ++y);
	}

	@Override
	public void validateView(final ValidationResults results, final Object model) {
		super.validateView(results, model);
		if (datWunsch.isSelected()) {
			if (TimestampUtil.isNull(datWunsch.component.getTimestamp())) {
				results.add("Wunschdatum darf nicht leer sein", datWunsch.component);
			}
		}
	}

	/**
	 * Apply mutation to element
	 */
	protected void applyTo(final AbstractZuordnung zuordnung) {
		applyTo(zuordnung.getAuftrag());
	}

	protected void applyTo(final Auftrag auftrag) {
		if (auftrag == null)
			return;
		if (datWunsch.isSelected()) {
			final long timestamp = datWunsch.component.getTimestamp();
			auftrag.setWunschdatum(timestamp);
		}
		if (datAbschluss.isSelected()) {
			final long timestamp = datAbschluss.component.getTimestamp();
			auftrag.setAbschlussdatum(timestamp);
		}
		if (datLieferung.isSelected()) {
			final long timestamp = datLieferung.component.getTimestamp();
			auftrag.setAbbruchDatum(timestamp);
		}
		if (cmbStatus.isSelected()) {
			final LWPlanungStatusCode status = cmbStatus.component.getSelectedCode();
			auftrag.setPlanungStatus(status);
		}
		if (cmbDisponent.isSelected()) {
			final LWDisponentSirCode disponent = cmbDisponent.component.getSelectedCode();
			auftrag.setDisponent(disponent);
		}
		if (ckbGedruckt.isSelected()) {
			final AuftragPrintStatusCode code = ckbGedruckt.component.isSelected()
					? AuftragPrintStatusCode.PRINTED : AuftragPrintStatusCode.NULL;
			auftrag.setAuftragPrintStatus(code);
		}
		if (cmbDienstSir.isSelected()) {
			final LWDienstSirCode code = cmbDienstSir.component.getSelectedCode();
			auftrag.setDienstSir(code);
		}
	}

}

package ch.eisenring.dispo.client.gui.report.endmontagen;

import java.awt.BorderLayout;
import java.awt.Dimension;

import ch.eisenring.core.tag.TagSet;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.client.gui.components.dialog.AbstractDSPDialog;
import ch.eisenring.dispo.client.resource.imgnew.Images;
import ch.eisenring.gui.model.StoreResults;
import ch.eisenring.gui.window.SACButtonPanel;
import ch.eisenring.gui.window.WindowTags;
import ch.eisenring.network.packet.AbstractPacket;

@SuppressWarnings("serial")
public class ReportEndmontagenDialog extends AbstractDSPDialog {

	protected final SACButtonPanel buttonPanel = new SACButtonPanel();
	protected final ReportEndmontagenPanel paramPanel;
	
	public ReportEndmontagenDialog(final Client client) {
		super(client, new TagSet(
				WindowTags.ICON, Images.LINEGRAPH,
				WindowTags.TITLE, "Auswertung Endmontagen",
				WindowTags.MINIMUM_SIZE, new Dimension(400, 160)
		));
		this.paramPanel = new ReportEndmontagenPanel(client);
		initComponents();
		initLayout();
		pack();
	}

	private void initComponents() {
		setLayout(new BorderLayout());
		buttonPanel.configureSaveButton("Auswerten", "Auswertung starten");
		buttonPanel.configureApplyButton(null, null);
	}

	private void initLayout() {
		add(paramPanel, BorderLayout.CENTER);
		add(buttonPanel, BorderLayout.SOUTH);
	}

	@Override
	public void storeView(final StoreResults results, final Object model) {
		super.storeView(results, model);
		final AbstractPacket request = paramPanel.getRequest();
		if (request != null)
			client.sendPacket(request, false);
	}

}

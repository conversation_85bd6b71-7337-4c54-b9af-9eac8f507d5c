package ch.eisenring.dispo.client.gui.monteurteam;

import java.awt.Component;

import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.client.action.AbstractDSPAction;
import ch.eisenring.dispo.client.resource.imgnew.Images;
import ch.eisenring.dispo.shared.codetables.DSPRightCode;
import ch.eisenring.gui.util.GUIUtil;

public class ActionMonteurTeamDialog extends AbstractDSPAction {

	public ActionMonteurTeamDialog(final Client client) {
		super(client, Images.MONTEUR_TEAM, "Monteure Teams...");
		addPermissionObserver();
	}

	@Override
	protected void performAction() {
		GUIUtil.invokeLater(new Runnable() {
			@Override
			public void run() {
				MonteurTeamMaintainDialog.showDialog(client, (Component) client.getMainWindow());
			}
		});
	}
	
	@Override
	protected boolean isEnabledImpl() {
		return super.isEnabledImpl() && DSPRightCode.MITARBEITER_BEARBEITEN.isPermitted();
	}

}

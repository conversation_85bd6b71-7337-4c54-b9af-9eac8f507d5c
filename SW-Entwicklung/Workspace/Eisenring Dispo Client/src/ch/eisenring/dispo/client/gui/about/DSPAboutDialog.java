package ch.eisenring.dispo.client.gui.about;

import static ch.eisenring.dispo.shared.DSPConstants.APPLICATION_NAME;
import static ch.eisenring.dispo.shared.DSPConstants.VERSION_STRING;

import java.awt.BorderLayout;
import java.awt.Dimension;

import ch.eisenring.core.tag.TagSet;
import ch.eisenring.dispo.client.Client;
import ch.eisenring.dispo.client.gui.components.dialog.AbstractDSPDialog;
import ch.eisenring.dispo.client.resource.imgnew.Images;
import ch.eisenring.dispo.client.util.MiscUtil;
import ch.eisenring.gui.panels.AboutPanel;
import ch.eisenring.gui.window.DialogTags;
import ch.eisenring.gui.window.WindowTags;

@SuppressWarnings("serial")
public class DSPAboutDialog extends AbstractDSPDialog {

	private final AboutPanel aboutPanel;
	private final DSPAboutPanel dspPanel;
	
	public DSPAboutDialog(final Client client) {
		super(client, new TagSet(
				DialogTags.DIALOG_OWNER, client.getMainWindow(),
				WindowTags.ICON, Images.ABOUT,
				WindowTags.MINIMUM_SIZE, new Dimension(320, 440),
				WindowTags.TITLE, APPLICATION_NAME + " " + VERSION_STRING));
		this.dspPanel = new DSPAboutPanel(client);
		this.aboutPanel = new AboutPanel(dspPanel);
		initComponents();
		initLayout();
		pack();
		MiscUtil.ENABLED = true;
	}

	private void initComponents() {
		setLayout(new BorderLayout());
	}
	
	private void initLayout() {
		getContentPane().removeAll();
		add(aboutPanel, BorderLayout.CENTER);
	}

	@Override
	protected void onCancel() {
		super.onCancel();
		setVisible(false);
	}

}

#!/bin/bash

infile="build.log"
flatfile="build_flat.log"
summary="deprecation_summary.txt"

echo "Flattening deprecation warnings..."

# Step 1: Flatten javac warning lines
awk '
  /^\[javac\]/ {
    if (prev != "") {
      merged = prev " " $0
      print merged
      if (merged ~ /\[deprecation\]/) {
        print merged > "'"$summary"'"
      }
      prev = ""
    } else {
      prev = $0
    }
    next
  }
  {
    if (prev != "") {
      merged = prev " " $0
      print merged
      if (merged ~ /\[deprecation\]/) {
        print merged > "'"$summary"'"
      }
      prev = ""
    } else {
      print $0
    }
  }
  END {
    if (prev != "") print prev
  }
' "$infile" > "$flatfile"

# Step 2: Extract only the deprecation lines
grep "\[deprecation\]" "$summary" > tmp_dep.log

# Step 3: Summarize deprecated API usage
echo "" > "$summary"
echo "📄 Deprecation Summary" >> "$summary"
echo "======================" >> "$summary"
echo "" >> "$summary"

count=$(wc -l < tmp_dep.log)
echo "Total deprecation warnings: $count" >> "$summary"
echo "" >> "$summary"

echo "Unique deprecated members:" >> "$summary"
grep -o "\[deprecation\] [^ ]*" tmp_dep.log | sort | uniq >> "$summary"

echo "" >> "$summary"
echo "First few warnings:" >> "$summary"
head -n 10 tmp_dep.log >> "$summary"

# Cleanup
rm -f tmp_dep.log

echo "✅ Summary written to $summary"

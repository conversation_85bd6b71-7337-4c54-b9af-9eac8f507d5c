Buildfile: C:\HEAG Java Apps\SW-Entwicklung\Workspace\Eisenring DMS Client\build.xml

clean:
   [delete] Deleting directory C:\HEAG Java Apps\SW-Entwicklung\Workspace\Eisenring DMS Client\ant_build

compile:
    [mkdir] Created dir: C:\HEAG Java Apps\SW-Entwicklung\Workspace\Eisenring DMS Client\ant_build\classes
    [javac] Compiling 2390 source files to C:\HEAG Java Apps\SW-Entwicklung\Workspace\Eisenring DMS Client\ant_build\classes
    [javac] Hinweis: Die Annotationsverarbeitung ist aktiviert, da mindestens ein Prozessor im
    [javac]   Classpath gefunden wurde. In einem zuk�nftigen Release von javac kann die Annotationsverarbeitung deaktiviert werden,
    [javac]   es sei denn, mindestens ein Prozessor ist namentlich angegeben (-processor), oder ein Suchpfad
    [javac]   ist angegeben (--processor-path, --processor-module-path), oder die Annotationsverarbeitung
    [javac]   wurde explizit aktiviert (-proc:only, -proc:full).
    [javac]   Verwenden Sie "-Xlint:-options", um diese Meldung zu unterdr�cken.
    [javac]   Verwenden Sie "-proc:none", um die Annotationsverarbeitung zu deaktivieren.
    [javac] C:\HEAG Java Apps\SW-Entwicklung\Workspace\Eisenring Core\src\ch\eisenring\core\codetype\CodeType.java:345: Warnung: [deprecation] isAccessible() in AccessibleObject ist veraltet
    [javac] 						if (!constructor.isAccessible())
    [javac] 						                ^
    [javac] C:\HEAG Java Apps\SW-Entwicklung\Workspace\Eisenring Core\src\ch\eisenring\core\codetype\DynamicCode.java:56: Warnung: [deprecation] isAccessible() in AccessibleObject ist veraltet
    [javac] 			if (!constructor.isAccessible())
    [javac] 			                ^
    [javac] C:\HEAG Java Apps\SW-Entwicklung\Workspace\Eisenring Core\src\ch\eisenring\core\codetype\DynamicCode.java:73: Warnung: [deprecation] isAccessible() in AccessibleObject ist veraltet
    [javac] 			if (!constructor.isAccessible())
    [javac] 			                ^
    [javac] C:\HEAG Java Apps\SW-Entwicklung\Workspace\Eisenring Core\src\ch\eisenring\core\reflect\BinaryClassLoader.java:52: Warnung: [deprecation] getPackage(String) in ClassLoader ist veraltet
    [javac] 				if (getPackage(packageName) == null) {
    [javac] 				    ^
    [javac] C:\HEAG Java Apps\SW-Entwicklung\Workspace\Eisenring Core\src\ch\eisenring\core\reflect\FieldAccessorUnsafe.java:13: Warnung: Unsafe ist eine interne propriet�re API, die in einem zuk�nftigen Release entfernt werden kann
    [javac] 	final static sun.misc.Unsafe UNSAFE; static {
    [javac] 	                     ^
    [javac] C:\HEAG Java Apps\SW-Entwicklung\Workspace\Eisenring Core\src\ch\eisenring\core\reflect\FieldAccessorUnsafe.java:13: Warnung: Unsafe ist eine interne propriet�re API, die in einem zuk�nftigen Release entfernt werden kann
    [javac] 	final static sun.misc.Unsafe UNSAFE; static {
    [javac] 	                     ^
    [javac] C:\HEAG Java Apps\SW-Entwicklung\Workspace\Eisenring Core\src\ch\eisenring\core\reflect\FieldAccessorUnsafe.java:13: Warnung: Unsafe ist eine interne propriet�re API, die in einem zuk�nftigen Release entfernt werden kann
    [javac] 	final static sun.misc.Unsafe UNSAFE; static {
    [javac] 	                     ^
    [javac] C:\HEAG Java Apps\SW-Entwicklung\Workspace\Eisenring Core\src\ch\eisenring\core\reflect\FieldAccessorUnsafe.java:19: Warnung: Unsafe ist eine interne propriet�re API, die in einem zuk�nftigen Release entfernt werden kann
    [javac] 		UNSAFE = (sun.misc.Unsafe) UnsafeProvider.getUnsafe();
    [javac] 		                  ^
    [javac] C:\HEAG Java Apps\SW-Entwicklung\Workspace\Eisenring Core\src\ch\eisenring\core\reflect\FieldAccessorUnsafe.java:28: Warnung: [deprecation] objectFieldOffset(Field) in Unsafe ist veraltet
    [javac] 		offset = UNSAFE.objectFieldOffset(field);
    [javac] 		               ^
    [javac] C:\HEAG Java Apps\SW-Entwicklung\Workspace\Eisenring Core\src\ch\eisenring\core\resource\ResourceInitializer.java:70: Warnung: [deprecation] isAccessible() in AccessibleObject ist veraltet
    [javac] 					if (!field.isAccessible())
    [javac] 					          ^
    [javac] C:\HEAG Java Apps\SW-Entwicklung\Workspace\Eisenring Core\src\ch\eisenring\core\util\DeepCloneable.java:75: Warnung: [deprecation] isAccessible() in AccessibleObject ist veraltet
    [javac] 		if (cloneMethod != null && !cloneMethod.isAccessible())
    [javac] 		                                       ^
    [javac] C:\HEAG Java Apps\SW-Entwicklung\Workspace\Eisenring Core\src\ch\eisenring\core\util\sizeof\MemSize.java:81: Warnung: [deprecation] isAccessible() in AccessibleObject ist veraltet
    [javac] 				if (!field.isAccessible())
    [javac] 				          ^
    [javac] C:\HEAG Java Apps\SW-Entwicklung\Workspace\Eisenring Network\src\ch\eisenring\network\ClassListPacketDispatcher.java:50: Warnung: [deprecation] isAccessible() in AccessibleObject ist veraltet
    [javac] 			if (!constructor.isAccessible())
    [javac] 			                ^
    [javac] C:\HEAG Java Apps\SW-Entwicklung\Workspace\Eisenring Model Shared\src\ch\eisenring\model\Model.java:315: Warnung: [deprecation] isAccessible() in AccessibleObject ist veraltet
    [javac] 			if (!field.isAccessible())
    [javac] 			          ^
    [javac] C:\HEAG Java Apps\SW-Entwicklung\Workspace\Eisenring PRT Shared\src\ch\eisenring\print\shared\job\PrintReportJobBase.java:174: Warnung: [deprecation] JRExporter in net.sf.jasperreports.engine ist veraltet
    [javac] 			final JRExporter exporter = new JRPrintServiceExporter();
    [javac] 			      ^
    [javac] C:\HEAG Java Apps\SW-Entwicklung\Workspace\Eisenring PRT Shared\src\ch\eisenring\print\shared\job\PrintReportJobBase.java:177: Warnung: [deprecation] JRExporterParameter in net.sf.jasperreports.engine ist veraltet
    [javac] 				exporter.setParameter(JRExporterParameter.JASPER_PRINT, prints.get(0));
    [javac] 				                      ^
    [javac] C:\HEAG Java Apps\SW-Entwicklung\Workspace\Eisenring PRT Shared\src\ch\eisenring\print\shared\job\PrintReportJobBase.java:177: Warnung: [deprecation] JASPER_PRINT in JRExporterParameter ist veraltet
    [javac] 				exporter.setParameter(JRExporterParameter.JASPER_PRINT, prints.get(0));
    [javac] 				                                         ^
    [javac] C:\HEAG Java Apps\SW-Entwicklung\Workspace\Eisenring PRT Shared\src\ch\eisenring\print\shared\job\PrintReportJobBase.java:177: Warnung: [deprecation] setParameter(JRExporterParameter,Object) in JRExporter ist veraltet
    [javac] 				exporter.setParameter(JRExporterParameter.JASPER_PRINT, prints.get(0));
    [javac] 				        ^
    [javac] C:\HEAG Java Apps\SW-Entwicklung\Workspace\Eisenring PRT Shared\src\ch\eisenring\print\shared\job\PrintReportJobBase.java:179: Warnung: [deprecation] JRExporterParameter in net.sf.jasperreports.engine ist veraltet
    [javac] 				exporter.setParameter(JRExporterParameter.JASPER_PRINT_LIST, prints);
    [javac] 				                      ^
    [javac] C:\HEAG Java Apps\SW-Entwicklung\Workspace\Eisenring PRT Shared\src\ch\eisenring\print\shared\job\PrintReportJobBase.java:179: Warnung: [deprecation] JASPER_PRINT_LIST in JRExporterParameter ist veraltet
    [javac] 				exporter.setParameter(JRExporterParameter.JASPER_PRINT_LIST, prints);
    [javac] 				                                         ^
    [javac] C:\HEAG Java Apps\SW-Entwicklung\Workspace\Eisenring PRT Shared\src\ch\eisenring\print\shared\job\PrintReportJobBase.java:179: Warnung: [deprecation] setParameter(JRExporterParameter,Object) in JRExporter ist veraltet
    [javac] 				exporter.setParameter(JRExporterParameter.JASPER_PRINT_LIST, prints);
    [javac] 				        ^
    [javac] C:\HEAG Java Apps\SW-Entwicklung\Workspace\Eisenring PRT Shared\src\ch\eisenring\print\shared\job\PrintReportJobBase.java:181: Warnung: [deprecation] JRPrintServiceExporterParameter in net.sf.jasperreports.engine.export ist veraltet
    [javac] 			exporter.setParameter(JRPrintServiceExporterParameter.PRINT_SERVICE, printer.getPrintService());
    [javac] 			                      ^
    [javac] C:\HEAG Java Apps\SW-Entwicklung\Workspace\Eisenring PRT Shared\src\ch\eisenring\print\shared\job\PrintReportJobBase.java:181: Warnung: [deprecation] PRINT_SERVICE in JRPrintServiceExporterParameter ist veraltet
    [javac] 			exporter.setParameter(JRPrintServiceExporterParameter.PRINT_SERVICE, printer.getPrintService());
    [javac] 			                                                     ^
    [javac] C:\HEAG Java Apps\SW-Entwicklung\Workspace\Eisenring PRT Shared\src\ch\eisenring\print\shared\job\PrintReportJobBase.java:181: Warnung: [deprecation] setParameter(JRExporterParameter,Object) in JRExporter ist veraltet
    [javac] 			exporter.setParameter(JRPrintServiceExporterParameter.PRINT_SERVICE, printer.getPrintService());
    [javac] 			        ^
    [javac] C:\HEAG Java Apps\SW-Entwicklung\Workspace\Eisenring PRT Shared\src\ch\eisenring\print\shared\job\PrintReportJobBase.java:182: Warnung: [deprecation] JRPrintServiceExporterParameter in net.sf.jasperreports.engine.export ist veraltet
    [javac] 			exporter.setParameter(JRPrintServiceExporterParameter.PRINT_SERVICE_ATTRIBUTE_SET, printer.getPrintService().getAttributes());
    [javac] 			                      ^
    [javac] C:\HEAG Java Apps\SW-Entwicklung\Workspace\Eisenring PRT Shared\src\ch\eisenring\print\shared\job\PrintReportJobBase.java:182: Warnung: [deprecation] PRINT_SERVICE_ATTRIBUTE_SET in JRPrintServiceExporterParameter ist veraltet
    [javac] 			exporter.setParameter(JRPrintServiceExporterParameter.PRINT_SERVICE_ATTRIBUTE_SET, printer.getPrintService().getAttributes());
    [javac] 			                                                     ^
    [javac] C:\HEAG Java Apps\SW-Entwicklung\Workspace\Eisenring PRT Shared\src\ch\eisenring\print\shared\job\PrintReportJobBase.java:182: Warnung: [deprecation] setParameter(JRExporterParameter,Object) in JRExporter ist veraltet
    [javac] 			exporter.setParameter(JRPrintServiceExporterParameter.PRINT_SERVICE_ATTRIBUTE_SET, printer.getPrintService().getAttributes());
    [javac] 			        ^
    [javac] C:\HEAG Java Apps\SW-Entwicklung\Workspace\Eisenring PRT Shared\src\ch\eisenring\print\shared\job\PrintReportJobBase.java:183: Warnung: [deprecation] JRPrintServiceExporterParameter in net.sf.jasperreports.engine.export ist veraltet
    [javac] 			exporter.setParameter(JRPrintServiceExporterParameter.PRINT_REQUEST_ATTRIBUTE_SET, printRequestAttributeSet);
    [javac] 			                      ^
    [javac] C:\HEAG Java Apps\SW-Entwicklung\Workspace\Eisenring PRT Shared\src\ch\eisenring\print\shared\job\PrintReportJobBase.java:183: Warnung: [deprecation] PRINT_REQUEST_ATTRIBUTE_SET in JRPrintServiceExporterParameter ist veraltet
    [javac] 			exporter.setParameter(JRPrintServiceExporterParameter.PRINT_REQUEST_ATTRIBUTE_SET, printRequestAttributeSet);
    [javac] 			                                                     ^
    [javac] C:\HEAG Java Apps\SW-Entwicklung\Workspace\Eisenring PRT Shared\src\ch\eisenring\print\shared\job\PrintReportJobBase.java:183: Warnung: [deprecation] setParameter(JRExporterParameter,Object) in JRExporter ist veraltet
    [javac] 			exporter.setParameter(JRPrintServiceExporterParameter.PRINT_REQUEST_ATTRIBUTE_SET, printRequestAttributeSet);
    [javac] 			        ^
    [javac] C:\HEAG Java Apps\SW-Entwicklung\Workspace\Eisenring PRT Shared\src\ch\eisenring\print\shared\job\PrintReportJobBase.java:184: Warnung: [deprecation] JRPrintServiceExporterParameter in net.sf.jasperreports.engine.export ist veraltet
    [javac] 			exporter.setParameter(JRPrintServiceExporterParameter.DISPLAY_PAGE_DIALOG, Boolean.FALSE);
    [javac] 			                      ^
    [javac] C:\HEAG Java Apps\SW-Entwicklung\Workspace\Eisenring PRT Shared\src\ch\eisenring\print\shared\job\PrintReportJobBase.java:184: Warnung: [deprecation] DISPLAY_PAGE_DIALOG in JRPrintServiceExporterParameter ist veraltet
    [javac] 			exporter.setParameter(JRPrintServiceExporterParameter.DISPLAY_PAGE_DIALOG, Boolean.FALSE);
    [javac] 			                                                     ^
    [javac] C:\HEAG Java Apps\SW-Entwicklung\Workspace\Eisenring PRT Shared\src\ch\eisenring\print\shared\job\PrintReportJobBase.java:184: Warnung: [deprecation] setParameter(JRExporterParameter,Object) in JRExporter ist veraltet
    [javac] 			exporter.setParameter(JRPrintServiceExporterParameter.DISPLAY_PAGE_DIALOG, Boolean.FALSE);
    [javac] 			        ^
    [javac] C:\HEAG Java Apps\SW-Entwicklung\Workspace\Eisenring PRT Shared\src\ch\eisenring\print\shared\job\PrintReportJobBase.java:185: Warnung: [deprecation] JRPrintServiceExporterParameter in net.sf.jasperreports.engine.export ist veraltet
    [javac] 			exporter.setParameter(JRPrintServiceExporterParameter.DISPLAY_PRINT_DIALOG, Boolean.FALSE);
    [javac] 			                      ^
    [javac] C:\HEAG Java Apps\SW-Entwicklung\Workspace\Eisenring PRT Shared\src\ch\eisenring\print\shared\job\PrintReportJobBase.java:185: Warnung: [deprecation] DISPLAY_PRINT_DIALOG in JRPrintServiceExporterParameter ist veraltet
    [javac] 			exporter.setParameter(JRPrintServiceExporterParameter.DISPLAY_PRINT_DIALOG, Boolean.FALSE);
    [javac] 			                                                     ^
    [javac] C:\HEAG Java Apps\SW-Entwicklung\Workspace\Eisenring PRT Shared\src\ch\eisenring\print\shared\job\PrintReportJobBase.java:185: Warnung: [deprecation] setParameter(JRExporterParameter,Object) in JRExporter ist veraltet
    [javac] 			exporter.setParameter(JRPrintServiceExporterParameter.DISPLAY_PRINT_DIALOG, Boolean.FALSE);
    [javac] 			        ^
    [javac] C:\HEAG Java Apps\SW-Entwicklung\Workspace\Eisenring PRT Shared\src\ch\eisenring\print\shared\job\PrintReportJobBase.java:232: Warnung: [deprecation] JRExporterParameter in net.sf.jasperreports.engine ist veraltet
    [javac] 				exporter.setParameter(JRExporterParameter.OUTPUT_STREAM, memOut);
    [javac] 				                      ^
    [javac] C:\HEAG Java Apps\SW-Entwicklung\Workspace\Eisenring PRT Shared\src\ch\eisenring\print\shared\job\PrintReportJobBase.java:232: Warnung: [deprecation] OUTPUT_STREAM in JRExporterParameter ist veraltet
    [javac] 				exporter.setParameter(JRExporterParameter.OUTPUT_STREAM, memOut);
    [javac] 				                                         ^
    [javac] C:\HEAG Java Apps\SW-Entwicklung\Workspace\Eisenring PRT Shared\src\ch\eisenring\print\shared\job\PrintReportJobBase.java:232: Warnung: [deprecation] setParameter(JRExporterParameter,Object) in JRAbstractExporter ist veraltet
    [javac] 				exporter.setParameter(JRExporterParameter.OUTPUT_STREAM, memOut);
    [javac] 				        ^
    [javac] C:\HEAG Java Apps\SW-Entwicklung\Workspace\Eisenring PRT Shared\src\ch\eisenring\print\shared\job\PrintReportJobBase.java:233: Warnung: [deprecation] JRExporterParameter in net.sf.jasperreports.engine ist veraltet
    [javac] 				exporter.setParameter(JRExporterParameter.JASPER_PRINT, prints.get(0));
    [javac] 				                      ^
    [javac] C:\HEAG Java Apps\SW-Entwicklung\Workspace\Eisenring PRT Shared\src\ch\eisenring\print\shared\job\PrintReportJobBase.java:233: Warnung: [deprecation] JASPER_PRINT in JRExporterParameter ist veraltet
    [javac] 				exporter.setParameter(JRExporterParameter.JASPER_PRINT, prints.get(0));
    [javac] 				                                         ^
    [javac] C:\HEAG Java Apps\SW-Entwicklung\Workspace\Eisenring PRT Shared\src\ch\eisenring\print\shared\job\PrintReportJobBase.java:233: Warnung: [deprecation] setParameter(JRExporterParameter,Object) in JRAbstractExporter ist veraltet
    [javac] 				exporter.setParameter(JRExporterParameter.JASPER_PRINT, prints.get(0));
    [javac] 				        ^
    [javac] C:\HEAG Java Apps\SW-Entwicklung\Workspace\Eisenring PRT Shared\src\ch\eisenring\print\shared\job\PrintReportJobBase.java:238: Warnung: [deprecation] JRExporterParameter in net.sf.jasperreports.engine ist veraltet
    [javac] 				exporter.setParameter(JRExporterParameter.OUTPUT_STREAM, memOut);
    [javac] 				                      ^
    [javac] C:\HEAG Java Apps\SW-Entwicklung\Workspace\Eisenring PRT Shared\src\ch\eisenring\print\shared\job\PrintReportJobBase.java:238: Warnung: [deprecation] OUTPUT_STREAM in JRExporterParameter ist veraltet
    [javac] 				exporter.setParameter(JRExporterParameter.OUTPUT_STREAM, memOut);
    [javac] 				                                         ^
    [javac] C:\HEAG Java Apps\SW-Entwicklung\Workspace\Eisenring PRT Shared\src\ch\eisenring\print\shared\job\PrintReportJobBase.java:238: Warnung: [deprecation] setParameter(JRExporterParameter,Object) in JRAbstractExporter ist veraltet
    [javac] 				exporter.setParameter(JRExporterParameter.OUTPUT_STREAM, memOut);
    [javac] 				        ^
    [javac] C:\HEAG Java Apps\SW-Entwicklung\Workspace\Eisenring PRT Shared\src\ch\eisenring\print\shared\job\PrintReportJobBase.java:239: Warnung: [deprecation] JRExporterParameter in net.sf.jasperreports.engine ist veraltet
    [javac] 				exporter.setParameter(JRExporterParameter.JASPER_PRINT_LIST, prints);
    [javac] 				                      ^
    [javac] C:\HEAG Java Apps\SW-Entwicklung\Workspace\Eisenring PRT Shared\src\ch\eisenring\print\shared\job\PrintReportJobBase.java:239: Warnung: [deprecation] JASPER_PRINT_LIST in JRExporterParameter ist veraltet
    [javac] 				exporter.setParameter(JRExporterParameter.JASPER_PRINT_LIST, prints);
    [javac] 				                                         ^
    [javac] C:\HEAG Java Apps\SW-Entwicklung\Workspace\Eisenring PRT Shared\src\ch\eisenring\print\shared\job\PrintReportJobBase.java:239: Warnung: [deprecation] setParameter(JRExporterParameter,Object) in JRAbstractExporter ist veraltet
    [javac] 				exporter.setParameter(JRExporterParameter.JASPER_PRINT_LIST, prints);
    [javac] 				        ^
    [javac] C:\HEAG Java Apps\SW-Entwicklung\Workspace\Eisenring PRT Client\src\ch\eisenring\print\client\gui\AbstractPrintDialog.java:196: Warnung: [deprecation] JRExporterParameter in net.sf.jasperreports.engine ist veraltet
    [javac] 			exporter.setParameter(JRExporterParameter.JASPER_PRINT, jrPrint);
    [javac] 			                      ^
    [javac] C:\HEAG Java Apps\SW-Entwicklung\Workspace\Eisenring PRT Client\src\ch\eisenring\print\client\gui\AbstractPrintDialog.java:196: Warnung: [deprecation] JASPER_PRINT in JRExporterParameter ist veraltet
    [javac] 			exporter.setParameter(JRExporterParameter.JASPER_PRINT, jrPrint);
    [javac] 			                                         ^
    [javac] C:\HEAG Java Apps\SW-Entwicklung\Workspace\Eisenring PRT Client\src\ch\eisenring\print\client\gui\AbstractPrintDialog.java:196: Warnung: [deprecation] setParameter(JRExporterParameter,Object) in JRAbstractExporter ist veraltet
    [javac] 			exporter.setParameter(JRExporterParameter.JASPER_PRINT, jrPrint);
    [javac] 			        ^
    [javac] C:\HEAG Java Apps\SW-Entwicklung\Workspace\Eisenring PRT Client\src\ch\eisenring\print\client\gui\AbstractPrintDialog.java:197: Warnung: [deprecation] JRPrintServiceExporterParameter in net.sf.jasperreports.engine.export ist veraltet
    [javac] 			exporter.setParameter(JRPrintServiceExporterParameter.PRINT_SERVICE, printer.getPrintService());
    [javac] 			                      ^
    [javac] C:\HEAG Java Apps\SW-Entwicklung\Workspace\Eisenring PRT Client\src\ch\eisenring\print\client\gui\AbstractPrintDialog.java:197: Warnung: [deprecation] PRINT_SERVICE in JRPrintServiceExporterParameter ist veraltet
    [javac] 			exporter.setParameter(JRPrintServiceExporterParameter.PRINT_SERVICE, printer.getPrintService());
    [javac] 			                                                     ^
    [javac] C:\HEAG Java Apps\SW-Entwicklung\Workspace\Eisenring PRT Client\src\ch\eisenring\print\client\gui\AbstractPrintDialog.java:197: Warnung: [deprecation] setParameter(JRExporterParameter,Object) in JRAbstractExporter ist veraltet
    [javac] 			exporter.setParameter(JRPrintServiceExporterParameter.PRINT_SERVICE, printer.getPrintService());
    [javac] 			        ^
    [javac] C:\HEAG Java Apps\SW-Entwicklung\Workspace\Eisenring PRT Client\src\ch\eisenring\print\client\gui\AbstractPrintDialog.java:198: Warnung: [deprecation] JRPrintServiceExporterParameter in net.sf.jasperreports.engine.export ist veraltet
    [javac] 			exporter.setParameter(JRPrintServiceExporterParameter.PRINT_SERVICE_ATTRIBUTE_SET, printer.getPrintService().getAttributes());
    [javac] 			                      ^
    [javac] C:\HEAG Java Apps\SW-Entwicklung\Workspace\Eisenring PRT Client\src\ch\eisenring\print\client\gui\AbstractPrintDialog.java:198: Warnung: [deprecation] PRINT_SERVICE_ATTRIBUTE_SET in JRPrintServiceExporterParameter ist veraltet
    [javac] 			exporter.setParameter(JRPrintServiceExporterParameter.PRINT_SERVICE_ATTRIBUTE_SET, printer.getPrintService().getAttributes());
    [javac] 			                                                     ^
    [javac] C:\HEAG Java Apps\SW-Entwicklung\Workspace\Eisenring PRT Client\src\ch\eisenring\print\client\gui\AbstractPrintDialog.java:198: Warnung: [deprecation] setParameter(JRExporterParameter,Object) in JRAbstractExporter ist veraltet
    [javac] 			exporter.setParameter(JRPrintServiceExporterParameter.PRINT_SERVICE_ATTRIBUTE_SET, printer.getPrintService().getAttributes());
    [javac] 			        ^
    [javac] C:\HEAG Java Apps\SW-Entwicklung\Workspace\Eisenring PRT Client\src\ch\eisenring\print\client\gui\AbstractPrintDialog.java:199: Warnung: [deprecation] JRPrintServiceExporterParameter in net.sf.jasperreports.engine.export ist veraltet
    [javac] 			exporter.setParameter(JRPrintServiceExporterParameter.PRINT_REQUEST_ATTRIBUTE_SET, printRequestAttributeSet);
    [javac] 			                      ^
    [javac] C:\HEAG Java Apps\SW-Entwicklung\Workspace\Eisenring PRT Client\src\ch\eisenring\print\client\gui\AbstractPrintDialog.java:199: Warnung: [deprecation] PRINT_REQUEST_ATTRIBUTE_SET in JRPrintServiceExporterParameter ist veraltet
    [javac] 			exporter.setParameter(JRPrintServiceExporterParameter.PRINT_REQUEST_ATTRIBUTE_SET, printRequestAttributeSet);
    [javac] 			                                                     ^
    [javac] C:\HEAG Java Apps\SW-Entwicklung\Workspace\Eisenring PRT Client\src\ch\eisenring\print\client\gui\AbstractPrintDialog.java:199: Warnung: [deprecation] setParameter(JRExporterParameter,Object) in JRAbstractExporter ist veraltet
    [javac] 			exporter.setParameter(JRPrintServiceExporterParameter.PRINT_REQUEST_ATTRIBUTE_SET, printRequestAttributeSet);
    [javac] 			        ^
    [javac] C:\HEAG Java Apps\SW-Entwicklung\Workspace\Eisenring PRT Client\src\ch\eisenring\print\client\gui\AbstractPrintDialog.java:200: Warnung: [deprecation] JRPrintServiceExporterParameter in net.sf.jasperreports.engine.export ist veraltet
    [javac] 			exporter.setParameter(JRPrintServiceExporterParameter.DISPLAY_PAGE_DIALOG, Boolean.FALSE);
    [javac] 			                      ^
    [javac] C:\HEAG Java Apps\SW-Entwicklung\Workspace\Eisenring PRT Client\src\ch\eisenring\print\client\gui\AbstractPrintDialog.java:200: Warnung: [deprecation] DISPLAY_PAGE_DIALOG in JRPrintServiceExporterParameter ist veraltet
    [javac] 			exporter.setParameter(JRPrintServiceExporterParameter.DISPLAY_PAGE_DIALOG, Boolean.FALSE);
    [javac] 			                                                     ^
    [javac] C:\HEAG Java Apps\SW-Entwicklung\Workspace\Eisenring PRT Client\src\ch\eisenring\print\client\gui\AbstractPrintDialog.java:200: Warnung: [deprecation] setParameter(JRExporterParameter,Object) in JRAbstractExporter ist veraltet
    [javac] 			exporter.setParameter(JRPrintServiceExporterParameter.DISPLAY_PAGE_DIALOG, Boolean.FALSE);
    [javac] 			        ^
    [javac] C:\HEAG Java Apps\SW-Entwicklung\Workspace\Eisenring PRT Client\src\ch\eisenring\print\client\gui\AbstractPrintDialog.java:201: Warnung: [deprecation] JRPrintServiceExporterParameter in net.sf.jasperreports.engine.export ist veraltet
    [javac] 			exporter.setParameter(JRPrintServiceExporterParameter.DISPLAY_PRINT_DIALOG, Boolean.FALSE);
    [javac] 			                      ^
    [javac] C:\HEAG Java Apps\SW-Entwicklung\Workspace\Eisenring PRT Client\src\ch\eisenring\print\client\gui\AbstractPrintDialog.java:201: Warnung: [deprecation] DISPLAY_PRINT_DIALOG in JRPrintServiceExporterParameter ist veraltet
    [javac] 			exporter.setParameter(JRPrintServiceExporterParameter.DISPLAY_PRINT_DIALOG, Boolean.FALSE);
    [javac] 			                                                     ^
    [javac] C:\HEAG Java Apps\SW-Entwicklung\Workspace\Eisenring PRT Client\src\ch\eisenring\print\client\gui\AbstractPrintDialog.java:201: Warnung: [deprecation] setParameter(JRExporterParameter,Object) in JRAbstractExporter ist veraltet
    [javac] 			exporter.setParameter(JRPrintServiceExporterParameter.DISPLAY_PRINT_DIALOG, Boolean.FALSE);
    [javac] 			        ^
    [javac] C:\HEAG Java Apps\SW-Entwicklung\Workspace\Eisenring DMS Client\src\ch\eisenring\dms\client\gui\tree\FolderPanel.java:151: Fehler: Symbol nicht gefunden
    [javac] 		inputMap.put(KeyStroke.getKeyStroke(KeyEvent.VK_V, InputEvent.CTRL_DWON_MASK),
    [javac] 		                                                             ^
    [javac]   Symbol: Variable CTRL_DWON_MASK
    [javac]   Ort: Klasse InputEvent
    [javac] Hinweis: Einige Eingabedateien verwenden nicht gepr�fte oder unsichere Vorg�nge.
    [javac] Hinweis: Wiederholen Sie die Kompilierung mit -Xlint:unchecked, um Details zu erhalten.
    [javac] 1 Fehler
    [javac] 66 Warnungen

package ch.eisenring.dms.client.action;

import ch.eisenring.core.collections.Collection;
import ch.eisenring.dms.client.DMSClient;
import ch.eisenring.dms.client.async.PrintAction;
import ch.eisenring.dms.service.api.DMSObject;
import ch.eisenring.dms.service.codetables.DMSObjectType;
import ch.eisenring.dms.shared.client.util.FilePrintType;
import ch.eisenring.dms.shared.client.util.FileType;
import ch.eisenring.dms.shared.codetables.DMSObjectTypeProperties;
import ch.eisenring.print.shared.resource.images.Images;

public final class ActionFilePrint extends ActionFileAbstract {

	public ActionFilePrint(final DMSClient client, final Object gui,
						   final Collection<DMSObject> documents) {
		super(client, gui, "Drucken", Images.PRINTER);
		addDocuments(documents);
	}

	public ActionFilePrint(final DMSClient client, final Object gui,
			   			   final DMSObject document) {
		super(client, gui, "Drucken", Images.PRINTER);
		addDocument(document);
	}

	@Override
	protected boolean isEnabledImpl() {
		if (!super.isEnabledImpl())
			return false;
		// check permission on all documents
		boolean printable = false;
		for (final DMSObject object : documents) {
			final DMSObjectType type = object.getType();
			if (!DMSObjectType.FILE.equals(type))
				return false;
			final DMSObjectTypeProperties properties = DMSObjectTypeProperties.get(type);
			if (properties == null || !objectCache.isPermitted(object, properties.getViewPermissionCode()))
				return false;
			final FilePrintType printType = FileType.getPrintType(object);
			printable |= (FilePrintType.PRINT_INTERNAL.equals(printType)) ||
			             (FilePrintType.PRINT_OS.equals(printType));
		}
		return printable;
	}

	@Override
	protected void performAction() {
		final PrintAction printAction = new PrintAction(client);
		printAction.addDocuments(documents);
		printAction.start();
	}

}

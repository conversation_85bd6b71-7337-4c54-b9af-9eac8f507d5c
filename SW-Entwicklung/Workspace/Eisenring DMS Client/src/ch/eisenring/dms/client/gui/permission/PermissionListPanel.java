package ch.eisenring.dms.client.gui.permission;

import java.awt.GridBagLayout;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.awt.event.MouseListener;
import java.util.Collections;

import javax.swing.JButton;
import javax.swing.JList;
import javax.swing.JScrollPane;
import javax.swing.ListSelectionModel;
import javax.swing.ScrollPaneConstants;
import javax.swing.event.ListSelectionEvent;
import javax.swing.event.ListSelectionListener;

import ch.eisenring.app.client.network.RPCAdapter;
import ch.eisenring.app.shared.network.RPCContext;
import ch.eisenring.app.shared.network.RPCHandler;
import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.observable.Observable;
import ch.eisenring.core.observable.Observer;
import ch.eisenring.dms.client.DMSClient;
import ch.eisenring.dms.client.resources.Images;
import ch.eisenring.dms.shared.codetables.DMSRightCode;
import ch.eisenring.dms.shared.model.data.PermissionCache;
import ch.eisenring.dms.shared.model.data.PermissionDescriptor;
import ch.eisenring.dms.shared.network.PacketPermissionBase;
import ch.eisenring.dms.shared.network.PacketPermissionDeleteRequest;
import ch.eisenring.gui.GridBagConstraints;
import ch.eisenring.gui.balloontip.BalloonTipManager;
import ch.eisenring.gui.components.DecorationHeader;
import ch.eisenring.gui.components.GenericListModel;
import ch.eisenring.gui.components.HEAGPanel;
import ch.eisenring.gui.components.Spacer;
import ch.eisenring.gui.util.GUIUtil;
import ch.eisenring.gui.window.AbstractBaseWindow;

@SuppressWarnings("serial")
public class PermissionListPanel extends HEAGPanel {

	private final DMSClient client;
	
	private final DecorationHeader header = new DecorationHeader(
			DecorationHeader.LABEL, "Objektbezogene Berechtigungen",
			DecorationHeader.ICON, Images.PERMISSIONS,
			DecorationHeader.SIZE, DecorationHeader.MEDIUM);

	private final JButton btnAdd = new JButton("Neu...", Images.PERMISSION_ADD.getIcon(16));
	private final JButton btnEdit = new JButton("Bearbeiten...", Images.PERMISSION_EDIT.getIcon(16));
	private final JButton btnDel = new JButton("Löschen", Images.PERMISSION_DEL.getIcon(16));
	
	private final GenericListModel<DescriptorWrapper> listModel = new GenericListModel<DescriptorWrapper>();
	private final JList<DescriptorWrapper> jList = new JList<DescriptorWrapper>(listModel);
	private final JScrollPane scrollPane = new JScrollPane(jList);

	private final Observer<Object> observer = new Observer<Object>() {
		@Override
		public void observableChanged(final Observable<Object> observable) {
			// the permissions have changed, update the list contents
			updateList();
		}
	};

	private final ListSelectionListener selectionListener = new ListSelectionListener() {
		@Override
		public void valueChanged(final ListSelectionEvent event) {
			updateButtons();
		}
	};

	private final MouseListener editDblClickListener = new MouseAdapter() {
		@Override
		public void mouseClicked(final MouseEvent event) {
			if (!event.isConsumed() &&
					isEditable() &&
					event.getButton() == MouseEvent.BUTTON1 &&
					event.getClickCount() == 2) {
				final int index = jList.locationToIndex(event.getPoint());
				if (index < 0)
					return;
				final DescriptorWrapper wrapper = listModel.getItem(index);
				if (wrapper == null)
					return;
				event.consume();
				showDialog(wrapper.getDelegate());
			}
		}
	};

	private final ActionListener addListener = new ActionListener() {
		@Override
		public void actionPerformed(final ActionEvent event) {
			showDialog(null);
		}
	};

	private final ActionListener editListener = new ActionListener() {
		@Override
		public void actionPerformed(final ActionEvent event) {
			final DescriptorWrapper wrapper = (DescriptorWrapper) jList.getSelectedValue();
			if (wrapper != null) {
				showDialog(wrapper.getDelegate());
			}
		}
	};

	private final ActionListener delListener = new ActionListener() {
		@Override
		public void actionPerformed(final ActionEvent event) {
			final DescriptorWrapper wrapper = (DescriptorWrapper) jList.getSelectedValue();
			if (wrapper != null) {
				setWindowEditable(false);
				final PacketPermissionDeleteRequest request = PacketPermissionDeleteRequest.create(
						client.getLoginname(), wrapper.getDelegate());
				client.sendPacket(delRPCHandler, request);
			}
		}
	};
	
	private final RPCHandler delRPCHandler = new RPCAdapter() {
		@Override
		public void timeoutOccured(final RPCContext rpcContext) {
			super.timeoutOccured(rpcContext);
			setWindowEditable(true);
		}
		@Override
		public void replyReceived(final RPCContext rpcContext) {
			setWindowEditable(true);
			final PacketPermissionBase reply = (PacketPermissionBase) rpcContext.getReply();
			final ErrorMessage message = reply.getMessage();
			if (!message.isSuccess()) {
				BalloonTipManager.show(btnDel, message);
			}
		}
	};

	public PermissionListPanel(final DMSClient client) {
		this.client = client;
		client.PERMISSIONS.addObserver(observer);
		initComponents();
		initLayout();
	}

	private void initComponents() {
		GUIUtil.makeSameSize(btnAdd, btnEdit, btnDel);
		setLayout(new GridBagLayout());
		jList.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
		jList.addListSelectionListener(selectionListener);
		jList.addMouseListener(editDblClickListener);
		btnAdd.addActionListener(addListener);
		btnEdit.addActionListener(editListener);
		btnDel.addActionListener(delListener);
		scrollPane.setVerticalScrollBarPolicy(ScrollPaneConstants.VERTICAL_SCROLLBAR_ALWAYS);
		scrollPane.setHorizontalScrollBarPolicy(ScrollPaneConstants.HORIZONTAL_SCROLLBAR_NEVER);
	}

	private void initLayout() {
		removeAll();
		add(header, GridBagConstraints.decorator(0, 0));
		
		add(btnAdd, GridBagConstraints.button(0, 1));
		add(btnEdit, GridBagConstraints.button(1, 1));
		add(btnDel, GridBagConstraints.button(2, 1));
		add(new Spacer(), GridBagConstraints.field(3, 1));
		
		add(scrollPane, GridBagConstraints.area(0, 2).gridWidthRemainder());
	}

	/**
	 * Refresh the contents of the permission list
	 */
	protected void updateList() {
		jList.clearSelection();
		listModel.clear();
		final PermissionCache cache = client.PERMISSIONS.get();
		final ArrayList<DescriptorWrapper> permList = new ArrayList<DescriptorWrapper>();
		for (final PermissionDescriptor descriptor : cache.getAllPermissions(true)) {
			final DescriptorWrapper wrapper = new DescriptorWrapper(descriptor);
			permList.add(wrapper);
		}
		Collections.sort(permList, DescriptorWrapper.COMPARATOR_ASC);
		listModel.addItems(permList);
		updateButtons();
	}

	@Override
	public void setEditable(final boolean editable) {
		super.setEditable(editable);
		jList.setEnabled(editable);
		updateButtons();
	}

	/**
	 * Update enabling status of the add/edit/delete buttons in panel
	 */
	protected void updateButtons() {
		final boolean permitted = DMSRightCode.ALTER_PERMISSION_DESCRIPTIONS.isPermitted() && isEditable() && isEnabled();
		final boolean selected = !jList.getSelectedValuesList().isEmpty();
		btnAdd.setEnabled(permitted);
		btnEdit.setEnabled(permitted && selected);
		btnDel.setEnabled(permitted && selected);
		jList.setEnabled(permitted);
	}

	/**
	 * Shows edit dialog for PermissionDescriptor.
	 * If null is passed for descriptor, a new descriptor is created.
	 */
	protected void showDialog(final PermissionDescriptor descriptor) {
		PermissionEditDialog dialog = new PermissionEditDialog(client, this, descriptor);
		AbstractBaseWindow.showWindow(dialog);
	}

}

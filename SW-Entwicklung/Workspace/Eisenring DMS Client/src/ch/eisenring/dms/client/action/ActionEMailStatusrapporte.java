package ch.eisenring.dms.client.action;

import ch.eisenring.app.client.network.RPCAdapter;
import ch.eisenring.app.shared.network.RPCContext;
import ch.eisenring.app.shared.network.RPCHandler;
import ch.eisenring.core.util.file.FileImage;
import ch.eisenring.dms.client.DMSClient;
import ch.eisenring.dms.client.resources.templates.EMailTemplates;
import ch.eisenring.dms.service.api.DMSObject;
import ch.eisenring.dms.service.codetables.DMSDocumentType;
import ch.eisenring.dms.service.codetables.DMSObjectStatus;
import ch.eisenring.dms.service.codetables.DMSObjectType;
import ch.eisenring.dms.service.codetables.DMSPropertyCode;
import ch.eisenring.dms.shared.creepingevil.MailTemplateContext;
import ch.eisenring.dms.shared.model.api.DMSProperty;
import ch.eisenring.dms.shared.model.api.DMSPropertyAccessor;
import ch.eisenring.dms.shared.network.PacketGetChildDocumentsReply;
import ch.eisenring.dms.shared.network.PacketGetChildDocumentsRequest;
import ch.eisenring.email.EMailAttachment;
import ch.eisenring.email.EMailTemplateResource;
import ch.eisenring.gui.dialogs.BusyDialog;

public class ActionEMailStatusrapporte extends ActionEMailAbstract {

	protected final DMSObject object;
	protected final DMSPropertyAccessor propertyAccessor;

	public ActionEMailStatusrapporte(
			final DMSClient client,
			final DMSObject object,
			final DMSPropertyAccessor propertyAccessor) {
		super(client, getTemplate(propertyAccessor),
				getMailContext(client, object, propertyAccessor));
		this.object = object;
		this.propertyAccessor = propertyAccessor;
	}

	@Override
	protected boolean isEnabledImpl() {
		if (!super.isEnabledImpl())
			return false;
		if (object == null || propertyAccessor == null)
			return false;
		if (!DMSObjectType.FOLDER.equals(object.getType()))
			return false;
		final DMSProperty projectNumber = propertyAccessor.getProperty(DMSPropertyCode.PROJECTNUMBER, DMSPropertyAccessor.Mode.ALL);
		final DMSProperty baseNumber = propertyAccessor.getProperty(DMSPropertyCode.BASEPROJECTNUMBER, DMSPropertyAccessor.Mode.ALL);
		if (projectNumber == null && baseNumber == null)
			return false;
		return true;
	}

	@Override
	protected void performAction() {
		DMSProperty propProjectNumber = propertyAccessor.getProperty(DMSPropertyCode.PROJECTNUMBER);
		boolean isBaseNumber;
		String projectNumber;
		if (propProjectNumber != null) {
			projectNumber = propProjectNumber.getPropertyValue();
			isBaseNumber = false;
		} else {
			DMSProperty propBaseNumber = propertyAccessor.getProperty(DMSPropertyCode.BASEPROJECTNUMBER);
			projectNumber = propBaseNumber.getPropertyValue();
			isBaseNumber = true;
		}
		if (projectNumber == null)
			throw new RuntimeException("invalid object for action");
		final PacketGetChildDocumentsRequest request = PacketGetChildDocumentsRequest.create(
				projectNumber, isBaseNumber, DMSDocumentType.STATUSRAPPORT, DMSObjectStatus.ARCHIVED);
		client.sendPacket(rpcHandler, request);
	}

	private void performAction2() {
		super.performAction();
	}

	private RPCHandler rpcHandler = new RPCAdapter() {
		BusyDialog busyDialog;

		@Override
		public void requestSent(final RPCContext rpcContext) {
			super.requestSent(rpcContext);
			busyDialog = new BusyDialog();
			busyDialog.setVisible(true);
		}

		@Override
		public void timeoutOccured(final RPCContext rpcContext) {
			super.timeoutOccured(rpcContext);
			busyDialog.setVisible(false);
		}

		@Override
		public void replyReceived(final RPCContext rpcContext) {
			busyDialog.setVisible(false);
			final PacketGetChildDocumentsReply reply = (PacketGetChildDocumentsReply) rpcContext.getReply();
			if (reply.isValid()) {
				for (final FileImage document : reply.getDocuments()) {
					final EMailAttachment attachment = new EMailAttachment(document);
					mailContext.attachments.add(attachment);
				}
				performAction2();
			} else {
				client.message(reply.getMessage());
			}
		}
	};

	/**
	 * Determines which template to use
	 */
	private static EMailTemplateResource getTemplate(final DMSPropertyAccessor propertyAccessor) {
		return EMailTemplates.VERSAND_STATUSRAPPORTE;
	}

	/**
	 * Determine the recipients of the mail.
	 */
	private static MailTemplateContext getMailContext(
			final DMSClient client,
			final DMSObject document,
			final DMSPropertyAccessor propertyAccessor) {
		final MailTemplateContext result = new MailTemplateContext();
		result.user = client.getLoginname();
		return result;
	}

}

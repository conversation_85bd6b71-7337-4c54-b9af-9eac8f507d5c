package ch.eisenring.dms.client.gui.zipbuilder;

import java.io.File;
import java.io.IOException;

import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.dms.client.DMSClient;
import ch.eisenring.dms.client.action.AbstractDMSAction;
import ch.eisenring.dms.client.gui.util.TempFile;
import ch.eisenring.email.client.resource.images.Images;

@SuppressWarnings("serial")
public class ZipActionSendWithOutlook extends AbstractDMSAction {

	protected final static String[] OUTLOOK_PATHES = {
		"C:\\Program Files (x86)\\Microsoft Office\\Office15\\OUTLOOK.EXE",
		"C:\\Programme\\Microsoft Office\\Office11\\OUTLOOK.EXE"
	};

	protected final TempFile tempFile;
	
	public ZipActionSendWithOutlook(final DMSClient client, final TempFile tempFile) {
		super(client, Images.EMAIL_WITH_ATTACHMENT, "Mit Outlook versenden...");
		this.tempFile = tempFile;
	}

	@Override
	protected boolean isEnabledImpl() {
		return super.isEnabledImpl() 
			&& tempFile != null
			&& getOutlookPath() != null;
	}

	@Override
	protected void performAction() {
		try {
			final File outlookPath = getOutlookPath();
			final String[] cmd = { outlookPath.getAbsolutePath(), "/a", tempFile.getFile().getAbsolutePath() };
			final ProcessBuilder pb = new ProcessBuilder(cmd);
			pb.start();
		} catch (final IOException e) {
			client.message(new ErrorMessage(e));
		}
	}

	/**
	 * Attempt to locate MS-Outlook exe
	 */
	protected File getOutlookPath() {
		for (final String path : OUTLOOK_PATHES) {
			final File file = checkOutlookPath(path);
			if (file != null)
				return file;
		}
		return null;
	}

	protected static File checkOutlookPath(final String path) {
		do {
			try {
				if (Strings.isEmpty(path))
					break;
				File file = new File(path);
				if (!file.exists())
					break;
				file = file.getCanonicalFile();
				return file;
			} catch (final Exception e) {
				break;
			}
		} while (false);
		return null;
	}

}

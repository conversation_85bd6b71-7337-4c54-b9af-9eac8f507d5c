package ch.eisenring.dms.client.gui.menu;

import ch.eisenring.dms.client.action.ActionCreateVersion;
import ch.eisenring.dms.client.action.ActionFolderCreate;
import ch.eisenring.gui.action.AbstractAction;
import ch.eisenring.gui.menu.MenuBuilder;

final class NewF<PERSON>erDefault extends NewFolderBase {

	@Override
	public boolean acceptsFile(final NewFolderContext context) {
		return context.contentView != null && context.file != null;
	}

	@Override
	public boolean acceptsFolder(final NewFolderContext context) {
		return context.contentView != null && context.folder != null;
	}

	@Override
	public void addFolderItems(final NewFolderContext context, final MenuBuilder menuBuilder) {
		final AbstractAction action = new ActionFolderCreate(
				context.client, context.folder.getPKValue(), context.contentView); 
		menuBuilder.add(action);
	}

	@Override
	public void addFileItems(final NewFolderContext context, final MenuBuilder menuBuilder) {
		final AbstractAction action = new ActionCreateVersion(
				context.client, context.file.getPKValue(), context.contentView);
		menuBuilder.add(action);
	}
	
}

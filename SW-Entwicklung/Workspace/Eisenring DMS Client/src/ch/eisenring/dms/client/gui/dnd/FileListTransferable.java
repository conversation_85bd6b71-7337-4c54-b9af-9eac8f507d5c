package ch.eisenring.dms.client.gui.dnd;

import java.awt.datatransfer.DataFlavor;
import java.awt.datatransfer.Transferable;
import java.awt.datatransfer.UnsupportedFlavorException;
import java.io.File;

import ch.eisenring.core.collections.Collection;
import ch.eisenring.core.collections.alterableview.AlterableView;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.dms.client.DMSClient;

public class FileListTransferable implements Transferable {

	protected final DMSClient client;
	protected final ArrayList<File> fileList = new ArrayList<File>();

	protected Object transferData;
	
	public FileListTransferable(final DMSClient client,
							    final File file) {
		this.client = client;
		this.fileList.add(file);
	}

	public FileListTransferable(final DMSClient client,
							    final Collection<File> files) {
		this.client = client;
		this.fileList.addAll(files);
	}

	@SuppressWarnings("unchecked")
	public synchronized Object getTransferData(final DataFlavor flavor) throws UnsupportedFlavorException {
		if (DataFlavor.javaFileListFlavor.equals(flavor)) {
			return AlterableView.of(fileList);
		} else {
			throw new UnsupportedFlavorException(flavor);
		}
	}

	private final static DataFlavor[] DATAFLAVORS = {
		DataFlavor.javaFileListFlavor,
	};

	public DataFlavor[] getTransferDataFlavors() {
		return DATAFLAVORS.clone();
	}

	public boolean isDataFlavorSupported(final DataFlavor flavor) {
		final DataFlavor[] flavors = getTransferDataFlavors();
		if (flavors == null)
			return false;
		for (int i=flavors.length-1; i>=0; --i)
			if (flavors[i].equals(flavor))
				return true;
		return false;
	}
	
}

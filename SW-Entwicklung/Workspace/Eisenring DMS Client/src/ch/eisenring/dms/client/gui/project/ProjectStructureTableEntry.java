package ch.eisenring.dms.client.gui.project;

import java.util.Comparator;

import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.dms.shared.logiware.DMSLogiwareAuftrag;

class ProjectStructureTableEntry {

	public final static Comparator<ProjectStructureTableEntry> ORDER = new Comparator<ProjectStructureTableEntry>() {
		@Override
		public int compare(final ProjectStructureTableEntry e1, final ProjectStructureTableEntry e2) {
			final String s1 = e1.getAuftrag().getProjektnummer();
			final String s2 = e2.getAuftrag().getProjektnummer();
			return Strings.compare(s1, s2);
		}
	};

	private DMSLogiwareAuftrag auftrag;

	public ProjectStructureTableEntry(final DMSLogiwareAuftrag auftrag) {
		this.auftrag = auftrag;
		boolean b = Boolean.valueOf(auftrag.getFolderRowId() == null) & auftrag.getCreateFlag();
		auftrag.setCreateFlag(b);
	}

	public DMSLogiwareAuftrag getAuftrag() {
		return auftrag;
	}

	public Boolean getFolderExists() {
		return Boolean.valueOf(auftrag.getFolderRowId() != null);
	}

	public Boolean getCreateFlag() {
		return auftrag.getCreateFlag();
	}

	public void setCreateFlag(final Boolean createFlag) {
		boolean b = Boolean.valueOf(Boolean.TRUE.equals(createFlag));
		auftrag.setCreateFlag(b);
	}

}

package ch.eisenring.dms.client.codetables;

import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.codetype.StaticCode;
import ch.eisenring.dms.service.api.DMSObject;
import ch.eisenring.dms.shared.client.util.DMSObjectLabelFormatter;

public final class DMSObjectLabelFormat extends StaticCode {

	public final static DMSObjectLabelFormat DETAILED =	new DMSObjectLabelFormat(
			0, "Name, Grösse + Datum", null, DMSObjectLabelFormatter.DETAILED);

	public final static DMSObjectLabelFormat NAMEONLY =	new DMSObjectLabelFormat(
			1, "Name", null, DMSObjectLabelFormatter.NAMEONLY);
	
	public final static DMSObjectLabelFormat NAMEANDTYPE = new DMSObjectLabelFormat(
			2, "Name + Dateierweiterung", null, DMSObjectLabelFormatter.NAMEANDTYPE);

	public final static DMSObjectLabelFormat MAXDETAILED = new DMSObjectLabelFormat(
			3, "Name + Erweiterung, Grösse + Datum", null, DMSObjectLabelFormatter.MAXDETAILED);

	private final Float fontSize;
	private final DMSObjectLabelFormatter formatter;
	
	DMSObjectLabelFormat(final int id, final String name, final Float fontSize, final DMSObjectLabelFormatter formatter) {
		super(id, Integer.valueOf(id), name, name);
		this.fontSize = fontSize;
		this.formatter = formatter;
	}

	@Override
	public Class<? extends AbstractCode> getTypeClass() {
		return DMSObjectLabelFormat.class;
	}

	public final String getLabel(final DMSObject object) {
		return formatter.format(object);
	}

	public Float getFontSize() {
		return fontSize;
	}

}

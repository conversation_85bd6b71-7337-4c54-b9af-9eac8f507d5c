package ch.eisenring.dms.client.gui.menu;

import java.util.Collections;

import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.Set;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.dms.client.action.AbstractDMSAction;
import ch.eisenring.dms.client.action.ActionFolderCreateSubAuftrag;
import ch.eisenring.dms.service.codetables.DMSFolderType;
import ch.eisenring.dms.service.codetables.DMSPropertyCode;
import ch.eisenring.dms.shared.network.PacketFindSubOrdersReply;
import ch.eisenring.dms.shared.network.PacketFindSubOrdersRequest;
import ch.eisenring.gui.menu.MenuBuilder;

final class NewFolderSubAuftrag extends NewFolderBase {

	private final static Set<DMSFolderType> ACCEPTED_FOLDER_TYPES = Set.asReadonlySet(
			DMSFolderType.AUSFUEHRUNGSPLAENE);
	
	@Override
	public boolean acceptsFolder(final NewFolderContext context) {
		if (context.contentView == null)
			return false;
		if (!ACCEPTED_FOLDER_TYPES.contains(context.folderType))
			return false;
		final String projectNumber = Strings.toString(context.propertyAccessor.getPropertyValue(DMSPropertyCode.PROJECTNUMBER));
		return !Strings.isEmpty(projectNumber);
	}
	
	@Override
	public void addFolderItems(final NewFolderContext context, final MenuBuilder menuBuilder) {
		// determine the project number of the folder
		final String projectNumber = Strings.toString(context.propertyAccessor.getPropertyValue(DMSPropertyCode.PROJECTNUMBER));
		// now detect the possible sub folders from logiware...
		try {
			final PacketFindSubOrdersRequest request = PacketFindSubOrdersRequest.create(projectNumber);
			final PacketFindSubOrdersReply reply = (PacketFindSubOrdersReply) context.client.sendAndWait(request);
			if (reply.isValid()) {
				final List<String> subNumbers = reply.getSubNumbers();
				Collections.sort(subNumbers, Strings.Order.NATURAL);
				for (final String subNumber : subNumbers) {
					final AbstractDMSAction action = new ActionFolderCreateSubAuftrag(
							context.client, context.contentView, context.folder, subNumber);
					menuBuilder.add(action);
				}
			} else {
				context.client.message(reply.getMessage());
			}
		} catch (final Exception e) {
			// ignore...
		}
	}

}

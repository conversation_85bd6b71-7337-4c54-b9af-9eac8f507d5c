package ch.eisenring.dms.client.gui.dnd;

import java.awt.datatransfer.DataFlavor;
import java.awt.datatransfer.Transferable;
import java.io.File;

import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.dms.client.DMSClient;
import ch.eisenring.dms.client.cache.DMSBinaryLoader;
import ch.eisenring.dms.client.gui.util.TempFile;
import ch.eisenring.dms.client.gui.util.VersionCacheFile;
import ch.eisenring.dms.client.model.ObjectProxyCache;
import ch.eisenring.dms.service.api.DMSObject;
import ch.eisenring.dms.service.codetables.DMSObjectType;
import ch.eisenring.dms.shared.model.api.DMSBinary;
import ch.eisenring.dms.shared.model.api.VersionIdentifier;
import ch.eisenring.dms.shared.model.api.VersionIdentifierList;

public class BinaryTransferable implements Transferable {

	protected final DMSClient client;
	protected final VersionIdentifierList versionList = new VersionIdentifierList();

	protected List<File> transferData;
	
	public BinaryTransferable(final DMSClient client, 
			                  final DMSObject object,
			                  final Long binaryRowId) {
		this.client = client;
		this.versionList.add(new VersionIdentifier(object, binaryRowId));
	}

	public BinaryTransferable(final DMSClient client,
							  final java.util.Collection<VersionIdentifier> versions) {
		this.client = client;
		this.versionList.addAll(versions);
	}

	@SuppressWarnings("unchecked")
	public synchronized Object getTransferData(final DataFlavor flavor) {
		if (VersionIdentifierList.DATAFLAVOR.equals(flavor)) {
			// this type is used only internally 
			return new VersionIdentifierList(versionList);
		}

		if (transferData != null) {
			return (Object) transferData;
		}
		
		// --- load the binary
		//     (this is hacky as hell, since this is called on the EDT)
		final ObjectProxyCache cache = client.CACHE_OBJECT.get();
		final DMSBinaryLoader loader = client.BINARY_LOADER.get();
		try {
			final List<DMSBinary> binaries = loader.getBinaries(versionList);
			transferData = new ArrayList<File>();
			for (final DMSBinary binary : binaries) {
				final DMSObject object = cache.getProxy(binary.getObjectRowId());
				final TempFile tempFile = VersionCacheFile.createReadOnly(object, binary);
				transferData.add(tempFile.getFile());
			}
			return (Object) transferData;
		} catch (final Exception e) {
			transferData = null;
			client.error(e.getMessage());
			throw new RuntimeException(e.getMessage(), e);
		}
	}

	private final static DataFlavor[] FLAVORS_FILES_ONLY = {
		DataFlavor.javaFileListFlavor,
		VersionIdentifierList.DATAFLAVOR
	};
	private final static DataFlavor[] FLAVORS_MIXED = {
		VersionIdentifierList.DATAFLAVOR
	};

	public DataFlavor[] getTransferDataFlavors() {
		if (versionList.size(DMSObjectType.FOLDER) > 0) {
			return FLAVORS_MIXED.clone();
		} else {
			return FLAVORS_FILES_ONLY.clone();
		}
	}

	public boolean isDataFlavorSupported(final DataFlavor flavor) {
		final DataFlavor[] flavors = getTransferDataFlavors();
		if (flavors == null)
			return false;
		for (int i=flavors.length-1; i>=0; --i)
			if (flavors[i].equals(flavor))
				return true;
		return false;
	}

}
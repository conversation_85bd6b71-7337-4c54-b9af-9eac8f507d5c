package ch.eisenring.dms.client.gui.editlist;

import java.awt.BorderLayout;
import java.awt.Dimension;

import ch.eisenring.core.tag.TagSet;
import ch.eisenring.dms.client.gui.AbstractDMSDialog;
import ch.eisenring.dms.client.gui.DMSMainWindow;
import ch.eisenring.dms.client.resources.Images;
import ch.eisenring.gui.window.DialogTags;
import ch.eisenring.gui.window.WindowTags;

@SuppressWarnings("serial")
public class DocumentEditListDialog extends AbstractDMSDialog {

	protected final DMSMainWindow window;
	protected final DocumentEditListPanel panel;
	
	public DocumentEditListDialog(final DMSMainWindow window) {
		super(window.getClient(), new TagSet(
				WindowTags.TITLE, "Dokumente in Bearbeitung/Schreibsperre",
				WindowTags.POSITION_SETTINGS_ID, "DocumentEditListWindow",
				WindowTags.MINIMUM_SIZE, new Dimension(480, 224),
				WindowTags.ICON, Images.DOCUMENT_EDIT,
				DialogTags.DIALOG_OWNER, window
		));
		this.window = window;
		this.panel = new DocumentEditListPanel(window);
		initComponents();
		initLayout();
		pack();
	}

	private void initComponents() {
		setLayout(new BorderLayout());
	}

	private void initLayout() {
		getContentPane().removeAll();
		add(panel, BorderLayout.CENTER);
	}

	@Override
	protected void onCancel() {
		super.onCancel();
		panel.dispose();
	}

}

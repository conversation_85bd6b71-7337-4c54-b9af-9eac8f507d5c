package ch.eisenring.dms.client.action;

import ch.eisenring.dms.client.DMSClient;

public class ActionTerminateOnClose extends AbstractDMSAction {

	public ActionTerminateOnClose(final DMSClient client) {
		super(client, "<PERSON><PERSON><PERSON><PERSON> des Hauptfensters beendet Dokument Manager");
		addObservable(client.TERMINATE_ON_CLOSE);
	}

	@Override
	protected boolean isCheckedImpl() {
		return Boolean.TRUE.equals(client.TERMINATE_ON_CLOSE.get());
	}

	@Override
	protected void performAction() {
		final Boolean b = client.TERMINATE_ON_CLOSE.get();
		client.TERMINATE_ON_CLOSE.set(Boolean.valueOf(!b.equals(Boolean.TRUE)));
	}

}

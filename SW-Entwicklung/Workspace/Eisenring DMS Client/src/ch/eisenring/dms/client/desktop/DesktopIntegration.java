package ch.eisenring.dms.client.desktop;

import java.awt.event.MouseEvent;

import ch.eisenring.commons.resource.images.Images;
import ch.eisenring.core.application.SoftwareDescriptor;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.dms.client.DMSClient;
import ch.eisenring.dms.client.action.ActionExit;
import ch.eisenring.gui.desktop.DesktopIntegrationBase;
import ch.eisenring.gui.menu.MenuBuilder;

public final class DesktopIntegration extends DesktopIntegrationBase {

	protected final DMSClient client;
	
	public DesktopIntegration(final DMSClient client) {
		super(Images.DMS);
		this.client = client;
	}

	@Override
	public void attachTrayIcon() {
		super.attachTrayIcon();
		updateToolTip();
	}

	@Override
	protected MenuBuilder getTrayContextMenu() {
		final MenuBuilder b = new MenuBuilder();
		b.add(new TrayActionShowWindow(client));
		b.add(new TrayActionZipBuilder(client));
		b.add(new TrayActionNewWindow(client));
		b.addSeparator();
		b.add(new ActionExit(client, false));
		return b;
	}

	/**
	 * Updates tray icon tool tip
	 */
	protected final void updateToolTip() {
		final SoftwareDescriptor appDescriptor = client.getCore().getCoreDescriptor();
		final String toolTip = Strings.concat(appDescriptor.getSoftwareName(),
				"\nDoppelklicken um Fenster zu öffnen oder" +
				"\nRechtsklick um Menü zu öffnen");
		updateToolTipImpl(toolTip);
	}

	@Override
	protected void onLeftClick(final MouseEvent event) {
		client.WINDOW_MANAGER.get().restoreAllWindows();
	}

}

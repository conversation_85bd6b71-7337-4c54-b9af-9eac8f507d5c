package ch.eisenring.dms.client.action;

import ch.eisenring.core.collections.Collection;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.dms.client.DMSClient;
import ch.eisenring.dms.client.model.ObjectProxyCache;
import ch.eisenring.dms.service.api.DMSObject;

public abstract class ActionFileAbstract extends AbstractDMSAction {

	protected final ObjectProxyCache objectCache;

	protected final ArrayList<DMSObject> documents = new ArrayList<DMSObject>();
	
	protected ActionFileAbstract(final DMSClient client, final Object sourceGUI, final Object ... properties) {
		super(client, properties);
		setSourceGUI(sourceGUI);
		this.objectCache = client.CACHE_OBJECT.get();
	}
	
	public void addDocument(final DMSObject document) {
		if (document != null && !documents.contains(document)) {
			documents.add(document);
		}
	}

	public void addDocuments(final Collection<DMSObject> documents) {
		if (documents != null && !documents.isEmpty()) {
			for (final DMSObject document : documents) {
				addDocument(document);
			}
		}
	}

	@Override
	protected boolean isEnabledImpl() {
		if (!super.isEnabledImpl())
			return false;
		if (documents == null || documents.isEmpty())
			return false;
		return true;
	}

}

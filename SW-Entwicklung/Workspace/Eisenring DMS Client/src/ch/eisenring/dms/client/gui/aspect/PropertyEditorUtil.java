package ch.eisenring.dms.client.gui.aspect;

import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.collections.Set;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.dms.client.DMSClient;
import ch.eisenring.dms.service.codetables.DMSPropertyCode;
import ch.eisenring.dms.shared.model.metaold.DMSPropertyMetaModel;
import ch.eisenring.gui.components.HEAGCodeComboBox;
import ch.eisenring.gui.components.HEAGTextField;
import ch.eisenring.gui.interfaces.ValueEditor;
import ch.eisenring.gui.util.ConversionUtil;

import javax.swing.*;

public class PropertyEditorUtil {

	// --------------------------------------------------------------
	// ---
	// --- Modified Code ComboBox class that implements
	// --- the ValueEditor interface to return/accept values
	// --- as Strings
	// ---
	// --------------------------------------------------------------
	@SuppressWarnings("serial")
	static class HEAGCodeComboBoxId<T extends AbstractCode> extends HEAGCodeComboBox<T> {
		
		public HEAGCodeComboBoxId(Class<T> typeClass) {
			super(typeClass);
		}

		/**
		 * Returns selected code id as string
		 */
		@Override
		public Object getValue() {
			final AbstractCode code = getSelectedCode();
			return Strings.toString(AbstractCode.getId(code, 0));
		}
	
		/**
		 * Selects code id from string
		 */
		@Override
		public void setValue(final Object value) {
			if (value instanceof AbstractCode) {
				super.setValue(value);
			} else {
				T code;
				try {
					final int intValue = Strings.parseInt(Strings.toCharSequence(value));
					code = AbstractCode.getById(intValue, super.getValueClass());
				} catch (Exception e) {
					code = AbstractCode.getNull(super.getValueClass());
				}
				super.setValue(code);
			}
		}
		
	}

	protected final DMSClient client;

	protected final HEAGTextField noValueEditor = new HEAGTextField(250);

	{
		noValueEditor.setEditable(false);
		noValueEditor.setText("Zuerst Eigenschaft auswählen");
	}

	protected ValueEditor currentEditor = noValueEditor;
	protected Class<?> currentClass = null;
	
	public PropertyEditorUtil(final DMSClient client) {
		this.client = client;
	}

	public ValueEditor getEditor() {
		return currentEditor;
	}

	@SuppressWarnings("unchecked")
	public void setValueClass(final Class<?> valueClass) {
		if (valueClass == null) {
			currentEditor = noValueEditor;
		} else if (valueClass.equals(currentClass)) {
			return;
		} else if (AbstractCode.class.isAssignableFrom(valueClass)) {
			currentEditor = new HEAGCodeComboBox<AbstractCode>((Class) valueClass);
		} else if (String.class.equals(valueClass)) {
			currentEditor = new HEAGTextField(DMSPropertyMetaModel.ATR_PROPERTYVALUE.getLength());
		} else {
			throw new IllegalArgumentException("unsupported value class: " + valueClass);
		}
		currentClass = valueClass;
	}

	/**
	 * Sets the current value of the editor
	 */
	@SuppressWarnings("unchecked")
	public void setValue(final String value) {
		if (currentClass == null) {
			return;
		} else if (AbstractCode.class.isAssignableFrom(currentClass)) {
			AbstractCode code;
			if (value == null) {
				code = AbstractCode.getNull((Class) currentClass);
			} else {
				try {
					final Integer key = Strings.parseInt(value.toString());
					code = AbstractCode.getByKey(key, (Class) currentClass);
				} catch (final Exception e) {
					code = AbstractCode.getNull((Class) currentClass);
				}
			}
			((HEAGCodeComboBox<AbstractCode>) currentEditor).setSelectedCode(code);
		} else {
			currentEditor.setValue(value);
		}
	}
	
	/**
	 * Gets the current value of the editor
	 */
	public String getValue() {
		final String value;
		if (currentClass == null) {
			value = null;
		} else if (AbstractCode.class.isAssignableFrom(currentClass)) {
			final AbstractCode code = (AbstractCode) currentEditor.getValue();
			final Object key = AbstractCode.getKey(code, null);
			value = key == null ? null : key.toString(); 
		} else {
			value = ConversionUtil.convert(currentEditor, (String) null);
		}
		return value;
	}
	
	private final static Set<DMSPropertyCode> EDIT_VARCHAR30 = Set.asReadonlySet(
			DMSPropertyCode.PROJECTNUMBER,
			DMSPropertyCode.BASEPROJECTNUMBER,
			DMSPropertyCode.SUBPROJECTNUMBER,
			DMSPropertyCode.SUBJEKTID,
			DMSPropertyCode.FAHRZEUG_SCHILDNUMMER
	);

	private final static Set<DMSPropertyCode> EDIT_VARCHAR60 = Set.asReadonlySet(
			DMSPropertyCode.DISPONENT,
			DMSPropertyCode.OBJEKTBETREUER,
			DMSPropertyCode.KUNDENBERATER,
			DMSPropertyCode.BEMUSTERER
	);

	/**
	 * Gets editor component for PropertyCode
	 */
	@SuppressWarnings("unchecked")
	public static JComponent getEditorComponent(final DMSPropertyCode propertyCode) {
		final Class<?> valueClass = propertyCode.getValueClass();
		if (EDIT_VARCHAR30.contains(propertyCode)) {
				return new HEAGTextField(30);
		} else if (EDIT_VARCHAR60.contains(propertyCode)) {
			return new HEAGTextField(60);
		} else if (AbstractCode.class.isAssignableFrom(valueClass)) {
			// generic code combobox
			return new HEAGCodeComboBoxId<AbstractCode>((Class) propertyCode.getValueClass());
		} else {
		    // generic editor, allows max length the DB can hold
			// editing disabled
			final HEAGTextField unknown = new HEAGTextField(DMSPropertyMetaModel.ATR_PROPERTYVALUE.getLength());
			unknown.setValue("Eigenschaft nicht unterstützt");
			return unknown;
		}
	}

}

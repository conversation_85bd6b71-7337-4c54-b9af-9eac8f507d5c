package ch.eisenring.dms.client.gui.tree;

import java.awt.datatransfer.Transferable;
import java.awt.dnd.DropTarget;
import java.awt.dnd.DropTargetDropEvent;

import ch.eisenring.dms.client.DMSClient;
import ch.eisenring.dms.client.action.ActionShowZipBuilder;
import ch.eisenring.dms.client.gui.zipbuilder.ZipEntryDropListener;
import ch.eisenring.dms.client.resources.Images;

@SuppressWarnings("serial")
public class ZipBuilderButton extends ToolbarButton {

	private final ZipEntryDropListener dropListener;

	public ZipBuilderButton(final CompoundContentView contentView) {
		super(contentView, Images.TYPE_ARCHIVE);
		final DMSClient client = contentView.getClient();
		this.dropListener = new ZipEntryDropListener(client, client.ZIP_ENTRYLIST.get()) {
			@Override
			public boolean drop(final DropTargetDropEvent event, final Transferable transferable) {
				final boolean success = super.drop(event, event.getTransferable());
				// show the zip builder after drop
				if (success)
					buttonClicked();
				return success;
			}
		};
		initComponent();
	}

	private void initComponent() {
		new DropTarget(this, dropListener);
		setToolTipText(
				"<html>Öffnet das Fenster zum Erstellen von ZIP-Archiven." +
				"<br>" +
				"<br>Sie können Dokumente auch direkt auf diesen Button legen," +
				"<br>um sie dem ZIP-Fenster hinzuzufügen.");
	}

	@Override
	public void buttonClicked() {
		ActionShowZipBuilder.showZipBuilder(contentView.getClient());
	}

}

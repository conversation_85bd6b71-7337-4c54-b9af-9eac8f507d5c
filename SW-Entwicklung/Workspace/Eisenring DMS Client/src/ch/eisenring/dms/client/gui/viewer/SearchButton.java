package ch.eisenring.dms.client.gui.viewer;

import java.awt.Window;
import java.awt.event.ActionEvent;
import java.awt.event.InputEvent;
import java.awt.event.KeyEvent;

import javax.swing.AbstractAction;
import javax.swing.KeyStroke;

import ch.eisenring.dms.client.DMSClient;
import ch.eisenring.dms.client.gui.search.SearchExtDialog;
import ch.eisenring.dms.client.resources.Images;
import ch.eisenring.dms.shared.network.PacketFullTextSearchRequest;
import ch.eisenring.gui.util.GUIUtil;
import ch.eisenring.gui.window.AbstractBaseWindow;

@SuppressWarnings("serial")
public class SearchButton extends ControlButton {

	protected final DMSClient client;
	protected final PacketFullTextSearchRequest request;

	private final AbstractAction searchAction = new AbstractAction() {
		@Override
		public void actionPerformed(final ActionEvent event) {
			if (isActionEnabled()) {
				final Window window = GUIUtil.getWindowAncestor(SearchButton.this);
				final SearchExtDialog dialog = new SearchExtDialog(client, request, window);
				AbstractBaseWindow.showWindow(dialog);
			}
		}
	};

	public SearchButton(final DMSClient client, final PacketFullTextSearchRequest request) {
		super(Images.SEARCH2, 24);
		this.client = client;
		this.request = request;
		setToolTipText("Erneut Suchen (F)");
		// bind the keys
        registerKeyboardAction(searchAction,
        		KeyStroke.getKeyStroke(KeyEvent.VK_F, 0),
        		WHEN_IN_FOCUSED_WINDOW);
        registerKeyboardAction(searchAction,
        		KeyStroke.getKeyStroke(KeyEvent.VK_F, InputEvent.CTRL_DOWN_MASK),
        		WHEN_IN_FOCUSED_WINDOW);
        registerKeyboardAction(searchAction,
        		KeyStroke.getKeyStroke(KeyEvent.VK_F3, 0),
        		WHEN_IN_FOCUSED_WINDOW);
	}

	public void buttonClicked() {
		searchAction.actionPerformed((ActionEvent) null);
	}

}

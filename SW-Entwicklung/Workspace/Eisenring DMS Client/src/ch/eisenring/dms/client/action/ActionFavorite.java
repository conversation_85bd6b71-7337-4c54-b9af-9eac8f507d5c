package ch.eisenring.dms.client.action;

import ch.eisenring.dms.client.gui.tree.CompoundContentView;
import ch.eisenring.dms.client.gui.tree.ContentViewMemento;
import ch.eisenring.dms.client.model.ObjectProxyCache;
import ch.eisenring.dms.service.api.DMSObject;

@SuppressWarnings("serial")
public final class ActionFavorite extends AbstractDMSAction {

	private final CompoundContentView contentView;
	private final ContentViewMemento memento;

	private boolean initialized;
	private boolean valid;
	private DMSObject folder;
	private String labelText;

	public ActionFavorite(final CompoundContentView contentView,
						  final ContentViewMemento memento) {
		super(contentView.getClient());
		this.contentView = contentView;
		this.memento = memento;
		initialize();
	}

	private void initialize() {
		if (initialized)
			return;
		final ObjectProxyCache cache = contentView.getCache();
		try {
			folder = cache.getProxy(memento.getParentRowId());
			labelText = folder.getObjectname();
			valid = true;
		} catch (final Exception e) {
			folder = null;
			labelText = "<Ungültige Ansicht: " + e.getMessage() + ">";
			valid = false;
		}
		if (labelText == null)
			labelText = "<Gelöschter Ordner>";
		else
			labelText = labelText.replace("_", "\\_");
		setLabel(labelText);
		initialized = true;
	}

	@Override
	protected boolean isEnabledImpl() {
		initialize();
		return valid;
	}
	
	@Override
	protected void performAction() {
		memento.updateTimestamp();
		contentView.getHistory().recordState();
		contentView.getHistory().restore(memento);
	}

}

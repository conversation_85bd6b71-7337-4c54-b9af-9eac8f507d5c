package ch.eisenring.dms.client.action;

import java.awt.Component;
import java.io.File;

import javax.swing.JFileChooser;

import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.codetable.MessageClassCode;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.io.file.FileItem;
import ch.eisenring.dms.client.DMSClient;
import ch.eisenring.dms.client.gui.export.DMSExport;
import ch.eisenring.dms.client.gui.export.DMSExportListener;
import ch.eisenring.dms.client.resources.Images;
import ch.eisenring.dms.service.api.DMSObject;
import ch.eisenring.dms.service.codetables.DMSObjectType;

public class ActionSaveAs extends AbstractDMSAction {

	private final DMSObject object;
	private final Component parent;
	
	public ActionSaveAs(final DMSClient client, final DMSObject object, final Component parent) {
		super(client, "Speichern unter...", Images.DISK);
		this.object = object;
		this.parent = parent;
		isEnabled();
	}

	@Override
	protected boolean isEnabledImpl() {
		if (!super.isEnabledImpl())
			return false;
		if (object == null)
			return false;
		if (!DMSObjectType.FILE.equals(object.getType()))
			return false;
		return true;
	}

	@Override
	protected void performAction() {
		// ask where...
		final JFileChooser fileChooser = new JFileChooser((File) null);
		fileChooser.setFileSelectionMode(JFileChooser.DIRECTORIES_ONLY);
		final int result = fileChooser.showSaveDialog(parent);
		if (result != JFileChooser.APPROVE_OPTION)
			return;
		if (fileChooser.getSelectedFile() == null)
			return;
		final FileItem directory = FileItem.create(fileChooser.getSelectedFile());

		// save file in background
		final DMSExport export = new DMSExport(client);
		export.setRootNode(object);
		export.setRootPath(directory);
		export.setListener(new DMSExportListener() {
			@Override
			public boolean acceptsNode(final DMSObject object) {
				return true;
			}
			@Override
			public void nodeError(final DMSObject object, final ErrorMessage error) {
				client.message(error);
			}
			@Override
			public void nodeExported(final DMSObject object) {
				client.message(new ErrorMessage(MessageClassCode.INFO, Strings.concat(
						"Speichern von \"", object.getObjectname(), "\" abgeschlossen")));
			}
		});
		final Runnable r = new Runnable() {
			@Override
			public void run() {
				export.doExport();
			}
		};
		client.getThreadPool().startDaemon(r, "SaveAs", Thread.MIN_PRIORITY);
	}

}

package ch.eisenring.dms.client.network;

import ch.eisenring.dms.client.DMSClient;
import ch.eisenring.dms.shared.network.PacketGetPropertiesReply;
import ch.eisenring.network.PacketSink;
import ch.eisenring.network.implementation.PacketDispatchMode;

public final class GetPropertiesReplyHandler extends AbstractDM<PERSON>acketHandler<PacketGetPropertiesReply> {

	private GetPropertiesReplyHandler(final DMSClient client) {
		super(client, PacketGetPropertiesReply.class, PacketDispatchMode.ASYNCHRONOUS);
	}

	@Override
	public void handle(final PacketGetPropertiesReply packet, final PacketSink sink) {
		// nothing here
	}

}

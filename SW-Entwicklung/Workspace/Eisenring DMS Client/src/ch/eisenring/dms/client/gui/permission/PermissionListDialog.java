package ch.eisenring.dms.client.gui.permission;

import java.awt.BorderLayout;
import java.awt.Component;
import java.awt.Dimension;

import ch.eisenring.core.tag.TagSet;
import ch.eisenring.dms.client.DMSClient;
import ch.eisenring.dms.client.gui.AbstractDMSDialog;
import ch.eisenring.dms.client.resources.Images;
import ch.eisenring.gui.window.DialogTags;
import ch.eisenring.gui.window.WindowTags;

@SuppressWarnings("serial")
public class PermissionListDialog extends AbstractDMSDialog {

	private final PermissionListPanel panel;
	
	public PermissionListDialog(final DMSClient client, final Component parentWindow) {
		super(client, new TagSet(
				WindowTags.ICON, Images.WAND,
				WindowTags.TITLE, "Objektbezogene Berechtigungen",
				WindowTags.MINIMUM_SIZE, new Dimension(550, 480),
				WindowTags.PREFERRED_SIZE, new Dimension(550, 480),
				DialogTags.MODALITY, DialogTags.MODALITY_MODAL,
				DialogTags.DIALOG_OWNER, parentWindow));
		this.panel = new PermissionListPanel(client);
		initComponents();
		initLayout();
		pack();
	}

	private void initComponents() {
		setLayout(new BorderLayout());
	}

	private void initLayout() {
		getContentPane().removeAll();
		add(panel, BorderLayout.CENTER);
	}
	
}

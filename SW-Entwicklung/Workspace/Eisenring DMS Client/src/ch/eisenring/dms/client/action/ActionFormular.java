package ch.eisenring.dms.client.action;

import ch.eisenring.dms.client.DMSClient;
import ch.eisenring.dms.client.gui.formular.DMSFormularDialog;
import ch.eisenring.dms.client.resources.Images;
import ch.eisenring.dms.shared.formular.DMSFormularCode;
import ch.eisenring.gui.window.AbstractBaseWindow;

public final class ActionFormular extends AbstractDMSAction {

	protected final DMSFormularCode formularCode;
	
	public ActionFormular(final DMSClient client, final DMSFormularCode formularCode) {
		super(client, Images.INVOICE, formularCode.getLongText());
		this.formularCode = formularCode;
	}

	@Override
	protected void performAction() {
		final DMSFormularDialog dialog = new DMSFormularDialog(client, formularCode);
		AbstractBaseWindow.showWindow(dialog);
		
		
		// test create print job
//		final ClientPrintReportJob<?> job = new ClientPrintReportJob<DMSClient>(
//			 client, Reports.LEISTUNGSBEWERTUNG, new FormularLeistungsbewertungSource());
//		job.addTag(PrintTags.PRINTER, PrintServiceIdentifier.lookupDefaultPrintService());
//		job.addTag(PrintTags.NUM_COPIES, 1);
//		final AppCore core = client.getCore();
//		final PRTClient service = (PRTClient) core.getComponent(PRTClient.class, true);
//		service.addJob(job);
	}

}

package ch.eisenring.dms.client.gui.addfiles;

import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.io.binary.BinaryHolder;
import ch.eisenring.core.util.file.FileUtil;
import ch.eisenring.dms.service.api.DMSObject;
import ch.eisenring.dms.service.codetables.DMSDocumentType;

/**
 * Parameter object used to create new document
 */
public class DocumentCreateArgument {

	protected String fullName;
	protected DMSObject targetFolder;
	protected BinaryHolder binary;
	protected DMSDocumentType documentType;
	public boolean reduceImages = true;
	public ErrorMessage message = new ErrorMessage("Not processed yet");
	public AddFileStatusCode statusCode = AddFileStatusCode.NULL;
	
	public DocumentCreateArgument(
			final DMSObject targetFolder,
			final String fullName,
			final BinaryHolder binary,
			final DMSDocumentType documentType) {
		this.targetFolder = targetFolder;
		this.fullName = fullName;
		this.binary = binary;
		this.documentType = documentType;
	}

	public DMSObject getTargetFolder() {
		return targetFolder;
	}

	/**
	 * Returns the document name including extension
	 */
	public String getFullName() {
		return fullName;
	}

	/**
	 * Returns the document name excluding extension
	 */
	public String getDocumentName() {
		return FileUtil.removeFileExtension(fullName);
	}

	public BinaryHolder getBinary() {
		return binary;
	}

	public DMSDocumentType getDocumentType() {
		return documentType;
	}

}

package ch.eisenring.dms.client.action;

import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.util.SimpleParameterMap;
import ch.eisenring.dms.client.DMSClient;
import ch.eisenring.dms.client.gui.tree.ContentView;
import ch.eisenring.dms.client.model.ObjectProxyCache;
import ch.eisenring.dms.client.resources.Images;
import ch.eisenring.dms.service.api.DMSObject;
import ch.eisenring.dms.service.codetables.DMSPermissionCode;
import ch.eisenring.dms.service.codetables.DMSSpecialFunctionCode;
import ch.eisenring.dms.shared.network.PacketReplicateTemplateReply;
import ch.eisenring.dms.shared.network.PacketReplicateTemplateRequest;

public class ActionFolderCreateMoAu extends ActionFolderCreateSpecial {

	public ActionFolderCreateMoAu(
			final DMSClient client,
			final ContentView contentView,
			final DMSObject parentFolder,
			final String newFolderName) {
		super(client, contentView, parentFolder, newFolderName,
				newFolderName + " erstellen",
				Images.TYPE_FOLDER);
	}

	@Override
	protected boolean isEnabledImpl() {
		if (!super.isEnabledImpl())
			return false;
		final ObjectProxyCache cache = client.CACHE_OBJECT.get();
		return cache.isPermitted(parentFolder.getPKValue(), DMSPermissionCode.FOLDER_CREATE);
	}

	@Override
	protected void performAction() {
		try {
			final SimpleParameterMap parameters = new SimpleParameterMap();
			parameters.put("Name", newFolderName);
			final PacketReplicateTemplateRequest request = PacketReplicateTemplateRequest.create(
					parentFolder.getPKValue(), DMSSpecialFunctionCode.MONATSAUSWERTUNG_TEMPLATE,
					parameters, client.getLoginname());
			final PacketReplicateTemplateReply reply = (PacketReplicateTemplateReply) client.sendAndWait(request);
			handleReply(reply);
		} catch (final Exception e) {
			client.message(new ErrorMessage(e));
		}
	}

}

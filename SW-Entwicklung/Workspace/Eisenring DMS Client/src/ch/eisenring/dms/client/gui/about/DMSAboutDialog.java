package ch.eisenring.dms.client.gui.about;

import java.awt.BorderLayout;
import java.awt.Window;

import ch.eisenring.commons.resource.images.Images;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.tag.TagSet;
import ch.eisenring.dms.client.DMSClient;
import ch.eisenring.dms.client.gui.AbstractDMSDialog;
import ch.eisenring.dms.shared.DMSConstants;
import ch.eisenring.gui.panels.AboutPanel;
import ch.eisenring.gui.window.DialogTags;
import ch.eisenring.gui.window.WindowTags;

@SuppressWarnings("serial")
public class DMSAboutDialog extends AbstractDMSDialog {

	private final AboutPanel aboutPanel;
	private final DMSAboutPanel appPanel;
	
	public DMSAboutDialog(final DMSClient client, final Window parentWindow) {
		super(client, new TagSet(
				WindowTags.TITLE, Strings.concat("Über ", DMSConstants.APPNAME),
				WindowTags.RESIZEABLE, WindowTags.RESIZEABLE_NO,
				WindowTags.ICON, Images.DMS,
				DialogTags.DIALOG_OWNER, parentWindow
		));
		this.appPanel = new DMSAboutPanel(client);
		this.aboutPanel = new AboutPanel(appPanel);
		initComponents();
		initLayout();
		pack();
	}

	private void initComponents() {
		setLayout(new BorderLayout());
	}

	private void initLayout() {
		getContentPane().removeAll();
		add(aboutPanel, BorderLayout.CENTER);
	}

}

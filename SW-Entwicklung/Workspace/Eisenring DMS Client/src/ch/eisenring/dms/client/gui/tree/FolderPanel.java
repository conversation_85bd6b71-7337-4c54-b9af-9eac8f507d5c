package ch.eisenring.dms.client.gui.tree;

import static ch.eisenring.gui.GUIConstants.HSPACE;
import static java.awt.GridBagConstraints.BOTH;
import static java.awt.GridBagConstraints.NORTHWEST;

import java.awt.GridBagLayout;
import java.awt.Insets;
import java.awt.Point;
import java.awt.datatransfer.Transferable;
import java.awt.event.FocusAdapter;
import java.awt.event.FocusEvent;
import java.awt.event.FocusListener;
import java.awt.event.InputEvent;
import java.awt.event.KeyEvent;
import java.util.Collections;

import javax.swing.Action;
import javax.swing.ActionMap;
import javax.swing.InputMap;
import javax.swing.JComponent;
import javax.swing.JScrollPane;
import javax.swing.KeyStroke;
import javax.swing.ScrollPaneConstants;
import javax.swing.TransferHandler;

import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.observable.Observable;
import ch.eisenring.core.observable.Observer;
import ch.eisenring.dms.client.action.ActionClipboardCopy;
import ch.eisenring.dms.client.action.ActionClipboardPaste;
import ch.eisenring.dms.client.action.ActionFileOpen;
import ch.eisenring.dms.client.gui.dnd.BinaryTransferable;
import ch.eisenring.dms.client.gui.dnd.FileListDropListener;
import ch.eisenring.dms.client.gui.dnd.OutlookDropListener;
import ch.eisenring.dms.client.gui.dnd.TargetLocator;
import ch.eisenring.dms.client.gui.dnd.VersionListDropListener;
import ch.eisenring.dms.client.model.ObjectProxyListener;
import ch.eisenring.dms.service.api.DMSObject;
import ch.eisenring.dms.service.codetables.DMSObjectStatus;
import ch.eisenring.dms.service.codetables.DMSObjectType;
import ch.eisenring.dms.shared.codetables.DMSObjectTypeProperties;
import ch.eisenring.dms.shared.model.api.VersionIdentifierList;
import ch.eisenring.gui.GridBagConstraints;
import ch.eisenring.gui.action.AbstractAction;
import ch.eisenring.gui.dnd.HEAGDropTargetManager;
import ch.eisenring.gui.memento.ListSelectionMemento;
import ch.eisenring.gui.memento.Memento;

@SuppressWarnings("serial")
public class FolderPanel extends ContentViewPanel implements ObjectProxyListener {

	protected final Observer<Object> observer = new Observer<Object>() {
		@Override
		public void observableChanged(final Observable<Object> observeable) {
			final DMSObject showingFolder = getShowingFolder();
			if (showingFolder != null) {
				final List<DMSObject> showing = List.asList(showingFolder);
				proxiesUpdated(showing);
			}
		}
	};

	protected final Observer<Integer> iconSizeObserver = new Observer<Integer>() {
		@Override
		public void observableChanged(final Observable<Integer> observable) {
			final int size = observable.get();
			list.setFixedCellHeight(size + 2);
			list.setFixedCellWidth(size + 320);
		}
	};

	protected final FocusListener focusListener = new FocusAdapter() {
		@Override
		public void focusGained(final FocusEvent event) {
			contentView.setLastFocusedView(FolderPanel.this);
		}
	};

	protected final FolderHelper helper;
	protected final DMSListModel listModel = new DMSListModel();
	protected final TransferHandler transferHandler = new TransferHandler() {
		@Override
		public int getSourceActions(final JComponent source) {
			return COPY;
		}

		@Override
		protected Transferable createTransferable(final JComponent source) {
			final VersionIdentifierList versions = getSelectedVersions();
			if (versions.isEmpty())
				return null;
			return new BinaryTransferable(client, versions);
		}

		@Override
		protected void exportDone(final JComponent source, final Transferable data, final int action) {
			super.exportDone(source, data, action);
		}
		
		public boolean importData(final JComponent component, final Transferable transferable) {
			return dropManager.importData(transferable);
		}
	};

	protected final IconObjectList list = new IconObjectList(client) {{
			setDragEnabled(true);
			setTransferHandler(transferHandler);
		}
	};

	protected final JScrollPane scrollPane = new JScrollPane();
	private final HEAGDropTargetManager dropManager;

	public FolderPanel(final CompoundContentView contentView) {
		super(contentView);
		this.helper = new FolderHelper(this,  list);
		initComponents();
		initLayout();
		client.SHOWING_OBJECTSTATUS.addObserver(observer);
		client.PERMISSIONS.addObserver(observer);
		client.SHOW_OBJECTSTATUS_IN_LIST.addObserver(observer);
		client.ICON_SIZE.addObserver(iconSizeObserver);
		final TargetLocator targetLocator = new TargetLocator() {
			@Override
			public DMSObject getTargetImpl(final Point location) {
				return location == null ? getShowingFolder() : getFolder(location);
			}
		};
		this.dropManager = new HEAGDropTargetManager(list, contentView);
		this.dropManager.addListener(new OutlookDropListener(contentView, targetLocator));
		this.dropManager.addListener(new VersionListDropListener(contentView, targetLocator));
		this.dropManager.addListener(new FileListDropListener(contentView, targetLocator));
	}

	private void initComponents() {
		setLayout(new GridBagLayout());
		list.setModel(listModel);
		list.addFocusListener(focusListener);
//		scrollPane.addKeyListener(new ContentViewKeyListener(this));
		scrollPane.setHorizontalScrollBarPolicy(ScrollPaneConstants.HORIZONTAL_SCROLLBAR_AS_NEEDED);
		scrollPane.setVerticalScrollBarPolicy(ScrollPaneConstants.VERTICAL_SCROLLBAR_ALWAYS);
		scrollPane.setViewportView(list);
		
		// enable paste on the list
        final ActionMap actionMap = list.getActionMap();
        actionMap.put(TransferHandler.getPasteAction().getValue(Action.NAME),
                TransferHandler.getPasteAction());
		final InputMap inputMap = list.getInputMap();
		inputMap.put(KeyStroke.getKeyStroke(KeyEvent.VK_V, InputEvent.CTRL_DWON_MASK),
		        TransferHandler.getPasteAction().getValue(Action.NAME));

		// key bindings
		ContentViewKeyBoardActions.registerKeyBoardActions(this);
	}
	
	private void initLayout() {
		removeAll();
		GridBagConstraints gc;
		gc = new GridBagConstraints(0, 0, 1, 1, 1D, 1D, NORTHWEST, BOTH, new Insets(0, 0, 0, HSPACE), 0, 0);
		add(scrollPane, gc);
	}

	protected DMSObject getFolder(final Point location) {
		DMSObject folder = helper.getItemForLocation(location);
		if (folder == null || !DMSObjectType.FOLDER.equals(folder.getType()))
			folder = getShowingFolder();
		if (folder != null && !DMSObjectType.FOLDER.equals(folder.getType()))
			return null;
		return folder;
	}

	@Override
	public List<DMSObject> getSelectedDocuments() {
		return helper.getSelectedDocuments();
	}

	@Override
	public VersionIdentifierList getSelectedVersions() {
		return helper.getSelectedVersions();
	}

	@Override
	public AbstractAction getCopyAction() {
		return new ActionClipboardCopy(this, transferHandler);
	}
	
	@Override
	public AbstractAction getPasteAction() {
		return isReadOnly() ? null : new ActionClipboardPaste(this, transferHandler);
	}

	// --------------------------------------------------------------
	// ---
	// --- Showing content control
	// ---
	// --------------------------------------------------------------
	private DMSObject showingFolder;

	@Override
	public DMSObject getShowingFolder() {
		synchronized (listModel.getLock()) {
			return showingFolder;
		}
	}
	
	@Override
	public void setShowingFolder(final DMSObject folder) {
		// this may be an odd place to add the listener,
		// but its the easiest way...
		cache.addListener(this);

		boolean changed;
		synchronized (listModel.getLock()) {
			final DMSObject oldShowingFolder = getShowingFolder();
			if (folder == null) {
				changed = oldShowingFolder != null;
			} else {
				changed = !folder.equals(oldShowingFolder);
			}
			if (changed) {
				this.contentView.historyState.recordState();
				this.list.clearSelection();
				this.showingFolder = folder;
				if (folder == null) {
					setListContent(null);
				} else {
					setListContent(getVisibleChildren(folder));
				}
			}
		}
	}

	@Override
	public void proxiesUpdated(final java.util.Collection<DMSObject> proxies) {
		boolean update = false;
		if (proxies.contains(getShowingFolder())) {
			update = true;
		} else {
			for (final DMSObject proxy : proxies) {
				if (listModel.contains(proxy)) {
					update = true;
					break;
				}
			}
		}
		if (update) {
			final Memento m = new ListSelectionMemento(list);
			setListContent(getVisibleChildren(getShowingFolder()));
			m.applyTo(list);
		}
	}

	protected List<DMSObject> getVisibleChildren(final DMSObject parent) {
		if (!isConnected())
			return List.emptyReadOnly(DMSObject.class);
		final DMSObject[] children = cache.getChildren(parent);
		if (children == null || children.length <= 0)
			return List.emptyReadOnly(DMSObject.class);
		final List<DMSObject> list = new ArrayList<DMSObject>(children.length);
		final DMSObjectStatus status = client.SHOWING_OBJECTSTATUS.get();
		for (int i=0; i<children.length; ++i) {
			final DMSObject child = children[i];
			final DMSObjectType type = child.getType();
			final DMSObjectTypeProperties properties = DMSObjectTypeProperties.get(type);
			if (properties == null || !cache.isPermitted(child, properties.getViewPermissionCode()))
				continue;
			if (child.getObjectStatus().isVisibleIn(status))
				list.add(child);
		}
		if (list.isEmpty())
			return List.emptyReadOnly(DMSObject.class);
		Collections.sort(list, contentView.toolBar.getSortOrder());
		return list;
	}

	protected void setListContent(final List<DMSObject> content) {
		final boolean connected = isConnected();
		listModel.setProxyList(connected ? content : null);
	}

	// --------------------------------------------------------------
	// ---
	// --- Asynchronous Update handling
	// ---
	// --------------------------------------------------------------

	// --------------------------------------------------------------
	// ---
	// --- Disposable implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public void dispose() {
		super.dispose();
		client.SHOWING_OBJECTSTATUS.removeObserver(observer);
		client.PERMISSIONS.removeObserver(observer);
		client.SHOW_OBJECTSTATUS_IN_LIST.removeObserver(observer);
	}

	// --------------------------------------------------------------
	// ---
	// --- ContentView implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public void setSelected(final Long longId, final int options) {
		helper.setSelected(longId, options);
	}
	
	@Override
	public void doubleClicked(final Long longId) {
		helper.setSelected(longId, ContentView.SCROLL);
		final DMSObject proxy = cache.getProxy(longId);
		if (proxy == null)
			return;
		final DMSObjectType type = proxy.getType();
		if (DMSObjectType.FILE.equals(type)) {
			// request current version for open
			final ActionFileOpen action = new ActionFileOpen(client, this, proxy);
			action.fire();
		} else if (DMSObjectType.FOLDER.equals(type)) {
			// descend into the folder
			contentView.setShowingFolder(proxy);
		}
	}
	
	public void restore(final Memento memento) {
		if (memento != null)
			memento.applyTo(list);
	}

}

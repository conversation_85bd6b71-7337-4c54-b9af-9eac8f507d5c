package ch.eisenring.dms.client.gui.menu;

import ch.eisenring.core.collections.Set;
import ch.eisenring.dms.client.action.AbstractDMSAction;
import ch.eisenring.dms.client.gui.employee.ActionMoveVehicle;
import ch.eisenring.dms.service.codetables.DMSFolderType;
import ch.eisenring.gui.menu.MenuBuilder;

final class NewFolderVehicleMove extends NewFolderBase {

	private final static Set<DMSFolderType> ACCEPTED_FOLDER_TYPES = Set.asReadonlySet(
			DMSFolderType.FAHRZEUG_ORDNER);

	@Override
	public boolean acceptsFolder(final NewFolderContext context) {
		if (context.contentView == null)
			return false;
		return ACCEPTED_FOLDER_TYPES.contains(context.folderType);
	}

	@Override
	public void addFolderItems(final NewFolderContext context, final MenuBuilder menuBuilder) {
		final AbstractDMSAction action = new ActionMoveVehicle(
				context.client, context.contentView, context.folder);
		menuBuilder.add(action);
	}

}

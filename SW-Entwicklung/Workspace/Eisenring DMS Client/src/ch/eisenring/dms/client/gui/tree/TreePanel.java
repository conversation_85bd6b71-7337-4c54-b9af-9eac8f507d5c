package ch.eisenring.dms.client.gui.tree;

import static ch.eisenring.gui.GUIConstants.HSPACE;
import static java.awt.GridBagConstraints.BOTH;
import static java.awt.GridBagConstraints.NORTHWEST;
import java.awt.*;
import java.awt.datatransfer.Transferable;
import java.awt.event.*;
import javax.swing.*;
import javax.swing.event.TreeSelectionListener;
import javax.swing.tree.TreePath;
import javax.swing.tree.TreeSelectionModel;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.interfaces.Disposable;
import ch.eisenring.core.observable.Observer;
import ch.eisenring.core.observable.ObserverNotificationPolicy;
import ch.eisenring.dms.client.action.ActionClipboardPaste;
import ch.eisenring.dms.client.gui.dnd.*;
import ch.eisenring.dms.client.gui.menu.DMSContextMenuUtil;
import ch.eisenring.dms.client.model.ObjectProxyCache;
import ch.eisenring.dms.client.model.ObjectProxyListener;
import ch.eisenring.dms.service.api.DMSObject;
import ch.eisenring.dms.service.codetables.DMSObjectType;
import ch.eisenring.dms.shared.model.api.VersionIdentifier;
import ch.eisenring.dms.shared.model.api.VersionIdentifierList;
import ch.eisenring.gui.GridBagConstraints;
import ch.eisenring.gui.action.AbstractAction;
import ch.eisenring.gui.components.HEAGTree;
import ch.eisenring.gui.dnd.DnDUsabilityEnhancer;
import ch.eisenring.gui.dnd.HEAGDropTargetManager;
import ch.eisenring.gui.memento.ComponentStateMemento;
import ch.eisenring.gui.util.KeyBoardActionUtil;

@SuppressWarnings("serial")
public class TreePanel extends ContentViewPanel implements Disposable {

	protected final ObjectProxyListener cacheListener = proxies -> refreshTree();

	protected final Observer<Object> observer = observeable -> refreshTree();

	protected final TreeRenderer treeRenderer;
	protected final JScrollPane scrollPane = new JScrollPane();
	protected final DMSTreeModel treeModel;
	protected final HEAGTree tree = new HEAGTree();
	protected final HEAGDropTargetManager dropManager;

	protected final FocusListener focusListener = new FocusAdapter() {
		@Override
		public void focusGained(final FocusEvent event) {
			contentView.setLastFocusedView(TreePanel.this);
		}
	};

	protected final MouseListener mouseListener = new MouseAdapter() {
		private boolean checkPopupTrigger(final MouseEvent event) {
			if (event.isConsumed() || !event.isPopupTrigger())
				return false;
			final DMSObject object = getObjectForLocation(event.getPoint());
			if (object == null)
				return false;
			event.consume();
			new DMSContextMenuUtil(contentView).showContextMenu(object, event);
			return true;
		}

		@Override
		public void mousePressed(final MouseEvent event) {
			if (checkPopupTrigger(event))
				return;
			if (event.getButton() == MouseEvent.BUTTON1) {
				final DMSObject object = getObjectForLocation(event.getPoint());
				if (object != null) {
					tree.setSelectionPath(cache.getTreePath(object));
				}
			}
		}

		@Override
		public void mouseReleased(final MouseEvent event) {
			checkPopupTrigger(event);
		}
	};

	/**
	 * Lister for tree selection events
	 */
	protected final TreeSelectionListener selectionListener = event -> {
		if (!DnDUsabilityEnhancer.isPerformingDnD(tree)) {
			final TreePath path = event.getPath();
			final DMSObject document;
			if (path == null) {
				document = null;
			} else {
				document = (DMSObject) path.getLastPathComponent();
			}
			contentView.setShowingFolder(document);
		}
	};

	protected final TransferHandler transferHandler = new TransferHandler() {
		@Override
		public int getSourceActions(final JComponent source) {
			return COPY;
		}

		@Override
		protected Transferable createTransferable(final JComponent source) {
			final VersionIdentifierList versions = getSelectedVersions();
			if (versions.isEmpty())
				return null;
			return new BinaryTransferable(client, versions);
		}

		@Override
		protected void exportDone(final JComponent source, final Transferable data, final int action) {
			super.exportDone(source, data, action);
		}

		@Override
		public boolean importData(final JComponent component, final Transferable transferable) {
			return dropManager.importData(transferable);
			//return dropListener.drop(null, transferable);
		}
	};

	public TreePanel(final CompoundContentView contentView) {
		super(contentView);

		this.treeRenderer = new TreeRenderer(client);
		this.treeModel = new DMSTreeModel(client, (Long) null);
		final TargetLocator targetLocator = location -> {
			if (location == null) {
				final List<DMSObject> selection = getSelectedDocuments();
				if (selection == null || selection.size() != 1)
					return null;
				return selection.get(0);
			}
			return getObjectForLocation(location);
		};
		this.dropManager = new HEAGDropTargetManager(tree, contentView);
		this.dropManager.addListener(new VersionListDropListener(contentView, targetLocator));
		this.dropManager.addListener(new OutlookDropListener(contentView, targetLocator));
		this.dropManager.addListener(new FileListDropListener(contentView, targetLocator));
		contentView.add(this);
		initComponents();
		initLayout();
		try {
			tree.setSelectionPath(new TreePath(treeModel.getRoot()));
		} catch (final Exception e) {
			// ignore
		}
	}

	private void initComponents() {
		setLayout(new GridBagLayout());
		tree.setModel(treeModel);
		tree.setRowHeight(calculateCurrentRowHeight());
		tree.setCellRenderer(treeRenderer);
		tree.addMouseListener(mouseListener);
		//		tree.addKeyListener(new ContentViewKeyListener(this));
		tree.addTreeSelectionListener(selectionListener);
		tree.setRootVisible(true);
		tree.getSelectionModel().setSelectionMode(TreeSelectionModel.SINGLE_TREE_SELECTION);
		tree.setDragEnabled(true);
		tree.setTransferHandler(transferHandler);
		tree.addFocusListener(focusListener);
		scrollPane.setHorizontalScrollBarPolicy(ScrollPaneConstants.HORIZONTAL_SCROLLBAR_AS_NEEDED);
		scrollPane.setVerticalScrollBarPolicy(ScrollPaneConstants.VERTICAL_SCROLLBAR_ALWAYS);
		scrollPane.setViewportView(tree);
		client.SHOWING_OBJECTSTATUS.addObserver(observer, ObserverNotificationPolicy.EDT_ASYNCHRONOUS);
		client.SHOW_OBJECTSTATUS_IN_TREE.addObserver(observer, ObserverNotificationPolicy.EDT_ASYNCHRONOUS);
		final CompoundContentView compoundView = contentView;
		if (compoundView != null) {
			compoundView.participantFilter.addObserver(observer, ObserverNotificationPolicy.EDT_ASYNCHRONOUS);
		}
		client.CACHE_OBJECT.get().addListener(cacheListener);
		client.PERMISSIONS.addObserver(observer);

		// hack away the F2 key binding JTree has by default
		KeyBoardActionUtil.unbindKey(tree, KeyStroke.getKeyStroke(KeyEvent.VK_F2, 0));

		// enable paste on the tree
		final ActionMap actionMap = tree.getActionMap();
		actionMap.put(TransferHandler.getPasteAction().getValue(Action.NAME),
			TransferHandler.getPasteAction());
		final InputMap inputMap = tree.getInputMap();
		inputMap.put(KeyStroke.getKeyStroke(KeyEvent.VK_V, InputEvent.CTRL_DOWN_MASK),
			TransferHandler.getPasteAction().getValue(Action.NAME));
		// key bindings
		ContentViewKeyBoardActions.registerKeyBoardActions(this);
	}

	private int calculateCurrentRowHeight() {
		return new JLabel("ABCDEFGHIJKLMNOPQRSTUVWXYZ abcdefghijklmnopqrstuvwxyz").getPreferredSize().height;
	}

	private void initLayout() {
		removeAll();
		GridBagConstraints gc;
		gc = new GridBagConstraints(0, 0, 1, 1, 1D, 1D, NORTHWEST, BOTH, new Insets(0, HSPACE, 0, 0), 0, 0);
		add(scrollPane, gc);
	}

	protected void refreshTree() {
		final ComponentStateMemento state = ComponentStateMemento.create(tree);
		final DMSTreeModel model = (DMSTreeModel) tree.getModel();
		model.visibleStatus = client.SHOWING_OBJECTSTATUS.get();
		final CompoundContentView compoundView = contentView;
		if (compoundView == null) {
			model.participant = null;
		} else {
			model.participant = compoundView.participantFilter.get();
		}
		model.fireRootChanged(TreePanel.this);
		state.applyTo(tree);
	}

	/**
	 * Finds object for event location
	 */
	protected DMSObject getObjectForLocation(final Point location) {
		final TreePath treePath = tree.getPathForLocation(location.x, location.y);
		if (treePath == null)
			return null;
		return (DMSObject) treePath.getLastPathComponent();
	}

	// --------------------------------------------------------------
	// ---
	// --- ContentView implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public AbstractAction getCopyAction() {
		return null;
	}

	@Override
	public AbstractAction getPasteAction() {
		return isReadOnly() ? null : new ActionClipboardPaste(this, transferHandler);
	}

	@Override
	public void setSelected(final Long longId, final int options) {
		setSelected(longId, options, tree, cache);
	}

	/**
	 * Implements setSelected() for any tree
	 */
	public static void setSelected(final Long longId, int options,
		final JTree tree, final ObjectProxyCache cache) {
		final DMSObject proxy = cache.getProxy(longId);
		final DMSObject parent = cache.getParent(proxy);
		if (parent != null)
			tree.expandPath(cache.getTreePath(parent));
		final TreePath path;
		if (DMSObjectType.FOLDER.equals(proxy.getType())) {
			path = cache.getTreePath(proxy);
			tree.setSelectionPath(path);
		} else if (parent != null) {
			path = cache.getTreePath(parent);
			options &= ~ContentView.COLLAPSE;
		} else {
			path = null;
		}
		if (path == null)
			return;
		tree.setSelectionPath(path);
		if ((options & ContentView.EXPAND) != 0) {
			tree.expandPath(path);
		} else if ((options & ContentView.COLLAPSE) != 0) {
			tree.collapsePath(path);
		}
		if ((options & ContentView.SCROLL) != 0) {
			final Component parentComponent = tree.getParent();
			if (parentComponent != null) {
				final int row = tree.getRowForPath(path);
				final Rectangle rect = tree.getRowBounds(row);
				if (rect != null) {
					rect.height = (parentComponent.getHeight() * 3) >> 2;
					tree.scrollRectToVisible(rect);
				}
			}
			tree.scrollPathToVisible(path);
		}
	}

	@Override
	public void doubleClicked(final Long longId) {
		setSelected(longId, ContentView.EXPAND | ContentView.SCROLL);
		final DMSObject object = cache.getProxy(longId);
		if (object != null) {
			tree.expandPath(cache.getTreePath(object));
		}
	}

	@Override
	public List<DMSObject> getSelectedDocuments() {
		final List<DMSObject> result = new ArrayList<>();
		final TreePath[] selection = tree.getSelectionPaths();
		if (selection == null)
			return result;
		for (final TreePath path : selection) {
			final DMSObject document = (DMSObject) path.getLastPathComponent();
			if (document != null && !result.contains(document))
				result.add(document);
		}
		return result;
	}

	@Override
	public VersionIdentifierList getSelectedVersions() {
		final VersionIdentifierList versions = new VersionIdentifierList();
		final TreePath[] selection = tree.getSelectionPaths();
		if (selection == null)
			return versions;
		for (final TreePath path : selection) {
			final DMSObject object = (DMSObject) path.getLastPathComponent();
			versions.add(new VersionIdentifier(object, null));
		}
		return versions;
	}

	@Override
	public DMSObject getShowingFolder() {
		return contentView.getShowingFolder();
	}

	@Override
	public void setShowingFolder(final DMSObject folder) {
		if (folder == null) {
			if (treeModel.showingFolder == null)
				return;
		} else if (folder.equals(treeModel.showingFolder)) {
			return;
		}
		treeModel.showingFolder = folder;
		refreshTree();
	}

	@Override
	public void dispose() {
		super.dispose();
		tree.removeMouseListener(mouseListener);
		client.PERMISSIONS.removeObserver(observer);
		client.SHOWING_OBJECTSTATUS.removeObserver(observer);
		client.CACHE_OBJECT.removeObserver(observer);
		client.SHOW_OBJECTSTATUS_IN_TREE.removeObserver(observer);
		final CompoundContentView compoundView = contentView;
		if (compoundView != null) {
			compoundView.participantFilter.removeObserver(observer);
		}
		scrollPane.setViewportView(null);
		tree.setModel(null);
		removeAll();
	}

}

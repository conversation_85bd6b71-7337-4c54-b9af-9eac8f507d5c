package ch.eisenring.dms.client.async;

import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.collections.Collection;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.dms.client.DMSClient;
import ch.eisenring.dms.client.gui.util.TempFile;
import ch.eisenring.dms.client.gui.util.VersionCacheFile;
import ch.eisenring.dms.client.gui.viewer.InternalViewer;
import ch.eisenring.dms.client.gui.viewer.ViewerContext;
import ch.eisenring.dms.service.api.DMSObject;
import ch.eisenring.dms.service.codetables.DMSObjectStatus;
import ch.eisenring.dms.service.codetables.DMSObjectType;
import ch.eisenring.dms.shared.client.util.FileOpenType;
import ch.eisenring.dms.shared.client.util.FileType;
import ch.eisenring.dms.shared.model.api.DMSBinary;
import ch.eisenring.dms.shared.model.api.VersionIdentifierList;
import ch.eisenring.gui.util.GUIUtil;

public class OpenAction extends AbstractBinaryAction {

	public OpenAction(final DMSClient client) {
		super(client);
	}

	// --------------------------------------------------------------
	// ---
	// --- Document to be processed
	// ---
	// --------------------------------------------------------------
	protected final List<DMSObject> documents = new ArrayList<DMSObject>();
	
	/**
	 * Adds a bunch of documents to print
	 */
	public void addDocuments(final Collection<DMSObject> documents) {
		for (final DMSObject object : documents) {
			addDocument(object);
		}
	}

	/**
	 * Adds the document to the list of objects to be printed
	 */
	public boolean addDocument(final DMSObject document) {
		if (document == null)
			return false;
		final DMSObjectType type = document.getType();
		if (!DMSObjectType.FILE.equals(type))
			return false;
		if (documents.contains(document))
			return false;
		return documents.add(document);
	}
	
	// --------------------------------------------------------------
	// ---
	// --- Implementation of action
	// ---
	// --------------------------------------------------------------
	@Override
	protected void performImpl() throws Exception {
		final ViewerContext context = new ViewerContext(client);
		final VersionIdentifierList externalList = new VersionIdentifierList();
		for (final DMSObject document : documents) {
			final FileOpenType openType = FileType.getOpenType(document);
			if (FileOpenType.OPEN_QUICKVIEW.equals(openType)) {
				// handle this document with internal viewer
				final boolean first = context.isEmpty();
				final DMSObject parent = cache.getParent(document);
				if (parent != null) {
					// also show all compatible documents in the quickviewer...
					final DMSObject[] children = cache.getChildren(parent);
					final DMSObjectStatus showingStatus = client.SHOWING_OBJECTSTATUS.get();
					for (final DMSObject child : children) {
						if (!DMSObjectType.FILE.equals(child.getType()))
							continue;
						if (!child.getObjectStatus().isVisibleIn(showingStatus))
							continue;
						if (!FileOpenType.OPEN_QUICKVIEW.equals(FileType.getOpenType(child)))
							continue;
						context.add(child);
					}
				}
				context.add(document);
				if (first)
					context.setCurrentDocument(document);
				context.setSelected(document, true);
			} else if (FileOpenType.OPEN_OS.equals(openType)) {
				// handle this document with external viewer
				externalList.add(document);
			} else {
				// this type can not be opened at all
			}
		}
		// process documents that show in external viewer
		if (!externalList.isEmpty()) {
			try {
				final List<DMSBinary> binaries = loader.getBinaries(externalList);
				for (final DMSBinary binary : binaries) {
					final DMSObject object = cache.getProxy(binary.getObjectRowId());
					final TempFile tempFile = VersionCacheFile.createReadOnly(object, binary);
					tempFile.open();
				}
			} catch (final Exception e) {
				client.message(new ErrorMessage(e));
			}
		}
		// process document that show in internal viewer
		if (!context.isEmpty()) {
			final Runnable task = new Runnable() {
				@Override
				public void run() {
					final InternalViewer viewer = new InternalViewer(context);
					viewer.setVisible(true);
				} 
			};
			GUIUtil.invokeLater(task);
		}
	}

}

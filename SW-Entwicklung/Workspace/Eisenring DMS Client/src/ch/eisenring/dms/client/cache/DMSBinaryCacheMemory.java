package ch.eisenring.dms.client.cache;

import java.util.concurrent.atomic.AtomicLong;

import ch.eisenring.app.shared.Configurable;
import ch.eisenring.core.application.Configuration;
import ch.eisenring.core.application.ConfigurationException;
import ch.eisenring.core.collections.Collection;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.dms.shared.cache.binary.DMSBinaryCache;
import ch.eisenring.dms.shared.model.api.DMSBinary;

/**
 * An implementation of DMSBinaryCache that caches 
 * data in memory (using soft references).
 */
public class DMSBinaryCacheMemory implements DMSBinaryCache, Configurable {

	private final Object lock = new Object();

	/**
	 * The maximum size of the cache (in bytes). Defaults to 16MB.
	 */
	private final AtomicLong maxCacheSize = new AtomicLong(16L << 20);
	
	/**
	 * The list of caches entries.
	 */
	private final List<MemoryCacheEntry> cacheList = new ArrayList<>(64);

	@Override
	public void tick() {
		// nothing for this cache
	}

	@Override
	public void setCachesize(final long maxCacheSize) {
		this.maxCacheSize.set(maxCacheSize);
	}

	@Override
	public void addBinary(final DMSBinary binary) {
		if (binary == null)
			return;
		final long maxCacheSize = this.maxCacheSize.get();
		if (binary.getRawsize() > maxCacheSize)
			return;
		final MemoryCacheEntry newEntry = new MemoryCacheEntry(binary);
		synchronized (lock) {
			// add the new entry to the cache
			final List<MemoryCacheEntry> cacheList = this.cacheList;
			cacheList.add(0, newEntry);
			// scan cache
			int i = 1;
			long total = maxCacheSize; 
			while (i < cacheList.size()) {
				final MemoryCacheEntry entry = cacheList.get(i);
				boolean remove = true;
				do {
					final DMSBinary version = entry.binary.get();
					// if the entry has been GC'd, remove it
					if (version == null)
						break;
					// if the entry has been replaced by the new entry, remove it
					if (entry.equals(newEntry))
						break;
					total -= version.getRawsize();
					// if the entry exceeds the cache size, remove it
					if (total < 0)
						break;
					remove = false;
				} while (false);
				if (remove) {
					cacheList.remove(i);
				} else {
					++i;
				}
			}
		}
	}

	@Override
	public DMSBinary getBinary(final long binaryRowId) {
		DMSBinary result = null;
		MemoryCacheEntry resultEntry = null;
		synchronized (lock) {
			final List<MemoryCacheEntry> cacheList = this.cacheList;
			for (int i=cacheList.size()-1; i>=0; --i) {
				final MemoryCacheEntry entry = cacheList.get(i);
				final DMSBinary binary = entry.binary.get();
				if (binary == null) {
					// this entry was GC'd, forget it
					cacheList.remove(i);
				} else if (binary.getPKValue() == binaryRowId) {
					resultEntry = entry;
					result = binary;
				}
			}
			// move the hit to front of list
			if (resultEntry != null) {
				cacheList.remove(resultEntry);
				cacheList.add(0, resultEntry);
			}
		}
		return result;
	}

	@Override
	public List<DMSBinary> getBinaries(final long objectRowId) {
		List<DMSBinary> result = null;
		synchronized (lock) {
			final List<MemoryCacheEntry> cacheList = this.cacheList;
			for (int i=cacheList.size()-1; i>=0; --i) {
				final MemoryCacheEntry entry = cacheList.get(i);
				final DMSBinary binary = entry.binary.get();
				if (binary == null) {
					// this entry was GC'd, forget it
					cacheList.remove(i);
				} else if (binary.getObjectRowId() == objectRowId) {
					// add to result list
					if (result == null)
						result = new ArrayList<DMSBinary>();
					result.add(binary);
				}
			}
		}
		return result;
	}

	@Override
	public void flush() {
		synchronized (lock) {
			cacheList.clear();
		}
	}

	/**
	 * Clears out any GC'd entries from the cache list.
	 * Must only be called while holding the cache lock.
	 */

	// --------------------------------------------------------------
	// ---
	// --- Configurable implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public void getConfigurationFileNames(final Collection<String> fileNames) {
		// nothing
	}

	@Override
	public void configure(final Configuration configuration) throws ConfigurationException {
		final Integer maxCacheMemory = configuration.getInteger("DMSClient:MaxCacheMemory", 0, 1000000, 16);
		setCachesize(maxCacheMemory.longValue() << 20);
	}
	
}

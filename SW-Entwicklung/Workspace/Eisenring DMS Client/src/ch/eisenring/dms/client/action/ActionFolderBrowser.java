package ch.eisenring.dms.client.action;

import java.awt.event.InputEvent;
import java.awt.event.KeyEvent;

import javax.swing.KeyStroke;

import ch.eisenring.dms.client.gui.DMSMainWindow;
import ch.eisenring.dms.client.gui.search.FileBrowserFrame;
import ch.eisenring.dms.client.gui.tree.ContentView;
import ch.eisenring.dms.service.api.DMSObject;

public class ActionFolderBrowser extends AbstractDMSAction {

	protected final DMSMainWindow window;
	
	public ActionFolderBrowser(final DMSMainWindow window) {
		super(window.getClient(), KeyStroke.getKeyStroke(KeyEvent.VK_B, InputEvent.CTRL_DOWN_MASK),
				"Neuer Ordner-_Browser...");
		this.window = window;
	}

	@Override
	protected void performAction() {
		if (window == null)
			return;
		final ContentView contentView = window.getContentView();
		if (contentView == null)
			return;
		final DMSObject object = contentView.getShowingFolder();
		if (object != null) {
			final FileBrowserFrame frame = new FileBrowserFrame(client, object);
			frame.setVisible(true);
		}
	}

}

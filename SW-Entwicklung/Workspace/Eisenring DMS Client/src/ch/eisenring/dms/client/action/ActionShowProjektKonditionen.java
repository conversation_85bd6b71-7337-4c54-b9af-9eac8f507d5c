package ch.eisenring.dms.client.action;

import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.dms.client.DMSClient;
import ch.eisenring.dms.shared.codetables.DMSRightCode;
import ch.eisenring.dms.shared.model.api.DMSPropertyAccessor;
import heag.huo.client.gui.konditionen.EPKMainWindow;
import heag.huo.client.resources.images.Images;

public class ActionShowProjektKonditionen extends ActionJumpToApp {

	public ActionShowProjektKonditionen(final DMSClient client, final DMSPropertyAccessor propertyAccessor) {
		super(client, propertyAccessor, "Projektkonditionen", Images.EPK);
		isEnabled();
	}

	@Override
	protected boolean isEnabledImpl() {
		return !Strings.isEmpty(getBasisProjektNummer());
	}

	@Override
	protected void performAction() {
		EPKMainWindow.doIt(getClient(), getBasisProjektNummer(), DMSRightCode.EPK_EDIT_PERMISSIONS.isPermitted());
	}

}

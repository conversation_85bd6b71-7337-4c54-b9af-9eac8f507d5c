package ch.eisenring.dms.client.gui.zipbuilder;

import java.awt.Color;
import java.awt.GridBagLayout;
import java.awt.datatransfer.Transferable;
import java.awt.dnd.DropTarget;
import java.awt.event.InputEvent;
import java.awt.event.KeyAdapter;
import java.awt.event.KeyEvent;
import java.awt.event.KeyListener;
import java.io.IOException;

import javax.swing.Action;
import javax.swing.ActionMap;
import javax.swing.DropMode;
import javax.swing.InputMap;
import javax.swing.JComponent;
import javax.swing.JScrollPane;
import javax.swing.KeyStroke;
import javax.swing.ListSelectionModel;
import javax.swing.ScrollPaneConstants;
import javax.swing.SwingConstants;
import javax.swing.TransferHandler;
import javax.swing.event.ListSelectionEvent;
import javax.swing.event.ListSelectionListener;

import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.datatypes.strings.format.impl.ByteSizeDescription;
import ch.eisenring.core.interfaces.Disposable;
import ch.eisenring.core.io.binary.BinaryHolder;
import ch.eisenring.dms.client.DMSClient;
import ch.eisenring.dms.client.gui.components.DMSComponentFactory;
import ch.eisenring.dms.client.gui.util.TempFile;
import ch.eisenring.dms.client.resources.Images;
import ch.eisenring.gui.GridBagConstraints;
import ch.eisenring.gui.balloontip.BalloonTipManager;
import ch.eisenring.gui.components.CompoundLinePanel;
import ch.eisenring.gui.components.HEAGFloatingButton;
import ch.eisenring.gui.components.HEAGLabel;
import ch.eisenring.gui.components.HEAGPanel;
import ch.eisenring.gui.components.HEAGTable;
import ch.eisenring.gui.components.HEAGTextField;
import ch.eisenring.gui.components.Spacer;
import ch.eisenring.gui.util.GUIUtil;

@SuppressWarnings("serial")
public class ZipBuilderPanel extends HEAGPanel implements Disposable {

	protected final DMSClient client;
	protected final ZipEntryList zipEntryList;
	protected final ZipBuilderDaemon zipBuilder;
	protected final HEAGTextField txtZipName;
	protected final HEAGTextField txtZipSize;
	protected final HEAGTextField txtB64Size;
	protected final ZipEntryTableModel tblModel;
	protected final HEAGTable tblEntries;
	protected final JScrollPane scrollPane; 
	protected final ZipEntryDropListener dropListener;
	//protected final HEAGDropTargetManager dropManager;

	// integrated tool bar
	protected final JComponent pnlTools = new HEAGPanel();
	protected final HEAGFloatingButton btnOpen;
	protected final HEAGFloatingButton btnClear;
	protected final HEAGFloatingButton btnUnZip;
	protected final HEAGFloatingButton btnZIP;

	protected final HEAGFloatingButton btnRemove = new HEAGFloatingButton(Images.DELETE, 32) {
		@Override
		public void buttonClicked() {
			final int[] rowIndices = tblEntries.getSelectedRows();
			if (rowIndices != null && rowIndices.length > 0) {
				final ArrayList<DMSZipEntry> selection = new ArrayList<DMSZipEntry>(rowIndices.length);
				for (final int rowIndex : rowIndices) {
					final DMSZipEntry entry = tblModel.getRow(rowIndex);
					selection.add(entry);
				}
				zipEntryList.removeEntries(selection);
			}
		}
	};


	private final ListSelectionListener selectionListener = new ListSelectionListener() {
		@Override
		public void valueChanged(final ListSelectionEvent event) {
			updateButtons();
		}
	};

	private final KeyListener tableKeyListener = new KeyAdapter() {
		@Override
		public void keyReleased(final KeyEvent event) {
			if (event.isConsumed())
				return;
			switch (event.getKeyCode()) {
				case KeyEvent.VK_DELETE: {
					event.consume();
					btnRemove.buttonClicked();
					break;
				}
			}
		}
	};

	public ZipBuilderPanel(final DMSClient client) {
		this.client = client;
		this.zipEntryList = client.ZIP_ENTRYLIST.get();
		this.tblModel = new ZipEntryTableModel(zipEntryList);
		this.tblEntries = new HEAGTable(tblModel);
		this.txtZipName = DMSComponentFactory.createDocumentNameField();
		this.txtZipSize = new HEAGTextField();
		this.txtB64Size = new HEAGTextField();
		this.scrollPane = new JScrollPane();
		this.btnZIP = new ZipResultButton(client, this);
		this.zipBuilder = new ZipBuilderDaemon(this, zipEntryList);
		this.btnOpen = new ZipAddFilesButton(client, zipEntryList);
		this.btnClear = new ZipClearFilesButton(client, zipEntryList);
		this.btnUnZip = new ZipExtractButton(client, zipEntryList);
		initComponents();
		initLayout();
		updateButtons();
//		this.dropManager = new HEAGDropTargetManager(zipEntryList);
//		this.dropManager.addListener(new OutlookDropListener(contentView, targetLocator));
//		this.dropManager.addListener(new VersionListDropListener2(contentView, targetLocator));
//		this.dropManager.addListener(new FileListDropListener(contentView, targetLocator));

		this.dropListener = new ZipEntryDropListener(client, zipEntryList);
		tblEntries.setTransferHandler(new TransferHandler() {
			@Override
			public int getSourceActions(final JComponent source) {
				return TransferHandler.NONE;
			}

			@Override
			protected Transferable createTransferable(final JComponent source) {
				return null;
			}

			@Override
			protected void exportDone(final JComponent source, final Transferable data, final int action) {
				super.exportDone(source, data, action);
			}
			
			public boolean importData(final JComponent component, final Transferable transferable) {
				return dropListener.drop(null, transferable);
			}
		});
		new DropTarget(tblEntries, dropListener);
		new DropTarget(scrollPane, dropListener);
	}

	private void initComponents() {
		txtZipName.setText(client.ZIP_FILENAME.get());
		pnlTools.setLayout(new GridBagLayout());
		setLayout(new GridBagLayout());
		scrollPane.setVerticalScrollBarPolicy(ScrollPaneConstants.VERTICAL_SCROLLBAR_AS_NEEDED);
		scrollPane.setHorizontalScrollBarPolicy(ScrollPaneConstants.HORIZONTAL_SCROLLBAR_AS_NEEDED);
		scrollPane.setViewportView(tblEntries);
		txtZipSize.setEditable(false);
		txtZipSize.setHorizontalAlignment(SwingConstants.RIGHT);
		txtZipSize.setToolTipText("<html>Physikalische Grösse der ZIP-Datei");
		GUIUtil.setMinSizeForText(txtZipSize, ".00.0 WW");
		txtB64Size.setEditable(false);
		txtB64Size.setHorizontalAlignment(SwingConstants.RIGHT);
		txtB64Size.setToolTipText("<html>Grösse wenn das ZIP per Email versendet wird");
		GUIUtil.setMinSizeForText(txtB64Size, ".00.0 WW");
		btnRemove.setToolTipText("<html>Markierte Dateien aus ZIP entfernen");
		tblModel.applyLayout(tblEntries);
		tblEntries.setSelectionMode(ListSelectionModel.MULTIPLE_INTERVAL_SELECTION);
		tblEntries.getSelectionModel().addListSelectionListener(selectionListener);
		tblEntries.addKeyListener(tableKeyListener);
		setZipSize(-1);
		
		// enable paste on the table
        final ActionMap actionMap = tblEntries.getActionMap();
        actionMap.put(TransferHandler.getPasteAction().getValue(Action.NAME),
                TransferHandler.getPasteAction());
		final InputMap inputMap = tblEntries.getInputMap();
		inputMap.put(KeyStroke.getKeyStroke(KeyEvent.VK_V, InputEvent.CTRL_MASK),
		        TransferHandler.getPasteAction().getValue(Action.NAME));
		tblEntries.setDragEnabled(true);
		tblEntries.setDropMode(DropMode.INSERT_ROWS);
		
	}

	private void initLayout() {
		removeAll();

		pnlTools.removeAll();
		pnlTools.add(btnOpen, GridBagConstraints.button(0, 0));
		pnlTools.add(btnUnZip, GridBagConstraints.button(0, 1));
		pnlTools.add(btnRemove, GridBagConstraints.button(0, 2));
		pnlTools.add(btnClear, GridBagConstraints.button(0, 3));
		pnlTools.add(btnZIP, GridBagConstraints.button(0, 4));
		pnlTools.add(new Spacer(), GridBagConstraints.area(0, 5, GridBagConstraints.NORTH, GridBagConstraints.VERTICAL).weightX(0));
		
		final CompoundLinePanel nameLine = new CompoundLinePanel();
		nameLine.addLabel(new HEAGLabel("ZIP-Name"));
		nameLine.addField(txtZipName, 99);
		
		final CompoundLinePanel sizeLine = new CompoundLinePanel();
		sizeLine.addLabel(new HEAGLabel("Grösse ZIP"));
		sizeLine.addField(txtZipSize, 99);
		sizeLine.addLabel(new HEAGLabel("in Email"));
		sizeLine.addField(txtB64Size, 99);

		add(nameLine, GridBagConstraints.field(0, 0));
		add(scrollPane, GridBagConstraints.area(0, 1));
		add(sizeLine, GridBagConstraints.field(0, 2));
		add(pnlTools, GridBagConstraints.area(1, 0).gridHeight(3).weightX(0));
	}

	/**
	 * Updates the zip size indicators, and also the button states
	 */
	public void setZipSize(final long zipSize) {
		final long b64Size = (long) (zipSize * 1.375D);
		final String zipText = zipSize < 0 ? "-" : ByteSizeDescription.getDescription(zipSize);
		final String b64Text = zipSize < 0 ? "-" : ByteSizeDescription.getDescription(b64Size);
		GUIUtil.invokeLater(new Runnable() {
			@Override
			public void run() {
				final Color fg = b64Size > (5 << 20) ? Color.RED : Color.BLACK;
				txtZipSize.setText(zipText);
				txtB64Size.setText(b64Text);
				txtB64Size.setForeground(fg);
				updateButtons();
			}
		});
	}

	/**
	 * Displays error message from builder daemon
	 */
	public void showBuilderError(final ErrorMessage message) {
		GUIUtil.invokeLater(new Runnable() {
			@Override
			public void run() {
				BalloonTipManager.show(btnZIP, message);
			}
		});
	}
	public void updateButtons() {
		btnClear.setEnabled(tblModel.getRowCount() > 0);
		btnRemove.setEnabled(tblEntries.getSelectedRowCount() > 0);
		boolean zipReady = zipBuilder.getZipData() != null;
		btnZIP.setEnabled(zipReady);
	}

	@Override
	public void dispose() {
		// disconnect listeners and stop ZIP builder thread
		client.ZIP_FILENAME.set(txtZipName.getText());
		tblModel.setZipEntryList(null);
		zipBuilder.dispose();
	}

	// --------------------------------------------------------------
	// ---
	// --- ZipTempFile management
	// ---
	// --------------------------------------------------------------
	private final Object zipTempLock = new Object(); 
	private BinaryHolder zipTempContent;
	private TempFile zipTempFile;
	private String zipTempName;

	public BinaryHolder getZIPContent() {
		return zipTempContent;
	}

	public TempFile getZIPTempFile() {
		TempFile result = null;
		synchronized (zipTempLock) {
			do {
				final BinaryHolder zipData = zipBuilder.getZipData();
				if (zipData == null) {
					// ZIP not available
					break;
				}
				String name = txtZipName.getText();
				if (Strings.isEmpty(name)) {
					name = "Unbenannt.zip";
				} else {
					name += ".zip";
				}
				if (zipData == zipTempContent && zipTempFile != null &&
						Strings.equals(name, zipTempName)) {
					// last result is still valid
					result = zipTempFile;
				} else {
					try {
						result = new TempFile(zipData, name, false);
						zipTempContent = zipData;
						zipTempName = name;
					} catch (final IOException e) {
						result = null;
					}
				}
			} while (false);
			zipTempFile = result;
			if (result == null) {
				zipTempContent = null;
			}
		}
		return result;
	}

}

package ch.eisenring.dms.client.gui.tree.action;

import java.awt.event.ActionEvent;

import javax.swing.AbstractAction;

import ch.eisenring.core.collections.List;
import ch.eisenring.dms.client.gui.tree.ContentView;
import ch.eisenring.dms.service.api.DMSObject;

@SuppressWarnings("serial")
public class OpenAction extends AbstractAction {

	protected final ContentView contentView;
	
	public OpenAction(final ContentView contentView) {
		this.contentView = contentView;
	}

	@Override
	public boolean isEnabled() {
		return super.isEnabled() && contentView != null;
	}

	@Override
	public void actionPerformed(final ActionEvent event) {
		final List<DMSObject> selected = contentView.getSelectedDocuments();
		if (selected != null && !selected.isEmpty()) {
			contentView.getCompoundContentView().doubleClicked(selected.get(0).getPKValue());
		}
	}

}

package ch.eisenring.dms.client.gui.tree;

import java.awt.Component;
import java.awt.Dimension;
import java.awt.Font;
import java.awt.Graphics;

import javax.swing.DefaultListCellRenderer;
import javax.swing.Icon;
import javax.swing.JComponent;
import javax.swing.JLabel;
import javax.swing.JList;

import ch.eisenring.core.observable.Observable;
import ch.eisenring.core.observable.Observer;
import ch.eisenring.core.observable.ObserverNotificationPolicy;
import ch.eisenring.dms.client.DMSClient;
import ch.eisenring.dms.client.codetables.DMSObjectLabelFormat;
import ch.eisenring.dms.service.api.DMSObject;
import ch.eisenring.dms.shared.client.util.DMSIconUtil;

@SuppressWarnings("serial")
public class ListElementRenderer extends DefaultListCellRenderer {

	protected final DMSClient client;
	protected DMSObject object;
	protected int iconSize = 64;
	protected int preferredHeight = 66;
	protected DMSObjectLabelFormat labelFormat = DMSObjectLabelFormat.NAMEONLY;
	protected Font defaultFont;
	protected Font font;
	protected final ListElementRendererComponent component = new ListElementRendererComponent();
	protected boolean showStatusIcon;
	
	private final Observer<Object> observer = new Observer<Object>() {
		public void observableChanged(final Observable<Object> observeable) {
			iconSize = client.ICON_SIZE.get().intValue();
			preferredHeight = iconSize + 2;
			labelFormat = client.LABEL_FORMAT.get(DMSObjectLabelFormat.NAMEONLY);
			final Float fontSize = labelFormat.getFontSize();
			if (fontSize == null) {
				font = defaultFont;
			} else {
				font = defaultFont.deriveFont(defaultFont.getSize2D() * fontSize);
			}
			showStatusIcon = Boolean.TRUE.equals(client.SHOW_OBJECTSTATUS_IN_LIST.get());
		}
	};

	public ListElementRenderer(final DMSClient client) {
		this.client = client;
		this.defaultFont = getFont();
		this.font = defaultFont;
		client.ICON_SIZE.addObserver(observer, ObserverNotificationPolicy.SYNCHRONOUS);
		client.LABEL_FORMAT.addObserver(observer, ObserverNotificationPolicy.SYNCHRONOUS);
		client.SHOW_OBJECTSTATUS_IN_LIST.addObserver(observer, ObserverNotificationPolicy.SYNCHRONOUS);
	}

	@Override
	public Component getListCellRendererComponent(final JList<?> list, final Object value,
												  final int index, final boolean isSelected,
												  final boolean cellHasFocus) {
		final JLabel label = (JLabel) super.getListCellRendererComponent(list, value, index, isSelected, cellHasFocus);
		component.setBackground(label.getBackground());
		component.setBorder(label.getBorder());
		object = (DMSObject) value;

		final String text = labelFormat.getLabel(object);
		label.setFont(font);
		label.setText(text);

		// fix up the preferred size
		setPreferredSize(null);
		Dimension d = getPreferredSize();
		d = new Dimension(d.width + 6 + iconSize, d.height);
		if (d.width < 256)
			d.width = 256;
		d.height = preferredHeight;
		component.setPreferredSize(d);
		return component;
	}

	@Override
	protected void paintBorder(final Graphics g) {
		// suppress painting of label border
	}
	
	@Override
	public void paintComponent(Graphics g) {
		super.paintComponent(g);
	}

	final class ListElementRendererComponent extends JComponent {

		public JLabel label = ListElementRenderer.this;

		{
			setOpaque(true);
		}
	
		@Override
		protected void paintChildren(final Graphics g) {
			// suppressed (this component has no children) 
		}
	
		@Override
		protected void paintComponent(final Graphics g) {
			// calculate the positions of the elements
			final int wTotal = getWidth();
			final int hTotal = getHeight();
			final int xLabel = iconSize + 5;
			final int wLabel = wTotal - xLabel;
			
			// fill background portion not covered by the label
			g.setPaintMode();
			g.setColor(label.getBackground());
			g.fillRect(0,  0, xLabel, hTotal);
			
			// position the label and paint it
			label.setBounds(xLabel, 0, wLabel, hTotal);
			final Graphics g2 = g.create(xLabel, 0, wLabel, hTotal);
			ListElementRenderer.this.paintComponent(g2);
			g2.dispose();

			// paint element border
			paintBorder(g);
			

			{ // paint the main icon
				final Icon thumbnail = DMSIconUtil.getIcon(object, iconSize);
				if (thumbnail != null) {
					final int x = ((iconSize - thumbnail.getIconWidth()) >> 1) + 1;
					final int y = ((iconSize - thumbnail.getIconHeight()) >> 1) + 1;
					thumbnail.paintIcon(this, g, x, y);
				}
			}

			// paint the status icon (if any)
			if (showStatusIcon) { 
				final Icon icon = StatusImages.Util.getListIcon(object.getObjectStatus());
				if (icon != null) {
					icon.paintIcon(this, g, 1, 1);
				}
			}
		}

	}	

}


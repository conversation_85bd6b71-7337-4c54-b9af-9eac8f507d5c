package ch.eisenring.dms.client.remotecontrol;

import ch.eisenring.dms.client.DMSClient;
import ch.eisenring.dms.service.api.DMSObject;
import ch.eisenring.dms.service.codetables.DMSPropertyCode;
import ch.eisenring.dms.service.util.LogiwareUtil;
import ch.eisenring.dms.shared.model.record.DMSObjectModel;
import ch.eisenring.dms.shared.network.AbstractDAOReplyPacket;
import ch.eisenring.dms.shared.network.PacketFindPropertyRequest;

class ElementLocatorProjectnumber extends ElementLocator {

	public final static String TYPE = "Projektnummer";

	public ElementLocatorProjectnumber(final DMSClient client) {
		super(client, TYPE);
	}

	@Override
	public ElementLocation locateElement(final String[] parameters) throws IllegalArgumentException {
		parameterCountCheck(parameters, 1);
		final String projectNumber = LogiwareUtil.getSigificantProjektnummer(parameters[0]);
		final PacketFindPropertyRequest request = PacketFindPropertyRequest.create(
				DMSPropertyCode.PROJECTNUMBER, projectNumber, client.getLoginname());
		final AbstractDAOReplyPacket reply = (AbstractDAOReplyPacket) client.sendAndWait(request);
		if (reply == null) {
			throw new RuntimeException("Zeitüberschreitung der Serveranfrage. Keine Antwort vom Server.");
		}
		final DMSObject object = reply.getFirstModel(DMSObjectModel.class);
		return ElementLocation.create(object);
	}

}

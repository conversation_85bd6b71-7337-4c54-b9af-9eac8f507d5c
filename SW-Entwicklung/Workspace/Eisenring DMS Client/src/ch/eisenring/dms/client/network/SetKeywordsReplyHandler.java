package ch.eisenring.dms.client.network;

import ch.eisenring.dms.client.DMSClient;
import ch.eisenring.dms.client.model.ObjectProxyCache;
import ch.eisenring.dms.shared.network.PacketSetKeywordsReply;
import ch.eisenring.dms.shared.network.cmd.ObjectMessageSet;
import ch.eisenring.network.PacketSink;

public final class SetKeywordsReplyHandler extends AbstractDMSPacketHandler<PacketSetKeywordsReply> {

	private SetKeywordsReplyHandler(final DMSClient client) {
		super(client, PacketSetKeywordsReply.class);
	}

	@Override
	public void handle(final PacketSetKeywordsReply packet, final PacketSink sink) {
		if (packet.isValid()) {
			final ObjectProxyCache cache = client.CACHE_OBJECT.get();
			final ObjectMessageSet changeMessages = packet.getMessages();
			cache.update(changeMessages);
		}
	}

}

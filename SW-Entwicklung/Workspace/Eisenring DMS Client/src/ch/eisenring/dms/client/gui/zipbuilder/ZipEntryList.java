package ch.eisenring.dms.client.gui.zipbuilder;

import java.util.Iterator;

import ch.eisenring.core.collections.Collection;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.iterators.ArrayIterator;
import ch.eisenring.core.logging.Logger;

/**
 * Holds ZIP entries to convert to ZIP.
 * Also keeps track of changes to the list and protects the list from
 * multiple thread access.
 */
public class ZipEntryList implements Iterable<DMSZipEntry> {

	private final List<DMSZipEntry> entryList = new ArrayList<DMSZipEntry>();

	private int modCount;

	// --------------------------------------------------------------
	// ---
	// --- Content modification
	// ---
	// --------------------------------------------------------------
	public boolean addEntry(final DMSZipEntry entry) {
		if (entry == null)
			return false;
		synchronized (getLock()) {
			if (entryList.contains(entry))
				return false;;
			entryList.add(entry);
			fireModified(true);
		}
		return true;
	}

	/**
	 * Adds the given entries to this list.
	 */
	public boolean addEntries(final Collection<DMSZipEntry> entries) {
		if (entries == null || entries.isEmpty())
			return false;
		boolean modified = false;
		synchronized (getLock()) {
			for (final DMSZipEntry entry : entries) {
				if (entry == null)
					continue;
				if (entryList.contains(entry))
					continue;
				entryList.add(entry);
				modified =  true;
			}
		}
		fireModified(modified);
		return modified;
	}

	/**
	 * Adds the entire content of another list to this list.
	 */
	public void addList(final ZipEntryList entryList) {
		if (entryList == null || entryList == this)
			return;
		addEntries(entryList.entryList);
	}

	public void removeEntry(final DMSZipEntry entry) {
		if (entry == null)
			return;
		final boolean removed;
		synchronized (getLock()) {
			removed = removeEntryImpl(entry);
		}
		fireModified(removed);
	}

	/**
	 * Removes multiple entries from the list.
	 */
	public void removeEntries(final Collection<DMSZipEntry> entries) {
		if (entries == null || entries.isEmpty())
			return;
		boolean modified = false;
		synchronized (getLock()) {
			for (final DMSZipEntry entry : entries) {
				modified |=  removeEntryImpl(entry);
			}
		}
		fireModified(modified);
	}

	/**
	 * Removes the given entry.
	 * Must hold the list lock when calling this!
	 */
	private boolean removeEntryImpl(final DMSZipEntry entry) {
		if (entry == null)
			return false;
		final int index0 = entryList.indexOf(entry);
		if (index0 < 0)
			return false;
		if (entry.isFolder()) {
			// remove all children of folder
			final int depth0 = Strings.countChars(entry.getFullName(), '/');
			entryList.remove(index0);
			while (index0 < entryList.size()) {
				final DMSZipEntry entry1 = entryList.get(index0);
				final int depth1 = Strings.countChars(entry1.getFullName(), '/');
				if (depth1 <= depth0)
					break;
				entryList.remove(index0);
			}
		} else {
			// just remove the file
			entryList.remove(index0);
		}
		return true;
	}

	/**
	 * Removes all entries from the list.
	 */
	public void clearEntries() {
		synchronized (getLock()) {
			if (!entryList.isEmpty()) {
				entryList.clear();
				fireModified(true);
			}
		}
	}

	/**
	 * Get the size of the list
	 */
	public int size() {
		synchronized (getLock()) {
			return entryList.size();
		}
	}

	/**
	 * Returns true if the entry list contains nothing
	 */
	public boolean isEmpty() {
		synchronized (getLock()) {
			return entryList.size() <= 0;
		}
	}

	/**
	 * Gets entry by index
	 */
	public DMSZipEntry getEntry(final int index) {
		if (index < 0)
			return null;
		synchronized (getLock()) {
			if (index >= entryList.size())
				return null;
			return entryList.get(index);
		}
	}

	/**
	 * Gets all entries
	 */
	public List<DMSZipEntry> getEntries() {
		synchronized (getLock()) {
			return new ArrayList<DMSZipEntry>(entryList);
		}
	}

	/**
	 * Gets iterator of the entries.
	 * The iterator uses a snapshot copy of the list, so it
	 * will not support remove nor will it see any changes
	 * made after the call.
	 */
	public Iterator<DMSZipEntry> iterator() {
		final DMSZipEntry[] entryArray;
		synchronized (getLock()) {
			entryArray = Collection.toArray(entryList, DMSZipEntry.class);
		}
		return ArrayIterator.get(entryArray);
	}

	// --------------------------------------------------------------
	// ---
	// --- Listener management and notification
	// ---
	// --------------------------------------------------------------
	public static interface ChangeListener {
		public void listChanged(final ZipEntryList zipEntryList);
	}

	private final List<ChangeListener> listeners = new ArrayList<ChangeListener>();

	protected Object getLock() {
		return this;
	}

	public void addChangeListener(final ChangeListener listener) {
		if (listener == null)
			return;
		synchronized (getLock()) {
			if (!listeners.contains(listener))
				listeners.add(listener);
		}
	}

	public void removeChangeListener(final ChangeListener listener) {
		synchronized (getLock()) {
			listeners.remove(listener);
		}
	}

	public int getModCount() {
		synchronized (getLock()) {
			return modCount;
		}
	}

	protected void fireModified(final boolean modified) {
		if (!modified)
			return;
		++modCount;
		for (final ChangeListener listener : listeners) {
			try {
				listener.listChanged(this);
			} catch (final Throwable t) {
				Logger.error(t);
			}
		}
	}

}


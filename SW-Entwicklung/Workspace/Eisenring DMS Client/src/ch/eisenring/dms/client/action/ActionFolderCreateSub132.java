package ch.eisenring.dms.client.action;

import ch.eisenring.dms.client.DMSClient;
import ch.eisenring.dms.client.gui.tree.ContentView;
import ch.eisenring.dms.client.resources.Images;
import ch.eisenring.dms.service.api.DMSObject;
import ch.eisenring.dms.shared.network.PacketCreateProjectSubFoldersRequest;
import ch.eisenring.logiware.code.pseudo.AbwicklungsartCode;

public class ActionFolderCreateSub132 extends ActionFolderCreateSpecial {

	private final static AbwicklungsartCode[] CREATE_AWA = {
			AbwicklungsartCode.AWA002, AbwicklungsartCode.AWA132
	};
	
	private final String basisNummer;

	public ActionFolderCreateSub132(
			final DMSClient client, 
			final ContentView contentView,
			final DMSObject parentFolder,
			final String basisNummer) {
		super(client, contentView, parentFolder, "",
				"Garderobe/Einbauschränke Unterordner erstellen",
				Images.TYPE_FOLDER_WRENCH);
		this.basisNummer = basisNummer;
	}

	@Override
	protected void performAction() {
		final PacketCreateProjectSubFoldersRequest request = PacketCreateProjectSubFoldersRequest.create(
				basisNummer, client.getLoginname(), CREATE_AWA);
		client.sendPacket(request);
	}

}

<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="IV_A4_4L" columnCount="2" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="385" columnSpacing="4" leftMargin="34" rightMargin="34" topMargin="25" bottomMargin="25" uuid="a1c6e160-f00d-42ab-a300-5e5194bb17af">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<parameter name="TITLE" class="java.lang.String" isForPrompting="false"/>
	<parameter name="DRUCKDATUM" class="java.lang.String" isForPrompting="false"/>
	<parameter name="USER" class="java.lang.String" isForPrompting="false"/>
	<field name="documentImage" class="java.awt.Image"/>
	<field name="documentName" class="java.lang.String"/>
	<field name="documentSize" class="java.lang.String"/>
	<field name="documentDate" class="java.lang.String"/>
	<field name="documentText" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band height="12" splitType="Prevent">
			<textField isBlankWhenNull="false">
				<reportElement uuid="10a823c1-7851-40b9-96d1-1f74a1eed368" key="PrintedBy" x="0" y="0" width="772" height="12" forecolor="#999999"/>
				<textElement textAlignment="Right">
					<font fontName="Avenir LT 35 Light" size="8" isBold="true" isItalic="true" pdfFontName="Helvetica-BoldOblique" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Gedruckt am " + $P{DRUCKDATUM} + " von " + $P{USER}]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<columnHeader>
		<band splitType="Prevent"/>
	</columnHeader>
	<detail>
		<band height="265" splitType="Stretch">
			<image scaleImage="RetainShape" hAlign="Center" vAlign="Middle">
				<reportElement uuid="0ff59b0a-2348-4ad8-8c49-cbaa72d46995" key="DocumentImage" mode="Transparent" x="1" y="1" width="385" height="245"/>
				<imageExpression><![CDATA[$F{documentImage}]]></imageExpression>
			</image>
			<textField isBlankWhenNull="false">
				<reportElement uuid="c7837112-751b-4280-9884-d38c45c6d86a" key="DocumentName" x="0" y="245" width="385" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font fontName="Avenir LT 35 Light" size="8" isBold="true" pdfFontName="Helvetica-Bold" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{documentName}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement uuid="b3bea1fc-acf1-43c2-b063-d50a4f410d4f" key="DocumentProperties" x="0" y="245" width="385" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Bottom">
					<font fontName="Avenir LT 35 Light" size="8" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Grösse " + $F{documentSize} + ", Version vom " + $F{documentDate}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>

package ch.eisenring.dms.client.network;

import ch.eisenring.dms.client.DMSClient;
import ch.eisenring.dms.client.model.ObjectProxyCache;
import ch.eisenring.dms.shared.network.PacketObjectMessages;
import ch.eisenring.dms.shared.network.cmd.ObjectMessageSet;
import ch.eisenring.network.PacketSink;
import ch.eisenring.network.implementation.PacketDispatchMode;

/**
 * Processes object messages and updates the object cache accordingly
 */
public final class ObjectMessagesHandler extends AbstractDMSPacketHandler<PacketObjectMessages> {

	private ObjectMessagesHandler(final DMSClient client) {
		super(client, PacketObjectMessages.class, PacketDispatchMode.EDT_ASYNCHRONOUS);
	}
	
	@Override
	public void handle(final PacketObjectMessages packet, final PacketSink sink) {
		final ObjectProxyCache cache = client.CACHE_OBJECT.get();
		final ObjectMessageSet messages = packet.getMessages();
		cache.update(messages);
	}

}

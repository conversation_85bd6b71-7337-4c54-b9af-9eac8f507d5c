package ch.eisenring.dms.client.network;

import ch.eisenring.dms.client.DMSClient;
import ch.eisenring.dms.shared.network.PacketCreateFileReply;
import ch.eisenring.network.PacketSink;
import ch.eisenring.network.implementation.PacketDispatchMode;

public final class CreateFileReplyHandler extends AbstractDMSPacketHandler<PacketCreateFileReply> {

	private CreateFileReplyHandler(final DMSClient client) {
		super(client, PacketCreateFileReply.class, PacketDispatchMode.SYNCHRONOUS);
	}

	@Override
	public void handle(final PacketCreateFileReply packet, final PacketSink sink) {
		// ignore this packet
	}

}

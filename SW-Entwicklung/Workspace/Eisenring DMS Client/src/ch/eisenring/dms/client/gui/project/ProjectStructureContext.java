package ch.eisenring.dms.client.gui.project;

import java.awt.Component;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.alterableview.AlterableView;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.dms.client.DMSClient;
import ch.eisenring.dms.shared.logiware.DMSLogiwareAuftrag;
import ch.eisenring.dms.shared.network.PacketProjectStructureQueryReply;

public class ProjectStructureContext {

	public final DMSClient client;
	public final Component window;

	private PacketProjectStructureQueryReply queryReply;
	private final List<ProjectStructureTableEntry> entryList = new ArrayList<>();

	public ProjectStructureContext(final DMSClient client, final Component window) {
		this.client = client;
		this.window = window;
	}

	public Component getWindow() {
		return window;
	}

	public void setQueryResult(final PacketProjectStructureQueryReply queryResult) {
		this.queryReply = queryResult;
		this.entryList.clear();
		if (queryResult != null) {
			for (final DMSLogiwareAuftrag auftrag : queryReply.getAuftraege()) {
				entryList.add(new ProjectStructureTableEntry(auftrag));
			}
		}
	}

	public PacketProjectStructureQueryReply getQueryResult() {
		return queryReply;
	}

	public List<ProjectStructureTableEntry> getTableEntries() {
		return AlterableView.of(entryList);
	}

	public List<DMSLogiwareAuftrag> getAuftraegeForCreate() {
		final List<DMSLogiwareAuftrag> result = new ArrayList<>();
		for (final ProjectStructureTableEntry entry : entryList) {
			if (Boolean.TRUE.equals(entry.getCreateFlag())) {
				result.add(entry.getAuftrag());
			}
		}
		return result;
	}

	public int getAuftragCount() {
		return entryList.size();
	}
}

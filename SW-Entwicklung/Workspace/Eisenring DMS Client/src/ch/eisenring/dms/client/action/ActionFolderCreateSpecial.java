package ch.eisenring.dms.client.action;

import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.dms.client.DMSClient;
import ch.eisenring.dms.client.gui.tree.ContentView;
import ch.eisenring.dms.client.model.ObjectProxyCache;
import ch.eisenring.dms.service.api.DMSObject;
import ch.eisenring.dms.shared.network.PacketReplicateTemplateReply;
import ch.eisenring.dms.shared.network.cmd.ObjectMessageConstants;
import ch.eisenring.dms.shared.network.cmd.ObjectMessageSet;
import ch.eisenring.gui.util.GUIUtil;

public abstract class ActionFolderCreateSpecial extends AbstractDMSAction {

	protected final DMSObject parentFolder;
	protected final ContentView contentView;
	protected final String newFolderName;
	protected Boolean newFolderExists;

	public ActionFolderCreateSpecial(
			final DMSClient client,
			final ContentView contentView,
			final DMSObject parentFolder,
			final String newFolderName,
			Object ... properties) {
		super(client, properties);
		this.contentView = contentView;
		this.parentFolder = parentFolder;
		this.newFolderName = newFolderName;
	}

	@Override
	protected boolean isEnabledImpl() {
		return super.isEnabledImpl() && !exists();
	}

	// --------------------------------------------------------------
	// ---
	// --- Exists detection for Child objects 
	// ---
	// --------------------------------------------------------------
	public boolean exists(final DMSObject parentFolder, final String objectname) {
		final ObjectProxyCache cache = client.CACHE_OBJECT.get();
		final DMSObject[] children = cache.getChildren(parentFolder);
		boolean objectExists = Boolean.FALSE;
		for (final DMSObject child : children) {
			if (Strings.equalsIgnoreCase(child.getObjectname(), objectname)) {
				objectExists = true;
				break;
			}
		}
		return objectExists;
	}

	protected boolean exists() {
		if (newFolderExists == null)
			newFolderExists = exists(parentFolder, newFolderName);
		return newFolderExists;
	}

	// --------------------------------------------------------------
	// ---
	// --- Manage GUI selection update 
	// ---
	// --------------------------------------------------------------
	public void selectCreatedFolder(final Long folderRowId) {
		selectCreatedFolder(contentView, parentFolder.getPKValue(), folderRowId);
	}

	public static void selectCreatedFolder(
			final ContentView contentView,
			final Long parentFolderRowId,
			final Long childFolderRowId) {
		if (childFolderRowId == null)
			return;
		final DMSClient client = contentView.getClient();
		final ObjectProxyCache cache = client.CACHE_OBJECT.get();
		final ObjectMessageSet messages = new ObjectMessageSet();
		messages.addFlags(parentFolderRowId, ObjectMessageConstants.CHILDREN);
		cache.update(messages);
		GUIUtil.invokeLater(new Runnable() {
			@Override
			public void run() {
				contentView.setSelected(childFolderRowId, ContentView.SCROLL | ContentView.EXPAND);
			}
		});
	}

	public void handleReply(final PacketReplicateTemplateReply reply) {
		if (reply == null) {
			return;
		} else if (reply.isValid()) {
			selectCreatedFolder(reply.getCreatedFolderRowId());
		} else {
			client.message(reply.getMessage());
		}
	}

}

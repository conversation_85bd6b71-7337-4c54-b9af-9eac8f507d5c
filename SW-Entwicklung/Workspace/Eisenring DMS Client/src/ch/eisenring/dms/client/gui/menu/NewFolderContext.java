package ch.eisenring.dms.client.gui.menu;

import java.awt.event.InputEvent;

import ch.eisenring.dms.client.DMSClient;
import ch.eisenring.dms.client.gui.tree.ContentView;
import ch.eisenring.dms.service.api.DMSObject;
import ch.eisenring.dms.service.codetables.DMSFolderType;
import ch.eisenring.dms.service.codetables.DMSPropertyCode;
import ch.eisenring.dms.shared.model.api.DMSPropertyAccessor;

/**
 * New folder menu context
 */
final class NewFolderContext {

	public final DMSClient client;
	public final ContentView contentView;
	public final DMSObject folder;
	public final DMSObject file;
	public final DMSPropertyAccessor propertyAccessor;
	public final InputEvent event;
	public final DMSFolderType folderType;

	NewFolderContext(final DMSClient client,
			final ContentView contentView,
			final DMSObject folder,
			final DMSObject file,
			final DMSPropertyAccessor propertyAccessor,
			final InputEvent event) {
		this.client = client;
		this.contentView = contentView;
		this.folder = folder;
		this.file = file;
		this.propertyAccessor = propertyAccessor;
		this.event = event;
		this.folderType = (DMSFolderType) propertyAccessor.getPropertyValue(DMSPropertyCode.FOLDERTYPE);
	}
	
}

package ch.eisenring.dms.client.gui.tree;

import javax.swing.JList;
import javax.swing.event.ListDataEvent;

import ch.eisenring.core.observable.Observable;
import ch.eisenring.core.observable.Observer;
import ch.eisenring.dms.service.api.DMSObject;
import ch.eisenring.gui.util.GUIUtil;

public class IconSizeObserver implements Observer<Object> {

	private final JList<DMSObject> listComponent;

	public IconSizeObserver(final JList<DMSObject> listComponent) {
		this.listComponent = listComponent;
	}

	@Override
	public void observableChanged(final Observable<Object> observeable) {
		GUIUtil.invokeLater(new Runnable() {
			@Override
			public void run() {
				final DMSListModel model = (DMSListModel) listComponent.getModel();
				model.fireEvent(new ListDataEvent(listComponent, ListDataEvent.CONTENTS_CHANGED, 0, model.getSize()));
			}
		});
	}
	
}

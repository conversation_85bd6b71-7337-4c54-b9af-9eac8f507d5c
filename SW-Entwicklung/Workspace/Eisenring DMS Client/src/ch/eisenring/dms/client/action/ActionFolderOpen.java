package ch.eisenring.dms.client.action;

import ch.eisenring.dms.client.DMSClient;
import ch.eisenring.dms.client.gui.tree.ContentView;
import ch.eisenring.dms.service.api.DMSObject;
import ch.eisenring.dms.service.codetables.DMSObjectType;
import ch.eisenring.dms.shared.codetables.DMSObjectTypeProperties;

public final class ActionFolderOpen extends ActionObjectBase {

	private final ContentView contentView;
	
	public ActionFolderOpen(final DMSClient client,
							final DMSObject object,
							final ContentView contentView) {
		super(client, null, object.getPKValue(), "Öffnen", DEFAULT_ACTION_TRUE);
		this.contentView = contentView;
	}

	@Override
	protected boolean isEnabledImpl() {
		if (!super.isEnabledImpl())
			return false;
		final DMSObject object = getObject();
		final DMSObjectType type = object.getType();
		if (!DMSObjectType.FOLDER.equals(type))
			return false;
		final DMSObjectTypeProperties properties = DMSObjectTypeProperties.get(type);
		return properties != null && isPermitted(properties.getViewPermissionCode());
	}

	@Override
	protected void performAction() {
		contentView.doubleClicked(getObjectRowId());
	}

}

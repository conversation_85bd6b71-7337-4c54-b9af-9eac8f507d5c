package ch.eisenring.dms.client.gui.tree;

import java.awt.Component;

import ch.eisenring.dms.client.DMSClient;

/**
 * Interface implemented by views that can be instructed
 * to select a DMSObject by rowId.
 */
public interface ObjectSelectView {

	public int NONE = 0;
	public int SCROLL = 1;
	public int EXPAND = 2;
	public int COLLAPSE = 4;

	/**
	 * Sets the currently selected object
	 * (usually in response to a user action)
	 */
	public void setSelected(final Long longId, final int options);

	/**
	 * Gets the DMSClient instance this view belongs to
	 */
	public DMSClient getClient();
	
	/**
	 * Gets the GUI component that realizes this view.
	 * Entirely implementation dependent what this really is.
	 */
	public Component getGUI();

}

package ch.eisenring.dms.client.gui.project;

import java.awt.GridBagLayout;

import javax.swing.JScrollPane;
import javax.swing.ScrollPaneConstants;

import ch.eisenring.dms.client.DMSClient;
import ch.eisenring.gui.GridBagConstraints;
import ch.eisenring.gui.components.HEAGPanel;
import ch.eisenring.gui.components.HEAGTable;

@SuppressWarnings("serial")
public class ProjectStructurePanel extends HEAGPanel {

	protected final JScrollPane scrollPane = new JScrollPane();
	protected final HEAGTable table = new HEAGTable() {};
	protected final ProjectStructureTableModel tableModel;

	public ProjectStructurePanel(final DMSClient client) {
		this.tableModel = new ProjectStructureTableModel();
		initComponents();
		initLayout();
	}

	private void initComponents() {
		setLayout(new GridBagLayout());
		table.setModel(tableModel);
		scrollPane.setVerticalScrollBarPolicy(ScrollPaneConstants.VERTICAL_SCROLLBAR_ALWAYS);
		scrollPane.setHorizontalScrollBarPolicy(ScrollPaneConstants.HORIZONTAL_SCROLLBAR_NEVER);
		scrollPane.setViewportView(table);
		tableModel.applyLayout(table);
	}

	private void initLayout() {
		removeAll();
		add(scrollPane, GridBagConstraints.area(0, 0).insetTop(0));
	}

}

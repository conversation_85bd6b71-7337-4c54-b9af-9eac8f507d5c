package ch.eisenring.dms.client.action;

import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.dms.client.DMSClient;
import ch.eisenring.dms.client.codetables.LinkOpenBehaviorCode;

public class ActionLinkOpenBehavior extends AbstractDMSAction {

	private final LinkOpenBehaviorCode behavior;
	
	public ActionLinkOpenBehavior(final DMSClient client, final LinkOpenBehaviorCode behavior) {
		super(client, AbstractCode.getLongText(behavior, "???"), behavior.getIcon());
		this.behavior = behavior;
		addObservable(client.LINK_OPEN_CODE);
	}

	@Override
	protected boolean isCheckedImpl() {
		final LinkOpenBehaviorCode current = client.LINK_OPEN_CODE.get();
		return AbstractCode.equals(behavior, current);
	}

	@Override
	protected void performAction() {
		client.LINK_OPEN_CODE.set(behavior);
	}

}

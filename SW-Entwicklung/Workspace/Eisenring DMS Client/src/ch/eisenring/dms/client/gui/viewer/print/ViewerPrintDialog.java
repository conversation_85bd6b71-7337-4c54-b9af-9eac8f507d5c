//package ch.eisenring.dms.client.gui.viewer.print;
//
//import ch.eisenring.core.tag.TagSet;
//import ch.eisenring.core.usersettings.UserSettings;
//import ch.eisenring.dms.client.DMSClient;
//import ch.eisenring.dms.client.gui.viewer.ViewerContext;
//import ch.eisenring.dms.client.resources.images.Images;
//import ch.eisenring.dms.client.resources.reports.Reports;
//import ch.eisenring.gui.window.DialogTags;
//import ch.eisenring.gui.window.FeedbackHandler;
//import ch.eisenring.gui.window.WindowTags;
//import ch.eisenring.print.client.gui.AbstractPrintDialog;
//import ch.eisenring.print.client.job.PrintReportJob;
//import ch.eisenring.print.client.job.PrintTags;
//
//@SuppressWarnings("serial")
//public class ViewerPrintDialog extends AbstractPrintDialog {
//
//	private final ViewerContext context;
//
//	public ViewerPrintDialog(final ViewerContext context) {
//		super(context.getClient(), new TagSet(
//				WindowTags.POSITION_SETTINGS_ID, "ViewerPrintWindow",
//				WindowTags.ICON, Images.PRINTER,
//				DialogTags.MODALITY, DialogTags.MODALITY_MODAL));
//		this.context = context;
//	}
//
//	@Override
//	protected UserSettings getUserSettings() {
//		return context.getClient().getUserSettings();
//	}
//
//	@Override
//	public FeedbackHandler getFeedbackHandler() {
//		return context.getClient().getFeedbackHandler();
//	}
//
//	@Override
//	public void doPrintJob() {
//		final PrintReportJob<?> job = new PrintReportJob<DMSClient>(
//				context.getClient(), Reports.IV_A4_4L, new ViewerReportSource(context));
//		job.addTag(PrintTags.PRINTER, getSelectedPrinter());
//		job.addTag(PrintTags.NUM_COPIES, getNumCopies());
//		enqueueJob(job);
//		
//	}
//
//}

package ch.eisenring.dms.client.action;

import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.util.SimpleParameterMap;
import ch.eisenring.dms.client.DMSClient;
import ch.eisenring.dms.client.gui.tree.ContentView;
import ch.eisenring.dms.client.resources.Images;
import ch.eisenring.dms.service.api.DMSObject;
import ch.eisenring.dms.service.codetables.DMSSpecialFunctionCode;
import ch.eisenring.dms.shared.network.PacketReplicateTemplateReply;
import ch.eisenring.dms.shared.network.PacketReplicateTemplateRequest;
import ch.eisenring.dms.shared.util.FolderTemplateVariables;

public class ActionFolderCreateSubAuftrag extends ActionFolderCreateSpecial {

	public ActionFolderCreateSubAuftrag(
			final DMSClient client, 
			final ContentView contentView,
			final DMSObject parentFolder,
			final String newFolderName) {
		super(client, contentView, parentFolder, newFolderName,
				Strings.concat(newFolderName, " Unterordner erstellen"),
				Images.TYPE_FOLDER_WRENCH);
	}

	@Override
	protected void performAction() {
		try {
			final SimpleParameterMap parameters = FolderTemplateVariables
					.AusfuehrungsPlaeneSubFolder.getParameters(newFolderName);
			final PacketReplicateTemplateRequest request = PacketReplicateTemplateRequest.create(
					parentFolder.getPKValue(), DMSSpecialFunctionCode.AUSFUEHRUNGSPLAENE_SUBTEMPLATE,
					parameters, client.getLoginname());
			final PacketReplicateTemplateReply reply = (PacketReplicateTemplateReply) client.sendAndWait(request);
			handleReply(reply);
		} catch (final Exception e) {
			client.message(new ErrorMessage(e));
		}
	}

}

package ch.eisenring.dms.client.gui.permission;

import java.util.Collections;

import javax.swing.JComponent;

import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.dms.client.DMSClient;
import ch.eisenring.dms.shared.model.data.PermissionDescriptor;
import ch.eisenring.dms.shared.model.record.DMSPermissionModel;
import ch.eisenring.gui.util.GUIUtil;
import ch.eisenring.model.PrimaryKey;
import ch.eisenring.model.TransactionContext;
import ch.eisenring.user.shared.service.RoleFacade;
import ch.eisenring.user.shared.service.USRService;
import ch.eisenring.user.shared.service.UserFacade;

class PermissionEditContext {

	public final DMSClient client;
	public final USRService userService;
	public final List<PermissionBase> roles;
	public final List<PermissionBase> users;
	public JComponent errorComponent;
	public boolean closeWindow;
	private PermissionDescriptor editingDescriptor;
	private PermissionDescriptor storedDescriptor;

	public PermissionEditContext(final DMSClient client, final PermissionDescriptor descriptor) throws Exception{
		this.client = client;
		final PermissionDescriptor editingDescriptor;
		if (descriptor == null) {
			// A new row id is created by creating a temporary instance
			// of the persistence mapping class. Although it may look
			// like it will cope with any changes to the persistent class,
			// this will break if the primary key is changed to a
			// non-numeric type or larger than 64 bits.
			final TransactionContext context = new TransactionContext();
			final DMSPermissionModel rowId = context.createModel(DMSPermissionModel.class, (PrimaryKey) null);
			editingDescriptor = new PermissionDescriptor(((Number) rowId.getPrimaryKey().getKeyValue()).longValue());
		} else {
			editingDescriptor = new PermissionDescriptor(descriptor);
		}
		this.storedDescriptor = descriptor;
		this.editingDescriptor = editingDescriptor;
		this.userService = client.locateService(USRService.class);
		
		final List<RoleFacade> roleList = userService.getAllRoles();
		this.roles = new ArrayList<PermissionBase>(roleList.size());
		for (final RoleFacade role : roleList) {
			roles.add(new PermissionRole(role));
		}
		Collections.sort(roles, PermissionRole.COMPARATOR_ASC);
		
		final List<UserFacade> userList = userService.getAllUsers();
		this.users = new ArrayList<PermissionBase>(userList.size() + 1);
		this.users.add(new PermissionUser(null)); // <-- Everybody
		for (final UserFacade user : userList) {
			users.add(new PermissionUser(user));
		}
		Collections.sort(users, PermissionUser.COMPARATOR_ASC);
	}

	/**
	 * Gets permission object by rowId
	 */
	public PermissionBase getPermissionBase(final long rowId) {
		for (int i=roles.size()-1; i>=0; --i) {
			final PermissionBase obj = roles.get(i);
			if (obj.getRowId() == rowId)
				return obj;
		}
		for (int i=users.size()-1; i>=0; --i) {
			final PermissionBase obj = users.get(i);
			if (obj.getRowId() == rowId)
				return obj;
		}
		return null;
	}

	/**
	 * Gets the descriptor currently editing
	 */
	public PermissionDescriptor getEditingDescriptor() {
		return editingDescriptor;
	}

	/**
	 * Returns true as long as the descriptor is new
	 */
	public boolean isNew() {
		return storedDescriptor == null;
	}

	/**
	 * Checks if the editing descriptor has been altered
	 */
	public boolean isChanged() {
		return !editingDescriptor.isSame(storedDescriptor);
	}

	/**
	 * Commits the current changes on editing descriptor
	 */
	public void commit() {
		storedDescriptor = new PermissionDescriptor(editingDescriptor);
	}
	
	/**
	 * Undoes the current changes on editing descriptor
	 */
	public void rollback() {
		if (isNew()) {
			editingDescriptor = new PermissionDescriptor(editingDescriptor.getRowId());
		} else {
			editingDescriptor = new PermissionDescriptor(storedDescriptor);
		}
	}

	/**
	 * Closes the window containing the error component
	 */
	public void closeWindow() {
		if (closeWindow && errorComponent != null) {
			GUIUtil.hideWindow(errorComponent);
		}
	}
}

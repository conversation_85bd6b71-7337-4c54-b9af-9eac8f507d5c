package ch.eisenring.dms.client.codetables;

import java.awt.event.InputEvent;
import java.awt.event.KeyEvent;

import javax.swing.KeyStroke;

import ch.eisenring.core.codetype.StaticCode;
import ch.eisenring.dms.client.DMSClient;

public final class IconSizeCode extends StaticCode {

	private final String KEY_HOTKEY = "\nHotkey";

	public final static IconSizeCode S20H = new IconSizeCode(32, "Winzig", KeyEvent.VK_1);
	public final static IconSizeCode S30H = new IconSizeCode(48, "Klein", KeyEvent.VK_2);
	public final static IconSizeCode S40H = new IconSizeCode(64, "Mittel", KeyEvent.VK_3);
	public final static IconSizeCode S60H = new IconSizeCode(96, "Gross", KeyEvent.VK_4);
	public final static IconSizeCode S80H = new IconSizeCode(128, "Riesig", KeyEvent.VK_5);
	
	private IconSizeCode(final int id, final String text, final Integer key) {
		super(id, Integer.valueOf(id), text, text);
		setData(KEY_HOTKEY, key);
	}

	public int getSize() {
		return getId();
	}

	public void apply(final DMSClient client) {
		final int size = getSize();
		client.ICON_SIZE.set(Integer.valueOf(size));
		client.getUserSettings().save(client.ICON_SIZE.getName(), size);
	}

	public KeyStroke getHotkey() {
		final Integer key = (Integer) getData(KEY_HOTKEY, null);
		if (key == null)
			return null;
		return KeyStroke.getKeyStroke(key.intValue(), InputEvent.CTRL_DOWN_MASK);
	}

}

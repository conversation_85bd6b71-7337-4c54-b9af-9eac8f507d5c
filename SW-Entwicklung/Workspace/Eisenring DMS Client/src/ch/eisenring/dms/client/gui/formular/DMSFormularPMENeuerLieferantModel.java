package ch.eisenring.dms.client.gui.formular;

import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import ch.eisenring.core.collections.impl.ArrayList;

public class DMSFormularPMENeuerLieferantModel {

	private static final String REGEX_EMAIL = ".+@.+\\..{2,3}";

	protected final DMSFormularField<String> begruendung = new DMSFormularField<String>("Begründung").setMandatory();
	protected final DMSFormularField<String> lieferantName = new DMSFormularField<String>("Name").setMandatory();
	protected final DMSFormularField<String> lieferantStrasseHausnummer = new DMSFormularField<String>("Strasse/Hausnummer").setMandatory();
	protected final DMSFormularField<String> lieferantPlzOrt = new DMSFormularField<String>("Postleitzahl/Ort").setMandatory();
	protected final DMSFormularField<String> lieferantLand = new DMSFormularField<String>("Land").setMandatory();
	protected final DMSFormularField<String> lieferantPostfachMitNr = new DMSFormularField<>("Postfach/Postfachnummer");
	protected final DMSFormularField<String> lieferantPostfachOhneNr = new DMSFormularField<>("Postfach ohne Nr.");
	protected final DMSFormularField<String> sprache = new DMSFormularField<String>("Sprache").setMandatory();
	protected final DMSFormularField<String> lieferantTelefon = new DMSFormularField<String>("Telefon").setMandatory();
	protected final DMSFormularField<String> lieferantFax = new DMSFormularField<String>("Fax").setMandatory();
	protected final DMSFormularField<String> lieferantEmail = new DMSFormularField<String>("E-Mail").setMandatory().setValidation(text -> text != null && text.matches(REGEX_EMAIL),
		"${name} darf nicht leer sein oder ist keine gültige E-Mail-Adresse");
	protected final DMSFormularField<String> lieferantHomepage = new DMSFormularField<>("Homepage URL");
	protected final DMSFormularField<String> bestellWaehrung = new DMSFormularField<String>("Bestellwährung").setMandatory();
	protected final DMSFormularField<String> zahlungsbedingungSkonto = new DMSFormularField<String>("Zahlungsbedingung/Skonto").setComboBoxValues(getZahlungsKonditionen());
	protected final DMSFormularField<String> incoterms = new DMSFormularField<>("Incoterms");
	protected final DMSFormularField<String> kontaktPerson = new DMSFormularField<>("Ansprechperson");
	protected final DMSFormularField<String> kontaktFunktion = new DMSFormularField<>("Funktion");
	protected final DMSFormularField<String> kontaktTelefon = new DMSFormularField<>("Telefon");
	protected final DMSFormularField<String> kontaktEmail = new DMSFormularField<String>("E-Mail").setValidation(text -> text == null || text.matches(REGEX_EMAIL),
		"${name} ist keine gültige E-Mail-Adresse");
	protected final DMSFormularField<String> bestellAdresse = new DMSFormularField<>("Bestelladresse");
	protected final DMSFormularField<Boolean> dummyArtikel = new DMSFormularField<>("Dummyartikel erfassen?");

	public Collection<DMSFormularField<String>> getStringFields() {
		return Arrays.asList(begruendung, lieferantName, lieferantStrasseHausnummer, lieferantPlzOrt, lieferantLand, lieferantPostfachMitNr, lieferantPostfachOhneNr, sprache, lieferantTelefon,
			lieferantFax, lieferantEmail, lieferantHomepage, bestellWaehrung, zahlungsbedingungSkonto, incoterms, kontaktPerson, kontaktFunktion, kontaktTelefon, kontaktEmail, bestellAdresse);
	}

	public Collection<DMSFormularField<Boolean>> getBooleanFields() {
		return Arrays.asList(dummyArtikel);
	}

	private List<String> getZahlungsKonditionen() {
		List<String> list = new ArrayList<>();
		list.add("30 Tage netto");
		list.add("30 Tage 2% Skonto, 60 Tage netto");
		list.add("60 Tage netto");
		list.add("10 Tage 2% Skonto, 30 Tage netto");
		list.add("30 Tage 3% Skonto");
		list.add("10 Tage 1.5% Skonto, 30 Tage netto");
		list.add("60 Tage 2%, 90 Tage netto");
		list.add("10 Tage netto");
		list.add("30 Tage 3%, 60 Tage netto");
		list.add("30 Tage 4%, 60 Tage netto");
		list.add("10 Tage 3%, 30 Tage netto");
		list.add("10 Tage 2%, 20 Tage netto");
		list.add("10 Tage 4%, 30 Tage netto");
		list.add("60 Tage 3%, 90 Tage netto");
		list.add("10 Tage 5%, 90 Tage netto");
		list.add("10 Tage 6%, 90 Tage netto");
		list.add("10 Tage 7%, 90 Tage netto");
		list.add("10 Tage 8%, 90 Tage netto");
		list.add("30 Tage 5%, 60 Tage netto");
		list.add("30 Tage 6%, 60 Tage netto");
		list.add("30 Tage 8%, 60 Tage netto");
		list.add("90 Tage 3%, 120 Tage netto");
		list.add("60 Tage 6%, 90 Tage netto");
		list.add("20 Tage 4%, 30 Tage netto");
		list.add("30 Tage 5%, 40 Tage netto");
		list.add("10 Tage 5%, 30 Tage netto");
		list.add("30 Tage 2%, 90 Tage netto");
		list.add("15 Tage 4%, 30 Tage netto");
		list.add("20 Tage netto");
		list.add("90 Tage 2%, 120 Tage netto");
		list.add("15 Tage 5%, 30 Tage netto");
		list.add("15 Tage 3%, 30 Tage netto");
		list.add("90 Tage netto");
		list.add("10 Tage 6% Skonto,");
		list.add("10 Tage 3% Skonto,");
		list.add("30 Tage 2% Skonto");
		list.add("45 Tage netto");
		list.add("3 Tage 5%, 30 Tage netto");
		list.add("14 Tage netto");
		list.add("60 Tage 3%, 61 Tage netto");
		list.add("10 Tage 4% Skonto");
		list.add("10 Tage 5% Skonto");
		list.add("2/3 bei Lieferung, 1/3 b. Fertigstellung");
		list.add("3 Tage netto");
		list.add("45 Tage 2%, 60 Tage Netto");
		list.add("45 Tage 3%, 46 Tage Netto");
		list.add("Barzahlung");
		list.add("(M) 30 Tage Netto");
		list.add("(M) 45 Tage Netto");
		list.add("120 Tage netto");
		list.add("10 Tage 1%, 30 Tage Netto");
		list.add("8 Tage 3%, 14 Tage netto");
		list.add("14 Tage 5%, 30 Tage Netto");
		list.add("8 Tage Netto");
		list.add("20 Tage 3%, 45 Tage Netto");
		list.add("LSV 1.5%");
		list.add("20 Tage 2%, 30 Tage Netto");
		list.add("14 Tage 2%, 30 Tage Netto");
		list.add("30 Tage 2%, 45 Tage Netto");
		list.add("LSV 3%");
		list.add("30 Tage 3%, 45 Tage Netto");
		list.add("15 Tage 2%, 30 Tage Netto");
		list.add("10 Tage 3%, 30 Tage 2%, 60 Tage netto");
		list.add("45 Tage 3%, 60 Tage Netto");
		list.add("14 Tage 3% Skonto, 30 Tage Netto");
		list.add("15 Tage netto");
		list.add("20 Tage 5%, 30 Tage Netto");
		list.add("30 Tage 1.5% Skonto, 35 Tage Netto");
		list.add("sofort fällig");
		return list;
	}

	public DMSFormularField<String> getBegruendung() {
		return begruendung;
	}

	public DMSFormularField<String> getLieferantName() {
		return lieferantName;
	}

	public DMSFormularField<String> getLieferantStrasseHausnummer() {
		return lieferantStrasseHausnummer;
	}

	public DMSFormularField<String> getLieferantPlzOrt() {
		return lieferantPlzOrt;
	}

	public DMSFormularField<String> getLieferantLand() {
		return lieferantLand;
	}

	public DMSFormularField<String> getLieferantPostfachMitNummer() {
		return lieferantPostfachMitNr;
	}

	public DMSFormularField<String> getLieferantPostfachOhneNummer() {
		return lieferantPostfachOhneNr;
	}

	public DMSFormularField<String> getSprache() {
		return sprache;
	}

	public DMSFormularField<String> getLieferantTelefon() {
		return lieferantTelefon;
	}

	public DMSFormularField<String> getLieferantFax() {
		return lieferantFax;
	}

	public DMSFormularField<String> getLieferantEmail() {
		return lieferantEmail;
	}

	public DMSFormularField<String> getLieferantHomepage() {
		return lieferantHomepage;
	}

	public DMSFormularField<String> getBestellWaehrung() {
		return bestellWaehrung;
	}

	public DMSFormularField<String> getZahlungsbedingungSkonto() {
		return zahlungsbedingungSkonto;
	}

	public DMSFormularField<String> getIncoterms() {
		return incoterms;
	}

	public DMSFormularField<String> getKontaktPerson() {
		return kontaktPerson;
	}

	public DMSFormularField<String> getKontaktFunktion() {
		return kontaktFunktion;
	}

	public DMSFormularField<String> getKontaktTelefon() {
		return kontaktTelefon;
	}

	public DMSFormularField<String> getKontaktEmail() {
		return kontaktEmail;
	}

	public DMSFormularField<String> getBestellAdresse() {
		return bestellAdresse;
	}

	public DMSFormularField<Boolean> getDummyArtikelErfassen() {
		return dummyArtikel;
	}

}

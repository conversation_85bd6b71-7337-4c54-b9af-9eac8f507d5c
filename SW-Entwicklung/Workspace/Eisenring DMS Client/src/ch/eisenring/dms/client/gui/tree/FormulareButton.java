package ch.eisenring.dms.client.gui.tree;

import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.awt.event.MouseListener;

import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.collections.List;
import ch.eisenring.dms.client.DMSClient;
import ch.eisenring.dms.client.action.ActionFormular;
import ch.eisenring.dms.client.resources.Images;
import ch.eisenring.dms.shared.formular.DMSFormularCode;
import ch.eisenring.gui.menu.MenuBuilder;

@SuppressWarnings("serial")
public final class FormulareButton extends ToolbarButton {

	private final MouseListener mouseListener = new MouseAdapter() {
		@Override
		public void mousePressed(final MouseEvent event) {
			checkPopupTrigger(event);
		}
		@Override
		public void mouseReleased(final MouseEvent event) {
			if (checkPopupTrigger(event))
				return;
			if (!event.isConsumed() && event.getButton() == MouseEvent.BUTTON1) {
				showPopup(event);
			}
		}
		private boolean checkPopupTrigger(final MouseEvent event) {
			if (event.isConsumed())
				return false;
			if (!event.isPopupTrigger())
				return false;
			showPopup(event);
			return true;
		}
		private void showPopup(final MouseEvent event) {
			event.consume();
			final MenuBuilder builder = getFormulareActions(contentView.getClient());
			builder.showPopupMenu(event);
		}
	};
	
	public FormulareButton(final CompoundContentView contentView) {
		super(contentView, Images.INVOICE);
		addMouseListener(mouseListener);
		setToolTipText("Ein Formular erstellen");
	}

	public static MenuBuilder getFormulareActions(final DMSClient client) {
		final MenuBuilder builder = new MenuBuilder();
		final List<DMSFormularCode> codes = AbstractCode.getInstances(DMSFormularCode.class, AbstractCode.Order.LongText);
		for (final DMSFormularCode code : codes) {
			if (code.isNull())
				continue;
			builder.add(new ActionFormular(client, code));
		}
		builder.sort(MenuBuilder.Order.ItemName);
		return builder;
	}

}

package ch.eisenring.dms.client.menu;

import java.awt.event.InputEvent;
import java.awt.event.KeyEvent;
import javax.swing.KeyStroke;
import ch.eisenring.app.client.action.ShowHelpAction;
import ch.eisenring.app.client.gui.menu.MainMenuBar;
import ch.eisenring.app.client.gui.menu.MainMenuFeature;
import ch.eisenring.app.client.gui.menu.MainMenuId;
import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.collections.List;
import ch.eisenring.dms.client.DMSClient;
import ch.eisenring.dms.client.action.*;
import ch.eisenring.dms.client.codetables.DMSObjectLabelFormat;
import ch.eisenring.dms.client.codetables.IconSizeCode;
import ch.eisenring.dms.client.codetables.LinkOpenBehaviorCode;
import ch.eisenring.dms.client.gui.DMSMainWindow;
import ch.eisenring.dms.client.gui.tree.FormulareButton;
import ch.eisenring.dms.client.resources.Images;
import ch.eisenring.dms.service.codetables.DMSObjectStatus;
import ch.eisenring.dms.shared.codetables.SafetyQuestionCode;
import ch.eisenring.email.client.action.ActionEMailer;
import ch.eisenring.email.client.action.EMailOptionsMenu;
import ch.eisenring.gui.menu.MenuBuilder;
import ch.eisenring.print.client.menu.PrintOptionsMenu;
import ch.eisenring.user.client.action.ActionDevKeys;
import ch.eisenring.user.client.action.ActionUserAccounts;
import ch.eisenring.user.client.action.ActionUserMyAccount;
import ch.eisenring.user.client.action.ActionUserRoles;

@SuppressWarnings("serial")
public class DMSMainMenuBar extends MainMenuBar<DMSClient, DMSMainWindow> {

	public DMSMainMenuBar(final DMSMainWindow window) {
		super(window.getClient(), window);
		initMainMenu(window.getClient());
	}

	protected void initMainMenu(final DMSClient client) {
		getFileMenu(client).addTo(this, MainMenuId.FILE);
		getViewMenu(client).addTo(this, MainMenuId.VIEW);
		getSearchMenu(client).addTo(this, MainMenuId.SEARCH);
		getOptionMenu(client).addTo(this, MainMenuId.OPTIONS);
		getAdminMenu(client).addTo(this, MainMenuId.ADMIN);
		getHelpMenu(client).addTo(this, MainMenuId.HELP);
	}

	protected MenuBuilder getFileMenu(final DMSClient client) {
		final MenuBuilder b = new MenuBuilder();
		b.add(new ActionCreateProjectFolder(client, window));
		b.add(new ActionCreateAbacusProject(client, window));
		b.add(getFormularMenu(client), "Formular erstellen", Images.INVOICE);
		b.add(new ActionEMailer(client.getCore(), KeyStroke.getKeyStroke(KeyEvent.VK_M, InputEvent.CTRL_DOWN_MASK)));
		b.addSeparator();
		b.add(new ActionExit(client, true));
		return b;
	}

	protected MenuBuilder getEditMenu(final DMSClient client) {
		final MenuBuilder b = new MenuBuilder();
		addMenuFeatures(b, MainMenuId.EDIT);
		return b;
	}

	protected MenuBuilder getViewMenu(final DMSClient client) {
		final MenuBuilder b = new MenuBuilder();
		addMenuFeatures(b, MainMenuId.VIEW);
		b.add(getShowingStatusMenu(client), "Anzeigen");
		b.add(getIconSizeMenu(client), "Symbolgrösse");
		b.addSeparator();
		b.add(new ActionNewWindow(window));
		b.add(new ActionFolderBrowser(window));
		b.add(new ActionShowZipBuilder(client, window));
		b.add(new ActionDocumentEditList(window));
		b.addSeparator();
		b.add(new ActionRefresh(client));
		return b;
	}

	protected MenuBuilder getFormularMenu(final DMSClient client) {
		final MenuBuilder builder = FormulareButton.getFormulareActions(client);
		return builder;
	}

	protected MenuBuilder getSearchMenu(final DMSClient client) {
		final MenuBuilder b = new MenuBuilder();
		addMenuFeatures(b, MainMenuId.SEARCH);
		b.add(new ActionSearch(client, window, null));
		return b;
	}

	protected MenuBuilder getShowingStatusMenu(final DMSClient client) {
		final MenuBuilder b = new MenuBuilder();
		b.add(new ActionShowingStatus(client, DMSObjectStatus.ACTIVE));
		b.add(new ActionShowingStatus(client, DMSObjectStatus.ARCHIVED));
		b.add(new ActionShowingStatus(client, DMSObjectStatus.RETRACTED));
		b.addSeparator();
		b.add(new ActionShowObjectstatusInTree(client));
		b.add(new ActionShowObjectstatusInList(client));
		return b;
	}

	@SuppressWarnings("unchecked")
	protected MenuBuilder getIconSizeMenu(final DMSClient client) {
		final MenuBuilder b = new MenuBuilder();
		final List<IconSizeCode> list = AbstractCode.getInstances(IconSizeCode.class, AbstractCode.Order.Id);
		for (final IconSizeCode sizeCode : list) {
			b.add(new ActionIconSize(client, sizeCode));
		}
		return b;
	}

	protected MenuBuilder getAdminMenu(final DMSClient client) {
		final MenuBuilder b = new MenuBuilder();
		if (false)
			addMenuFeatures(b, MainMenuId.ADMIN, MainMenuFeature.FEATURE_LEVEL_BASIC | MainMenuFeature.FEATURE_LEVEL_EXTENDED);
		b.add(new ActionUserMyAccount(client, window));
		b.addSeparator();
		b.add(new ActionUserAccounts(client, window));
		b.add(new ActionUserRoles(client, window));
		b.add(new ActionEditPermissions(window));
		b.addSeparator();
		b.add(new ActionDevKeys(client, window));
		return b;
	}

	protected MenuBuilder getHelpMenu(final DMSClient client) {
		final MenuBuilder b = new MenuBuilder();
		addMenuFeatures(b, MainMenuId.HELP);
		b.add(new ShowHelpAction(client, "DMS-Handbuch...", "../help", "SD_DMS-Handbuch.pdf", KeyStroke.getKeyStroke(KeyEvent.VK_F1, 0)));
		b.add(new ShowHelpAction(client, "Scanner Handbuch...", "../help", "SD_DMS-NSI-Scanner.pdf", (KeyStroke) null));
		b.add(new ShowHelpAction(client, "Versionsgeschichte...", "../help", "dms-versionhistory.html", KeyStroke.getKeyStroke(KeyEvent.VK_F1, InputEvent.SHIFT_DOWN_MASK)));
		b.add(new ActionDatabaseInfo(window));
		b.add(new ActionAbout(window));
		return b;
	}

	protected MenuBuilder getOptionMenu(final DMSClient client) {
		final MenuBuilder b = new MenuBuilder();
		addMenuFeatures(b, MainMenuId.OPTIONS);
		b.add(new PrintOptionsMenu(client));
		EMailOptionsMenu.addTo(client, b);
		{ // safety question sub menu
			final MenuBuilder s = new MenuBuilder();
			s.add(new ActionSafetyQuestionMode(client, SafetyQuestionCode.ALWAYS));
			s.add(new ActionSafetyQuestionMode(client, SafetyQuestionCode.MULTIPLE));
			s.add(new ActionSafetyQuestionMode(client, SafetyQuestionCode.NEVER));
			b.add(s, "Sicherheitsabfragen");
		}
		{ // label format sub menu
			final MenuBuilder s = new MenuBuilder();
			s.add(new ActionLabelFormat(client, DMSObjectLabelFormat.NAMEONLY));
			s.add(new ActionLabelFormat(client, DMSObjectLabelFormat.NAMEANDTYPE));
			s.add(new ActionLabelFormat(client, DMSObjectLabelFormat.DETAILED));
			s.add(new ActionLabelFormat(client, DMSObjectLabelFormat.MAXDETAILED));
			b.add(s, "Dokumente Anzeige-Typ");
		}
		{ // link open behavior
			final MenuBuilder s = new MenuBuilder();
			s.add(new ActionLinkOpenBehavior(client, LinkOpenBehaviorCode.SINGLE_WINDOW));
			s.add(new ActionLinkOpenBehavior(client, LinkOpenBehaviorCode.MULTIPLE_WINDOW));
			b.add(s, "Verknüpfungen öffnen");
		}
		b.addSeparator();
		b.add(new ActionClearZipOnClose(client));
		b.add(new ActionTerminateOnClose(client));
		return b;
	}

}

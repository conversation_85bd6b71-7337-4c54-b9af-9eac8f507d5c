package ch.eisenring.dms.client.gui.addfiles;

import java.awt.Component;
import java.awt.GridBagLayout;
import java.io.File;

import javax.swing.JScrollPane;
import javax.swing.ScrollPaneConstants;
import javax.swing.event.TreeSelectionEvent;
import javax.swing.event.TreeSelectionListener;
import javax.swing.tree.TreePath;
import javax.swing.tree.TreeSelectionModel;

import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.util.file.FileUtil;
import ch.eisenring.dms.client.DMSClient;
import ch.eisenring.dms.client.gui.components.DMSComponentFactory;
import ch.eisenring.dms.client.gui.tree.DMSTreeModel;
import ch.eisenring.dms.client.gui.tree.GotoPanel;
import ch.eisenring.dms.client.gui.tree.ObjectSelectView;
import ch.eisenring.dms.client.gui.tree.TreePanel;
import ch.eisenring.dms.client.gui.tree.TreeRenderer;
import ch.eisenring.dms.client.model.ObjectProxyCache;
import ch.eisenring.dms.service.api.DMSObject;
import ch.eisenring.dms.service.codetables.DMSDocumentType;
import ch.eisenring.dms.service.codetables.DMSObjectType;
import ch.eisenring.gui.components.HEAGCodeComboBox;
import ch.eisenring.gui.components.HEAGLabel;
import ch.eisenring.gui.components.HEAGPanel;
import ch.eisenring.gui.components.HEAGTextField;
import ch.eisenring.gui.components.HEAGTree;
import ch.eisenring.gui.model.ValidationResults;
import ch.eisenring.gui.util.GUIUtil;
import ch.eisenring.gui.util.LayoutUtil;

@SuppressWarnings("serial")
public class ImportAsPanel extends HEAGPanel implements ObjectSelectView {

	protected final DMSClient client;
	protected final HEAGCodeComboBox<DMSDocumentType> cmbDocType =
			new HEAGCodeComboBox<DMSDocumentType>(DMSDocumentType.class);
	protected final HEAGTextField txtObjectname = DMSComponentFactory.createDocumentNameField();
	protected final DMSTreeModel treeModel;
	protected final HEAGTree treeComponent = new HEAGTree();
	protected final TreeRenderer treeRenderer;
	protected final JScrollPane scrollPane = new JScrollPane();
	protected final GotoPanel gotoPanel;
	protected final DocumentDefaultNameHelper nameHelper;
	protected final File sourceFile;

	private final TreeSelectionListener treeListener = new TreeSelectionListener() {
		@Override
		public void valueChanged(final TreeSelectionEvent event) {
			final DMSObject folder = getTargetFolder();
			nameHelper.setTargetFolder(folder);
		}
	};

	public ImportAsPanel(final DMSClient client, final File sourceFile) {
		this.client = client;
		this.treeRenderer = new TreeRenderer(client);
		this.sourceFile = sourceFile;
		this.treeModel = new DMSTreeModel(client, (Long) null);
		this.nameHelper = new DocumentDefaultNameHelper(client, cmbDocType, txtObjectname);
		this.nameHelper.setFileName(FileUtil.removeFileExtension(sourceFile.getName()));
		this.treeComponent.addTreeSelectionListener(treeListener);
		this.gotoPanel = new GotoPanel(this);
		initComponents();
		initLayout();
	}

	private void initComponents() {
		treeComponent.setCellRenderer(treeRenderer);
		treeComponent.setModel(treeModel);
		treeComponent.getSelectionModel().setSelectionMode(TreeSelectionModel.SINGLE_TREE_SELECTION);
		scrollPane.setVerticalScrollBarPolicy(ScrollPaneConstants.VERTICAL_SCROLLBAR_ALWAYS);
		scrollPane.setHorizontalScrollBarPolicy(ScrollPaneConstants.HORIZONTAL_SCROLLBAR_AS_NEEDED);
		scrollPane.setViewportView(treeComponent);
	}

	private void initLayout() {
		removeAll();
		setLayout(new GridBagLayout());
		
		final LayoutUtil l = new LayoutUtil(2);

		// search panel
		add(new HEAGLabel("Finde Ordner"), l.label());
		add(gotoPanel, l.field());
	
		// folder tree
		add(scrollPane, l.area());
	
		// document type
		add(new HEAGLabel("Dokument-Art"), l.label());
		add(cmbDocType, l.field());
		
		// document name
		add(new HEAGLabel("Dokumentname"), l.label());
		add(txtObjectname, l.field());
	}

	@Override
	public void validateView(final ValidationResults results, final Object model) {
		super.validateView(results, model);

		// ensure a file name is entered
		if (Strings.isEmpty(txtObjectname.getText()))
			results.add("Dokumentname fehlt", txtObjectname);

		// ensure a target folder is selected
		final DMSObject folder = getTargetFolder();
		if (folder == null)
			results.add("Kein Zielordner ausgewählt", txtObjectname);
		
		if (!sourceFile.exists())
			results.add("Die Quelldatei \"" + sourceFile.getAbsolutePath() + "\" existiert nicht", txtObjectname);

		if (!sourceFile.canRead())
			results.add("Die Quelldatei \"" + sourceFile.getAbsolutePath() + "\" ist nicht lesbar", txtObjectname);
	}
	
	public DMSObject getTargetFolder() {
		final TreePath path = treeComponent.getSelectionPath();
		if (path != null) {
			final Object object = path.getLastPathComponent();
			if (object instanceof DMSObject) {
				final DMSObject folder = (DMSObject) object;
				if (DMSObjectType.FOLDER.equals(folder.getType())) {
					return folder;
				}
			}
		}
		return null;
	}

	// --------------------------------------------------------------
	// ---
	// --- Magic Folder find
	// ---
	// --------------------------------------------------------------
	protected void doMagicFind(final String filename) {
		// find consecutive digits
		final int l = Strings.length(filename);
		if (l < 3)
			return;
		int i = -1, s = -1, e = -1;
		while (++i < l) {
			final char c = filename.charAt(i);
			if (Strings.isASCIIDigit(c)) {
				if (s < 0) {
					s = i;
				} else {
					e = i + 1;
				}
			} else {
				if (e - s >= 3)
					break;
				s = e = -1;
			}
		}
		if ((e - s) < 3)
			return;
		// parse project number
		final String projectnumber = Strings.subString(filename, s, e);
		final Runnable task = new Runnable() {
			@Override
			public void run() {
				gotoPanel.performGoto(projectnumber);
			}
		};
		GUIUtil.invokeLater(task);
	}
	

	// --------------------------------------------------------------
	// ---
	// --- ObjectSelectView implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public Component getGUI() {
		return this;
	}

	@Override
	public DMSClient getClient() {
		return client;
	}

	@Override
	public void setSelected(final Long longId, final int options) {
		final ObjectProxyCache cache = client.CACHE_OBJECT.get();
		TreePanel.setSelected(longId, options, treeComponent, cache);
	}
	
}

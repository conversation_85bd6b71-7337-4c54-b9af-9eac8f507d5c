package ch.eisenring.dms.client.gui.tree;

import java.awt.GridBagLayout;
import java.awt.event.ActionEvent;
import java.awt.event.ItemEvent;
import java.awt.event.ItemListener;
import java.awt.event.KeyEvent;

import javax.swing.Action;
import javax.swing.KeyStroke;

import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.sort.Comparator;
import ch.eisenring.dms.client.codetables.SortCriterionCode;
import ch.eisenring.dms.client.gui.search.SearchPanel;
import ch.eisenring.dms.client.gui.tree.action.ShowPropertiesAction;
import ch.eisenring.dms.service.api.DMSObject;
import ch.eisenring.dms.shared.model.api.VersionIdentifierList;
import ch.eisenring.gui.GridBagConstraints;
import ch.eisenring.gui.action.AbstractAction;
import ch.eisenring.gui.components.CompoundLinePanel;
import ch.eisenring.gui.components.HEAGCodeComboBox;
import ch.eisenring.gui.components.HEAGLabel;
import ch.eisenring.gui.components.HEAGPanel;
import ch.eisenring.gui.util.GUIUtil;

@SuppressWarnings("serial")
public final class ContentViewToolBar extends ContentViewPanel {

	protected final HistoryButton btnBack;
	protected final HistoryButton btnForward;
	protected final ToolbarButton btnFavorites;
	protected final ToolbarButton btnBookmark;
	protected final ToolbarButton btnParent;
	protected final ToolbarButton btnZIP;
	protected final ToolbarButton btnCreateProject;
	protected final ToolbarButton btnFormulare;
	protected final HEAGCodeComboBox<SortCriterionCode> cmbSort;
	protected final IconSizeCombo cmbIconSize;
	protected final GotoPanel pnlGoto;
	protected final SearchPanel pnlSearch;
	protected final FilterParticipantsPanel pnlFilter;
	
	private final ItemListener sortListener = new ItemListener() {
		@Override
		public void itemStateChanged(final ItemEvent event) {
		    if (event.getStateChange() == ItemEvent.SELECTED) {
		    	final FolderPanel folderPanel = contentView.folderPanel;
		    	if (folderPanel != null) {
			    	final DMSObject tempParent = contentView.getShowingFolder();
			    	contentView.setShowingFolder(null);
			    	contentView.setShowingFolder(tempParent);
		    	}
		    }
		}
	};

	private final Action actionShowProperties = new AbstractAction() {
		protected void performAction() {
			final Action a2 = new ShowPropertiesAction(contentView.getLastFocusedView());
			a2.actionPerformed((ActionEvent) null);
		}
	};

	public ContentViewToolBar(final CompoundContentView contentView) {
		super(contentView);
		this.btnBack = new HistoryBackwardButton(contentView);
		this.btnForward = new HistoryForwardButton(contentView);
		this.btnFavorites = new FavoritesButton(contentView);
		this.btnBookmark = new BookmarkButton(contentView);
		this.btnParent = new ParentFolderButton(contentView);
		this.btnZIP = new ZipBuilderButton(contentView);
		this.btnCreateProject = new CreateProjectFolderButton(contentView);
		this.btnFormulare = new FormulareButton(contentView);
		this.pnlGoto = new GotoPanel(contentView);
		this.pnlFilter = new FilterParticipantsPanel(contentView);
		this.pnlSearch = new SearchPanel(contentView);
		this.cmbSort = new HEAGCodeComboBox<SortCriterionCode>(SortCriterionCode.class);
		this.cmbIconSize = new IconSizeCombo(client);
		initComponents();
		initLayout();
	}

	private void initComponents() {
		setLayout(new GridBagLayout());
		cmbSort.setSelectedCode(SortCriterionCode.TYPE_AND_NAME_ASC);
		cmbSort.addItemListener(sortListener);
		cmbSort.setDisplayIcons(true);
		GUIUtil.makeSameHeight(cmbSort, cmbIconSize, pnlGoto, pnlFilter);
		
		registerKeyboardAction(actionShowProperties,
				KeyStroke.getKeyStroke(KeyEvent.VK_F2, 0),
				WHEN_ANCESTOR_OF_FOCUSED_COMPONENT);
	}

	private void initLayout() {
		removeAll();
		final CompoundLinePanel line1 = new CompoundLinePanel();
		final CompoundLinePanel line2 = new CompoundLinePanel();
		
		line1.addButton(btnBack);
		line1.addButton(btnForward);
		line1.addButton(btnFavorites);
		line1.addButton(btnBookmark);
		line1.addButton(btnParent);
		line1.addButton(btnZIP);
		line1.addButton(btnCreateProject);
		line1.addButton(btnFormulare);
		line1.addLabel(new HEAGLabel("Finde Ordner"));
		line1.addButton(pnlGoto);
		line1.addLabel(new HEAGLabel("Finde in Dokumenten"));
		line1.addButton(pnlSearch);
		line1.addField(new HEAGPanel(), 10);
		
		line2.addLabel(new HEAGLabel("Beteiligung"));
		line2.addFixed(pnlFilter);
		line2.addLabel(new HEAGLabel("Sortierung"));
		line2.addFixed(cmbSort);
		line2.addLabel(new HEAGLabel("Symbole"));
		line2.addFixed(cmbIconSize);
		line2.addField(new HEAGPanel(), 10);
		
		add(line1, GridBagConstraints.field(0, 0));
		add(line2, GridBagConstraints.field(0, 1));
	}

	public Comparator<DMSObject> /*SortCriterionCode*/ getSortOrder() {
		final SortCriterionCode order = cmbSort.getSelectedCode();
		if (order == null)
			return SortCriterionCode.TYPE_AND_NAME_ASC.getComparator();
		return order.getComparator();
	}
	
	// --------------------------------------------------------------
	// ---
	// --- ContentView implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public void historyChanged() {
		btnBack.historyChanged();
		btnForward.historyChanged();
	}

	@Override
	public AbstractAction getCopyAction() {
		return null;
	}
	
	@Override
	public AbstractAction getPasteAction() {
		return null;
	}

	@Override
	public List<DMSObject> getSelectedDocuments() {
		return new ArrayList<DMSObject>();
	}

	@Override
	public VersionIdentifierList getSelectedVersions() {
		return new VersionIdentifierList();
	}

	@Override
	public DMSObject getShowingFolder() {
		return contentView.getShowingFolder();
	}

	@Override
	public void setShowingFolder(final DMSObject folder) {
		// nothing
	}

}

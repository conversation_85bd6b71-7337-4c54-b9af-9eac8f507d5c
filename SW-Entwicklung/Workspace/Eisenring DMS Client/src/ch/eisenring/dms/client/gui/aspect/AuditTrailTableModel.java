package ch.eisenring.dms.client.gui.aspect;

import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.dms.shared.model.record.DMSAuditTrailModel;
import ch.eisenring.gui.components.HEAGListTableModel;
import ch.eisenring.gui.components.HEAGTableLayout;
import ch.eisenring.gui.components.SortKey;

@SuppressWarnings("serial")
public final class AuditTrailTableModel extends HEAGListTableModel<DMSAuditTrailModel> {

	// --------------------------------------------------------------
	// ---
	// --- Table layout
	// ---
	// --------------------------------------------------------------
	private final static HEAGTableLayout LAYOUT; static {
		final HEAGTableLayout l = new HEAGTableLayout("AuditTrailTableLayout", 2, new SortKey(0, false));
		l.addColumn("Datum/Zeit", 128, DMSAuditTrailModel.Order.RowId);
		l.addColumn("Benutzer", 48, 48, 256, DMSAuditTrailModel.Order.MadeBy);
		l.addColumn("Aktion", 128, 256, 2000);
		LAYOUT = l;
	}

	public AuditTrailTableModel() {
		super(LAYOUT);
	}

	@Override
	public Object getColumnValue(final DMSAuditTrailModel auditTrail, final int columnIndex) {
		if (auditTrail == null)
			return null;
		switch (columnIndex) {
			default: return "???";
			case 0: return TimestampUtil.DATETIME.format(auditTrail.getMadeOn());
			case 1: return Strings.trim(auditTrail.getMadeBy());
			case 2: return Strings.trim(auditTrail.getDescription());
		}
	}
	
}

package ch.eisenring.dms.client.gui.employee;

import ch.eisenring.dms.client.DMSClient;
import ch.eisenring.dms.client.action.AbstractDMSAction;
import ch.eisenring.dms.client.gui.AbstractDMSDialog;
import ch.eisenring.dms.client.gui.tree.ContentView;
import ch.eisenring.dms.client.model.ObjectProxyCache;
import ch.eisenring.dms.client.resources.Images;
import ch.eisenring.dms.service.api.DMSObject;
import ch.eisenring.dms.service.codetables.DMSPermissionCode;
import ch.eisenring.gui.window.AbstractBaseWindow;
import ch.eisenring.gui.window.DialogTags;

public class ActionMoveEmployee extends AbstractDMSAction {

	protected final ContentView contentView;
	protected final DMSObject employeeFolder;

	public ActionMoveEmployee(
			final DMSClient client,
			final ContentView contentView,
			final DMSObject employeeFolder) {
		super(client, Images.GOTO, "Übertritt...");
		this.contentView = contentView;
		this.employeeFolder = employeeFolder;
	}

	@Override
	protected boolean isEnabledImpl() {
		if (!super.isEnabledImpl())
			return false;
		final ObjectProxyCache cache = client.CACHE_OBJECT.get();
		return cache.isPermitted(employeeFolder, DMSPermissionCode.FOLDER_CREATE);
	}

	@Override
	protected void performAction() {
		final AbstractDMSDialog dialog = new MoveEmployeeDialog(contentView,
				employeeFolder.getPKValue(),
				DialogTags.MODALITY, DialogTags.MODALITY_MODAL,
				DialogTags.DIALOG_OWNER, contentView.getGUI());
		AbstractBaseWindow.showWindow(dialog);
	}

}

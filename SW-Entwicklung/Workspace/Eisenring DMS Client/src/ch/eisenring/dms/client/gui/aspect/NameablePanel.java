package ch.eisenring.dms.client.gui.aspect;

import java.awt.Component;
import java.awt.GridBagLayout;

import javax.swing.JComponent;

import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.util.OperationResult;
import ch.eisenring.dms.client.DMSClient;
import ch.eisenring.dms.client.gui.components.DMSComponentFactory;
import ch.eisenring.dms.service.api.DMSObject;
import ch.eisenring.dms.service.codetables.DMSObjectType;
import ch.eisenring.dms.shared.codetables.DMSObjectTypeProperties;
import ch.eisenring.dms.shared.network.AbstractDAORequestPacket;
import ch.eisenring.dms.shared.network.PacketRenameObjectRequest;
import ch.eisenring.gui.GridBagConstraints;
import ch.eisenring.gui.components.HEAGLabel;
import ch.eisenring.gui.components.HEAGTextField;
import ch.eisenring.gui.model.ValidationResults;
import ch.eisenring.network.packet.AbstractPacket;

/**
 * Panel for renaming objects.
 * Can be subclassed to create new objects.
 */
@SuppressWarnings("serial")
public class NameablePanel extends AspectPanel {

	protected final HEAGTextField txtName = DMSComponentFactory.createDocumentNameField();

	public NameablePanel(final DMSClient client,
			             final DMSObject object) {
		super(client, object);
		initComponents();
		initLayout();
	}

	private void initComponents() {
		setLayout(new GridBagLayout());
		// we do NOT provide a default name if the panel
		// is shown for CREATING a new object.
		txtName.setValue(object == null ? "" : object.getObjectname());
	}

	private void initLayout() {
		removeAll();
		int y = -1;
		final JComponent header = getDecorationHeader();
		if (header != null) {
			++y;
			add(header, GridBagConstraints.decorator(0, y));
		}
		++y;
		add(new HEAGLabel("Name"), GridBagConstraints.label(0, y));
		add(txtName, GridBagConstraints.field(1, y));
	}

	protected String getGUIValue() {
		return txtName.getText();
	}

	/**
	 * Can optionally provide a decorator for the panel
	 */
	protected JComponent getDecorationHeader() {
		return null;
	}

	// --------------------------------------------------------------
	// ---
	// --- AspectPanel implementation
	// ---
	// --------------------------------------------------------------
	@Override
	protected void showingFirstTimeImpl() {
		// nothing
	}

	@Override
	protected OperationResult<Component> validateContents() {
		final String guiValue = getGUIValue();
		if (Strings.isEmpty(guiValue)) {
			return new OperationResult<Component>(new ErrorMessage("Der Name darf nicht leer sein"), null, txtName);
		}
		return new OperationResult<Component>(txtName);
	}

	/**
	 * Must overwrite this if the panel is to be used
	 * for something other than renaming.
	 */
	@Override
	protected AbstractDAORequestPacket createRequestImpl() {
		final String guiValue = getGUIValue();
		return PacketRenameObjectRequest.create(object.getPKValue(), client.getLoginname(), guiValue);
	}

	@Override
	protected OperationResult<Component> processReply(final AbstractPacket reply) {
		if (reply.isValid()) {
			return new OperationResult<Component>(this);
		} else {
			return new OperationResult<Component>(reply.getMessage(), null, txtName);
		}
	}

	@Override
	protected void updateEditableImpl() {
		final boolean editable;
		if (object == null) {
			editable = true;
		} else {
			final DMSObjectType type = object.getType();
			final DMSObjectTypeProperties properties = DMSObjectTypeProperties.get(type);
			editable = properties != null && cache.isPermitted(object, properties.getAlterPermissionCode());
		}
		setEditable(editable);
	}

	// --------------------------------------------------------------
	// ---
	// --- HEAGPanelLazy implementation
	// ---
	// --------------------------------------------------------------
	private boolean changedStatus;
	
	@Override
	public void evaluateChanged(final Object model) {
		boolean changedStatus = false;
		if (wasShown() && isEditable()) {
			if (object != null) {
				final String guiValue = getGUIValue();
				final String objValue = object.getObjectname();
				changedStatus = !Strings.equals(guiValue, objValue);
			}
		}
		this.changedStatus = changedStatus;
	}

	@Override
	public boolean isViewChanged() {
		return wasShown() && changedStatus;
	}	

	@Override
	public void validateView(final ValidationResults results, final Object model) {
		final String guiValue = getGUIValue();
		if (Strings.isEmpty(guiValue)) {
			results.add(new ErrorMessage("Der Name darf nicht leer sein"), txtName);
		}
	}	

}

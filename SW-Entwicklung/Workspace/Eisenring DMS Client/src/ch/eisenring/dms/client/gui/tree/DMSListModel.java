package ch.eisenring.dms.client.gui.tree;

import javax.swing.ListModel;
import javax.swing.event.ListDataEvent;
import javax.swing.event.ListDataListener;

import ch.eisenring.core.collections.Collection;
import ch.eisenring.core.collections.WeakAtomicArraySet;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.dms.service.api.DMSObject;

public class DMSListModel implements ListModel<DMSObject> {

	private final ArrayList<DMSObject> proxyList = new ArrayList<DMSObject>();
	
	public final Object getLock() {
		return this;
	}

	@Override
	public int getSize() {
		return proxyList.size();
	}
	
	@Override
	public DMSObject getElementAt(final int index) {
		if (index >= 0 && index < proxyList.size())
			return proxyList.get(index);
		return null;
	}

	public void setProxyList(final Collection<DMSObject> collection) {
		proxyList.clear();
		if (collection != null && !collection.isEmpty()) {
			proxyList.addAll(collection);
		}
		final ListDataEvent event = new ListDataEvent(this, ListDataEvent.CONTENTS_CHANGED, 0, Integer.MAX_VALUE);
		fireEvent(event);
	}

	/**
	 * Returns true, if the model contains the given object
	 */
	public boolean contains(final DMSObject proxy) {
		return proxyList.contains(proxy);
	}

	/**
	 * Returns the index of the object or -1 if not found
	 */
	public int indexOf(final DMSObject object) {
		if (object == null)
			return -1;
		return proxyList.indexOf(object);
	}

	// --------------------------------------------------------------
	// ---
	// --- Listener handling
	// ---
	// --------------------------------------------------------------
	/**
	 * Set of all registered listeners
	 */
	private final WeakAtomicArraySet<ListDataListener> listeners = new WeakAtomicArraySet<ListDataListener>();

	@Override
	public void addListDataListener(final ListDataListener listener) {
		listeners.add(listener);
	}
	
	@Override
	public void removeListDataListener(final ListDataListener listener) {
		listeners.remove(listener);
	}

	public void fireEvent(final ListDataEvent event) {
		switch (event.getType()) {
			case ListDataEvent.CONTENTS_CHANGED:
				for (final ListDataListener listener : listeners) {
					listener.contentsChanged(event);
				}
				break;
			case ListDataEvent.INTERVAL_ADDED:
				for (final ListDataListener listener : listeners) {
					listener.intervalAdded(event);
				}
				break;
			case ListDataEvent.INTERVAL_REMOVED:
				for (final ListDataListener listener : listeners) {
					listener.intervalRemoved(event);
				}
				break;
			default:
				throw new IllegalArgumentException("unsupported event type: " + event.getType());
		}
	}

}

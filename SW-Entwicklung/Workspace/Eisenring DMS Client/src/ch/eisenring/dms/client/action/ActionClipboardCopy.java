package ch.eisenring.dms.client.action;

import javax.swing.TransferHandler;

import ch.eisenring.dms.client.gui.tree.ContentView;
import ch.eisenring.dms.service.codetables.DMSObjectType;
import ch.eisenring.dms.shared.model.api.VersionIdentifierList;
import ch.eisenring.gui.resource.images.Images;

public class ActionClipboardCopy extends ActionClipboardBase {

	public ActionClipboardCopy(final ContentView contentView, final TransferHandler transferHandler) {
		super(contentView, transferHandler, "Kopieren", Images.COPY);
	}

	@Override
	protected boolean isEnabledImpl() {
		if (!super.isEnabledImpl())
			return false;
		final VersionIdentifierList list = contentView.getSelectedVersions();
		// we can copy only files
		return list.size() == list.size(DMSObjectType.FILE);
	}

	@Override
	protected void performAction() {
		transferHandler.exportToClipboard(getComponent(), getClipboard(), TransferHandler.COPY);
	}

}

package ch.eisenring.dms.client.network;

import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.codetable.MessageClassCode;
import ch.eisenring.core.datatypes.strings.Msg;
import ch.eisenring.dms.client.DMSClient;
import ch.eisenring.dms.shared.network.PacketCreateProjectSubFoldersReply;
import ch.eisenring.network.PacketSink;
import ch.eisenring.network.packet.AbstractPacket;

final class CreateProjectSubFoldersReplyHandler extends AbstractDMSPacketHandler2 {

	CreateProjectSubFoldersReplyHandler(final DMSClient client) {
		super(client, PacketCreateProjectSubFoldersReply.class);
	}

	@Override
	public void handle(final AbstractPacket packet, final PacketSink sink) {
		handleImpl((PacketCreateProjectSubFoldersReply) packet);
	}

	private void handleImpl(final PacketCreateProjectSubFoldersReply packet) {
		ErrorMessage message;
		if (packet.isValid()) {
			message = new ErrorMessage(MessageClassCode.OK,
					Msg.mk("{} Unterordner erstellt", packet.getChangeSet().size()));
		} else {
			message = packet.getMessage();
		}
		client.message(message);
	}

}

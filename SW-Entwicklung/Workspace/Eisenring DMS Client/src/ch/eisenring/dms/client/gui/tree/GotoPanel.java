package ch.eisenring.dms.client.gui.tree;

import java.awt.GridBagLayout;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.InputEvent;
import java.awt.event.KeyEvent;
import java.util.Collections;

import javax.swing.AbstractAction;
import javax.swing.Action;
import javax.swing.KeyStroke;

import ch.eisenring.app.shared.network.RPCContext;
import ch.eisenring.app.shared.network.RPCHandler;
import ch.eisenring.app.shared.network.RPCHandlerEDT;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.observable.Observable;
import ch.eisenring.core.observable.Observer;
import ch.eisenring.core.observable.ObserverNotificationPolicy;
import ch.eisenring.dms.client.DMSClient;
import ch.eisenring.dms.client.codetables.SortCriterionCode;
import ch.eisenring.dms.client.gui.tree.action.ShowPropertiesAction;
import ch.eisenring.dms.client.resources.Images;
import ch.eisenring.dms.service.api.DMSObject;
import ch.eisenring.dms.shared.network.PacketFindFolderReply;
import ch.eisenring.dms.shared.network.PacketFindFolderRequest;
import ch.eisenring.gui.GUIConstants;
import ch.eisenring.gui.GridBagConstraints;
import ch.eisenring.gui.balloontip.BalloonTipManager;
import ch.eisenring.gui.components.HEAGFloatingButton;
import ch.eisenring.gui.components.HEAGPanel;
import ch.eisenring.gui.components.HEAGTextField;
import ch.eisenring.gui.util.ConversionUtil;
import ch.eisenring.gui.util.GUIUtil;

/**
 * Folder quick search panel 
 */
@SuppressWarnings("serial")
public class GotoPanel extends HEAGPanel {

	private final static String INVALID_SEARCH_PATTERN = "\n\t\n";
	
	protected final ObjectSelectView controlledView;
	protected final HEAGTextField txtGoto = new HEAGTextField(60);
	protected final HEAGFloatingButton btnGoto = new HEAGFloatingButton(Images.GOTO, 16);

	protected String lastSearchPattern = INVALID_SEARCH_PATTERN;
	protected List<Long> resultList;
	protected int resultIndex;
	protected int hiddenCount;

	private final Observer<Object> observer = new Observer<Object>() {
		@Override
		public void observableChanged(final Observable<Object> observeable) {
			clearSearchResult();
		}
	};
	
	private final ActionListener actionListener = new ActionListener() {
		public void actionPerformed(final ActionEvent event) {
			if (isEnabled() && getGUISearchCriterion() != null) {
				performGoto();
			}
		}
	};

	private final ActionListener focusAction = new ActionListener() {
		@Override
		public void actionPerformed(final ActionEvent event) {
			txtGoto.requestFocusInWindow();
		}
	};

	private final Action actionShowProperties = new AbstractAction() {
		@Override
		public void actionPerformed(final ActionEvent event) {
			if (!(controlledView instanceof CompoundContentView))
				return;
			final CompoundContentView ccv = (CompoundContentView) controlledView;
			final TreePanel treePanel = ccv.getTreePanel();
			if (treePanel == null)
				return;
			final Action action = new ShowPropertiesAction(treePanel);
			action.actionPerformed(event);
		}
	};

	public GotoPanel(final ObjectSelectView controlledView) {
		this.controlledView = controlledView;
		btnGoto.addActionListener(actionListener);
		controlledView.getClient().SHOWING_OBJECTSTATUS.addObserver(
				observer, ObserverNotificationPolicy.EDT_ASYNCHRONOUS);
		initComponents();
		initLayout();
		registerKeyboardAction(actionListener, KeyStroke.getKeyStroke(KeyEvent.VK_ENTER, 0), WHEN_ANCESTOR_OF_FOCUSED_COMPONENT);
		registerKeyboardAction(focusAction, KeyStroke.getKeyStroke(KeyEvent.VK_S, InputEvent.CTRL_DOWN_MASK), WHEN_IN_FOCUSED_WINDOW);
		registerKeyboardAction(actionShowProperties, KeyStroke.getKeyStroke(KeyEvent.VK_F2, 0), WHEN_ANCESTOR_OF_FOCUSED_COMPONENT);
	}

	private void initComponents() {
		setLayout(new GridBagLayout());
		GUIUtil.makeSameHeight(txtGoto, btnGoto);
		final String tip =
			  "<html>Geben Sie hier die Bezeichnung eines Ordners ein<br>"
			+ "und drücken Sie Enter (oder klicken auf den Pfeil daneben),<br>"
			+ "um zu einem Ordner zu springen";
		txtGoto.setToolTipText(tip);
		btnGoto.setToolTipText(tip);
		GUIUtil.setMinSizeForText(txtGoto, "000000000000000");
	}
	
	private void initLayout() {
		removeAll();
		add(txtGoto, GridBagConstraints.field(0, 0).insets(0));
		add(btnGoto, GridBagConstraints.fixed(1, 0).insets(0, GUIConstants.HSPACE, 0, 0));
	}
	
	@Override
	public void setEnabled(final boolean enabled) {
		super.setEnabled(enabled);
		btnGoto.setEnabled(enabled);
		txtGoto.setEnabled(enabled);
	}
	
	/**
	 * Gets the current search string from input field
	 */
	public String getGUISearchCriterion() {
		return ConversionUtil.convert(txtGoto, (String) null);
	}

	/**
	 * Triggers Goto Action programmatically
	 */
	public void performGoto(final String text) {
		txtGoto.setText(text);
		performGoto();
	}

	/**
	 * Starts/Continues goto action
	 */
	public void performGoto() {
		if (!isEnabled())
			return;
		BalloonTipManager.clear(txtGoto);
		final String newSearchPattern = Strings.trim(getGUISearchCriterion());
		if (Strings.length(newSearchPattern) < 3) {
			BalloonTipManager.showError(txtGoto, "Der Suchbegriff muss mindestens 3 Zeichen lang sein", BalloonTipManager.TIMEOUT_DEFAULT);
			return;
		}
		if (Strings.equals(lastSearchPattern, newSearchPattern)) {
			// continue search
			if (resultList == null || resultIndex >= resultList.size()) {
				String message = "Keine weiteren Treffer für \"" + lastSearchPattern + "\"";
				if (hiddenCount > 0)
					message += "\n(" + hiddenCount + " Treffer wurden aufgrund der Ansichtsoptionen übersprungen)";
				BalloonTipManager.show(txtGoto, message);
				clearSearchResult();
			} else {
				final Long folderRowId = resultList.get(resultIndex);
				controlledView.setSelected(folderRowId, ContentView.EXPAND | ContentView.SCROLL);
				
				++resultIndex;
			}
		} else {
			// new search
			clearSearchResult();
			lastSearchPattern = newSearchPattern;
			setEnabled(false);
			final DMSClient client = controlledView.getClient();
			final PacketFindFolderRequest request = PacketFindFolderRequest.create(
					client.getLoginname(), lastSearchPattern, client.SHOWING_OBJECTSTATUS.get());
			client.sendPacket(rpcHandler, request);
		}
	}

	protected void clearSearchResult() {
		lastSearchPattern = INVALID_SEARCH_PATTERN;
		resultList = null;
		resultIndex = 0;
	}

	// --------------------------------------------------------------
	// ---
	// --- ReplyAction implementation
	// ---
	// --------------------------------------------------------------
	private final RPCHandler rpcHandler = new RPCHandlerEDT() {
		@Override
		public void replyReceived(final RPCContext rpcContext) {
			final DMSClient client = (DMSClient) rpcContext.getComponent();
			setEnabled(true);
			txtGoto.requestFocusInWindow();
			final PacketFindFolderRequest request = (PacketFindFolderRequest) rpcContext.getRequest();
			final PacketFindFolderReply reply = (PacketFindFolderReply) rpcContext.getReply();
			if (reply.isValid()) {
				hiddenCount = reply.getHiddenCount();
				final List<DMSObject> results = reply.getObjects(client.CACHE_OBJECT.get().BUILDER);
				if (results.isEmpty()) {
					clearSearchResult();
					String message = Strings.concat("Die Suche nach \"", request.getPattern(), "\" lieferte keine Treffer");
					if (hiddenCount > 0) {
						message = Strings.concat(message , "\n(", hiddenCount,
								" mögliche Treffer sind aufgrund der gewählten Ansichtsoptionen verborgen)");
					}
					BalloonTipManager.show(txtGoto, message);
				} else {
					Collections.sort(results, SortCriterionCode.TYPE_AND_NAME_DESC.getComparator());
					// process results 
					resultIndex = 1;
					resultList = new ArrayList<Long>(results.size());
					for (DMSObject object : results) {
						resultList.add(object.getPKValue());
					}
					controlledView.setSelected(resultList.get(0), ContentView.EXPAND | ContentView.SCROLL);
				}
			} else {
				BalloonTipManager.show(txtGoto, reply.getMessage());
			}
		}
		
		@Override
		public void timeoutOccured(final RPCContext rpcContext) {
			setEnabled(true);
			txtGoto.requestFocusInWindow();
			BalloonTipManager.showError(txtGoto, "Die Suche wurde wegen Zeitüberschreitung der Serveranfrage abgebrochen");
		}
	};

}

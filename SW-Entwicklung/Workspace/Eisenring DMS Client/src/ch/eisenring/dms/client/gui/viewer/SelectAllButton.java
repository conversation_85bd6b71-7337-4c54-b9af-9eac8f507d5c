package ch.eisenring.dms.client.gui.viewer;

import java.awt.event.ActionEvent;
import java.awt.event.KeyEvent;

import javax.swing.AbstractAction;
import javax.swing.KeyStroke;

import ch.eisenring.dms.client.resources.Images;

@SuppressWarnings("serial")
public class SelectAllButton extends SelectionButton {

	protected final AbstractAction actionSelectAll = new AbstractAction() {
		@Override
		public void actionPerformed(final ActionEvent event) {
			if (isActionEnabled()) {
				viewerContext.setSelectedAll(true);
			}
		}
	};

	public SelectAllButton(final ViewerContext viewerContext) {
		super(Images.ACTIVE, 24, viewerContext);
		setToolTipText("Alle markieren (A)");
		// bind the keys
        registerKeyboardAction(actionSelectAll,
        		KeyStroke.getKeyStroke(KeyEvent.VK_A, 0),
        		WHEN_IN_FOCUSED_WINDOW);
	}

	@Override
	public void buttonClicked() {
		actionSelectAll.actionPerformed((ActionEvent) null);
	}

}

package ch.eisenring.dms.client.gui.permission;

import java.awt.GridBagLayout;

import javax.swing.JComponent;

import ch.eisenring.core.resource.ImageResource;
import ch.eisenring.dms.client.resources.Images;
import ch.eisenring.dms.service.codetables.DMSPermissionGroupCode;
import ch.eisenring.gui.GridBagConstraints;
import ch.eisenring.gui.components.DecorationHeader;
import ch.eisenring.gui.components.HEAGPanel;
import ch.eisenring.gui.components.HEAGTabbedPane;

@SuppressWarnings("serial")
public class PermissionGroupPanel extends HEAGPanel {

	private final DMSPermissionGroupCode groupCode;
	private final HEAGTabbedPane tabbedPane;

	public PermissionGroupPanel(final DMSPermissionGroupCode groupCode) {
		this.groupCode = groupCode;
		this.tabbedPane = new HEAGTabbedPane();
		initComponents();
		initLayout();
	}

	private void initComponents() {
		setLayout(new GridBagLayout());
	}

	private void initLayout() {
		removeAll();
		final DecorationHeader header = new DecorationHeader(
				DecorationHeader.LABEL, groupCode.getLongText() + " Berechtigungen",
				DecorationHeader.ICON, getIcon(groupCode),
				DecorationHeader.SIZE, DecorationHeader.MEDIUM,
				DecorationHeader.SEPARATOR, Boolean.FALSE);
		add(header, GridBagConstraints.decorator(0, 0));
		add(tabbedPane, GridBagConstraints.area(0, 1));
	}

	public void addTab(final JComponent component, final String label) {
		tabbedPane.add(component, label);
	}

	public static ImageResource getIcon(final DMSPermissionGroupCode code) {
		final ImageResource icon;
		if (DMSPermissionGroupCode.FILE.equals(code)) {
			icon = Images.TYPE_DOCUMENT;
		} else if (DMSPermissionGroupCode.FOLDER.equals(code)) {
			icon = Images.TYPE_FOLDER;
		} else {
			icon = Images.TYPE_UNKNOWN;
		}
		return icon;
	}

}

package ch.eisenring.dms.client.action;

import ch.eisenring.core.collections.List;
import ch.eisenring.dms.client.DMSClient;
import ch.eisenring.dms.client.model.ObjectProxyCache;
import ch.eisenring.dms.service.api.DMSObject;
import ch.eisenring.dms.service.codetables.DMSPermissionCode;

/**
 * Base class for actions on a DMSObject.
 * Provides utility methods to check rights and other stuff.
 */
public abstract class ActionObjectBase extends AbstractDMSAction {

	protected final Long objectRowId;

	protected final ObjectProxyCache objectCache;

	protected ActionObjectBase(final DMSClient client, final Object sourceGUI,
			                   final Long objectRowId, final Object ... properties) {
		super(client, properties);
		setSourceGUI(sourceGUI);
		this.objectRowId = objectRowId;
		this.objectCache = client.CACHE_OBJECT.get();
	}

	public final DMSObject getObject() {
		return objectCache.getProxy(objectRowId);
	}

	public final List<DMSObject> getObjectList() {
		final DMSObject object = getObject();
		return List.asList(object);
	}

	public final Long getObjectRowId() {
		return objectRowId;
	}

	public final boolean isPermitted(final DMSPermissionCode permissionCode) {
		return objectCache.isPermitted(objectRowId, permissionCode);
	}

	@Override
	protected boolean isEnabledImpl() {
		if (!super.isEnabledImpl())
			return false;
		return objectRowId != null;
	}

}

package ch.eisenring.dms.client.gui.aspect;

import java.util.Collections;

import javax.swing.ComboBoxModel;

import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.observable.Observable;
import ch.eisenring.core.observable.Observer;
import ch.eisenring.dms.client.DMSClient;
import ch.eisenring.dms.client.gui.permission.DescriptorWrapper;
import ch.eisenring.dms.shared.model.data.PermissionCache;
import ch.eisenring.dms.shared.model.data.PermissionDescriptor;
import ch.eisenring.gui.ValueEditorBinding;
import ch.eisenring.gui.components.HEAGComboBox;
import ch.eisenring.gui.util.ToStringWrapper;

@SuppressWarnings("serial")
public final class PermissionCombo extends HEAGComboBox<Object> {

	private final DMSClient client;

	private final Observer<Object> observer = new Observer<Object>() {
		@Override
		public void observableChanged(final Observable<Object> observable) {
			populate();
		}
	};

	public PermissionCombo(final DMSClient client, final ValueEditorBinding binding) {
		super(binding);
		this.client = client;
		populate();
		client.PERMISSIONS.addObserver(observer);
	}

	protected void populate() {
		final List<Object> items = new ArrayList<Object>(getAllItems());
		items.add(0, "");
		populate(false, items.toArray());
	}

	protected List<DescriptorWrapper> getAllItems() {
		final PermissionCache cache = client.PERMISSIONS.get();
		final List<PermissionDescriptor> permissions = cache.getAllPermissions(true);
		final List<DescriptorWrapper> result = new ArrayList<DescriptorWrapper>(32);
		for (final PermissionDescriptor descriptor : permissions) {
			final DescriptorWrapper wrapper = new DescriptorWrapper(descriptor);
			result.add(wrapper);
		}
		Collections.sort(result, ToStringWrapper.COMPARATOR_ASC);
		return result;
	}

	@Override
	public Class<?> getValueClass() {
		return Long.class;
	}

	@SuppressWarnings("unchecked")
	@Override
	public Object getValue() {
		final Object selected = getSelectedItem();
		if (selected instanceof DescriptorWrapper) {
			return ((DescriptorWrapper) selected).getRowId();
		}
		return null;
	}

	@Override
	public void setValue(final Object value) {
		Long rowId;
		if (value instanceof Long) {
			rowId = (Long) value;
		} else if (value instanceof PermissionDescriptor) {
			rowId = Long.valueOf(((PermissionDescriptor) value).getRowId());
		} else if (value instanceof DescriptorWrapper) {
			rowId = ((DescriptorWrapper) value).getRowId();
		} else {
			rowId = null;
		}
		int selectedIndex = -1;
		if (rowId != null) {
			final ComboBoxModel<Object> model = getModel();
			if (model != null) {
				for (int i=model.getSize()-1; i>=0; --i) {
					final Object item = model.getElementAt(i);
					if (item instanceof DescriptorWrapper) {
						final DescriptorWrapper wrapper = (DescriptorWrapper) item;
						if (rowId.equals(wrapper.getRowId())) {
							selectedIndex = i;
							break;
						}
					}
				}
			}
		}
		setSelectedIndex(selectedIndex);
	}

}

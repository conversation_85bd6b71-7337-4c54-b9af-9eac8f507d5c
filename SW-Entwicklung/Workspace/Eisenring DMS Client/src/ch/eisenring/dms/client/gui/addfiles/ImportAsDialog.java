package ch.eisenring.dms.client.gui.addfiles;

import java.awt.BorderLayout;
import java.awt.Dimension;
import java.io.File;
import java.io.IOException;

import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.io.binary.BinaryHolder;
import ch.eisenring.core.io.binary.BinaryHolderUtil;
import ch.eisenring.core.tag.TagSet;
import ch.eisenring.core.util.file.FileUtil;
import ch.eisenring.dms.client.DMSClient;
import ch.eisenring.dms.client.gui.AbstractDMSDialog;
import ch.eisenring.dms.client.resources.Images;
import ch.eisenring.dms.service.api.DMSObject;
import ch.eisenring.dms.service.codetables.DMSDocumentType;
import ch.eisenring.gui.model.StoreResults;
import ch.eisenring.gui.window.DialogTags;
import ch.eisenring.gui.window.SACButtonPanel;
import ch.eisenring.gui.window.WindowTags;

@SuppressWarnings("serial")
public class ImportAsDialog extends AbstractDMSDialog {

	public final static int OPTION_AUTO_DELETE = 1;
	public final static int OPTION_MAGIC_FIND = 2;
	
	protected final ImportAsPanel panel;
	protected final SACButtonPanel buttons = new SACButtonPanel();
	protected final File file;
	protected final DocumentCreateHelper createHelper;
	protected final int options;

	public ImportAsDialog(final DMSClient client, final File file, final int options) {
		super(client, new TagSet(
				DialogTags.MODALITY, DialogTags.MODALITY_MODELESS,
				DialogTags.DIALOG_OWNER, null,
				WindowTags.ICON, Images.ADD,
				WindowTags.TITLE, "Neues Dokument hinzufügen",
				WindowTags.MINIMUM_SIZE, new Dimension(450, 600)
		));
		this.createHelper = new DocumentCreateHelper(client, buttons.getBalloonTipComponent(), this) {
			@Override
			protected void requestDone() {
				super.requestDone();
				if (createParams.message.isSuccess()) {
					setVisible(false);
					file.delete();
				}
			}
		};
		this.panel = new ImportAsPanel(client, file);
		this.file = file;
		this.options = options;
		initComponents();
		initLayout();
		pack();
		if ((options & OPTION_MAGIC_FIND) != 0) {
			panel.doMagicFind(file.getName());
		}
	}

	private void initComponents() {
		buttons.configureSaveButton("Hinzufügen", "Speichert das Dokument im ausgewählten Ordner im DMS", Images.ADD);
		buttons.configureCancelButton(SACButtonPanel.DEFAULT, SACButtonPanel.DEFAULT, Images.DELETE);
		buttons.configureApplyButton(null, null);
	}
	
	private void initLayout() {
		getContentPane().removeAll();
		setLayout(new BorderLayout());
		add(panel, BorderLayout.CENTER);
		add(buttons, BorderLayout.SOUTH);
	}

	@Override
	protected void onCancel() {
		super.onCancel();
		if ((options & OPTION_AUTO_DELETE) != 0) {
			file.delete();
		}
	}

	@Override
	public void storeView(final StoreResults results, final Object model) {
		super.storeView(results, model);
		if (!results.isSuccess())
			return;
		ErrorMessage message = ErrorMessage.ERROR;
		BinaryHolder binary = null;
			// create the file
			try {
				binary = BinaryHolderUtil.create(file);
				message = ErrorMessage.OK;
			} catch (final IOException e) {
				message = new ErrorMessage(e);
			}
		if (message.isSuccess()) {
			// create the file
			final DMSDocumentType documentType = panel.cmbDocType.getSelectedCode();
			final String documentName = panel.txtObjectname.getText();
			final String extension = FileUtil.getFileExtension(file.getName());
			final String fullName = FileUtil.addFileExtension(documentName, extension);
			final DMSObject targetFolder = panel.getTargetFolder();
			DocumentCreateArgument createParams = new DocumentCreateArgument(
					targetFolder, fullName, binary, documentType);
			createHelper.doEverything(createParams);
		} else {
			results.add(message, panel);
		}
	}

	@Override
	public void setEnabled(final boolean enabled) {
		super.setEnabled(enabled);
		panel.setEnabled(enabled);
		buttons.setEditable(enabled);
	}

}

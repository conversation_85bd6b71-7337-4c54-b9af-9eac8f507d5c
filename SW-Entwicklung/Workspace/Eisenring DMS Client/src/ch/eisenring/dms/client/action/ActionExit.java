package ch.eisenring.dms.client.action;

import java.awt.event.InputEvent;
import java.awt.event.KeyEvent;

import javax.swing.JOptionPane;
import javax.swing.KeyStroke;

import ch.eisenring.app.client.AppCore;
import ch.eisenring.dms.client.DMSClient;
import ch.eisenring.dms.client.gui.DMSMainWindow;
import ch.eisenring.dms.shared.locking.DMSObjectLockSet;
import ch.eisenring.gui.resource.images.Images;
import ch.eisenring.gui.util.GUIUtil;

public class ActionExit extends AbstractDMSAction {

	public ActionExit(final DMSClient client, final boolean showShortCutKey) {
		super(client, (showShortCutKey ? 
				KeyStroke.getKeyStroke(KeyEvent.VK_Q, InputEvent.CTRL_DOWN_MASK | InputEvent.SHIFT_DOWN_MASK) : null),
				"Beenden", Images.DELETE);
	}
	
	@Override
	protected void performAction() {
		// check if the user has still locks
		final DMSObjectLockSet lockSet = client.OBJECTLOCKSET.get();
		final int count = lockSet.getAll(client.getLoginname()).size();
		final AppCore core = client.getCore();
		final DMSMainWindow window = (DMSMainWindow) core.getRootWindow();
		if (count == 0) {
			core.shutdown();
		} else {
			final String text = "<html>"
					+ "Sie haben noch " + count + " Dokumente zum Bearbeiten gesperrt.<br>"
					+ "<br>"
					+ "Die Dokumente bleiben gesperrt, wenn Sie jetzt beenden.<br>"
					+ "Eventuell noch gemachte Änderungen an den Dokumenten werden<br>"
					+ "jedoch nicht mehr in das DMS übernommen.<br>"
					+ "<br>"
					+ "Wollen Sie das Programm wirklich beenden?";
			final int result = JOptionPane.showConfirmDialog(window,
					text, "Achtung", JOptionPane.YES_OPTION, JOptionPane.WARNING_MESSAGE);
			if (result == JOptionPane.OK_OPTION) {
				core.shutdown();
			} else {
				GUIUtil.invokeLater(new Runnable() {
					@Override
					public void run() {
						client.showMainWindow();
						new ActionDocumentEditList(window).fire();
					}
				});
			}
		}
	}

}

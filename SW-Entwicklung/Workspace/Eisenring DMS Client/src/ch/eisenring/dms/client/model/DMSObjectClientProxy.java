package ch.eisenring.dms.client.model;

import ch.eisenring.dms.client.DMSClient;
import ch.eisenring.dms.service.DMSServiceException;
import ch.eisenring.dms.service.api.DMSObject;
import ch.eisenring.dms.service.api.DMSObjectIdentity;
import ch.eisenring.dms.shared.cache.object.DMSObjectBaseProxy;
import ch.eisenring.dms.shared.cache.object.DMSObjectData;
import ch.eisenring.dms.shared.network.PacketGetObjectReply;
import ch.eisenring.dms.shared.network.PacketGetObjectRequest;

/**
 * Client side proxy for DMSObject.
 */
public final class DMSObjectClientProxy extends DMSObjectBaseProxy {

	/**
	 * Cache that manages this object
	 */
	public final ObjectProxyCache cache;

	/**
	 * Constructor. Only to be called by the cache.
	 * Instances of ObjectProxy must be obtained only
	 * through the caches calls.
	 */
	protected DMSObjectClientProxy(final ObjectProxyCache cache, final long pkValue, final DMSObjectData dataObject) {
		super(pkValue);
		this.cache = cache;
		setDataObject(dataObject);
	}

	/**
	 * Creates a fake node
	 */
	public static DMSObject createFake(final DMSClient client, final String name) {
		final DMSObject fake = new DMSObjectClientProxy(
				client.CACHE_OBJECT.get(), 0L, DMSObjectData.createFakeFolder(name));
		return fake;
	}

	// --------------------------------------------------------------
	// ---
	// --- DMSObjectSpecifier implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public DMSObjectIdentity resolveIdentity() throws DMSServiceException {
		return this;
	}
	
	// --------------------------------------------------------------
	// ---
	// --- Parent/Child management
	// ---
	// --------------------------------------------------------------
	protected DMSObjectData update(final DMSObject object) {
		return super.update(object);
	}

	@Override
	protected void setDataObject(final DMSObjectData dataObject) {
		super.setDataObject(dataObject);
	}

	/**
	 * Gets the data object (with selectable loading behavior)
	 */
	protected DMSObjectData getDataObject(final boolean loadIfNotPresent) {
		while (true) {
			final DMSObjectData result = super.getDataObject(loadIfNotPresent);
			if (result != null || !loadIfNotPresent)
				return result;
			try {
				final PacketGetObjectRequest request = PacketGetObjectRequest.create(getPKValue(), cache.client.getLoginname());
				final PacketGetObjectReply reply = (PacketGetObjectReply) cache.client.sendAndWait(request);
				if (reply.isValid())
					cache.update(reply);
				else
					throw new RuntimeException(reply.getMessage().getText());
			} catch (final Exception e) {
				return null;
			}
		}
	}

}

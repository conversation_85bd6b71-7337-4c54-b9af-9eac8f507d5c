package ch.eisenring.dms.client.gui.addfiles;

import java.awt.Dimension;
import java.awt.GridBagLayout;

import javax.swing.JScrollPane;
import javax.swing.ScrollPaneConstants;

import ch.eisenring.dms.client.DMSClient;
import ch.eisenring.gui.components.HEAGPanel;
import ch.eisenring.gui.components.HEAGTable;
import ch.eisenring.gui.util.LayoutUtil;

@SuppressWarnings("serial")
public class AddFilesTablePanel extends HEAGPanel {

	protected final DMSClient client;
	protected final JScrollPane scrollPane = new JScrollPane();
	protected final AddFilesTableModel tableModel;
	protected final HEAGTable table;

	public AddFilesTablePanel(final AddFilesContext context) {
		this.client = context.client;
		this.tableModel = new AddFilesTableModel(context);
		this.table = new HEAGTable(tableModel) {};
		initComponents();
		initLayout();
	}

	private void initComponents() {
		setMinimumSize(new Dimension(256, 48));
		scrollPane.setVerticalScrollBarPolicy(ScrollPaneConstants.VERTICAL_SCROLLBAR_ALWAYS);
		scrollPane.setHorizontalScrollBarPolicy(ScrollPaneConstants.HORIZONTAL_SCROLLBAR_AS_NEEDED);
		scrollPane.setViewportView(table);
		tableModel.applyLayout(table);
	}
	
	private void initLayout() {
		removeAll();
		setLayout(new GridBagLayout());
		final LayoutUtil l = new LayoutUtil(1);
		add(scrollPane, l.area(10, 10, 1));
	}

	@Override
	public Dimension getPreferredSize() {
		if (isPreferredSizeSet())
			return super.getPreferredSize();
		final int rows = tableModel.getRowCount();
		int height = Math.max((rows * 16) + 32, 128);
		height = Math.min(height, 600);
		return new Dimension(600, height);
	}

}

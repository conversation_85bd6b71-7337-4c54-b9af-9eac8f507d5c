package ch.eisenring.dms.client.gui.recursiveimport;

import java.awt.GridBagLayout;
import java.io.File;
import java.io.IOException;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

import ch.eisenring.app.client.network.RPCAdapter;
import ch.eisenring.app.shared.network.RPCContext;
import ch.eisenring.app.shared.network.RPCHandler;
import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.io.binary.BinaryHolder;
import ch.eisenring.core.io.binary.BinaryHolderUtil;
import ch.eisenring.core.util.OperationResult;
import ch.eisenring.dms.client.DMSClient;
import ch.eisenring.dms.client.gui.dnd.FileListDropListener;
import ch.eisenring.dms.client.resources.Images;
import ch.eisenring.dms.service.api.DMSObject;
import ch.eisenring.dms.shared.codetables.DMSCompressionPolicy;
import ch.eisenring.dms.shared.model.DAOResult;
import ch.eisenring.dms.shared.model.record.DMSObjectModel;
import ch.eisenring.dms.shared.network.AbstractDAOReplyPacket;
import ch.eisenring.dms.shared.network.PacketCreateFileRequest;
import ch.eisenring.dms.shared.network.PacketCreateFolderRequest;
import ch.eisenring.gui.balloontip.BalloonTipManager;
import ch.eisenring.gui.components.DecorationHeader;
import ch.eisenring.gui.components.HEAGIntegerField;
import ch.eisenring.gui.components.HEAGLabel;
import ch.eisenring.gui.components.HEAGPanel;
import ch.eisenring.gui.components.HEAGTextField;
import ch.eisenring.gui.util.GUIUtil;
import ch.eisenring.gui.util.LayoutUtil;
import ch.eisenring.model.TransactionContext;
import ch.eisenring.network.packet.AbstractPacket;

@SuppressWarnings("serial")
public class RecursiveImportPanel extends HEAGPanel {

	protected final DMSClient client;

	protected AtomicBoolean isBusy = new AtomicBoolean(false);
	protected AtomicInteger doneCount = new AtomicInteger(0);
	protected AtomicInteger totalCount = new AtomicInteger(0);
	
	protected final HEAGTextField txtCurrent = new HEAGTextField();
	protected final HEAGIntegerField intDoneCount = new HEAGIntegerField();
	protected final HEAGIntegerField intTotalCount = new HEAGIntegerField();

	protected final List<ImportItem> importList = new ArrayList<ImportItem>();
	protected ImportItem currentItem;
	
	public RecursiveImportPanel(final DMSClient client) {
		this.client = client;
		initComponents();
		initLayout();
		setEditable(false);
	}

	private void initComponents() {
		setLayout(new GridBagLayout());
		GUIUtil.setMinSizeForText(txtCurrent, 50);
	}
	
	private void initLayout() {
		removeAll();
		final DecorationHeader header = new DecorationHeader(
				DecorationHeader.SIZE, DecorationHeader.MEDIUM,
				DecorationHeader.LABEL, "Import...",
				DecorationHeader.ICON, Images.DATABASE);
		final LayoutUtil l = new LayoutUtil(9);
		add(header, l.field());
		
		add(new HEAGLabel("Importiere"), l.label());
		add(txtCurrent, l.field());
	
		add(new HEAGLabel("Element"), l.label());
		add(intDoneCount, l.fixed(1));
		add(new HEAGLabel("von"), l.label());
		add(intTotalCount, l.fixed(1));
		add(new HEAGPanel(), l.field());
	}

	public void onCancel() {
		if (!isBusy.get()) {
			// only close the dialog if not still working
			GUIUtil.hideWindow(this);
		}
	}

	/**
	 * Performs the import
	 */
	public void performImport(final DMSObject target, final File[] files) {
		currentItem = null;
		importList.clear();
		doneCount.set(0);
		isBusy.set(true);
		// build list of import items
		for (final File file : files) {
			if (file.isFile()) {
				final ImportItem item = new ImportItem(file, target);
				importList.add(item);
			} else if (file.isDirectory()) {
				final ImportItem item = new ImportItem(file, target);
				importList.add(item);
				addImportDirectory(item);
			} else {
				isBusy.set(false);
				txtCurrent.setValue(file.getAbsolutePath());
				BalloonTipManager.showError(txtCurrent, "Nicht unterstützter Objekttyp");
				return;
			}
		}
		intTotalCount.setValue(importList.size());
		doNext();
	}

	private void addImportDirectory(final ImportItem folder) {
		final File[] files = folder.getFile().listFiles();
		for (final File file : files) {
			if (!FileListDropListener.canImportFile(file))
				continue;
			if (file.isFile()) {
				final ImportItem item = new ImportItem(file, folder);
				importList.add(item);
			} else if (file.isDirectory()) {
				final ImportItem item = new ImportItem(file, folder);
				importList.add(item);
				addImportDirectory(item);
			}
		}
	}

	private void doNext() {
		final OperationResult<AbstractPacket> result = getNextRequest();
		if (result.isSuccess()) {
			final AbstractPacket request = result.getResult();
			if (request == null) {
				isBusy.set(false);
				onCancel();
			} else {
				client.sendPacket(rpcHandler, request);
			}
		} else {
			isBusy.set(false);
			BalloonTipManager.show(txtCurrent, result.getMessage());
		}
	}

	private final RPCHandler rpcHandler = new RPCAdapter() {
		@Override
		public void replyReceived(final RPCContext rpcContext) {
			final AbstractDAOReplyPacket reply = (AbstractDAOReplyPacket) rpcContext.getReply();
			final DAOResult result = reply.getResult();
			if (reply.isValid()) {
				try {
					final TransactionContext context = result.getContext();
					final List<DMSObjectModel> list = context.getModels(DMSObjectModel.class);
					if (list.size() != 1) {
						isBusy.set(false);
						BalloonTipManager.showError(txtCurrent, "Result count != 1 (" + list.size() + ")");
					} else {
						currentItem.setObject(list.get(0));
						doNext();
					}
				} catch (final IOException e) {
					isBusy.set(false);
					BalloonTipManager.showError(txtCurrent, e.getMessage());
				}
			} else {
				// show error message and abort
				isBusy.set(false);
				BalloonTipManager.show(txtCurrent, result.getMessage());
			}
		}

		@Override
		public void timeoutOccured(final RPCContext rpcContext) {
			super.timeoutOccured(rpcContext);
			isBusy.set(false);
			BalloonTipManager.showError(txtCurrent, "Zeitüberschreitung der Serveranfrage.");
		}
	};

	protected OperationResult<AbstractPacket> getNextRequest() {
		final int i = doneCount.get();
		if (i >= importList.size()) {
			// we are done with all items
			currentItem = null;
			return new OperationResult<AbstractPacket>((AbstractPacket) null);
		}
		intDoneCount.setValue(doneCount.get());
		doneCount.incrementAndGet();
		currentItem = importList.get(i);
		final File file = currentItem.getFile();
		final String name = file.getName();
		txtCurrent.setValue(file.getAbsolutePath());
		if (file.isDirectory()) {
			final PacketCreateFolderRequest request = PacketCreateFolderRequest.create(
					currentItem.getFolder().getPKValue(), client.getLoginname(), name);
			return new OperationResult<AbstractPacket>(request);
		} else if (file.isFile()) {
			try {
				final BinaryHolder binaryHolder = BinaryHolderUtil.create(file);
				final PacketCreateFileRequest request = PacketCreateFileRequest.create(
						currentItem.getFolder().getPKValue(), client.getLoginname(), name, binaryHolder);
				request.setCompressionPolicy(DMSCompressionPolicy.AGGRESIVE);
				return new OperationResult<AbstractPacket>(request);
			} catch (final IOException e) {
				return new OperationResult<AbstractPacket>(new ErrorMessage(e));
			}
		} else {
			return new OperationResult<AbstractPacket>(new ErrorMessage("Unbekannter Objekttyp"));
		}
	}

}

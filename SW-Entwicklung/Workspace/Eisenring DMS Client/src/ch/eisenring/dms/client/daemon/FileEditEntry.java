package ch.eisenring.dms.client.daemon;

import java.io.File;
import java.io.IOException;
import java.io.RandomAccessFile;

import javax.swing.JOptionPane;

import ch.eisenring.app.shared.network.RPCContext;
import ch.eisenring.app.shared.network.RPCHandler;
import ch.eisenring.app.shared.network.RPCHandlerEDT;
import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.codetable.MessageClassCode;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.io.Streams;
import ch.eisenring.core.io.binary.BinaryHolder;
import ch.eisenring.core.io.binary.BinaryHolderUtil;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.core.threading.ThreadCore;
import ch.eisenring.dms.client.DMSClient;
import ch.eisenring.dms.client.gui.util.TempFile;
import ch.eisenring.dms.service.api.DMSObject;
import ch.eisenring.dms.shared.locking.DMSObjectLock;
import ch.eisenring.dms.shared.network.PacketCreateVersionReply;
import ch.eisenring.dms.shared.network.PacketCreateVersionRequest;
import ch.eisenring.dms.shared.network.PacketUnlockRequest;
import ch.eisenring.gui.util.GUIUtil;

/**
 * Represents a file currently being edited by the user.
 */
public final class FileEditEntry {

	private final DMSObject object;
	private final DMSObjectLock lock;
	private final TempFile tempFile;
	private final File file;
	private long lastModified;
	private long length;
	private boolean editingStarted;

	public FileEditEntry(final DMSObject object, final DMSObjectLock lock, final TempFile tempFile) {
		this.object = object;
		this.lock = lock;
		this.tempFile = tempFile;
		this.file = tempFile.getFile();
		this.lastModified = file.lastModified();
		this.length = file.length();
	}

	public final DMSObject getObject() {
		return object;
	}

	public final DMSObjectLock getLock() {
		return lock;
	}

	/**
	 * Starts editing this file
	 */
	public void startEdit() {
		if (!editingStarted) {
			editingStarted = true;
			tempFile.edit();
		}	
	}

	/**
	 * Aborts editing this file
	 */
	public void abortEdit() {
		if (editingStarted) {
			// TODO : terminate editor if possible
		}
		if (!tempFile.delete()) {
			file.setWritable(false);
		}
	}
	
	/**
	 * Called when the edit is completed
	 */
	public void editCompleted(final DMSClient client) {
		// create new version
		final BinaryHolder binary;
		try {
			binary = BinaryHolderUtil.create(file); 
		} catch (final IOException e) {
			String message = "Die bearbeitete Datei \"" + object.getObjectname() + "\" konnte nicht gelesen werden!";
			client.warn(message);
			JOptionPane.showMessageDialog(client.getCore().getRootWindow(), message);
			return;
		}

		final PacketCreateVersionRequest request = PacketCreateVersionRequest.create(
				object.getPKValue(), client.getLoginname(), file.getName(), binary);
		request.setCompressionPolicy(client.COMPRESSION_POLICY.get());
		final RPCHandler replyAction = new RPCHandlerEDT() {
			@Override
			public void requestSent(final RPCContext actionContext) {
			}
			@Override
			public void replyReceived(final RPCContext actionContext) {
				final PacketCreateVersionReply reply = (PacketCreateVersionReply) actionContext.getReply();
				final ErrorMessage message = reply.getMessage();
				if (message.isSuccess()) {
					tempFile.delete();
					GUIUtil.invokeLater(new Runnable() {
						@Override
						public void run() {
							client.message(new ErrorMessage(MessageClassCode.INFO, Strings.concat(
									"Ihre Änderungen am Dokument \"", object.getObjectname(), "\" wurden gespeichert.")));
							final String message = 
									  "<html>Ihre Änderungen am Dokument <b>" + object.getObjectname() + "</b><br>"
									+ "wurden als neue Version gespeichert.<br>"
								    + "<br>"
									+ "Wenn Sie das Dokument noch in der Anwendung geöffnet haben, schliessen Sie<br>"
									+ "es <b>jetzt</b>. Bearbeiten Sie das Dokument nicht weiter, Ihre weiteren<br>"
									+ "Änderungen werden nicht mehr gespeichert.<br>"
									+ "<br>"
									+ "Wenn Sie weitere Änderungen vornehmen wollen,<br>"
									+ "wählen Sie erneut <b>Bearbeiten</b> im Dokument Manager";
							JOptionPane.showMessageDialog(client.getCore().getRootWindow(), message);
						}
					});
				} else {
					client.message(message);
					JOptionPane.showMessageDialog(client.getCore().getRootWindow(), message);
				}
				releaseLock();
			}
			@Override
			public void timeoutOccured(final RPCContext actionContext) {
				String message = "Erstellen der neuen Version für \"" + object.getObjectname() + "\" fehlgeschlagen: Zeitüberschreitung";
				client.error(message);
				JOptionPane.showMessageDialog(client.getCore().getRootWindow(), message);
				releaseLock();
			}
			private void releaseLock() {
				final DMSObjectLock lock = DMSObjectLock.create(object.getPKValue(), client.getLoginname());
				final PacketUnlockRequest request = PacketUnlockRequest.create(lock);
				client.sendPacket(request, false);
			}
		};
		client.sendPacket(replyAction, request);
	}
	
	
	public FileEditState getEditState() {
		if (!file.exists())
			return FileEditState.DELETED;

		boolean changed = file.lastModified() != lastModified || file.length() != length;
		Logger.log(Logger.LogLevel.INFO, "File is changed: " + changed + "modified: " + file.lastModified() + " vs " + lastModified + " length: " +  file.length() + " vs " + length);
		if (!changed)
			return FileEditState.UNCHANGED;
		
		// check if the file is accessible again
		if (!file.canRead()) {
			//Logger.log(Logger.LogLevel.INFO, "Cannot read --> EDITING");
			return FileEditState.EDITING;
		}
		if (!file.canWrite()) {
			//Logger.log(Logger.LogLevel.INFO, "Cannot write --> EDITING");
			return FileEditState.EDITING;
		}

		RandomAccessFile rnd = null;
		try {
			rnd = new RandomAccessFile(file, "rws");
			rnd.close();
			// delay a bit to avoid disturbing the editing application
			// to possibly do a finishing touch.
			ThreadCore.sleep(1000);
			rnd = new RandomAccessFile(file, "rws");
			rnd.close();
			return FileEditState.EDITED;
		} catch (final IOException e) {
			Logger.debug(e);
			return FileEditState.EDITING;
		} finally {
			Streams.closeSilent(rnd);
		}
	}

	public boolean isForKey(final long objectRowId) {
		return object.getPKValue() == objectRowId;
	}

	public String getTempFilePath() {
		return tempFile.getPath();
	}

}

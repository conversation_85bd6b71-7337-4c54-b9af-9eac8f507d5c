package ch.eisenring.dms.client.network;

import ch.eisenring.dms.client.DMSClient;
import ch.eisenring.dms.shared.network.PacketAlterPropertiesReply;
import ch.eisenring.network.PacketSink;
import ch.eisenring.network.implementation.PacketDispatchMode;

public final class AlterPropertiesReplyHandler extends AbstractDM<PERSON>acketHandler<PacketAlterPropertiesReply> {

	private AlterPropertiesReplyHandler(final DMSClient client) {
		super(client, PacketAlterPropertiesReply.class, PacketDispatchMode.SYNCHRONOUS);
	}

	@Override
	public void handle(final PacketAlterPropertiesReply packet, final PacketSink sink) {
		// ignore this packet
	}

}

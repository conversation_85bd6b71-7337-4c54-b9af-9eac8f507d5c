package ch.eisenring.dms.client.gui.employee;

import ch.eisenring.core.tag.Tag;
import ch.eisenring.dms.service.codetables.DMSPropertyCode;

interface MoveFolderTags {

	/**
	 * Tooltip text to display for the "Ok" button
	 */
	public Tag<String> OK_BUTTON_TOOLTIP = new Tag<>("OkButtonTooltip", String.class, "In den ausgewählten Ordner verschieben");

	/**
	 * Label to display on the "Ok" button (defaults to "Ok")
	 */
	public Tag<String> OK_BUTTON_LABEL = new Tag<>("OkButtonLabel", String.class, "Ok");

	/**
	 * Message if no target folder was selected
	 */
	public Tag<String> NO_TARGET_MESSAGE = new Tag<>("NoFolderSelected", String.class, "Kein Zielordner ausgewählt");
	
	/**
	 * Message when target folder does not have suitable properties
	 */
	public Tag<String> INVALID_TARGET_MESSAGE = new Tag<>("InvalidTargetMessage", String.class, "Zielordner nicht geeignet");

	/**
	 * Property type used to determine if target folder is suitable
	 */
	public Tag<DMSPropertyCode> TARGET_FOLDER_PROPERTYTYPE = new Tag<>("TargetFolderPropertyType", DMSPropertyCode.class, DMSPropertyCode.FOLDERTYPE);
	
	/**
	 * Value of property that is detected as suitable
	 */
	public Tag<Object> TARGET_FOLDER_PROPERTYVALUE = new Tag<>("TargetFolderPropertyValue", Object.class);

}

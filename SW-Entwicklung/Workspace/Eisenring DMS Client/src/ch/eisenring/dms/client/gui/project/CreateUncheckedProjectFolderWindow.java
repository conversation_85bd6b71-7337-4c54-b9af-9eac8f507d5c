package ch.eisenring.dms.client.gui.project;

import java.awt.Component;
import java.awt.Container;
import java.awt.GridBagLayout;
import java.awt.event.FocusEvent;
import java.awt.event.FocusListener;
import java.awt.event.ItemEvent;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Objects;
import javax.swing.JPanel;
import javax.swing.JTextField;
import javax.swing.text.JTextComponent;
import ch.eisenring.app.shared.network.RPCContext;
import ch.eisenring.app.shared.network.RPCHandler;
import ch.eisenring.core.tag.TagSet;
import ch.eisenring.dms.client.DMSClient;
import ch.eisenring.dms.client.async.RPCHandlerAdapter;
import ch.eisenring.dms.client.gui.AbstractDMSFrame;
import ch.eisenring.dms.client.gui.DMSMainWindow;
import ch.eisenring.dms.client.model.ObjectProxyCache;
import ch.eisenring.dms.client.resources.Images;
import ch.eisenring.dms.service.codetables.DMSFolderStructureType;
import ch.eisenring.dms.shared.logiware.DMSLogiwareAuftrag;
import ch.eisenring.dms.shared.network.PacketCheckProjectNumberReply;
import ch.eisenring.dms.shared.network.PacketCheckProjectNumberRequest;
import ch.eisenring.dms.shared.network.PacketProjectStructureCreateReply;
import ch.eisenring.dms.shared.network.PacketProjectStructureCreateRequest;
import ch.eisenring.dms.shared.network.cmd.ObjectMessageConstants;
import ch.eisenring.dms.shared.network.cmd.ObjectMessageSet;
import ch.eisenring.gui.BorderLayout;
import ch.eisenring.gui.GridBagConstraints;
import ch.eisenring.gui.balloontip.BalloonTipManager;
import ch.eisenring.gui.components.HEAGLabel;
import ch.eisenring.gui.components.HEAGTextField;
import ch.eisenring.gui.util.GUIUtil;
import ch.eisenring.gui.window.SACButtonPanel;
import ch.eisenring.gui.window.WindowTags;
import ch.eisenring.network.packet.AbstractPacket;

public class CreateUncheckedProjectFolderWindow extends AbstractDMSFrame {

	// --------------------------------------------------------------
	// --- Constructors and initial Members
	// --------------------------------------------------------------

	private final ProjectStructureContext structureContext;

	/**
	 * Constructor
	 * @param client
	 * @param window
	 */
	public CreateUncheckedProjectFolderWindow(DMSClient client, Component window) {
		super(client, new TagSet(WindowTags.LAYOUTMANAGER, new BorderLayout() //
			, WindowTags.TITLE, "Ordnerstruktur anlegen" //
			, WindowTags.ICON, Images.QUESTION //
			, WindowTags.RESIZEABLE, WindowTags.RESIZEABLE_YES //
			, WindowTags.POSITION_SETTINGS_ID, CreateUncheckedProjectFolderWindow.class.getSimpleName() //
		));
		this.structureContext = new ProjectStructureContext(client, window);
		setContentPane(createContentPane());
		setAlwaysOnTop(true);
		pack();
	}

	private ProjectStructureContext getStructureContext() {
		return structureContext;
	}

	private Component getParentWindow() {
		return getStructureContext().getWindow();
	}

	@Override
	protected void onOk() {
		createFolders();
		dispose();
	}

	@Override
	protected void onApply() {
		createFolders();
	}

	@Override
	protected void onCancel() {
		dispose();
	}

	// --------------------------------------------------------------
	// --- Panels
	// --------------------------------------------------------------

	private Container createContentPane() {
		JPanel p = new JPanel(new BorderLayout());
		p.add(createProjectNumberPanel(), BorderLayout.NORTH);
		p.add(createButtonPanel(), BorderLayout.SOUTH);
		return p;
	}

	private JPanel createProjectNumberPanel() {
		JPanel p = new JPanel(new GridBagLayout());
		final int columnIndexLabel = 0;
		final int columnIndexTextField = 1;
		int y = 0;
		p.add(getFolderStructureTypeLabel(), GridBagConstraints.label(columnIndexLabel, y));
		p.add(getFolderStructureTypeComboBox(), GridBagConstraints.field(columnIndexTextField, y));
		y++;
		p.add(getBasisProjektnummerLabel(), GridBagConstraints.label(columnIndexLabel, y));
		p.add(getBasisProjektnummerTextField(), GridBagConstraints.field(columnIndexTextField, y));
		y++;
		p.add(getKommProjektnummerLabel(), GridBagConstraints.label(columnIndexLabel, y));
		p.add(getKommProjektnummerTextField(), GridBagConstraints.field(columnIndexTextField, y));
		return p;
	}

	private Component createButtonPanel() {
		SACButtonPanel buttonPanel = new SACButtonPanel();
		buttonPanel.configureSaveButton(null, null);
		buttonPanel.configureApplyButton("Ordner erstellen", "Erstellt Basis-Ordner mit gegebenenfalls einer Kommission");
		buttonPanel.configureCancelButton("Beenden", "Schliesst Eingabe ohne Aktion");
		return buttonPanel;
	}

	// --------------------------------------------------------------
	// --- Input Components
	// --------------------------------------------------------------

	private HEAGLabel lblFolderStructureType;

	public HEAGLabel getFolderStructureTypeLabel() {
		if (lblFolderStructureType == null) {
			lblFolderStructureType = new HEAGLabel("Ordner-Struktur-Typ");
		}
		return lblFolderStructureType;
	}

	private CodeComboBox<DMSFolderStructureType> cboFolderStructureType;

	private CodeComboBox<DMSFolderStructureType> getFolderStructureTypeComboBox() {
		if (cboFolderStructureType == null) {
			cboFolderStructureType = new CodeComboBox<>(DMSFolderStructureType.ABACUSOBJEKT, DMSFolderStructureType.MERXPROJEKTE);
		}
		return cboFolderStructureType;
	}

	private DMSFolderStructureType getFolderStructureType() {
		return getFolderStructureTypeComboBox().getSelectedItem();
	}

	private HEAGLabel lblBasisProjektnummer;

	public HEAGLabel getBasisProjektnummerLabel() {
		if (lblBasisProjektnummer == null) {
			lblBasisProjektnummer = new HEAGLabel("Basisprojektnummer");
			getFolderStructureTypeComboBox().addItemListener(e -> {
				if (e.getStateChange() == ItemEvent.SELECTED) {
					if (getFolderStructureType().hasBasisStructure()) {
						lblBasisProjektnummer.setText("Basisprojektnummer");
					} else {
						lblBasisProjektnummer.setText("Projektnummer");
					}
				}
			});
		}
		return lblBasisProjektnummer;
	}

	private HEAGLabel lblKommProjektnummer;

	public HEAGLabel getKommProjektnummerLabel() {
		if (lblKommProjektnummer == null) {
			lblKommProjektnummer = new HEAGLabel("Kommissionsnummer");
			getFolderStructureTypeComboBox().addItemListener(e -> {
				if (e.getStateChange() == ItemEvent.SELECTED) {
					lblKommProjektnummer.setEnabled(getFolderStructureType().hasBasisStructure());
				}
			});
		}
		return lblKommProjektnummer;
	}

	private JTextField txtBasisProjektnummer;

	private JTextField getBasisProjektnummerTextField() {
		if (txtBasisProjektnummer == null) {
			txtBasisProjektnummer = new HEAGTextField(16);
			GUIUtil.setSizeForText(txtBasisProjektnummer, 20);
			txtBasisProjektnummer.setToolTipText("Projekt-Nummer (Basis) für " + getFolderStructureType().getLongText());
			txtBasisProjektnummer.addActionListener(e -> createFolders());
			txtBasisProjektnummer.addFocusListener(new FocusListener() {
				@Override
				public void focusGained(FocusEvent e) {
					txtBasisProjektnummer.selectAll();
				}

				@Override
				public void focusLost(FocusEvent e) {
				}
			});
			getFolderStructureTypeComboBox().addItemListener(e -> {
				if (e.getStateChange() == ItemEvent.SELECTED) {

				}
			});
		}
		return txtBasisProjektnummer;
	}

	private JTextField txtKommProjektnummer;

	private JTextField getKommProjektnummerTextField() {
		if (txtKommProjektnummer == null) {
			txtKommProjektnummer = new HEAGTextField(16);
			GUIUtil.setSizeForText(txtKommProjektnummer, 20);
			txtKommProjektnummer.setToolTipText("Projekt-Nummer (Kommission) für " + getFolderStructureType().getLongText());
			txtKommProjektnummer.addActionListener(e -> createFolders());
			txtKommProjektnummer.addFocusListener(new FocusListener() {
				@Override
				public void focusGained(FocusEvent e) {
					txtKommProjektnummer.selectAll();
				}

				@Override
				public void focusLost(FocusEvent e) {
				}
			});
			getFolderStructureTypeComboBox().addItemListener(e -> {
				if (e.getStateChange() == ItemEvent.SELECTED) {
					txtKommProjektnummer.setEnabled(getFolderStructureType().hasBasisStructure());
					if (!txtKommProjektnummer.isEnabled())
						txtKommProjektnummer.setText(null);
				}
			});
		}
		return txtKommProjektnummer;
	}

	private String trim(JTextComponent component) {
		return component.getText() != null ? component.getText().trim() : "";
	}

	private String getBasisProjectNumber() {
		return trim(getBasisProjektnummerTextField());
	}

	private String getProjectNumber() {
		return trim(getKommProjektnummerTextField());
	}

	private boolean validateInputFields() {
		DMSFolderStructureType type = getFolderStructureType();
		String basisProjectNumber = getBasisProjectNumber();
		String projectNumber = getProjectNumber();
		if (type == null) {
			BalloonTipManager.show(getFolderStructureTypeComboBox(), "Die Ordner Struktur muss bekannt sein");
			return false;
		} else if (basisProjectNumber.isEmpty()) {
			BalloonTipManager.show(getBasisProjektnummerTextField(), "Die Projektnummer darf nicht leer sein");
			return false;
		} else if (Objects.equals(basisProjectNumber, projectNumber)) {
			BalloonTipManager.show(getBasisProjektnummerTextField(), "Basis-Projektnummer und Kommissions-Projektnummer dürfen nicht gleich sein");
			return false;
		} else {
			return true;
		}
	}

	// --------------------------------------------------------------
	// --- Execute Request
	// --------------------------------------------------------------

	private Collection<DMSLogiwareAuftrag> createProjectNumberList() {
		Collection<DMSLogiwareAuftrag> list = new ArrayList<>();
		if (getFolderStructureType().hasBasisStructure()) {
			String projectNumber = getProjectNumber();
			if (!projectNumber.isEmpty()) {
				DMSLogiwareAuftrag auftrag = new DMSLogiwareAuftrag();
				auftrag.setProjektnummer(projectNumber);
				list.add(auftrag);
			}
		} else {
			DMSLogiwareAuftrag auftrag = new DMSLogiwareAuftrag();
			auftrag.setProjektnummer(getBasisProjectNumber());
			list.add(auftrag);
		}
		return list;
	}

	private void navigateToResultFolder(final Long rowId) {
		final Component window = GUIUtil.getWindowAncestor(getParentWindow());
		if (window instanceof DMSMainWindow) {
			final ObjectProxyCache cache = getClient().CACHE_OBJECT.get();
			cache.update(new ObjectMessageSet(rowId, ObjectMessageConstants.CHILDREN));
			((DMSMainWindow) window).navigateToObject(rowId);
		}
	}

	private final RPCHandler rpcCreateFolderStrukturHandler = new RPCHandlerAdapter() {
		@Override
		public void timeoutOccured(final RPCContext actionContext) {
			super.timeoutOccured(actionContext);
			BalloonTipManager.showError(getBasisProjektnummerTextField(), "Zeitüberschreitung der Serveranfrage");
		}

		@Override
		public void replyReceived(final RPCContext actionContext) {
			super.replyReceived(actionContext);
			final PacketProjectStructureCreateReply reply = (PacketProjectStructureCreateReply) actionContext.getReply();
			if (reply.isValid()) {
				GUIUtil.invokeLater(() -> navigateToResultFolder(reply.getBaseFolderRowId()));
			} else {
				BalloonTipManager.show(getBasisProjektnummerTextField(), reply.getResult().getMessage());
			}
		}
	};

	private final RPCHandler rpcCheckProjectNumberHandler = new RPCHandlerAdapter() {
		@Override
		public void timeoutOccured(final RPCContext actionContext) {
			super.timeoutOccured(actionContext);
			BalloonTipManager.showError(getBasisProjektnummerTextField(), "Zeitüberschreitung der Serveranfrage");
		}

		@Override
		public void replyReceived(RPCContext rpcContext) {
			super.replyReceived(rpcContext);
			AbstractPacket packet = rpcContext.getReply();
			if (packet instanceof PacketCheckProjectNumberReply) {
				PacketCheckProjectNumberReply reply = (PacketCheckProjectNumberReply) packet;
				if (!reply.getMessage().isSuccess()) {
					BalloonTipManager.show(getBasisProjektnummerTextField(), "Verbindungs-Fehler");
				} else if (reply.existProjectNumber()) {
					BalloonTipManager.show(getKommProjektnummerTextField(), "Projektnummer " + getProjectNumber() + " bereits vorhanden");
				} else {
					PacketProjectStructureCreateRequest request = PacketProjectStructureCreateRequest.create(
						getBasisProjectNumber(), getClient().getLoginname(), getFolderStructureType(), createProjectNumberList());
					getClient().sendPacket(rpcCreateFolderStrukturHandler, request);
				}
			}
		}
	};

	private void createFolders() {
		BalloonTipManager.clearAll(getWindow());
		if (validateInputFields()) {
			getClient().sendPacket(rpcCheckProjectNumberHandler, PacketCheckProjectNumberRequest.create(getFolderStructureType().getActiveFolder(), getBasisProjectNumber(), getProjectNumber()));
			getKommProjektnummerTextField().requestFocus();
			getKommProjektnummerTextField().selectAll();
		}
	}

}

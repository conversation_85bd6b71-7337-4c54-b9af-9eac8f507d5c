package ch.eisenring.dms.client.gui.aspect;

import java.awt.Component;
import java.awt.GridBagLayout;
import java.io.IOException;

import javax.swing.JScrollPane;
import javax.swing.ScrollPaneConstants;

import ch.eisenring.app.shared.network.RPCContext;
import ch.eisenring.app.shared.network.RPCHandler;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.util.OperationResult;
import ch.eisenring.dms.client.DMSClient;
import ch.eisenring.dms.client.async.RPCHandlerAdapter;
import ch.eisenring.dms.client.resources.Images;
import ch.eisenring.dms.service.api.DMSObject;
import ch.eisenring.dms.shared.model.DAOResult;
import ch.eisenring.dms.shared.model.record.DMSAuditTrailModel;
import ch.eisenring.dms.shared.network.AbstractDAORequestPacket;
import ch.eisenring.dms.shared.network.PacketGetAuditTrailReply;
import ch.eisenring.dms.shared.network.PacketGetAuditTrailRequest;
import ch.eisenring.gui.GridBagConstraints;
import ch.eisenring.gui.components.DecorationHeader;
import ch.eisenring.gui.components.HEAGTable;
import ch.eisenring.gui.model.ValidationResults;
import ch.eisenring.model.TransactionContext;
import ch.eisenring.network.packet.AbstractPacket;

@SuppressWarnings("serial")
public final class AuditTrailPanel extends AspectPanel {

	protected final DecorationHeader header = new DecorationHeader(
			DecorationHeader.SIZE, DecorationHeader.MEDIUM,
			DecorationHeader.LABEL, "Mutations-Protokoll",
			DecorationHeader.SEPARATOR, Boolean.TRUE,
			DecorationHeader.ICON, Images.HISTORY);
	protected final JScrollPane scrollPane = new JScrollPane();
	protected final AuditTrailTableModel tableModel = new AuditTrailTableModel();
	protected final HEAGTable table = new HEAGTable(tableModel);

	public AuditTrailPanel(final DMSClient client,
						   final DMSObject object) {
		super(client, object);
		initComponents();
		initLayout();
	}

	private void initComponents() {
		setLayout(new GridBagLayout());
		scrollPane.setHorizontalScrollBarPolicy(ScrollPaneConstants.HORIZONTAL_SCROLLBAR_AS_NEEDED);
		scrollPane.setVerticalScrollBarPolicy(ScrollPaneConstants.VERTICAL_SCROLLBAR_ALWAYS);
		scrollPane.setViewportView(table);
		tableModel.applyLayout(table);
	}

	private void initLayout() {
		removeAll();
		add(header, GridBagConstraints.decorator(0, 0));
		add(scrollPane, GridBagConstraints.area(0, 1));
	}

	// --------------------------------------------------------------
	// ---
	// --- Loading of audit trail
	// ---
	// --------------------------------------------------------------
	protected void updateTable() {
		final PacketGetAuditTrailRequest request = PacketGetAuditTrailRequest.create(
				object.getPKValue(), client.getLoginname());
		sendAndNotify(request, tableUpdateListener);
	}

	final RPCHandler tableUpdateListener = new RPCHandlerAdapter() {
		@Override
		protected Component getGUI() {
			return AuditTrailPanel.this;
		}
	
		@SuppressWarnings("unchecked")
		@Override
		public void replyReceived(final RPCContext actionContext) {
			try {
				final PacketGetAuditTrailReply reply = (PacketGetAuditTrailReply) actionContext.getReply();
				final DAOResult result = reply.getResult();
				if (!result.isValid()) {
					client.message(result.getMessage());
					return;
				}
				try {
					final TransactionContext context = result.getContext();
					final List<DMSAuditTrailModel> list = (List) context.getModels(DMSAuditTrailModel.class);
					tableModel.setContent(list);
					tableModel.applyLayout(table);
				} catch (final IOException e) {
					client.error(e.getMessage());
				}
			} finally {
				super.replyReceived(actionContext);
			}
		}
		
		@Override
		public void timeoutOccured(final RPCContext actionContext) {
			super.timeoutOccured(actionContext);
			client.error("Die Audit-Information für Objekt Id " + object.getPKValue() +  " konnten nicht geladen werden: Zeitüberschreitung der Server-Anfrage");
		}
	};
	
	// --------------------------------------------------------------
	// ---
	// --- AspectPanel implementation
	// ---
	// --------------------------------------------------------------
	@Override
	protected void showingFirstTimeImpl() {
		// populate table
		updateTable();
	}

	@Override
	protected void showingAgainImpl() {
		super.showingAgainImpl();
		// populate table again
		updateTable();
	}

	@Override
	protected void updateEditableImpl() {
		// nothing here (always read-only)
	}

	@Override
	protected OperationResult<Component> validateContents() {
		// always valid
		return new OperationResult<Component>(this);
	}

	@Override
	protected AbstractDAORequestPacket createRequestImpl() {
		// can't edit
		return null;
	}

	@Override
	protected OperationResult<Component> processReply(final AbstractPacket packet) {
		// never gets a reply
		return new OperationResult<Component>(this);
	}

	// --------------------------------------------------------------
	// ---
	// --- ModelViewContainer overrides
	// ---
	// --------------------------------------------------------------
	@Override
	public void evaluateChanged(final Object model) {
		// this panel is read-only, never evaluate changed
	}

	@Override
	public final boolean isViewChanged() {
		// this panel is read-only (never changed)
		return false;
	}

	@Override
	public void validateView(final ValidationResults results, final Object model) {
		// nothing (this panel is always valid)
	}

}

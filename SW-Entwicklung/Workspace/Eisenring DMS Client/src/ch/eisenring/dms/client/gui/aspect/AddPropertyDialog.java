package ch.eisenring.dms.client.gui.aspect;

import java.awt.BorderLayout;

import ch.eisenring.core.collections.Collection;
import ch.eisenring.core.tag.TagSet;
import ch.eisenring.dms.client.DMSClient;
import ch.eisenring.dms.client.gui.AbstractDMSDialog;
import ch.eisenring.dms.client.gui.aspect.AddPropertyPanel.AddPropertyValues;
import ch.eisenring.dms.client.resources.Images;
import ch.eisenring.dms.service.api.DMSObject;
import ch.eisenring.dms.service.codetables.DMSPropertyCode;
import ch.eisenring.gui.model.StoreResults;
import ch.eisenring.gui.window.SACButtonPanel;
import ch.eisenring.gui.window.WindowTags;

@SuppressWarnings("serial")
public class AddPropertyDialog extends AbstractDMSDialog {

	protected final SACButtonPanel buttonPanel = new SACButtonPanel();
	protected final AddPropertyPanel propertyPanel;
	protected AddPropertyValues dialogResult;
	
	public AddPropertyDialog(final DMSClient client,
							 final DMSObject object,
							 final Collection<DMSPropertyCode> exclusionList) {
		super(client, new TagSet(
				WindowTags.TITLE, "Eigenschaft hinzufügen",
				WindowTags.POSITION_SETTINGS_ID, "AddPropertyWindow",
				WindowTags.ICON, Images.PROPERTIES));
		propertyPanel = new AddPropertyPanel(client, object, exclusionList);
		initComponents();
		initLayout();
		pack();
		setResizable(false);
		setModalityType(ModalityType.APPLICATION_MODAL);
	}
	
	private void initComponents() {
		buttonPanel.configureSaveButton("Hinzufügen", null);
		buttonPanel.configureApplyButton(null, null);
		buttonPanel.configureCancelButton("Abbrechen", null);
	}

	private void initLayout() {
		getContentPane().removeAll();
		setLayout(new BorderLayout());
		add(propertyPanel, BorderLayout.CENTER);
		add(buttonPanel, BorderLayout.SOUTH);
	}
	
	public AddPropertyValues doDialog() {
		dialogResult = null;
		setVisible(true);
		if (dialogResult == null)
			return new AddPropertyValues(null, null);
		return dialogResult;
	}

	@Override
	public void storeView(final StoreResults results, final Object model) {
		super.storeView(results, model);
		dialogResult = propertyPanel.getValues();
	}

}

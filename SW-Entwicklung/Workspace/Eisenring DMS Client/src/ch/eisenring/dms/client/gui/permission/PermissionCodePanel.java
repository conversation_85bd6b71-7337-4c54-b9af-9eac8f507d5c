package ch.eisenring.dms.client.gui.permission;

import java.awt.Dimension;
import java.awt.GridBagLayout;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.awt.event.MouseListener;
import java.util.Comparator;

import javax.swing.JButton;
import javax.swing.JList;
import javax.swing.JScrollPane;
import javax.swing.ListSelectionModel;
import javax.swing.ScrollPaneConstants;
import javax.swing.event.ListSelectionEvent;
import javax.swing.event.ListSelectionListener;

import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.dms.service.codetables.DMSPermissionCode;
import ch.eisenring.gui.GridBagConstraints;
import ch.eisenring.gui.components.GenericListModel;
import ch.eisenring.gui.components.HEAGLabel;
import ch.eisenring.gui.components.HEAGPanel;

@SuppressWarnings("serial")
public class PermissionCodePanel extends HEAGPanel {

	protected final PermissionEditContext editContext;
	protected final DMSPermissionCode code;
	private final JScrollPane scpPermitted;
	private final JScrollPane scpRoles;
	private final JScrollPane scpUsers;
	private final JList<PermissionData> permList;
	private final JList<PermissionBase> roleList;
	private final JList<PermissionBase> userList;
	private final GenericListModel<PermissionBase> roleListModel;
	private final GenericListModel<PermissionBase> userListModel;
	private final GenericListModel<PermissionData> permListModel;
	private final JButton btnAddRoles = new JButton(ch.eisenring.gui.resource.images.Images.ARROW_WEST.getIcon());
	private final JButton btnRemRoles = new JButton(ch.eisenring.gui.resource.images.Images.ARROW_EAST.getIcon());
	private final JButton btnAddUsers = new JButton(ch.eisenring.gui.resource.images.Images.ARROW_WEST.getIcon());
	private final JButton btnRemUsers = new JButton(ch.eisenring.gui.resource.images.Images.ARROW_EAST.getIcon());

	private final ActionListener addRolesListener = new ActionListener() {
		@Override
		public void actionPerformed(final ActionEvent event) {
			performAdd(roleList.getSelectedValuesList(), roleList);
		}
	};

	private final ActionListener addUsersListener = new ActionListener() {
		@Override
		public void actionPerformed(final ActionEvent event) {
			performAdd(userList.getSelectedValuesList(), userList);
		}
	};

	private final ActionListener remPermsListener = new ActionListener() {
		@Override
		public void actionPerformed(final ActionEvent event) {
			performRem(permList.getSelectedValuesList());
		}
	};


	private final MouseListener addRoleListener = new MouseAdapter() {
		@Override
		public void mouseClicked(final MouseEvent event) {
			performAdd(event, roleList);
		}
	};

	private final MouseListener addUserListener = new MouseAdapter() {
		@Override
		public void mouseClicked(final MouseEvent event) {
			performAdd(event, userList);
		}
	};

	private final MouseListener removeListener = new MouseAdapter() {
		@Override
		public void mouseClicked(final MouseEvent event) {
			if (!event.isConsumed()
					&& event.getClickCount() == 2 
					&& event.getButton() == MouseEvent.BUTTON1
					&& isEditable()) {
				final int index = permList.locationToIndex(event.getPoint());
			    final PermissionData data = permListModel.getItem(index);
			    if (data != null) {
					event.consume();
					final List<PermissionData> selection = new ArrayList<PermissionData>(1);
					selection.add(data);
			    	performRem(selection);
			    }
			}
		}
	};

	private final ListSelectionListener selectionListener = new ListSelectionListener() {
		@Override
		public void valueChanged(final ListSelectionEvent event) {
			updateButtons();
		}
	};

	public PermissionCodePanel(final PermissionEditContext editContext, final DMSPermissionCode code) {
		this.editContext = editContext;
		this.code = code;
		this.permListModel = new GenericListModel<PermissionData>();
		this.roleListModel = new GenericListModel<PermissionBase>();
		this.userListModel = new GenericListModel<PermissionBase>();
		this.permList = new JList<PermissionData>(permListModel);
		this.roleList = new JList<PermissionBase>(roleListModel);
		this.userList = new JList<PermissionBase>(userListModel);
		this.scpPermitted = new JScrollPane(permList);
		this.scpRoles = new JScrollPane(roleList);
		this.scpUsers = new JScrollPane(userList);
		roleListModel.addItems(editContext.roles);
		userListModel.addItems(editContext.users);
		updatePermList();
		initComponents();
		initLayout();
	}

	private void initComponents() {
		setLayout(new GridBagLayout());
		btnAddRoles.setFocusable(false);
		btnAddUsers.setFocusable(false);
		btnRemRoles.setFocusable(false);
		btnRemUsers.setFocusable(false);
		Dimension d = btnAddRoles.getPreferredSize();
		d.width = d.height;
		btnAddRoles.setPreferredSize(d);
		btnAddUsers.setPreferredSize(d);
		btnRemRoles.setPreferredSize(d);
		btnRemUsers.setPreferredSize(d);
		btnAddRoles.setMinimumSize(d);
		btnAddUsers.setMinimumSize(d);
		btnRemRoles.setMinimumSize(d);
		btnRemUsers.setMinimumSize(d);
		permList.setSelectionMode(ListSelectionModel.MULTIPLE_INTERVAL_SELECTION);
		roleList.setSelectionMode(ListSelectionModel.MULTIPLE_INTERVAL_SELECTION);
		userList.setSelectionMode(ListSelectionModel.MULTIPLE_INTERVAL_SELECTION);
		scpPermitted.setVerticalScrollBarPolicy(ScrollPaneConstants.VERTICAL_SCROLLBAR_ALWAYS);
		scpRoles.setVerticalScrollBarPolicy(ScrollPaneConstants.VERTICAL_SCROLLBAR_ALWAYS);
		scpUsers.setVerticalScrollBarPolicy(ScrollPaneConstants.VERTICAL_SCROLLBAR_ALWAYS);
		scpPermitted.setHorizontalScrollBarPolicy(ScrollPaneConstants.HORIZONTAL_SCROLLBAR_NEVER);
		scpRoles.setHorizontalScrollBarPolicy(ScrollPaneConstants.HORIZONTAL_SCROLLBAR_NEVER);
		scpUsers.setHorizontalScrollBarPolicy(ScrollPaneConstants.HORIZONTAL_SCROLLBAR_NEVER);
		permList.addMouseListener(removeListener);
		roleList.addMouseListener(addRoleListener);
		userList.addMouseListener(addUserListener);
		permList.addListSelectionListener(selectionListener);
		roleList.addListSelectionListener(selectionListener);
		userList.addListSelectionListener(selectionListener);
		btnAddRoles.addActionListener(addRolesListener);
		btnAddUsers.addActionListener(addUsersListener);
		btnRemRoles.addActionListener(remPermsListener);
		btnRemUsers.addActionListener(remPermsListener);
		btnAddRoles.setToolTipText("Ausgewählte Gruppen hinzufügen");
		btnAddUsers.setToolTipText("Ausgewählte Benutzer hinzufügen");
		btnRemRoles.setToolTipText("Ausgewählte Berechtigte entfernen");
		btnRemUsers.setToolTipText("Ausgewählte Berechtigte entfernen");
	}

	private void initLayout() {
		removeAll();
		add(new HEAGLabel("Berechtigte"), GridBagConstraints.label(0, 0, GridBagConstraints.WEST));
		add(new HEAGLabel("Gruppen"), GridBagConstraints.label(2, 0, GridBagConstraints.WEST));
		add(new HEAGLabel("Benutzer"), GridBagConstraints.label(2, 4, GridBagConstraints.WEST));
		add(btnAddRoles, GridBagConstraints.button(1, 1));
		add(btnRemRoles, GridBagConstraints.button(1, 2));
		add(btnAddUsers, GridBagConstraints.button(1, 5));
		add(btnRemUsers, GridBagConstraints.button(1, 6));
		add(scpPermitted, GridBagConstraints.area(0, 1).gridHeight(7));
		add(scpRoles, GridBagConstraints.area(2, 1).gridHeight(3));
		add(scpUsers, GridBagConstraints.area(2, 5).gridHeight(3));
	}

	@Override
	public void setEditable(final boolean editable) {
		super.setEditable(editable);
		permList.setEnabled(editable);
		roleList.setEnabled(editable);
		userList.setEnabled(editable);
	}

	@SuppressWarnings("unchecked")
	protected void performAdd(final MouseEvent event, final JList listComponent) {
		if (!event.isConsumed()
				&& event.getClickCount() == 2 
				&& event.getButton() == MouseEvent.BUTTON1
				&& isEditable()) {
			final int index = listComponent.locationToIndex(event.getPoint());
		    final PermissionBase base = (PermissionBase) listComponent.getModel().getElementAt(index);
		    if (base != null) {
				event.consume();
				final List<PermissionBase> selection = new ArrayList<PermissionBase>(1);
				selection.add(base);
				performAdd(selection, listComponent);
		    }
		}
	}

	@SuppressWarnings("unchecked")
	protected void performAdd(final java.util.Collection<PermissionBase> selection, final JList<PermissionBase> listComponent) {
		if (selection == null || selection.isEmpty())
			return;
		final GenericListModel<PermissionBase> listModel =
			(GenericListModel<PermissionBase>) listComponent.getModel();
		for (final PermissionBase base : selection) {
			final PermissionData data = new PermissionData(base);
			editContext.getEditingDescriptor().addPermission(code, base.getRowId());
			permListModel.addItem(data);
		}
		listModel.removeItems(selection);
		listComponent.clearSelection();
		permListModel.sortItems((Comparator) PermissionData.COMPARATOR_ASC);
		permList.clearSelection();
	}

	@SuppressWarnings("unchecked")
	protected void performRem(final java.util.Collection<PermissionData> selection) {
		if (selection == null || selection.isEmpty())
			return;
		boolean sortRoles = false;
		boolean sortUsers = false;
		for (final PermissionData data : selection) {
			editContext.getEditingDescriptor().remPermission(code, data.getRowId());
			permListModel.removeItem(data);
			permList.clearSelection();
			if (data.object instanceof PermissionRole) {
				roleListModel.addItem(data.object);
				sortRoles = true;
			} else if (data.object instanceof PermissionUser) {
				userListModel.addItem(data.object);
				sortUsers = true;
			}
		}
		if (sortRoles) {
			roleList.clearSelection();
			roleListModel.sortItems((Comparator) PermissionBase.COMPARATOR_ASC);
		}
		if (sortUsers) {
			userList.clearSelection();
			userListModel.sortItems((Comparator) PermissionBase.COMPARATOR_ASC);
		}
	}

	@SuppressWarnings("unchecked")
	protected void updatePermList() {
		permListModel.clear();
		updatePermList(editContext.roles, roleListModel);
		updatePermList(editContext.users, userListModel);
		permListModel.sortItems((Comparator) PermissionData.COMPARATOR_ASC);
		updateButtons();
	}

	protected void updatePermList(final List<PermissionBase> baseList,
			                      final GenericListModel<PermissionBase> listModel) {
		for (int i=baseList.size()-1; i>=0; --i) {
			final PermissionBase object = baseList.get(i);
			if (editContext.getEditingDescriptor().isPermitted(code, object.getRowId())) {
				permListModel.addItem(new PermissionData(object));
				listModel.removeItem(object);
			}
		}
	}

	protected void updateButtons() {
		final boolean editable = isEditable();
		final boolean bPerms = editable && permList.getSelectedIndices().length > 0;
		final boolean bRoles = editable && roleList.getSelectedIndices().length > 0;
		final boolean bUsers = editable && userList.getSelectedIndices().length > 0;
		btnAddRoles.setEnabled(bRoles);
		btnAddUsers.setEnabled(bUsers);
		btnRemRoles.setEnabled(bPerms);
		btnRemUsers.setEnabled(bPerms);
	}

}

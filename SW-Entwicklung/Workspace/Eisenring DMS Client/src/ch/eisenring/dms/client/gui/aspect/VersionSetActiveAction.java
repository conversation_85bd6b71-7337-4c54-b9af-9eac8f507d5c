package ch.eisenring.dms.client.gui.aspect;

import ch.eisenring.dms.client.DMSClient;
import ch.eisenring.dms.client.action.ActionObjectBase;
import ch.eisenring.dms.service.api.DMSObject;
import ch.eisenring.dms.service.codetables.DMSBinaryStatus;
import ch.eisenring.dms.service.codetables.DMSObjectType;
import ch.eisenring.dms.shared.codetables.DMSObjectTypeProperties;
import ch.eisenring.dms.shared.model.api.DMSVersion;

class VersionSetActiveAction extends ActionObjectBase {

	final DMSVersion version;
	final VersionInfoPanel versionPanel;

	public VersionSetActiveAction(final DMSClient client,
			                      final Long objectRowId,
								  final DMSVersion version,
            					  final VersionInfoPanel versionPanel) {
		super(client, null, objectRowId,
				"Aktive Version");
		this.version = version;
		this.versionPanel = versionPanel;
	}

	@Override
	protected boolean isCheckedImpl() {
		return DMSBinaryStatus.ACTIVE.equals(version.getStatusCode());
	}

	@Override
	protected boolean isEnabledImpl() {
		if (!super.isEnabledImpl())
			return false;
		if (DMSBinaryStatus.ACTIVE.equals(version.getStatusCode()))
			return false;
		final DMSObject object = getObject();
		final DMSObjectType type = object.getType();
		final DMSObjectTypeProperties properties = DMSObjectTypeProperties.get(type);
		return properties != null && isPermitted(properties.getAlterPermissionCode());
	}

	@Override
	protected void performAction() {
		versionPanel.tableModel.setActiveVersion(version);
	}

}

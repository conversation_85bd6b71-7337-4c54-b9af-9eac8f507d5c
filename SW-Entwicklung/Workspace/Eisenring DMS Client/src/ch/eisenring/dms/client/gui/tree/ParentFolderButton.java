package ch.eisenring.dms.client.gui.tree;

import ch.eisenring.dms.client.resources.Images;
import ch.eisenring.dms.service.api.DMSObject;

@SuppressWarnings("serial")
public final class ParentFolderButton extends ToolbarButton {

	public ParentFolderButton(final CompoundContentView contentView) {
		super(contentView, Images.PARENT);
		setToolTipText("<html>Aufwärts<br>(Zum übergeordneter Ordner)");
	}

	@Override
	public void buttonClicked() {
		boolean collapse = true;
		do {
			final DMSObject folder = contentView.folderPanel.getShowingFolder();
			if (folder == null)
				break;
			final Long parent = folder.getParentRowId();
			if (parent == null)
				break;
			contentView.doubleClicked(parent);
			collapse = false;
		} while (false);
		if (collapse) {
			contentView.applyDefaultView();
		}
	}

}

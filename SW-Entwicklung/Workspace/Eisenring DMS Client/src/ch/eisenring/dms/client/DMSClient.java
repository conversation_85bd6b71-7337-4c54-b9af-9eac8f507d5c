package ch.eisenring.dms.client;

import java.io.File;
import java.net.SocketAddress;

import javax.swing.Icon;
import javax.swing.JOptionPane;
import javax.swing.UIManager;

import ch.eisenring.app.client.AbstractAppComponent;
import ch.eisenring.app.client.AppCore;
import ch.eisenring.app.client.action.ShowVersionHistoryAction;
import ch.eisenring.app.client.fileassoc.ProtocolURLHandler;
import ch.eisenring.app.client.gui.CursorTickListener;
import ch.eisenring.app.client.util.ClientConnectedCondition;
import ch.eisenring.app.client.util.DoLater;
import ch.eisenring.app.shared.Configurable;
import ch.eisenring.app.shared.CoreTickListener;
import ch.eisenring.app.shared.CoreTickListenerAdapter;
import ch.eisenring.app.shared.ServiceNotFoundException;
import ch.eisenring.core.application.Configuration;
import ch.eisenring.core.application.ConfigurationException;
import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.collections.Collection;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.feature.Feature;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.core.logging.Logger.LogLevel;
import ch.eisenring.core.observable.Observable;
import ch.eisenring.core.observable.Observer;
import ch.eisenring.core.platform.Platform;
import ch.eisenring.core.platform.PlatformPath;
import ch.eisenring.core.resource.image.ImageScaleRule;
import ch.eisenring.core.util.singleinstance.SingleInstanceListener;
import ch.eisenring.dms.client.cache.DMSBinaryCacheClient;
import ch.eisenring.dms.client.cache.DMSBinaryLoader;
import ch.eisenring.dms.client.codetables.DMSObjectLabelFormat;
import ch.eisenring.dms.client.codetables.LinkOpenBehaviorCode;
import ch.eisenring.dms.client.daemon.FileEditSupervisor;
import ch.eisenring.dms.client.desktop.DesktopIntegration;
import ch.eisenring.dms.client.gui.DMSMainWindow;
import ch.eisenring.dms.client.gui.MainWindowManager;
import ch.eisenring.dms.client.gui.permission.ClientPermissionCache;
import ch.eisenring.dms.client.gui.zipbuilder.ZipEntryList;
import ch.eisenring.dms.client.model.ObjectProxyCache;
import ch.eisenring.dms.client.network.DMSPacketDispatcher;
import ch.eisenring.dms.client.remotecontrol.DMSProtocolURLHandler;
import ch.eisenring.dms.client.resources.CodeIconInitializer;
import ch.eisenring.dms.service.api.DMSObject;
import ch.eisenring.dms.service.codetables.DMSObjectStatus;
import ch.eisenring.dms.service.codetables.DMSObjectType;
import ch.eisenring.dms.shared.DMSConstants;
import ch.eisenring.dms.shared.codetables.DMSCompressionPolicy;
import ch.eisenring.dms.shared.codetables.SafetyQuestionCode;
import ch.eisenring.dms.shared.locking.DMSObjectLockSet;
import ch.eisenring.dms.shared.model.api.VersionIdentifierList;
import ch.eisenring.dms.shared.model.data.PermissionCache;
import ch.eisenring.gui.util.GUIUtil;
import ch.eisenring.user.client.service.UserContextAvailableCondition;
import ch.eisenring.user.shared.service.USRService;
import ch.eisenring.user.shared.service.UserFacade;

public class DMSClient extends AbstractAppComponent implements Configurable {

	// --------------------------------------------------------------
	// ---
	// --- Client private states
	// ---
	// --------------------------------------------------------------
	final DesktopIntegration desktopIntegration = new DesktopIntegration(this);
	String loginName = Strings.toUpper(Platform.getPlatform().getLoginName());
	Long loginUserRowId = null;
	UserFacade loginUser = null;
	
	// --------------------------------------------------------------
	// ---
	// --- Client public / observable states
	// ---
	// --------------------------------------------------------------
	public final Observable<ProtocolURLHandler> URL_HANDLER =
		Observable.create(true, "URLHandler", new DMSProtocolURLHandler(this));
	public final Observable<DMSMainWindow> MAINWINDOW =
		Observable.create(true, "MainWindow", null);
	public final Observable<MainWindowManager> WINDOW_MANAGER =
		Observable.create(true, "MainWindowManager", new MainWindowManager(this));
	public final Observable<SocketAddress> SERVER_SOCKET_ADDRESS =
		Observable.create(true, "ServerSocketAddress", null);
	public final Observable<ObjectProxyCache> CACHE_OBJECT =
		Observable.create(true, "ObjectProxyCache", new ObjectProxyCache(this));
	public final Observable<File> LAST_OPEN_PATH =
		Observable.create(true, "LastOpenPath", null);
	public final Observable<DMSObjectLockSet> OBJECTLOCKSET =
		Observable.create(true, "ObjectLockSet", new DMSObjectLockSet());
	public final Observable<FileEditSupervisor> EDITING_FILES =
		Observable.create(true, "EditingFiles", new FileEditSupervisor(this));
	public final Observable<Integer> ICON_SIZE =
		Observable.create(true, "IconSize", Integer.valueOf(32));
	public final Observable<DMSObjectLabelFormat> LABEL_FORMAT =
		Observable.create(true, "LabelFormat", DMSObjectLabelFormat.NAMEONLY);
	public final Observable<SafetyQuestionCode> SAFETY_QUESTION_MODE =
		Observable.create(true, "SafetyQuestionMode", SafetyQuestionCode.ALWAYS);
	public final Observable<Boolean> TERMINATE_ON_CLOSE =
		Observable.create(true, "TerminateOnClose", Boolean.FALSE);
	public final Observable<DMSObjectStatus> SHOWING_OBJECTSTATUS =
		Observable.create(true, "ShowingObjectStatus", DMSObjectStatus.ACTIVE);
	public final Observable<LinkOpenBehaviorCode> LINK_OPEN_CODE =
		Observable.create(true, "LinkOpenCode", LinkOpenBehaviorCode.SINGLE_WINDOW);
	public final Observable<DMSCompressionPolicy> COMPRESSION_POLICY =
		Observable.create(true, "DataCompressionPolicy", DMSCompressionPolicy.DEFAULT);
	public final Observable<String> PARTICIPANT_FILTER_DEFAULT =
		Observable.create(true, "ParticipantFilterDefault", null);
	public final Observable<Boolean> SHOW_OBJECTSTATUS_IN_LIST =
		Observable.create(true, "ShowObjectstatusInList", Boolean.TRUE);
	public final Observable<Boolean> SHOW_OBJECTSTATUS_IN_TREE =
		Observable.create(true, "ShowObjectstatusInTree", Boolean.FALSE);
	public final Observable<Boolean> REDUCE_LARGE_IMAGES =
		Observable.create(true, "ReduceLargeImages", Boolean.TRUE);
	public final Observable<DMSBinaryLoader> BINARY_LOADER =
		Observable.create(true, "BinaryLoader", new DMSBinaryLoader(this));
	public final Observable<DMSBinaryCacheClient> BINARY_CACHE =
		Observable.create(true, "BinaryCache", new DMSBinaryCacheClient(this));
	public final Observable<Integer> IMAGE_MAX_BYTES =
		Observable.create(true, "TargetByteSize", null);
	public final Observable<Integer> IMAGE_MAX_WIDTH =
		Observable.create(true, "ImageMaxWidth", null);
	public final Observable<Integer> IMAGE_MAX_HEIGHT =
		Observable.create(true, "ImageMaxHeight", null);

	// --- ZIP Builder states ---------------------------------------
	public final Observable<String> ZIP_FILENAME =
		Observable.create(true, "ZipFileName", "Dokumente");
	public final Observable<String> ZIP_OPENPATH =
		Observable.create(true, "ZipOpenPath", null);
	public final Observable<ZipEntryList> ZIP_ENTRYLIST =
		Observable.create(true, "ZipEntryList", new ZipEntryList());
	public final Observable<String> ZIP_SAVEPATH =
		Observable.create(true, "ZipSavePath", null);
	public final Observable<Boolean> ZIP_CLEARONCLOSE =
		Observable.create(true, "ZipClearOnClose", Boolean.FALSE);

	/**
	 * Contains the permission cache of the client.
	 * Also, this observable fires a value change,
	 * whenever either permissions OR user information
	 * has changed.
	 */
	public final Observable<PermissionCache> PERMISSIONS =
		new ClientPermissionCache(this).getObservable();
	
	// --------------------------------------------------------------
	// ---
	// --- Private states
	// ---
	// --------------------------------------------------------------
	private final Observer<Object> userServiceObserver = new Observer<Object>() {
		@Override
		public void observableChanged(final Observable<Object> observable) {
			try {
				final USRService userService = locateService(USRService.class);
				final UserFacade user = userService.getLoggedInUser();
				if (user != null && !user.isLoginAllowed()) {
					Logger.error("Benutzer ist gesperrt");
					getCore().shutdown(true);
				}
				loginUser = user;
			} catch (final ServiceNotFoundException e) {
				loginUser = null;
			}
			loginUserRowId = loginUser == null ? null : loginUser.getRowId();
			final PermissionCache cache = PERMISSIONS.get();
			if (cache != null) {
				cache.flush();
				PERMISSIONS.fireValueChange();
			}
		}
	};

	private final CoreTickListener tickListener = new CoreTickListenerAdapter(this, "DMSClient") {
		@Override
		protected void tickImpl(final long now) {
			final DMSBinaryCacheClient cache = BINARY_CACHE.get();
			if (cache != null)
				cache.tick();
		}
	};
	
	// --------------------------------------------------------------
	// ---
	// --- Feature management
	// ---
	// --------------------------------------------------------------
	private final Feature[] features = {
			new DMSPacketDispatcher(this),
			new CursorTickListener(this),
			tickListener
	};

	@Override
	public Feature[] getFeatures() {
		return features;
	}

	// --------------------------------------------------------------
	// ---
	// --- Launch / Shutdown
	// ---
	// --------------------------------------------------------------
	@Override
	public void launch() throws Exception {
		super.launch();
		CodeIconInitializer.initCodeIcons();

		final AppCore core = getCore();

		// bind user settings
		TERMINATE_ON_CLOSE.bind(this);
		ICON_SIZE.bind(this);
		LABEL_FORMAT.bind(this);
		ZIP_FILENAME.bind(this);
		ZIP_OPENPATH.bind(this);
		ZIP_SAVEPATH.bind(this);
		ZIP_CLEARONCLOSE.bind(this);
		LINK_OPEN_CODE.bind(this);
		SHOWING_OBJECTSTATUS.bind(this);
		PARTICIPANT_FILTER_DEFAULT.bind(this);
		SHOW_OBJECTSTATUS_IN_LIST.bind(this);
		SHOW_OBJECTSTATUS_IN_TREE.bind(this);

		desktopIntegration.attachTrayIcon();
	
		// connect the user system with the DMS permission system
		final USRService userService = locateService(USRService.class);
		userService.getPermissionObservable().addObserver(userServiceObserver);

		core.getSingleInstance().addListener(singleInstanceListener);
		askShowVersionHistory();
	}
	
	@Override
	public void shutdown() throws Exception {
		desktopIntegration.removeTrayIcon();

		// detach from user service
		try {
			final USRService userService = locateService(USRService.class);
			userService.getPermissionObservable().removeObserver(userServiceObserver);
		} catch (final ServiceNotFoundException e) {
			Logger.warn(e.getMessage());
		}

		super.shutdown();
	}

	// --------------------------------------------------------------
	// ---
	// --- Base class overrides
	// ---
	// --------------------------------------------------------------
	private final SingleInstanceListener singleInstanceListener = new SingleInstanceListener() {
		@Override
		public void otherInstanceStarted(final String[] argv, final File currentDir) {
			new DoLater(new Runnable() {
				@Override
				public void run() {
					final LinkOpenBehaviorCode behavior = LINK_OPEN_CODE.get();
					final MainWindowManager windowManager = WINDOW_MANAGER.get();
					if (argv == null || argv.length <= 0) {
						if (LinkOpenBehaviorCode.MULTIPLE_WINDOW.equals(behavior)) {
							final Runnable newWindowRunnable = new Runnable() {
								@Override
								public void run() {
									final DMSMainWindow newWindow = windowManager.newWindow(null);
									newWindow.setVisible(true);
								}
							};
							GUIUtil.invokeLater(newWindowRunnable);
						} else {
							windowManager.restoreAllWindows();
						}
					} else {
						final ProtocolURLHandler urlHandler = URL_HANDLER.get();
						urlHandler.processCmdLineArgs(argv);
					}
				}
			}, true, 
			new ClientConnectedCondition(DMSClient.this),
			new UserContextAvailableCondition(DMSClient.this)).start();
		}
	};

	// --------------------------------------------------------------
	// ---
	// --- Configuration
	// ---
	// --------------------------------------------------------------
	@Override
	public void getConfigurationFileNames(final Collection<String> fileNames) {
		final Platform platform = Platform.getPlatform();
		fileNames.add("DMS.cfg");
		fileNames.add("DMSUser.cfg");
		fileNames.add(new File(platform.getPath(PlatformPath.SETTINGS), "DMSUser.cfg").getAbsolutePath());
	}

	@Override
	public void configure(final Configuration configuration) throws ConfigurationException {
		// server address & port
		SERVER_SOCKET_ADDRESS.set(configuration.getSocketAddress("DMSClient:Server", "DMSClient:Port"));

		{
			int maxBytes = configuration.getInteger("DMSClient:ImageMaxBytes", 32768, 10000000, DMSConstants.DEFAULT_IMAGE_MAX_BYTES);
			int maxWidth = configuration.getInteger("DMSClient:ImageMaxWidth", 640, 4096, DMSConstants.DEFAULT_IMAGE_MAX_WIDTH);
			int maxHeight = configuration.getInteger("DMSClient:ImageMaxHeight", 480, 4096, DMSConstants.DEFAULT_IMAGE_MAX_HEIGHT);
			final ImageScaleRule rule = new ImageScaleRule(maxWidth, maxHeight, maxBytes);
			DMSConstants.IMAGE_SCALE_RULE.set(rule);
		}
		IMAGE_MAX_BYTES.set(configuration.getInteger("DMSClient:ImageMaxBytes", 32768, 10000000, DMSConstants.DEFAULT_IMAGE_MAX_BYTES));
		IMAGE_MAX_WIDTH.set(configuration.getInteger("DMSClient:ImageMaxWidth", 640, 4096, DMSConstants.DEFAULT_IMAGE_MAX_WIDTH));
		IMAGE_MAX_HEIGHT.set(configuration.getInteger("DMSClient:ImageMaxHeight", 480, 4096, DMSConstants.DEFAULT_IMAGE_MAX_HEIGHT));
		BINARY_CACHE.get().configure(configuration);
	}

	// --------------------------------------------------------------
	// ---
	// --- Local API
	// ---
	// --------------------------------------------------------------
	/**
	 * Gets the name of the logged in user
	 */
	public String getLoginname() {
		final UserFacade loginUser = getLoginUser();
		final String result = loginUser == null ? loginName : loginUser.getLoginname();
		return Strings.toUpper(result);
	}

	/**
	 * Gets the logged in user, NULL if no user logged in
	 */
	public UserFacade getLoginUser() {
		return loginUser;
	}
	/**
	 * Gets the logged in user's row id, NULL if no user logged in
	 */
	public Long getLoginUserRowId() {
		return loginUserRowId;
	}

	/**
	 * Generate user level error from exception
	 */
	public void showError(final Throwable t) {
		Logger.log(LogLevel.ERROR, t);
		error("Fehler: " + t.getMessage());
	}

	public void showError(final String errorMessage) {
		Logger.log(LogLevel.ERROR, errorMessage);
		error(errorMessage);
	}

	/**
	 * Show main window if it is currently hidden.
	 * If the window is already showing, it will pop to front.
	 */
	public void showMainWindow() {
		getCore().showMainWindow();
	}

	/**
	 * Checks if a safety question needs to be asked, and asks if necessary.
	 */
	public boolean askSafetyQuestion(final VersionIdentifierList selection, final String action) {
		final List<DMSObject> list = CACHE_OBJECT.get().getProxies(selection);
		return askSafetyQuestion(list, action);
	}

	public boolean askSafetyQuestion(final DMSObject object, final String action) {
		if (object == null)
			return false;
		final List<DMSObject> list = List.asList(object);
		return askSafetyQuestion(list, action);
	}

	/**
	 * Checks if a safety question needs to be asked, and asks if necessary.
	 */
	public boolean askSafetyQuestion(final Collection<DMSObject> selection, final String action) {
		final SafetyQuestionCode setting = getUserSettings().restore(SafetyQuestionCode.SETTINGS_SAFETYQUESTIONMODE, SafetyQuestionCode.class);
		return askSafetyQuestion(selection, action, setting);
	}

	/**
	 * Checks if a safety question needs to be asked, and asks if necessary.
	 */
	public boolean askSafetyQuestion(final java.util.Collection<DMSObject> selection,
			final String action, final SafetyQuestionCode setting) {
		if (selection == null || selection.isEmpty()) {
			// empty selection = deny
			return false;
		}
		final SafetyQuestionCode mode;
		if (AbstractCode.isNull(setting)) {
			mode = SafetyQuestionCode.ALWAYS;
		} else {
			mode = setting;
		}
		if (!mode.requiresQuestion(selection)) {
			// skip question and assume the user confirmed the action
			return true;
		}
		final DMSObject[] array = Collection.toArray(selection, DMSObject.class);
		String question;
		if (array.length == 1) {
			final DMSObjectType type = array[0].getType(); 
			if (DMSObjectType.FOLDER.equals(type)) {
				question = "Sind Sie sicher das Sie den Ordner \"${Objectname}\" ${Action} wollen?"; 
			} else if (DMSObjectType.FILE.equals(type)) {
				question = "Sind Sie sicher das Sie das Dokument \"${Objectname}\" ${Action} wollen?"; 
			} else {
				// invalid / not supported type
				return false;
			}
		} else {
			question = "Sind Sie sicher das Sie diese ${Count} Objekte ${Action} wollen?";
		}
		final StringMaker b = StringMaker.obtain();
		b.append(question);
		b.replace("${Objectname}", array[0].getObjectname());
		b.replace("${Count}", Strings.toString(array.length));
		b.replace("${Action}", action);
		question = b.release();
		question = Strings.concat("<html>", Strings.toHTMLEntities(question));
		final String okText = UIManager.getString("OptionPane.okButtonText");
		final String cancelText = UIManager.getString("OptionPane.cancelButtonText");
		final int result = JOptionPane.showOptionDialog(
				getCore().getRootWindow(), question, "Sicherheitsabfrage",
				JOptionPane.OK_CANCEL_OPTION, JOptionPane.QUESTION_MESSAGE,
				(Icon) null, new Object[] { okText, cancelText },
				cancelText);
		return result == JOptionPane.OK_OPTION;
	}

	/**
	 * Navigates the main view to the given object
	 */
	public void navigateToObject(final Long rowId) {
		if (rowId == null)
			return;
		final DMSMainWindow mainWindow = MAINWINDOW.get();
		if (mainWindow == null)
			return;
		mainWindow.navigateToObject(rowId);
	}

	private void askShowVersionHistory() {
		askShowVersionHistory(this, new ShowVersionHistoryAction(this, "../help", "dms-versionhistory.html"));
	}
	
}

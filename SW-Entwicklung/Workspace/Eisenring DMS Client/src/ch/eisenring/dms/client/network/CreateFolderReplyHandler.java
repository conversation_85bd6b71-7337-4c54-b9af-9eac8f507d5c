package ch.eisenring.dms.client.network;

import ch.eisenring.dms.client.DMSClient;
import ch.eisenring.dms.shared.network.PacketCreateFolderReply;
import ch.eisenring.network.PacketSink;
import ch.eisenring.network.implementation.PacketDispatchMode;

public final class CreateFolderReplyHandler extends AbstractDMSPacketHandler<PacketCreateFolderReply> {

	private CreateFolderReplyHandler(final DMSClient client) {
		super(client, PacketCreateFolderReply.class, PacketDispatchMode.SYNCHRONOUS);
	}

	@Override
	public void handle(final PacketCreateFolderReply packet, final PacketSink sink) {
		// ignore this packet
	}

}

package ch.eisenring.dms.client.network;

import ch.eisenring.dms.client.DMSClient;
import ch.eisenring.dms.client.model.ObjectProxyCache;
import ch.eisenring.dms.shared.network.PacketProjectStructureCreateReply;
import ch.eisenring.dms.shared.network.cmd.ObjectMessageConstants;
import ch.eisenring.dms.shared.network.cmd.ObjectMessageSet;
import ch.eisenring.network.PacketSink;
import ch.eisenring.network.packet.AbstractPacket;

public class ProjectStructureCreateReplyHandler extends AbstractDAOReplyHandler {

	ProjectStructureCreateReplyHandler(final DMSClient client) {
		super(client, PacketProjectStructureCreateReply.class);
	}

	@Override
	public void handle(final AbstractPacket abstractPacket, final PacketSink sink) {
		super.handle(abstractPacket, sink);
	}

	public static void handle(final DMSClient client,
			                  final PacketProjectStructureCreateReply reply) {
		if (reply.isValid()) {
			final long baseFolderRowId = reply.getBaseFolderRowId();
			final ObjectMessageSet messages = new ObjectMessageSet(baseFolderRowId, ObjectMessageConstants.CHILDREN);
			final ObjectProxyCache cache = client.CACHE_OBJECT.get();
			cache.update(messages);
		}
	}

}

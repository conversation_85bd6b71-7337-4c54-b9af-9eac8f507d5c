package ch.eisenring.dms.client.gui.viewer.print;

import java.awt.Image;

import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.Map;
import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.datatypes.strings.format.impl.ByteSizeDescription;
import ch.eisenring.core.util.OperationResult;
import ch.eisenring.dms.client.DMSClient;
import ch.eisenring.dms.client.gui.viewer.ViewerContext;
import ch.eisenring.dms.service.api.DMSObject;
import ch.eisenring.print.shared.model.AbstractReportSource;

public class ViewerReportSource extends AbstractReportSource {

	private final ViewerContext context;
	
	public ViewerReportSource(final ViewerContext context) {
		this.context = context;
	}

	public DMSClient getClient() {
		return context.getClient();
	}

	@Override
	protected void populateModelsImpl() {
		final List<DMSObject> selected = context.getSelected();
		setModelList(selected);
	}

	@Override
	public void populateParameters(final Map<String, Object> map) {
		super.populateParameters(map);
		map.put("TITLE", "Bilder");
		map.put("USER", getClient().getLoginname());
	}

	protected DMSObject getDocument() {
		return (DMSObject) getCurrent();
	}

	// --------------------------------------------------------------
	// ---
	// --- Report Field getters
	// ---
	// --------------------------------------------------------------
	public Image getDocumentImage() {
		final DMSObject document = getDocument();
		final OperationResult<Image> result = context.getImage(document);
		return result.isSuccess() ? result.getResult() : null;
	}

	public String getDocumentName() {
		final DMSObject document = getDocument();
		if (document == null)
			return "<Fehler>";
		return Strings.trim(document.getObjectname());
	}

	public String getDocumentSize() {
		final DMSObject document = getDocument();
		if (document == null)
			return "<Fehler>";
		return ByteSizeDescription.getDescription(document.getBytesize());
	}

	public String getDocumentDate() {
		final DMSObject document = getDocument();
		if (document == null)
			return "<Fehler>";
		return TimestampUtil.DATE10.format(document.getLastChanged());
	}

	public String getDocumentText() {
		final DMSObject document = getDocument();
		if (document == null)
			return "<Fehler>";
		return "<Nicht Implementiert>";
	}

}

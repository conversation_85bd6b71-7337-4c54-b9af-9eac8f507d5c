package ch.eisenring.dms.client.gui.tree.action;

import java.awt.event.ActionEvent;

import javax.swing.AbstractAction;

import ch.eisenring.dms.client.gui.tree.CompoundContentView;
import ch.eisenring.dms.client.gui.tree.HistoryState;

@SuppressWarnings("serial")
public class HistoryForwardAction extends AbstractAction {

	protected final CompoundContentView contentView;

	public HistoryForwardAction(final CompoundContentView contentView) {
		this.contentView = contentView;
	}

	@Override
	public void actionPerformed(final ActionEvent event) {
		if (contentView.getCompoundContentView() != null) {
			final HistoryState history = contentView.getCompoundContentView().getHistory();
			if (history != null && history.canGoForward()) {
				// perform history forward
				history.goForward();
			}
		}		
	}

	@Override
	public boolean isEnabled() {
		if (contentView.getCompoundContentView() != null) {
			final HistoryState history = contentView.getCompoundContentView().getHistory();
			if (history != null) {
				return history.canGoForward();
			}
		}
		return false;
	}	

}

package ch.eisenring.dms.client.action;

import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.util.SimpleParameterMap;
import ch.eisenring.dms.client.DMSClient;
import ch.eisenring.dms.client.gui.tree.ContentView;
import ch.eisenring.dms.client.resources.Images;
import ch.eisenring.dms.service.api.DMSObject;
import ch.eisenring.dms.service.codetables.DMSSpecialFunctionCode;
import ch.eisenring.dms.shared.network.PacketReplicateTemplateReply;
import ch.eisenring.dms.shared.network.PacketReplicateTemplateRequest;

public class ActionFolderCreateUmbau extends ActionFolderCreateSpecial {

	private final static String FOLDER_NAME = "Umbau";

	public ActionFolderCreateUmbau(
			final DMSClient client, 
			final ContentView contentView,
			final DMSObject parentFolder) {
		super(client, contentView, parentFolder, FOLDER_NAME,
				FOLDER_NAME + " Unterordner erstellen",
				Images.TYPE_FOLDER);
	}

	@Override
	protected void performAction() {
		try {
			final SimpleParameterMap parameters = new SimpleParameterMap();
			final PacketReplicateTemplateRequest request = PacketReplicateTemplateRequest.create(
					parentFolder.getPKValue(), DMSSpecialFunctionCode.AUFTRAGSBESTAETIGUNG_UMBAU,
					parameters, client.getLoginname());
			final PacketReplicateTemplateReply reply = (PacketReplicateTemplateReply) client.sendAndWait(request);
			handleReply(reply);
		} catch (final Exception e) {
			client.message(new ErrorMessage(e));
		}
	}

}

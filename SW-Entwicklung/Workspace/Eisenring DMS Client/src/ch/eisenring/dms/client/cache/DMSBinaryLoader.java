package ch.eisenring.dms.client.cache;

import java.io.IOException;

import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.dms.client.DMSClient;
import ch.eisenring.dms.service.api.DMSObject;
import ch.eisenring.dms.shared.cache.binary.DMSBinaryCache;
import ch.eisenring.dms.shared.model.api.DMSBinary;
import ch.eisenring.dms.shared.model.api.VersionIdentifier;
import ch.eisenring.dms.shared.model.api.VersionIdentifierList;
import ch.eisenring.dms.shared.network.PacketGetBinaryReply;
import ch.eisenring.dms.shared.network.PacketGetBinaryRequest;
import ch.eisenring.dms.shared.network.brq.BinaryReply;
import ch.eisenring.dms.shared.network.brq.BinaryRequest;
import ch.eisenring.dms.shared.network.brq.BinaryRequestActive;
import ch.eisenring.dms.shared.network.brq.BinaryRequestVersion;

public class DMSBinaryLoader {

	protected final DMSClient client;
	
	public DMSBinaryLoader(final DMSClient client) {
		this.client = client;
	}

	private DMSBinaryCache getCache() {
		return client.BINARY_CACHE.get();
	}

	/**
	 * Gets the specified version
	 */
	public DMSBinary getVersion(final long objectRowId, final long binaryRowId) throws IOException {
		final DMSBinaryCache cache = getCache();
		final PacketGetBinaryRequest request = PacketGetBinaryRequest.create(client.getLoginname());
		request.addRequest(new BinaryRequestVersion(binaryRowId, cache.getBinaries(objectRowId)));
		final List<DMSBinary> list = getBinaries(request);
		for (final DMSBinary binary : list) {
			if (binary.getPKValue() == binaryRowId)
				return binary;
		}
		throw new IOException(Strings.concat("Version #", binaryRowId, " nicht gefunden"));
	}
	
	/**
	 * Gets the active version of object
	 */
	public DMSBinary getActiveVersion(final long objectRowId) throws IOException {
		final DMSBinaryCache cache = getCache();
		final PacketGetBinaryRequest request = PacketGetBinaryRequest.create(client.getLoginname());
		request.addRequest(new BinaryRequestActive(objectRowId, cache.getBinaries(objectRowId)));
		final List<DMSBinary> list = getBinaries(request);
		for (final DMSBinary binary : list) {
			if (binary.getObjectRowId() == objectRowId)
				return binary;
		}
		throw new IOException("Keine Aktive Version für Objekt #" + objectRowId + " gefunden");
	}
	
	/**
	 * Gets the active versions for the objects
	 */
	public List<DMSBinary> getActiveVersions(final java.util.Collection<DMSObject> documents) throws IOException {
		if (documents == null || documents.isEmpty())
			return List.emptyReadOnly(DMSBinary.class);
		final VersionIdentifierList versionList = new VersionIdentifierList();
		for (final DMSObject document : documents) {
			versionList.add(document);
		}
		final List<DMSBinary> binaries = getBinaries(versionList);
		return binaries;
	}

	/**
	 * Gets the binaries identified by VersionIdentifierList.
	 */
	public List<DMSBinary> getBinaries(final VersionIdentifierList versionList) throws IOException {
		if (versionList == null || versionList.isEmpty())
			return List.emptyReadOnly(DMSBinary.class);
		// create requests for each list item
		final DMSBinaryCache cache = getCache();
		final PacketGetBinaryRequest request = PacketGetBinaryRequest.create(client.getLoginname());
		for (final VersionIdentifier identifier : versionList) {
			final long objectRowId = identifier.getObjectRowId();
			final Long binaryRowId = identifier.getBinaryRowId();
			final List<DMSBinary> cachedBinaries = cache.getBinaries(objectRowId);
			if (binaryRowId != null) {
				request.addRequest(new BinaryRequestVersion(binaryRowId, cachedBinaries));
			} else {
				request.addRequest(new BinaryRequestActive(objectRowId, cachedBinaries));
			}
		}
		return getBinaries(request);
	}

	/**
	 * Perform all steps required to load the binaries requested by the packet.
	 */
	private List<DMSBinary> getBinaries(final PacketGetBinaryRequest request) throws IOException {
		final List<DMSBinary> result = new ArrayList<DMSBinary>();
		final PacketGetBinaryReply reply;
		try {
			reply = (PacketGetBinaryReply) client.sendAndWait(request);
		} catch (final ClassCastException e) {
			throw new IOException("Unerwartete Serverantwort: " + e.getMessage(), e);
		}
		if (reply == null) {
			throw new IOException("Zeitüberschreitung der Serveranfrage: " + request.getClass());
		}
		// process the reply
		final DMSBinaryCache cache = getCache();
		final List<BinaryRequest> brqs = request.getRequests();
		for (final BinaryRequest brq : brqs) {
			final BinaryReply brp = reply.getReply(brq);
			final ErrorMessage error = brp == null ? ErrorMessage.ERROR : brp.getError();
			if (error.isSuccess()) {
				final DMSBinary binary = brp.getBinary(cache);
				result.add(binary);
				cache.addBinary(binary);
			} else {
				throw new IOException(error.getText());
			}
		}
		return result;
	}

}

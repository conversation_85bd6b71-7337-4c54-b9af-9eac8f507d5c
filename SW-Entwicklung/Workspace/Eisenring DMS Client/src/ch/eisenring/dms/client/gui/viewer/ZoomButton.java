package ch.eisenring.dms.client.gui.viewer;

import java.awt.event.ActionEvent;

import javax.swing.AbstractAction;

import ch.eisenring.core.resource.ImageResource;

@SuppressWarnings("serial")
abstract class ZoomButton extends ControlButton {

	private final ViewerContext viewerContext;

	protected final AbstractAction actionZoomIn = new AbstractAction() {
		@Override
		public void actionPerformed(final ActionEvent event) {
			if (isActionEnabled()) {
				viewerContext.changeZoom(1.25D);
			}
		}
	};

	protected final AbstractAction actionZoomOut = new AbstractAction() {
		@Override
		public void actionPerformed(final ActionEvent event) {
			if (isActionEnabled()) {
				viewerContext.changeZoom(0.80D);
			}
		}
	};

	protected final AbstractAction actionZoom100 = new AbstractAction() {
		@Override
		public void actionPerformed(final ActionEvent event) {
			if (isActionEnabled()) {
				viewerContext.setZoom(ViewerContext.ZOOM_ONE);
			}
		}
	};

	protected ZoomButton(final ImageResource image, final int size,
			             final ViewerContext viewerContext) {
		super(image, size);
		this.viewerContext = viewerContext;
	}

}

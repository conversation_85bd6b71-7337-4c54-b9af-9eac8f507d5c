package ch.eisenring.dms.client.action;

import ch.eisenring.core.collections.Collection;
import ch.eisenring.dms.client.DMSClient;
import ch.eisenring.dms.client.async.OpenAction;
import ch.eisenring.dms.service.api.DMSObject;
import ch.eisenring.dms.service.codetables.DMSObjectType;
import ch.eisenring.dms.shared.client.util.FileOpenType;
import ch.eisenring.dms.shared.client.util.FileType;
import ch.eisenring.dms.shared.codetables.DMSObjectTypeProperties;

public final class ActionFileOpen extends ActionFileAbstract {

	public ActionFileOpen(final DMSClient client, final Object gui, final DMSObject document) {
		super(client, gui, "Anzeigen...", DEFAULT_ACTION_TRUE);
		addDocument(document);
	}

	public ActionFileOpen(final DMSClient client, final Object gui, final Collection<DMSObject> documents) {
		super(client, gui, "Anzeigen...", DEFAULT_ACTION_TRUE);
		addDocuments(documents);
	}

	@Override
	protected boolean isEnabledImpl() {
		if (!super.isEnabledImpl())
			return false;
		// check permission on all documents
		boolean openable = false;
		for (final DMSObject object : documents) {
			final DMSObjectType type = object.getType();
			if (!DMSObjectType.FILE.equals(type))
				return false;
			final DMSObjectTypeProperties properties = DMSObjectTypeProperties.get(type);
			if (properties == null || !objectCache.isPermitted(object, properties.getViewPermissionCode()))
				return false;
			final FileOpenType openType = FileType.getOpenType(object);
			openable |= (FileOpenType.OPEN_OS.equals(openType)) ||
						(FileOpenType.OPEN_QUICKVIEW.equals(openType));
		}
		return openable;
	}

	@Override
	protected void performAction() {
		final OpenAction openAction = new OpenAction(client);
		openAction.addDocuments(documents);
		openAction.start();
	}

}

package ch.eisenring.dms.client.action;

import java.awt.event.InputEvent;
import java.awt.event.KeyEvent;

import javax.swing.KeyStroke;

import ch.eisenring.dms.client.DMSClient;
import ch.eisenring.dms.client.gui.DMSMainWindow;
import ch.eisenring.dms.client.gui.zipbuilder.ZipBuilderDialog;
import ch.eisenring.dms.client.resources.Images;
import ch.eisenring.gui.window.AbstractBaseDialog;

public class ActionShowZipBuilder extends AbstractDMSAction {

	protected final DMSMainWindow window;
	
	public ActionShowZipBuilder(final DMSClient client, final DMSMainWindow window) {
		super(client, KeyStroke.getKeyStroke(KeyEvent.VK_Z, InputEvent.CTRL_DOWN_MASK),
				Images.TYPE_ARCHIVE, "_ZIP-Fenster anzeigen");
		this.window = window;
	}

	@Override
	protected void performAction() {
		showZipBuilder(client);
	}

	public static void showZipBuilder(final DMSClient client) {
		if (AbstractBaseDialog.hasActiveInstance(ZipBuilderDialog.class))
			return;
		final ZipBuilderDialog dialog = new ZipBuilderDialog(client);
		AbstractBaseDialog.showWindow(dialog);
	}

}

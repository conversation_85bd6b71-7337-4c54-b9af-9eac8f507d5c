package ch.eisenring.dms.client.gui.addfiles;

import java.awt.GridBagLayout;
import java.io.File;

import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.io.binary.BinaryHolder;
import ch.eisenring.core.tag.TagSet;
import ch.eisenring.core.util.file.FileUtil;
import ch.eisenring.dms.client.DMSClient;
import ch.eisenring.dms.client.codetables.DMSNamingMode;
import ch.eisenring.dms.client.gui.AbstractDMSDialog;
import ch.eisenring.dms.client.gui.dnd.FileProxy;
import ch.eisenring.dms.client.resources.Images;
import ch.eisenring.dms.service.api.DMSObject;
import ch.eisenring.gui.balloontip.BalloonTipManager;
import ch.eisenring.gui.components.Separator;
import ch.eisenring.gui.util.GUIUtil;
import ch.eisenring.gui.util.LayoutUtil;
import ch.eisenring.gui.window.DialogTags;

/**
 * <PERSON>les adding of files to a folder.
 *
 * The dialog will query the user interactively for
 * the required meta data for each file.
 */
@SuppressWarnings("serial")
public class AddFilesDialog extends AbstractDMSDialog {

	protected final AddFilesContext context;
	protected final AddFileMetaDataPanel metaDataPanel;
	protected final AddFileButtonPanel buttonPanel;
	protected final AddFilesTablePanel tablePanel;
	protected final DocumentCreateHelper createHelper;

	public AddFilesDialog(final AddFilesContext context) {
		super(context.client, new TagSet(
				DialogTags.TITLE, "Dateien hinzufügen",
				DialogTags.DIALOG_OWNER, context.client.getCore().getRootWindow(),
				DialogTags.POSITION_SETTINGS_ID, "AddFilesWindow",
				DialogTags.ICON, Images.ADD));
		this.context = context;
		this.metaDataPanel = new AddFileMetaDataPanel(client);
		this.buttonPanel = new AddFileButtonPanel(this);
		this.tablePanel = new AddFilesTablePanel(context);
		this.createHelper = new DocumentCreateHelper(client, buttonPanel.btnAdd, this) {
			@Override
			protected void requestDone() {
				super.requestDone();
				final AddFileEntry entry = context.getFile();
				entry.statusCode = createParams.statusCode;
				entry.resultMessage = createParams.message.getText();
				if (createParams.message.isSuccess()) {
					entry.statusCode = AddFileStatusCode.OK;
					entry.resultMessage = "Ok";
					if (metaDataPanel.isUseOnAll()) {
						GUIUtil.invokeLater(new Runnable() {
							public void run() {
								if (nextFile())
									onOk();
								else
									onCancel();
							}
						});
					} else {
						nextFile();
					}
				}
			}
		};
		initComponents();
		initLayout();
		pack();
		setResizable(false);
	}

	private void initComponents() {
	}
	
	private void initLayout() {
		getContentPane().removeAll();
		setLayout(new GridBagLayout());
		final LayoutUtil l = new LayoutUtil(1);
		add(metaDataPanel, l.panel(1, 0));
		add(Separator.create(), l.separator());
		add(buttonPanel, l.panel(1, 0));
		add(Separator.create(), l.separator());
		add(tablePanel, l.panel(1, 1, 1));
	}

	// --------------------------------------------------------------
	// ---
	// --- AbstractDialog implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public void dispose() {
		if (metaDataPanel.isDeleteAdded()) {
			// delete the files successfully added from local disk
			context.deleteSuccessfulFiles();
		}
		context.dispose();
		super.dispose();
	}

	@Override
	protected void onApply() {
		BalloonTipManager.clearAll(this);
		// apply works as "skip file" here
		final AddFileEntry fileEntry = context.getFile();
		fileEntry.statusCode = AddFileStatusCode.SKIPPED;
		fileEntry.resultMessage = "Übersprungen";
		nextFile();
	}
	
	@Override
	protected void onOk() {
		BalloonTipManager.clearAll(this);
		doOk();
		repaint();
	}

	private void doOk() {
		metaDataPanel.updateModel();
		final AddFileEntry fileEntry = context.getFile();
		final DMSObject targetFolder = fileEntry.context.folder;
		final BinaryHolder binary = fileEntry.getFileContent();
		String documentName = metaDataPanel.getSelectedFileName();
		final DMSNamingMode mode = metaDataPanel.getNamingMode();
		if (mode.isNumbered()) {
			documentName = Strings.concat(documentName, " (", (context.getFileIndex() + 1), ")");
		}
		final String fullName = FileUtil.addFileExtension(documentName, fileEntry.fileExtension);
		final DocumentCreateArgument createParams = new DocumentCreateArgument(
				targetFolder, fullName, binary, fileEntry.documentCode);
		createParams.reduceImages = metaDataPanel.isReduceLargeImages();

		// set entry status to "Busy" and add the file
		fileEntry.statusCode = AddFileStatusCode.BUSY;
		repaint();
		createHelper.doEverything(createParams);
	}

	@Override
	public void setEnabled(final boolean enabled) {
		super.setEnabled(enabled);
		metaDataPanel.setEnabled(enabled);
		buttonPanel.setEnabled(enabled);
		tablePanel.setEnabled(enabled);
	}

	// --------------------------------------------------------------
	// ---
	// --- Option control (see AddFileConstants)
	// ---
	// --------------------------------------------------------------
	public void setOptions(final int options) {
		metaDataPanel.chkDeleteAdded.setEnabled((options & AddFileConstants.OPTION_DELETE_ENABLED) != 0);
		metaDataPanel.chkDeleteAdded.setSelected((options & AddFileConstants.OPTION_DELETE_CHECKED) != 0);
	}

	// --------------------------------------------------------------
	// ---
	// ---
	// ---
	// --------------------------------------------------------------
	/**
	 * Advances to the next file in context,
	 * closes dialog if no more files.
	 */
	public boolean nextFile() {
		if (!context.next()) {
			buttonPanel.setFileEntry(null);
			metaDataPanel.setEnabled(false);
			// automatically close dialog if no errors
			if (context.isAllFine()) {
				GUIUtil.hideWindow(this);
				this.dispose();
			}
			return false;
		}
		final AddFileEntry fileEntry = context.getFile();
		metaDataPanel.setFileEntry(fileEntry);
		buttonPanel.setFileEntry(fileEntry);
		return true;
	}
	
	/**
	 * Handles adding of files
	 */
	@Deprecated
	public static void doDialog(final DMSClient client,
								final DMSObject folder,
								final File[] files) {
		if (files == null || files.length <= 0)
			client.error("Keine Dateien ausgewählt");
		final AddFilesContext context = new AddFilesContext(client, folder, files);
		final AddFilesDialog dialog = new AddFilesDialog(context);
		dialog.nextFile();
		dialog.setVisible(true);
	}

	/**
	 * Handles adding of files
	 */
	public static void doDialog(final DMSClient client,
			                    final DMSObject folder,
			                    final FileProxy[] files,
			                    final int options) {
		if (files == null || files.length <= 0)
			client.error("Keine Dateien ausgewählt");
		final AddFilesContext context = new AddFilesContext(client, folder, files);
		final AddFilesDialog dialog = new AddFilesDialog(context);
		dialog.setOptions(options);
		dialog.nextFile();
		dialog.setVisible(true);
	}

}

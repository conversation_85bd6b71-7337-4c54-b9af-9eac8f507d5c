package ch.eisenring.dms.client.action;

import ch.eisenring.core.tag.TagSet;
import ch.eisenring.dms.client.DMSClient;
import ch.eisenring.dms.client.gui.aspect.CreateFolderPanel;
import ch.eisenring.dms.client.gui.tree.ContentView;
import ch.eisenring.dms.client.resources.Images;
import ch.eisenring.dms.service.api.DMSObject;
import ch.eisenring.dms.service.codetables.DMSObjectType;
import ch.eisenring.dms.shared.codetables.DMSObjectTypeProperties;
import ch.eisenring.gui.window.WindowTags;

public final class ActionFolderCreate extends AspectDialogAction {

	private final ContentView contentView;

	public ActionFolderCreate(final DMSClient client,
			                  final Long parentId,
			                  final ContentView contentView) {
		super(client, parentId, null,
				Images.TYPE_FOLDER, "Neuer Ordner...");
		this.contentView = contentView;
	}

	@Override
	protected boolean isEnabledImpl() {
		if (!super.isEnabledImpl())
			return false;
		final DMSObject parent = getObject();
		final DMSObjectType type = parent.getType();
		// only folders can have children
		if (!DMSObjectType.FOLDER.equals(type))
			return false;
		// disable if no permission to create folder
		final DMSObjectTypeProperties properties = DMSObjectTypeProperties.get(type);
		return properties != null && isPermitted(properties.getCreatePermissionCode());
	}

	@Override
	@SuppressWarnings("serial")
	protected void performAction() {
		final CreateFolderPanel panel = new CreateFolderPanel(client, getObjectRowId(), contentView);
		showDialog(panel, new TagSet(
				WindowTags.POSITION_SETTINGS_ID, "FolderCreateDialog",
				WindowTags.TITLE, "Neuer Ordner",
				WindowTags.RESIZEABLE, WindowTags.RESIZEABLE_NO));
	}

}

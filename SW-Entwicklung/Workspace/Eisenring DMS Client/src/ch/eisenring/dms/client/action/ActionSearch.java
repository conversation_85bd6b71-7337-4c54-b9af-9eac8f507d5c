package ch.eisenring.dms.client.action;

import java.awt.event.InputEvent;
import java.awt.event.KeyEvent;

import javax.swing.KeyStroke;

import ch.eisenring.dms.client.DMSClient;
import ch.eisenring.dms.client.gui.DMSMainWindow;
import ch.eisenring.dms.client.gui.search.SearchExtDialog;
import ch.eisenring.dms.client.resources.Images;
import ch.eisenring.dms.service.api.DMSObject;
import ch.eisenring.dms.shared.network.PacketFullTextSearchRequest;
import ch.eisenring.gui.window.AbstractBaseWindow;

public class ActionSearch extends AbstractDMSAction {

	private final PacketFullTextSearchRequest request;
	
	public ActionSearch(final DMSClient client,
					    final Object gui,
			            final PacketFullTextSearchRequest request) {
		super(client, 
				(request == null ? KeyStroke.getKeyStroke(KeyEvent.VK_F, InputEvent.CTRL_DOWN_MASK) : null),
				Images.SEARCH2, "Erweiterte Dokument-_Suche");
		setSourceGUI(gui);
		this.request = request;
	}

	@Override
	protected void performAction() {
		PacketFullTextSearchRequest request = this.request;
		if (request == null) {
			// determine current folder id
			Long currentFolderId = null;
			try {
				DMSObject i = ((DMSMainWindow) sourceGUI).getContentView().getShowingFolder();
				currentFolderId = i.getPKValue();
			} catch (final Exception e) {
				// ignore
			}
			request = PacketFullTextSearchRequest.create(
					currentFolderId, null, client.getLoginname());
			request.setSearchChildFolders(true);
		}
		final SearchExtDialog dialog = new SearchExtDialog(client, request, getSourceGUI());
		AbstractBaseWindow.showWindow(dialog);
	}

}

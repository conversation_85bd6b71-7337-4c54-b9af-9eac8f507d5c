package ch.eisenring.dms.client.gui.addfiles;

import java.io.File;

import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.interfaces.Disposable;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.dms.client.DMSClient;
import ch.eisenring.dms.client.gui.dnd.FileProxy;
import ch.eisenring.dms.service.api.DMSObject;
import ch.eisenring.dms.shared.model.api.DMSProperty;
import ch.eisenring.dms.shared.model.api.DMSPropertyAccessor;
import ch.eisenring.dms.shared.model.api.DMSPropertyAccessor.Mode;
import ch.eisenring.dms.shared.network.PacketGetPropertiesReply;
import ch.eisenring.dms.shared.network.PacketGetPropertiesRequest;

public class AddFilesContext implements Disposable {

	protected final DMSClient client;
	protected final DMSObject folder;
	protected final AddFileEntry[] files;
	protected int fileIndex;
	
	@Deprecated
	public AddFilesContext(final DMSClient client,
						   final DMSObject folder,
						   final File ... files) {
		this.client = client;
		this.folder = folder;
		this.files = new AddFileEntry[files.length];
		for (int i=0; i<files.length; ++i) {
			this.files[i] = new AddFileEntry(files[i], this);
		}
		this.fileIndex = -1;
	}

	public AddFilesContext(final DMSClient client,
			   			   final DMSObject folder,
			   			   final FileProxy ... files) {
		this.client = client;
		this.folder = folder;
		this.files = new AddFileEntry[files.length];
		for (int i=0; i<files.length; ++i) {
			this.files[i] = new AddFileEntry(files[i], this);
		}
		this.fileIndex = -1;
	}

	/**
	 * Advances to the next file in the context
	 */
	public boolean next() {
		if (fileIndex >= 0 && fileIndex <= files.length)
			files[fileIndex].marker = false;
		if (files == null)
			return false;
		if (fileIndex + 1 >= files.length)
			return false;
		++fileIndex;
		files[fileIndex].marker = true;
		return true;
	}

	public int getFileIndex() {
		return fileIndex;
	}

	/**
	 * Gets the current file in context
	 */
	public AddFileEntry getFile() {
		if (fileIndex < 0 || fileIndex >= files.length)
			return null;
		return files[fileIndex];
	}

	/**
	 * Determines if there are any errors in the context.
	 */
	public boolean isAllFine() {
		for (final AddFileEntry entry : files) {
			if (!AddFileStatusCode.OK.equals(entry.statusCode))
				return false;
		}
		return true;
	}

	// --------------------------------------------------------------
	// ---
	// --- Folder Property retrieval
	// ---
	// --------------------------------------------------------------
	private final List<DMSProperty> folderProperties = new ArrayList<>();
	private DMSObject propertyFolder;

	/**
	 * Gets all properties of parent folder
	 */
	public List<DMSProperty> getFolderProperties() {
		try {
			boolean propertiesValid = folder.equals(propertyFolder);
			if (!propertiesValid) {
				folderProperties.clear();
				propertyFolder = null;
				// fetch the properties for the folder
				final PacketGetPropertiesRequest request =
						PacketGetPropertiesRequest.create(folder.getPKValue());
				final PacketGetPropertiesReply reply = (PacketGetPropertiesReply) client.sendAndWait(request);
				if (!reply.isValid())
					throw new IllegalStateException("invalid server reply!");
				try {
					final DMSPropertyAccessor accessor = reply.getAccessor();
					final List<DMSProperty> properties = accessor.getProperties(Mode.REGULAR);
					folderProperties.addAll(properties);
				} catch (final Exception e) {
					// ignore error
					Logger.warn(e);
				}
				propertyFolder = folder;
			}
		} catch (final Throwable t) {
			// swallow the exception, but invalidate the cached properties
			folderProperties.clear();
			propertyFolder = null;
		}
		return folderProperties;
	}

	/**
	 * Deletes all files sucessfully added
	 */
	public void deleteSuccessfulFiles() {
		for (final AddFileEntry fileEntry : files) {
			if (fileEntry == null)
				continue;
			if (!AddFileStatusCode.OK.equals(fileEntry.statusCode))
				continue;
			final File file = fileEntry.file.getFile();
			if (file == null)
				continue;
			fileEntry.dispose();
			file.delete();
		}
	}
	
	@Override
	public void dispose() {
		for (final AddFileEntry file : files) {
			if (file != null)
				file.dispose();
		}
	}

}

package ch.eisenring.dms.client.gui.viewer;

import java.awt.BorderLayout;
import java.awt.Color;
import java.awt.Component;
import java.awt.Dimension;
import java.awt.Rectangle;
import java.awt.event.ItemEvent;
import java.awt.event.ItemListener;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.awt.event.MouseListener;

import javax.swing.JCheckBox;
import javax.swing.JComponent;
import javax.swing.JLabel;
import javax.swing.JPanel;
import javax.swing.JScrollPane;
import javax.swing.ScrollPaneConstants;
import javax.swing.SwingConstants;
import javax.swing.UIManager;

import ch.eisenring.dms.service.api.DMSObject;
import ch.eisenring.dms.shared.DMSConstants;
import ch.eisenring.dms.shared.client.util.DMSIconUtil;
import ch.eisenring.gui.components.HEAGPanel;

@SuppressWarnings("serial")
public class ViewerNavigationPanel extends HEAGPanel implements ViewerComponent {

	protected final ViewerContext context;

	protected final JScrollPane scrollPane;
	protected final JPanel contentPanel;
	
	public ViewerNavigationPanel(final ViewerContext context) {
		this.context = context;
		this.scrollPane = new JScrollPane();
		this.contentPanel = new JPanel();
		initComponents();
		initLayout();
	}

	private void initComponents() {
		final Dimension d = new Dimension(172, 172);
		scrollPane.setMinimumSize(d);
		scrollPane.setVerticalScrollBarPolicy(ScrollPaneConstants.VERTICAL_SCROLLBAR_NEVER);
		scrollPane.setHorizontalScrollBarPolicy(ScrollPaneConstants.HORIZONTAL_SCROLLBAR_ALWAYS);
		scrollPane.setViewportView(contentPanel);
		scrollPane.getHorizontalScrollBar().setUnitIncrement(130);
		Color bg = UIManager.getColor("List.background");
		if (bg == null)
			bg = Color.WHITE;
		scrollPane.setBackground(bg);
		contentPanel.setBackground(bg);
		setLayout(new BorderLayout());
		add(scrollPane, BorderLayout.CENTER);
		contentPanel.setLayout(null);
	}

	private void initLayout() {
		contentPanel.removeAll();
		int width = 0;
		int height = 0;
		for (int i=0; i<context.size(); ++i) {
			final DMSObject document = context.getDocument(i);
			if (document != null) {
				final ViewerNavigationSubPanel documentPanel = new ViewerNavigationSubPanel(this, document);
				documentPanel.setLocation(width, 0);
				contentPanel.add(documentPanel);
				width += documentPanel.getWidth();
				if (documentPanel.getHeight() > height)
					height = documentPanel.getHeight();
			}
		}
		if (width <= 0)
			width = 1;
		contentPanel.setBounds(0, 0, width, height);
		contentPanel.setPreferredSize(new Dimension(width, height));
		scrollPane.validate();
		scrollPane.repaint();
	}

	@Override
	public void dispose() {
		// nothing for this class
	}

	@Override
	public ViewerContext getContext() {
		return context;
	}

	@Override
	public JComponent getComponent() {
		return this;
	}
	
	@Override
	public void documentsChanged() {
		initLayout();
	}
	
	@Override
	public void currentDocumentChanged() {
		ViewerNavigationSubPanel currentPanel = null;
		for (final Component component : contentPanel.getComponents()) {
			final ViewerNavigationSubPanel panel = (ViewerNavigationSubPanel) component;
			panel.updateComponents();
			if (ViewerContext.equals(panel.getDocument(), getContext().getCurrentDocument())) {
				currentPanel = panel;
			}
		}
		if (currentPanel != null) {
			final Rectangle r = currentPanel.getBounds();
			r.height = 1;
			contentPanel.scrollRectToVisible(r);
		}
	}

	@Override
	public void selectionChanged() {
		currentDocumentChanged();		
	}

	@Override
	public void zoomChanged() {
		// ignore		
	}

	@Override
	public void rotationChanged() {
		// ignore
	}

}

@SuppressWarnings("serial")
class ViewerNavigationSubPanel extends JPanel {
	
	private final static MouseListener mouseListener = new MouseAdapter() {
		@Override
		public void mouseClicked(final MouseEvent event) {
			if (event.isConsumed())
				return;
			Component component = event.getComponent();
			while (component != null) {
				if (component instanceof ViewerNavigationSubPanel) {
					final ViewerNavigationSubPanel panel = (ViewerNavigationSubPanel) component;
					panel.getContext().setCurrentDocument(panel.getDocument());
					break;
				}
				component = component.getParent();
			}
		}
	};

	private final static ItemListener itemListener = new ItemListener() {
		@Override
		public void itemStateChanged(final ItemEvent event) {
			final int stateChange = event.getStateChange();
			switch (stateChange) {
				case ItemEvent.SELECTED:
				case ItemEvent.DESELECTED:
					final JCheckBox checkBox = (JCheckBox) event.getSource();
					final boolean selected = checkBox.isSelected();
					final ViewerNavigationSubPanel panel = (ViewerNavigationSubPanel) checkBox.getParent();
					if (panel != null) {
						panel.getContext().setSelected(panel.getDocument(), selected);
					}
			}
		}
	};

	private final ViewerNavigationPanel navigationPanel;
	private final DMSObject document;
	private final JLabel lblIcon;
	private final JCheckBox chkSelect;

	ViewerNavigationSubPanel(final ViewerNavigationPanel navigationPanel,
			                 final DMSObject document) {
		this.navigationPanel = navigationPanel;
		this.document = document;
		this.lblIcon = new JLabel();
		this.chkSelect = new JCheckBox();
		initComponents();
		initLayout();
		updateComponents();
	}

	private void initComponents() {
		setLayout(null);
		addMouseListener(mouseListener);
		lblIcon.addMouseListener(mouseListener);
		chkSelect.addMouseListener(mouseListener);
		chkSelect.addItemListener(itemListener);
		chkSelect.setFocusable(false);
	}

	private void initLayout() {
		// layout the panel (fixed layout)
		lblIcon.setIcon(DMSIconUtil.getIcon(document, DMSConstants.THUMBNAIL_SIZE));
		lblIcon.setHorizontalAlignment(SwingConstants.CENTER);
		lblIcon.setVerticalAlignment(SwingConstants.CENTER);
		chkSelect.setSelected(navigationPanel.getContext().isSelected(document));
		chkSelect.setText(document.getObjectname());
		final int s = 1;
		final int w1 = DMSConstants.THUMBNAIL_SIZE;
		final int h1 = DMSConstants.THUMBNAIL_SIZE;
		final int w0 = (s * 2) + w1;
		final int h0 = (s * 3) + h1 + 21;
		final int w2 = w1; 
		final int h2 = chkSelect.getPreferredSize().height;
		final int x1 = s;
		final int y1 = s;
		final int x2 = s;
		final int y2 = (s << 1) + h1;
		final Dimension d = new Dimension(w0, h0);
		setPreferredSize(d);
		setMinimumSize(d);
		setMaximumSize(d);
		setSize(w0, h0);
		lblIcon.setBounds(x1, y1, w1, h1);
		chkSelect.setBounds(x2, y2, w2, h2);
		add(lblIcon);
		add(chkSelect);
	}

	/**
	 * Updates the colors of the components
	 */
	public void updateComponents() {
		Color fg, bg;
		if (ViewerContext.equals(getContext().getCurrentDocument(), document)) {
			fg = UIManager.getColor("List.selectionForeground");
			bg = UIManager.getColor("List.selectionBackground");
			if (fg == null)
				fg = Color.WHITE;
			if (bg == null)
				bg = Color.BLUE;
		} else {
			fg = UIManager.getColor("List.foreground");
			bg = UIManager.getColor("List.background");
			if (fg == null)
				fg = Color.BLACK;
			if (bg == null)
				bg = Color.WHITE;
		}
		setComponentColors(this, fg, bg);
		setComponentColors(lblIcon, fg, bg);
		setComponentColors(chkSelect, fg, bg);
		chkSelect.setSelected(getContext().isSelected(document));
	}

	private static void setComponentColors(final JComponent component, final Color fg, final Color bg) {
		component.setForeground(fg);
		component.setBackground(bg);
	}

	public ViewerContext getContext() {
		return navigationPanel.getContext();
	}

	public DMSObject getDocument() {
		return document;
	}

}
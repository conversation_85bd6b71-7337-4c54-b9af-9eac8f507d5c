package ch.eisenring.dms.client.gui.employee;

import java.awt.BorderLayout;

import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.tag.TagSet;
import ch.eisenring.core.util.SimpleParameterMap;
import ch.eisenring.dms.client.DMSClient;
import ch.eisenring.dms.client.action.ActionFolderCreateSpecial;
import ch.eisenring.dms.client.gui.AbstractDMSDialog;
import ch.eisenring.dms.client.gui.tree.ContentView;
import ch.eisenring.dms.client.resources.Images;
import ch.eisenring.dms.service.codetables.DMSSpecialFunctionCode;
import ch.eisenring.dms.shared.network.PacketReplicateTemplateReply;
import ch.eisenring.dms.shared.network.PacketReplicateTemplateRequest;
import ch.eisenring.gui.model.StoreResults;
import ch.eisenring.gui.window.DialogTags;
import ch.eisenring.gui.window.SACButtonPanel;
import ch.eisenring.gui.window.WindowTags;

@SuppressWarnings("serial")
public class NewEmployeeDialog extends AbstractDMSDialog {

	protected final SACButtonPanel buttonPanel = new SACButtonPanel();
	protected final NewEmployeePanel namePanel = new NewEmployeePanel("Mitarbeiter Ordner Name");
	protected final ContentView contentView;
	protected final Long targetFolderId;
	
	public NewEmployeeDialog(final ContentView contentView, final Long targetFolderId, final Object ... tagValuePairs) {
		super(contentView.getClient(), extendTags(contentView.getClient(), tagValuePairs));
		this.contentView = contentView;
		this.targetFolderId = targetFolderId;
		initComponents();
		initLayout();
		pack();
	}

	private static TagSet extendTags(final DMSClient client, final Object ... tagValuePairs) {
		final TagSet tags = new TagSet(tagValuePairs);
		tags.addMissing( 
				DialogTags.DIALOG_OWNER, client.WINDOW_MANAGER.get().getPrimaryWindow(),
				DialogTags.MODALITY, DialogTags.MODALITY_MODAL,
				WindowTags.ICON, Images.TYPE_FOLDER,
				WindowTags.TITLE, "Neuer Ordner",
				WindowTags.RESIZEABLE, WindowTags.RESIZEABLE_NO);
		return tags;
	}

	private void initComponents() {
		setLayout(new BorderLayout());
		buttonPanel.configureApplyButton(null, null);
		buttonPanel.configureSaveButton("Erstellen", "Ordner für Mitarbeiter erstellen");
	}

	private void initLayout() {
		getContentPane().removeAll();
		add(namePanel, BorderLayout.CENTER);
		add(buttonPanel, BorderLayout.SOUTH);
	}

	protected String getFolderName() {
		return namePanel.getFolderName();
	}

	/**
	 * This is a create-dialog, its always changed
	 */
	@Override
	public boolean isViewChanged() {
		return true;
	}

	public void storeView(final StoreResults results, final Object model) {
		try {
			final SimpleParameterMap parameters = new SimpleParameterMap();
			parameters.put("Mitarbeitername", getFolderName());
			final PacketReplicateTemplateRequest request = PacketReplicateTemplateRequest.create(
					targetFolderId, DMSSpecialFunctionCode.MITARBEITER_VORLAGE,
					parameters, client.getLoginname());
			final PacketReplicateTemplateReply reply = (PacketReplicateTemplateReply) client.sendAndWait(request);
			if (reply.isValid()) {
				ActionFolderCreateSpecial.selectCreatedFolder(contentView,
						targetFolderId, reply.getCreatedFolderRowId());
			} else {
				results.add(reply.getMessage());
			}
		} catch (final Exception e) {
			results.add(new ErrorMessage(e));
		}
	}

}

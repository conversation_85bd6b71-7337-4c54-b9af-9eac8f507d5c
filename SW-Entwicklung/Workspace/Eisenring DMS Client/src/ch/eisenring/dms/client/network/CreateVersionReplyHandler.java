package ch.eisenring.dms.client.network;

import ch.eisenring.dms.client.DMSClient;
import ch.eisenring.dms.shared.network.PacketCreateVersionReply;
import ch.eisenring.network.PacketSink;
import ch.eisenring.network.implementation.PacketDispatchMode;

final class CreateVersionReplyHandler extends AbstractDMSPacketHandler<PacketCreateVersionReply> {

	CreateVersionReplyHandler(final DMSClient client) {
		super(client, PacketCreateVersionReply.class, PacketDispatchMode.SYNCHRONOUS);
	}

	@Override
	public void handle(final PacketCreateVersionReply packet, final PacketSink sink) {
		// ignore this packet
	}

}

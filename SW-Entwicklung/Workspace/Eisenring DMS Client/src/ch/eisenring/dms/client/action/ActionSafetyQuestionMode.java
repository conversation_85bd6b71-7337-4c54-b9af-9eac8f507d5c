package ch.eisenring.dms.client.action;

import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.observable.Observable;
import ch.eisenring.core.observable.Observer;
import ch.eisenring.core.usersettings.UserSettings;
import ch.eisenring.dms.client.DMSClient;
import ch.eisenring.dms.shared.codetables.SafetyQuestionCode;

public final class ActionSafetyQuestionMode extends AbstractDMSAction {

	private final Observer<Object> observer = new Observer<Object>() {
		public void observableChanged(final Observable<Object> observeable) {
			isChecked();
		}
	};

	private final SafetyQuestionCode code;
	
	public ActionSafetyQuestionMode(final DMSClient client, final SafetyQuestionCode code) {
		super(client, AbstractCode.getLongText(code, "???"));
		final SafetyQuestionCode selected = (SafetyQuestionCode) client.getUserSettings().restore(
				SafetyQuestionCode.SETTINGS_SAFETYQUESTIONMODE, SafetyQuestionCode.class);
		if (AbstractCode.isNull(selected)) {
			client.SAFETY_QUESTION_MODE.set(SafetyQuestionCode.ALWAYS);
		} else {
			client.SAFETY_QUESTION_MODE.set(selected);
		}
		client.SAFETY_QUESTION_MODE.addObserver(observer);
		this.code = code;
	}

	@Override
	protected boolean isCheckedImpl() {
		final UserSettings userSettings = client.getUserSettings();
		final SafetyQuestionCode code = (SafetyQuestionCode) userSettings.restore(SafetyQuestionCode.SETTINGS_SAFETYQUESTIONMODE, SafetyQuestionCode.class);
		return this.code.equals(code);
	}
	
	@Override
	protected void performAction() {
		final UserSettings userSettings = client.getUserSettings();
		userSettings.save(SafetyQuestionCode.SETTINGS_SAFETYQUESTIONMODE, code);
		client.SAFETY_QUESTION_MODE.set(code);
	}

}

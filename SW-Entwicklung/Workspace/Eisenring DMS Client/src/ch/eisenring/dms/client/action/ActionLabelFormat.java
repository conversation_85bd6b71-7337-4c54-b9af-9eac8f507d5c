package ch.eisenring.dms.client.action;

import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.dms.client.DMSClient;
import ch.eisenring.dms.client.codetables.DMSObjectLabelFormat;

public class ActionLabelFormat extends AbstractDMSAction {

	private final DMSObjectLabelFormat labelFormat;
	
	public ActionLabelFormat(DMSClient client, final DMSObjectLabelFormat labelFormat) {
		super(client, AbstractCode.getLongText(labelFormat, "???"));
		this.labelFormat = labelFormat;
		addObservable(client.LABEL_FORMAT);
	}

	@Override
	protected boolean isCheckedImpl() {
		final AbstractCode currentFormat = client.LABEL_FORMAT.get();
		return AbstractCode.equals(labelFormat, currentFormat);
	}

	@Override
	protected void performAction() {
		client.LABEL_FORMAT.set(labelFormat);
	}
	
}

package ch.eisenring.dms.client.network;

import ch.eisenring.dms.client.DMSClient;
import ch.eisenring.dms.shared.network.PacketPermissionDeleteReply;
import ch.eisenring.network.PacketSink;

final class PermissionDeleteReplyHandler extends AbstractDMSPacketHandler<PacketPermissionDeleteReply> {

	PermissionDeleteReplyHandler(final DMSClient client) {
		super(client, PacketPermissionDeleteReply.class);
	}

	@Override
	public void handle(final PacketPermissionDeleteReply packet, final PacketSink sink) {
		// this packet must be handled by RPC
	}

}

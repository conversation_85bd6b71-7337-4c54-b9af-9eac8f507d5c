package ch.eisenring.dms.client.action;

import java.awt.Component;
import java.awt.Toolkit;
import java.awt.datatransfer.Clipboard;

import javax.swing.JComponent;
import javax.swing.TransferHandler;

import ch.eisenring.dms.client.gui.tree.ContentView;

public abstract class ActionClipboardBase extends AbstractDMSAction {

	protected final ContentView contentView;
	protected final TransferHandler transferHandler;

	public ActionClipboardBase(
			final ContentView contentView,
			final TransferHandler transferHandler,
			final Object ... properties) {
		super(contentView.getClient(), properties);
		this.contentView = contentView;
		this.transferHandler = transferHandler;
	}

	protected JComponent getComponent() {
		final Component component = contentView.getGUI();
		if (component instanceof JComponent)
			return (JComponent) component;
		return null;
	}

	protected Clipboard getClipboard() {
		return Toolkit.getDefaultToolkit().getSystemClipboard();
	}

	@Override
	protected boolean isEnabledImpl() {
		return super.isEnabledImpl() && transferHandler != null && getComponent() != null;
	}

}

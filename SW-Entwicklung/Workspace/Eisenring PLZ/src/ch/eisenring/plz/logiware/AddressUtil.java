package ch.eisenring.plz.logiware;

import java.util.Iterator;

import ch.eisenring.core.StringUtil;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.plz.IZipConstants;
import ch.eisenring.plz.ZipCode;
import ch.eisenring.plz.ZipCodeImport;

public abstract class AddressUtil {

	static {
		// init ZIP database
		ZipCodeImport.importDefaults(IZipConstants.CH);
	}

	protected AddressUtil() {
	}

	/**
	 * Versuche Postleitzahl aus Freitext zu extrahieren
	 */
	public static String getPostleitZahl(final CharSequence text) {
		if (Strings.isEmpty(text))
			return null;
		final int length = text.length();
		int start = -1;
		int end = -1;
		int i = -1;
		while (++i < length) {
			final char c = text.charAt(i);
			if (Character.isDigit(c)) {
				if (start < 0) {
					start = i;
				} else {
					end = i+1;
				}
			} else if (start >= 0) {
				break;
			}
		}
		if (start >= 0 && end >= 0) {
			final int plzLen = end-start;
			if (plzLen >= 4 && plzLen <= 5) {
				return Strings.subString(text, start, end);
			}
		}
		return null;
	}

	public static ZipCode findeOrtZIP(final CharSequence text) {
		if (Strings.isEmpty(text))
			return null;
		final String plz = AddressUtil.getPostleitZahl(text);
		String ort = getOrt(text);
		if (plz == null || ort == null)
			return null;
		ort = ort.toLowerCase();
		final Iterator<ZipCode> i = ZipCode.getZipIterator(IZipConstants.CH);
		final String phOrt = StringUtil.getDefaultPhoneticCode(ort);
		if (phOrt == null)
			return null;
		ZipCode matchZip = null;
		int matchScore = -1;
		while (i.hasNext()) {
			final ZipCode zip = i.next();
			int score = -1;
			if (plz.equals(zip.getZip())) {
				// does the name match?
				do {
					final String city = Strings.toLower(zip.getCity());
					final String phCity = StringUtil.getDefaultPhoneticCode(city);
					if (phOrt.equals(phCity)) {
						// exact hit, take immediately
						return zip;
					}
					final String phCityState = StringUtil.getDefaultPhoneticCode(Strings.concat(city, " ", zip.getStateCode()));
					if (phOrt.equals(phCityState)) {
						// good hit, good score
						score = 10;
						break;
					}
					if (phCity.startsWith(phOrt) || city.startsWith(ort)) {
						// head hit
						score = 7;
						break;
					}
					if (phCity.endsWith(phOrt) || city.endsWith(ort)) {
						// tail hit
						score = 5;
						break;
					}
				} while (false);
				if (score >= 0 && matchScore < score) {
					matchScore = score;
					matchZip = zip;
				}
			}
		}
		return matchZip;
	}

	public static String getOrt(final CharSequence text) {
		if (Strings.isEmpty(text))
			return null;
		final int length = text.length();
		int i = -1;
		while (++i < length) {
			final char c = text.charAt(i);
			if (Character.isDigit(c)) {
				if (i < length-1) {
					return Strings.subString(text, 0, i).trim();
				}
			}
		}
		return null;
	}

	/**
	 * Attempts to get Kanton from Logiware Projektbezeichnung.
	 * Returns Kanton (String) or empty String.
	 */
	public static String getKanton(final CharSequence projektBezeichnung) {
		final ZipCode zip = AddressUtil.findeOrtZIP(projektBezeichnung);
		final String result = zip == null ? "" : zip.getStateCode();
		return Strings.trim(result);		
	}

}

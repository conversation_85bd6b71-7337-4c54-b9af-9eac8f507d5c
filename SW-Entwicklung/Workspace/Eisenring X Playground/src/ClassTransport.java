import java.io.ByteArrayOutputStream;
import java.io.InputStream;

	public class ClassTransport {
	
		public byte[] data;
		public String qualifiedName;
	
		public static ClassTransport create(Class<?> theClass) throws Exception {
			ClassTransport result = new ClassTransport();
			result.qualifiedName = theClass.getName();
			// brute force derive the class file name
			String classResourceName = theClass.getName().replace('.', '/') + ".class";				
			
			ClassLoader cl = theClass.getClassLoader();
			try (InputStream in = cl.getResourceAsStream(classResourceName)) {
				ByteArrayOutputStream out = new ByteArrayOutputStream();
				int b;
				while ((b = in.read()) >= 0) {
					out.write(b);
				}
				out.close();
				result.data = out.toByteArray();
			}
			return result;		
		}
	
	}

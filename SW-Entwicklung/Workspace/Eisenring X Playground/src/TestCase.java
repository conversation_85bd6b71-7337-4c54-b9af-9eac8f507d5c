import static java.awt.BasicStroke.*;

import java.awt.*;
import java.awt.geom.*;
import java.awt.image.BufferedImage;

import javax.swing.*;

@SuppressWarnings("serial")
public class TestCase {
	public static void main(String[] args) {
		JFrame frame = new JFrame("Test case");
		frame.setSize(125 << 2, 125 << 2);
		frame.setDefaultCloseOperation(WindowConstants.EXIT_ON_CLOSE);

		frame.getContentPane().add(new TestPanel());

		frame.setVisible(true);
	}

	private static class TestPanel extends JPanel {
		TestPanel() {
			setOpaque(true);
		}

		@Override
		protected void paintComponent(Graphics g) {
			int w = getWidth();
			int h = getHeight();
			BufferedImage buf = new BufferedImage(w >> 2, h >> 2, BufferedImage.TYPE_INT_ARGB);
			{
				buf.setAccelerationPriority(1F);
				Graphics2D g2 = buf.createGraphics();
				paintComponent2(g2);
				g2.dispose();
			}
			Graphics2D g2 = (Graphics2D) g;
			g2.scale(4, 4);
			g2.drawImage(buf, 0, 0, null);
			buf.flush();
		}
	
		protected void paintComponent2(Graphics g) {
			Graphics2D g2 = (Graphics2D) g;
			g2.setColor(Color.white);
			g2.fill(getBounds());

			Rectangle2D rect = new Rectangle2D.Double();
			Color background = new Color(0, 255, 255);
			Color border = new Color(0x80_FF0000, true);
			Stroke STROKE_1PX = new BasicStroke(1, CAP_SQUARE, JOIN_MITER);
			Stroke STROKE_2PX = new BasicStroke(2, CAP_SQUARE, JOIN_MITER);
			Stroke STROKE_3PX = new BasicStroke(3, CAP_SQUARE, JOIN_MITER);
			g2.translate(10, 10);

			/**
			 * Filling and stroking by original coordinates
			 */
			rect.setRect(0, 0, 25, 25);

			g2.setColor(background);
			g2.fill(rect);
			g2.setColor(border);
			g2.setStroke(STROKE_1PX);
			g2.draw(rect);
			
			g2.translate(0, 35);
			g2.setColor(background);
			g2.fill(rect);
			g2.setColor(border);
			g2.setStroke(STROKE_2PX);
			g2.draw(rect);
			
			g2.translate(0, 35);
			g2.setColor(background);
			g2.fill(rect);
			g2.setColor(border);
			g2.setStroke(STROKE_3PX);
			g2.draw(rect);

			/**
			 * Stroking is shrunk to be inside the filling rect
			 */
			g2.translate(35, -70);
			rect.setRect(0, 0, 25, 25);
			g2.setColor(background);
			g2.fill(rect);
			rect.setRect(0.5, 0.5, 24, 24);
			g2.setColor(border);
			g2.setStroke(STROKE_1PX);
			g2.draw(rect);
			g2.translate(0, 35);
			rect.setRect(0, 0, 25, 25);
			g2.setColor(background);
			g2.fill(rect);
			rect.setRect(1, 1, 23, 23);
			g2.setColor(border);
			g2.setStroke(STROKE_2PX);
			g2.draw(rect);
			g2.translate(0, 35);
			rect.setRect(0, 0, 25, 25);
			g2.setColor(background);
			g2.fill(rect);
			rect.setRect(1.5, 1.5, 22, 22);
			g2.setColor(border);
			g2.setStroke(STROKE_3PX);
			g2.draw(rect);

			/**
			 * Filling rect is additionally shrunk and centered
			 */
			g2.translate(35, -70);
			rect.setRect(0.5, 0.5, 24, 24);
			g2.setColor(background);
			g2.fill(rect);
			g2.setColor(border);
			g2.setStroke(STROKE_1PX);
			g2.draw(rect);
			g2.translate(0, 35);
			rect.setRect(1, 1, 23, 23);
			g2.setColor(background);
			g2.fill(rect);
			g2.setColor(border);
			g2.setStroke(STROKE_2PX);
			g2.draw(rect);
			g2.translate(0, 35);
			rect.setRect(1.5, 1.5, 22, 22);
			g2.setColor(background);
			g2.fill(rect);
			g2.setColor(border);
			g2.setStroke(STROKE_3PX);
			g2.draw(rect);
		}
	}

}
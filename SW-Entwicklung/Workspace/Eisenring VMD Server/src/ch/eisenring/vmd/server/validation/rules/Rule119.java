package ch.eisenring.vmd.server.validation.rules;

import ch.eisenring.core.collections.Set;
import ch.eisenring.lw.model.navigable.LWAuftrag;
import ch.eisenring.lw.model.navigable.LWProjekt;
import ch.eisenring.lw.model.navigable.LWXEtappe;
import ch.eisenring.vmd.server.VMDServer;
import ch.eisenring.vmd.server.validation.VMDRuleParameters;
import ch.eisenring.vmd.server.validation.VMDRuleValidator;
import ch.eisenring.vmd.server.validation.VMDValidationContext;

/**
 * Rule 117: Einzelküche ausserhalb Etappierung
 */
public class Rule119 extends VMDRuleValidator {

    public Rule119(final VMDServer server, final int ruleId) {
        super(server, ruleId);
    }

    @Override
    public void validateImpl(final VMDValidationContext validationContext) throws Exception {
        final Parameters parameters = (Parameters) validationContext.getParameters(this);
        final LWProjekt projekt = validationContext.getValidationProjekt();
        final Set<LWAuftrag> auftraege = projekt.getAuftraege();
        for (final LWAuftrag auftrag : auftraege) {
            int severity = 0;
            do {
                // passender auftrag?
                if (!Rule120.isMatchingAuftrag(validationContext, auftrag, parameters))
                    break;
                // noch nicht etappiert?
                final LWXEtappe etappe = Rule120.getEtappe(auftrag);
                if (etappe == null)
                    break;
                // wunschdatum in etappe?
                if (etappe.intersects(auftrag.getWunschDatum()))
                    break;
                // fehlerfirst nicht unterschritten?
                final int nDays = validationContext.getNumberOfWeekdaysFromNow(auftrag.getWunschDatum());
                if (nDays > parameters.workingDays)
                    break;
                // Bestellt?
                if (auftrag.getAuftragKopf().getKuechenBestellung() == null)
                    break;
                severity = Math.max(severity, 3);
            } while (false);
            validationContext.addResult(this, severity, auftrag, auftrag);
        }
    }

    // --------------------------------------------------------------
    // ---
    // --- Parameters
    // ---
    // --------------------------------------------------------------
    static class Parameters extends Rule120.ParameterBase {
        public final int workingDays;

        Parameters(final VMDRuleValidator rule) {
            super(rule);
            workingDays = rule.getIntParameter(2, 7);
        }
    }

    @Override
    protected VMDRuleParameters getParameters() throws Exception {
        return new Parameters(this);
    }

}


package ch.eisenring.vmd.server.validation.rules;

import ch.eisenring.core.collections.Set;
import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.logiware.code.pseudo.AbwicklungsartCode;
import ch.eisenring.lw.model.navigable.LWAuftrag;
import ch.eisenring.lw.model.navigable.LWAuftragKopf;
import ch.eisenring.lw.model.navigable.LWProjekt;
import ch.eisenring.vmd.server.VMDServer;
import ch.eisenring.vmd.server.validation.VMDRuleParameters;
import ch.eisenring.vmd.server.validation.VMDRuleValidator;
import ch.eisenring.vmd.server.validation.VMDValidationContext;

/**
 * Implements rule #126: "Bezugsdatum vor W-Datum Küche"
 */
public final class Rule126 extends VMDRuleValidator {

    public Rule126(final VMDServer server, final int ruleId) {
        super(server, ruleId);
    }

    @Override
    public void validateImpl(final VMDValidationContext validationContext) throws Exception {
        final Rule126.Parameters parameters = (Rule126.Parameters) validationContext.getParameters(this);
        final LWProjekt projekt = validationContext.getValidationProjekt();

        for (LWAuftrag auftrag : projekt.getAuftraege()) {
            if (!isAuftragRelevant(validationContext, auftrag, parameters)) {
                continue;
            }

            final LWAuftragKopf kopf = auftrag.getAuftragKopf();
            if (kopf == null) {
                continue;
            }

            if (TimestampUtil.isNull(kopf.getBezugsDatum())) {
                continue;
            }

            // -- determine severity ----------------------------------------------------------------------------------
            int severity = 0;
            if (kopf.getBezugsDatum() <= auftrag.getWunschDatum()) {
                severity = 3;
            }
            validationContext.addResult(this, severity, auftrag);
        }
    }

    private boolean isAuftragRelevant(final VMDValidationContext validationContext, LWAuftrag auftrag,
                                      Rule126.Parameters parameters) {
        if (!validationContext.isRelevant(auftrag))
            return false;

        if (!parameters.abwicklungsArten.contains(auftrag.getAbwicklungsart()))
            return false;

        return true;
    }

    // --------------------------------------------------------------
    // ---
    // --- Parameters
    // ---
    // --------------------------------------------------------------
    private final static Set<AbwicklungsartCode> ABWICKLUNGSARTEN_DEFAULT = Set.asReadonlySet(
            AbwicklungsartCode.AWA001,
            AbwicklungsartCode.AWA135
    );

    static class Parameters extends VMDRuleParameters {
        public final Set<AbwicklungsartCode> abwicklungsArten;

        Parameters(final VMDRuleValidator rule) {
            abwicklungsArten = rule.getCodeSetParameter(0, AbwicklungsartCode.class, ABWICKLUNGSARTEN_DEFAULT);
        }
    }

    @Override
    protected VMDRuleParameters getParameters() throws Exception {
        return new Rule126.Parameters(this);
    }

}

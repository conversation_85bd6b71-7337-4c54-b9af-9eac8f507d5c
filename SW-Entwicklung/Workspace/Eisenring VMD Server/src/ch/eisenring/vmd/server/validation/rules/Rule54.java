package ch.eisenring.vmd.server.validation.rules;

import ch.eisenring.core.collections.Set;
import ch.eisenring.dms.service.codetables.DMSDocumentType;
import ch.eisenring.logiware.LWProjektKey;
import ch.eisenring.logiware.code.pseudo.AbwicklungsartCode;
import ch.eisenring.logiware.code.soft.LWGranit2Code;
import ch.eisenring.lw.model.navigable.LWAuftrag;
import ch.eisenring.lw.model.navigable.LWAuftragKopf;
import ch.eisenring.lw.model.navigable.LWProjekt;
import ch.eisenring.vmd.server.VMDServer;
import ch.eisenring.vmd.server.validation.VMDRuleParameters;
import ch.eisenring.vmd.server.validation.VMDRuleValidator;
import ch.eisenring.vmd.server.validation.VMDValidationContext;

/**
 * Rule 54: "Werkvertrag für Mietobjekt nicht im DMS"
 * 
 * @See EisenringDMS://Goto/ObjectId/1519304562
 */
public class Rule54 extends VMDRuleValidator {

	public Rule54(final VMDServer server, final int ruleId) {
		super(server, ruleId);
	}

	@Override
	public void validateImpl(final VMDValidationContext validationContext) throws Exception {
		final Parameters parameters = (Parameters) validationContext.getParameters(this);
		final LWProjekt basis = validationContext.getValidationProjekt().getBasisProjekt();
		if (basis == null)
			return;
		final LWProjektKey basisKey = basis.getProjektKey();
		if (validationContext.getRuleAccel(basisKey) != null)
			return;
		LWAuftrag triggeringAuftrag = null;
		BasisLoop: for (final LWProjekt projekt : basis.getChildProjekte()) {
			for (final LWAuftrag auftrag : projekt.getAuftraege()) {
				if (!validationContext.isRelevant(auftrag))
					continue;
				if (!parameters.abwicklungsArten.contains(auftrag.getAbwicklungsart()))
					continue;
				if (!isRelevant(auftrag))
					continue ;
				final LWAuftragKopf kopf = auftrag.getAuftragKopf();
				if (kopf == null || !parameters.granitCodes.contains(kopf.getGranit2()))
					continue;
				// Check for days before WunschDatum
				final int daysToWuDat = validationContext.getNumberOfWeekdaysFromNow(auftrag.getWunschDatum());
				if (parameters.days < daysToWuDat)
					continue;
				triggeringAuftrag = auftrag;
				break BasisLoop;
			}
		}
		// Werkvertrag vorhanden?
		int severity = 0;
		if (triggeringAuftrag != null) {
			severity = hasWerkvertrag(validationContext, basisKey) ? 0 : 2;
		}
		validationContext.addResult(this, severity, basis, triggeringAuftrag == null ? basis : triggeringAuftrag);
		validationContext.putRuleAccel(basis.getPK(), severity);
	}

	protected boolean isRelevant(final LWAuftrag auftrag) {
		return true;
	}

	protected static Boolean hasWerkvertrag(final VMDValidationContext context, final LWProjektKey basisKey) {
		Object result;
		try {
			final int count = context.getDMSService()
					.countDocuments(basisKey.getProjektnummer(), DMSDocumentType.WERKVERTRAG);
			result = count > 0;
		} catch (final Exception e) {
			result = Boolean.FALSE;
		}
		return Boolean.TRUE.equals(result);
	}

	// --------------------------------------------------------------
	// ---
	// --- Parameters
	// ---
	// --------------------------------------------------------------	
	static class Parameters extends VMDRuleParameters {
		public final Set<AbwicklungsartCode> abwicklungsArten; 
		public final Set<LWGranit2Code> granitCodes;
		public final int days;

		Parameters(final VMDRuleValidator rule) {
			abwicklungsArten = rule.getCodeSetParameter(0, AbwicklungsartCode.class);
			granitCodes = rule.getCodeSetParameter(1, LWGranit2Code.class);
			days = rule.getIntParameter(2, 25);
		}
	}

	@Override
	protected VMDRuleParameters getParameters() throws Exception {
		return new Parameters(this);
	}	

}

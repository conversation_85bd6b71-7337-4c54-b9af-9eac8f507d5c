package ch.eisenring.vmd.server.network;

import static ch.eisenring.vmd.server.dao.VMDMapping.TM_RESULT;

import java.sql.SQLException;
import java.util.Iterator;

import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.jdbc.StatementWrapper;
import ch.eisenring.model.Model;
import ch.eisenring.model.TransactionContext;
import ch.eisenring.network.PacketSink;
import ch.eisenring.network.packet.AbstractPacket;
import ch.eisenring.vmd.server.VMDServer;
import ch.eisenring.vmd.server.dao.VMDContextSink;
import ch.eisenring.vmd.shared.codetables.VMDResultStatusType;
import ch.eisenring.vmd.shared.model.meta.VMDRuleResultMeta;
import ch.eisenring.vmd.shared.network.PacketCloseResultReply;
import ch.eisenring.vmd.shared.network.PacketCloseResultRequest;
import ch.eisenring.vmd.shared.network.PacketProblemCountChangeNotification;

final class CloseResultRequestHandler extends AbstractVMDPacketHandler {

	private CloseResultRequestHandler(final VMDServer server) {
		super(server, PacketCloseResultRequest.class);
	}

	@Override
	public void handle(final AbstractPacket packet, final PacketSink sink) {
		final PacketCloseResultRequest request = (PacketCloseResultRequest) packet;
		final PacketCloseResultReply reply = handle(server, request);
		sink.sendPacket(reply, false);
		
		// notify every client results may have changed
		if (reply.isValid()) {
			final PacketProblemCountChangeNotification broadcast = PacketProblemCountChangeNotification.create();
			broadcast.setEverythingChanged();
			server.broadcast(broadcast, false);
		}
	}

	static PacketCloseResultReply handle(final VMDServer server, final PacketCloseResultRequest request) {
		try {
			final PacketCloseResultReply reply = handleImpl(server, request);
			if (reply != null)
				return reply;
			return PacketCloseResultReply.create(request, ErrorMessage.ERROR);
		} catch (final Exception e) {
			return PacketCloseResultReply.create(request, new ErrorMessage(e));
		}
	}

	static PacketCloseResultReply handleImpl(final VMDServer server, final PacketCloseResultRequest request) throws Exception {
		final VMDContextSink sink = new VMDContextSink(server) {
			@Override
			protected void storeImpl(final Iterator<Model> modelItr, final TransactionContext context) throws SQLException {
				final StringMaker sql = StringMaker.obtain();
				sql.append("UPDATE ");
				qualifyTableName(sql, TM_RESULT.getTableSpecifier());
				sql.append(" SET ");
				sql.append(TM_RESULT.getColumn(VMDRuleResultMeta.ATR_STATUS));
				sql.append(" = ?, ");
				sql.append(TM_RESULT.getColumn(VMDRuleResultMeta.ATR_CLOSEDBY));
				sql.append(" = ?, ");
				sql.append(TM_RESULT.getColumn(VMDRuleResultMeta.ATR_CLOSEDON));
				sql.append(" = ? WHERE ");
				sql.append(TM_RESULT.getPrimaryKeyColumn());
				sql.append(" = ?");
				final VMDResultStatusType resultStatus = request.getResultStatus();
				final long lClosedOn = request.getClosedOn();
				final java.sql.Timestamp closedOn = TimestampUtil.isNull(lClosedOn) ? null : new java.sql.Timestamp(lClosedOn);
				final String closedBy = request.getClosedBy();
				final StatementWrapper update = prepareStatement(sql.release());
				try {
					for (final Long rowId : request.getResultRowIds()) {
						update.setObject(1, resultStatus);
						update.setObject(2, closedBy);
						update.setObject(3, closedOn);
						update.setObject(4, rowId);
						update.executeUpdate();
					}
				} finally {
					closeSilent(update);
				}
			}
		};
		final TransactionContext context = new TransactionContext();
		context.store(sink);
		return PacketCloseResultReply.create(request, ErrorMessage.OK);
	}

}

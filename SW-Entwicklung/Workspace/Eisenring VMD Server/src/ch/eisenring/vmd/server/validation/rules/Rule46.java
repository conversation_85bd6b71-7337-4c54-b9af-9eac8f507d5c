package ch.eisenring.vmd.server.validation.rules;

import ch.eisenring.core.collections.Set;
import ch.eisenring.dms.service.DMSService;
import ch.eisenring.dms.service.codetables.DMSDocumentType;
import ch.eisenring.logiware.code.pseudo.AbwicklungsartCode;
import ch.eisenring.logiware.code.soft.LWGranit2Code;
import ch.eisenring.lw.model.navigable.LWAuftrag;
import ch.eisenring.lw.model.navigable.LWAuftragKopf;
import ch.eisenring.lw.model.navigable.LWObject;
import ch.eisenring.lw.model.navigable.LWProjekt;
import ch.eisenring.vmd.server.VMDServer;
import ch.eisenring.vmd.server.validation.VMDRuleParameters;
import ch.eisenring.vmd.server.validation.VMDRuleValidator;
import ch.eisenring.vmd.server.validation.VMDValidationContext;

/**
 * Rule 46: "Wohnungsübersicht nicht im DMS vorhanden"
 *
 * @See EisenringDMS://Goto/ObjectId/1507341991
 */
public class Rule46 extends VMDRuleValidator {

	public Rule46(final VMDServer server, final int ruleId) {
		super(server, ruleId);
	}

	@Override
	public void validateImpl(final VMDValidationContext validationContext) throws Exception {
		final DMSService service = validationContext.getDMSService();
		final Parameters parameters = (Parameters) validationContext.getParameters(this);
		final LWProjekt projekt = validationContext.getValidationProjekt();

		final LWProjekt basis = projekt.getBasisProjekt();
		int severity = 0;
		LWObject ownerSource = null;
		if (projekt.equals(basis)) {
			final Set<LWProjekt> children = projekt.getChildProjekteRecursive();
			// check each child if it contains order type we're looking for
			boolean isMiet = false;
			for (final LWProjekt child : children) {
				for (final LWAuftrag auftrag : child.getAuftraege()) {
					if (!validationContext.isRelevant(auftrag))
						continue;
					if (!parameters.abwicklungsArten.contains(auftrag.getAbwicklungsart()))
						continue;
					// ist mietobjekt?
					final LWAuftragKopf kopf = auftrag.getAuftragKopf();
					if (kopf == null)
						continue;
					final LWGranit2Code code = kopf.getGranit2();
					if (LWGranit2Code.MIET.equals(code)) {
						isMiet = true;
						ownerSource = LWObject.maxAddDate(ownerSource, auftrag);
					}
				}
			}
			if (isMiet) {
				// Hausübersicht vorhanden?
				try {
					int count = service.countDocuments(projekt.getProjektnummer(), DMSDocumentType.HAUSUEBERSICHT);
					if (count <= 0)
						severity = 2;
				} catch (final Exception e) {
					severity = 2;
				}				
			}
		}
		validationContext.addResult(this, severity, projekt, ownerSource);
	}

	// --------------------------------------------------------------
	// ---
	// --- Parameters
	// ---
	// --------------------------------------------------------------	
	private final static Set<AbwicklungsartCode> ABWICKLUNGSARTEN_DEFAULT = Set.asReadonlySet(
			AbwicklungsartCode.AWA135
	);

	static class Parameters extends VMDRuleParameters {
		public final Set<AbwicklungsartCode> abwicklungsArten; 

		Parameters(final VMDRuleValidator rule) {
			abwicklungsArten = rule.getCodeSetParameter(0, AbwicklungsartCode.class, ABWICKLUNGSARTEN_DEFAULT);
		}
	}

	@Override
	protected VMDRuleParameters getParameters() throws Exception {
		return new Parameters(this);
	}	

}

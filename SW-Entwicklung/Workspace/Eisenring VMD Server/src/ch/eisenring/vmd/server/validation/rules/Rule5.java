package ch.eisenring.vmd.server.validation.rules;

import ch.eisenring.core.collections.Set;
import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.dms.service.codetables.DMSFolderType;
import ch.eisenring.logiware.code.pseudo.AbwicklungsartCode;
import ch.eisenring.lw.model.navigable.LWAuftrag;
import ch.eisenring.lw.model.navigable.LWProjekt;
import ch.eisenring.vmd.server.VMDServer;
import ch.eisenring.vmd.server.validation.VMDRuleParameters;
import ch.eisenring.vmd.server.validation.VMDRuleValidator;
import ch.eisenring.vmd.server.validation.VMDValidationContext;

/**
 * Implements rule #5: "Keine Ausführungspläne im DMS"
 * 
 * Specified by: BADA, 2013-09-20
 */
public class Rule5 extends VMDRuleValidator {

	public Rule5(final VMDServer server, final int ruleId) {
		super(server, ruleId);
	}

	@Override
	public void validateImpl(final VMDValidationContext validationContext) throws Exception {
		final Parameters parameters = (Parameters) validationContext.getParameters(this);
		final LWProjekt projekt = validationContext.getValidationProjekt();
		Object hasPlaene = null;
		for (final LWAuftrag auftrag : projekt.getAuftraege()) {
			int severity = 0;
			do {
				if (!validationContext.isRelevant(auftrag))
					break;
				if (!parameters.abwicklungsArten.contains(auftrag.getAbwicklungsart()))
					break;
				final long wunschDatum = auftrag.getWunschDatum();
				if (TimestampUtil.isNull(wunschDatum))
					break;
				final int delta = validationContext.getNumberOfWeekdaysFromNow(wunschDatum);
				// wenn mehr als 3 wochen in der zukunft, ignorieren
				if (delta > parameters.deltaDays)
					break;
				// wenn mehr als 60 tage nach ausführung, ignorieren
				if (delta < -60)
					break;
				// ausführungspläne vorhanden?
				if (hasPlaene == null) {
					hasPlaene = validationContext.getDMSService().countFolderContents(
							projekt.getProjektnummer(), DMSFolderType.AUSFUEHRUNGSPLAENE) > 0;
				}
				if (!Boolean.TRUE.equals(hasPlaene))
					severity = Math.max(severity, 2);
			} while (false);
			validationContext.addResult(this, severity, auftrag, auftrag);
		}
	}

	// --------------------------------------------------------------
	// ---
	// --- Parameters
	// ---
	// --------------------------------------------------------------	
	private final static Set<AbwicklungsartCode> ABWICKLUNGSARTEN_DEFAULT = Set.asReadonlySet(
			AbwicklungsartCode.AWA135
	);

	static class Parameters extends VMDRuleParameters {
		public final Set<AbwicklungsartCode> abwicklungsArten;  
		public final int deltaDays;

		Parameters(final Rule5 rule) {
			abwicklungsArten = rule.getCodeSetParameter(1, AbwicklungsartCode.class, ABWICKLUNGSARTEN_DEFAULT);
			deltaDays = rule.getIntParameter(0, 15);
		}
	}

	@Override
	protected VMDRuleParameters getParameters() throws Exception {
		return new Parameters(this);
	}

}

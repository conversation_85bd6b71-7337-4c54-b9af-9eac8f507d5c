package ch.eisenring.vmd.server.validation.rules;

import ch.eisenring.core.collections.Set;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.logiware.LWProjektKey;
import ch.eisenring.logiware.code.pseudo.AbwicklungsartCode;
import ch.eisenring.logiware.code.soft.LWKuechenTypCode;
import ch.eisenring.lw.model.navigable.LWAuftrag;
import ch.eisenring.lw.model.navigable.LWAuftragKopf;
import ch.eisenring.lw.model.navigable.LWProjekt;
import ch.eisenring.vmd.server.VMDServer;
import ch.eisenring.vmd.server.validation.VMDRuleParameters;
import ch.eisenring.vmd.server.validation.VMDRuleValidator;
import ch.eisenring.vmd.server.validation.VMDValidationContext;

/**
 * Rule 59: "Küchentyp bei Waschmaschinen-  Waschturmauftrag nicht definiert"
 *
 * @See: EisenringDMS://Goto/ObjectId/1519310785
 */
public final class Rule59 extends VMDRuleValidator {

	public Rule59(final VMDServer server, final int ruleId) {
		super(server, ruleId);
	}

	@Override
	public void validateImpl(final VMDValidationContext validationContext) throws Exception {
		final Parameters parameters = (Parameters) validationContext.getParameters(this);
		final LWProjekt projekt = validationContext.getValidationProjekt();
		for (final LWAuftrag auftrag : projekt.getAuftraege()) {
			int severity = 0;
			do {
				if (!validationContext.isRelevant(auftrag))
					break;
				if (!parameters.abwicklungsArten.contains(auftrag.getAbwicklungsart()))
					break;
				final LWProjektKey key = (LWProjektKey) projekt.getPK();
				final String extension = LWProjektKey.getProjektnummerExtension(key.getProjektnummer());
				if (!parameters.extensionNumbers.contains(extension))
					break;
				final LWAuftragKopf kopf = auftrag.getAuftragKopf();
				if (kopf == null)
					break;
				if (parameters.kuechenTypen.contains(kopf.getKuechenTyp()))
					break;
				severity = Math.max(severity, 1);
			} while (false);
			validationContext.addResult(this, severity, auftrag, auftrag);
		}
	}

	// --------------------------------------------------------------
	// ---
	// --- Parameters
	// ---
	// --------------------------------------------------------------	
	static class Parameters extends VMDRuleParameters {
		public final Set<AbwicklungsartCode> abwicklungsArten;  
		public final Set<LWKuechenTypCode> kuechenTypen;
		public final Set<String> extensionNumbers;
		
		Parameters(final Rule59 rule) {
			abwicklungsArten = rule.getCodeSetParameter(0, AbwicklungsartCode.class);
			kuechenTypen = rule.getCodeSetParameter(1, LWKuechenTypCode.class);
			String param2 = rule.getStringParameter(2, "");
			String[] exploded = Strings.explode(param2, ',');
			extensionNumbers = Set.asReadonlySet(exploded);
		}
	}

	@Override
	protected VMDRuleParameters getParameters() throws Exception {
		return new Parameters(this);
	}

}

package ch.eisenring.vmd.server.validation.rules;

import ch.eisenring.dms.service.codetables.DMSDocumentType;
import ch.eisenring.vmd.server.VMDServer;
import ch.eisenring.vmd.server.validation.VMDValidationContext;

/**
 * Rule 81: "Kein Krok<PERSON> für Einzelküche abgelegt"
 *
 * @See EisenringDMS://Goto/ObjectId/1507580491
 */
public class Rule81 extends Rule80 {

	public Rule81(final VMDServer server, final int ruleId) {
		super(server, ruleId);
	}

	@Override
	public void validateImpl(final VMDValidationContext validationContext) throws Exception {
		validateImpl(validationContext, DMSDocumentType.KROKI);
	}

}

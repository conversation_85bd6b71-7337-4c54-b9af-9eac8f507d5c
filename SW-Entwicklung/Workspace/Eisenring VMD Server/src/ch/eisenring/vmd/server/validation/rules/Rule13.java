package ch.eisenring.vmd.server.validation.rules;

import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.collections.Collection;
import ch.eisenring.core.collections.Set;
import ch.eisenring.logiware.code.pseudo.AbwicklungsartCode;
import ch.eisenring.logiware.code.soft.LWGranit2Code;
import ch.eisenring.lw.model.navigable.LWAuftrag;
import ch.eisenring.lw.model.navigable.LWAuftragKopf;
import ch.eisenring.lw.model.navigable.LWObject;
import ch.eisenring.lw.model.navigable.LWProjekt;
import ch.eisenring.lw.subjekt.LWSubjektSpecial;
import ch.eisenring.vmd.server.VMDServer;
import ch.eisenring.vmd.server.validation.VMDRuleParameters;
import ch.eisenring.vmd.server.validation.VMDRuleValidator;
import ch.eisenring.vmd.server.validation.VMDValidationContext;

/**
 * Implements rule #12: "Einhaltung Frist Erstellung 135'er für 001'er"
 * 
 * Specified by: BADA, 2013-11-27
 */
public final class Rule13 extends VMDRuleValidator {

	public Rule13(final VMDServer server, final int ruleId) {
		super(server, ruleId);
	}

	@Override
	public void validateImpl(final VMDValidationContext validationContext) throws Exception {
		final Parameters parameters = (Parameters) validationContext.getParameters(this);
		final LWProjekt projekt = validationContext.getValidationProjekt();
		final Collection<LWAuftrag> auftraege = projekt.getAuftraege();
		final boolean isVerrechenbar = LWSubjektSpecial.getSpecialInfo(projekt.getSubjektAG()).isVerrechenbarerKunde();
		LWObject ownerSource = null;
		boolean isEinzel = false;
		for (final LWAuftrag auftrag : auftraege) {
			if (!validationContext.isRelevant(auftrag))
				continue;
			if (!parameters.abwicklungsArten.contains(auftrag.getAbwicklungsart()))
				continue;
			final LWAuftragKopf kopf = auftrag.getAuftragKopf();
			if (kopf == null)
				continue;
			final LWGranit2Code g2Code = kopf.getGranit2();
			if (AbstractCode.isNull(g2Code))
				continue;
			if (g2Code.isEinzelkueche()) {
				isEinzel = true;
				ownerSource = LWObject.maxAddDate(ownerSource, auftrag);
			}
		}
		int severity = 0;
		if (isEinzel && isVerrechenbar) {
			final Double summe = projekt.getAuftragsSumme();
			if (!isPresent(summe))
				severity = 2;
		}
		validationContext.addResult(this, severity, projekt, ownerSource);
	}

	// --------------------------------------------------------------
	// ---
	// --- Parameters
	// ---
	// --------------------------------------------------------------	
	private final static Set<AbwicklungsartCode> ABWICKLUNGSARTEN_DEFAULT = Set.asReadonlySet(
			AbwicklungsartCode.AWA134, AbwicklungsartCode.AWA135
	);

	static class Parameters extends VMDRuleParameters {
		public final Set<AbwicklungsartCode> abwicklungsArten;  

		Parameters(final Rule13 rule) {
			abwicklungsArten = rule.getCodeSetParameter(0, AbwicklungsartCode.class, ABWICKLUNGSARTEN_DEFAULT);
		}
	}

	@Override
	protected VMDRuleParameters getParameters() throws Exception {
		return new Parameters(this);
	}

}

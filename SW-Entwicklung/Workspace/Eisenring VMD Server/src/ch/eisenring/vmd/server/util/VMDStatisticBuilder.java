package ch.eisenring.vmd.server.util;

import static ch.eisenring.vmd.server.dao.VMDMapping.OM_DAYSTATISTIC;
import static ch.eisenring.vmd.server.dao.VMDMapping.OM_RULESTATISTIC;
import static ch.eisenring.vmd.server.dao.VMDMapping.TM_RESULT;

import java.io.IOException;
import java.sql.SQLException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Calendar;
import java.util.Iterator;
import java.util.concurrent.atomic.AtomicLong;

import ch.eisenring.app.shared.CoreTickListenerAdapter;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.datatypes.date.DateGranularityCode;
import ch.eisenring.core.datatypes.date.HEAGCalendar;
import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.jdbc.JDBCResultSet;
import ch.eisenring.jdbc.RowHandler;
import ch.eisenring.jdbc.StatementParameters;
import ch.eisenring.model.Model;
import ch.eisenring.model.TransactionContext;
import ch.eisenring.vmd.server.VMDServer;
import ch.eisenring.vmd.server.dao.VMDContextSink;
import ch.eisenring.vmd.shared.codetables.VMDResultStatusType;
import ch.eisenring.vmd.shared.model.meta.VMDRuleResultMeta;
import ch.eisenring.vmd.shared.model.pojo.VMDDayStatistic;
import ch.eisenring.vmd.shared.model.pojo.VMDDayStatisticRule;

public final class VMDStatisticBuilder extends CoreTickListenerAdapter {

	private final AtomicLong runNext = new AtomicLong(getNextActivation());

	private final VMDServer server;

	public VMDStatisticBuilder(final VMDServer server) {
		super(server);
		this.server = server;
	}

	// --------------------------------------------------------------
	// ---
	// --- Batch activation management
	// ---
	// --------------------------------------------------------------
	@Override
	protected boolean isDue(final long now) {
		final long time = runNext.get();
		if (TimestampUtil.compare(now, time) < 0)
			return false;
		return runNext.compareAndSet(time, getNextActivation());
	}
	
	/**
	 * Gets the timestamp for the next batch run
	 */
	private static long getNextActivation() {
		LocalDateTime now = LocalDateTime.now();
        LocalDateTime todayAt22Hours = LocalDateTime.now().with(LocalTime.of(22, 0));
		final HEAGCalendar calendar= HEAGCalendar.obtainNow();
		calendar.setTime(22, 0, 0, 0); // 22:00:00.000
		if (!now.isBefore(todayAt22Hours)) {
			calendar.add(Calendar.DAY_OF_YEAR, 1);
		} final long result = calendar.release().getTimeInMillis();
		Logger.info(Strings.concat("Next Statistics run: ", TimestampUtil.DATETIME.format(result)));
		return result;
	}

	// --------------------------------------------------------------
	// ---
	// --- Validation process
	// ---
	// --------------------------------------------------------------
	private final Runnable batchTask = new Runnable() {
		@Override
		public void run() {
			Logger.info("Statistic USER update started");
			try {
				doStatisticUserBatch();
			} finally {
				Logger.info("Statistic USER update ended");
			}
			Logger.info("Statistic RULE update started");
			try {
				doStatisticRuleBatch();
			} finally {
				Logger.info("Statistic RULE update ended");
			}
		}
	};

	void doStatisticUserBatch() {
		final List<VMDDayStatistic> dataList = new ArrayList<>();
		final long now = DateGranularityCode.DAY.round(System.currentTimeMillis(), 0);
		final VMDContextSink sink = new VMDContextSink(server) {
			@Override
			protected void storeImpl(final Iterator<Model> modelItr, final TransactionContext context) throws SQLException {
				// select data for statistics
				final StringMaker select = StringMaker.obtain();
				select.append("SELECT ");
				select.append(TM_RESULT.getColumn(VMDRuleResultMeta.ATR_PROBLEMOWNER));
				select.append(", ");
				select.append("COUNT(*) FROM ");
				select.append(TM_RESULT.getTableSpecifier());
				select.append(" WHERE ");
				select.append(TM_RESULT.getColumn(VMDRuleResultMeta.ATR_STATUS));
				select.append(" = ? GROUP BY ");
				select.append(TM_RESULT.getColumn(VMDRuleResultMeta.ATR_PROBLEMOWNER));
				final StatementParameters params = new StatementParameters(1);
				params.add(VMDResultStatusType.OPEN);
				doQuery(select.release(), params, new RowHandler() {
					@Override
					public void handleRow(final JDBCResultSet resultSet) throws SQLException {
						final String problemOwner = resultSet.getString(1);
						final long problemCount = resultSet.getLong(2);
						final VMDDayStatistic data = VMDDayStatistic.create(now, problemOwner, problemCount);
						dataList.add(data);
					}
				});
				// store the collected data
				OM_DAYSTATISTIC.insert(this, dataList);
			}
		};
		try {
			final TransactionContext context = new TransactionContext();
			context.store(sink);
		} catch (final IOException e) {
			Logger.error(e);
		}
	}

	void doStatisticRuleBatch() {
		final List<VMDDayStatisticRule> dataList = new ArrayList<>();
		final long now = DateGranularityCode.DAY.round(System.currentTimeMillis(), 0);
		final VMDContextSink sink = new VMDContextSink(server) {
			@Override
			protected void storeImpl(final Iterator<Model> modelItr, final TransactionContext context) throws SQLException {
				// select data for statistics
				final StringMaker select = StringMaker.obtain();
				select.append("SELECT ");
				select.append(TM_RESULT.getColumn(VMDRuleResultMeta.ATR_RUELID));
				select.append(", ");
				select.append("COUNT(*) FROM ");
				select.append(TM_RESULT.getTableSpecifier());
				select.append(" WHERE ");
				select.append(TM_RESULT.getColumn(VMDRuleResultMeta.ATR_STATUS));
				select.append(" = ? GROUP BY ");
				select.append(TM_RESULT.getColumn(VMDRuleResultMeta.ATR_RUELID));
				final StatementParameters params = new StatementParameters(1);
				params.add(VMDResultStatusType.OPEN);
				doQuery(select.release(), params, new RowHandler() {
					@Override
					public void handleRow(final JDBCResultSet resultSet) throws SQLException {
						final int ruleId = resultSet.getInt(1);
						final long problemCount = resultSet.getLong(2);
						final VMDDayStatisticRule data = VMDDayStatisticRule.create(now, ruleId, problemCount);
						dataList.add(data);
					}
				});
				// store the collected data
				OM_RULESTATISTIC.insert(this, dataList);
			}
		};
		try {
			final TransactionContext context = new TransactionContext();
			context.store(sink);
		} catch (final IOException e) {
			Logger.error(e);
		}
	}

	// --------------------------------------------------------------
	// ---
	// --- CoreTickListener implementation
	// ---
	// --------------------------------------------------------------
	@Override
	protected void tickImpl(final long now) {
		server.getThreadPool().start(batchTask,
				Strings.concat(getName(), "-Runner"), Thread.MIN_PRIORITY + 2);
	}

}

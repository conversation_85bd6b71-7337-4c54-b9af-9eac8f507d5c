package ch.eisenring.vmd.server.validation.rules;

import ch.eisenring.core.collections.Set;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.logiware.code.pseudo.AbwicklungsartCode;
import ch.eisenring.logiware.code.soft.LWKuechenTypCode;
import ch.eisenring.lw.model.navigable.LWAuftrag;
import ch.eisenring.lw.model.navigable.LWAuftragKopf;
import ch.eisenring.lw.model.navigable.LWProjekt;
import ch.eisenring.vmd.server.VMDServer;
import ch.eisenring.vmd.server.data.VMDServerData;
import ch.eisenring.vmd.server.validation.VMDRuleParameters;
import ch.eisenring.vmd.server.validation.VMDRuleValidator;
import ch.eisenring.vmd.server.validation.VMDValidationContext;

/**
 * Rule 101: "Ke<PERSON>-Set für VE Leitersockel erfasst"
 *
 * @See EisenringDMS://Goto/ObjectId/1644902775
 */
public class Rule101 extends VMDRuleValidator {

	public Rule101(final VMDServer server, final int ruleId) {
		super(server, ruleId);
	}

	@Override
	public void validateImpl(final VMDValidationContext validationContext) throws Exception {
		final Parameters parameters = (Parameters) validationContext.getParameters(this);
		final LWProjekt projekt = validationContext.getValidationProjekt();
		for (final LWAuftrag auftrag : projekt.getAuftraege()) {
			int severity = 0;
			do {
				if (!validationContext.isRelevant(auftrag))
					break;
				if (!parameters.abwicklungsArten.contains(auftrag.getAbwicklungsart()))
					break;
				final LWAuftragKopf kopf = auftrag.getAuftragKopf();
				if (kopf == null)
					break;
				if (!parameters.kuechenTypen.contains(kopf.getKuechenTyp()))
					break;
				// contains deckblatt text?
				if (!hasDeckblattText(auftrag, parameters.text))
					break;
				// has schallschutz position?
				if (hasProductPosition(auftrag, parameters.productNumbers))
					break;
				severity = Math.max(severity, 2);
			} while (false);
			validationContext.addResult(this, severity, auftrag, auftrag);
		}
	}

	static boolean hasDeckblattText(final LWAuftrag auftrag, final String text) {
		final LWAuftragKopf kopf = auftrag.getAuftragKopf();
		if (kopf == null || Strings.isEmpty(text))
			return false;
		for (int i = LWAuftragKopf.DECKBLATT_INDEX_FIRST; i <= LWAuftragKopf.DECKBLATT_INDEX_LAST; ++i) {
			final String s = kopf.getDeckblatt(i);
			if (s != null && Strings.contains(s, text))
				return true;
		}
		return false;
	}

	// --------------------------------------------------------------
	// ---
	// --- Parameters
	// ---
	// --------------------------------------------------------------
	static class Parameters extends VMDRuleParameters {
		public final Set<AbwicklungsartCode> abwicklungsArten;
		public final Set<LWKuechenTypCode> kuechenTypen; 
		public final String text;
		public final Set<String> productNumbers;

		Parameters(final VMDRuleValidator rule) {
			this.abwicklungsArten = rule.getCodeSetParameter(0, AbwicklungsartCode.class);
			this.kuechenTypen = rule.getCodeSetParameter(1, LWKuechenTypCode.class);
			this.text = rule.getStringParameter(2, "T1-OFS,OSF");
			this.productNumbers = rule.getStringSetParameter(3);
		}
	}

	@Override
	protected VMDRuleParameters getParameters() throws Exception {
		return new Parameters(this);
	}	

}

package ch.eisenring.vmd.server.validation.rules;

import ch.eisenring.lw.model.navigable.LWAuftrag;
import ch.eisenring.lw.model.navigable.LWObject;
import ch.eisenring.lw.model.navigable.LWProjekt;
import ch.eisenring.vmd.server.VMDServer;
import ch.eisenring.vmd.server.validation.VMDRuleValidator;
import ch.eisenring.vmd.server.validation.VMDValidationContext;

/**
 * Implements rule #139: "Ungültige Zeichen in Kommissionsnummer vorhanden"
 */
public class Rule139 extends VMDRuleValidator {

	public static final String VALID_REGEX = "^[1-9]\\d*(-[0-9a-zA-Z]+((?<![a-zA-Z])([/])?([0-9])+)*)*$";

	public Rule139(final VMDServer server, final int ruleId) {
		super(server, ruleId);
	}

	@Override
	public void validateImpl(final VMDValidationContext validationContext) throws Exception {
		final LWProjekt projekt = validationContext.getValidationProjekt();
		final String projektNummer = projekt.getProjektnummer();

		LWObject ownerSource = null;
		for (final LWAuftrag auftrag : projekt.getAuftraege()) {
			do {
				if (!validationContext.isRelevant(auftrag))
					break;
				ownerSource = LWObject.maxAddDate(ownerSource, auftrag);
			} while (false);
		}
		boolean containsInvalidCharacters = false;
		int severity = 0;
		if (null != ownerSource) {
			if (!projektNummer.matches(VALID_REGEX)) {
				containsInvalidCharacters = true;
			}

			if (containsInvalidCharacters) {
				severity = Math.max(severity, 3);
			}
		}

		validationContext.addResult(this, severity, projekt, ownerSource);
	}

}

package ch.eisenring.vmd.server.validation.rules;

import ch.eisenring.core.collections.Set;
import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.logiware.code.hard.LWPlanungStatusCode;
import ch.eisenring.logiware.code.pseudo.AbwicklungsartCode;
import ch.eisenring.lw.model.navigable.LWAuftrag;
import ch.eisenring.lw.model.navigable.LWAuftragKopf;
import ch.eisenring.lw.model.navigable.LWProjekt;
import ch.eisenring.vmd.server.VMDServer;
import ch.eisenring.vmd.server.validation.VMDRuleParameters;
import ch.eisenring.vmd.server.validation.VMDRuleValidator;
import ch.eisenring.vmd.server.validation.VMDValidationContext;

/**
 * Rule 53: "Auftrag nicht abgeschlossen"
 * 
 * @See EisenringDMS://Goto/ObjectId/1516006597
 */
public class Rule53 extends VMDRuleValidator {

	public Rule53(final VMDServer server, final int ruleId) {
		super(server, ruleId);
	}

	@Override
	public void validateImpl(final VMDValidationContext validationContext) throws Exception {
		final Parameters parameters = (Parameters) validationContext.getParameters(this);
		final LWProjekt projekt = validationContext.getValidationProjekt();
		for (final LWAuftrag auftrag : projekt.getAuftraege()) {
			int severity = 0;
			do {
				if (!validationContext.isRelevant(auftrag))
					continue;
				if (!parameters.abwicklungsArten.contains(auftrag.getAbwicklungsart()))
					break;
				final LWAuftragKopf kopf = auftrag.getAuftragKopf();
				if (kopf == null)
					break;
				// kein fehler wenn schon abgeschlossen
				if (parameters.planungStati.contains(kopf.getPlanungStatus()))
					break;
				final long abschlussDatum = kopf.getAbschlussDatum();
				if (TimestampUtil.isNull(abschlussDatum))
					break;
				// ok wenn frist nicht überschritten
				final int delta = -validationContext.getNumberOfWeekdaysFromNow(abschlussDatum);
				if (delta <= parameters.maxDays)
					break;
				severity = 1;
			} while (false);
			validationContext.addResult(this, severity, auftrag, auftrag);
		}
	}

	// --------------------------------------------------------------
	// ---
	// --- Parameters
	// ---
	// --------------------------------------------------------------	
	private final static Set<AbwicklungsartCode> ABWICKLUNGSARTEN_DEFAULT = Set.asReadonlySet(
			AbwicklungsartCode.AWA134, AbwicklungsartCode.AWA135
	);

	private final static Set<LWPlanungStatusCode> PLANUNG_STATI_DEFAULT = Set.asReadonlySet(
			LWPlanungStatusCode.ABGESCHLOSSEN
	);

	static class Parameters extends VMDRuleParameters {
		public final int maxDays; 
		public final Set<AbwicklungsartCode> abwicklungsArten;
		public final Set<LWPlanungStatusCode> planungStati;

		Parameters(final VMDRuleValidator rule) {
			abwicklungsArten = rule.getCodeSetParameter(0, AbwicklungsartCode.class, ABWICKLUNGSARTEN_DEFAULT);
			maxDays = rule.getIntParameter(1, 3);
			planungStati = rule.getCodeSetParameter(2, LWPlanungStatusCode.class, PLANUNG_STATI_DEFAULT);
		}
	}

	@Override
	protected VMDRuleParameters getParameters() throws Exception {
		return new Parameters(this);
	}

}

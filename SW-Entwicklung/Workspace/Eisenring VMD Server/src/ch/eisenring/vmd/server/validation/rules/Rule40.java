package ch.eisenring.vmd.server.validation.rules;

import ch.eisenring.logiware.code.pseudo.AbwicklungsartCode;
import ch.eisenring.lw.model.navigable.LWAuftrag;
import ch.eisenring.lw.model.navigable.LWObject;
import ch.eisenring.lw.model.navigable.LWProjekt;
import ch.eisenring.vmd.server.VMDServer;
import ch.eisenring.vmd.server.validation.VMDRuleParameters;
import ch.eisenring.vmd.server.validation.VMDRuleValidator;
import ch.eisenring.vmd.server.validation.VMDValidationContext;

/**
 * Rule 40: "Mehr als 1 ObjZusammenstellung-A vorhanden"
 * 
 * @See EisenringDMS://Goto/ObjectId/1507580497
 */
public class Rule40 extends VMDRuleValidator {

	public Rule40(final VMDServer server, final int ruleId) {
		super(server, ruleId);
	}

	@Override
	public void validateImpl(final VMDValidationContext validationContext) throws Exception {
		final LWProjekt projekt = validationContext.getValidationProjekt();
		int count = 0;
		LWObject errorSource = null;
		for (final LWAuftrag auftrag : projekt.getAuftraege()) {
			if (auftrag.isStorniert())
				continue;
			final AbwicklungsartCode awa = auftrag.getAbwicklungsart();
			if (!AbwicklungsartCode.AWA173.equals(awa))
				continue;
			++count;
			errorSource = LWObject.maxAddDate(errorSource,  auftrag);
		}
		
		final int severity = count > 1 ? 2 : 0;
		
		for (final LWAuftrag auftrag : projekt.getAuftraege()) {
			if (auftrag.equals(errorSource)) {
				validationContext.addResult(this, severity, auftrag, auftrag);
			} else {
				validationContext.addResult(this, 0, auftrag, auftrag);
			}
		}
	}

	// --------------------------------------------------------------
	// ---
	// --- Parameters
	// ---
	// --------------------------------------------------------------	
	static class Parameters extends VMDRuleParameters {
		Parameters(final VMDRuleValidator rule) {
		}
	}

	@Override
	protected VMDRuleParameters getParameters() throws Exception {
		return new Parameters(this);
	}	

}

package ch.eisenring.vmd.server.validation.rules;

import ch.eisenring.core.collections.Set;
import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.logiware.code.pseudo.AbwicklungsartCode;
import ch.eisenring.logiware.code.soft.LWGranit1Code;
import ch.eisenring.logiware.code.soft.LWGranit2Code;
import ch.eisenring.lw.condition.Factory;
import ch.eisenring.lw.meta.LWProjektMeta;
import ch.eisenring.lw.model.navigable.LWAuftrag;
import ch.eisenring.lw.model.navigable.LWAuftragKopf;
import ch.eisenring.lw.model.navigable.LWProjekt;
import ch.eisenring.vmd.server.VMDServer;
import ch.eisenring.vmd.server.validation.VMDRuleParameters;
import ch.eisenring.vmd.server.validation.VMDRuleValidator;
import ch.eisenring.vmd.server.validation.VMDValidationContext;

/**
 * Implements rule #133: "WDatum Waschturm Miet bewohnt nicht in Zeitfenster"
 */
public class Rule133 extends VMDRuleValidator {

    public Rule133(final VMDServer server, final int ruleId) {
        super(server, ruleId);
    }

    @Override
    public void validateImpl(final VMDValidationContext validationContext) throws Exception {
        final Rule133.Parameters parameters = (Rule133.Parameters) validationContext.getParameters(this);
        final LWProjekt projekt = validationContext.getValidationProjekt();
        final long now = validationContext.getNow();

        if (projekt.getProjektnummer().contains("-")) {
            String basisCandidateProjektnummer =
                    projekt.getProjektnummer().substring(0, projekt.getProjektnummer().indexOf("-"));

            LWProjekt basisCandidate = projekt.getCache().loadSingle(LWProjekt.class,
                    Factory.eq(LWProjektMeta.ATR_PROJEKTNUMMER, basisCandidateProjektnummer),
                    Factory.eq(LWProjektMeta.ATR_GSE_PROJEKT, projekt.getPK().getGSE()));

            if (!(basisCandidate == null) && basisCandidate.isBasisprojekt()) {
                for (LWAuftrag auftrag : projekt.getAuftraege()) {
                    if (!isEligibleForCheck(auftrag, validationContext, parameters)) {
                        continue;
                    }

                    LWAuftragKopf kopf = auftrag.getAuftragKopf();
                    final int delta = TimestampUtil.numberOfCalendarDaysBetween(now, kopf.getBezugsDatum());
                    if (delta < parameters.maxDays) {
                        continue;
                    }

                    // -- determine severity -------------------------------------------------------------------------
                    int severity = 0;
                    if (!liesWithinSpecifiedTimeInterval(
                            auftrag.getWunschDatum(), kopf.getBezugsDatum(), parameters)) {
                        severity= 3;
                    }
                    validationContext.addResult(this, severity, auftrag);
                }
            }
        }
    }

    private boolean isEligibleForCheck(LWAuftrag auftrag, VMDValidationContext validationContext,
                                       Rule133.Parameters parameters) {
        if (!validationContext.isRelevant(auftrag)) {
            return false;
        }

        if (!parameters.abwicklungsArtenBaseAuftrag.contains(auftrag.getAbwicklungsart())) {
            return false;
        }

        if (auftrag.getEtappeRowId() == null) {
            return false;
        }

        if (TimestampUtil.isNull(auftrag.getWunschDatum())) {
            return false;
        }

        final LWAuftragKopf kopf = auftrag.getAuftragKopf();
        if (kopf == null) {
            return false;
        }

        if (TimestampUtil.isNull(kopf.getBezugsDatum())) {
            return false;
        }

        final LWGranit1Code g1Code = kopf.getGranit1();
        if (g1Code == null) {
            return false;
        }

        if (!(g1Code.isBewohnt())) {
            return false;
        }

        final LWGranit2Code g2Code = kopf.getGranit2();
        if (g2Code == null) {
            return false;
        }

        if (!g2Code.isMietobjekt()) {
            return false;
        }

        return true;
    }

    private boolean liesWithinSpecifiedTimeInterval(final long wunschDatum, final long bezugsDatum,
                                                    Rule133.Parameters parameters) {
        final long startTimeInterval = bezugsDatum -
                parameters.maxNumberOfWeeksBeforeBezugsdatum * TimestampUtil.INTERVAL_ONE_WEEK;
        final long endTimeInterval = bezugsDatum -
                parameters.minNumberOfWeeksBeforeBezugsdatum * TimestampUtil.INTERVAL_ONE_WEEK;
        return startTimeInterval <= wunschDatum && wunschDatum < endTimeInterval;
    }

    // --------------------------------------------------------------
    // ---
    // --- Parameters
    // ---
    // --------------------------------------------------------------
    private final static Set<AbwicklungsartCode> ABWICKLUNGSARTEN_BASE_AUFTRAG_DEFAULT = Set.asReadonlySet(
            AbwicklungsartCode.AWA137
    );

    private final static Set<AbwicklungsartCode> ABWICKLUNGSARTEN_REQUIRED_AUFTRAG_DEFAULT = Set.asReadonlySet(
            AbwicklungsartCode.AWA001,
            AbwicklungsartCode.AWA135
    );

    static class Parameters extends VMDRuleParameters {
        public final int maxDays;
        public final int maxNumberOfWeeksBeforeBezugsdatum;
        public final int minNumberOfWeeksBeforeBezugsdatum;
        public final Set<AbwicklungsartCode> abwicklungsArtenBaseAuftrag;
        public final Set<AbwicklungsartCode> abwicklungsArtenRequiredAuftrag;

        Parameters(final VMDRuleValidator rule) {
            maxDays = rule.getIntParameter(0, 35);
            maxNumberOfWeeksBeforeBezugsdatum = rule.getIntParameter(1, 3);
            minNumberOfWeeksBeforeBezugsdatum = rule.getIntParameter(2, 0);
            abwicklungsArtenBaseAuftrag = rule.getCodeSetParameter(
                    3, AbwicklungsartCode.class, ABWICKLUNGSARTEN_BASE_AUFTRAG_DEFAULT);
            abwicklungsArtenRequiredAuftrag = rule.getCodeSetParameter(
                    4, AbwicklungsartCode.class, ABWICKLUNGSARTEN_REQUIRED_AUFTRAG_DEFAULT);
        }
    }

    @Override
    protected VMDRuleParameters getParameters() throws Exception {
        return new Rule133.Parameters(this);
    }
}

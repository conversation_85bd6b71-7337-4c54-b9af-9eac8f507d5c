package ch.eisenring.vmd.server.validation.rules;

import ch.eisenring.core.collections.Set;
import ch.eisenring.logiware.code.pseudo.AbwicklungsartCode;
import ch.eisenring.lw.model.navigable.LWAuftrag;
import ch.eisenring.lw.model.navigable.LWObject;
import ch.eisenring.lw.model.navigable.LWProjekt;
import ch.eisenring.vmd.server.VMDServer;
import ch.eisenring.vmd.server.validation.VMDRuleParameters;
import ch.eisenring.vmd.server.validation.VMDRuleValidator;
import ch.eisenring.vmd.server.validation.VMDValidationContext;

/**
 * Rule 86: "Rechnungswert ist negativ"
 *
 * @See EisenringDMS://Goto/ObjectId/1562696938
 */
public class Rule86 extends VMDRuleValidator {

	public Rule86(final VMDServer server, final int ruleId) {
		super(server, ruleId);
	}

	protected void validateImpl(final VMDValidationContext validationContext) throws Exception {
		final Parameters parameters = (Parameters) validationContext.getParameters(this);
		final LWProjekt projekt = validationContext.getValidationProjekt();
		int severity = 0;
		LWObject ownerSource = null;
		for (final LWAuftrag auftrag : projekt.getAuftraege()) {
			if (!validationContext.isRelevant(auftrag))
				break;
			if (!parameters.abwicklungsArten.contains(auftrag.getAbwicklungsart()))
				continue;
			final Double auftragsSumme = projekt.getAuftragsSumme();
			final Double kundenPreis = projekt.getSubjektWert();
			final Double basisPreis = projekt.getBasisWert();
			double v = auftragsSumme == null ? 0D : auftragsSumme.doubleValue();
			v -= kundenPreis == null ? 0D : kundenPreis.doubleValue();
			v += basisPreis == null ? 0D : basisPreis.doubleValue();
			if (v < 0D) {
				severity = Math.max(severity, 3);
				ownerSource = auftrag;
			}
		}
		validationContext.addResult(this, severity, projekt, ownerSource);
	}

	// --------------------------------------------------------------
	// ---
	// --- Parameters
	// ---
	// --------------------------------------------------------------	
	static class Parameters extends VMDRuleParameters {
		public final Set<AbwicklungsartCode> abwicklungsArten;
		
		Parameters(final VMDRuleValidator rule) {
			abwicklungsArten = rule.getCodeSetParameter(0, AbwicklungsartCode.class);
		}
	}

	@Override
	protected VMDRuleParameters getParameters() throws Exception {
		return new Parameters(this);
	}	

}

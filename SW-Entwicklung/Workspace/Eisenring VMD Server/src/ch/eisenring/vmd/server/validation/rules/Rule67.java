package ch.eisenring.vmd.server.validation.rules;

import ch.eisenring.core.collections.Set;
import ch.eisenring.logiware.code.pseudo.AbwicklungsartCode;
import ch.eisenring.logiware.code.soft.LWBasisobjektCode;
import ch.eisenring.logiware.code.soft.LWGranit2Code;
import ch.eisenring.lw.model.navigable.LWAuftrag;
import ch.eisenring.lw.model.navigable.LWAuftragKopf;
import ch.eisenring.lw.model.navigable.LWObject;
import ch.eisenring.lw.model.navigable.LWProjekt;
import ch.eisenring.vmd.server.VMDServer;
import ch.eisenring.vmd.server.validation.VMDRuleParameters;
import ch.eisenring.vmd.server.validation.VMDRuleValidator;
import ch.eisenring.vmd.server.validation.VMDValidationContext;

/**
 * Rule 67: "<PERSON><PERSON><PERSON> in Granitschlüssel nicht mit Projektbild übereinstimmend (Objekt)"
 *
 * @See EisenringDMS://Open/ObjectId/1522622536
 */
public class Rule67 extends VMDRuleValidator {

	public Rule67(final VMDServer server, final int ruleId) {
		super(server, ruleId);
	}

	@Override
	public void validateImpl(final VMDValidationContext validationContext) throws Exception {
		final Parameters parameters = (Parameters) validationContext.getParameters(this);
		final LWProjekt projekt = validationContext.getValidationProjekt();
		for (final LWAuftrag auftrag : projekt.getAuftraege()) {
			int severity = 0;
			do {
				if (!validationContext.isRelevant(auftrag))
					break;
				if (!parameters.abwicklungsArten.contains(auftrag.getAbwicklungsart()))
					break;
				final LWAuftragKopf kopf = LWObject.validateExists(auftrag.getAuftragKopf());
				if (kopf == null)
					break;
				if (!parameters.granitCodes.contains(kopf.getGranit2()))
					break;
				if (parameters.basisObjektCodes.contains(projekt.getBasisobjektCode()))
					break;
				severity = Math.max(severity, 2);
			} while (false);
			validationContext.addResult(this, severity, auftrag, auftrag);
		}
	}

	// --------------------------------------------------------------
	// ---
	// --- Parameters
	// ---
	// --------------------------------------------------------------	
	static class Parameters extends VMDRuleParameters {
		public final Set<AbwicklungsartCode> abwicklungsArten;
		public final Set<LWGranit2Code> granitCodes;
		public final Set<LWBasisobjektCode> basisObjektCodes;
		
		Parameters(final Rule67 rule) {
			abwicklungsArten = rule.getCodeSetParameter(0, AbwicklungsartCode.class);
			granitCodes = rule.getCodeSetParameter(1, LWGranit2Code.class);
			basisObjektCodes = rule.getCodeSetParameter(2, LWBasisobjektCode.class);
		}
	}

	@Override
	protected VMDRuleParameters getParameters() throws Exception {
		return new Parameters(this);
	}

}

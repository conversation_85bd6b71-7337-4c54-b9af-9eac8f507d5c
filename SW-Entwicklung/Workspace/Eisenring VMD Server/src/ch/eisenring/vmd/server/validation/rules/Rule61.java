package ch.eisenring.vmd.server.validation.rules;

import ch.eisenring.core.collections.Collection;
import ch.eisenring.core.collections.Set;
import ch.eisenring.core.datatypes.primitives.Primitives;
import ch.eisenring.logiware.code.pseudo.AbwicklungsartCode;
import ch.eisenring.logiware.code.soft.GSECode;
import ch.eisenring.lw.model.navigable.LWAuftrag;
import ch.eisenring.lw.model.navigable.LWProjekt;
import ch.eisenring.lw.model.navigable.LWSubjekt;
import ch.eisenring.vmd.server.VMDServer;
import ch.eisenring.vmd.server.validation.VMDRuleParameters;
import ch.eisenring.vmd.server.validation.VMDRuleValidator;
import ch.eisenring.vmd.server.validation.VMDValidationContext;

/**
 * Rule 61: "Id-Nr. Auftrag und Projektbild nicht identisch"
 * 
 * @See EisenringDMS://Goto/ObjectId/1520878420
 */
public final class Rule61 extends VMDRuleValidator {

	public Rule61(final VMDServer server, final int ruleId) {
		super(server, ruleId);
	}

	@Override
	public void validateImpl(final VMDValidationContext validationContext) throws Exception {
		final Parameters parameters = (Parameters) validationContext.getParameters(this);
		final LWProjekt projekt = validationContext.getValidationProjekt();
		final Collection<LWAuftrag> auftraege = projekt.getAuftraege();
		final LWSubjekt subjektAG = projekt.getSubjektAG();
		int severity = 0;
		for (final LWAuftrag auftrag : auftraege) {
			do {
				if (!validationContext.isRelevant(auftrag))
					break;
				if (!parameters.gses.contains(auftrag.getGSEAuftrag()))
					break;
				if (!parameters.abwicklungsArten.contains(auftrag.getAbwicklungsart()))
					break;
				final LWSubjekt subjektA = auftrag.getSubjekt();
				if (Primitives.equals(subjektA, subjektAG))
					break;
				severity = Math.max(severity, 3);
			} while (false);
		}
		validationContext.addResult(this, severity, projekt, projekt);
	}

	// --------------------------------------------------------------
	// ---
	// --- Parameters
	// ---
	// --------------------------------------------------------------	
	static class Parameters extends VMDRuleParameters {
		public final Set<AbwicklungsartCode> abwicklungsArten;
		public final Set<GSECode> gses;
		
		Parameters(final Rule61 rule) {
			abwicklungsArten = rule.getCodeSetParameter(0, AbwicklungsartCode.class);
			gses = rule.getCodeSetParameter(1, GSECode.class);
		}
	}

	@Override
	protected VMDRuleParameters getParameters() throws Exception {
		return new Parameters(this);
	}

}

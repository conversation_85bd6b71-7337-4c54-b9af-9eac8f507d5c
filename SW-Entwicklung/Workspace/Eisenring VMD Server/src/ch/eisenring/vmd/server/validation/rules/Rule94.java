package ch.eisenring.vmd.server.validation.rules;

import ch.eisenring.core.collections.Set;
import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.logiware.code.pseudo.AbwicklungsartCode;
import ch.eisenring.logiware.code.soft.LWGranit2Code;
import ch.eisenring.lw.model.navigable.LWAuftrag;
import ch.eisenring.lw.model.navigable.LWAuftragKopf;
import ch.eisenring.lw.model.navigable.LWProjekt;
import ch.eisenring.vmd.server.VMDServer;
import ch.eisenring.vmd.server.validation.VMDRuleParameters;
import ch.eisenring.vmd.server.validation.VMDRuleValidator;
import ch.eisenring.vmd.server.validation.VMDValidationContext;

/**
 * @See EisenringDMS://Goto/ObjectId/1624462039
 */
public class Rule94 extends VMDRuleValidator {

	public Rule94(final VMDServer server, final int ruleId) {
		super(server, ruleId);
	}

	@Override
	public void validateImpl(final VMDValidationContext validationContext) throws Exception {
		final Parameters parameters = (Parameters) validationContext.getParameters(this);
		final LWProjekt projekt = validationContext.getValidationProjekt();
		for (final LWAuftrag auftrag : projekt.getAuftraege()) {
			int severity = 0;
			do {
				if (!validationContext.isRelevant(auftrag))
					break;
				if (!parameters.abwicklungsArten.contains(auftrag.getAbwicklungsart()))
					break;
				// ok wenn noch kein wunschdatum gesetzt
				final long wunschDatum = auftrag.getWunschDatum();
				if (TimestampUtil.isNull(wunschDatum))
					break;
				// ok wenn frist nicht überschritten
				final int delta = validationContext.getNumberOfWeekdaysFromNow(wunschDatum);
				if (delta >= parameters.maxDays)
					break;
				// passender objekttyp?
				final LWAuftragKopf kopf = auftrag.getAuftragKopf();
				if (kopf == null)
					break;
				if (!parameters.objektTypen.contains(kopf.getGranit2()))
					break;
				// ettapiert?
				if (projekt.getEtappeRowId() != null)
					break;
				// typenhaus? ignore
				if (isTypenhaus(auftrag))
					break;				
				severity = Math.max(severity, 3);
			} while (false);
			validationContext.addResult(this, severity, auftrag, auftrag);
		}
	}

	// --------------------------------------------------------------
	// ---
	// --- Parameters
	// ---
	// --------------------------------------------------------------	
	private final static Set<AbwicklungsartCode> ABWICKLUNGSARTEN_DEFAULT = Set.asReadonlySet(
			AbwicklungsartCode.AWA001,
			AbwicklungsartCode.AWA135
	);

	private final static Set<LWGranit2Code> OBJEKTTYPEN_DEFAULT = Set.asReadonlySet(
			LWGranit2Code.EIGENTUM,
			LWGranit2Code.EINZEL_B,
			LWGranit2Code.EINZEL_C
	);

	static class Parameters extends VMDRuleParameters {
		public final Set<AbwicklungsartCode> abwicklungsArten;
		public final Set<LWGranit2Code> objektTypen;
		public final int maxDays; 

		Parameters(final VMDRuleValidator rule) {
			abwicklungsArten = rule.getCodeSetParameter(0, AbwicklungsartCode.class, ABWICKLUNGSARTEN_DEFAULT);
			objektTypen = rule.getCodeSetParameter(1, LWGranit2Code.class, OBJEKTTYPEN_DEFAULT);
			maxDays = rule.getIntParameter(2, 60);
		}
	}

	@Override
	protected VMDRuleParameters getParameters() throws Exception {
		return new Parameters(this);
	}	

}

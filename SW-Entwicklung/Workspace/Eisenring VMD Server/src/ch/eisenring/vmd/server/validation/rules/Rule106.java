package ch.eisenring.vmd.server.validation.rules;

import ch.eisenring.core.collections.Set;
import ch.eisenring.logiware.code.pseudo.AbwicklungsartCode;
import ch.eisenring.logiware.code.soft.LWObjektStatusCode;
import ch.eisenring.lw.model.navigable.LWAuftrag;
import ch.eisenring.lw.model.navigable.LWProjekt;
import ch.eisenring.vmd.server.VMDServer;
import ch.eisenring.vmd.server.validation.VMDRuleParameters;
import ch.eisenring.vmd.server.validation.VMDRuleValidator;
import ch.eisenring.vmd.server.validation.VMDValidationContext;

/**
 * Rule 106: "Offerte Service nach 2 Tagen nicht nachgefasst"
 *
 * @See EisenringDMS://Goto/ObjectId/1678843322
 */
public class Rule106 extends VMDRuleValidator {

	public Rule106(final VMDServer server, final int ruleId) {
		super(server, ruleId);
	}

	@Override
	public void validateImpl(final VMDValidationContext validationContext) throws Exception {
		final Parameters parameters = (Parameters) validationContext.getParameters(this);
		final LWProjekt projekt = validationContext.getValidationProjekt();

		final boolean validStatus = parameters.statusCodes.contains(projekt.getObjektStatus());
		for (final LWAuftrag auftrag : projekt.getAuftraege()) {
			if (!parameters.abwicklungsArten.contains(auftrag.getAbwicklungsart()))
				continue;
			int severity = 0;
			do {
				if (!validationContext.isRelevant(auftrag))
					break;
				if (validStatus)
					break;
				final long date = auftrag.getWunschDatum();
				final int days = -validationContext.getNumberOfWeekdaysFromNow(date);
				final boolean daysOk = days <= parameters.days;
//if (true) {
//String ok = daysOk ? "+" : "-";
//String now = TimestampUtil.DATE10.format(validationContext.getNow());
//String then = TimestampUtil.DATE10.format(date);
//System.out.println(ok + " " + days + "   " + now + "    " + then);
//}
				if (daysOk)
					break;
				severity = Math.max(severity, 2);
			} while (false);
			validationContext.addResult(this, severity, auftrag);
		}
	}

	// --------------------------------------------------------------
	// ---
	// --- Parameters
	// ---
	// --------------------------------------------------------------	
	static class Parameters extends VMDRuleParameters {
		public final Set<AbwicklungsartCode> abwicklungsArten;
		public final Set<LWObjektStatusCode> statusCodes;
		public final int days;

		Parameters(final VMDRuleValidator rule) {
			abwicklungsArten = rule.getCodeSetParameter(0, AbwicklungsartCode.class);
			statusCodes = rule.getCodeSetParameter(1, LWObjektStatusCode.class);
			days = rule.getIntParameter(2, 2);
		}
	}

	@Override
	protected VMDRuleParameters getParameters() throws Exception {
		return new Parameters(this);
	}	

}

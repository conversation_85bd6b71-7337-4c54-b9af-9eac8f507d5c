package ch.eisenring.vmd.server.network;

import ch.eisenring.app.server.network.AbstractPacketHandler;
import ch.eisenring.network.implementation.PacketDispatchMode;
import ch.eisenring.network.packet.AbstractPacket;
import ch.eisenring.vmd.server.VMDServer;

abstract class AbstractVMDPacketHandler extends AbstractPacketHandler<VMDServer, AbstractPacket> {

	protected AbstractVMDPacketHandler(final VMDServer server, final Class<? extends AbstractPacket> packetClass) {
		this(server, packetClass, PacketDispatchMode.ASYNCHRONOUS);
	}

	@SuppressWarnings("unchecked")
	protected AbstractVMDPacketHandler(final VMDServer server,
			                           final Class<? extends AbstractPacket> packetClass,
			                           final PacketDispatchMode dispatchMode) {
		super(server, (Class<AbstractPacket>) packetClass, dispatchMode);
	}
}

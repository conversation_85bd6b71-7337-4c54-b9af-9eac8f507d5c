package ch.eisenring.vmd.server.validation.rules;

import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.collections.Collection;
import ch.eisenring.core.collections.Set;
import ch.eisenring.logiware.code.pseudo.AbwicklungsartCode;
import ch.eisenring.logiware.code.soft.LWGranit2Code;
import ch.eisenring.lw.model.navigable.LWAuftrag;
import ch.eisenring.lw.model.navigable.LWAuftragKopf;
import ch.eisenring.lw.model.navigable.LWObject;
import ch.eisenring.lw.model.navigable.LWProjekt;
import ch.eisenring.vmd.server.VMDServer;
import ch.eisenring.vmd.server.validation.VMDRuleParameters;
import ch.eisenring.vmd.server.validation.VMDRuleValidator;
import ch.eisenring.vmd.server.validation.VMDValidationContext;

/**
 * Implements rule #19: "Auftragssumme und Basis/Verrechnungspreis bei Objektküche vorhanden"
 * 
 * Specified by: BADA, 2013-11-27
 */
public final class Rule19 extends VMDRuleValidator {

	public Rule19(final VMDServer server, final int ruleId) {
		super(server, ruleId);
	}

	@Override
	public void validateImpl(final VMDValidationContext validationContext) throws Exception {
		final Parameters parameters = (Parameters) validationContext.getParameters(this);
		final LWProjekt projekt = validationContext.getValidationProjekt();
		final Collection<LWAuftrag> auftraege = projekt.getAuftraege();
		LWObject ownerSource = null;
		for (final LWAuftrag auftrag : auftraege) {
			do {
				if (!validationContext.isRelevant(auftrag))
					break;
				if (!parameters.abwicklungsArten.contains(auftrag.getAbwicklungsart()))
					break;
				final LWAuftragKopf kopf = auftrag.getAuftragKopf();
				if (kopf == null)
					break;
				final LWGranit2Code g2Code = kopf.getGranit2();
				if (AbstractCode.isNull(g2Code))
					break;
				if (!parameters.anzahlTyp.contains(g2Code))
					break;
				ownerSource = LWObject.maxAddDate(ownerSource, auftrag);
			} while (false);
		}
		int severity = 0;
		if (ownerSource != null) {
			final Number auftragsSumme = projekt.getAuftragsSumme();
			if (auftragsSumme == null)
				severity = Math.max(severity, 2);
			final Number basisWert = projekt.getBasisWert();
			if (basisWert == null)
				severity = Math.max(severity, 2);
		}
		validationContext.addResult(this, severity, projekt);
	}

	// --------------------------------------------------------------
	// ---
	// --- Parameters
	// ---
	// --------------------------------------------------------------	
	private final static Set<AbwicklungsartCode> ABWICKLUNGSARTEN_DEFAULT = Set.asReadonlySet(
			AbwicklungsartCode.AWA135
	);

	private final static Set<LWGranit2Code> ANZAHL_TYP_DEFAULT = Set.asReadonlySet(
			LWGranit2Code.EIGENTUM
	);

	static class Parameters extends VMDRuleParameters {
		public final Set<AbwicklungsartCode> abwicklungsArten;
		public final Set<LWGranit2Code> anzahlTyp;

		Parameters(final VMDRuleValidator rule) {
			abwicklungsArten = rule.getCodeSetParameter(0, AbwicklungsartCode.class, ABWICKLUNGSARTEN_DEFAULT);
			anzahlTyp = rule.getCodeSetParameter(1, LWGranit2Code.class, ANZAHL_TYP_DEFAULT);
		}
	}

	@Override
	protected VMDRuleParameters getParameters() throws Exception {
		return new Parameters(this);
	}

}

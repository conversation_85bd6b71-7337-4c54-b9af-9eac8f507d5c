package ch.eisenring.vmd.server.validation.rules;

import ch.eisenring.core.collections.Set;
import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.logiware.LWProjektKey;
import ch.eisenring.logiware.code.pseudo.AbwicklungsartCode;
import ch.eisenring.lw.api.LWEtappeData;
import ch.eisenring.lw.model.navigable.LWAuftrag;
import ch.eisenring.lw.model.navigable.LWProjekt;
import ch.eisenring.lw.model.navigable.LWXEtappe;
import ch.eisenring.lw.model.navigable.LWXPAuftrag;
import ch.eisenring.lw.pojo.LWXPAuftragPojo;
import ch.eisenring.vmd.server.VMDServer;
import ch.eisenring.vmd.server.validation.VMDRuleParameters;
import ch.eisenring.vmd.server.validation.VMDRuleValidator;
import ch.eisenring.vmd.server.validation.VMDValidationContext;

import java.sql.Timestamp;
import java.time.LocalDate;
import java.util.Comparator;

public class Rule153 extends VMDRuleValidator {

    // In sync mit Wert in heag.huo.shared.pojo.HUOObjekt halten
    private final int BAUPROGRAMM_MAX_AGE = 56;

    public Rule153(final VMDServer server, final int ruleId) {
        super(server, ruleId);
    }

    @Override
    public void validateImpl(final VMDValidationContext validationContext) throws Exception {
        final Rule153.Parameters parameters = (Rule153.Parameters) validationContext.getParameters(this);
        final LWProjekt projekt = validationContext.getValidationProjekt();
        final LWProjekt basis = projekt.getBasisProjekt();
        if (basis == null || isEinzelküche(projekt)) {
            return;
        }

        if (!hasValidAuftrag(projekt, validationContext, parameters)) {
            return;
        }

        final LWProjektKey basisKey = basis.getProjektKey();
        if (validationContext.getRuleAccel(basisKey) != null) {
            return;
        }
        int severity = 0;

        if (!isEtappierungUpToDate(basis)) {
            severity = 3;
        }

        validationContext.addResult(this, severity, basis);
        validationContext.putRuleAccel(basisKey, severity);
    }

    private boolean isEinzelküche(LWProjekt projekt) {
        final LWProjekt basis = projekt.getBasisProjekt();
        final String projektNummerBasis = basis.getProjektKey().getProjektnummer();
        String projektNummerKommission = projekt.getProjektKey().getProjektnummer();
        if (projektNummerKommission.contains("-")) {
            return projektNummerBasis.equals(projektNummerKommission.substring(0, projektNummerKommission.indexOf("-")));
        } else {
            return projektNummerBasis.equals(projektNummerKommission);
        }
    }

    private boolean hasValidAuftrag(LWProjekt projekt, VMDValidationContext validationContext, Parameters parameters) {
        Set<LWAuftrag> auftraege = projekt.getAuftraege();
        for (LWAuftrag auftrag : auftraege) {
            if (isAuftragRelevant(validationContext, auftrag, parameters))
                return true;
        }
        return false;
    }

    public boolean isEtappierungUpToDate(LWProjekt basis) {
        final long now = Timestamp.valueOf(LocalDate.now().atStartOfDay()).getTime();
        final LWXPAuftragPojo lwxBasis = LWXPAuftragPojo.create(basis.getProjektKey());
        // Eintrag kann NULL sein, wenn es für die Basis noch keinen LWX-Record gibt
        final LWXPAuftrag ex = basis.getLWXPAuftrag();
        // Angelehnt an HOUProjekt.create(final LWProjekt projekt)
        if (ex == null) {
            lwxBasis.setDatumBauprogramm(TimestampUtil.NULL_TIMESTAMP);
        } else {
            ex.updatePojo(lwxBasis);
        }
        final Set<LWXEtappe> etappen = basis.getEtappen();
        final long datumBauProgramm = lwxBasis.getDatumBauprogramm();
        if (etappen.size() > 0) {
            if (hasRelevantEtappe(etappen)) {
                int daysSinceUpdated = TimestampUtil.isNull(datumBauProgramm) ? Integer.MAX_VALUE : TimestampUtil.numberOfCalendarDaysBetween(datumBauProgramm, now);
                return daysSinceUpdated < BAUPROGRAMM_MAX_AGE;
            } else {
                return true;
            }
        } else {
            return false;
        }
    }

    private boolean hasRelevantEtappe(Set<LWXEtappe> etappen) {
        final long now = Timestamp.valueOf(LocalDate.now().atStartOfDay()).getTime();
        LWEtappeData lastEnd = etappen.stream().max(Comparator.comparingLong(LWEtappeData::getEnd)).get();
        return now < lastEnd.getEnd();
    }

    private boolean isAuftragRelevant(VMDValidationContext validationContext,
                                      LWAuftrag auftrag,
                                      Parameters parameters) {
        if (!validationContext.isRelevant(auftrag)) {
            return false;
        }

        if (!parameters.abwicklungsArten.contains(auftrag.getAbwicklungsart())) {
            return false;
        }
        return true;
    }

    // --------------------------------------------------------------
    // ---
    // --- Parameters
    // ---
    // --------------------------------------------------------------
    private final static Set<AbwicklungsartCode> ABWICKLUNGSART_DEFAULT = Set.asReadonlySet(
            AbwicklungsartCode.AWA135, AbwicklungsartCode.AWA001, AbwicklungsartCode.AWA002, AbwicklungsartCode.AWA132, AbwicklungsartCode.AWA134
    );

    static class Parameters extends VMDRuleParameters {
        public final Set<AbwicklungsartCode> abwicklungsArten;
        public final Integer days;

        Parameters(final VMDRuleValidator rule) {
            abwicklungsArten = rule.getCodeSetParameter(0, AbwicklungsartCode.class, ABWICKLUNGSART_DEFAULT);
            days = rule.getIntParameter(1, 40);
        }
    }

    @Override
    protected VMDRuleParameters getParameters() throws Exception {
        return new Rule153.Parameters(this);
    }

}

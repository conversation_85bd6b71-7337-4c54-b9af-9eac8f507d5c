package ch.eisenring.vmd.server.validation.rules;

import ch.eisenring.core.collections.Set;
import ch.eisenring.logiware.LWEtappeKey;
import ch.eisenring.logiware.code.pseudo.AbwicklungsartCode;
import ch.eisenring.logiware.code.soft.LWGranit2Code;
import ch.eisenring.logiware.code.soft.LWGranitCCode;
import ch.eisenring.lw.model.navigable.*;
import ch.eisenring.vmd.server.VMDServer;
import ch.eisenring.vmd.server.validation.VMDRuleParameters;
import ch.eisenring.vmd.server.validation.VMDRuleValidator;
import ch.eisenring.vmd.server.validation.VMDValidationContext;

/**
 * Rule 120: Einzelküche nicht etappiert
 */
public class Rule120 extends VMDRuleValidator {

    public Rule120(final VMDServer server, final int ruleId) {
        super(server, ruleId);
    }

    @Override
    public void validateImpl(final VMDValidationContext validationContext) throws Exception {
        final Parameters parameters = (Parameters) validationContext.getParameters(this);
        final LWProjekt projekt = validationContext.getValidationProjekt();
        final Set<LWAuftrag> auftraege = projekt.getAuftraege();
        for (final LWAuftrag auftrag : auftraege) {
            int severity = 0;
            do {
                // passender auftrag?
                if (!isMatchingAuftrag(validationContext, auftrag, parameters))
                    break;
                // vor fehlerfrist?
                final int nDays = validationContext.getNumberOfWeekdaysFromNow(auftrag.getWunschDatum());
                if (nDays > parameters.workingDays)
                    break;
                // ist etappiert?
                if (getEtappe(auftrag) != null)
                    break;
                severity = Math.max(severity, 3);
            } while (false);
            validationContext.addResult(this, severity, auftrag, auftrag);
        }
    }

    static boolean isMatchingAuftrag(final VMDValidationContext context, final LWAuftrag auftrag,
                                     final ParameterBase parameters) {
        final LWAuftragKopf kopf = auftrag.getAuftragKopf();
        return kopf != null
                && parameters.awa.contains(auftrag.getAbwicklungsart())
                && context.isRelevant(auftrag)
                && parameters.g2Codes.contains(kopf.getGranit2());
    }

    static LWXEtappe getEtappe(final LWAuftrag auftrag) {
        final Long rowId = auftrag.getEtappeRowId();
        if (rowId == null)
            return null;
        final LWEtappeKey key = LWEtappeKey.get(rowId);
        final LWXEtappe etappe = auftrag.getCache().getObject(key, LWXEtappe.class);
        return LWObject.validateExists(etappe);
    }

    // --------------------------------------------------------------
    // ---
    // --- Parameters
    // ---
    // --------------------------------------------------------------
    static abstract class ParameterBase extends VMDRuleParameters {
        public final Set<AbwicklungsartCode> awa;
        public final Set<LWGranit2Code> g2Codes;

        protected ParameterBase(final VMDRuleValidator rule) {
            awa = rule.getCodeSetParameter(0, AbwicklungsartCode.class);
            g2Codes = rule.getCodeSetParameter(1, LWGranit2Code.class);
        }
    }
    static class Parameters extends ParameterBase {
        public final int workingDays;

        Parameters(final VMDRuleValidator rule) {
            super(rule);
            workingDays = rule.getIntParameter(2, 45);
        }
    }

    @Override
    protected VMDRuleParameters getParameters() throws Exception {
        return new Parameters(this);
    }

}


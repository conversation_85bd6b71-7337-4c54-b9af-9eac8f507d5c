package ch.eisenring.vmd.server.network;

import static ch.eisenring.vmd.server.dao.VMDMapping.OM_RESULT;
import static ch.eisenring.vmd.server.dao.VMDMapping.TM_RESULT;

import java.sql.SQLException;
import java.util.Calendar;

import ch.eisenring.app.server.model.ContextSource;
import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.collections.Collection;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.datatypes.date.HEAGCalendar;
import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.jdbc.JDBCUtil;
import ch.eisenring.jdbc.StatementParameters;
import ch.eisenring.model.TransactionContext;
import ch.eisenring.model.TransactionSource;
import ch.eisenring.model.engine.jdbc.CollectingRowHandler;
import ch.eisenring.model.engine.jdbc.TableMapping;
import ch.eisenring.network.PacketSink;
import ch.eisenring.network.packet.AbstractPacket;
import ch.eisenring.vmd.server.VMDServer;
import ch.eisenring.vmd.shared.VMDConstants;
import ch.eisenring.vmd.shared.model.meta.VMDRuleResultMeta;
import ch.eisenring.vmd.shared.model.pojo.VMDRuleResult;
import ch.eisenring.vmd.shared.network.PacketResultSearchReply;
import ch.eisenring.vmd.shared.network.PacketResultSearchRequest;

final class ResultSearchRequestHandler extends AbstractVMDPacketHandler {

	ResultSearchRequestHandler(final VMDServer server) {
		super(server, PacketResultSearchRequest.class);
	}

	@Override
	public void handle(final AbstractPacket packet, final PacketSink sink) {
		final PacketResultSearchRequest request = (PacketResultSearchRequest) packet;
		final PacketResultSearchReply reply = handle(server, request);
		sink.sendPacket(reply, false);
	}

	static PacketResultSearchReply handle(final VMDServer server, final PacketResultSearchRequest request) {
		try {
			final PacketResultSearchReply reply = handleImpl(server, request);
			if (reply != null)
				return reply;
			return PacketResultSearchReply.create(request, ErrorMessage.ERROR);
		} catch (final Exception e) {
			return PacketResultSearchReply.create(request, new ErrorMessage(e));
		}
	}

	static PacketResultSearchReply handleImpl(final VMDServer server, final PacketResultSearchRequest request) throws Exception {
		final List<VMDRuleResult> results = new ArrayList<>(); 
		final TransactionSource source = new ContextSource(VMDConstants.VMD_DATABASE) {
			@Override
			protected void loadImpl(final TransactionContext context) throws SQLException {
				final StatementParameters params = new StatementParameters();
				final StringMaker sql = StringMaker.obtain(512);
				TM_RESULT.appendSelectSQL(sql, this, TableMapping.NO_TABLE_ALIAS);
				String connector = " WHERE ";
				// construct the where clause
				final Collection<String> problemOwners = request.getProblemOwners();
				switch (problemOwners.size()) {
					case 0:
						break;
					case 1:
						final String problemOwner = problemOwners.iterator().next();
						sql.append(connector); connector = " AND ";
						sql.append(TM_RESULT.getColumn(VMDRuleResultMeta.ATR_PROBLEMOWNER));
						if (JDBCUtil.containsWildcards(problemOwner)) {
							sql.append(" LIKE ?");
							params.add(JDBCUtil.replaceWildcards(problemOwner));
						} else {
							sql.append(" = ?");
							params.add(problemOwner);
						}
						break;
					default:
						sql.append(connector); connector = " AND ";
						sql.append(TM_RESULT.getColumn(VMDRuleResultMeta.ATR_PROBLEMOWNER));
						sql.append(" IN ");
						prepareIn(sql, problemOwners);
						params.addAll(problemOwners);
						break;
				}
				if (request.getRuleId() != null) {
					sql.append(connector); connector = " AND ";
					sql.append(TM_RESULT.getColumn(VMDRuleResultMeta.ATR_RUELID));
					sql.append(" = ?");
					params.add(request.getRuleId());
				}
				if (!AbstractCode.isNull(request.getStatus())) {
					sql.append(connector); connector = " AND ";
					sql.append(TM_RESULT.getColumn(VMDRuleResultMeta.ATR_STATUS));
					sql.append(" = ?");
					params.add(request.getStatus());
				}
				if (!AbstractCode.isNull(request.getSeverity())) {
					sql.append(connector); connector = " AND ";
					sql.append(TM_RESULT.getColumn(VMDRuleResultMeta.ATR_SEVERITY));
					sql.append(" = ?");
					params.add(request.getSeverity());
				}
				if (!TimestampUtil.isNull(request.getDetectDateFrom())) {
					sql.append(connector); connector = " AND ";
					sql.append(TM_RESULT.getColumn(VMDRuleResultMeta.ATR_OPENSINCE));
					sql.append(" >= ?");
					params.addDate(request.getDetectDateFrom());
				}
				if (!TimestampUtil.isNull(request.getDetectDateUpto())) {
					sql.append(connector); connector = " AND ";
					sql.append(TM_RESULT.getColumn(VMDRuleResultMeta.ATR_OPENSINCE));
					sql.append(" < ?");
					final HEAGCalendar c = HEAGCalendar.obtain(request.getDetectDateUpto());
					c.setTime(0, 0, 0, 0);
					c.add(Calendar.DAY_OF_YEAR, 1);
					params.addDate(c.release().getTimeInMillis());
				}
				doQuery(sql.release(), params, new CollectingRowHandler<>(OM_RESULT, results, context));
			}
		};
		final TransactionContext context = new TransactionContext();
		context.load(source);
		return PacketResultSearchReply.create(request, results);
	}

}

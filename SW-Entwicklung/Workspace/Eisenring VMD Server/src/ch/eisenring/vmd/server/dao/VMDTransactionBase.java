package ch.eisenring.vmd.server.dao;

import java.io.IOException;
import java.sql.SQLException;
import java.util.Iterator;

import ch.eisenring.jdbc.JDBCBase;
import ch.eisenring.model.Model;
import ch.eisenring.model.TransactionContext;
import ch.eisenring.model.TransactionSink;
import ch.eisenring.model.TransactionSource;
import ch.eisenring.vmd.server.VMDServer;
import ch.eisenring.vmd.shared.VMDConstants;

/**
 * Unified base class, can be used for loading and storing.
 */
public abstract class VMDTransactionBase extends JDBCBase
	implements TransactionSource, TransactionSink {

	protected final VMDServer server;
	
	protected VMDTransactionBase(final VMDServer server) {
		super(VMDConstants.VMD_DATABASE.getConnectionInfo());
		this.server = server;
	}

	@Override
	public final void load(final TransactionContext context) throws IOException {
		try {
			open();
			loadImpl(context);
		} catch (final Exception e) {
			throw new IOException(e.getMessage(), e);
		} finally {
			close();
		}
	}

	protected abstract void loadImpl(final TransactionContext context) throws SQLException;
	
	@Override
	public final void store(final Iterator<Model> modelItr, final TransactionContext context) throws IOException {
		try {
			open();
			storeImpl(context);
		} catch (final Exception e) {
			throw new IOException(e.getMessage(), e);
		} finally {
			close();
		}
	}

	protected abstract void storeImpl(final TransactionContext context) throws SQLException;

}

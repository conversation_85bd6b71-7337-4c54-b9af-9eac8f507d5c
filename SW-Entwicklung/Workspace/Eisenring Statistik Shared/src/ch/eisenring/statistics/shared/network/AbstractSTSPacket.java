package ch.eisenring.statistics.shared.network;

import ch.eisenring.network.packet.AbstractPacket;
import ch.eisenring.network.security.SecurityDomain;

public abstract class AbstractSTSPacket extends AbstractPacket {

	public final static SecurityDomain STS_DOMAIN = SecurityDomain.get("STS");
	
	// not necessary (yet)
	
//	public final static int COMPATIBILITY_MASK = allocateCompatibilityMaskBit();
//
//	@Override
//	public int getCompatibilityMask() {
//		return COMPATIBILITY_MASK;
//	}

	AbstractSTSPacket(final int caps, final int priority) {
		super(caps, priority);
	}

	@Override
	public final SecurityDomain getSecurityDomain() {
		return STS_DOMAIN;
	}

}

package ch.eisenring.statistics.shared.util;

import java.util.Iterator;

import org.jfree.data.KeyToGroupMap;
import org.jfree.data.category.CategoryDataset;
import org.jfree.data.category.DefaultCategoryDataset;
import org.jfree.data.general.DefaultPieDataset;
import org.jfree.data.general.PieDataset;

import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.tag.TagSet;
import ch.eisenring.statistics.shared.codetables.DataSetFilterCode;
import ch.eisenring.statistics.shared.data.DataHolderInterface;
import ch.eisenring.statistics.shared.data.DataPoint;

public final class DataSetBuilder {

	public static class GroupedDataSetResult {
		public DefaultCategoryDataset dataSet;
		public KeyToGroupMap groupMap;
	}

	public static PieDataset getPieDataSet(final DataHolderInterface dataHolder) {
		final DefaultPieDataset data = new DefaultPieDataset();
		final String[] labels = dataHolder.getLabels();
		double[] values = dataHolder.getData();
		for (int i=0; i<values.length; ++i) {
			final String label = labels[i];
			data.setValue(label, values[i]);
		}
		return data;
	}

	public static List<String> getCategoryDataSetColumns(final DataHolderInterface dataHolder) {
		final String[] labels = dataHolder.getLabels();
		final List<String> columnList = new ArrayList<String>(64);
		double[] values = dataHolder.getData();
		for (int i=0; i<values.length; ++i) {
			final String label = labels[i];
			final String[] split = DataPoint.splitLabel(label);
			final String columnKey = split.length >= 2 ? split[1] : null;
			if (!columnList.contains(columnKey)) {
				columnList.add(columnKey);
			}
		}
		return columnList;
	}
	
	public static List<String> getCategoryDataSetColumns(final DataHolderInterface dataHolder, int pageNo, int itemsPerPage) {
		final List<String> allColumns = getCategoryDataSetColumns(dataHolder);
		int nPages = (allColumns.size() + itemsPerPage - 1) / itemsPerPage;
		int nLast = allColumns.size() % itemsPerPage;
		int realItemsPerPage = itemsPerPage;
		if ((nLast + nPages - 1) < itemsPerPage) {
			nLast += nPages - 1;
			realItemsPerPage -= 1;
		}
		final int offset = pageNo * realItemsPerPage;
		final List<String> result = new ArrayList<String>(32);
		for (int i=0; i<realItemsPerPage; ++i) {
			final int j = i+offset;
			if (j<allColumns.size()) {
				result.add(allColumns.get(j));
			}
		}
		return result.size() <= 0 ? null : result;
	}

	public static CategoryDataset getCategoryDataSet(final DataHolderInterface dataHolder, int pageNo, int itemsPerPage) {
		final List<String> columns = getCategoryDataSetColumns(dataHolder, pageNo, itemsPerPage);
		if (columns == null) {
			return null;
		} else {
			return getCategoryDataSet(dataHolder, columns);
		}
	}
	
	public static CategoryDataset getCategoryDataSet(final DataHolderInterface dataHolder, final List<String> columnList) {
		final DefaultCategoryDataset data = new DefaultCategoryDataset();
		final String[] labels = dataHolder.getLabels();
		double[] values = dataHolder.getData();
		final Iterator<String> columnItr = columnList == null ? null : columnList.iterator();
		String columnId = null;
		do {
			if (columnItr != null) {
				if (columnItr.hasNext()) {
					columnId = columnItr.next();
				} else {
					break;
				}
			}
			for (int i=0; i<values.length; ++i) {
				final String label = labels[i];
				final String[] split = DataPoint.splitLabel(label);
				final String rowKey = split.length >= 1 ? split[0] : null;
				final String columnKey = split.length >= 2 ? split[1] : null;
				if (columnId == null || columnId.equals(columnKey)) {
					data.addValue(values[i], rowKey, columnKey);
				}
			}
		} while (columnId != null);
		return data;
	}

	public static GroupedDataSetResult getGroupedCategoryDataSet(final DataHolderInterface dataHolder,
			                                                     final int pageNo, final int itemsPerPage,
			                                                     final TagSet tags) {
		final List<String> columns = getCategoryDataSetColumns(dataHolder, pageNo, itemsPerPage);
		if (columns == null) {
			return null;
		} else {
			return getGroupedCategoryDataSet(dataHolder, columns, tags);
		}
	}

	public static GroupedDataSetResult getGroupedCategoryDataSet(final DataHolderInterface dataHolder,
			                                                     final List<String> columnList,
			                                                     final TagSet tags) {
		final ChartCustomizerBase customizer = ChartCustomizerBase.get(tags);
		final DataSetFilterCode dataFilter = DataSetFilterCode.get(tags);
		final GroupedDataSetResult result = new GroupedDataSetResult();
		result.dataSet = new DefaultCategoryDataset();
		final String[] labels = dataHolder.getLabels();
		double[] values = dataHolder.getData();
		Iterator<String> columnItr = columnList == null ? null : columnList.iterator();
		String columnId = columnItr.hasNext() ? columnItr.next() : null;
		if (customizer != null)
			customizer.customizeGroupedDataSetData(result, labels, values, columnId);
		columnItr = columnList == null ? null : columnList.iterator();
		do {
			if (columnItr != null && columnItr.hasNext()) {
				columnId = columnItr.next();
			} else {
				break;
			}
			for (int i=0; i<values.length; ++i) {
				if (!dataFilter.accepts(values[i]))
					continue;
				final String label = labels[i];
				final String[] split = DataPoint.splitLabel(label);
				final String rowKey = split.length >= 1 ? split[0] : null;
				final String columnKey = split.length >= 2 ? split[1] : null;
				final String groupKey = split.length >= 3 ? split[2] : null;
				if (columnId == null || columnId.equals(columnKey)) {
					final String mapFrom;
					if (groupKey == null) {
						result.dataSet.addValue(values[i], rowKey, columnKey);
						mapFrom = rowKey;
					} else {
						mapFrom = rowKey + " (" + groupKey + ")";
						result.dataSet.addValue(values[i], mapFrom, columnKey);
					}
					if (result.groupMap == null) {
						result.groupMap = new KeyToGroupMap(rowKey);
					}
					result.groupMap.mapKeyToGroup(mapFrom, rowKey);
				}
			}
		} while (columnId != null);
		return result;
	}

}

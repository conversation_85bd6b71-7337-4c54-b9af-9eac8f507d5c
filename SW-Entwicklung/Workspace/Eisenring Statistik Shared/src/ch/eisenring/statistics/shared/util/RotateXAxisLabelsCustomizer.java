package ch.eisenring.statistics.shared.util;

import java.awt.Font;

import org.jfree.chart.axis.CategoryAxis;
import org.jfree.chart.axis.CategoryLabelPositions;
import org.jfree.chart.plot.CategoryPlot;

import ch.eisenring.core.tag.TagSet;

public class RotateXAxisLabelsCustomizer extends ChartCustomizerBase {

	@Override
	public void customizePlot(final CategoryPlot plot, final TagSet chartTags) {
		super.customizePlot(plot, chartTags);
		final CategoryAxis axis = plot.getDomainAxis();
		axis.setTickLabelFont(new Font("sans serif", 0, 7));
		axis.setCategoryLabelPositions(CategoryLabelPositions.UP_90);
	}

}

package ch.eisenring.statistics.shared.network;

import java.util.Date;

import ch.eisenring.core.datatypes.date.DateUtil;
import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.statistics.shared.codetables.StatisticTypeCode;

public final class PacketStatisticRequest extends PacketStatisticBase {

	PacketStatisticRequest() {
		super(CAPS_SEQUENCE_REQUEST, PRIORITY_REQUEST);
	}

	public static PacketStatisticRequest create(final StatisticTypeCode type,
												final Date dateFrom,
												final Date dateUpTo) {
		final PacketStatisticRequest packet = new PacketStatisticRequest();
		packet.type = type;
		packet.timestampFrom = TimestampUtil.toTimestamp(DateUtil.setTime(dateFrom, 0, 0, 0, 0));
		packet.timestampUpto = TimestampUtil.toTimestamp(DateUtil.setTime(dateUpTo, 23, 59, 59, 0));
		return packet;
	}

}

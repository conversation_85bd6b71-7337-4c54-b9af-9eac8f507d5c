package ch.eisenring.statistics.shared.codetables;

import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.codetype.CodeReference;
import ch.eisenring.core.codetype.DynamicCode;
import ch.eisenring.core.codetype.Serializable;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.user.shared.codetables.RightCode;

/**
 * This class is designated not serializable, so it won't be
 * streamed automatically.
 */
@Serializable(false)
public final class StatisticTypeCode extends DynamicCode {

	private final static String KEY_DESCRIPTION = "\rDescription";
	private final static String KEY_LEGENDCODE = "\rLegendCode";
	private final static String KEY_ORIENTATIONCODE = "\rOrientationCode";
	private final static String KEY_PLOTTYPECODE = "\rPlotTypeCode";
	private final static String KEY_ITEMSPERPAGE = "\rItemsPerPage";
	private final static String KEY_RIGHT_CODE_ID = "\rRightCodeId";
	private final static String KEY_SUBTYPE1 = "\rSubType1";
	private final static String KEY_SUBTYPE2 = "\rSubType2";
	private final static String KEY_DATA_N = "\rData";
	private final static String KEY_GROUPCODE = "\rGroup";
	private final static String KEY_DATE_GRANULARITY = "\rDateGranularity";
	private final static String	KEY_DATASETFILTER = "\rDataSetFilter"; 
	
	public StatisticTypeCode(final Object key,
			                 final String longText,
			                 final int itemsPerPage,
			                 final String description,
			                 final Object ... properties) {
		super(key, longText, longText);
		setData(KEY_ITEMSPERPAGE, Integer.valueOf(itemsPerPage));
		setData(KEY_DESCRIPTION, description);
		setProperties(properties);
	}

	/**
	 * Constructor for streaming
	 */
	protected StatisticTypeCode(final int id, final Object key,
			                    final String shortText, final String longText) {
		super(id, key, shortText, longText);
	}

	/**
	 */
	private void setProperties(final Object ... properties) {
		PlotTypeCode type = PlotTypeCode.NONE;
		PlotOrientationCode orientation = null; //PlotOrientationCode.HORIZONTAL;
		PlotLegendCode legend = PlotLegendCode.SHOW;
		Integer rightCodeId = null;
		final ArrayList<CodeReference> subTypes = new ArrayList<CodeReference>(4);
		StatisticGroupCode groupCode = StatisticGroupCode.NULL;
		STDateGranularityCode granularity = STDateGranularityCode.DAY;
		DataSetFilterCode filterCode = null;
		final ArrayList<Object> objects = new ArrayList<Object>(); 
		for (int i=0; properties != null && i<properties.length; ++i) {
			final Object property = properties[i];
			if (property == null)
				continue;
			if (property instanceof PlotTypeCode) {
				type = (PlotTypeCode) property;
				if (orientation == null) {
					orientation = PlotTypeCode.LINE.equals(type) ? PlotOrientationCode.VERTICAL : PlotOrientationCode.HORIZONTAL;
				}
			} else if (property instanceof PlotOrientationCode) {
				orientation = (PlotOrientationCode) property;
			} else if (property instanceof PlotLegendCode) {
				legend = (PlotLegendCode) property;
			} else if (property instanceof STDateGranularityCode) {
				granularity = (STDateGranularityCode) property;
			} else if (property instanceof RightCode) {
				rightCodeId = Integer.valueOf(((RightCode) property).getId());
			} else if (property instanceof StatisticGroupCode) {
				groupCode = (StatisticGroupCode) property;
			} else if (property instanceof DataSetFilterCode) {
				filterCode = (DataSetFilterCode) property;
			} else if (property instanceof AbstractCode) {
				final CodeReference codeRef = new CodeReference((AbstractCode) property);
				if (!subTypes.contains(codeRef)) {
					subTypes.add(codeRef);
				}
			} else if (property instanceof CodeReference) {
				final CodeReference codeRef = (CodeReference) property;
				if (!subTypes.contains(codeRef)) {
					subTypes.add(codeRef);
				}
			} else {
				objects.add(property);
			}
		}
		setData(KEY_LEGENDCODE, legend);
		setData(KEY_ORIENTATIONCODE, orientation);
		setData(KEY_PLOTTYPECODE, type);
		setData(KEY_RIGHT_CODE_ID, rightCodeId);
		setData(KEY_GROUPCODE, groupCode);
		setData(KEY_DATE_GRANULARITY, granularity);
		setData(KEY_DATASETFILTER, filterCode);
		if (subTypes.size() > 0)
			setData(KEY_SUBTYPE1, subTypes.get(0));
		if (subTypes.size() > 1)
			setData(KEY_SUBTYPE2, subTypes.get(1));
		for (int i=0; i<objects.size(); ++i) {
			setData(KEY_DATA_N+i, objects.get(i));
		}
	}

	// --------------------------------------------------------------
	// ---
	// --- Permissions
	// ---
	// --------------------------------------------------------------
	/**
	 * Gets the right code this statistic type depends on.
	 * Returns null if no permission is required for this type.
	 */
	public final RightCode getRightCode() {
		final Integer rightCodeId = (Integer) getData(KEY_RIGHT_CODE_ID, null);
		if (rightCodeId == null) {
			return null;
		} else {
			return RightCode.getById(rightCodeId.intValue());
		}
	}

	/**
	 * Returns the description for this type
	 */
	public final String getDescription() {
		return (String) getData(KEY_DESCRIPTION, null);
	}

	/**
	 * Returns true if the logged in user is permitted to use
	 * the statistic type. If no right code was set for this type,
	 * it assumes no permission is needed and returns true.
	 * Likewise, if used on the server side, it always returns
	 * true.
	 */
	public final boolean isPermitted() {
		final RightCode rightCode = getRightCode();
		return rightCode == null ? true : rightCode.isPermitted();
	}

	// --------------------------------------------------------------
	// ---
	// --- Orientation
	// ---
	// --------------------------------------------------------------
	/**
	 * Gets the plot orientation for this type
	 */
	public final PlotOrientationCode getPlotOrientation() {
		return (PlotOrientationCode) getData(KEY_ORIENTATIONCODE, PlotOrientationCode.HORIZONTAL);
	}

	// --------------------------------------------------------------
	// ---
	// --- Legend
	// ---
	// --------------------------------------------------------------
	/**
	 * Returns true is the legend is to be shown
	 */
	public final boolean isLegendVisible() {
		final PlotLegendCode code = (PlotLegendCode) getData(KEY_LEGENDCODE, PlotLegendCode.SHOW);
		return PlotLegendCode.SHOW.equals(code);
	}

	// --------------------------------------------------------------
	// ---
	// --- Date from/upto granularity
	// ---
	// --------------------------------------------------------------
	public final STDateGranularityCode getDateGranularity() {
		Object o = getData(KEY_DATE_GRANULARITY, null);
		if (o instanceof STDateGranularityCode) {
			return (STDateGranularityCode) o;
		}
		return STDateGranularityCode.DAY;
	}

	// --------------------------------------------------------------
	// ---
	// --- Chart type
	// ---
	// --------------------------------------------------------------
	public boolean isPlotable() {
		final Object code = getData(KEY_PLOTTYPECODE, null);
		return code instanceof PlotTypeCode ? !PlotTypeCode.NONE.equals(code) : false;
	}

	public PlotTypeCode getPlotType() {
		final Object code = getData(KEY_PLOTTYPECODE, PlotTypeCode.NONE);
		return (PlotTypeCode) code;
	}

	public DataSetFilterCode getDataSetFilter() {
		return (DataSetFilterCode) getData(KEY_DATASETFILTER, null); 
	}

	// --------------------------------------------------------------
	// ---
	// --- Items per Page
	// ---
	// --------------------------------------------------------------
	/**
	 * Gets the number of items that can be fit onto one page
	 */
	public int getItemsPerPage() {
		final Object o = getData(KEY_ITEMSPERPAGE, null);
		return o instanceof Number ? ((Number) o).intValue() : 10;
	}
	
	// --------------------------------------------------------------
	// ---
	// --- Sub type code
	// ---
	// --------------------------------------------------------------
	/**
	 * Gets the sub type of this statistic type (legacy)
	 */
	public AbstractCode getSubType1() {
		final Object code = getData(KEY_SUBTYPE1, null);
		if (code instanceof AbstractCode) {
			return (AbstractCode) code;
		} else if (code instanceof CodeReference) {
			return ((CodeReference) code).getCode();
		}
		return null;
	}

	public AbstractCode getSubType2() {
		final Object code = getData(KEY_SUBTYPE2, null);
		if (code instanceof AbstractCode) {
			return (AbstractCode) code;
		} else if (code instanceof CodeReference) {
			return ((CodeReference) code).getCode();
		}
		return null;
	}

	// --------------------------------------------------------------
	// ---
	// --- Grouping
	// ---
	// --------------------------------------------------------------
	/**
	 * Gets the group code of this statistic type
	 */
	public StatisticGroupCode getGroupCode() {
		final StatisticGroupCode group = (StatisticGroupCode) getData(KEY_GROUPCODE, StatisticGroupCode.NULL);
		return group;
	}

}

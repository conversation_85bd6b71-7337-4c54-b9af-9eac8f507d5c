package ch.eisenring.statistics.shared.util;

import java.awt.Color;

import org.jfree.chart.JFreeChart;
import org.jfree.chart.LegendItemCollection;
import org.jfree.chart.axis.SubCategoryAxis;
import org.jfree.chart.plot.CategoryPlot;
import org.jfree.chart.renderer.category.GroupedStackedBarRenderer;

import ch.eisenring.core.tag.TagSet;
import ch.eisenring.statistics.shared.util.DataSetBuilder.GroupedDataSetResult;

public class AuslastungKWCustomizer extends ChartCustomizerBase {

	public final static Color COLOR_001M = new Color(0xFF880000);
	public final static Color COLOR_001O = new Color(0xFFAA0000);
	public final static Color COLOR_001E = new Color(0xFFCC0000);
	public final static Color COLOR_112 = new Color(0xFFEF0000);
	public final static Color COLOR_113 = new Color(0xFFFF3F3F);
	public final static Color COLOR_115 = new Color(0xFFFF7F7F);
	public final static Color COLOR_133 = new Color(0xFF2F9F9F);
	public final static Color COLOR_134 = new Color(0xFF3FCFCF);
	public final static Color COLOR_135M = new Color(0xFF6666FF);
	public final static Color COLOR_135O = new Color(0xFF7777FF);
	public final static Color COLOR_135E = new Color(0xFF8888FF);
	public final static Color COLOR_BEG = new Color(0xFFDF9FFF);
	public final static Color COLOR_GEZ = new Color(0xFF4FCF4F);
	public final static Color COLOR_GEW = new Color(0xFF4FCF4F);

	@Override
	public void customizeGroupedDataSetData(final GroupedDataSetResult dataSet,
											final String[] labels,
											final double[] values,
											final String firstColumn) {
		super.customizeGroupedDataSetData(dataSet, labels, values, firstColumn);
		//final String firstKW = firstColumn; //DataPoint.splitLabel(labels[0])[1];
		dataSet.dataSet.addValue(0D, "Bedarf FA 1.5 (0011)", firstColumn);
		dataSet.dataSet.addValue(0D, "Bedarf FA 1.5 (0015)", firstColumn);
		dataSet.dataSet.addValue(0D, "Bedarf FA 1.5 (0019)", firstColumn);
		dataSet.dataSet.addValue(0D, "Bedarf FA 1.5 (1120)", firstColumn);
		dataSet.dataSet.addValue(0D, "Bedarf FA 1.5 (1130)", firstColumn);
		dataSet.dataSet.addValue(0D, "Bedarf FA 1.5 (1150)", firstColumn);
		dataSet.dataSet.addValue(0D, "Bedarf FA 1.5 (1330)", firstColumn);
		dataSet.dataSet.addValue(0D, "Bedarf FA 1.5 (1340)", firstColumn);
		dataSet.dataSet.addValue(0D, "Bedarf FA 1.5 (1351)", firstColumn);
		dataSet.dataSet.addValue(0D, "Bedarf FA 1.5 (1355)", firstColumn);
		dataSet.dataSet.addValue(0D, "Bedarf FA 1.5 (1359)", firstColumn);
		dataSet.dataSet.addValue(0D, "Bedarf FA 1.5 (Begleitung)", firstColumn);
		dataSet.dataSet.addValue(0D, "Bedarf FA Anzahl (0011)", firstColumn);
		dataSet.dataSet.addValue(0D, "Bedarf FA Anzahl (0015)", firstColumn);
		dataSet.dataSet.addValue(0D, "Bedarf FA Anzahl (0019)", firstColumn);
		dataSet.dataSet.addValue(0D, "Bedarf FA Anzahl (1120)", firstColumn);
		dataSet.dataSet.addValue(0D, "Bedarf FA Anzahl (1130)", firstColumn);
		dataSet.dataSet.addValue(0D, "Bedarf FA Anzahl (1150)", firstColumn);
		dataSet.dataSet.addValue(0D, "Bedarf FA Anzahl (1330)", firstColumn);
		dataSet.dataSet.addValue(0D, "Bedarf FA Anzahl (1340)", firstColumn);
		dataSet.dataSet.addValue(0D, "Bedarf FA Anzahl (1351)", firstColumn);
		dataSet.dataSet.addValue(0D, "Bedarf FA Anzahl (1355)", firstColumn);
		dataSet.dataSet.addValue(0D, "Bedarf FA Anzahl (1359)", firstColumn);
		dataSet.dataSet.addValue(0D, "Bedarf FA Anzahl (Begleitung)", firstColumn);
		dataSet.dataSet.addValue(0D, "Gebucht (Gezählt)", firstColumn);
		//dataSet.dataSet.addValue(0D, "Gebucht (Gewichtet)", firstColumn);
	}

	@Override
	public void customizeGroupedStackedBarRenderer(final GroupedStackedBarRenderer renderer) {
		super.customizeGroupedStackedBarRenderer(renderer);

		// Bedarf FA 1.5
		int s = -1;
		renderer.setSeriesPaint(++s, COLOR_001M); // 001 Miet
		renderer.setSeriesPaint(++s, COLOR_001O); // 001 Obj
		renderer.setSeriesPaint(++s, COLOR_001E); // 001 Einzel
		renderer.setSeriesPaint(++s, COLOR_112); // 112
		renderer.setSeriesPaint(++s, COLOR_113); // 113
		renderer.setSeriesPaint(++s, COLOR_115); // 115
		renderer.setSeriesPaint(++s, COLOR_133); // 133
		renderer.setSeriesPaint(++s, COLOR_134); // 134
		renderer.setSeriesPaint(++s, COLOR_135M); // 135 Miet
		renderer.setSeriesPaint(++s, COLOR_135O); // 135 Obj
		renderer.setSeriesPaint(++s, COLOR_135E); // 135 Einzel
		renderer.setSeriesPaint(++s, COLOR_BEG); // Begleitung
		
		// Bedarf FA Anzahl
		renderer.setSeriesPaint(++s, COLOR_001M); // 001 Miet
		renderer.setSeriesPaint(++s, COLOR_001O); // 001 Obj
		renderer.setSeriesPaint(++s, COLOR_001E); // 001 Einzel
		renderer.setSeriesPaint(++s, COLOR_112); // 112
		renderer.setSeriesPaint(++s, COLOR_113); // 113
		renderer.setSeriesPaint(++s, COLOR_115); // 115
		renderer.setSeriesPaint(++s, COLOR_133); // 133
		renderer.setSeriesPaint(++s, COLOR_134); // 134
		renderer.setSeriesPaint(++s, COLOR_135M); // 135 Miet
		renderer.setSeriesPaint(++s, COLOR_135O); // 135 Obj
		renderer.setSeriesPaint(++s, COLOR_135E); // 135 Einzel
		renderer.setSeriesPaint(++s, COLOR_BEG); // Begleitung

		renderer.setSeriesPaint(++s, COLOR_GEZ); // Gebucht (Gezählt)
		renderer.setSeriesPaint(++s, COLOR_GEW); // Gebucht (Gewichtet)
	}
	
	@Override
	public void customizePlot(final CategoryPlot plot, final TagSet chartTags) {
		super.customizePlot(plot, chartTags);
		SubCategoryAxis domainAxis = new SubCategoryAxis(chartTags.get(ChartTags.X_AXIS_LABEL,  ""));
	    domainAxis.setCategoryMargin(0.05D);
		domainAxis.addSubCategory("FA 1.5 ");
		domainAxis.addSubCategory("FA Anz ");
		domainAxis.addSubCategory("Gebucht ");
		plot.setDomainAxis(domainAxis);
	}

	@Override
	public void customizeLegend(final JFreeChart chart) {
		super.customizeLegend(chart);
		final CategoryPlot plot = (CategoryPlot) chart.getPlot();
        final LegendItemCollection items = new LegendItemCollection();
        items.add(createLegendItem("M", COLOR_001M));
        items.add(createLegendItem("O", COLOR_001O));
        items.add(createLegendItem("E 001/2", COLOR_001E));
        // --- hide AbwArt 112/113, as they can't appear in results (currently)
        //items.add(createLegendItem("112", COLOR_112));
        //items.add(createLegendItem("113", COLOR_113));
        items.add(createLegendItem("115", COLOR_115));
        items.add(createLegendItem("133", COLOR_133));
        items.add(createLegendItem("K/G/E", COLOR_134));
        items.add(createLegendItem("M", COLOR_135M));
        items.add(createLegendItem("O", COLOR_135O));
        items.add(createLegendItem("E 135 ", COLOR_135E));
        items.add(createLegendItem("Begleitung", COLOR_BEG));
        items.add(createLegendItem("Gebucht", COLOR_GEZ));
		plot.setFixedLegendItems(items);
	}

}

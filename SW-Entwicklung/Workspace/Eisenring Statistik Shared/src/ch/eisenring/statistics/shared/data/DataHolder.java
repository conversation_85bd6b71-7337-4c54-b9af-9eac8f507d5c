package ch.eisenring.statistics.shared.data;

import java.io.IOException;

import ch.eisenring.core.collections.Collection;
import ch.eisenring.core.collections.Map;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.collections.impl.LinkedHashMap;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.io.StreamReader;
import ch.eisenring.core.io.StreamWriter;
import ch.eisenring.core.io.Streamable;
import ch.eisenring.statistics.shared.network.PacketStatisticResponse;

public final class DataHolder implements DataHolderInterface, Streamable {

	private final ArrayList<DataPoint> list = new ArrayList<>();
	private final Map<String, DataPoint> map = new LinkedHashMap<>(128);

	public DataHolder() {
		// TODO Auto-generated constructor stub
	}

	public DataHolder(final Collection<DataPoint> dataPoints) {
		for (final DataPoint dataPoint : dataPoints) {
			setDataPoint(dataPoint);
		}
	}

	protected void setDataPoint(final DataPoint dataPoint) {
		if (dataPoint == null || list.contains(dataPoint))
			return;
		list.add(dataPoint);
		map.put(dataPoint.getLabel(), dataPoint);
	}

	public void clear() {
		list.clear();
		map.clear();
	}

	/**
	 * Sets a DataPoint. If the point already existed, its value is
	 * replaced with the new value, removing whatever value existed.
	 */
	public void setDataPoint(final String label, final double value) {
		DataPoint dataPoint = map.get(label);
		if (dataPoint == null) {
			// insert new DataPoint
			dataPoint = new DataPoint(label, value);
			setDataPoint(dataPoint);
		} else {
			// replace existing DataPoint
			dataPoint.setValue(value);
		}
	}

	/**
	 * Adds a DataPoint. If the point already existed, the value
	 * is accumulated on the existing point.
	 */
	public void addDataPoint(final String label, final double value) {
		DataPoint dataPoint = map.get(label);
		if (dataPoint == null) {
			// create new DataPoint
			dataPoint = new DataPoint(label, value);
			setDataPoint(dataPoint);
	 	} else {
	 		// accumulate on existing DataPoint
	 		dataPoint.addValue(value);
	 	}
	}

	/**
	 * Gets the data values as double[] array
	 */
	public double[] getData() {
		final int l = list.size();
		final double[] result = new double[l];
		for (int i=0; i<l; ++i) {
			final DataPoint dataPoint = list.get(i);
			result[i] = dataPoint.getValue();
		}
		return result;
	}

	/**
	 * Gets the data labels as String[] array
	 */
	public String[] getLabels() {
		final int l = list.size();
		final String[] result = new String[l];
		for (int i=0; i<l; ++i) {
			final DataPoint dataPoint = list.get(i);
			result[i] = dataPoint.getLabel();
		}
		return result;
	}

	/**
	 * Updates a response packet with all the data series in holder
	 */
	public void updateResponse(final PacketStatisticResponse response) {
		final double[][] data = new double[1][];
		data[0] = getData();
		response.setDataSeries(data);
		response.setLabels(getLabels());
	}

	/**
	 * Gets a new DataHolder that contains only the series
	 * that match the specified by name(s).
	 */
	public DataHolder filter(final String ... seriesNames) {
		final DataHolder result = new DataHolder();
		if (seriesNames == null || seriesNames.length <= 0)
			return result;
		for (final DataPoint dataPoint : list) {
			for (final String seriesName : seriesNames) {
				if (seriesName == null)
					continue;
				if (Strings.equals(dataPoint.getSeriesName(), seriesName)) {
					final DataPoint copyPoint = new DataPoint(dataPoint);
					result.setDataPoint(copyPoint);
				}
			}
		}
		return result;
	}

	/**
	 * Gets data point value specified by key.
	 * 0 if no such point does not exist.
	 */
	public double getDataPointValue(final String key) {
		if (key == null)
			return 0D;
		for (final DataPoint point : list) {
			if (key.equals(point.getLabel())) {
				return point.getValue();
			}
		}
		return 0D;
	}

	// --------------------------------------------------------------
	// ---
	// --- Streamable implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public void read(final StreamReader reader) throws IOException {
		final int count = reader.readInt();
		for (int i=0; i<count; ++i) {
			final DataPoint dataPoint = new DataPoint();
			dataPoint.read(reader);
			setDataPoint(dataPoint);
		}
	}
	
	@Override
	public void write(final StreamWriter writer) throws IOException {
		int count = list.size();
		writer.writeInt(count);
		for (int i=0; i<count; ++i) {
			final DataPoint dataPoint = list.get(i);
			dataPoint.write(writer);
		}
	}

}

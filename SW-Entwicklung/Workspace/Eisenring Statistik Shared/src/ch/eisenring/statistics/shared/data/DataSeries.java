package ch.eisenring.statistics.shared.data;

import ch.eisenring.core.collections.Map;
import ch.eisenring.core.collections.impl.HashMap;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.io.AutoStreamable;
import ch.eisenring.core.io.AutoStreamed;

/**
 * Class representing a data series
 */
public final class DataSeries<T> implements AutoStreamable {

	@AutoStreamed
	String name;

	@AutoStreamed
	DataGroup group;
	
	@AutoStreamed
	final Map<T, DataValue> dataMap = new HashMap<T, DataValue>(1, 2F);
	
	DataSeries() {
	}

	DataSeries(final String name, final DataGroup group) {
		this.name = name;
		this.group = group == null ? DataGroup.DEFAULT_GROUP : group;
	}

	DataSeries(final DataSeries<T> source) {
		this.name = source.name;
		this.group = source.group;
		for (final Map.Entry<T, DataValue> entry : source.dataMap.entrySet()) {
			final T xAxisLabel = entry.getKey();
			final DataValue value = entry.getValue();
			dataMap.put(xAxisLabel, new DataValue(value));
		}
	}

	// --------------------------------------------------------------
	// ---
	// --- DataPoint modification
	// ---
	// --------------------------------------------------------------
	/**
	 * Sets the specified xAxisPosition to the specified value.
	 * This replaces any value that might have existed for the
	 * xAxisPosition before.
	 */
	public void setPoint(final T xAxisPosition, final double value) {
		DataValue dataValue = dataMap.get(xAxisPosition);
		if (dataValue == null) {
			dataValue = new DataValue(value);
			dataMap.put(xAxisPosition, dataValue);
		} else {
			dataValue.value = value;
		}
	}

	/**
	 * Adds the given delta to the data value at the specified
	 * xAxisPosition. If there is no value yet at the position,
	 * the existing value is assumed to be zero, putting delta
	 * value as the new value.
	 */
	public void addPoint(final T xAxisPosition, final double delta) {
		DataValue dataValue = dataMap.get(xAxisPosition);
		if (dataValue == null) {
			dataValue = new DataValue(delta);
			dataMap.put(xAxisPosition, dataValue);
		} else {
			dataValue.value += delta;
		}
	}

	/**
	 * Gets the current value of the point specified by xAxisPosition.
	 * If there is no current value, a default of zero is returned.
	 */
	public double getPoint(final T xAxisPosition) {
		final DataValue value = dataMap.get(xAxisPosition);
		return value == null ? 0D : value.value;
	}

	/**
	 * Gets the current value of the point specified by xAxisPosition.
	 * If there is no current value, the default value is returned.
	 */
	public double getPoint(final T xAxisPosition, final double defaultValue) {
		final DataValue value = dataMap.get(xAxisPosition);
		return value == null ? defaultValue : value.value;
	}

	/**
	 * Clears all data points in this series.
	 */
	public void clearValues() {
		dataMap.clear();
	}

	// --------------------------------------------------------------
	// ---
	// --- Object overrides
	// ---
	// --------------------------------------------------------------
	@Override
	public int hashCode() {
		return (Strings.hashCode(name) * 997) ^ group.hashCode();
	}

	@Override
	public boolean equals(final Object o) {
		if (o instanceof DataSeries) {
			final DataSeries<?> s = (DataSeries<?>) o;
			return Strings.equals(s.name, name) && s.group.equals(group);
		}
		return false;
	}

}

package ch.eisenring.statistics.shared.network;

import java.io.IOException;

import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.codetype.CodeType;
import ch.eisenring.core.io.StreamReader;
import ch.eisenring.core.io.StreamWriter;
import ch.eisenring.statistics.shared.codetables.StatisticGroupCode;
import ch.eisenring.statistics.shared.codetables.StatisticTypeCode;

/**
 * Packet sent by the server in response to an EnumerateCodesRequest.
 * Contains all the statistic codes the server understands.
 */
public final class PacketEnumerateCodesResponse extends AbstractSTSPacket {

	private PacketEnumerateCodesResponse() {
		super(CAPS_SEQUENCE_REPLY, PRIORITY_MAX);
	}
	
	public static PacketEnumerateCodesResponse create(final PacketEnumerateCodesRequest request) {
		final PacketEnumerateCodesResponse packet = new PacketEnumerateCodesResponse();
		packet.setReplyId(request);
		return packet;
	}

	// --------------------------------------------------------------
	// ---
	// --- AbstractPacket implementation
	// ---
	// --------------------------------------------------------------
	@Override
	protected void readImpl(final StreamReader reader) throws IOException {
		super.readImpl(reader);
		// just deserialize the code types
		CodeType.read(reader, false);
		CodeType.read(reader, false);
	}
	
	@Override
	protected void writeImpl(final StreamWriter writer) throws IOException {
		super.writeImpl(writer);
		CodeType codeType;
		codeType = AbstractCode.getCodeType(StatisticGroupCode.class);
		codeType.write(writer);
		codeType = AbstractCode.getCodeType(StatisticTypeCode.class);
		codeType.write(writer);
	}
	
}

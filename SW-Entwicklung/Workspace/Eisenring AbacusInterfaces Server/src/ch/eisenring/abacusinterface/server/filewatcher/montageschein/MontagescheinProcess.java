package ch.eisenring.abacusinterface.server.filewatcher.montageschein;

import ch.eisenring.abacusinterface.server.filewatcher.montageschein.model.*;
import ch.eisenring.abacusinterface.server.handler.EmailHandler;
import ch.eisenring.abacusinterface.server.handler.PrinterBatch;
import ch.eisenring.app.shared.ServiceNotFoundException;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.user.shared.service.USRService;
import ch.eisenring.user.shared.service.UserFacade;

import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

public class MontagescheinProcess {

    private static final String DEFAULT_EMAIL_RECIPIENT_ADDRESS = "<EMAIL>";
    private MontagescheinContext context;
    private File doneDir;
    private File errorDir;
    private int maxTries;
    private long retryInterval;


    public MontagescheinProcess(MontagescheinContext context, File doneDir, File errorDir, int maxTries, long retryInterval) {
        this.context = context;
        this.doneDir = doneDir;
        this.errorDir = errorDir;
        this.maxTries = maxTries;
        this.retryInterval = retryInterval;
    }

    /**
     * The transaction is processed as defined in the XML following the different level of the transaction structures
     * To be printed are the following documents in the order below:
     * - Team Overview
     * - Project Documents (the project documents for all projects of a team are to be printed after the Team Overview)
     * - Order Documents (All order documents are to be printed after the project documents. The order must be based on the start value of the Order.
     *
     * @throws Exception
     */
    public void process() throws Exception {
        try {
            processTransaction();
            updateTransaction(TransactionStatus.OK);
            deleteAllFiles();
        } catch (Exception e) {
            Logger.error("NSW Batch Error: Print Job für " + context.getXmlFile().getName() + " konnte nicht verarbeitet werden " + e.getMessage());
            updateTransaction(TransactionStatus.ERROR);
            deleteAllFiles();
            context.getMessageHandler().addMessage(context, "ERROR : PrintJob für " + context.getXmlFile().getName() + " konnte nicht verarbeitet werden. Files evt. müssen manuell gelöscht werden.", context.getXmlFile().getName());
            context.getMessageHandler().addMessage(context, "ERROR : Exception für PrintJob von " + context.getXmlFile().getName() + e.getMessage() + " " + e.getCause());
        }
        sendEmailWithoutAttachment();
    }

    /**
     * Prints all documents as defined by the transaction
     */
    private void processTransaction() {
        for (Company company : context.getTransaction().getCompanies()) {
            processCompany(company);
        }
    }

    private void processCompany(Company company) {
        for (Team team : company.getTeams()) {
            processTeam(team);
        }
    }

    private void processTeam(Team team) {
        context.printTeamDocuments(team);
        for (Project project : team.getProjects()) {
            List<Order> openOrderList = new ArrayList<>();
            processProject(project);
            openOrderList.addAll(project.getOrders().stream().filter(o -> o.isOpen()).collect(Collectors.toList()));
            openOrderList.sort(Comparator.comparing(Order::getStart));
            for (Order order : openOrderList) {
                Logger.info("NSW Batch: Starte Verarbeitung für " + order.getRefKomm());
                processOrder(order);
            }
        }
    }

    private void processProject(Project project) {
        context.printProjectDocuments(project);
    }

    /**
     * Is processing an order of a print request
     *
     * @param order to be processed
     */
    private void processOrder(Order order) {
        try {
            for (int i = 0; i < maxTries && order.isOpen(); i++) {
                Logger.info("NSW Batch: Prozessiere Auftrag für " + order.getRefKomm());
                if (context.validate(order)) {
                    Logger.info("NSW Batch: Drucke für " + order.getRefKomm());
                    context.printOrderDocuments(order);
                    order.setTaskStatus(OrderTaskStatus.COMPLETED);
                } else {
                    Logger.info("NSW Batch: Auftrag für " + order.getRefKomm() + " nicht ready. Retry-Status: " + i + "/" + maxTries);
                    Thread.sleep(retryInterval);
                }
            }
            if (order.isOpen()) {
                Logger.info("NSW Batch: Auftrag  konnte nicht verarbeitet werden " + order.getRefKomm());
                order.setTaskStatus(OrderTaskStatus.ERROR);
            }
        } catch (Exception e) {
            order.setTaskStatus(OrderTaskStatus.ERROR);
            Logger.error("NSW Batch: Eine Exception ist bei " + order.getRefKomm() + " aufgetreten: " + e.getMessage());
        }
    }

    /**
     * Saves the transaction response into a designated folder to complete a print request
     *
     * @throws Exception in case of failure of decoding xml
     */
    private void updateTransaction(TransactionStatus transactionStatus) throws Exception {
        context.getTransaction().setType(TransactionType.RESPONSE);
        context.getTransaction().setTimestamp(LocalDateTime.now());
        File xmlFile = context.getXmlFile();
        if (TransactionStatus.OK.equals(transactionStatus)) {
            TransactionBuilder.writeTransaction(context.getTransaction(), new File(doneDir, xmlFile.getName()));
        } else {
            TransactionBuilder.writeTransaction(context.getTransaction(), new File(errorDir, xmlFile.getName()));
        }
    }

    /**
     * Collects all files provided by Abacus for a print transaction and deletes them from the input folder
     */
    private void deleteAllFiles() {
        List<String> filesToBeDeleted = new ArrayList<>();
        //Gathering files
        filesToBeDeleted.add(context.getXmlFile().getName());
        if (context.getTransaction().getOverview() != null) {
            filesToBeDeleted.add(context.getTransaction().getOverview());
        }
        for (Company company : context.getTransaction().getCompanies()) {
            filesToBeDeleted.add(company.getOverview());
            for (Team team : company.getTeams()) {
                filesToBeDeleted.add(team.getOverview());
                for (Project project : team.getProjects()) {
                    for (Order order : project.getOrders()) {
                        if (order.getAbacusDokument() != null) {
                            filesToBeDeleted.add(order.getAbacusDokument());
                        }
                    }
                }
            }
        }
        //Delete
        for (String fileToBeDeleted : filesToBeDeleted) {
            if (fileToBeDeleted != null) {
                try {
                    File file = context.getFile(fileToBeDeleted);
                    if (file != null && file.exists()) {
                        Logger.info("NSW Batch: Lösche File " + file.getName());
                        file.delete();
                    }
                } catch (Exception e) {
                    Logger.info("NSW Batch: File konnte nicht gelöscht werden " + fileToBeDeleted);
                }
            }
        }

    }

    /**
     * Sends out an email without the printed documents attached to the person having requested the print.
     */
    private void sendEmailWithoutAttachment() {
        EmailHandler.sendWithoutAttachment(context.getServer(), "<EMAIL>", getRecipientAddress(), "Abacus Batch Druck Report", createEmailResponseBody());
    }

    /**
     * Sends out an email the documents having been printed attached to the person having requested the print.
     */
    private void sendEmailWithAttachment(Collection<PrinterBatch> printerBatches) throws IOException {
        File folder = new File(System.getProperty("user.home"), "Downloads");
        Collection<File> files = new ArrayList<>();
        for (PrinterBatch printerBatch : printerBatches) {
            File file = new File(folder, String.format("AbacusBatchDruck_%s_%tF.pdf", printerBatch.getName(), System.currentTimeMillis()));
            printerBatch.savePdf(file);
            files.add(file);
        }
        EmailHandler.sendWithFileAttachments(context.getServer(), "<EMAIL>", getRecipientAddress(), "Abacus Batch Druck", createEmailResponseBody(), files.toArray(new File[files.size()]));
    }

    /**
     * Gets the recipient address for the email response to a print request. Defaults to a predefined address in case no user address can be derived.
     *
     * @return the users email or a fallback emal
     */
    private String getRecipientAddress() {
        USRService userService;
        try {
            userService = context.getServer().locateService(USRService.class);
        } catch (final ServiceNotFoundException e) {
            return DEFAULT_EMAIL_RECIPIENT_ADDRESS;
        }

        UserFacade user = userService.getUser(context.getTransaction().getProcessUser());
        if (user != null) {
            String userEmailAddress = user.getEMailAddress();
            if (userEmailAddress != null && userEmailAddress.trim().length() > 0) {
                return userEmailAddress;
            }
        }
        return DEFAULT_EMAIL_RECIPIENT_ADDRESS;
    }

    /**
     * Creates the email body for the response email.
     *
     * @return email body
     */
    private String createEmailResponseBody() {
        StringBuilder msg = new StringBuilder();
        msg.append("<html><body style=\"font-family: Verdana; font-size: 10pt;\">");
        msg.append("<h1>Abacus Batch Druck Report</h1>");
        for (java.util.Map.Entry<String, java.util.Collection<String>> entry : context.getMessageHandler().getMessageEntries()) {
            msg.append("<div>").append(entry.getKey()).append(":</div><ul>");
            for (String message : entry.getValue()) {
                msg.append("<li>").append(message).append("</li>");
            }
            msg.append("</ul>");
        }
        msg.append("</body></html>");
        return msg.toString();
    }

}

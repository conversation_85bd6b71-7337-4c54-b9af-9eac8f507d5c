package ch.eisenring.app.server.network;

import ch.eisenring.app.server.ServerComponent;
import ch.eisenring.app.shared.network.AbstractAPPPacketHandler;
import ch.eisenring.network.implementation.PacketDispatchMode;
import ch.eisenring.network.packet.AbstractPacket;

public abstract class AbstractPacketHandler<T extends ServerComponent, P extends AbstractPacket> extends AbstractAPPPacketHandler<P> {

	protected final T server;

	protected AbstractPacketHandler(final T server,
			                        final Class<P> packetClass,
			                        final PacketDispatchMode dispatchMode) {
		super(packetClass, dispatchMode);
		this.server = server;
	}

}

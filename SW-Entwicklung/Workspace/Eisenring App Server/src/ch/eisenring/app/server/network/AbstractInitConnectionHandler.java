package ch.eisenring.app.server.network;

import java.io.File;
import java.io.IOException;

import ch.eisenring.app.shared.network.PacketApplicationFingerPrint;
import ch.eisenring.app.shared.update.FileFingerPrint;
import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.core.logging.Logger.LogLevel;
import ch.eisenring.core.threading.ThreadCore;
import ch.eisenring.network.StreamableConnection;
import ch.eisenring.network.ConnectionProperties;
import ch.eisenring.network.PacketSink;
import ch.eisenring.network.handler.PacketHandler;
import ch.eisenring.network.implementation.ConnectionStatusCode;
import ch.eisenring.network.implementation.PacketDispatchMode;
import ch.eisenring.network.packet.AbstractPacket;
import ch.eisenring.network.packet.PacketErrorMessage;
import ch.eisenring.network.packet.PacketHandshakeBase;
import ch.eisenring.network.packet.PacketNOP;
import ch.eisenring.network.update.FileImage;

public abstract class AbstractInitConnectionHandler<T extends AbstractPacket> implements PacketHandler<T> {

	private final Class<T> packetClass;

	protected AbstractInitConnectionHandler(final Class<T> packetClass) {
		this.packetClass = packetClass;
	}

	@Override
	public final Class<T> getPacketClass() {
		return packetClass;
	}
	
	@Override
	public final PacketDispatchMode getPacketDispatchMode() {
		return PacketDispatchMode.SYNCHRONOUS;
	}

	/**
	 * Gets the directory where the update files for the
	 * client are located (on the server!)
	 */
	public abstract File getUpdateDir();

	/**
	 * Gets the files that need update on the client
	 */
	public List<FileImage> getUpdateFiles(final List<FileFingerPrint> clientFingerPrints) throws IOException {
		final File updateDir = getUpdateDir();
		return getUpdateFiles(clientFingerPrints, updateDir);
	}

	/**
	 * Gets the files that need update on the client
	 */
	public List<FileImage> getUpdateFiles(final List<FileFingerPrint> clientFingerPrints, final File updateDir) throws IOException {
		final List<FileImage> updateFiles = new ArrayList<>();
		final List<FileFingerPrint> updateFingerPrints = PacketApplicationFingerPrint.getFingerPrints(updateDir, null);
		for (final FileFingerPrint current : updateFingerPrints) {
			final FileFingerPrint client = current.find(clientFingerPrints);
			if (client == null ||
				!client.isSameVersion(current)) {
				// the file does not exist on the client,
				// or the server version differs from the client version
				final FileImage fileImage = FileImage.create(
						updateDir.getAbsolutePath(), current.getPath());
				updateFiles.add(fileImage);
			}
		}
		return updateFiles;
	}

	protected void setConnectionProperties(final PacketHandshakeBase packet, final PacketSink connection) {
		final ConnectionProperties properties = connection.getConnectionProperties();
		if (properties != null) {
			properties.set(ConnectionProperties.LOGIN_USER, packet.getUserName());
			properties.set(ConnectionProperties.APP_NAME, packet.getAppName());
			properties.set(ConnectionProperties.APP_VERSION, packet.getAppVersionString());
			properties.set(ConnectionProperties.COMPUTER_NAME, packet.getClientName());
			properties.set(ConnectionProperties.JAVA_VERSION, packet.getJVMVersionString());
		}
	}

	/**
	 * Dump the packets general information (client & version)
	 * to the log
	 */
	protected void log(final PacketHandshakeBase packet,
					   final Object connection,
					   final String prefix) {
		if (packet == null) {
			Logger.error("packet == null");
		} else if (Logger.isEnabled(LogLevel.INFO)) {
			final StringMaker b = StringMaker.obtain();
			b.append('\n');
			b.append(prefix);
			b.append(" - Client Application: ");
			b.append(packet.getAppName());
			b.append('\n');

			b.append(prefix);
			b.append(" - Client Version: ");
			b.append(packet.getAppVersionString());
			b.append('\n');

			b.append(prefix);
			b.append(" - Client User: ");
			b.append(packet.getUserName());
			b.append('\n');

			b.append(prefix);
			b.append(" - Client Machine: ");
			b.append(packet.getClientName());
			b.append('\n');

			b.append(prefix);
			b.append(" - Client Java Version: ");
			b.append(packet.getJVMVersionString());

			if (connection instanceof StreamableConnection) {
				b.append('\n');
				b.append(prefix);
				b.append(" - Client IP-Address: ");
				b.append(((StreamableConnection) connection).getRemoteAddress());
			}
			Logger.info(b);
			b.releaseSilent();
		}
	}

	protected void replyAndTerminate(final PacketSink sink,
									 final AbstractPacket reply) {
		sink.sendPacket(reply, true);
		try {
			ThreadCore.sleep(250);
			sink.sendPacket(PacketNOP.create(), true);
			ThreadCore.sleep(250);
			sink.sendPacket(PacketNOP.create(), true);
			ThreadCore.sleep(250);
			sink.sendPacket(PacketNOP.create(), true);
			ThreadCore.sleep(250);
		} catch (final Exception e) {
		} finally {
			((StreamableConnection) sink).terminate(
					ConnectionStatusCode.CON_CLOSED,
					"Version Conflict",
					null);
		}
	}

	protected void sendReadOnlyWarning(final PacketSink sink) {
		sink.sendPacket(PacketErrorMessage.create(new ErrorMessage(
				"Diese Serverversion ist im READONLY-Modus compiliert." +
				" \nIhre Änderungen werden möglicherweise nicht (oder nur teilweise) gespeichert." +
				" \nArbeiten Sie auf keinen Fall mit dem Programm. Beenden Sie es jetzt sofort und" +
				" \nverständigen Sie den Administrator/Support!")));
	}

}


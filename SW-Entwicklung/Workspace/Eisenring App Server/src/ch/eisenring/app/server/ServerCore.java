package ch.eisenring.app.server;

import ch.eisenring.app.server.model.ModelServiceServer;
import ch.eisenring.app.server.network.ConnectionManager;
import ch.eisenring.app.server.network.ServerCorePacketDispatcher;
import ch.eisenring.app.server.network.ServerSocketProvider;
import ch.eisenring.app.server.services.DataFileServiceServer;
import ch.eisenring.app.server.services.ErrorReportingServiceImpl;
import ch.eisenring.app.shared.CoreStatistics;
import ch.eisenring.app.shared.SoftwareCore;
import ch.eisenring.core.application.Configuration;
import ch.eisenring.core.application.ConfigurationException;
import ch.eisenring.core.application.SoftwareDescriptor;
import ch.eisenring.core.collections.Collection;
import ch.eisenring.core.feature.FeatureManager;
import ch.eisenring.core.io.Streams;
import ch.eisenring.core.io.stats.StreamStatsManager;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.core.usersettings.GenericUserSettings;
import ch.eisenring.network.MasterPacketDispatcher;
import ch.eisenring.network.PacketBroadcaster;
import ch.eisenring.network.packet.AbstractPacket;

public final class ServerCore extends SoftwareCore<ServerComponent> implements PacketBroadcaster {

	// --------------------------------------------------------------
	// ---
	// --- Public accessible core states
	// ---
	// --------------------------------------------------------------
	public final ServerCorePacketDispatcher DISPATCHER = new ServerCorePacketDispatcher(this);

	/**
	 * Must be located through locateService()
	 */
	private final ErrorReportingServiceImpl ERROR_REPORTING = new ErrorReportingServiceImpl(this);

	public ServerCore(final SoftwareDescriptor descriptor) {
		super(descriptor);
		// enable treating missing packet handler as error
		MasterPacketDispatcher.INSTANCE.setMissingHandlerCausesError(true);
		// register core features
		((FeatureManager) getFeatureLookup()).addFeatures(
			DISPATCHER, ERROR_REPORTING);
		// these components are integrated with the core and always available
		addComponent(new DataFileServiceServer());
		addComponent(new ModelServiceServer());
	}

	@Override
	public boolean isClient() {
		return false;
	}

	@Override
	public boolean isServer() {
		return true;
	}

	@Override
	protected void startNetwork() throws Exception {
		super.startNetwork();
		// starts all server sockets
		final Collection<ServerSocketProvider> sockets = getFeatureLookup().getFeatures(ServerSocketProvider.class);
		for (final ServerSocketProvider socket : sockets) {
			socket.start();
		}
	}

	@Override
	protected void stopNetwork() {
		super.stopNetwork();
		final Collection<ServerSocketProvider> sockets = getFeatureLookup().getFeatures(ServerSocketProvider.class);
		for (final ServerSocketProvider socket : sockets) {
			Streams.closeSilent(socket);
		}
	}

	// --------------------------------------------------------------
	// ---
	// --- Component management
	// ---
	// --------------------------------------------------------------
	/**
	 * Attempts to create and add a component of the given class.
	 * The class must provide a default constructor for this to succeed.
	 */
	public <U extends ServerComponent> boolean addComponent(final Class<U> componentClass) {
		try {
			final U component = componentClass.newInstance();
			if (component != null) {
				addComponent(component);
				return true;
			}
		} catch (final Exception e) {
			// ignore
		}
		return false;
	}

	/**
	 * Attempts to create and add a component by qualified class name.
	 * This call is useful if the server components are to be configured
	 * by configuration file etc.
	 * The class must be a sub-type of ServerComponent and provide a default constructor.
	 */
	public boolean addComponent(final String qualifiedClassName) {
		try {
			final Class<?> componentClass = Class.forName(qualifiedClassName);
			// check if it is a ServerComponent
			if (!ServerComponent.class.isAssignableFrom(componentClass))
				return false;
			// attempt to create an instance of class
			final ServerComponent component = (ServerComponent) componentClass.newInstance();
			addComponent(component);
			return true;
		} catch (final Exception e) {
			// ignore
		}
		return false;
	}

	// --------------------------------------------------------------
	// ---
	// --- Startup / Shutdown
	// ---
	// --------------------------------------------------------------
	@Override
	protected void launch(final ServerComponent component) throws Exception {
		boolean exception = true;
		Logger.info(">>>>> launching server component {}", component);
		try {
			component.launch();
			exception = false;
		} finally {
			if (exception) {
				Logger.fatal("<<<<< launch of server component {} failed", component);
			} else {
				Logger.info("<<<<< launch of server component {} completed", component);
			}
		}
	}

	@Override
	protected void shutdown(final ServerComponent component) throws Exception {
		component.shutdown();
	}

	public static void awaitEndOfWorld() {
		// ensure main thread does not exit (server may consist of daemon threads only!)
		final Object awaitEOW = new Object();
		while (true) {
			try {
				// sleep forever
				synchronized (awaitEOW) {
					awaitEOW.wait();
				}
			} catch (final InterruptedException e) {
				break;
			}
		}
	}

	// --------------------------------------------------------------
	// ---
	// --- Connection management
	// ---
	// --------------------------------------------------------------
	private final ConnectionManager connectionManager = new ConnectionManager(getThreadPool());

	/**
	 * Broadcasts given packet to all compatible connections.
	 */
	@Override
	public void broadcast(final AbstractPacket packet, final boolean waitForSend) {
		final PacketBroadcaster broadcaster = connectionManager;
		broadcaster.broadcast(packet, waitForSend);
	}

	/**
	 * Gets the connection manager for this server instance
	 */
	public ConnectionManager getConnectionManager() {
		return connectionManager;
	}

	@Override
	public void configure(final Configuration configuration) throws ConfigurationException {
		super.configure(configuration);
		ERROR_REPORTING.configure(configuration);
		StreamStatsManager.configure(configuration.subConfiguration("StreamStatistics:"));
	}

	// --------------------------------------------------------------
	// ---
	// --- UserSettings
	// ---
	// --------------------------------------------------------------
	@Override
	protected GenericUserSettings createUserSettings() {
		// server has no user settings
		throw new IllegalStateException("UserSettings not available on server side");
	}

	/**
	 * Specialize getCoreStatistics() for server.
	 */
	@Override
	public CoreStatistics getCoreStatistics() {
		final CoreStatistics result = super.getCoreStatistics();
		final ConnectionManager manager = getConnectionManager();
		result.setOpenConnections(manager.getOpenCount());
		result.setTotalConnections((int) manager.getTotalCount());
		result.addThroughput(manager.getThroughput());
		return result;
	}

}

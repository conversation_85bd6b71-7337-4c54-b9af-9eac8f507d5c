package ch.eisenring.app.server.network;

import ch.eisenring.app.server.ServerCore;
import ch.eisenring.network.DynamicPacketDispatcherBase;
import ch.eisenring.network.handler.PacketHandler;

public final class ServerCorePacketDispatcher extends DynamicPacketDispatcherBase {

	public ServerCorePacketDispatcher(final ServerCore serverCore) {
		//super(serverCore);
	}

	@Override
	public void addHandler(final PacketHandler<?> handler) {
		super.addHandler(handler);
	}

}
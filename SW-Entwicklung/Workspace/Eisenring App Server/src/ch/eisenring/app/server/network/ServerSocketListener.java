package ch.eisenring.app.server.network;

import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.ServerSocket;
import java.net.Socket;
import java.util.concurrent.atomic.AtomicReference;

import ch.eisenring.app.server.ServerComponent;
import ch.eisenring.core.io.Streams;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.core.threading.ThreadPool;
import ch.eisenring.network.NetworkConnection;
import ch.eisenring.network.security.NetworkSecurityManager;

/**
 * Base class for server sockets accepting incomming connections
 */
public class ServerSocketListener<T extends ServerComponent> implements ServerSocketProvider {

	protected final String name;
	protected final T server;

	protected int port = -1;
	protected ServerSocket serverSocket;
	protected NetworkSecurityManager securityManager;

	/** The thread running the accept loop */
	protected final AtomicReference<Thread> acceptThread = new AtomicReference<>();

	public ServerSocketListener(final String name, final T server, final NetworkSecurityManager securityManager) {
		this.name = name;
		this.server = server;
		this.securityManager = securityManager;
	}

	/**
	 * This hook is internally called to create the server socket.
	 * 
	 * It must be implemented to create the desired kind of ServerSocket (e.g. SSLServerSocket).
	 * The default implementation returns a standard ServerSocket instance.
	 */
	protected ServerSocket createServerSocket() throws IOException {
		return new ServerSocket();
	}

	/**
	 * Called when a client connects to this socket.
	 * 
	 * Must build the infrastructure to manage the connection, including
	 * registering necessary data structures somewhere and initiate all
	 * required processing.
	 * 
	 * The default implementation creates a standard ClientConnection and adds
	 * it to the system core connection manager.
	 */
	protected final void accept(final Socket socket) throws IOException {
		final ConnectionManager manager = server.getCore().getConnectionManager();
		final NetworkConnection connection = createConnection(socket);
		manager.addConnection(connection);
	}

	protected NetworkConnection createConnection(final Socket socket) throws IOException {
		return new ClientConnection<>(socket, server, securityManager);
	}

	/**
	 * Attempts to set the accept thread reference
	 */
	final boolean setAcceptThread(final Thread thread) {
		if (thread == null) {
			acceptThread.set(null);
			return true;
		}
		return acceptThread.compareAndSet(null, thread)
				|| acceptThread.compareAndSet(thread, thread);
	}

	/**
	 * Internal connection accepting loop. This runs in a separate thread.
	 */
	final void acceptLoop() {
		final Thread thread = Thread.currentThread();
		if (!setAcceptThread(thread)) {
			Logger.error("Problem starting accept loop, already running");
			return;
		}
		try {
			while (thread == acceptThread.get()) {
				try {
					final Socket socket = serverSocket.accept();
					accept(socket);
				} catch (final Throwable e) {
					// if terminating, ignore the error...
					if (acceptThread.get() == thread) {
						Logger.error("error accepting client connection");
						Logger.error(e);
					}
				}
			}
		} finally {
			acceptThread.compareAndSet(null, thread);
			Logger.info("{} socket accept listener has terminated", this);
		}
	}
	
	/**
	 * Returns true as long as this socket is accepting connections.
	 */
	public final boolean isAlive() {
		return acceptThread.get() != null;
	}

	/**
	 * Sets the port number this socket listens to
	 */
	@Override
	public void setPort(final int port) {
		if (this.port == port)
			return;
		this.port = port;
		if (!isAlive())
			return;
		Streams.closeSilent(this);
		try {
			start();
		} catch (final IOException e) {
			Logger.error("{}: restart after port change failed", this);
		}
	}

	public static boolean isValidPort(final int port) {
		return port > 0 && port <= 65535;
	}

	// --------------------------------------------------------------
	// ---
	// --- Feature implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public String getFeatureName() {
		return name;
	}

	// --------------------------------------------------------------
	// ---
	// --- Implements Closeable
	// ---
	// --------------------------------------------------------------
	/**
	 * Shuts down this server socket if its alive, otherwise NO-OP.
	 */
	@Override
	public final void close() throws IOException {
		final Thread thread = acceptThread.get();
		if (acceptThread.compareAndSet(thread, null)) {
			if (thread == null)
				return;
			Logger.info("Signaling {} socket accept listener thread to shut down", this);
			acceptThread.set(null);
			try {
				serverSocket.close();
			} catch (final Throwable t) {
				// ignore shutdown errors
			} finally {
				serverSocket = null;
			}
			thread.interrupt();
		}
	}
	
	/**
	 * Starts this socket listener.
	 * Does nothing if the currently configured port number is invalid.
	 */
	public final void start() throws IOException {
		if (!isValidPort(port)) {
			Logger.info("{}: is disabled (port specification invalid)", this);
			return;
		} 
		Logger.info("{}: opening server socket on port: {}", this, port);
		serverSocket = createServerSocket();
		serverSocket.setReceiveBufferSize(1 << 16);
		serverSocket.bind(new InetSocketAddress(port));
		Logger.info("{}: bound server socket to port: {}", this, serverSocket.getLocalPort());
		// start the client listener thread
		final Runnable task = new Runnable() {
			@Override
			public void run() {
				acceptLoop();
			}
		}; 
		final ThreadPool threadPool = server.getCore().getThreadPool();
		final Thread thread = threadPool.start(task, name, Thread.MAX_PRIORITY);
		if (setAcceptThread(thread)) {
			Logger.info("{}: starting socket accept listener thread", this);
		} else {
			Logger.error("{}: problem starting accept thread: already running", this);
		}
	}
	
	@Override
	public String toString() {
		return name;
	}

}

package ch.eisenring.app.server.services;

import ch.eisenring.app.shared.Service;
import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.util.file.FileImage;

public interface ErrorReportingService extends Service {

	@Override
	public default String getFeatureName() {
		return "ErrorReportingService";
	}

	@Override
	public default Class<ErrorReportingService> getFeatureType() {
		return ErrorReportingService.class;
	}

	public void reportError(final String subject, final String text,
			final java.util.Collection<ErrorMessage> messages,
			final java.util.Collection<FileImage> files);

	public void reportError(final String subject, final String text,
			final java.util.Collection<ErrorMessage> messages,
			final java.util.Collection<FileImage> files,
			final java.util.Collection<String> recipients);
	
}

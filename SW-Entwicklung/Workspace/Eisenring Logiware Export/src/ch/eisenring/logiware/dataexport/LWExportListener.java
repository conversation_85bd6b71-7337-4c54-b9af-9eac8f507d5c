package ch.eisenring.logiware.dataexport;

import ch.eisenring.core.collections.List;
import ch.eisenring.core.feature.Feature;
import ch.eisenring.lw.export.LWExportAuftrag;

/**
 * Callback interface used to notify listeners when "Auftrag" has been written
 * back to Logiware. The listener is notified after the data has been committed
 * to the Logiware database.
 * 
 * Listeners need to be published as Features by the listening Component
 * (and are automatically discovered through FeatureLookup when needed) 
 */
public interface LWExportListener extends Feature {

	// --------------------------------------------------------------
	// ---
	// --- Interface API
	// ---
	// --------------------------------------------------------------
	/**
	 * Callback where all data that has been written is exposed to the listener.
	 * The listener may not modify the data or the list. Data has already been
	 * committed to the Logiware database.
	 * 
	 * The data in the passed objects is considered current and valid,
	 * although there is only a simple lock that blocks concurrent
	 * exports - nothing else. 
	 */
	public void exported(final List<LWExportAuftrag> exportAuftraege);

	// --------------------------------------------------------------
	// ---
	// --- Feature default implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public default Class<LWExportListener> getFeatureType() {
		return LWExportListener.class;
	}

	@Override
	public default String getFeatureName() {
		return "LWExportListener";
	}

}

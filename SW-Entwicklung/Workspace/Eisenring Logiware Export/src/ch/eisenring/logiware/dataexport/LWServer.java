package ch.eisenring.logiware.dataexport;

import ch.eisenring.app.server.AbstractServerComponent;
import ch.eisenring.app.shared.Configurable;
import ch.eisenring.core.application.Configuration;
import ch.eisenring.core.application.ConfigurationException;
import ch.eisenring.core.collections.Collection;
import ch.eisenring.core.feature.Feature;
import ch.eisenring.core.observable.Observable;
import ch.eisenring.core.platform.Platform;
import ch.eisenring.core.platform.PlatformPath;
import ch.eisenring.jdbc.ConnectionInfo;
import ch.eisenring.lw.LWConstants;

import java.io.File;

public final class LWServer extends AbstractServerComponent implements Configurable {

	// --------------------------------------------------------------
	// ---
	// --- Server globals
	// ---
	// --------------------------------------------------------------
	public final Observable<ConnectionInfo> VERTRIEB_CONNECTION_LEGACY =
		Observable.create(false, "LogiwareConnectionInfo", null);

	// --------------------------------------------------------------
	// ---
	// --- Feature management
	// ---
	// --------------------------------------------------------------
	private final Feature[] features = {
			new LogiwareServerService(this)
		};

	public Feature[] getFeatures() {
		return features;
	}

	// --------------------------------------------------------------
	// ---
	// --- Configurable Implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public void getConfigurationFileNames(final Collection<String> fileNames) {
		fileNames.add("Logiware.cfg");
		fileNames.add(new File(Platform.getPlatform().getPath(PlatformPath.SETTINGS), "LogiwareServerUser.cfg").getAbsolutePath());
	}

	@Override
	public void configure(final Configuration configuration) throws ConfigurationException {
		VERTRIEB_CONNECTION_LEGACY.set(ConnectionInfo.create("LogiwareVertriebLegacy:", configuration));

		LWConstants.BASIS.setConnectionInfo(ConnectionInfo.create("LogiwareBasis:", configuration));
		LWConstants.VERTRIEB.setConnectionInfo(ConnectionInfo.create("LogiwareVertrieb:", configuration));
		LWConstants.LWX.setConnectionInfo(ConnectionInfo.create("LogiwareExtension:", configuration));
	}

	// --------------------------------------------------------------
	// ---
	// --- Launch / Shutdown
	// ---
	// --------------------------------------------------------------

	// no specialized startup / shutdown

	// --------------------------------------------------------------
	// ---
	// --- Public API
	// ---
	// --------------------------------------------------------------
	public LWExportListener[] getExportListeners() {
		final Collection<LWExportListener> listeners = getCore().getFeatureLookup().getFeatures(LWExportListener.class);
		return Collection.toArray(listeners, LWExportListener.class);
	}

}

package ch.eisenring.lw.export;

import static ch.eisenring.lw.LWConstants.VERTRIEB;
import ch.eisenring.core.util.VarArgs;
import ch.eisenring.logiware.LWAuftragKey;
import ch.eisenring.lw.meta.LWAuftragKopfMeta;
import ch.eisenring.lw.meta.LWAuftragMeta;
import ch.eisenring.lw.meta.LWProjektMeta;
import ch.eisenring.model.MetaAttribute;
import ch.eisenring.model.MetaClass;
import ch.eisenring.model.MetaClassMember;
import ch.eisenring.model.MetaProperty;
import ch.eisenring.model.engine.jdbc.ColumnSpecifier;
import ch.eisenring.model.engine.jdbc.TableSpecifier;
import ch.eisenring.model.factory.AttributeFactory;

interface LWExportAuftragMeta {

	TableSpecifier TABLE_X = TableSpecifier.get(VERTRIEB, "HEAG_Dispo_to_Logiware");
	TableSpecifier TABLE_A = LWAuftragMeta.TABLE;
	TableSpecifier TABLE_K = LWAuftragKopfMeta.TABLE;
	TableSpecifier TABLE_P = LWProjektMeta.TABLE;
	
	MetaAttribute ATR_PROJEKTNUMMER = AttributeFactory.clone(
			LWAuftragMeta.ATR_PROJEKTNUMMER,
			ColumnSpecifier.get(TABLE_X, "projektNummer"));

	MetaAttribute ATR_GSEPROJEKT = AttributeFactory.clone(
			LWAuftragMeta.ATR_GSE_PROJEKT,
			ColumnSpecifier.get(TABLE_X, "ID_GSE_PA"));
	
	MetaAttribute ATR_ABBRUCHDATUM = AttributeFactory.clone(
			LWAuftragKopfMeta.ATR_ABBRUCHDATUM,
			ColumnSpecifier.get(TABLE_X, "abbruchDatum"));
	
	MetaAttribute ATR_ABDECKUNG = AttributeFactory.clone(
			LWAuftragKopfMeta.ATR_ABDECKUNG,
			ColumnSpecifier.get(TABLE_X, "abdeckung"));

	MetaAttribute ATR_ABSCHLUSSDATUM = AttributeFactory.clone(
			LWAuftragKopfMeta.ATR_ABSCHLUSSDATUM,
			ColumnSpecifier.get(TABLE_X, "abschlussDatum"));

	MetaAttribute ATR_AUFTRAGSDATUM = AttributeFactory.clone(
			LWAuftragMeta.ATR_AUFTRAGSDATUM,
			ColumnSpecifier.get(TABLE_X, "auftragsDatum"));

	MetaAttribute ATR_AUSFUEHRUNG = AttributeFactory.clone(
			LWAuftragKopfMeta.ATR_AUSFUEHRUNG,
			ColumnSpecifier.get(TABLE_X, "ausfuehrung"));

	MetaAttribute ATR_AVORTEAM = AttributeFactory.clone(
			LWAuftragMeta.ATR_AVORTEAM,
			ColumnSpecifier.get(TABLE_X, "avorTeam"));

	MetaAttribute ATR_PROJEKTBEZEICHNUNG = AttributeFactory.clone(
			LWProjektMeta.ATR_PROJEKTBEZEICHNUNG,
			ColumnSpecifier.get(TABLE_X, "bauherr"));
	
	MetaAttribute ATR_BEMERKUNGENAUFTRAG = AttributeFactory.clone(
			LWAuftragKopfMeta.ATR_BEMERKUNGEN,
			ColumnSpecifier.get(TABLE_X, "bemerkungen"));

	MetaAttribute ATR_BEMERKUNGENMONTEUR = AttributeFactory.clone(
			LWAuftragKopfMeta.ATR_BEMERKUNGENMONTEUR,
			ColumnSpecifier.get(TABLE_X, "bemerkungenMonteur"));

	MetaAttribute ATR_BEZUGSDATUM = AttributeFactory.clone(
			LWAuftragKopfMeta.ATR_BEZUGSDATUM,
			ColumnSpecifier.get(TABLE_X, "granitProduktionBegin"));	

	MetaAttribute ATR_DIENST1PFY = AttributeFactory.clone(
			LWAuftragKopfMeta.ATR_DIENST1PFY,
			ColumnSpecifier.get(TABLE_X, "Dienst1PFY"));
	
	MetaAttribute ATR_DIENST2PFY = AttributeFactory.clone(
			LWAuftragKopfMeta.ATR_DIENST2PFY,
			ColumnSpecifier.get(TABLE_X, "Dienst2PFY"));

	MetaAttribute ATR_DIENST3PFY = AttributeFactory.clone(
			LWAuftragKopfMeta.ATR_DIENST3PFY,
			ColumnSpecifier.get(TABLE_X, "Dienst3PFY"));

	MetaAttribute ATR_DIENST4PFY = AttributeFactory.clone(
			LWAuftragKopfMeta.ATR_DIENST4PFY,
			ColumnSpecifier.get(TABLE_X, "Dienst4PFY"));
	
	MetaAttribute ATR_DIENST5PFY = AttributeFactory.clone(
			LWAuftragKopfMeta.ATR_DIENST5PFY,
			ColumnSpecifier.get(TABLE_X, "Dienst5PFY"));
	
	MetaAttribute ATR_DIENSTSIR = AttributeFactory.clone(
			LWAuftragKopfMeta.ATR_DIENSTSIR,
			ColumnSpecifier.get(TABLE_X, "dienstSir"));

	MetaAttribute ATR_DISPONENT = AttributeFactory.clone(
			LWAuftragKopfMeta.ATR_DISPONENTSIR,
			ColumnSpecifier.get(TABLE_X, "disponent"));

	MetaAttribute ATR_EMAILBAUFUEHRER = AttributeFactory.clone(
			LWProjektMeta.ATR_EMAILBAUFUEHRER,
			ColumnSpecifier.get(TABLE_X, "EMailBaufuehrer"));

	MetaAttribute ATR_EMAILBAUHERR = AttributeFactory.clone(
			LWProjektMeta.ATR_EMAILBAUHERR,
			ColumnSpecifier.get(TABLE_X, "Reserve6"));

	MetaAttribute ATR_FUSSTEXT = AttributeFactory.clone(
			LWAuftragMeta.ATR_FUSSTEXT,
			ColumnSpecifier.get(TABLE_X, "fussText"));

	MetaAttribute ATR_GPSKOORDINATEN = AttributeFactory.clone(
			LWProjektMeta.ATR_GPSKOORDINATEN,
			ColumnSpecifier.get(TABLE_X, "GPSKoordinaten"));

	MetaAttribute ATR_GRANIT1CODE = AttributeFactory.clone(
			LWAuftragKopfMeta.ATR_GRANIT1CODE,
			ColumnSpecifier.get(TABLE_X, "granitSchluessel1"));
	
	MetaAttribute ATR_GRANIT2CODE = AttributeFactory.clone(
			LWAuftragKopfMeta.ATR_GRANIT2CODE,
			ColumnSpecifier.get(TABLE_X, "granitSchluessel2"));

	MetaAttribute ATR_GRANIT3CODE = AttributeFactory.clone(
			LWAuftragKopfMeta.ATR_GRANIT3CODE,
			ColumnSpecifier.get(TABLE_X, "granitSchluessel3"));

	MetaAttribute ATR_GRANIT4CODE = AttributeFactory.clone(
			LWAuftragKopfMeta.ATR_GRANIT4CODE,
			ColumnSpecifier.get(TABLE_X, "granitSchluessel4"));

	MetaAttribute ATR_GRANIT5CODE = AttributeFactory.clone(
			LWAuftragKopfMeta.ATR_GRANIT5CODE,
			ColumnSpecifier.get(TABLE_X, "granitSchluessel5"));

	MetaAttribute ATR_GRANIT6CODE = AttributeFactory.clone(
			LWAuftragKopfMeta.ATR_GRANIT6CODE,
			ColumnSpecifier.get(TABLE_X, "granitSchluessel6"));

	MetaAttribute ATR_GRANIT7CODE = AttributeFactory.clone(
			LWAuftragKopfMeta.ATR_GRANIT7CODE,
			ColumnSpecifier.get(TABLE_X, "granitSchluessel7"));

	MetaAttribute ATR_GRANIT8CODE = AttributeFactory.clone(
			LWAuftragKopfMeta.ATR_GRANIT8CODE,
			ColumnSpecifier.get(TABLE_X, "granitSchluessel8"));

	MetaAttribute ATR_GRANIT9CODE = AttributeFactory.clone(
			LWAuftragKopfMeta.ATR_GRANIT9CODE,
			ColumnSpecifier.get(TABLE_X, "granitSchluessel9"));

	MetaAttribute ATR_GRANITACODE = AttributeFactory.clone(
			LWAuftragKopfMeta.ATR_GRANITACODE,
			ColumnSpecifier.get(TABLE_X, "granitSchluesselA"));

	MetaAttribute ATR_GRANITSTARTDATUM = AttributeFactory.clone(
			LWAuftragKopfMeta.ATR_GRANITSTARTDATUM,
			ColumnSpecifier.get(TABLE_X, "granitMontageAnfang"));
	
	MetaAttribute ATR_GRANITENDEDATUM = AttributeFactory.clone(
			LWAuftragKopfMeta.ATR_GRANITENDEDATUM,
			ColumnSpecifier.get(TABLE_X, "granitMontageEnde"));

	MetaAttribute ATR_INFOMASSE = AttributeFactory.clone(
			LWAuftragKopfMeta.ATR_INFOMASSE,
			ColumnSpecifier.get(TABLE_X, "InfoMasse"));

	MetaAttribute ATR_INFOMONTEUR = AttributeFactory.clone(
			LWAuftragKopfMeta.ATR_INFOMONTEUR,
			ColumnSpecifier.get(TABLE_X, "infoMonteur"));

	MetaAttribute ATR_KONTAKTPERSON = AttributeFactory.clone(
			LWAuftragKopfMeta.ATR_KONTAKTPERSON,
			ColumnSpecifier.get(TABLE_X, "kontaktPerson"));

	MetaAttribute ATR_KOPFTEXT = AttributeFactory.clone(
			LWAuftragMeta.ATR_KOPFTEXT,
			ColumnSpecifier.get(TABLE_X, "kopfText"));

	MetaAttribute ATR_KUNDENBERATER = AttributeFactory.clone(
			LWAuftragMeta.ATR_KUNDENBERATER,
			ColumnSpecifier.get(TABLE_X, "kundenBerater"));	

	MetaAttribute ATR_MARKETING = AttributeFactory.clone(
			LWProjektMeta.ATR_MARKETING,
			ColumnSpecifier.get(TABLE_X, "marketing"));

	MetaAttribute ATR_MONTEURPFYN = AttributeFactory.clone(
			LWAuftragKopfMeta.ATR_MONTEURPFYN,
			ColumnSpecifier.get(TABLE_X, "MonteurPfy"));	

	MetaAttribute ATR_MONTEURSIR = AttributeFactory.clone(
			LWAuftragKopfMeta.ATR_MONTEURSIR,
			ColumnSpecifier.get(TABLE_X, "chefMonteur"));

	MetaAttribute ATR_OBJEKTBETREUER = AttributeFactory.clone(
			LWProjektMeta.ATR_OBJEKTBETREUER,
			ColumnSpecifier.get(TABLE_X, "ObjektBetreuer"));

	MetaAttribute ATR_NAMEBAUFUEHRER = AttributeFactory.clone(
			LWProjektMeta.ATR_NAMEBAUFUEHRER,
			ColumnSpecifier.get(TABLE_X, "NameBaufuehrer"));

	MetaAttribute ATR_PHONEBAUFUEHRER = AttributeFactory.clone(
			LWProjektMeta.ATR_PHONEBAUFUEHRER,
			ColumnSpecifier.get(TABLE_X, "phoneBaufuehrer"));	

	MetaAttribute ATR_MOBILEBAUFUEHRER = AttributeFactory.clone(
			LWProjektMeta.ATR_MOBILEBAUFUEHRER,
			ColumnSpecifier.get(TABLE_X, "Reserve52"));	
	
	MetaAttribute ATR_NAMEBAUHERR = AttributeFactory.clone(
			LWProjektMeta.ATR_NAMEBAUHERR,
			ColumnSpecifier.get(TABLE_X, "phoneBauherr"));

	MetaAttribute ATR_PHONEBAUHERR = AttributeFactory.clone(
			LWProjektMeta.ATR_PHONEBAUHERR,
			ColumnSpecifier.get(TABLE_X, "phoneKunde"));

	MetaAttribute ATR_MOBILEBAUHERR = AttributeFactory.clone(
			LWProjektMeta.ATR_MOBILEBAUHERR,
			ColumnSpecifier.get(TABLE_X, "Reserve51"));
	
	MetaAttribute ATR_PLANUNGSTATUS = AttributeFactory.clone(
			LWAuftragKopfMeta.ATR_PLANUNGSTATUS,
			ColumnSpecifier.get(TABLE_X, "planungStatus"));

	MetaAttribute ATR_SPEZIALDATUM = AttributeFactory.clone(
			LWAuftragKopfMeta.ATR_SPEZIALDATUM,
			ColumnSpecifier.get(TABLE_X, "spezialDatum"));	

	MetaAttribute ATR_TERMINZEIT = AttributeFactory.clone(
			LWAuftragKopfMeta.ATR_TERMINZEIT,
			ColumnSpecifier.get(TABLE_X, "zeitA"));

	MetaAttribute ATR_VERKAEUFERCODE = AttributeFactory.clone(
			LWAuftragMeta.ATR_VERKAEUFERCODE,
			ColumnSpecifier.get(TABLE_X, "verkaeufer"));

	MetaAttribute ATR_WUNSCHDATUM = AttributeFactory.clone(
			LWAuftragMeta.ATR_WUNSCHDATUM,
			ColumnSpecifier.get(TABLE_X, "wunschDatum"));

	MetaClass METACLASS = new MetaClass("LogiwareExportAuftrag", null,
			LWAuftragKey.class, (MetaClass) null,
			// Attributes
			new VarArgs<MetaProperty>(MetaProperty.class).flat(
					TABLE_X,
					MetaClassMember.discoverMetaClassMembers(LWExportAuftragMeta.class)
			));
	
}

package ch.eisenring.dispo.shared.network.packets;

import java.util.Date;

import ch.eisenring.core.collections.Collection;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.alterableview.AlterableView;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.datatypes.date.DateUtil;
import ch.eisenring.core.io.AutoStreamed;
import ch.eisenring.dispo.shared.codetables.AbrufModus;

public final class PacketBestellInfosRequest extends AbstractDSPPacket {

	@AutoStreamed
	private final List<String> projektnummern = new ArrayList<String>();
	
	@AutoStreamed
	private long callForDate;
	
	@AutoStreamed
	private AbrufModus modus;

	private PacketBestellInfosRequest() {
		super(CAPS_SEQUENCE_REQUEST, PRIORITY_REQUEST);
	}

	public static PacketBestellInfosRequest create(final long callForDate, final AbrufModus modus) {
		final PacketBestellInfosRequest packet = new PacketBestellInfosRequest();
		packet.callForDate = callForDate;
		packet.modus = modus;
		return packet;
	}

	public void addProjektnummern(final Collection<String> projektnummern) {
		if (projektnummern != null && !projektnummern.isEmpty()) {
			for (final String projektnummer : projektnummern) {
				addProjektnummer(projektnummer);
			}
		}
	}

	public void addProjektnummer(final String projektnummer) {
		if (projektnummer != null && ! projektnummern.contains(projektnummer))
			projektnummern.add(projektnummer);
	}

	public boolean isEmpty() {
		return projektnummern.isEmpty();
	}

	public List<String> getProjektnummern() {
		return AlterableView.of(projektnummern);
	}

	public Date getCallForDate() {
		return DateUtil.toDate(callForDate);
	}

	public AbrufModus getModus() {
		return modus;
	}

}

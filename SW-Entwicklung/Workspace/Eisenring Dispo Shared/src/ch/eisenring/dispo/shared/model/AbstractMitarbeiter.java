package ch.eisenring.dispo.shared.model;

import static ch.eisenring.dispo.shared.DSPConstants.MDLF_NONE;
import static ch.eisenring.dispo.shared.codetables.EmploymentRole.MITARBEITER_CHAUFEUR;
import static ch.eisenring.dispo.shared.codetables.EmploymentRole.MITARBEITER_MONTEUR;

import java.io.IOException;
import java.util.Iterator;

import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.datatypes.date.Period;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.io.StreamReader;
import ch.eisenring.core.io.StreamWriter;
import ch.eisenring.core.sort.Comparator;
import ch.eisenring.dispo.shared.codetables.EmploymentRole;
import ch.eisenring.dispo.shared.codetables.EmploymentType;
import ch.eisenring.dispo.shared.pojo.DSPMonteurTeam;
import ch.eisenring.logiware.code.soft.GSECode;
import ch.eisenring.logiware.code.soft.LWMonteurSirCode;

public abstract class AbstractMitarbeiter extends AbstractModel {

	// --------------------------------------------------------------
	// ---
	// --- Ordering
	// ---
	// --------------------------------------------------------------
	public static class Order {
		public final static Comparator<AbstractMitarbeiter> ToString = Comparator.wrapNullSafe(
				new Comparator<AbstractMitarbeiter>() {
			@Override
			public int compare(final AbstractMitarbeiter m1, final AbstractMitarbeiter m2) {
				int r = Strings.compareIgnoreCase(m1.getLastName(), m2.getLastName());
				if (r != 0)
					return r;
				r = Strings.compareIgnoreCase(m1.getFirstName(), m2.getFirstName());
				if (r != 0)
					return r;
				return Strings.compareIgnoreCase(m1.getShortName(),  m2.getShortName());
			}
		});

		public final static Comparator<AbstractMitarbeiter> LastName = Comparator.wrapNullSafe(
				new Comparator<AbstractMitarbeiter>() {
			@Override
			public int compare(final AbstractMitarbeiter o1, final AbstractMitarbeiter o2) {
				return Strings.compareIgnoreCase(o1.getLastName(), o2.getLastName());
			}
		});

		public final static Comparator<AbstractMitarbeiter> FirstName = Comparator.wrapNullSafe(
				new Comparator<AbstractMitarbeiter>() {
			@Override
			public int compare(final AbstractMitarbeiter o1, final AbstractMitarbeiter o2) {
				return Strings.compareIgnoreCase(o1.getFirstName(), o2.getFirstName());
			}
		});

		public final static Comparator<AbstractMitarbeiter> ShortName = Comparator.wrapNullSafe(
				new Comparator<AbstractMitarbeiter>() {
			@Override
			public int compare(final AbstractMitarbeiter o1, final AbstractMitarbeiter o2) {
				return Strings.compareIgnoreCase(o1.getShortName(), o2.getShortName());
			}
		});
		
		public final static Comparator<AbstractMitarbeiter> FullName = Comparator.combine(LastName, FirstName);

		public final static Comparator<AbstractMitarbeiter> Type = Comparator.wrapNullSafe(
				new Comparator<AbstractMitarbeiter>() {
			@Override
			public int compare(final AbstractMitarbeiter o1, final AbstractMitarbeiter o2) {
				return AbstractCode.Order.Id.compare(o1.getEmploymentType(), o2.getEmploymentType());
			}
		});

		public final static Comparator<AbstractMitarbeiter> Role = Comparator.wrapNullSafe(
				new Comparator<AbstractMitarbeiter>() {
			@Override
			public int compare(final AbstractMitarbeiter o1, final AbstractMitarbeiter o2) {
				return AbstractCode.Order.Id.compare(o1.getEmploymentRole(), o2.getEmploymentRole());
			}
		});

		public final static Comparator<AbstractMitarbeiter> RowId = Comparator.wrapNullSafe(
				new Comparator<AbstractMitarbeiter>() {
			@Override
			public int compare(final AbstractMitarbeiter o1, final AbstractMitarbeiter o2) {
				return Comparator.compareSigned(o1.getPresentoId(), o2.getPresentoId());
			}
		});

		public final static Comparator<AbstractMitarbeiter> StandardDisplayOrder = Comparator.wrapNullSafe(
				new Comparator<AbstractMitarbeiter>() {
			@Override
			public int compare(final AbstractMitarbeiter m1, final AbstractMitarbeiter m2) {
				int r = Comparator.compareSigned(m1.getSortHint(), m2.getSortHint());
				if (r != 0)
					return r;
				return ToString.compare(m1, m2);
			}
		});

		public final static Comparator<AbstractMitarbeiter> AbrechnungDisplayOrder = Comparator.wrapNullSafe(
				new Comparator<AbstractMitarbeiter>() {
			@Override
			public int compare(final AbstractMitarbeiter m1, final AbstractMitarbeiter m2) {
				int r = AbstractCode.Order.Id.compare(m1.getEmploymentType(), m2.getEmploymentType());
				if (r != 0)
					return r;
				return ToString.compare(m1, m2);
			}
		});
		
		public final static Comparator<AbstractMitarbeiter> Team = Comparator.wrapNullSafe(
				new Comparator<AbstractMitarbeiter>() {
			@Override
			public int compare(final AbstractMitarbeiter m1, final AbstractMitarbeiter m2) {
				final DSPMonteurTeam team1 = DSPMonteurTeam.getTeam(m1.getMonteurTeamId());
				final DSPMonteurTeam team2 = DSPMonteurTeam.getTeam(m2.getMonteurTeamId());
				return DSPMonteurTeam.Order.TeamName.compare(team1, team2);
			}
		});
	}

	private long presentoId;
	private int sortHint;
	private int highlight;
	private int glzSaldo;
	private String logiwareId;
	private String firstName;
	private String lastName;
	private String shortName;
	private String phoneMobile;
	
	public AbstractMitarbeiter(ModelContext context) {
		this(context, null);
	}
	
	public AbstractMitarbeiter(ModelContext context, Long objectId) {
		super(context, objectId);
	}

	/**
	 * Gets the primary key in Presento for this employee.
	 */
	public final long getPresentoId() {
		return this.presentoId;
	}

	public final void setPresentoId(final long presentoId) {
		updateDirty(this.presentoId, presentoId, MDLF_NONE);
		this.presentoId = presentoId;
	}

	public final int getGLZSaldo() {
		return glzSaldo;
	}

	public final void setGLZSaldo(final int glzSaldo) {
		updateDirty(this.glzSaldo, glzSaldo, MDLF_NONE);
		this.glzSaldo = glzSaldo;
	}

	public final int getSortHint() {
		return sortHint;
	}

	public final void setSortHint(int sortHint) {
		updateDirty(this.sortHint, sortHint, MDLF_NONE);
		this.sortHint = sortHint;
	}

	public final int getHighlight() {
		return highlight;
	}
	
	public final void setHighlight(int highlight) {
		updateDirty(this.highlight, highlight, MDLF_NONE);
		this.highlight = highlight;
	}

	public final void setLogiwareId(String logiwareId) {
		logiwareId = Strings.clean(logiwareId);
		updateDirty(this.logiwareId, logiwareId, MDLF_NONE);
		this.logiwareId = logiwareId;
	}

	public String getLogiwareId() {
		return this.logiwareId;
	}

	public final void setFirstName(String firstName) {
		firstName = Strings.clean(firstName, 30);
		updateDirty(this.firstName, firstName, MDLF_NONE);
		this.firstName = firstName;
	}
	
	public final String getFirstName() {
		return this.firstName;
	}

	public final void setLastName(String lastName) {
		lastName = Strings.clean(lastName, 30);
		updateDirty(this.lastName, lastName, MDLF_NONE);
		this.lastName = lastName;
	}
	
	public final String getLastName() {
		return this.lastName;
	}
	
	public final void setShortName(String shortName) {
		shortName = Strings.clean(shortName, 30);
		updateDirty(this.shortName, shortName, MDLF_NONE);
		this.shortName = shortName;
	}
	
	public final String getShortName() {
		return this.shortName;
	}

	public final void setPhoneMobile(String phoneMobile) {
		phoneMobile = Strings.clean(phoneMobile, 30);
		updateDirty(this.phoneMobile, phoneMobile, MDLF_NONE);
		this.phoneMobile = phoneMobile;
	}
	
	public final String getPhoneMobile() {
		return phoneMobile;
	}

	@Override
	public void write(final StreamWriter output) throws IOException {
		super.write(output);
		output.writeLong(getPresentoId());
		output.writeInt(getSortHint());
		output.writeInt(getHighlight());
		output.writeInt(getGLZSaldo());
		output.writeString(getShortName());
		output.writeString(getFirstName());
		output.writeString(getLastName());
		output.writeString(getLogiwareId());
		output.writeString(getPhoneMobile());
		output.writeObject(monteurTeamId);
	}
	
	@Override
	public void read(StreamReader input) throws IOException {
		super.read(input);
		setPresentoId(input.readLong());
		setSortHint(input.readInt());
		setHighlight(input.readInt());
		setGLZSaldo(input.readInt());
		setShortName(input.readString());
		setFirstName(input.readString());
		setLastName(input.readString());
		setLogiwareId(input.readString());
		setPhoneMobile(input.readString());
		setMonteurTeamId((Long) input.readObject());
	}

	/****************************************************************
	 * Derived Attributes
	 ****************************************************************/
	public boolean isChauffeur() {
		return MITARBEITER_CHAUFEUR.equals(getEmploymentRole());
	}
	
	public boolean isMonteur() {
		return MITARBEITER_MONTEUR.equals(getEmploymentRole());
	}

	public String getLabel() {
		return getName();
	}
	
	public String getName() {
		final String n1 = getLastName();
		final String n2 = getFirstName();
		final StringMaker b = StringMaker.obtain(64);
		if (null!=n1)
			b.append(n1);
		if (null!=n2) {
			if (b.length()>0)
				b.append(' ');
			b.append(n2);
		}
		return b.release("<Unbenannt>");
	}

	@Override
	public void toStringMaker(final StringMaker target) {
		final String n1 = getLastName();
		final String n2 = getFirstName();
		final String n3 = getShortName();
		final int l = target.length();
		if (!Strings.isEmpty(n1))
			target.append(n1);
		if (!Strings.isEmpty(n2)) {
			target.append(' ');
			target.append(n2);
		}
		if (target.length() == l) 
			target.append("<Unbenannt>");
		if (!Strings.isEmpty(n3)) {
			target.append(" (");
			target.append(shortName);
			target.append(')');
		}
	}
	
	public abstract EmploymentType getEmploymentType();

	public abstract EmploymentRole getEmploymentRole();

	public abstract boolean getEmployed();

	public abstract Iterator<FeinZuordnung> newFeinZuordnungIterator();

	// subclasses may implement different values
	public final boolean isHighlighted() {
		return highlight != 0;
	}

	public boolean canDelete() {
		return false;
	}
	
	public boolean canEdit() {
		return false;
	}

	public abstract boolean isEmployedWithin(final Period period);
	
	public GSECode getGSE(final ModelContext globalContext) {
		if (globalContext != null) {
			final MasterMitarbeiter master = globalContext.getMitarbeiter(getPresentoId(), MasterMitarbeiter.class);
			if (master != null) {
				return master.getGSE();
			}
		}
		return null;
	}

	public LWMonteurSirCode getMonteurSirCode() {
		LWMonteurSirCode code = null;
		String s = getLogiwareId();
		if (s != null) {
			code = (LWMonteurSirCode) AbstractCode.getByKey(s, LWMonteurSirCode.class);
		}
		if (AbstractCode.isNull(code)) {
			s = getShortName();
			code = (LWMonteurSirCode) AbstractCode.getByShortText(s, LWMonteurSirCode.class);
		}
		if (AbstractCode.isNull(code)) {
			code = LWMonteurSirCode.NULL;
		}
		return code;
	}

	// --------------------------------------------------------------
	// ---
	// --- Team assignment
	// ---
	// --------------------------------------------------------------
	private transient Long monteurTeamId;
	
	public final Long getMonteurTeamId() {
		return monteurTeamId;
	}

	public final void setMonteurTeamId(final Long teamId) {
		updateDirty(this.monteurTeamId, teamId, MDLF_NONE);
		this.monteurTeamId = teamId;
	}

	public final void setMonteurTeam(final DSPMonteurTeam team) {
		setMonteurTeamId(team == null ? null : team.getRowId());
	}

}

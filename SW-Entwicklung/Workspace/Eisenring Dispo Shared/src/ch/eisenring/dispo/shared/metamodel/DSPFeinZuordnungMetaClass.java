package ch.eisenring.dispo.shared.metamodel;

import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.core.util.VarArgs;
import ch.eisenring.dispo.shared.DSPConstants;
import ch.eisenring.dispo.shared.codetables.ColorCode;
import ch.eisenring.dispo.shared.codetables.ZuordnungCode;
import ch.eisenring.model.MetaAttribute;
import ch.eisenring.model.MetaClass;
import ch.eisenring.model.MetaClassMember;
import ch.eisenring.model.MetaProperty;
import ch.eisenring.model.engine.jdbc.ColumnSpecifier;
import ch.eisenring.model.engine.jdbc.TableSpecifier;
import ch.eisenring.model.factory.AttributeFactory;
import ch.eisenring.model.primarykey.LongPrimaryKey;

public interface DSPFeinZuordnungMetaClass {

	TableSpecifier TABLE = TableSpecifier.get(DSPConstants.DSP_DATABASE, "FeinZuordnung");

	MetaAttribute ATR_AUFTRAG_ROWID = AttributeFactory.wLong(
			"Auftrag_rowId", null, Long.MIN_VALUE, Long.MAX_VALUE,
			ColumnSpecifier.get(TABLE, "auftrag_rowId"));

	MetaAttribute ATR_MITARBEITER_PID = AttributeFactory.wLong(
			"Mitarbeiter_PID", null, Long.MIN_VALUE, Long.MAX_VALUE,
			ColumnSpecifier.get(TABLE, "mitarbeiter_pid"));

	MetaAttribute ATR_DATEFROM = AttributeFactory.datetime(
			"DateFrom", TimestampUtil.NULL_TIMESTAMP,
			ColumnSpecifier.get(TABLE, "dateFrom"));

	MetaAttribute ATR_DATEUPTO = AttributeFactory.datetime(
			"DateUpto", TimestampUtil.NULL_TIMESTAMP,
			ColumnSpecifier.get(TABLE, "dateUpTo"));

	MetaAttribute ATR_LABEL = AttributeFactory.string(
			"Label", null, 30,
			ColumnSpecifier.get(TABLE, "label"));

	MetaAttribute ATR_BEMERKUNGEN = AttributeFactory.string(
			"Bemerkungen", null, 4000,
			ColumnSpecifier.get(TABLE, "bemerkungen"));

	MetaAttribute ATR_TYPE = AttributeFactory.code(
			"Type", ZuordnungCode.class, ZuordnungCode.ZUORDNUNG_FAHRAUFTRAG,
			ColumnSpecifier.get(TABLE, "type"));
	
	MetaAttribute ATR_AUFTRAGNUMMER = AttributeFactory.wInteger(
			"Auftragnummer", null, Integer.MIN_VALUE, Integer.MAX_VALUE, 
			ColumnSpecifier.get(TABLE, "Auftrag_Nummer"));

	MetaAttribute ATR_PRJNUMMERTEXT = AttributeFactory.string(
			"PrjNummerText", null, 250,
			ColumnSpecifier.get(TABLE, "prjNrText"));

	MetaAttribute ATR_COLORID = AttributeFactory.code(
			"ColorId", ColorCode.class, ColorCode.COLOR_DEFAULT,
			ColumnSpecifier.get(TABLE, "colorId"));

	MetaAttribute ATR_FAHRZEUG = AttributeFactory.string(
			"Fahrzeug", null, 30,
			ColumnSpecifier.get(TABLE, "fahrzeug"));

	MetaAttribute ATR_ANHAENGER = AttributeFactory.string(
			"Anhaenger", null, 30,
			ColumnSpecifier.get(TABLE, "anhaenger"));

	MetaAttribute ATR_CREATEDBY = AttributeFactory.string(
			"CreatedBy", null, 30,
			ColumnSpecifier.get(TABLE, "createdBy"));

	MetaClass METACLASS = new MetaClass(
			"DSPFeinZuordnung", null,
			LongPrimaryKey.class,
			null,
			// Attributes
			new VarArgs<MetaProperty>(MetaProperty.class).flat(
			ColumnSpecifier.get(TABLE, "rowId"),
			TABLE,
			MetaClassMember.discoverMetaClassMembers(DSPFeinZuordnungMetaClass.class)
			));
	
}
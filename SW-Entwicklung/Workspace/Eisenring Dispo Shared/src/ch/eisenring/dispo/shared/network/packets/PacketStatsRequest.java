package ch.eisenring.dispo.shared.network.packets;


/**
 * Request statistics.
 * 
 * This packet contains no data.
 *
 * This packet can be sent from both client and server,
 * although the server actually doesn't do so for now.
 */
public final class PacketStatsRequest extends AbstractDSPPacket {

	PacketStatsRequest() {
		super(CAPS_SEQUENCE_REQUEST | CAPS_SILENT, PRIORITY_REQUEST);
	}
	
	public static PacketStatsRequest create() {
		return new PacketStatsRequest();
	}

}

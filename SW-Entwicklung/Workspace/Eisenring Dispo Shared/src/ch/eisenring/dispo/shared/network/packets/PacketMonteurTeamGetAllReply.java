package ch.eisenring.dispo.shared.network.packets;

import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.alterableview.AlterableView;
import ch.eisenring.dispo.shared.pojo.DSPMonteurTeam;

public final class PacketMonteurTeamGetAllReply extends PacketMonteurTeamBase {

	private PacketMonteurTeamGetAllReply() {
		super(CAPS_SEQUENCE_REPLY | CAPS_ERRORMESSAGE, PRIORITY_REPLY,
				TRANSPORT_LIST);
	}

	// --------------------------------------------------------------
	// ---
	// --- Factory methods
	// ---
	// --------------------------------------------------------------
	public static PacketMonteurTeamGetAllReply create(final PacketMonteurTeamGetAllRequest request, final ErrorMessage message) {
		final PacketMonteurTeamGetAllReply packet = new PacketMonteurTeamGetAllReply();
		packet.setReplyId(request, message);
		return packet;
	}

	public static PacketMonteurTeamGetAllReply create(final PacketMonteurTeamGetAllRequest request,
			final java.util.Collection<DSPMonteurTeam> teams) {
		final PacketMonteurTeamGetAllReply packet = create(request, ErrorMessage.OK);
		packet.teamList.addAll(teams);
		return packet;
	}

	// --------------------------------------------------------------
	// ---
	// --- API
	// ---
	// --------------------------------------------------------------
	public List<DSPMonteurTeam> getTeams() {
		return AlterableView.of(this.teamList);
	}

}

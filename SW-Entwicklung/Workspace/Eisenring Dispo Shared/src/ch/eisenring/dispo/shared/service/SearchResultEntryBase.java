package ch.eisenring.dispo.shared.service;

import java.util.Date;

import ch.eisenring.core.io.AutoStreamable;
import ch.eisenring.core.io.AutoStreamed;

public abstract class SearchResultEntryBase implements AutoStreamable {

	@AutoStreamed
	private long rowId;

	@AutoStreamed
	private Date dateFrom;

	@AutoStreamed
	private Date dateUpto;

	@AutoStreamed
	private String label;
	
	// --------------------------------------------------------------
	// ---
	// --- Getters / Setters
	// ---
	// --------------------------------------------------------------
	public long getRowId() {
		return rowId;
	}

	public void setRowId(final long rowId) {
		this.rowId = rowId;
	}

	public Date getDateFrom() {
		return dateFrom;
	}

	public void setDateFrom(final Date dateFrom) {
		this.dateFrom = dateFrom;
	}

	public Date getDateUpto() {
		return dateUpto;
	}

	public void setDateUpto(final Date dateUpto) {
		this.dateUpto = dateUpto;
	}

	public String getLabel() {
		return label;
	}

	public void setLabel(final String label) {
		this.label = label;
	}

	// --------------------------------------------------------------
	// ---
	// --- Object overrides
	// ---
	// --------------------------------------------------------------
	@Override
	public int hashCode() {
		return ((int) rowId) ^ ((int) (rowId >> 32));
	}

	@Override
	public boolean equals(final Object o) {
		return o instanceof SearchResultEntryBase && ((SearchResultEntryBase) o).rowId == rowId;
	}
	
}

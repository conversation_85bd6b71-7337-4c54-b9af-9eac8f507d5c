package ch.eisenring.dispo.shared.model;

import static ch.eisenring.dispo.shared.DSPConstants.MDLF_DELETED;
import static ch.eisenring.dispo.shared.DSPConstants.MDLF_DIRTY;
import static ch.eisenring.dispo.shared.DSPConstants.MDLF_LOGIDIRTY;
import static ch.eisenring.dispo.shared.DSPConstants.MDLF_NEW;
import static ch.eisenring.dispo.shared.DSPConstants.MDLF_NONE;
import static ch.eisenring.dispo.shared.DSPConstants.MDLF_REMOVED;

import java.io.IOException;
import java.lang.reflect.Constructor;
import java.util.Date;

import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.Map;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.collections.impl.HashMap;
import ch.eisenring.core.datatypes.date.DateGranularityCode;
import ch.eisenring.core.datatypes.date.DateUtil;
import ch.eisenring.core.datatypes.date.HEAGCalendar;
import ch.eisenring.core.datatypes.date.Period;
import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.core.datatypes.primitives.Primitives;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.StringMakerFriendly;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.io.StreamReader;
import ch.eisenring.core.io.StreamWriter;
import ch.eisenring.core.iterators.Filter;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.core.logging.Logger.LogLevel;
import ch.eisenring.dispo.shared.DSPConstants;
import ch.eisenring.dispo.shared.base.SemaphoreLock;
import ch.eisenring.logiware.LWAuftragKey;

/**
 * Holds model object that are dependent of each other in a pool.
 * 
 * The pool plays a major role in serialization.
 */
public class ModelContext implements Period, StringMakerFriendly {

	// Map of OID->Model
	protected final Map<Long, AbstractModel> modelMap = new HashMap<>(1024);

	// List of all Models
	protected final List<AbstractModel> modelList = new ArrayList<>(4096);
	
	private final SemaphoreLock SEMAPHORE = new SemaphoreLock(this);
	private final AbstractOIDGenerator oidGenerator;
	private final long begin;
	private final long end;
	private final long createdTimestamp = System.currentTimeMillis();
	private long lastTouched;
	private int modelVersion;
	private String lastChangedBy;
	
	public ModelContext(final long date, final AbstractOIDGenerator oidGenerator) {
		final HEAGCalendar calendar = HEAGCalendar.obtain(date); 
		DateGranularityCode.KW.round(calendar, 0);
		this.begin = calendar.getTimeInMillis();
		DateGranularityCode.KW.round(calendar, 1);
		this.end = calendar.getTimeInMillis();
		this.oidGenerator = oidGenerator;
		touch();
	}

	public AbstractOIDGenerator getOidGenerator() {
		return this.oidGenerator;
	}

	public Long newOID() {
		return oidGenerator.nextOID();
	}

	/**
	 * Locks this ModelContext
	 */
	public final void obtain() {
		SEMAPHORE.obtain();
	}

	/**
	 * Releases this ModelContext
	 */
	public final void release() {
		SEMAPHORE.release();
	}

	/**
	 * Returns version number of this ModelContext
	 */
	public final int getVersion() {
		return this.modelVersion;
	}

	/**
	 * Sets the version number of this ModelContext.
	 */
	public void setVersion(int modelVersion) {
		this.modelVersion = modelVersion;
	}

	/**
	 * Sets the name of the user who last changed the context
	 */
	public final void setLastChangedBy(String lastChangedBy) {
		this.lastChangedBy = lastChangedBy;
	}

	/**
	 * Gets the name of the user who last changed the context
	 */
	public final String getLastChangedBy() {
		return lastChangedBy;
	}

	// --------------------------------------------------------------
	// ---
	// --- Period implementation
	// ---
	// --------------------------------------------------------------
	/**
	 * The begin date of this ModelContext (Monday 00:00:00.000)
	 */
	public final long getBegin() {
		return begin;
	}

	/**
	 * The first timestamp after this context (following Monday 00:00:00)
	 */
	public final long getEnd() {
		return end;
	}

	/**
	 * The end date of this ModelContext
	 * (the end date, Sunday 23:59:59)
	 */
	//@Deprecated
	public final Date getDateUpTo() {
		return DateUtil.toDate(end - 1000);
	}

	/**
	 * Gets a list of all models in the context.
	 * The result list is independent of the context. 
	 */
	public final List<AbstractModel> getModelList() {
		final List<AbstractModel> result = new ArrayList<AbstractModel>(modelList.size());
		obtain();
		try {
			result.addAll(modelList);
		} finally {
			release();
		}
		return result;
	}

	/** 
	 * Gets list of all models of class in context.
	 * Beware that deleted instances will not appear in the list.
	 */
	public final <T extends AbstractModel> List<T> getModelList(final Class<T> modelClass) {
		return getModelList(modelClass, null);
	}

	@SuppressWarnings("unchecked")
	public final <T extends AbstractModel> List<T> getModelList(final Class<T> modelClass, final Filter<AbstractModel> filter) {
		final List<T> result = new ArrayList<T>(256);
		obtain();
		try {
			for (int i=modelList.size()-1; i>=0; --i) {
				final T model = (T) modelList.get(i);
				if ((modelClass == null || modelClass.equals(model.getClass()))
						&& !model.isDeleted()
						&& !model.isRemoved()) {
					if (filter == null || filter.accepts(model))
						result.add(model);
				}
			}
		} finally {
			release();
		}
		return result;
	}

	public final List<AbstractModel> getModelList(final Filter<AbstractModel> filter) {
		return getModelList(null, filter);
	}

	/**
	 * Counts objects of type (with optional filter).
	 * This operation is still expensive, but cheaper than obtaining
	 * a list of said objects using getModelList().
	 */
	public final int countModels(final Filter<AbstractModel> filter) {
		int result = 0;
		obtain();
		try {
			for (int i=modelList.size()-1; i>=0; --i) {
				final AbstractModel model = modelList.get(i);
				if (filter.accepts(model))
					++result;
			}
		} finally {
			release();
		}
		return result;
	}
	
	/**
	 * Gets a model by its object id
	 */
	public final AbstractModel getModel(final Object id) {
		obtain();
		try {
			return modelMap.get(id);
		} finally {
			release();
		}
	}

	/**
	 * Gets the first model that matches the filter
	 */
	public final AbstractModel getModel(final Filter<AbstractModel> filter) {
		obtain();
		try {
			for (int i=modelList.size()-1; i>=0; --i) {
				final AbstractModel model = modelList.get(i);
				if (filter.accepts(model)) {
					return model;
				}
			}
		} finally {
			release();
		}
		return null;
	}

	/**
	 * Reads objects from the given input and adds them into the pool.
	 * Existing object states are overwritten.
	 */
	@SuppressWarnings("unchecked")
	public final void read(final StreamReader input) throws IOException {
		obtain();
		try {
			try {
				while (true) {
					final Class<?> modelClass = input.readClass();
					if (modelClass == null)
						break;
					final Long objectId = Long.valueOf(input.readLong());
					final AbstractModel model = getOrCreateModel((Class) modelClass, objectId);
					model.read(input);
				}
				// resolve all objects read
				for (int i=modelList.size()-1; i>=0; --i)
					modelList.get(i).resolveRead();
			} catch (final RuntimeException e) {
				throw new IOException(e.getMessage(), e);
			}
		} finally {
			release();
		}
	}
	
	/**
	 * Writes this pool to the given output.
	 */
	public final void write(final StreamWriter output, boolean deleted, boolean unchanged) throws IOException {
		final Map<Class<?>, Integer> statMap = new HashMap<>();
		obtain();
		try {
			int objectCount = 0;
			final boolean logDetails = false; //Logger.isEnabled(LogLevel.TRACE);
			for (int i=modelList.size()-1; i>=0; --i) {
				final AbstractModel model = modelList.get(i);
				boolean write = false;
				if (model.isDeleted() && deleted) {
					write = true;
				} else if (model.isDirty()) {
					write = true;
				} else if (unchanged) {
					write = true;
				}
				if (write) {
					model.write(output);
					//if (logDetails) {
					//    Logger.log(LogLevel.TRACE, "Serializing Model #"+model.getId()+" ["+model.getClass().getSimpleName()+"]");
					//}
					++objectCount;
					final Class<?> statKey = model.getClass();
					Integer statCount = statMap.get(statKey);
					if (statCount == null) {
						statMap.put(statKey, Integer.valueOf(1));
					} else {
						statMap.put(statKey, Integer.valueOf(statCount.intValue() + 1));
					}
				}
				if (model.evictOnTransactionBoundary()) {
					model.hookEvict();
					if (logDetails && Logger.isTraceEnabled()) {
						Logger.trace(Strings.concat("evictOnTransactionBoundary for model id #", model.getId(),
								" ", Primitives.getSimpleName(model.getClass()), " (", model, ")"));
					}
					removeModel(model);
				}
			}
			output.writeInt(0);
			if (Logger.isDebugEnabled())
				Logger.debug(Strings.concat("Serialized ", objectCount, " model objects"));
			for (Class<?> statKey : statMap.keySet()) {
				final Integer statCount = statMap.get(statKey);
				if (Logger.isDebugEnabled())
					Logger.debug(Strings.concat("-=> ", statCount, " ", statKey.getSimpleName()));
			}
		} finally {
			release();
		}
	}

	/**
	 * Adds the model to this context
	 */
	protected final void addModel(final AbstractModel model) {
		if (model == null)
			return;
		final Long objectId = model.getId();
		obtain();
		try {
			if (!modelMap.containsKey(objectId)) {
				modelMap.put(objectId, model);
				modelList.add(model);
			}
		} finally {
			release();
		}
	}
	
	public final void removeModel(final AbstractModel model) {
		if (model == null)
			return;
		obtain();
		try {
			modelMap.remove(model.getId());
			modelList.remove(model);
		} finally {
			release();
		}
	}

	/**
	 * Gets Auftrag by AuftragsNummer
	 * (Use getAuftrag(LWAuftragKey) instead!)
	 */
	//@Deprecated
	public final Auftrag getAuftragByAuftragsNummer(final int auftragsNummer) {
		return (Auftrag) getModel(new Filter<AbstractModel>() {
			@Override
			public boolean accepts(final AbstractModel model) {
				return (model instanceof Auftrag) &&
				 ((Auftrag) model).getAuftragnummer() == auftragsNummer;
			}
		});
	}

	/**
	 * Gets Auftrag by LWAuftragKey
	 */
	public final Auftrag getAuftrag(final LWAuftragKey key) {
		return (Auftrag) getModel(new Filter<AbstractModel>() {
			@Override
			public boolean accepts(final AbstractModel model) {
				if (!(model instanceof Auftrag))
					return false;
				final Auftrag auftrag = (Auftrag) model;
				return key.getAuftragnummer() == auftrag.getAuftragnummer()
						&& AbstractCode.equals(key.getGSE(), auftrag.getGSEAuftrag());
			}
		});
	}
	
	/**
	 * Clears the DIRTY and NEW flags on all models in the context
	 */
	public final void clearDirty() {
		modifyFlags(~(MDLF_DIRTY | MDLF_LOGIDIRTY | MDLF_NEW), MDLF_NONE);
	}
	
	public final void clearLogiwareDirty() {
		modifyFlags(~MDLF_LOGIDIRTY, MDLF_NONE);
	}

	public final void modifyFlags(final int andVal, final int orVal) {
		obtain();
		try {
			for (int i=modelList.size()-1; i>=0; --i) {
				final AbstractModel model = modelList.get(i);
				model.modifyFlags(andVal, orVal);
			}
		} finally {
			release();
		}
	}

	/**
	 * Clears all deleted models from the context.
	 * This also clears dirty flags.
	 */
	public final void clearDeleted() {
		obtain();
		try {
			for (int i=modelList.size()-1; i>=0; --i) {
				final AbstractModel model = modelList.get(i);
				// removed and deleted are slightly different,
				// deleted objects have been deleted from the DB,
				// while removed objects have only been removed
				// from the context.
				if (model.isDeleted()) {
					// make sure the objects relations are cleared!
					model.delete();
					removeModel(model);
				} else if (model.isRemoved()) {
					removeModel(model);
				}
			}
			clearDirty();
		} finally {
			release();
		}
	}

	/**
	 * Gets the logical OR of all model objects flags.
	 * This is useful to detect if there is at least
	 * one model object with a certain flag set.
	 */
	public final int getAccumulatedFlags() {
		int flags = 0;
		obtain();
		try {
			for (int i=modelList.size()-1; i>=0; --i)
				flags |= modelList.get(i).getFlags();
		} finally {
			release();
		}
		return flags;
	}

	/**
	 * Gets the model by id, if it doesn't exists, creates a
	 * new instance.
	 */
	public final AbstractModel getOrCreateModel(final Class<?> modelClass, final Long objectId) {
		if (objectId == null || objectId.longValue() == 0 || modelClass == null)
			return null;
		final AbstractModel model;
		obtain();
		try {
			model = modelMap.containsKey(objectId)
				? modelMap.get(objectId) : createModel(modelClass, objectId);
		} finally {
			release();
		}
		if (modelClass.equals(model.getClass()))
			return model;
		String msg = Strings.concat("class mismatch (", modelClass, "!=", model.getClass(), ") for object id ", objectId);
		Logger.fatal(msg);
		throw new RuntimeException(msg);
	}

	// --------------------------------------------------------------
	// ---
	// --- Model creation for streaming
	// ---
	// --------------------------------------------------------------
	private final static Class<?>[] MODEL_CONSTRUCTOR_SIGNATURE = { ModelContext.class, Long.class };

	protected AbstractModel createModel(final Class<?> modelClass, final Long objectId) {
		try {
			final Constructor<?> constructor = modelClass.getDeclaredConstructor(MODEL_CONSTRUCTOR_SIGNATURE);
			if (!constructor.isAccessible())
				constructor.setAccessible(true);
			return (AbstractModel) constructor.newInstance(this, objectId);
		} catch (final NoSuchMethodException e) {
			throw new IllegalArgumentException(Strings.concat(modelClass, " has no suitable constructor"), e);
		} catch (final ReflectiveOperationException e) {
			throw new RuntimeException(e.getMessage(), e);
		}
	}

	/**
	 * Returns an iterator of recently deleted object id's.
	 * The iterator depends directly on an internal collection,
	 * thus the model context should:
	 * a.) be locked before calling
	 * b.) not be modified while iterating.
	 */
	public List<AbstractModel> getDeletionList() {
		final List<AbstractModel> list = new ArrayList<AbstractModel>(256);
		obtain();
		try {
			for (int i=modelList.size()-1; i>=0; --i) {
				final AbstractModel model = modelList.get(i);
				if (model.isDeleted())
					list.add(model);
			}
		} finally {
			release();
		}
		return list;
	}
	
	/**
	 * Returns the time stamp when this context was last used 
	 */
	public long getLastTouched() {
		return this.lastTouched;
	}

	protected void touch() {
		this.lastTouched = System.currentTimeMillis();
	}
	
	@Override
	public boolean equals(Object o) {
		return o instanceof ModelContext ? ((ModelContext) o).begin == begin : false;
	}
	
	@Override
	public int hashCode() {
		return ((int) begin) ^ ((int) (begin >> 32));
	}

	/**
	 * Description string for the context
	 */
	@Override
	public final String toString() {
		return StringMakerFriendly.toString(this);
	}

	@Override
	public void toStringMaker(final StringMaker target) {
		final long age = ((999L+getContextAge())/1000L);
		final long idle = (System.currentTimeMillis() - lastTouched) / 1000L;
		target.append("ModelContext(date=");
		target.append(getBegin(), TimestampUtil.DATE10);
		target.append(",age=");
		target.append(age);
		target.append("s,idle=");
		target.append(idle);
		target.append("s)");
	}

	@SuppressWarnings("unchecked")
	private <U extends AbstractMitarbeiter> U getMitarbeiterImpl(final String shortName, final Class<U> mitarbeiterClass) {
		U result = null;
		obtain();
		try {
			for (int i=modelList.size()-1; i>=0; --i) {
				final AbstractModel model = modelList.get(i);
				if (model instanceof AbstractMitarbeiter
						&& mitarbeiterClass.isAssignableFrom(model.getClass())) {
					final U mitarbeiter = (U) model;
					if (Strings.equalsIgnoreCase(mitarbeiter.getShortName(), shortName)) {
						if (result == null) {
							result = mitarbeiter;
						} else if (!Strings.equalsIgnoreCase(shortName, DSPConstants.MONT_SHORTNAME)) {
							Logger.warn("Die Mitarbeiter " + result + " und " + mitarbeiter + " haben das gleiche Kurzzeichen!");
						}
					}
				}
			}
		} finally {
			release();
		}
		return result;
	}

	/**
	 * Find Mitarbeiter by short name
	 */
	public <U extends AbstractMitarbeiter> U getMitarbeiter(final String shortName, final Class<U> type) {
		if (Strings.isEmpty(shortName))
			return null;
		if (DSPConstants.MONT_SHORTNAME.equalsIgnoreCase(shortName))
			return (U) getMitarbeiter(DSPConstants.MONT_PRESENTO_ID, type);
		return getMitarbeiterImpl(shortName, type);
	}

	/**
	 * Find Mitarbeiter by PRESENTO id
	 */
	@SuppressWarnings("unchecked")
	public <U extends AbstractMitarbeiter> U getMitarbeiter(final long presentoId, final Class<U> type) {
		if (presentoId == DSPConstants.PRESENTO_NULL_ID)
			return null;
		obtain();
		try {
			for (final AbstractModel model : modelList) {
				if (model instanceof AbstractMitarbeiter 
						&& type.isAssignableFrom(model.getClass())
						&& ((U) model).getPresentoId() == presentoId) {
					return (U) model;
				}
			}
		} finally {
			release();
		}
		return null;
	}

	/**
	 * Detects if the context has been modified.
	 * 
	 * The context is considered dirty if any of the objects
	 * within the context is marked dirty, deleted or new.
	 */
	public boolean isModified() {
		final int mask = (MDLF_DELETED | MDLF_DIRTY | MDLF_NEW | MDLF_REMOVED);
		boolean result = false;
		obtain();
		try {
			for (int i=modelList.size()-1; i>=0; --i) {
				final int flags = modelList.get(i).getFlags();
				if (0!=(flags&mask)) {
					result = true;
					break;
				}
			}
		} finally {
			release();
		}
		return result;
	}
	
	/**
	 * Returns the age of this context in milliseconds
	 */
	public final long getContextAge() {
		return System.currentTimeMillis() - createdTimestamp;
	}

	public boolean checkReferentialIntegrity() {
		boolean result = true;
		obtain();
		try {
			for (int i=modelList.size()-1; i>=0; --i) {
				final AbstractModel model = modelList.get(i);
				result &= model.checkReferentialIntegrity();
			}
		} catch (final Exception e) {
			result = false;
			Logger.warn(Strings.concat("Referential integrity check failed for ", this));
			Logger.warn(e);
		} finally {
			release();
		}
		return result;
	}

	/**
	 * Checks if Date falls inside the context boundaries
	 */
	public final boolean isInsideWeek(final Date date) {
		if (date == null)
			return false;
		final long timestamp = DateUtil.toTimestamp(date);
		return timestamp >= begin && timestamp < end;
	}

}

package ch.eisenring.dispo.shared.model;

import static ch.eisenring.dispo.shared.DSPConstants.MDLF_NONE;
import static ch.eisenring.dispo.shared.codetables.ColorCode.COLOR_DEFAULT;
import static ch.eisenring.dispo.shared.codetables.EmploymentRole.MITARBEITER_CHAUFEUR;
import static ch.eisenring.dispo.shared.codetables.ZuordnungCode.ZUORDNUNG_FAHRAUFTRAG;
import static ch.eisenring.dispo.shared.codetables.ZuordnungCode.ZUORDNUNG_OBSOLETE;
import static ch.eisenring.dispo.shared.codetables.ZuordnungCode.ZUORDNUNG_OVERFLOW;

import java.io.IOException;

import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.io.StreamReader;
import ch.eisenring.core.io.StreamWriter;
import ch.eisenring.dispo.shared.codetables.ColorCode;
import ch.eisenring.dispo.shared.codetables.ZuordnungCode;
import ch.eisenring.dispo.shared.modelapi.Zuordnung;

public class FeinZuordnung extends AbstractZuordnung implements Zuordnung {

	private final RelationOne<FeinZuordnung, Auftrag> relationAuftrag = new RelationOne<FeinZuordnung, Auftrag>(this) {
		@Override
		protected RelationBase<Auftrag, FeinZuordnung> getOtherEnd(final Auftrag model) {
			return model.getFeinZuordnungenRelation();
		}
		@Override
		public void add(final Auftrag model) {
			super.add(model);
			updateAuftrag();
		}
		@Override
		protected void resolveRead(final int mode) throws IOException {
			super.resolveRead(mode);
			updateAuftrag();
		}
		private void updateAuftrag() {
			final Auftrag a = get();
			if (a != null) {
				setAuftragNummer(a.getAuftragnummer());
				setAuftragGSE(a.getGSEAuftrag());
				lastConnectedAuftrag = a.getId().longValue();
			}
		}
	};
	private final RelationOne<FeinZuordnung, Mitarbeiter> relationMitarbeiter = new RelationOne<FeinZuordnung, Mitarbeiter>(this) {
		@Override
		protected RelationBase<Mitarbeiter, FeinZuordnung> getOtherEnd(final Mitarbeiter model) {
			return model.getFeinZuordungenRelation();
		}
		@Override
		public void add(final Mitarbeiter model) {
			super.add(model);
			updateMitarbeiter();
		}
		@Override
		protected void resolveRead(final int mode) throws IOException {
			super.resolveRead(mode);
			updateMitarbeiter();
		}
		private void updateMitarbeiter() {
			AbstractMitarbeiter m = getMitarbeiter();
			if (m != null)
				lastConnectedMitarbeiter = m.getId().longValue();
		}
	};

	private ZuordnungCode type = ZUORDNUNG_FAHRAUFTRAG;
	private ColorCode color = COLOR_DEFAULT;
	private String prjNrText;
	private String fahrzeug;
	private String anhaenger;
	private long lastConnectedMitarbeiter;
	private long lastConnectedAuftrag;
	
	public FeinZuordnung(final ModelContext context) {
		this(context, null);
	}

	protected FeinZuordnung(final ModelContext context, final Long objectId) {
		super(context, objectId);
	}

	@Override
	public boolean isEditable() {
		return true;
	}

	@Override
	public void delete() {
		final Auftrag auftrag = getAuftrag();
		relationAuftrag.clear();
		relationMitarbeiter.clear();
		super.delete();

		final ZuordnungCode type = getType();
		if (auftrag != null && type != null && type.isMontagePlanung()) {
			// check if Auftrag needs to be reset
			auftrag.resetDisposition();
		}
	}

	protected RelationOne<FeinZuordnung, Mitarbeiter> getMitarbeiterRelation() {
		return this.relationMitarbeiter;
	}

	public AbstractMitarbeiter getMitarbeiter() {
		return (AbstractMitarbeiter) this.relationMitarbeiter.get();
	}
	
	public AbstractMitarbeiter getLastConnectedMitarbeiter() {
		return (AbstractMitarbeiter) getContext().getModel(Long.valueOf(lastConnectedMitarbeiter));
	}
	
	public void setMitarbeiter(final AbstractMitarbeiter mitarbeiter) {
		this.relationMitarbeiter.add((Mitarbeiter) mitarbeiter);
	}
	
	protected RelationOne<FeinZuordnung, Auftrag> getAuftragRelation() {
		return relationAuftrag;
	}

	public Auftrag getAuftrag() {
		return relationAuftrag.get();
	}
	
	public Auftrag getLastConnectedAuftrag() {
		return (Auftrag) getContext().getModel(Long.valueOf(lastConnectedAuftrag));
	}

	public void setAuftrag(Auftrag auftrag) {
		this.relationAuftrag.add(auftrag);
	}
	
	/**
	 * Derived label getter (hacks for changing projektnummer)
	 */
	@Override
	public String getLabel() {
		final Auftrag a = getAuftrag();
		final String p = a == null ? null : a.getProjektnummer();
		String l = super.getLabel();
		if (l == null) {
			l = p;
		} else if (p != null && !l.startsWith(p)) {
			l = Strings.concat(p, " ", l);
		}
		return Strings.trim(l, 30);
	}
	
	@Override
	public final ZuordnungCode getType() {
		return type;
	}
	
	public void setType(ZuordnungCode type) {
		updateDirty(this.type, type, MDLF_NONE);
		this.type = type;
	}

	public String getFahrzeug() {
		return fahrzeug;
	}
	
	public void setFahrzeug(String fahrzeug) {
		fahrzeug = Strings.clean(fahrzeug, 30);
		updateDirty(this.fahrzeug, fahrzeug, MDLF_NONE);
		this.fahrzeug = fahrzeug;
	}
	
	public String getAnhaenger() {
		return anhaenger;
	}
	
	public void setAnhaenger(String anhaenger) {
		anhaenger = Strings.clean(anhaenger, 30);
		updateDirty(this.anhaenger, anhaenger, MDLF_NONE);
		this.anhaenger = anhaenger;
	}
	
	public String getPrjNrText() {
		return prjNrText;
	}
	
	public void setPrjNrText(String prjNrText) {
		prjNrText = Strings.clean(prjNrText, 250);
		updateDirty(this.prjNrText, prjNrText, MDLF_NONE);
		this.prjNrText = prjNrText;
	}

	public ColorCode getColor() {
		return color;
	}
	
	public void setColor(ColorCode color) {
		updateDirty(this.color, color, MDLF_NONE);
		this.color = color;
	}

	@Override
	public void write(final StreamWriter output) throws IOException {
		super.write(output);
		relationAuftrag.write(output);
		relationMitarbeiter.write(output);
		output.writeLong(lastConnectedAuftrag);
		output.writeLong(lastConnectedMitarbeiter);
		output.writeCode(getType());
		output.writeCode(getColor());
		output.writeString(getPrjNrText());
		output.writeString(getFahrzeug());
		output.writeString(getAnhaenger());
	}
	
	@Override
	public void read(StreamReader input) throws IOException {
		super.read(input);
		relationAuftrag.read(input);
		relationMitarbeiter.read(input);
		lastConnectedAuftrag = input.readLong();
		lastConnectedMitarbeiter = input.readLong();
		setType(input.readCode(ZuordnungCode.class));
		setColor(input.readCode(ColorCode.class));
		setPrjNrText(input.readString());
		setFahrzeug(input.readString());
		setAnhaenger(input.readString());
	}
	
	@Override
	public void resolveRead() throws IOException {
		super.resolveRead();
		relationAuftrag.resolveRead();
		relationMitarbeiter.resolveRead();
	}
	
	public boolean isEchterFahrauftrag() {
		// Echter Fahrauftrag muss vom Typ Fahrauftrag sein
		// UND auf einem Chauffeur platziert sein
		if (ZUORDNUNG_FAHRAUFTRAG.equals(getType())) {
			AbstractMitarbeiter m = getMitarbeiter();
			if (m != null && MITARBEITER_CHAUFEUR.equals(m.getEmploymentRole())) {
				return true;
			}
		}
		return false;
	}

	@Override
	public int getNavigateToAuftragNummer() {
		if (null==getAuftrag()) {
			final ZuordnungCode type = getType(); 
			if (ZUORDNUNG_OBSOLETE.equals(type) ||
				ZUORDNUNG_OVERFLOW.equals(type)) {
				return getAuftragNummer();
			}
		}
		return 0;
	}

	@Override
	public boolean checkReferentialIntegrity() {
		return super.checkReferentialIntegrity() &&
			   relationAuftrag.checkReferentialIntegrity() &&
			   relationMitarbeiter.checkReferentialIntegrity();
	}

}

package ch.eisenring.dispo.shared.network.packets;

import ch.eisenring.core.io.AutoStreamed;

public final class PacketContextVote extends AbstractDSPPacket {

	@AutoStreamed
	private long contextDate;
	
	@AutoStreamed
	private int points;
	
	PacketContextVote() {
		super(CAPS_SYNCHRONOUS, PRIORITY_NOTIFY);
	}

	public static PacketContextVote create(final long contextDate, final int points) {
		final PacketContextVote packet = new PacketContextVote();
		packet.contextDate = contextDate;
		packet.points = points;
		return packet;
	}

	public long getContextDate() {
		return contextDate;
	}

	public int getPoints() {
		return points;
	}

}

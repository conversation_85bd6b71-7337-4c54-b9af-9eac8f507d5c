package ch.eisenring.dispo.shared.codetables;

import ch.eisenring.core.codetype.StaticCode;
import ch.eisenring.core.resource.ImageResource;
import ch.eisenring.dispo.shared.resource.images.Images;

public final class AbrufModus extends StaticCode {

	public final static AbrufModus ABRUFEN = new AbrufModus(
			1, Integer.valueOf(1), "Abruf", "Abrufen",
			Images.TERMIN_OK, "Abruf-Liste.xlsx");

	public final static AbrufModus VERSCHIEBEN = new AbrufModus(
			2, Integer.valueOf(2), "Verschiebung", "Verschieben",
			Images.TERMIN_BAD, "Verschiebungen-Liste.xlsx");
	
	private final String xlsName;

	private AbrufModus(final int id, final Object key,
			final String shortText, final String longText,
			final ImageResource icon, final String xlsName) {
		super(id, key, shortText, longText);
		setData(KEY_ICON, icon);
		this.xlsName = xlsName;
	}

	public String getXLSName() {
		return xlsName;
	}

}

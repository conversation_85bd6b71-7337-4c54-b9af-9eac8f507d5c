package ch.eisenring.dispo.shared.network.packets;

import ch.eisenring.core.io.AutoStreamed;

public final class PacketGUUebersichtRequest extends AbstractDSPPacket {

	@AutoStreamed
	private Integer subjektNummer;

	@AutoStreamed
	private long dateFrom;

	@AutoStreamed
	private long dateUpto;

	private PacketGUUebersichtRequest() {
		super(CAPS_SEQUENCE_REQUEST, PRIORITY_REQUEST);
	}

	public static PacketGUUebersichtRequest create(final Integer subjektNummer,
			final long dateFrom, final long dateUpto) {
		final PacketGUUebersichtRequest packet = new PacketGUUebersichtRequest();
		packet.subjektNummer = subjektNummer;
		packet.dateFrom = dateFrom;
		packet.dateUpto = dateUpto;
		return packet;
	}

	// --------------------------------------------------------------
	// ---
	// --- Packet specific API
	// ---
	// --------------------------------------------------------------
	public long getDateFrom() {
		return dateFrom;
	}

	public long getDateUpto() {
		return dateUpto;
	}

	public Integer getSubjektNummer() {
		return subjektNummer;
	}

	// --------------------------------------------------------------
	// ---
	// --- Base class overrides
	// ---
	// --------------------------------------------------------------

	// none

}

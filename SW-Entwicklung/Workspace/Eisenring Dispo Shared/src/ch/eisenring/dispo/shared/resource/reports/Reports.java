package ch.eisenring.dispo.shared.resource.reports;

import ch.eisenring.print.shared.resource.ReportResource;

public interface Reports extends ch.eisenring.print.shared.resource.reports.Reports {

	ReportResource REPORT_MONTEUR_WOCHE_AUFTRAG = new ReportResource(
			Reports.class, "Monteur Wochenplanung", "MonteurWochenAuftragListe");
	
	ReportResource REPORT_MONTEUR_TAG_AUFTRAG = new ReportResource(
			Reports.class, "Monteur Tagesplanung", "MonteurTagAuftragListe");

	ReportResource REPORT_FOTOBERICHT_MONTAGE = new ReportResource(
			Reports.class, "Fotobericht Montage", "FotoBerichtMontage");

	ReportResource REPORT_DISPOEVENT_LIST = new ReportResource(
			Reports.class, "Ereignis-Liste", "EreignisListe");

	ReportResource REPORT_MONTAGE_RAPPORT = new ReportResource(
			Reports.class, "Montagerapport", "MontageRapport");

	ReportResource REPORT_GRANITSERVICEAUFTRAG = new ReportResource(
			Reports.class, "Granitservice Auftrag", "ServiceAuftragPfyn");

	ReportResource REPORT_SERVICEAUFTRAGSC = new ReportResource(
			Reports.class, "Serviceauftrag", "ServiceAuftragSC");

	ReportResource REPORT_MBPABSCHLUSSSEITE = new ReportResource(
			Reports.class, "MBPAbschlussSeite", "MBPAbschlussSeite");

	ReportResource REPORT_MBPQRCODESSEITE = new ReportResource(
			Reports.class, "MBPQRCodePage", "MBPQRCodePage");


}

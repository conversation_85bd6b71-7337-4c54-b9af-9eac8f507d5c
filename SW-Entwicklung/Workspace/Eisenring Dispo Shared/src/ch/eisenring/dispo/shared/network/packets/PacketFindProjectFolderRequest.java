package ch.eisenring.dispo.shared.network.packets;

import ch.eisenring.core.io.AutoStreamed;

public class PacketFindProjectFolderRequest extends AbstractDSPPacket {

	@AutoStreamed
	private String projektnummer;

	private PacketFindProjectFolderRequest() {
		super(CAPS_SEQUENCE_REQUEST, PRIORITY_REQUEST);
	}

	public static PacketFindProjectFolderRequest create(final String projektnummer) {
		final PacketFindProjectFolderRequest packet = new PacketFindProjectFolderRequest();
		packet.projektnummer = projektnummer;
		return packet;
	}

	public String getProjektnummer() {
		return projektnummer;
	}

	// --------------------------------------------------------------
	// ---
	// --- AbstractPacket implementation & overrides
	// ---
	// --------------------------------------------------------------

	// none so far

}

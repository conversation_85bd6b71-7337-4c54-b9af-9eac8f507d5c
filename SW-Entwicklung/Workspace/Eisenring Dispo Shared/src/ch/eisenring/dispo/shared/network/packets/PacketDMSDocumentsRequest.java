package ch.eisenring.dispo.shared.network.packets;

import java.io.IOException;

import ch.eisenring.core.io.StreamReader;
import ch.eisenring.core.io.StreamWriter;

/**
 * Request all relevant documents for project number
 */
public class PacketDMSDocumentsRequest extends AbstractDSPPacket {

	private String projectnumber;

	private PacketDMSDocumentsRequest() {
		super(CAPS_SEQUENCE_REQUEST, PRIORITY_REQUEST);
	}

	// --------------------------------------------------------------
	// ---
	// --- Factory methods
	// ---
	// --------------------------------------------------------------
	public static PacketDMSDocumentsRequest create(final String projectnumber) {
		final PacketDMSDocumentsRequest packet = new PacketDMSDocumentsRequest();
		packet.projectnumber = projectnumber;
		return packet;
	}

	// --------------------------------------------------------------
	// ---
	// --- Packet API
	// ---
	// --------------------------------------------------------------
	public String getProjectnumber() {
		return projectnumber;
	}

	// --------------------------------------------------------------
	// ---
	// --- Base class overrides
	// ---
	// --------------------------------------------------------------
	@Override
	protected void readImpl(final StreamReader reader) throws IOException {
		super.readImpl(reader);
		projectnumber = reader.readString();
	}
	
	@Override
	protected void writeImpl(final StreamWriter writer) throws IOException {
		super.writeImpl(writer);
		writer.writeString(projectnumber);
	}

}

package ch.eisenring.dispo.shared.codetables;

import ch.eisenring.core.codetype.StaticCode;

public final class DSPObjectType extends StaticCode {

	public final static DSPObjectType AUFTRAG = new DSPObjectType(1, "Auftrag");
	public final static DSPObjectType GROBPLANUNG = new DSPObjectType(2, "Grobplanung");
	public final static DSPObjectType FEINPLANUNG = new DSPObjectType(3, "Feinplanung");
	
	private DSPObjectType(final int id, final String text) {
		super(id, Integer.valueOf(id), text, text);
	}

}

package ch.eisenring.dispo.shared.network.packets;

import ch.eisenring.core.io.AutoStreamed;

public final class PacketDMSTemplateRequest extends AbstractDSPPacket {

	@AutoStreamed
	private int documentTemplateId;

	private PacketDMSTemplateRequest() {
		super(CAPS_SEQUENCE_REQUEST, PRIORITY_REQUEST);
	}

	// --------------------------------------------------------------
	// ---
	// --- Create Factories
	// ---
	// --------------------------------------------------------------
	public static PacketDMSTemplateRequest create(final int documentTemplateId) {
		final PacketDMSTemplateRequest packet = new PacketDMSTemplateRequest();
		packet.documentTemplateId = documentTemplateId;
		return packet;
	}

	// --------------------------------------------------------------
	// ---
	// --- Packet API
	// ---
	// --------------------------------------------------------------
	public int getDocumentTemplateId() {
		return documentTemplateId;
	}

	// --------------------------------------------------------------
	// ---
	// --- Base class overrides
	// ---
	// --------------------------------------------------------------

	// none

}

package ch.eisenring.dispo.shared.event;

import static ch.eisenring.dispo.shared.codetables.DispoEventCode.EVENT_UNKNOWN;

import java.nio.charset.StandardCharsets;

import ch.eisenring.core.StringUtil;
import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.io.file.FileItem;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.core.platform.Platform;
import ch.eisenring.core.template.AbstractTemplate;
import ch.eisenring.dispo.shared.codetables.DispoEventCode;
import ch.eisenring.email.EMailTemplate;

public final class DispoEventTemplate extends EMailTemplate {

	private final static Object LOCK = new Object();
	private final static List<DispoEventTemplate> TEMPLATE_LIST = new ArrayList<>();
	
	private final int id;

	private DispoEventTemplate(final int id) {
		super("");
		this.id = id;
		TEMPLATE_LIST.remove(this);
		TEMPLATE_LIST.add(this);
	}

	/****************************************************************
	 * Static helpers 
	 ****************************************************************/
	/**
	 * Get locking object for static data
	 */
	protected static Object getLock() {
		return LOCK;
	}

	/**
	 * Find template by DispoEventType
	 */
	public static DispoEventTemplate getTemplate(DispoEventCode eventType) {
		return getTemplate(null==eventType ? EVENT_UNKNOWN.getId() : eventType.getId());
	}

	/**
	 * Find template by id
	 */
	public static DispoEventTemplate getTemplate(int id) {
		DispoEventTemplate template = null;
		synchronized (getLock()) {
			for (int i=TEMPLATE_LIST.size()-1; i>=0; --i) {
				template = TEMPLATE_LIST.get(i);
				if (id==template.getId()) {
					return template;
				}
			}
		}
		if (EVENT_UNKNOWN.getId()==id) {
			return null;
		} else {
			return getTemplate(EVENT_UNKNOWN.getId());
		}
	}

	/**
	 * Creates a new template with the given id.
	 */
	public static DispoEventTemplate create(final int id) {
		synchronized (getLock()) {
			DispoEventTemplate template = getTemplate(id);
			if (null==template || template.getId()!=id) {
				template = new DispoEventTemplate(id);
			}
			return template;
		}
	}

	public int getId() {
		return id;
	}

	/**
	 * Gets the event subject template string
	 */
	public String getSubject() {
		final String template = getTemplateString();
		final String subject = StringUtil.getLines(template, 0, 0);
		return Strings.trim(subject);
	}

	/**
	 * Gets the event body template string
	 */
	public String getBody() {
		return StringUtil.getLines(getTemplateString(), 1, Integer.MAX_VALUE);
	}

	/****************************************************************
	 * Loads template(s) from disk
	 ****************************************************************/
	@SuppressWarnings("unchecked")
	public static void loadTemplates(String templatePath, final Class typeClass) {
		templatePath = templatePath.replace('\\', '/');
		if (!templatePath.endsWith("/")) {
			templatePath += '/';
		}
		templatePath = Platform.getPlatform().toPlatformSpecificPath(templatePath);

		// load all templates for this type
		final List<DispoEventCode> typeList = (List) AbstractCode.getInstances(typeClass);
		for (int i=typeList.size()-1; i>=0; --i) {
			final DispoEventCode type = typeList.get(i);
			final FileItem file = FileItem.create(Strings.concat(templatePath, type.getId(), ".template"));
			final DispoEventTemplate template = create(type.getId());
			final String s = AbstractTemplate.loadTemplate(file, StandardCharsets.ISO_8859_1);
			template.setTemplateString(s);
			Logger.info(Strings.concat("template \"", file, "\" loaded"));
		}
	}

	/****************************************************************
	 * Object overrides
	 ****************************************************************/
	@Override
	public int hashCode() {
		return id;
	}

	@Override
	public boolean equals(Object o) {
		return o instanceof DispoEventTemplate ? ((DispoEventTemplate) o).id==id : false;
	}

}

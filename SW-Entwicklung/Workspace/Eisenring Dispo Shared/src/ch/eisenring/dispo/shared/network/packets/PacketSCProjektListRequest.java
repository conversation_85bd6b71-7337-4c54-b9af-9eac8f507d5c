package ch.eisenring.dispo.shared.network.packets;

import ch.eisenring.core.datatypes.date.Period;
import ch.eisenring.core.io.AutoStreamed;

public final class PacketSCProjektListRequest extends AbstractDSPPacket {

	@AutoStreamed
	private Period period;

	private PacketSCProjektListRequest() {
		super(CAPS_SEQUENCE_REQUEST, PRIORITY_REQUEST);
	}

	// --------------------------------------------------------------
	// ---
	// --- Factory methods
	// ---
	// --------------------------------------------------------------
	public static PacketSCProjektListRequest create(final Period period) {
		final PacketSCProjektListRequest packet = new PacketSCProjektListRequest();
		packet.period = period;
		return packet;
	}


	// --------------------------------------------------------------
	// ---
	// --- Packet specific API
	// ---
	// --------------------------------------------------------------
	public Period getPeriod() {
		return period;
	}

}

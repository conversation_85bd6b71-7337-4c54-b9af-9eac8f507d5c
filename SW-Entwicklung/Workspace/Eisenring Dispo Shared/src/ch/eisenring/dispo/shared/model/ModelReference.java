package ch.eisenring.dispo.shared.model;

/**
 * Model object reference
 * 
 * (can retrieve the proper object, even if the
 *  holding context has been flushed)
 */
public final class ModelReference<T extends AbstractModel> {

	private T model;
//	private final Long modelId;
//	private final Date contextDate;
//	private final ContextCache contextCache;

	public ModelReference(final T model, final DSPContextCache cache) {
		this.model = model;
//		this.modelId = model.getId();
//		this.contextDate = model.getContext().getDate();
//		this.contextCache = cache;
	}

	/**
	 * Retrieves the referenced model from the context cache
	 */
	@SuppressWarnings("unchecked")
	public T getModel(final DSPContextCache cache) {
		return model;
//		if (cache == null) {
//			return null;
//		}
//		final ModelContext context = cache.getContext(contextDate, true);
//		if (context == null) {
//			return null;
//		} else {
//			return (T) context.getModel(modelId);
//		}
	}
	
	/**
	 * Retrieves the referenced model from the context cache
	 */
	@SuppressWarnings("unchecked")
	public T getModel() {
		return model;
//		return getModel(contextCache);
	}
	
}

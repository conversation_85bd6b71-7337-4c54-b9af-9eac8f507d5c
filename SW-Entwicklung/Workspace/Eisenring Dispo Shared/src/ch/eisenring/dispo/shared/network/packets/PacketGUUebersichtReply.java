package ch.eisenring.dispo.shared.network.packets;

import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.io.AutoStreamed;
import ch.eisenring.core.util.file.FileImage;

public class PacketGUUebersichtReply extends AbstractDSPPacket {

	@AutoStreamed
	private FileImage fileImage;
	
	private PacketGUUebersichtReply() {
		super(CAPS_SEQUENCE_REPLY | CAPS_ERRORMESSAGE, PRIORITY_REPLY);
	}

	public static PacketGUUebersichtReply create(final PacketGUUebersichtRequest request, final ErrorMessage error) {
		final PacketGUUebersichtReply packet = new PacketGUUebersichtReply();
		packet.setReplyId(request);
		packet.setMessage(error);
		return packet;
	}

	public static PacketGUUebersichtReply create(final PacketGUUebersichtRequest request, final FileImage fileImage) {
		final PacketGUUebersichtReply packet = new PacketGUUebersichtReply();
		packet.setReplyId(request);
		packet.setMessage(ErrorMessage.OK);
		packet.fileImage = fileImage;
		return packet;
	}

	// --------------------------------------------------------------
	// ---
	// --- Packet specific API
	// ---
	// --------------------------------------------------------------
	public FileImage getFileImage() {
		return fileImage;
	}

	// --------------------------------------------------------------
	// ---
	// --- Base class overrides
	// ---
	// --------------------------------------------------------------

	// none

}

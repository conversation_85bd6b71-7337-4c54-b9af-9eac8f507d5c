package ch.eisenring.dispo.shared.network.packets;

import ch.eisenring.core.io.AutoStreamed;

public final class PacketSearchRequest extends AbstractDSPPacket {

	@AutoStreamed
	private int auftragnummer;

	@AutoStreamed
	private String projektnummer;

	PacketSearchRequest() {
		super(CAPS_SEQUENCE_REQUEST, PRIORITY_REQUEST);
	}

	public static PacketSearchRequest create(final int auftragnummer, final String projektnummer) {
		final PacketSearchRequest packet = new PacketSearchRequest();
		packet.auftragnummer = auftragnummer;
		packet.projektnummer = projektnummer;
		return packet;
	}

	public int getAuftragnummer() {
		return auftragnummer;
	}
	
	public String getProjektnummer() {
		return projektnummer;
	}

	// --------------------------------------------------------------
	// ---
	// --- AbstractPacket implementation & overrides
	// ---
	// --------------------------------------------------------------

	// none
}

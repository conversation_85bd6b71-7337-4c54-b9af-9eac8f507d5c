package ch.eisenring.dispo.shared.pojo;

import java.io.IOException;

import ch.eisenring.core.io.StreamReader;
import ch.eisenring.core.io.StreamWriter;
import ch.eisenring.core.io.Streamable;
import ch.eisenring.core.io.binary.BinaryHolder;

public class DSPBinary implements Streamable {

	private long rowId;
	private BinaryHolder rawData;

	// --------------------------------------------------------------
	// ---
	// --- Getters / Setters
	// ---
	// --------------------------------------------------------------
	public long getRowId() {
		return rowId;
	}

	public BinaryHolder getRawData() {
		return rawData;
	}

	public void setRawData(final BinaryHolder rawData) {
		this.rawData = rawData;
	}
	
	// --------------------------------------------------------------
	// ---
	// --- Streamable implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public void read(final StreamReader reader) throws IOException {
		rowId = reader.readLong();
		rawData = reader.readBinaryHolder();
	}

	@Override
	public void write(final StreamWriter writer) throws IOException {
		writer.writeLong(rowId);
		writer.writeBinaryHolder(rawData);
	}

	// --------------------------------------------------------------
	// ---
	// --- Object overrides
	// ---
	// --------------------------------------------------------------
	@Override
	public int hashCode() {
		return ((int) rowId) ^ ((int) (rowId >> 32));
	}

	@Override
	public boolean equals(final Object o) {
		return o instanceof DSPBinary && ((DSPBinary) o).rowId == rowId;
	}

}

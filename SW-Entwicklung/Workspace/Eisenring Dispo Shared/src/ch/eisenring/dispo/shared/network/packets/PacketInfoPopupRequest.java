package ch.eisenring.dispo.shared.network.packets;

import ch.eisenring.logiware.LWAuftragKey;
import ch.eisenring.logiware.LWProjektKey;

/**
 * Requests additional information for displaying the info-popup from the server
 */
public final class PacketInfoPopupRequest extends PacketInfoPopupBase {
	
	private PacketInfoPopupRequest() {
		super(CAPS_SEQUENCE_REQUEST, PRIORITY_REQUEST + 1);
	}

	// --------------------------------------------------------------
	// ---
	// --- Factory methods
	// ---
	// --------------------------------------------------------------
	public static PacketInfoPopupRequest create(final LWProjektKey projektKey, 
			final LWAuftragKey auftragKey, final int mode) {
		final PacketInfoPopupRequest packet = new PacketInfoPopupRequest();
		packet.auftragKey = auftragKey;
		packet.projektKey = projektKey;
		packet.mode = mode;
		return packet;
	}

}

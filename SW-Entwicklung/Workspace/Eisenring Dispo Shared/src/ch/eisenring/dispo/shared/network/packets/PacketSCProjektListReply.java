package ch.eisenring.dispo.shared.network.packets;

import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.datatypes.date.Period;
import ch.eisenring.core.io.AutoStreamed;
import ch.eisenring.core.util.file.FileImage;

public final class PacketSCProjektListReply extends AbstractDSPPacket {

	@AutoStreamed
	private Period period;

	@AutoStreamed
	private FileImage fileImage;

	private PacketSCProjektListReply() {
		super(CAPS_SEQUENCE_REPLY | CAPS_ERRORMESSAGE, PRIORITY_REPLY);
	}

	// --------------------------------------------------------------
	// ---
	// --- Factory methods
	// ---
	// --------------------------------------------------------------
	public static PacketSCProjektListReply create(final PacketSCProjektListRequest request, final ErrorMessage error) {
		final PacketSCProjektListReply packet = new PacketSCProjektListReply();
		packet.setReplyId(request);
		packet.setMessage(error);
		packet.period = request.getPeriod();
		return packet;
	}

	public static PacketSCProjektListReply create(final PacketSCProjektListRequest request, final FileImage fileImage) {
		final PacketSCProjektListReply packet = create(request, ErrorMessage.OK);
		packet.fileImage = FileImage.create(fileImage);
		return packet;
	}

	// --------------------------------------------------------------
	// ---
	// --- Packet specific API
	// ---
	// --------------------------------------------------------------
	public Period getPeriod() {
		return period;
	}

	public FileImage getFileImage() {
		return fileImage;
	}

}

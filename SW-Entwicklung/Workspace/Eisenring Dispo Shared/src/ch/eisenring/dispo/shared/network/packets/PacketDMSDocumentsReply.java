package ch.eisenring.dispo.shared.network.packets;

import java.io.IOException;

import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.Map;
import ch.eisenring.core.collections.alterableview.AlterableView;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.collections.impl.HashMap;
import ch.eisenring.core.io.AutoStreamed;
import ch.eisenring.core.io.StreamReader;
import ch.eisenring.core.io.StreamUtil;
import ch.eisenring.core.io.StreamWriter;
import ch.eisenring.core.util.file.FileImage;
import ch.eisenring.dms.service.codetables.DMSDocumentType;

/**
 * Request all relevant documents for project number
 */
public class PacketDMSDocumentsReply extends AbstractDSPPacket {

	@AutoStreamed
	private List<FileImage> ausfuehrungsPlaene = new ArrayList<>();

	@AutoStreamed
	private List<FileImage> granitPlaene = new ArrayList<>();

	private Map<DMSDocumentType, List<FileImage>> documentMap = new HashMap<>();

	private PacketDMSDocumentsReply() {
		super(CAPS_SEQUENCE_REPLY | CAPS_ERRORMESSAGE, PRIORITY_REPLY);
	}

	// --------------------------------------------------------------
	// ---
	// --- Factory methods
	// ---
	// --------------------------------------------------------------
	public static PacketDMSDocumentsReply create(final PacketDMSDocumentsRequest request) {
		final PacketDMSDocumentsReply packet = new PacketDMSDocumentsReply();
		packet.setReplyId(request);
		packet.setMessage(ErrorMessage.OK);
		return packet;
	}

	public static PacketDMSDocumentsReply create(final PacketDMSDocumentsRequest request, final ErrorMessage error) {
		final PacketDMSDocumentsReply packet = new PacketDMSDocumentsReply();
		packet.setReplyId(request);
		packet.setMessage(error);
		return packet;
	}

	// --------------------------------------------------------------
	// ---
	// --- Packet API
	// ---
	// --------------------------------------------------------------
	public List<FileImage> getAusfuehrungsPlaene() {
		return AlterableView.of(ausfuehrungsPlaene);
	}

	public List<FileImage> getGranitPlaene() {
		return AlterableView.of(granitPlaene);
	}

	public List<FileImage> getDocuments(final DMSDocumentType documentType) {
		List<FileImage> result = documentMap.get(documentType);
		if (result == null)
			return new ArrayList<>();
		return AlterableView.of(result);
	}

	public void addAusfuehrungsPlan(final FileImage fileImage) {
		ausfuehrungsPlaene.add(FileImage.create(fileImage));
	}

	public void addGranitPlan(final FileImage fileImage) {
		granitPlaene.add(FileImage.create(fileImage));
	}

	public void addDocument(final DMSDocumentType documentType, final FileImage fileImage) {
		List<FileImage> documents = documentMap.get(documentType);
		if (documents == null) {
			documents = new ArrayList<>();
			documentMap.put(documentType, documents);
		}
		documents.add(FileImage.create(fileImage));
	}

	// --------------------------------------------------------------
	// ---
	// --- Base class overrides
	// ---
	// --------------------------------------------------------------

	// none

	// --------------------------------------------------------------
	// ---
	// --- Streamable implementation
	// ---
	// --------------------------------------------------------------
	@Override
	protected void readImpl(final StreamReader reader) throws IOException {
		super.readImpl(reader);
		StreamUtil.readMap(reader, documentMap);
		StreamUtil.readCollection(reader, ausfuehrungsPlaene);
		StreamUtil.readCollection(reader, granitPlaene);
	}

	@Override
	protected void writeImpl(final StreamWriter writer) throws IOException {
		super.writeImpl(writer);
		StreamUtil.writeMap(writer, documentMap);
		StreamUtil.writeCollection(writer, ausfuehrungsPlaene);
		StreamUtil.writeCollection(writer, granitPlaene);
	}

}

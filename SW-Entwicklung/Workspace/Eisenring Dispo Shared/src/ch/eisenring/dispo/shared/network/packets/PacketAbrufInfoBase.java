package ch.eisenring.dispo.shared.network.packets;

import ch.eisenring.core.io.AutoStreamable;
import ch.eisenring.core.io.AutoStreamed;
import ch.eisenring.logiware.LWProjektKey;

public class PacketAbrufInfoBase extends AbstractDSPPacket {

	PacketAbrufInfoBase(final int caps, final int priority) {
		super(caps, priority);
	}

	public static class AbrufProjektInfo implements AutoStreamable {
		@AutoStreamed
		public LWProjektKey projektKey;
		
		@AutoStreamed
		public LWProjektKey basisKey;

		@AutoStreamed
		public String gpsKoordinaten;

		@AutoStreamed
		public String monteurShort;

		@AutoStreamed
		public String monteurName;
		
		@AutoStreamed
		public String monteurNatel;
	}

}

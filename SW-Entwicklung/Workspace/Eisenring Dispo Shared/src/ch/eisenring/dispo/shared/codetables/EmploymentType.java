package ch.eisenring.dispo.shared.codetables;

import ch.eisenring.core.codetype.StaticCode;

public final class EmploymentType extends StaticCode {

	public final static EmploymentType EMPTYPE_INTERNAL		= new EmploymentType(2900, "Intern", true);
	public final static EmploymentType EMPTYPE_TEMPORARY	= new EmploymentType(2901, "Te<PERSON><PERSON><PERSON><PERSON>", true);
	public final static EmploymentType EMPTYPE_UNTERAKKORDANT	= new EmploymentType(2902, "Unterakkordant", true);
	public final static EmploymentType EMPTYPE_VIRTUAL		= new EmploymentType(2904, "Platzhalter f. Planung", false);

	private final boolean countsAsEmployee;

	public EmploymentType(final int id, final String text, final boolean countAsEmployee) {
		super(id, Integer.valueOf(id), text, text);
		this.countsAsEmployee = countAsEmployee;
	}
	
	public boolean isCountedAsEmployee() {
		return countsAsEmployee;
	}

}

package ch.eisenring.dispo.shared.network.packets;

import java.io.IOException;

import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.Set;
import ch.eisenring.core.collections.alterableview.AlterableView;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.collections.impl.HashSet;
import ch.eisenring.core.datatypes.date.Period;
import ch.eisenring.core.io.StreamReader;
import ch.eisenring.core.io.StreamUtil;
import ch.eisenring.core.io.StreamWriter;
import ch.eisenring.dispo.shared.lw.LWBestellInfo;
import ch.eisenring.lw.api.LWAuftragPositionData;
import ch.eisenring.lw.api.LWEtappeData;
import ch.eisenring.lw.pojo.LWAuftragPositionDataPojo;
import ch.eisenring.lw.pojo.LWEtappePojo;

public final class PacketInfoPopupReply extends PacketInfoPopupBase {

	private PacketInfoPopupReply() {
		super(CAPS_SEQUENCE_REPLY | CAPS_ERRORMESSAGE, PRIORITY_REPLY + 1);
	}

	transient Set<LWAuftragPositionData> auftragPositionen = new HashSet<>();
	transient List<LWBestellInfo> bestellInfos = new ArrayList<>();
	transient LWEtappeData etappe;

	// --------------------------------------------------------------
	// ---
	// --- Factory methods
	// ---
	// --------------------------------------------------------------
	public static PacketInfoPopupReply create(final PacketInfoPopupRequest request, final ErrorMessage error) {
		final PacketInfoPopupReply packet = new PacketInfoPopupReply();
		packet.setReplyId(request, error);
		packet.auftragKey = request.getAuftragKey();
		packet.projektKey = request.getProjektKey();
		packet.mode = request.getMode();
		return packet;
	}

	// --------------------------------------------------------------
	// ---
	// --- API
	// ---
	// --------------------------------------------------------------
	public void addPosition(final LWAuftragPositionData position) {
		if (position == null)
			return;
		final LWAuftragPositionData p = LWAuftragPositionDataPojo.create(position);
		if (!auftragPositionen.contains(p))
			auftragPositionen.add(p);
	}

	public void setBestellInfos(final java.util.Collection<LWBestellInfo> bestellInfos) {
		if (bestellInfos == null || bestellInfos.isEmpty())
			return;
		this.bestellInfos.clear();
		this.bestellInfos.addAll(bestellInfos);
	}

	public List<LWAuftragPositionData> getPositionen() {
		return new ArrayList<>(auftragPositionen);
	}

	public List<LWBestellInfo> getBestellInfos() {
		return AlterableView.of(bestellInfos);
	}

	public LWEtappeData getEtappe() { return etappe; }

	public void setEtappe(final LWEtappeData etappe) {
		this.etappe = etappe == null ? null : LWEtappePojo.create(etappe);
	}

	// --------------------------------------------------------------
	// ---
	// --- Streaming
	// ---
	// --------------------------------------------------------------
	@Override
	protected void readImpl(final StreamReader reader) throws IOException {
		super.readImpl(reader);
		StreamUtil.readCollection(reader, auftragPositionen);
		StreamUtil.readCollection(reader, bestellInfos);
		etappe = (LWEtappeData) reader.readObject();
	}

	@Override
	protected void writeImpl(final StreamWriter writer) throws IOException {
		super.writeImpl(writer);
		StreamUtil.writeCollection(writer, auftragPositionen);
		StreamUtil.writeCollection(writer, bestellInfos);
		writer.writeObject(etappe);
	}

}

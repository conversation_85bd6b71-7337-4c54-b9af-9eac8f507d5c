package ch.eisenring.dispo.shared.model;

import static ch.eisenring.dispo.shared.DSPConstants.MDLF_NONE;
import static ch.eisenring.dispo.shared.model.RelationBase.RESOLVE_THROW_UNRESOLVED;

import java.io.IOException;
import java.util.Date;
import java.util.Iterator;

import ch.eisenring.core.datatypes.date.DateUtil;
import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.io.StreamReader;
import ch.eisenring.core.io.StreamWriter;
import ch.eisenring.dispo.shared.codetables.FormClassCode;

public class FormModel extends AbstractModel {

	private RelationOne<FormModel, Auftrag> relationAuftrag = new RelationOne<FormModel, Auftrag>(this) {
		@Override
		protected RelationBase<Auftrag, FormModel> getOtherEnd(final Auftrag model) {
			return model.getFormRelation();
		}
	};
	
	/**
	 * The type of the form
	 */
	private FormClassCode type;

	/**
	 * Ordering criterion
	 */
	private int ordinalNumber;
	
	/**
	 * Status of the form
	 */
	private int status;
	
	/**
	 * The version of this form
	 */
	private int version;

	/**
	 * Time when the form was created
	 */
	private long createTimestamp;
	private long updateTimestamp;
	
	/**
	 * User who create form
	 */
	private String createUser;
	private String updateUser;
	
	/**
	 * A human readable description of the form
	 */
	private String description;
	
	/**
	 * Content of the form (values of fields)
	 */
	private String content;
	
	public FormModel(final ModelContext context) {
		super(context, null);
		setCreateTime(new Date());
	}

	protected FormModel(final ModelContext context, final Long objectId) {
		super(context, objectId);
	}
	
	@Override
	public String getLabel() {
		final StringMaker b = StringMaker.obtain(128);
		final FormClassCode type = getType();
		//final String description = getDescription();
		final Date date = getCreateTime();
		b.append(type == null ? "???" : type.getText());
		b.append(" #");
		b.append(getOrdinalNumber());
		b.append(" (");
		b.append(date, TimestampUtil.DATE10);
		b.append(')');
		return b.release();
	}
	
	/**
	 * Gets a description when and who created the form and
	 * if it has been updated afterwards, who updated it last and when.
	 */
	public String getTrackingInfo() {
		final StringMaker b = StringMaker.obtain(64);
		b.append("Erstellt: ");
		b.append(getCreateUser());
		b.append(" (");
		b.append(getCreateTime(), TimestampUtil.DATETIME);
		b.append(")");
		if (getUpdateUser() != null) {
			b.append(" Zuletzt geändert: ");
			b.append(getUpdateUser());
			b.append(" (");
			b.append(getUpdateTime(), TimestampUtil.DATETIME);
			b.append(")");
		}
		return b.release();
	}

	/***************************************************************
	 * Serialization
	 ***************************************************************/
	@Override
	public void read(final StreamReader input) throws IOException {
		super.read(input);
		relationAuftrag.read(input);
		setType(input.readCode(FormClassCode.class));
		setVersion(input.readInt());
		setOrdinalNumber(input.readInt());
		setStatus(input.readInt());
		this.createTimestamp = input.readLong();
		this.updateTimestamp = input.readLong();
		setCreateUser(input.readString());
		setUpdateUser(input.readString());
		setDescription(input.readString());
		setContent(input.readString());
	}
	
	@Override
	public void write(final StreamWriter output) throws IOException {
		super.write(output);
		relationAuftrag.write(output);
		output.writeCode(getType());
		output.writeInt(getVersion());
		output.writeInt(getOrdinalNumber());
		output.writeInt(getStatus());
		output.writeLong(createTimestamp);
		output.writeLong(updateTimestamp);
		output.writeString(getCreateUser());
		output.writeString(getUpdateUser());
		output.writeString(getDescription());
		output.writeString(getContent());
	}
	
	@Override
	public void resolveRead() throws IOException {
		super.resolveRead();
		relationAuftrag.resolveRead(RESOLVE_THROW_UNRESOLVED);
	}

	@Override
	public boolean checkReferentialIntegrity() {
		return super.checkReferentialIntegrity() &&
			   relationAuftrag.checkReferentialIntegrity();
	}

	@Override
	public void delete() {
		super.delete();
		relationAuftrag.clear();
	}

	/***************************************************************
	 * Static helpers
	 ***************************************************************/
	public static int getNextOrdinalNumber(final Auftrag auftrag) {
		int maxOrdinal = 0;
		final Iterator<FormModel> i = auftrag.getFormRelation().iterator();
		while (i.hasNext()) {
			final FormModel formModel = i.next();
			final int ordinal = formModel.getOrdinalNumber();
			if (ordinal > maxOrdinal)
				maxOrdinal = ordinal;
		}
		return maxOrdinal + 1;
	}

	/***************************************************************
	 * Relationships
	 ***************************************************************/
	public RelationOne<FormModel, Auftrag> getAuftragRelation() {
		return relationAuftrag;
	}

	public Auftrag getAuftrag() {
		return relationAuftrag.get();
	}
	
	public void setAuftrag(final Auftrag auftrag) {
		relationAuftrag.add(auftrag);
	}
	
	/***************************************************************
	 * Attributes
	 ***************************************************************/
	public FormClassCode getType() {
		return type;
	}
	
	public void setType(final FormClassCode type) {
		updateDirty(this.type, type, MDLF_NONE);
		this.type = type;
	}
	
	public int getOrdinalNumber() {
		return ordinalNumber;
	}
	
	public void setOrdinalNumber(final int ordinalNumber) {
		updateDirty(this.ordinalNumber, ordinalNumber, MDLF_NONE);
		this.ordinalNumber = ordinalNumber;
	}
	
	public String getContent() {
		return content;
	}
	
	public void setContent(String content) {
		content = Strings.clean(content, 8000);
		updateDirty(this.content, content, MDLF_NONE);
		this.content = content;
	}
	
	public String getDescription() {
		return description;
	}
	
	public void setDescription(String description) {
		description = Strings.clean(description, 60);
		updateDirty(this.description, description, MDLF_NONE);
		this.description = description;
	}

	public int getVersion() {
		return version;
	}
	
	public void setVersion(final int version) {
		updateDirty(this.version, version, MDLF_NONE);
		this.version = version;
	}

	public int getStatus() {
		return status;
	}
	
	public void setStatus(final int status) {
		updateDirty(this.status, status, MDLF_NONE);
		this.status = status;
	}
	
	public Date getCreateTime() {
		return DateUtil.toDate(createTimestamp);
	}
	
	public void setCreateTime(final Date createTime) {
		final long newTimestamp = DateUtil.toTimestamp(createTime);
		updateDirty(this.createTimestamp, newTimestamp, MDLF_NONE);
		this.createTimestamp = newTimestamp;
	}
	
	public Date getUpdateTime() {
		return DateUtil.toDate(updateTimestamp);
	}
	
	public void setUpdateTime(final Date updateTime) {
		final long newTimestamp = DateUtil.toTimestamp(updateTime);
		updateDirty(this.updateTimestamp, newTimestamp, MDLF_NONE);
		this.updateTimestamp = newTimestamp;
	}
	
	public String getCreateUser() {
		return createUser;
	}
	
	public void setCreateUser(String createUser) {
		createUser = Strings.clean(createUser, 30);
		updateDirty(this.createUser, createUser, MDLF_NONE);
		this.createUser = createUser;
	}

	public String getUpdateUser() {
		return updateUser;
	}
	
	public void setUpdateUser(String updateUser) {
		updateUser = Strings.clean(updateUser, 30);
		updateDirty(this.updateUser, updateUser, MDLF_NONE);
		this.updateUser = updateUser;
	}

}

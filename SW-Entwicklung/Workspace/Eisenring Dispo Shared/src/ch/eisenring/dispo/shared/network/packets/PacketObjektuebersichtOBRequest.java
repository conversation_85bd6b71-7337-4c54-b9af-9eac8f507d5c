package ch.eisenring.dispo.shared.network.packets;

import java.util.Date;

import ch.eisenring.core.codetable.RelationalOperator;
import ch.eisenring.core.collections.Set;
import ch.eisenring.core.collections.impl.HashSet;
import ch.eisenring.core.io.AutoStreamed;
import ch.eisenring.logiware.code.pseudo.AbwicklungsartCode;
import ch.eisenring.logiware.code.soft.LWObjektBetreuerCode;
import ch.eisenring.logiware.code.soft.LWVerkaueferCode;

public class PacketObjektuebersichtOBRequest extends AbstractDSPPacket {

	@AutoStreamed
	private Set<LWObjektBetreuerCode> obCodes = new HashSet<>();

	@AutoStreamed
	private transient Set<LWVerkaueferCode> vkCodes = new HashSet<>();

	@AutoStreamed
	private Date from;
	
	@AutoStreamed
	private Date upto;

	@AutoStreamed
	private Integer anzahlKuechen;

	@AutoStreamed
	private RelationalOperator anzahlOperator;

	@AutoStreamed
	private boolean neuProjekte;

	@AutoStreamed
	private boolean showKanton;

	@AutoStreamed
	private boolean showBauart;

	@AutoStreamed
	private boolean showUmbauleitung;

	@AutoStreamed
	private boolean showGU;

	@AutoStreamed
	private AbwicklungsartCode abwicklungsArt;
	
	private PacketObjektuebersichtOBRequest() {
		super(CAPS_SEQUENCE_REQUEST, PRIORITY_REQUEST);
	}

	// --------------------------------------------------------------
	// ---
	// --- Factory methods
	// ---
	// --------------------------------------------------------------
	public static PacketObjektuebersichtOBRequest create(final java.util.Collection<LWObjektBetreuerCode> obCodes,
			final java.util.Collection<LWVerkaueferCode> vkCodes,
			final Date from, final Date upto,
			final Integer anzahlKuechen, final RelationalOperator anzahlOperator,
			final boolean neuProjekte,
			final boolean showKanton,
			final boolean showBauart,
			final boolean showUmbauleitung,
			final AbwicklungsartCode abwicklungsArt,
			final boolean showGU) {
		final PacketObjektuebersichtOBRequest packet = new PacketObjektuebersichtOBRequest();
		packet.obCodes.addAll(obCodes);
		packet.vkCodes.addAll(vkCodes);
		packet.from = from;
		packet.upto = upto;
		packet.anzahlKuechen = anzahlKuechen;
		packet.anzahlOperator = anzahlOperator;
		packet.neuProjekte = neuProjekte;
		packet.showKanton = showKanton;
		packet.showBauart = showBauart;
		packet.showUmbauleitung = showUmbauleitung;
		packet.abwicklungsArt = abwicklungsArt;
		packet.showGU = showGU;
		return packet;
	}

	// --------------------------------------------------------------
	// ---
	// --- Packet API
	// ---
	// --------------------------------------------------------------
	public Set<LWObjektBetreuerCode> getObjektbetreuerCodes() {
		return new HashSet<>(obCodes);
	}

	public Set<LWVerkaueferCode> getVerkaueferCodes() {
		return new HashSet<>(vkCodes);
	}

	public Date getDateFrom() {
		return from;
	}

	public Date getDateUpto() {
		return upto;
	}

	public Integer getAnzahlKuechen() {
		return anzahlKuechen;
	}

	public RelationalOperator getAnzahlOperator() {
		return anzahlOperator;
	}

	public boolean isNeuProjekte() {
		return neuProjekte;
	}

	public boolean isShowKanton() {
		return showKanton;
	}

	public boolean isShowUmbauleitung() {
		return showUmbauleitung;
	}

	public boolean isShowBauart() {
		return showBauart;
	}

	public boolean isShowGU() {
		return showGU;
	}

	public AbwicklungsartCode getAbwicklungsArt() {
		return abwicklungsArt;
	}

}

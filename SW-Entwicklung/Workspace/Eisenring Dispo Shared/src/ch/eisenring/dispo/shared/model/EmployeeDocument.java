package ch.eisenring.dispo.shared.model;

import static ch.eisenring.dispo.shared.DSPConstants.MDLF_NONE;

import java.io.IOException;
import java.util.Date;

import ch.eisenring.core.datatypes.date.DateUtil;
import ch.eisenring.core.io.StreamReader;
import ch.eisenring.core.io.StreamWriter;
import ch.eisenring.dispo.shared.codetables.EmployeeDocumentCode;

public final class EmployeeDocument extends AbstractDocument {

	private Date creationDate;
	private Date mailDate;
	private EmployeeDocumentCode typeCode;
	
	private final RelationOne<EmployeeDocument, MasterMitarbeiter> employeeRelation = new RelationOne<EmployeeDocument, MasterMitarbeiter>(this) {
		@Override
		protected RelationBase<MasterMitarbeiter, EmployeeDocument> getOtherEnd(final MasterMitarbeiter model) {
			return model.getDocumentRelation();
		}
	};

	public EmployeeDocument(final ModelContext context) {
		this(context, null);
		creationDate = new Date();
		typeCode = EmployeeDocumentCode.UNSPECIFIED;
	}

	/**
	 * Main constructor - only to be used indirectly or through reflection 
	 */
	private EmployeeDocument(final ModelContext context, final Long objectId) {
		super(context, objectId);
	}

	public RelationOne<EmployeeDocument, MasterMitarbeiter> getEmployeeRelation() {
		return employeeRelation;
	}

	public Date getCreationDate() {
		return DateUtil.copy(creationDate);
	}

	public void setCreationDate(final Date creationDate) {
		updateDirty(this.creationDate, creationDate, MDLF_NONE);
		this.creationDate = DateUtil.copy(creationDate);
	}

	public Date getMailDate() {
		return DateUtil.copy(mailDate);
	}

	public void setMailDate(final Date mailDate) {
		updateDirty(this.mailDate, mailDate, MDLF_NONE);
		this.mailDate = DateUtil.copy(mailDate);
	}

	public EmployeeDocumentCode getTypeCode() {
		return typeCode;
	}

	public void setTypeCode(final EmployeeDocumentCode typeCode) {
		updateDirty(this.typeCode, typeCode, MDLF_NONE);
		this.typeCode = typeCode;
	}

	public MasterMitarbeiter getEmployee() {
		return (MasterMitarbeiter) employeeRelation.get();
	}

	public void setEmployee(final MasterMitarbeiter employee) {
		employeeRelation.add(employee);
	}

	@Override
	public String getLabel() {
		return getFileName();
	}

	@Override
	public void write(final StreamWriter writer) throws IOException {
		super.write(writer);
		employeeRelation.write(writer);
		writer.writeDate(getMailDate());
		writer.writeDate(getCreationDate());
		writer.writeCode(getTypeCode());
	}

	@Override
	public void read(final StreamReader reader) throws IOException {
		super.read(reader);
		employeeRelation.read(reader);
		setMailDate(reader.readDate());
		setCreationDate(reader.readDate());
		setTypeCode(reader.readCode(EmployeeDocumentCode.class));
	}

	@Override
	public void resolveRead() throws IOException {
		super.resolveRead();
		employeeRelation.resolveRead();
	}

	@Override
	public void delete() {
		super.delete();
		employeeRelation.clear();
	}

	@Override
	public boolean checkReferentialIntegrity() {
		return super.checkReferentialIntegrity() &&
			   employeeRelation.checkReferentialIntegrity();
	}

}

package ch.eisenring.dispo.shared.printing;

import java.util.concurrent.atomic.AtomicLong;

import ch.eisenring.dispo.shared.model.AbstractOIDGenerator;
import ch.eisenring.dispo.shared.model.ModelContext;

/**
 * A special subclass of ModelContext for printing.
 *
 * It sacrifices the ability to be network serialized
 * in favor of being usable on either client/server side.
 * 
 * Objects created in this context do not have a
 * generally unique id, rather the id is only JVM-unqiue.
 */
public class PrintModelContext extends ModelContext {

	final static AtomicLong nextId = new AtomicLong(-1000000000L);

	public final static AbstractOIDGenerator OID_GENERATOR = new AbstractOIDGenerator() {
		@Override
		public long next() {
			return nextId.incrementAndGet();
		}
	};
	
	/**
	 * Create a new PrintModelContext from another context.
	 * The only data taken from the other context is the date.
	 */
	public PrintModelContext(final ModelContext context) {
		super(context.getBegin(), OID_GENERATOR);
	}

	public PrintModelContext(final long date) {
		super(date, OID_GENERATOR);
	}

}

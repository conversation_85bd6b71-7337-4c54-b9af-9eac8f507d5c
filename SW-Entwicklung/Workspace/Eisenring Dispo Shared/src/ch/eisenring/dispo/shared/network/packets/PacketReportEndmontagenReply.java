package ch.eisenring.dispo.shared.network.packets;

import java.util.Date;

import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.collections.Collection;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.alterableview.AlterableView;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.io.AutoStreamed;
import ch.eisenring.logiware.code.soft.LWKundenberaterCode;
import ch.eisenring.logiware.code.soft.LWObjektBetreuerCode;
import ch.eisenring.statistics.shared.data.DataPoint;

public class PacketReportEndmontagenReply extends AbstractDSPPacket {

	@AutoStreamed
	private Date dateFrom;

	@AutoStreamed
	private Date dateUpto;

	@AutoStreamed
	private LWObjektBetreuerCode objektbetreuer;
	
	@AutoStreamed
	private LWKundenberaterCode kundenberater;

	@AutoStreamed
	private Integer verkauefer;

	@AutoStreamed
	private final List<DataPoint> dataPoints = new ArrayList<DataPoint>();

	private PacketReportEndmontagenReply() {
		super(CAPS_SEQUENCE_REPLY | CAPS_ERRORMESSAGE, PRIORITY_REPLY);
	}

	// --------------------------------------------------------------
	// ---
	// --- Factory methods
	// ---
	// --------------------------------------------------------------
	public static PacketReportEndmontagenReply create(
			final PacketReportEndmontagenRequest request,
			final Collection<DataPoint> dataPoints) {
		final PacketReportEndmontagenReply packet = create(request, ErrorMessage.OK);
		packet.dataPoints.addAll(dataPoints);
		return packet;
	}

	public static PacketReportEndmontagenReply create(
			final PacketReportEndmontagenRequest request,
			final ErrorMessage error) {
		final PacketReportEndmontagenReply packet = new PacketReportEndmontagenReply();
		packet.setReplyId(request);
		packet.setMessage(ErrorMessage.OK);
		packet.dateFrom = request.getDateFrom();
		packet.dateUpto = request.getDateUpto();
		packet.objektbetreuer = request.getObjektbetreuer();
		packet.kundenberater = request.getKundenberater();
		packet.verkauefer = request.getVerkaeufer();
		return packet;
	}
	
	// --------------------------------------------------------------
	// ---
	// --- Packet API
	// ---
	// --------------------------------------------------------------
	public Date getDateFrom() {
		return dateFrom;
	}

	public Date getDateUpto() {
		return dateFrom;
	}

	public LWKundenberaterCode getKundenberater() {
		return kundenberater;
	}

	public LWObjektBetreuerCode getObjektbetreuer() {
		return objektbetreuer;
	}

	public Integer getVerkaeufer() {
		return verkauefer;
	}

	public List<DataPoint> getDataPoints() {
		return AlterableView.of(dataPoints);
	}

}

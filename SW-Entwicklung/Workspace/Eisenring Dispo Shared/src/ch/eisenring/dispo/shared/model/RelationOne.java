package ch.eisenring.dispo.shared.model;

import java.io.IOException;
import java.util.Iterator;

import ch.eisenring.core.io.StreamException;
import ch.eisenring.core.io.StreamReader;
import ch.eisenring.core.io.StreamWriter;
import ch.eisenring.core.iterators.IteratorFactory;

public abstract class RelationOne<T extends AbstractModel, O extends AbstractModel> extends RelationBase<T, O> {

	private O object;
	private Long readResolve;
	private boolean readResolveSet;
	
	protected RelationOne(final T origin) {
		super(origin);
	}

	@SuppressWarnings("unchecked")
	@Override
	public Iterator<O> iterator() {
		return IteratorFactory.create(object);
	}

	@Override
	public final int size() {
		return object == null ? 0 : 1;
	}

	@Override
	public final void clear() {
		remove(object);
	}
	
	@Override
	public boolean contains(final Object model) {
		return object == null ? (model == null) : object.equals(model);
	}

	@Override
	public void delete() {
		if (object != null)
			object.delete();
		remove(object);
	}

	/**
	 * Gets the object in this relation, 
	 * or NULL if there is no object.
	 */
	public O get() {
		return object;
	}
	
	// unlike the to-many relation, adding NULL
	// has the effect of clear().
	// the implementation also takes care that
	// there can be only one object in the list.
	@Override
	public void add(final O model) {
		if (model==object || (null!=object && object.equals(model)))
			return;
		RelationBase<O, T> otherEnd;
		O oldObject = object;
		object = model;
		
		otherEnd = null==oldObject ? null : getOtherEnd(oldObject);
		if (null!=otherEnd)
			otherEnd.remove(origin);
		
		if (null!=object) {
			otherEnd = getOtherEnd(object);
			if (null!=otherEnd)
				otherEnd.add(origin);
		}

		setDirty();
	}

	@Override
	public void remove(final O model) {
		if (object != null && object.equals(model)) {
			final RelationBase<O, T> otherEnd = getOtherEnd(model);
			object = null;
			if (otherEnd != null)
				otherEnd.remove(origin);
			setDirty();
		}
	}

	@Override
	protected void read(final StreamReader reader) throws IOException {
		final long l = reader.readLong();
		readResolve = 0L==l ? null : Long.valueOf(l);
		readResolveSet = true;
	}

	@Override
	protected void write(final StreamWriter writer) throws IOException {
		if (null==object) {
			writer.writeLong(0L);
		} else {
			writer.writeLong(object.getId().longValue());
		}
	}

	@Override
	@SuppressWarnings("unchecked")
	protected void resolveRead(final int mode) throws IOException {
		if (readResolveSet) {
			if (readResolve != null) {
				final ModelContext context = origin.getContext();
				final AbstractModel model = context.getModel(readResolve);
				if (model == null) {
					if (mode == RESOLVE_THROW_UNRESOLVED) {
						throw new StreamException("unresolved model id: "+readResolve);
					} else {
						object = null;
					}
				} else {
					object = (O) model;
				}
			} else {
				object = null;
			}
			readResolve = null;
			readResolveSet = false;
		}
	}
	
	@Override
	public void dump(final StringBuilder b) {
		b.append('<');
		if (null!=object)
			b.append(object.getId());
		b.append(">\n");
	}

	@Override
	public boolean checkReferentialIntegrity() {
		if (object == null) {
			return true;
		} else {
			final ModelContext context = origin.getContext();
			return null!=context.getModel(object.getId());
		}
	}
	
}

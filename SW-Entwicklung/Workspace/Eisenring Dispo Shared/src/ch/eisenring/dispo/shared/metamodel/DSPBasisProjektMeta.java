package ch.eisenring.dispo.shared.metamodel;

import ch.eisenring.core.util.VarArgs;
import ch.eisenring.dispo.service.codetables.YesNoCode;
import ch.eisenring.dispo.service.model.DSPBasisProjekt;
import ch.eisenring.dispo.shared.DSPConstants;
import ch.eisenring.logiware.code.soft.GSECode;
import ch.eisenring.logiware.code.soft.LWMonteurSirCode;
import ch.eisenring.model.MetaAttribute;
import ch.eisenring.model.MetaClass;
import ch.eisenring.model.MetaClassMember;
import ch.eisenring.model.MetaProperty;
import ch.eisenring.model.PrimaryKeyOrdinal;
import ch.eisenring.model.engine.jdbc.ColumnSpecifier;
import ch.eisenring.model.engine.jdbc.TableSpecifier;
import ch.eisenring.model.factory.AttributeFactory;
import ch.eisenring.model.mapping.POJOField;
import ch.eisenring.model.primarykey.LongPrimaryKey;

public interface DSPBasisProjektMeta {

	TableSpecifier TABLE = TableSpecifier.get(DSPConstants.DSP_DATABASE, "DSPBasisProjekt");
	
	ColumnSpecifier PKCOL1 = ColumnSpecifier.get(TABLE, "projektNummer");
	ColumnSpecifier PKCOL2 = ColumnSpecifier.get(TABLE, "Id_GSE_PA");

	MetaAttribute ATR_PROJEKTNUMMER = AttributeFactory.cleanString(
			"Projektnummer", (String) null, 16,
			PKCOL1, PrimaryKeyOrdinal.O1,
			POJOField.get(DSPBasisProjekt.class, "projektnummer"));

	MetaAttribute ATR_ID_GSE_PA = AttributeFactory.code(
			"ID_GSE_PA", GSECode.class, GSECode.NULL,
			PKCOL2, PrimaryKeyOrdinal.O2,
			POJOField.get(DSPBasisProjekt.class, "gsePA"));

	MetaAttribute ATR_BAULEITENDER_MONTEUR = AttributeFactory.code(
			"bauleitenderMonteur", LWMonteurSirCode.class, LWMonteurSirCode.NULL,
			ColumnSpecifier.get(TABLE, "bauleitenderMonteur"),
			POJOField.get(DSPBasisProjekt.class, "bauleitenderMonteur"));

	MetaAttribute ATR_COMMENT_OB = AttributeFactory.cleanString(
			"CommentOB", (String) null, 120,
			ColumnSpecifier.get(TABLE, "commentOB"),
			POJOField.get(DSPBasisProjekt.class, "commentOB"));

	MetaAttribute ATR_COMMENT_WERKVERTRAG = AttributeFactory.cleanString(
			"CommentWerkvertrag", null, 250,
			ColumnSpecifier.get(TABLE, "commentWerkvertrag"),
			POJOField.get(DSPBasisProjekt.class, "commentWerkvertrag"));

	MetaAttribute ATR_OBJEKTUEBERGABE = AttributeFactory.code(
			"Objektuebergabe", YesNoCode.class, YesNoCode.NO,
			ColumnSpecifier.get(TABLE, "objektuebergabe"),
			POJOField.get(DSPBasisProjekt.class, "objektUebergabe"));

	MetaAttribute ATR_WERKVERTRAG = AttributeFactory.code(
			"Werkvertrag", YesNoCode.class, YesNoCode.NO,
			ColumnSpecifier.get(TABLE, "werkvertrag"),
			POJOField.get(DSPBasisProjekt.class, "werkvertrag"));

	MetaAttribute ATR_DEKLARATION_SOLIDARHAFTUNG = AttributeFactory.code(
			"DeklarationSolidarhaftung", YesNoCode.class, YesNoCode.NO,
			ColumnSpecifier.get(TABLE, "deklarationSolidarhaftung"),
			POJOField.get(DSPBasisProjekt.class, "deklarationSolidarhaftung"));

	MetaAttribute ATR_ANZAHLKUECHEN = AttributeFactory.cleanString(
			"AnzahlKuechen", null, 50,
			ColumnSpecifier.get(TABLE, "anzahlKuechen"),
			POJOField.get(DSPBasisProjekt.class, "anzahlKuechen"));

	MetaAttribute ATR_EMAILSOLIDARHAFTUNG = AttributeFactory.cleanString(
			"emailSolidarhaftung", null, 60,
			ColumnSpecifier.get(TABLE, "emailSolidarhaftung"),
			POJOField.get(DSPBasisProjekt.class, "emailSolidarhaftung"));

	MetaAttribute ATR_VERGABE = AttributeFactory.code(
			"Vergabe", YesNoCode.class, YesNoCode.NO,
			ColumnSpecifier.get(TABLE, "vergabe"),
			POJOField.get(DSPBasisProjekt.class, "vergabe"));

	MetaAttribute ATR_LIFTHEAG_GEBUCHT = AttributeFactory.code(
			"LiftHeagGebucht", YesNoCode.class, YesNoCode.NO,
			ColumnSpecifier.get(TABLE, "liftHeagGebucht"),
			POJOField.get(DSPBasisProjekt.class, "liftHeagGebucht"));

	MetaAttribute ATR_TREPPENLIFT_GEBUCHT = AttributeFactory.code(
			"TreppenLiftGebucht", YesNoCode.class, YesNoCode.NO,
			ColumnSpecifier.get(TABLE, "treppenLiftGebucht"),
			POJOField.get(DSPBasisProjekt.class, "treppenLiftGebucht"));

	MetaAttribute ATR_TEMPORAERPERSONAL_GEBUCHT = AttributeFactory.code(
			"TemporaerPersonalGebucht", YesNoCode.class, YesNoCode.NO,
			ColumnSpecifier.get(TABLE, "temporaerPersonalGebucht"),
			POJOField.get(DSPBasisProjekt.class, "temporaerPersonalGebucht"));

	MetaAttribute ATR_SONDERKONDITIONEN_ABRECHNUNG = AttributeFactory.code(
			"SonderKonditionenAbrechnung", YesNoCode.class, YesNoCode.NO,
			ColumnSpecifier.get(TABLE, "sonderKonditionenAbrechnung"),
			POJOField.get(DSPBasisProjekt.class, "sonderKonditionenAbrechnung"));

	MetaAttribute ATR_VEREINBARUNGEN_KONDITIONEN = AttributeFactory.cleanString(
			"VereinbarungenKonditionen", null, 1000,
			ColumnSpecifier.get(TABLE, "vereinbarungenKonditionen"),
			POJOField.get(DSPBasisProjekt.class, "vereinbarungenKonditionen"));

	MetaClass METACLASS = new MetaClass("DSPBasisProjekt", LongPrimaryKey.class,
			// Attributes
			new VarArgs<MetaProperty>(MetaProperty.class).flat(
			TABLE, PKCOL1, PKCOL2,
			MetaClassMember.discoverMetaClassMembers(DSPBasisProjektMeta.class)
			));
	
}

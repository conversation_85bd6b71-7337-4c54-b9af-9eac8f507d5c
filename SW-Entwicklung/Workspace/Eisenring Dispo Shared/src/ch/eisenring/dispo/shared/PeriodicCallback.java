package ch.eisenring.dispo.shared;

import ch.eisenring.core.logging.Logger;
import ch.eisenring.core.threading.ThreadCore;

/**
 * A separate thread for periodically necessary housekeeping.
 */
public abstract class PeriodicCallback {

	private final long period;
	private boolean active;
	private Thread thread;

	protected PeriodicCallback(long period) {
		this.period = period;
	}
	
	/**
	 * The method periodically called.
	 * Child classes must implement this.
	 */
	protected abstract void periodicCallback();

	/**
	 * Called to configure thread before it starts.
	 * Child classes can overwrite this to do configuration
	 * on the thread (priority, name, etc.)
	 */
	protected void configureThread(Thread thread) {
	}

	/**
	 * Stops the periodic call back 
	 */
	protected final void stopCallback() {
		synchronized (this) {
			active = false;
			if (thread != null)
				thread.interrupt();
		}
	}

	/**
	 * Starts the periodic call back
	 */
	protected final void startCallback() {
		synchronized (this) {
			if (thread == null) {
				active = true;
				thread = ThreadCore.create(() -> { mainLoop(); }, "PeriodicCallback", 1024);
			}
			try {
				configureThread(thread);
			} catch (Exception e) {
				// ignore exceptions
			}
			thread.start();
		}
	}
	
	void mainLoop() {
		try {
			while (active) {
				ThreadCore.sleep((int) period);
				if (active) {
					Thread.interrupted();
					periodicCallback();
				}
			}
		} finally {
			synchronized (this) {
				active = false;
				thread = null;
			}
			Logger.debug("terminated");
		}
	}
	
	public final boolean isAlive() {
		return active && thread != null;
	}

}

package ch.eisenring.dispo.shared.network.packets;

import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.dispo.shared.pojo.DSPMonteurTeam;

public final class PacketMonteurTeamUpdateReply extends PacketMonteurTeamBase {

	private PacketMonteurTeamUpdateReply() {
		super(CAPS_SEQUENCE_REPLY | CAPS_ERRORMESSAGE, PRIORITY_REPLY,
				TRANSPORT_TEAM);
	}

	// --------------------------------------------------------------
	// ---
	// --- Factory methods
	// ---
	// --------------------------------------------------------------
	public static PacketMonteurTeamUpdateReply create(final PacketMonteurTeamUpdateRequest request, final ErrorMessage message) {
		final PacketMonteurTeamUpdateReply packet = new PacketMonteurTeamUpdateReply();
		packet.setReplyId(request, message);
		packet.team = request.getTeam();
		return packet;
	}

	// --------------------------------------------------------------
	// ---
	// --- API
	// ---
	// --------------------------------------------------------------
	public DSPMonteurTeam getTeam() {
		return team;
	}

}

package ch.eisenring.dispo.shared.pojo;

import java.io.IOException;

import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.io.StreamReader;
import ch.eisenring.core.io.StreamWriter;
import ch.eisenring.core.io.Streamable;
import ch.eisenring.dispo.service.codetables.YesNoCode;
import ch.eisenring.logiware.LWProjektKey;
import ch.eisenring.logiware.code.hard.LWYesNoCode;
import ch.eisenring.logiware.code.soft.GSECode;

public final class DSPProjekt implements Streamable {

	private transient GSECode gsePA;
	private transient YesNoCode kuecheGemessen;
	private transient LWYesNoCode kuecheAbgenommen;
	private transient String projektnummer;

	DSPProjekt() {
	}

	public DSPProjekt(final LWProjektKey key) {
		this();
		this.projektnummer = key.getProjektnummer();
		this.gsePA = key.getGSE();
	}	

	// --------------------------------------------------------------
	// ---
	// --- Object overrides
	// ---
	// --------------------------------------------------------------
	@Override
	public int hashCode() {
		return (AbstractCode.hashCode(gsePA) * 31) ^ Strings.hashCode(projektnummer); 
	}

	@Override
	public boolean equals(final Object o) {
		if (o instanceof DSPProjekt) {
			final DSPProjekt p = (DSPProjekt) o;
			return Strings.equals(p.projektnummer, projektnummer) &&
				   AbstractCode.equals(p.gsePA, gsePA);
		}
		return false;
	}

	// --------------------------------------------------------------
	// ---
	// --- Derived Getters
	// ---
	// --------------------------------------------------------------
	/**
	 * Caches the key for reuse
	 */
	private LWProjektKey cachedKey;

	public LWProjektKey getKey() {
		LWProjektKey result = cachedKey;
		if (result == null)
			cachedKey = result = LWProjektKey.get(projektnummer, gsePA);
		return result;
	}

	/**
	 * Returns true if the key is for this object.
	 * Potentially faster than getKey().equals().
	 */
	public boolean matchesKey(final LWProjektKey key) {
		if (key == null)
			return false;
		return Strings.equals(key.getProjektnummer(), projektnummer) &&
			   AbstractCode.equals(key.getGSE(), gsePA);
	}

	// --------------------------------------------------------------
	// ---
	// --- Setters & Getters
	// ---
	// --------------------------------------------------------------
	public GSECode getGSE() {
		return gsePA;
	}

	public void setGSE(final GSECode gsePA) {
		this.gsePA = gsePA;
	}

	public YesNoCode getKuecheGemessen() {
		return kuecheGemessen;
	}

	public void setKuecheGemessen(final YesNoCode kuecheGemessen) {
		this.kuecheGemessen = kuecheGemessen;
	}

	public LWYesNoCode getKuecheAbgenommen() {
		return kuecheAbgenommen;
	}

	public void setKuecheAbgenommen(final LWYesNoCode kuecheAbgenommen) {
		this.kuecheAbgenommen = kuecheAbgenommen;
	}

	public String getProjektnummer() {
		return projektnummer;
	}

	public void setProjektnummer(final String projektnummer) {
		this.projektnummer = projektnummer;
	}

	// --------------------------------------------------------------
	// ---
	// --- Streamable
	// ---
	// --------------------------------------------------------------
	@Override
	public void read(final StreamReader reader) throws IOException {
		projektnummer = reader.readString();
		gsePA = reader.readCode(GSECode.class);
		kuecheGemessen = reader.readCode(YesNoCode.class);
		kuecheAbgenommen = reader.readCode(LWYesNoCode.class);
	}

	@Override
	public void write(final StreamWriter writer) throws IOException {
		writer.writeString(projektnummer);
		writer.writeCode(gsePA);
		writer.writeCode(kuecheGemessen);
		writer.writeCode(kuecheAbgenommen);
	}
	
}

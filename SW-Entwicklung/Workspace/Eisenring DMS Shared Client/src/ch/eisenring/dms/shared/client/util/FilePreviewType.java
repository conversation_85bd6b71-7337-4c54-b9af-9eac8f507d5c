package ch.eisenring.dms.shared.client.util;

import ch.eisenring.core.codetype.StaticCode;

/**
 * Defines the viewer to be used for various operations.
 */
public final class FilePreviewType extends StaticCode {

	/**
	 * Preview of the type is not supported
	 */
	public final static FilePreviewType NOT_SUPPORTED =
		new FilePreviewType(0, "Vorschau nicht unterstützt");

	/**
	 * Preview using the "image" preview 
	 */
	public final static FilePreviewType IMAGE =
		new FilePreviewType(1, "Bild");

	/**
	 * Preview using the "PDF" preview 
	 */
	public final static FilePreviewType PDF =
		new FilePreviewType(2, "PDF");

	/**
	 * Preview using the "TextOnly" preview 
	 */
	public final static FilePreviewType TEXT =
		new FilePreviewType(3, "Nur Text");
	
	private FilePreviewType(final int id, final String name) {
		super(id, Integer.valueOf(id), name, name);
	}

}

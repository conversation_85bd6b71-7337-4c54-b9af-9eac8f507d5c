package ch.eisenring.dms.shared.client.util;

import ch.eisenring.core.codetype.StaticCode;

/**
 * Defines the viewer to be used for various operations.
 */
public class FileOpenType extends StaticCode {

	/**
	 * Indicates the type can not be opened at all
	 */
	public final static FileOpenType OPEN_NONE = new FileOpenType(0, "Nicht unterstützt");

	/**
	 * Indicates the type should be shown by the OS default
	 */
	public final static FileOpenType OPEN_OS = new FileOpenType(1, "OS");
	
	/**
	 * Indicates the quick viewer can handle this type
	 */
	public final static FileOpenType OPEN_QUICKVIEW = new FileOpenType(2, "Quickviewer");

	private FileOpenType(final int id, final String name) {
		super(id, Integer.valueOf(id), name, name);
	}

}

package ch.eisenring.dms.shared.client.util;

import static ch.eisenring.dms.shared.client.util.DMSObjectLabelFormatterPrivate.AKTUELLEVERSION;
import static ch.eisenring.dms.shared.client.util.DMSObjectLabelFormatterPrivate.BBR;
import static ch.eisenring.dms.shared.client.util.DMSObjectLabelFormatterPrivate.DATEIGROESSE;
import static ch.eisenring.dms.shared.client.util.DMSObjectLabelFormatterPrivate.DATE_FORMAT;
import static ch.eisenring.dms.shared.client.util.DMSObjectLabelFormatterPrivate.HTMLB;
import static ch.eisenring.dms.shared.client.util.DMSObjectLabelFormatterPrivate.NULL_CHARARRAY;
import static ch.eisenring.dms.shared.client.util.DMSObjectLabelFormatterPrivate.NULL_LABEL;
import static ch.eisenring.dms.shared.client.util.DMSObjectLabelFormatterPrivate.ORDNERERSTELLT;
import static ch.eisenring.dms.shared.client.util.DMSObjectLabelFormatterPrivate.UNBEKANNTERTYP;
import ch.eisenring.core.datatypes.date.format.HEAGDateFormat;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.datatypes.strings.format.api.Formats;
import ch.eisenring.dms.service.api.DMSObject;
import ch.eisenring.dms.service.codetables.DMSObjectType;

public interface DMSObjectLabelFormatter {

	public abstract void appendTo(final StringMaker target, final DMSObject object);

	public default String format(final DMSObject object) {
		if (object == null)
			return NULL_LABEL;
		final StringMaker maker = StringMaker.obtain(128);
		appendTo(maker, object);
		return maker.release();
	}

	// --------------------------------------------------------------
	// ---
	// --- Default implementations
	// ---
	// --------------------------------------------------------------
	public final static DMSObjectLabelFormatter NAMEONLY = new DMSObjectLabelFormatter() {
		@Override
		public void appendTo(final StringMaker target, final DMSObject object) {
			if (object == null) {
				target.append(NULL_CHARARRAY);
				return;
			}
			target.append(HTMLB);
			Strings.toHTMLEntities(object.getObjectname(), target);
			target.append(BBR);
		}
	};

	public final static DMSObjectLabelFormatter NAMEANDTYPE = new DMSObjectLabelFormatter() {
		@Override
		public void appendTo(final StringMaker target, final DMSObject object) {
			if (object == null) {
				target.append(NULL_CHARARRAY);
				return;
			}
			target.append(HTMLB);
			Strings.toHTMLEntities(object.getObjectname(), target);
			final String extension = object.getFileExtension();
			if (extension != null) {
				target.append('.');
				Strings.toHTMLEntities(extension, target);
			}
			target.append(BBR);
		}
	};

	public final static DMSObjectLabelFormatter DETAILED = new DMSObjectLabelFormatter() {
		@Override
		public void appendTo(final StringMaker target, final DMSObject object) {
			if (object == null) {
				target.append(NULL_CHARARRAY);
				return;
			}
			final DMSObjectType type = object.getType();
			target.append(HTMLB);
			Strings.toHTMLEntities(object.getObjectname(), target);
			target.append(BBR);
			if (DMSObjectType.FILE.equals(type)) {
				target.append(DATEIGROESSE);
				target.append(object.getBytesize(), Formats.BYTESIZE);
				target.append(AKTUELLEVERSION);
			} else if (DMSObjectType.FOLDER.equals(type)) {
				target.append(ORDNERERSTELLT);
			} else { 
				target.append(UNBEKANNTERTYP);
			}
			target.append(object.getLastChanged(), DATE_FORMAT);			
		}
	};

	public final static DMSObjectLabelFormatter MAXDETAILED = new DMSObjectLabelFormatter() {
		@Override
		public void appendTo(final StringMaker target, final DMSObject object) {
			if (object == null) {
				target.append(NULL_CHARARRAY);
				return;
			}
			target.append(HTMLB);
			Strings.toHTMLEntities(object.getObjectname(), target);
			final String extension = object.getFileExtension();
			if (extension != null) {
				target.append('.');
				Strings.toHTMLEntities(extension, target);
			}
			target.append(BBR);
			final DMSObjectType type = object.getType();
			if (DMSObjectType.FILE.equals(type)) {
				target.append(DATEIGROESSE);
				target.append(object.getBytesize(), Formats.BYTESIZE);
				target.append(AKTUELLEVERSION);
			} else if (DMSObjectType.FOLDER.equals(type)) {
				target.append(ORDNERERSTELLT);
			} else {
				target.append(UNBEKANNTERTYP);
			}
			target.append(object.getLastChanged(), DATE_FORMAT);
		}
	};

}

// non-public constants
final class DMSObjectLabelFormatterPrivate {

	final static HEAGDateFormat DATE_FORMAT = HEAGDateFormat.get("dd.MM.yyyy HH:mm:ss");
	final static String NULL_LABEL = "<html>&lt;NULL&gt;";
	final static char[] NULL_CHARARRAY = NULL_LABEL.toCharArray();
	final static char[] HTMLB = { '<', 'h', 't', 'm', 'l', '>', '<', 'b', '>' };
	final static char[] BBR = { '<', '/', 'b', '>', '<', 'b', 'r', '>' };
	final static char[] DATEIGROESSE = "Dateigr&ouml;sse ".toCharArray();
	final static char[] AKTUELLEVERSION = ", aktuelle Version vom ".toCharArray();
	final static char[] ORDNERERSTELLT = "Ordner, erstellt am ".toCharArray();
	final static char[] UNBEKANNTERTYP = "Unbekannter Typ, ge&auml;ndert am ".toCharArray();

}

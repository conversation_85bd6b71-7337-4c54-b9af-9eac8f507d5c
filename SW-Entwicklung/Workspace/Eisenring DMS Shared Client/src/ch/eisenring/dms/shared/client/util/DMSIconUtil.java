package ch.eisenring.dms.shared.client.util;

import java.awt.Image;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.io.InputStream;

import javax.imageio.ImageIO;
import javax.media.jai.JAI;
import javax.media.jai.PlanarImage;
import javax.swing.Icon;

import ch.eisenring.core.io.Streams;
import ch.eisenring.core.io.binary.BinaryHolder;
import ch.eisenring.core.resource.ImageResource;
import ch.eisenring.core.resource.image.ImageImageResource;
import ch.eisenring.dms.service.api.DMSObject;
import ch.eisenring.dms.service.codetables.DMSIconCode;
import ch.eisenring.dms.service.codetables.DMSObjectType;
import ch.eisenring.dms.shared.DMSConstants;
import ch.eisenring.dms.shared.client.resources.images.Images;

import com.sun.media.jai.codec.SeekableStream;

public abstract class DMSIconUtil {

	static {
		final Object[] typeIcons = {
				DMSIconCode.FOLDER, Images.TYPE_FOLDER,

				DMSIconCode.IMAGE, Images.TYPE_IMAGE,
				DMSIconCode.DOCUMENT, Images.TYPE_DOCUMENT,
				DMSIconCode.TEXT, Images.TYPE_TEXT,
				DMSIconCode.ARCHIVE, Images.TYPE_ARCHIVE,
		        DMSIconCode.AUDIO, Images.TYPE_AUDIO,
		        DMSIconCode.VIDEO, Images.TYPE_VIDEO,
		        DMSIconCode.EXECUTABLE, Images.TYPE_EXECUTABLE,
		        DMSIconCode.SCRIPT, Images.TYPE_SCRIPT,
		        DMSIconCode.CAD3D, Images.TYPE_3D,

		        DMSIconCode.CSS, Images.TYPE_CSS,
		        DMSIconCode.DOC, Images.TYPE_DOC,
		        DMSIconCode.HTML, Images.TYPE_HTML,
		        DMSIconCode.JAVA, Images.TYPE_JAVA,
		        DMSIconCode.MSG, Images.TYPE_MAIL,
		        DMSIconCode.PHP, Images.TYPE_PHP,
		        DMSIconCode.PDF, Images.TYPE_PDF,
		        DMSIconCode.PPT, Images.TYPE_PPT,
		        DMSIconCode.SQL, Images.TYPE_SQL,
		        DMSIconCode.TTF, Images.TYPE_TTF,
		        DMSIconCode.XLS, Images.TYPE_XLS,
		        DMSIconCode.XML, Images.TYPE_XML,

				DMSIconCode.F_IDX, Images.TYPE_FOLDER_INDEX,
				DMSIconCode.F_MAG, Images.TYPE_FOLDER_MAGIC,
				DMSIconCode.F_PRJ, Images.TYPE_FOLDER_PROJECT,
				DMSIconCode.F_EMAIL, Images.TYPE_FOLDER_EMAIL,
				DMSIconCode.F_GRANITE, Images.TYPE_FOLDER_GRANITE,
				DMSIconCode.F_HAMMER, Images.TYPE_FOLDER_HAMMER,
				DMSIconCode.F_MEASURE, Images.TYPE_FOLDER_MESAURE,
				DMSIconCode.F_MISC, Images.TYPE_FOLDER_MISC,
				DMSIconCode.F_ORDER, Images.TYPE_FOLDER_ORDER,
				DMSIconCode.F_ORGANIZE, Images.TYPE_FOLDER_ORGANIZE,
				DMSIconCode.F_PHOTO, Images.TYPE_FOLDER_PHOTO,
				DMSIconCode.F_PROTOCOL, Images.TYPE_FOLDER_PROTOCOL,
				DMSIconCode.F_CONFIRMATION, Images.TYPE_FOLDER_SIGNATURE,
				DMSIconCode.F_WRENCH, Images.TYPE_FOLDER_WRENCH,
				DMSIconCode.F_KITCHEN, Images.TYPE_FOLDER_KITCHEN,
				DMSIconCode.F_MALE1, Images.TYPE_FOLDER_MALE1,
				DMSIconCode.F_MALE2, Images.TYPE_FOLDER_MALE2,
				DMSIconCode.F_MALE3, Images.TYPE_FOLDER_MALE3,
				DMSIconCode.F_FEMALE1, Images.TYPE_FOLDER_FEMALE1,
				DMSIconCode.F_FEMALE2, Images.TYPE_FOLDER_FEMALE2,
				DMSIconCode.F_FEMALE3, Images.TYPE_FOLDER_FEMALE3,
				DMSIconCode.F_TRASH, Images.TYPE_FOLDER_TRASH,
				DMSIconCode.F_PARENT, Images.TYPE_FOLDER_PARENT,
				DMSIconCode.F_LINK, Images.TYPE_FOLDER_LINK,
				DMSIconCode.F_LEAF, Images.TYPE_FOLDER_LEAF,
				DMSIconCode.F_BOSS, Images.TYPE_FOLDER_BOSS,
				DMSIconCode.F_EDUCATION, Images.TYPE_FOLDER_EDUCATION,
				DMSIconCode.F_LAPTOP, Images.TYPE_FOLDER_LAPTOP,
				DMSIconCode.F_STATISTIC1, Images.TYPE_FOLDER_STATISTIC1,
				DMSIconCode.F_STATISTIC2, Images.TYPE_FOLDER_STATISTIC2,
				DMSIconCode.F_STATISTIC3, Images.TYPE_FOLDER_STATISTIC3,
				DMSIconCode.F_TEMPLATE, Images.TYPE_FOLDER_TEMPLATE,
				DMSIconCode.F_PRODUCT, Images.TYPE_FOLDER_PRODUCT,
				DMSIconCode.F_PEOPLE1, Images.TYPE_FOLDER_PEOPLE1,
				DMSIconCode.F_PEOPLE2, Images.TYPE_FOLDER_PEOPLE2,
				DMSIconCode.F_PEOPLE3, Images.TYPE_FOLDER_PEOPLE3,
				DMSIconCode.F_PERSON1, Images.TYPE_FOLDER_PERSON1,
				DMSIconCode.F_PASSPORT, Images.TYPE_FOLDER_PASSPORT,
				DMSIconCode.F_MONEY, Images.TYPE_FOLDER_MONEY,
				DMSIconCode.F_INVOICE, Images.TYPE_FOLDER_INVOICE,
				DMSIconCode.F_RECEIPT, Images.TYPE_FOLDER_RECEIPT,
				DMSIconCode.F_CAR, Images.TYPE_FOLDER_CAR,
				DMSIconCode.F_VAN, Images.TYPE_FOLDER_VAN,
				DMSIconCode.F_LORRY, Images.TYPE_FOLDER_LORRY,
		};
		for (int i=0; i<typeIcons.length; i+=2) {
			final DMSIconCode iconCode = (DMSIconCode) typeIcons[i];
			final ImageResource image = (ImageResource) typeIcons[i | 1];
			iconCode.setIcon(image);
		}
	}

	protected DMSIconUtil() {
	}

	/**
	 * Gets the icon for object
	 */
	public static Icon getIcon(final DMSObject holder, final int size) {
		if (holder == null)
			return Images.QUESTION.getIcon(size);
		final ImageResource res = getThumbnail(holder.getIconCode(), holder.getThumbnail(), holder);
		return res.getIcon(size);
	}

	public static ImageResource getThumbnail(final DMSObject object) {
		if (object == null)
			return Images.TYPE_DOCUMENT;
		return getThumbnail(object.getIconCode(), object.getThumbnail(), object);
	}

	/**
	 * Gets thumbnail for object (if present, else NULL) 
	 */
	public static ImageResource getThumbnail(final DMSIconCode code,
			final BinaryHolder thumbnail, final DMSObject holder) {
		ImageResource result = code == null ? null : code.getIcon();
		if (result != null) {
			// use a predefined icon
			return result;
		}
		try {
			if (DMSIconCode.DEFAULT.equals(code) && thumbnail != null && thumbnail.size() > 0) {
				// use custom icon
				final Image image = createThumbnail(thumbnail, DMSConstants.THUMBNAIL_SIZE);
				if (image != null) {
					return new ImageImageResource(image);
				}
			}
		} catch (final IOException e) {
			// ignore if thumbnail can't be converted to image
		}

		// determine standard icon (by extension)
		final DMSObjectType type = holder.getType();
		if (DMSObjectType.FOLDER.equals(type)) {
			result = Images.TYPE_FOLDER;
		} else if (DMSObjectType.FILE.equals(type)) {
			result = FileType.getDefaultIcon(holder.getFileExtension());
		}

		// if all else fails use the default icon
		return result == null ? Images.TYPE_UNKNOWN : result;
	}

	// --------------------------------------------------------------
	// ---
	// --- Thumbnail creation
	// ---
	// --------------------------------------------------------------
	/**
	 * Attempts to get thumbnail from the binary, limited to the given size
	 */
	public static Image createThumbnail(final BinaryHolder binary, final int size) {
		if (binary == null)
			return null;
		try {
			if (binary.size() <= 0)
				return null;
		} catch (final IOException e) {
			return null;
		}

		InputStream binaryInput = null;
		try {
			do {
				binaryInput = binary.getInputStream();
				final BufferedImage image = ImageIO.read(binaryInput);
				if (image == null)
					break;
				final int w0 = image.getWidth();
				final int h0 = image.getHeight();
				if (w0 == size && h0 <= size) {
					return image;
				} else if (h0 == size && w0 <= size) {
					return image;
				}
				int w1, h1;
				float aspect;
				if (w0 > h0) {
					aspect = ((float) w0) / h0;
					w1 = size;
					h1 = Math.round(w1 / aspect);
				} else {
					aspect = ((float) h0) / w0;
					h1 = size;
					w1 = Math.round(h1 / aspect);
				}
				if (w1 > size)
					w1 = size;
				if (h1 > size)
					h1 = size;
				final Image thumb = image.getScaledInstance(w1, h1, Image.SCALE_AREA_AVERAGING);
				return thumb;
			} while (false);
		} catch (final Exception e) {
			Streams.closeSilent(binaryInput);
		}
		return null;
	}

	public static Image getImage(final BinaryHolder binaryHolder) {
		if (binaryHolder == null)
			return null;
		
		// attempt to load the image using ImageIO
		InputStream input = null;
		try {
			if (binaryHolder.size() == 0)
				return null;
			input = binaryHolder.getInputStream();
			final BufferedImage image = ImageIO.read(input);
			if (image != null)
				return image;
		} catch (final IOException e) {
			// ignore failure
		} finally {
			Streams.closeSilent(input);
		}
		
		// attempt to load the image using JAI
		input = null;
		try {
			SeekableStream seekStream = null;
			try {
				seekStream = SeekableStream.wrapInputStream(binaryHolder.getInputStream(), true);
				final PlanarImage planar = JAI.create("stream", seekStream);
				final BufferedImage image = planar.getAsBufferedImage();
				if (image != null)
					return image;
			} finally {
				Streams.closeSilent(seekStream);
			}
		} catch (final IOException e) {
			// ignore failure
		} finally {
			Streams.closeSilent(input);
		}

		return null;
	}

}

package ch.eisenring.dms.client.gui.util;

import java.io.IOException;

import ch.eisenring.core.util.file.FileUtil;
import ch.eisenring.dms.service.api.DMSObject;
import ch.eisenring.dms.shared.model.api.DMSBinary;

/**
 * Subclass of TempFile that may be cached/reused for multiple viewers
 * 
 * TODO : implement and take advantage of cacheability
 */
public class VersionCacheFile extends TempFile {

	protected final long objectRowId;
	protected final long versionRowId;

	protected VersionCacheFile(final DMSObject object, final DMSBinary binary) throws IOException {
		super(binary.getBinaryData(),
				FileUtil.addFileExtension(object.getObjectname(), binary.getFileExtension()),
				false);
		this.objectRowId = binary.getObjectRowId();
		this.versionRowId = binary.getPKValue();
	}

	/**
	 * Creates a writable TempFile
	 */
	public static TempFile createWritable(final DMSObject object, final DMSBinary binary) throws IOException {
		return new TempFile(binary.getBinaryData(),
				FileUtil.addFileExtension(object.getObjectname(), binary.getFileExtension()),
				true);
	}
	
	/**
	 * Creates a read-only TempFile
	 */
	public static TempFile createReadOnly(final DMSObject object, final DMSBinary binary) throws IOException {
		return new VersionCacheFile(object, binary);
	}

}

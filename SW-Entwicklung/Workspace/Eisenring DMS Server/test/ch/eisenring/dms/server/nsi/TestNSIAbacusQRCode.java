package ch.eisenring.dms.server.nsi;

import org.junit.Test;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

public class TestNSIAbacusQRCode {

    String validJson = " {\"documentNumber\": 12659, \"documentType\":\"ABAWEB\",\"projectNumber\":163353, \"purchaseOrderNumber\":5034378, \"subjectId\":20144, \"subjectCodeShort\":\"EL\"}";

    String invalidJson = "{\"documentNumber\": 12659, \"projectNumber\":163353, \"purchaseOrderNumber\":5034378, \"subjectId\":20144, \"subjectCodeShort\":\"EL\"}";

    @Test
    public void shouldTransformToValidQRCode() {
        NSIAbacusQRCode qrCode = NSIAbacusQRCode.buildNSIAbacusQRCodeFromJson(validJson);
        assertTrue(qrCode.isValidAbacusQRCode());
    }

    @Test
    public void shouldTransformToInvalidQRCode() {
        NSIAbacusQRCode qrCode = NSIAbacusQRCode.buildNSIAbacusQRCodeFromJson(invalidJson);
        assertFalse(qrCode.isValidAbacusQRCode());

    }
}

package ch.eisenring.dms.server.test;

import java.sql.SQLException;
import java.util.List;

import ch.eisenring.app.server.model.ContextSink;
import ch.eisenring.app.shared.CoreTickListener;
import ch.eisenring.dms.server.DMSServer;
import ch.eisenring.dms.server.util.ReplicationTemplate;
import ch.eisenring.dms.server.util.ReplicationTemplateSink;
import ch.eisenring.dms.service.codetables.DMSPropertyCode;
import ch.eisenring.dms.shared.DMSConstants;
import ch.eisenring.dms.shared.util.FolderVariableTemplateExpander;
import ch.eisenring.jdbc.StatementParameters;
import ch.eisenring.jdbc.StatementWrapper;
import ch.eisenring.model.TransactionContext;
import ch.eisenring.model.TransactionSink;

public class Miet2Eigentum extends UpdateBase implements CoreTickListener {

	public Miet2Eigentum(final DMSServer server) {
		super(server);
	}

	@Override
	public String getName() {
		return "Miet2Eigentum-Update";
	}

	@Override
	public void tick(final long now) {
		doUpdate();
	}
	
	/**
	 * Anwendung:
	 * 
	 * rowId von Basis eintragen, und laufen lassen.
	 * (Es werden die Ordner 1 & 4 angelegt und der Typ wird auf Eigentum geändert)
	 * 
	 * Dann Basis-Strukturtyp händisch auf "Eigentum" setzen und
	 * Basis-Ordner händisch in Eigentum verschieben
	 */
	public void doUpdate() {
		try {
			final List<Long> folderList = getKuechenOrdner(1546343328L); // rowId of basis
			alterProperty(folderList, DMSPropertyCode.STRUCTURETYPE, "2"); // set structuretype = Eigentum
			addFolder(folderList, 1184639078L);	// 1 Installationspläne
			addFolder(folderList, 1184639084L);	// 4 Auftragsbestätigung
		} catch (final Exception e) {
			e.printStackTrace();
		}
		server.getCore().shutdown(true);
	}

	private List<Long> getKuechenOrdner(final long basisRowId) throws Exception {
		final String sql = "SELECT O.rowId"
				+ " FROM DMSObject O, DMSProperty P"
				+ "	WHERE"
				+ " O.parent_rowId = " + basisRowId					// unterordner von
				+ " AND P.Propertycode = 8 AND P.PropertyValue = '3'"	// strukturtyp = Miet
				+ " AND O.Typecode = 1"								// objekttyp = ordner
				+ " AND O.rowId = P.Object_rowId";
		return getObjectRowIds(sql);
	}

	/**
	 * Alters the selected property to new value for given object row ids
	 */
	private void alterProperty(final List<Long> targetObjects, final DMSPropertyCode propertyCode, final String newValue) throws Exception {
		final TransactionSink sink = new ContextSink(DMSConstants.DMS_DATABASE) {
			@Override
			protected void storeImpl(final TransactionContext context) throws SQLException {
				String sql = "UPDATE DMSProperty SET PropertyValue = ? WHERE Object_rowId = ? AND Propertycode = ?";
				final StatementWrapper statement = prepareStatement(sql);
				for (long rowId : targetObjects) {
					StatementParameters params = new StatementParameters();
					params.add(newValue);
					params.add(rowId);
					params.add(propertyCode.getKey());
					statement.setParameters(params);
					statement.addBatch();
				}
				statement.executeBatch();
			}
		};
		TransactionContext context = new TransactionContext();
		context.store(sink);
	}

	private void addFolder(final List<Long> targetFolders, final long templateRowId) throws Exception {
		final FolderVariableTemplateExpander expander = new FolderVariableTemplateExpander();
		final ReplicationTemplate template = ReplicationTemplate.loadTemplate(server, templateRowId);
		final ReplicationTemplateSink sink = new ReplicationTemplateSink(server, USER);
		for (final Long folder : targetFolders) {
			String result = null;
			try {
				result = "Ok";
				sink.replicate(template, folder, expander);
			} catch (final Exception e) {
				result = e.getMessage();
			}
			System.out.println("Updated folder: " + folder + " (" + result + ")");				
		}
	}

}

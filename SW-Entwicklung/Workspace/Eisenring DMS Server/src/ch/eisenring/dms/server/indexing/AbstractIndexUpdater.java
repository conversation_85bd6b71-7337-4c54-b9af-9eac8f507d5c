package ch.eisenring.dms.server.indexing;

import ch.eisenring.app.shared.CoreTickListenerAdapter;
import ch.eisenring.app.shared.timing.Timer;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.core.logging.Logger.LogLevel;
import ch.eisenring.dms.server.DMSServer;
import ch.eisenring.jdbc.JDBCBase;

import java.util.concurrent.atomic.AtomicInteger;

public abstract class AbstractIndexUpdater extends CoreTickListenerAdapter {

	private final static AtomicInteger ACTIVE_COUNT = new AtomicInteger();
	
	protected final DMSServer server;
	
	/**
	 * Frequency of checking for invalid search index (in seconds)
	 */
	private final Timer timer;
	
	private final int transactionMinSize;
	private final int transactionMaxSize;

	/**
	 * Maximum number of records to be updated in one
	 * transaction. This number is increased when
	 * a transaction completes successfully and decreased
	 * when it failed.
	 */
	private int transactionSize;
	
	/**
	 * Transaction size limits should be chosen carefully,
	 * too small hinders performance, too large may cause
	 * OutOfMemory exceptions from which the system may
	 * not recover gracefully.
	 */
	protected AbstractIndexUpdater(final DMSServer server,
								   final int transactionMinSize,
								   final int transactionMaxSize,
								   final Timer timer) {
		super(server);
		this.server = server;
		this.transactionMinSize = transactionMinSize;
		this.transactionMaxSize = transactionMaxSize;
		this.transactionSize = transactionMaxSize;
		this.timer = timer;
		Logger.debug("IndexUpdater created with timer at {}.", timer.toString());
	}

	/**
	 * Marks the search index as invalid.
	 * This will trigger a rebuild of the search index at the next server tick
	 * (usually within a few seconds, but depending on server load it may take longer) 
	 */
	public final void invalidate() {
		timer.markAsDue();
	}	

	@Override
	protected LogLevel getLogLevel() {
		return LogLevel.WARN;
	}

	@Override
	protected boolean isDue(final long now) {
		return ACTIVE_COUNT.get() <= 0 && timer.isDue();
	}

	@Override
	protected final void tickImpl(final long now) {
		ACTIVE_COUNT.incrementAndGet();
		try {
			updateIndexImpl();
		} catch (final Throwable t) {
			// a serious problem on index rebuild!
			Logger.error("Problem updating search index: " + t);
			Logger.error(t);
			if (transactionSize <= 1) {
				Logger.warn("There seems to be a serious problem with the database integrity. Will retry again in a few seconds.");
				// mark for retry
				timer.markAsDue();
			}
			updateTransactionSize(false);
		} finally {
			ACTIVE_COUNT.decrementAndGet();
		}
	}

	/**
	 * Needs to be called regularly. Checks if there is something
	 * to rebuild and rebuilds it if necessary.
	 */
	public final void updateIndex() {
		// check if an update is necessary
		if (!timer.isDue()) 
			return;
		try {
			updateIndexImpl();
		} catch (final Throwable t) {
			// a serious problem on index rebuild!
			Logger.error("Problem updating search index: " + t);
			Logger.error(t);
			if (transactionSize <= 1) {
				Logger.warn("There seems to be a serious problem with the database integrity. Will retry again in a few seconds.");
				// mark for retry
				timer.markAsDue();
			}
			updateTransactionSize(false);
		}
	}
	
	/**
	 * Implements the update of the index. Only called if the
	 * index was explicitly marked invalid, or the regular
	 * update interval has expired. 
	 */
	protected abstract void updateIndexImpl() throws Exception;

	/**
	 * Called when transaction ended. Controls the transaction size,
	 * in success it is increased until it reaches the maximum,
	 * on failure it is halved until the minimum is reached.
	 */
	protected void updateTransactionSize(final boolean success) {
		if (success) {
			// double transaction size
			transactionSize <<= 1;
		} else {
			// halve transaction size
			transactionSize >>= 1;
		}
		// keep the transaction size within reasonable bounds
		if (transactionSize < transactionMinSize) {
			transactionSize = transactionMinSize;
		} else if (transactionSize > transactionMaxSize) {
			transactionSize = transactionMaxSize;
		}
	}

	/**
	 * Gets the current transaction size
	 */
	public int getTransactionSize() {
		return transactionSize;
	}

	/**
	 * Modifies a SELECT statement to obey the current transaction size
	 */
	public String limitSelectToTransactionSize(final CharSequence selectStatement) {
		return JDBCBase.limitSelectSize(selectStatement, getTransactionSize());
	}

}

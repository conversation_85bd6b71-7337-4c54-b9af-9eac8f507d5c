package ch.eisenring.dms.server;

import ch.eisenring.app.server.AbstractServerComponent;
import ch.eisenring.app.shared.Configurable;
import ch.eisenring.core.application.Configuration;
import ch.eisenring.core.application.ConfigurationException;
import ch.eisenring.core.collections.Collection;
import ch.eisenring.core.feature.Feature;
import ch.eisenring.core.observable.Observable;
import ch.eisenring.core.platform.Platform;
import ch.eisenring.core.platform.PlatformPath;
import ch.eisenring.dms.server.dav.DAVServer;
import ch.eisenring.dms.server.filewatch.AbacusDocumentWatcher;
import ch.eisenring.dms.server.filewatch.LogiwareDocumentWatcher;
import ch.eisenring.dms.server.indexing.*;
import ch.eisenring.dms.server.indexing.participants.ParticipantIndexUpdater;
import ch.eisenring.dms.server.network.DMSPacketDispatcher;
import ch.eisenring.dms.server.nsi.*;
import ch.eisenring.dms.server.service.DMSServiceImpl;
import ch.eisenring.dms.server.service.DMSServiceTickListener;
import ch.eisenring.dms.shared.DMSConstants;
import ch.eisenring.dms.shared.locking.DMSObjectLockSet;

import java.io.File;

/**
 * DMS Server
 */
public final class DMSServer extends AbstractServerComponent implements Configurable {

    // --------------------------------------------------------------
    // ---
    // --- Server
    // ---
    // --------------------------------------------------------------

    public final SearchIndexUpdater searchIndexUpdater = new SearchIndexUpdater(this);
    public final PlaintextIndexUpdater plaintextIndexUpdater = new PlaintextIndexUpdater(this);
    private final ParticipantIndexUpdater participantIndexUpdater = new ParticipantIndexUpdater(this);
    private final BinaryCompressionUpdater binaryCompressionOptimizer = new BinaryCompressionUpdater(this);
    private final ClientUpdater clientUpdater = new ClientUpdater(this);
    private final DAVServer davServer = new DAVServer(this);
    private final LogiwareDocumentWatcher logiwareWatcher = new LogiwareDocumentWatcher(this);
    /**
     * Abacus FileWatcher, since 2020-09-16
     **/
    private final AbacusDocumentWatcher abacusWatcher = new AbacusDocumentWatcher(this);
    public final DirtyIndex dirtyIndex = new DirtyIndex(this);
    public final Observable<String> UPDATE_LOCALPATH = Observable.create(false, "UpdateLocalPath", null);
    public final Observable<DMSObjectLockSet> OBJECTLOCKSET = Observable.create(false, "ObjectLockSet", new DMSObjectLockSet());
    /**
     * Locking object used for altering properties
     */
    public final Object PROPERTY_LOCK = new Object();

    // --------------------------------------------------------------
    // ---
    // --- Feature management
    // ---
    // --------------------------------------------------------------

    /**
     * Features provided by this component
     */
    private final Feature[] features = {
            // tick listeners
            new TempDirectoryCleaner(this),
            plaintextIndexUpdater,
            searchIndexUpdater,
            participantIndexUpdater,
            binaryCompressionOptimizer,
            clientUpdater,
            // packet dispatcher(s)
            new DMSPacketDispatcher(this),
            // NSI related
            new DMSDocumentHandler(this),
            new DMSBarcodeHandlerABA(this),
            new DMSQRCodeHandlerABA(this),
            new DMSBarcodeHandlerH(this),
            new DMSBarcodeHandlerLW(this),
            new DMSBarcodeHandlerLH(this),
            // services
            new DMSServiceImpl(this),
            new DMSServiceTickListener(this)};

    @Override
    public Feature[] getFeatures() {
        return features;
    }

    // --------------------------------------------------------------
    // ---
    // --- Launch / Shutdown
    // ---
    // --------------------------------------------------------------

    @Override
    public void launch() throws Exception {
        super.launch();
        new DMSServerLaunch(this).launch();

        // start jobs
        logiwareWatcher.start();
        abacusWatcher.start();
    }

    @Override
    public void shutdown() throws Exception {
        try {
            davServer.stop();

            // stop jobs
            logiwareWatcher.stop();
            abacusWatcher.stop();

        } finally {
            super.shutdown();
        }
    }

    // --------------------------------------------------------------
    // ---
    // --- Configurable implementation
    // ---
    // --------------------------------------------------------------

    @Override
    public void getConfigurationFileNames(final Collection<String> fileNames) {
        logiwareWatcher.getConfigurationFileNames(fileNames);
        abacusWatcher.getConfigurationFileNames(fileNames);
        fileNames.add("DMS.cfg");
        fileNames.add(new File(Platform.getPlatform().getPath(PlatformPath.SETTINGS), "DMSServerUser.cfg").getAbsolutePath());
    }

    @Override
    public void configure(final Configuration configuration) throws ConfigurationException {
        configure(configuration, "DMSDatabase:", DMSConstants.DMS_DATABASE);
        UPDATE_LOCALPATH.set(configuration.getString("DMSUpdates:Path", null));
        logiwareWatcher.configure(configuration);
        abacusWatcher.configure(configuration);
        davServer.configure(configuration);
    }

}

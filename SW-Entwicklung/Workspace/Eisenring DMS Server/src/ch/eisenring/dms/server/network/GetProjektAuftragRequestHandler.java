package ch.eisenring.dms.server.network;

import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.collections.Collection;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.dms.server.DMSServer;
import ch.eisenring.dms.shared.creepingevil.DMSLogiProjektAuftrag;
import ch.eisenring.dms.shared.network.PacketGetProjektAuftragReply;
import ch.eisenring.dms.shared.network.PacketGetProjektAuftragRequest;
import ch.eisenring.logiware.code.hard.LWPaKommArtCode;
import ch.eisenring.logiware.code.service.LogiwareService;
import ch.eisenring.logiware.code.soft.LWProjektStatusCode;
import ch.eisenring.lw.api.LWSubKontaktPartnerData;
import ch.eisenring.lw.condition.Factory;
import ch.eisenring.lw.meta.LWProjektMeta;
import ch.eisenring.lw.model.navigable.LWObjectCache;
import ch.eisenring.lw.model.navigable.LWProjekt;
import ch.eisenring.lw.model.navigable.LWSubjekt;
import ch.eisenring.network.PacketSink;
import ch.eisenring.network.packet.AbstractPacket;

import java.util.stream.Collectors;

final class GetProjektAuftragRequestHandler extends AbstractDMSPacketHandler {

	GetProjektAuftragRequestHandler(final DMSServer server) {
		super(server, PacketGetProjektAuftragRequest.class);
	}

	@Override
	public PacketGetProjektAuftragReply handle(final AbstractPacket packet) {
		final PacketGetProjektAuftragRequest request = (PacketGetProjektAuftragRequest) packet;
		return handle(server, request);
	}

	@Override
	public void handle(final AbstractPacket packet, final PacketSink sink) {
		final PacketGetProjektAuftragRequest request = (PacketGetProjektAuftragRequest) packet;
		final PacketGetProjektAuftragReply reply = handle(server, request);
		sink.sendPacket(reply);
	}

	private static PacketGetProjektAuftragReply handle(final DMSServer server,
															final PacketGetProjektAuftragRequest request) {
		PacketGetProjektAuftragReply reply;
		try {
			reply = handleImpl(server, request);
		} catch (final Exception e) {
			reply = PacketGetProjektAuftragReply.create(request);
			reply.addMessage(new ErrorMessage(e));
		}
		return reply;
	}

	private static PacketGetProjektAuftragReply handleImpl(final DMSServer server,
																final PacketGetProjektAuftragRequest request) throws Exception {
		final PacketGetProjektAuftragReply reply = PacketGetProjektAuftragReply.create(request);
		final List<String> projectNumbers = request.getProjectnumbers();
		final LWObjectCache cache = server.locateService(LogiwareService.class).createObjectCache();

		final Collection<LWProjekt> projekte = cache.load(LWProjekt.class,
				Factory.ne(LWProjektMeta.ATR_STATUS, LWProjektStatusCode.STORNIERT),
				Factory.in(LWProjektMeta.ATR_PROJEKTNUMMER, projectNumbers)
			);
		
		for (final LWProjekt projekt : projekte) {
			final DMSLogiProjektAuftrag wrapper = new DMSLogiProjektAuftrag();
			wrapper.projektnummer = projekt.getProjektnummer();
			wrapper.wohnungsBezeichnung = projekt.getProjektWohnungBez();
			wrapper.familienBezeichnung = projekt.getProjektFamilienBez();
			wrapper.ort = projekt.getProjektOrt();
            addContactData(projekt, wrapper);
			reply.addProjektauftrag(wrapper);
			projectNumbers.remove(wrapper.projektnummer);
		}

		for (final String projectNumber : projectNumbers) {
			reply.addMessage(new ErrorMessage(Strings.concat(
					"Kein Projektauftrag für ", projectNumber, " gefunden (Logiware)")));
		}
		return reply;
	}

	private static void addContactData(LWProjekt projekt, DMSLogiProjektAuftrag projektAuftrag) {
        List<String> kundenNamen = new ArrayList<>();
		List<String> kundenEmailAdressen = new ArrayList<>();

	    final LWSubjekt kaeufer = projekt.getSubjektB();
        if (kaeufer != null && kaeufer.isBauherr()) {
            final Collection<LWSubKontaktPartnerData> kontaktDaten = kaeufer.getContacts();
            for (LWSubKontaktPartnerData data : kontaktDaten) {
				kundenNamen.add(data.getNameFirst() + " " + data.getNameLast());
				kundenEmailAdressen.addAll(data.getKommAdressen()
                        .stream()
                        .filter(k -> k.getPaKommArt().equals(LWPaKommArtCode.EMAIL))
                        .map(a -> a.getKommAdresse()).collect(Collectors.toList()));
            }
        }
        projektAuftrag.kundenNamen = kundenNamen;
        projektAuftrag.kundenEmailAdressen = kundenEmailAdressen;
    }

}

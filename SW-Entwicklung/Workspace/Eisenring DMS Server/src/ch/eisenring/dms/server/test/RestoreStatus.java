//package ch.eisenring.dms.server.test;
//
//import java.sql.SQLException;
//import java.util.Iterator;
//
//import ch.eisenring.core.collections.impl.HashMap;
//import ch.eisenring.dms.server.DMSServer;
//import ch.eisenring.dms.server.dao.DMSContextSink;
//import ch.eisenring.dms.shared.model.record.DMSContext;
//import ch.eisenring.jdbc.JDBCResultSet;
//import ch.eisenring.jdbc.RowHandler;
//import ch.eisenring.jdbc.StatementParameters;
//import ch.eisenring.jdbc.StatementWrapper;
//import ch.eisenring.model.Model;
//import ch.eisenring.model.TransactionContext;
//
//public class RestoreStatus {
//	
//	public static void update(DMSServer server) {
//		DMSContextSink sink = new DMSContextSink(server) {
//			@Override
//			protected void storeImpl(Iterator<Model> modelItr, TransactionContext context) throws SQLException {
//				final HashMap<Long, Integer> map = new HashMap<>();
//				String select = "select A.Object_rowId, A.ValueOld from DMSAuditTrail A where A.MadeBy = 'ROOT' and A.MadeOn >= '2014-06-10 14:56:00' and A.Auditcode = 4 and A.Valuenew = 3 ORDER BY A.MadeOn ASC";
//				doQuery(select, new StatementParameters(), new RowHandler() {
//					@Override
//					public void handleRow(final JDBCResultSet resultSet) throws SQLException {
//						Long rowId = resultSet.getLong(1);
//						Integer code = resultSet.getInteger(2);
//						map.put(rowId, code);
//					}
//				});
//				
//				String update = "UPDATE DMSObject SET Statuscode = ? WHERE rowId = ?";
//				StatementWrapper w = prepareStatement(update);
//				for (final Long key : map.keySet()) {
//					Integer code = map.get(key);
//					w.setObject(1, code);
//					w.setObject(2, key);
//					w.addBatch();
//				}
//				w.executeBatch();
//			}
//		};
//		DMSContext context = new DMSContext();
//		try {
//			context.store(sink);
//		} catch (final Exception e) {
//			throw new RuntimeException(e.getMessage(), e);
//		}
//	}
//
//}

package ch.eisenring.dms.server.service;

import ch.eisenring.app.shared.CoreTickListener;
import ch.eisenring.dms.server.DMSServer;
import ch.eisenring.dms.service.DMSService;

public class DMSServiceTickListener implements CoreTickListener {

	final DMSServer server;
	long nextDue;

	public DMSServiceTickListener(final DMSServer server) {
		this.server = server;
		this.nextDue = System.currentTimeMillis() + 60_000;
	}

	// --------------------------------------------------------------
	// ---
	// --- CoreTickListener implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public String getName() {
		return "DMSServiceTickListener";
	}

	@Override
	public void tick(final long now) {
		if (now < nextDue)
			return;
		nextDue = now + 60_000;
		try {
			final DMSService service = server.locateService(DMSService.class);
			((DMSServiceImpl) service).tick();
		} catch (final Exception e) {
			// ignore 
		}
	}

}

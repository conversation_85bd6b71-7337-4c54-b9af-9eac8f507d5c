package ch.eisenring.dms.server.service;

import static ch.eisenring.dms.server.DMSMapping.OM_OBJECT;
import static ch.eisenring.dms.server.DMSMapping.TM_OBJECT;
import static ch.eisenring.dms.server.DMSMapping.TM_PROPERTY;

import java.sql.SQLException;

import ch.eisenring.app.server.model.ContextSource;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.dms.server.DMSServer;
import ch.eisenring.dms.service.api.DMSObject;
import ch.eisenring.dms.service.codetables.DMSPropertyCode;
import ch.eisenring.dms.shared.DMSConstants;
import ch.eisenring.dms.shared.model.meta.DMSPropertyMeta;
import ch.eisenring.jdbc.StatementParameters;
import ch.eisenring.logiware.LWProjektKey;
import ch.eisenring.model.TransactionContext;
import ch.eisenring.model.TransactionSource;

/**
 * Helper for DMSService: Lookup project and base folders
 */
class FolderLookup {

	final DMSServer server;

	FolderLookup(final DMSServer server) {
		this.server = server;
	}

	void tick() {
		
	}

	// --------------------------------------------------------------
	// ---
	// --- Project Folder lookup
	// ---
	// --------------------------------------------------------------
	public DMSObject getProjectFolder(final String projectNumber) {
		try {
			if (Strings.isEmpty(projectNumber))
				return null;
			final String number = LWProjektKey.getSignificantProjektnummer(projectNumber);
			DMSObject folder = getFolderImpl(number, DMSPropertyCode.PROJECTNUMBER);
			return folder;
		} catch (final Exception e) {
			return null;
		}
	}

	// --------------------------------------------------------------
	// ---
	// --- Internal stuff
	// ---
	// --------------------------------------------------------------
	/**
	 * Implements loading folder from DB by given property
	 */
	private DMSObject getFolderImpl(final String projectNumber, final DMSPropertyCode propertyCode) throws Exception {
		final List<DMSObject> results = new ArrayList<>();
		final TransactionSource source = new ContextSource(DMSConstants.DMS_DATABASE) {
			@Override
			protected void loadImpl(final TransactionContext context) throws SQLException {
				final StringMaker sql = StringMaker.obtain();
				final StatementParameters params = new StatementParameters();
				TM_OBJECT.appendSelectSQL(sql, this, "O");
				sql.append(", ");
				qualifyTableName(sql, TM_PROPERTY.getTableSpecifier());
				sql.append(" AS P WHERE ");
				sql.append("P.");
				sql.append(TM_PROPERTY.getColumn(DMSPropertyMeta.ATR_PROPERTYVALUE));
				sql.append(" = ? AND P.");
				sql.append(TM_PROPERTY.getColumn(DMSPropertyMeta.ATR_PROPERTYCODE));
				sql.append(" = ? AND P.");
				sql.append(TM_PROPERTY.getColumn(DMSPropertyMeta.ATR_OBJECT_ROWID));
				sql.append(" = O.");
				sql.append(TM_OBJECT.getPrimaryKeyColumn());
				params.add(projectNumber);
				params.add(propertyCode);
				doQuery(sql.release(), params, (resultSet) -> {
					final DMSObject object = OM_OBJECT.loadRow(context, resultSet);
					results.add(object);
				});
			}
		};
		final TransactionContext context = new TransactionContext();
		context.load(source);
		if (results.isEmpty())
			return null;
		return results.get(0);
	}
	
}

package ch.eisenring.dms.server.service;

import static ch.eisenring.dms.server.dao.DMSModelMapping.TM_OBJECT;
import static ch.eisenring.dms.server.dao.DMSModelMapping.TM_PROPERTY;

import java.io.IOException;
import java.sql.SQLException;
import java.util.concurrent.atomic.AtomicReference;

import ch.eisenring.app.server.model.ContextSource;
import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.datatypes.strings.Msg;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.dms.server.DMSServer;
import ch.eisenring.dms.server.functions.SQLFunctions;
import ch.eisenring.dms.service.DMSServiceException;
import ch.eisenring.dms.service.DMSServiceResultCode;
import ch.eisenring.dms.service.codetables.DMSDocumentType;
import ch.eisenring.dms.service.codetables.DMSObjectStatus;
import ch.eisenring.dms.service.codetables.DMSObjectType;
import ch.eisenring.dms.service.codetables.DMSPropertyCode;
import ch.eisenring.dms.service.proxy.DMSDocumentHandle;
import ch.eisenring.dms.service.proxy.DMSFolderHandle;
import ch.eisenring.dms.service.proxy.DMSObjectHandle;
import ch.eisenring.dms.service.util.LogiwareUtil;
import ch.eisenring.dms.shared.DMSConstants;
import ch.eisenring.dms.shared.model.metaold.DMSObjectMetaModel;
import ch.eisenring.dms.shared.model.metaold.DMSPropertyMetaModel;
import ch.eisenring.jdbc.JDBCResultSet;
import ch.eisenring.jdbc.RowHandler;
import ch.eisenring.jdbc.StatementParameters;
import ch.eisenring.model.TransactionContext;

abstract class ObjectFinder extends ContextSource {

	protected final DMSServer server;
	
	protected ObjectFinder(final DMSServer server) {
		super(DMSConstants.DMS_DATABASE);
		this.server = server;
	}
	
	/**
	 * Locates children by selected property.
	 * 
	 * Note:
	 *  - a NULL rootFolderRowId will find objects in the entire DMS
	 *  - a NULL typeCode parameter will find all object types
	 *  - a NULL statusCode parameter will find objects in any state
	 */
	protected List<Long> getChildrenByProperty(
			final Long rootFolderRowId,
			final DMSObjectType typeCode,
			final DMSObjectStatus statusCode,
			final DMSPropertyCode propertyCode,
			final String propertyValue) throws SQLException {
		final StatementParameters params = new StatementParameters(5);

		final boolean requiresObjectJoin = (!AbstractCode.isNull(statusCode))
				|| (!AbstractCode.isNull(typeCode));

		final List<Long> result = new ArrayList<>();

		final StringMaker sql = StringMaker.obtain(256);
		sql.append("SELECT ");
		sql.append(TM_PROPERTY.getColumn(DMSPropertyMetaModel.ATR_OBJECT_ROWID));
		sql.append(" FROM ");
		qualifyTableName(sql, TM_PROPERTY.getTableSpecifier(), "P");
		sql.append(WITH_NOLOCK);
		if (requiresObjectJoin) {
			sql.append(", ");
			qualifyTableName(sql, TM_OBJECT.getTableSpecifier(), "O");
			sql.append(WITH_NOLOCK);
		}
		sql.append(" WHERE ");
		if (rootFolderRowId != null) {
			// only children of selected folder
			sql.append("P.");
			sql.append(TM_PROPERTY.getColumn(DMSPropertyMetaModel.ATR_OBJECT_ROWID));
			sql.append(" IN (SELECT rowId FROM ");
			SQLFunctions.ALL_CHILDREN.appendCall(sql, this, "?");
			sql.append(") AND ");
			params.add(rootFolderRowId);
		}
		sql.append("P.");
		sql.append(TM_PROPERTY.getColumn(DMSPropertyMetaModel.ATR_PROPERTYCODE));
		sql.append(" = ?");
		params.add(propertyCode);
		
		sql.append(" AND P.");
		sql.append(TM_PROPERTY.getColumn(DMSPropertyMetaModel.ATR_PROPERTYVALUE));
		if (propertyValue == null) {
			sql.append(" IS NULL");
		} else {
			sql.append(" = ?");
			params.add(propertyValue);
		}

		if (!AbstractCode.isNull(typeCode)) {
			sql.append(" AND O.");
			sql.append(TM_OBJECT.getColumn(DMSObjectMetaModel.ATR_TYPECODE));
			sql.append(" = ?");
			params.add(typeCode);
		}

		if (!AbstractCode.isNull(statusCode)) {
			sql.append(" AND O.");
			sql.append(TM_OBJECT.getColumn(DMSObjectMetaModel.ATR_STATUSCODE));
			sql.append(" <= ?");
			params.add(statusCode);
		}

		if (requiresObjectJoin) {
			sql.append(" AND P.");
			sql.append(TM_PROPERTY.getColumn(DMSPropertyMetaModel.ATR_OBJECT_ROWID));
			sql.append(" = O.");
			sql.append(TM_OBJECT.getPrimaryKeyColumn());
		}

		doQuery(sql.release(), params, new RowHandler() {
			@Override
			public void handleRow(final JDBCResultSet resultSet) throws SQLException {
				final long folderRowId = resultSet.getLong(1);
				if (resultSet.wasNull())
					throw new SQLException("unexpected NULL rowId");
				result.add(folderRowId);
			}
		});

		return result;
	}

	/**
	 * RowHandler for reading object handles
	 */
	static class ObjectHandleRowHandler implements RowHandler {
		private final static int[] DEFAULT_COLUMN_INDICES = { 1, 2, 3, 4, 5, 6 };
		private final int[] columnIndices;
		private final java.util.Collection<DMSObjectHandle> result;
	
		public ObjectHandleRowHandler(final java.util.Collection<DMSObjectHandle> result) {
			this(result, DEFAULT_COLUMN_INDICES);
		}

		public ObjectHandleRowHandler(final java.util.Collection<DMSObjectHandle> result, final int[] columnIndices) {
			this.columnIndices = columnIndices;
			this.result = result;
		}
		
		@Override
		public void handleRow(final JDBCResultSet resultSet) throws SQLException {
			final long rowId = resultSet.getLong(columnIndices[0]);
			if (resultSet.wasNull())
				throw new SQLException("unexpected NULL rowId");
			final DMSObjectType objectType = resultSet.getCode(columnIndices[1], DMSObjectType.class);
			final String name = resultSet.getString(columnIndices[2]);
			final DMSObjectStatus status = resultSet.getCode(columnIndices[3], DMSObjectStatus.class);
			final DMSObjectHandle handle;
			if (DMSObjectType.FOLDER.equals(objectType)) {
				handle = DMSFolderHandle.create(rowId, name, status);
			} else if (DMSObjectType.FILE.equals(objectType)) {
				final long byteSize = resultSet.getLong(columnIndices[4]);
				final DMSDocumentType documentType = resultSet.getCode(columnIndices[5], DMSDocumentType.class);
				handle = DMSDocumentHandle.create(rowId, name, status, byteSize, documentType);
			} else {
				throw new SQLException(Strings.concat("invalid object type: ", objectType));
			}
			result.add(handle);
		}
	}

	/**
	 * Convert single rowId to object handle
	 */
	static DMSObjectHandle getObject(final DMSServer server, final long rowId) throws DMSServiceException {
		final AtomicReference<List<DMSObjectHandle>> result = new AtomicReference<>();
		final ObjectFinder source = new ObjectFinder(server) {
			@Override
			protected void loadImpl(final TransactionContext context) throws SQLException {
				final List<Long> rowIds = new ArrayList<>(1);
				rowIds.add(rowId);
				result.set(getObjects(rowIds));
			}
		};
		final TransactionContext context = new TransactionContext();
		try {
			context.load(source);
		} catch (final IOException e) {
			final String message = Msg.mk("Fehler beim laden von DMSObjekt {}: {}", rowId, e);
			throw new DMSServiceException(DMSServiceResultCode.GENERIC_ERROR, message);
		}
		final List<DMSObjectHandle> handles = result.get();
		switch (handles.size()) {
			case 0: {
				final String message = Msg.mk("Kein DMSObject mit rowId = {} gefunden", rowId);
				throw new DMSServiceException(DMSServiceResultCode.OBJECT_NOT_FOUND, message);
			}
			case 1: return handles.get(0);
			default: {
				final String message = Msg.mk("Mehr als ein DMSObject mit rowId = {} gefunden", rowId);
				throw new DMSServiceException(DMSServiceResultCode.TOO_MANY_RESULTS, message);
			}
		}
	
	}

	/**
	 * Converts rowId's to object handles
	 */
	protected List<DMSObjectHandle> getObjects(final java.util.Collection<Long> rowIds) throws SQLException {
		if (rowIds == null || rowIds.isEmpty())
			return new ArrayList<>();

		final StatementParameters params = new StatementParameters();
		final StringMaker sql = StringMaker.obtain(512);
		sql.append("SELECT ");
		sql.append(TM_OBJECT.getPrimaryKeyColumn());
		sql.append(", ");
		sql.append(TM_OBJECT.getColumn(DMSObjectMetaModel.ATR_TYPECODE));
		sql.append(", ");
		sql.append(TM_OBJECT.getColumn(DMSObjectMetaModel.ATR_OBJECTNAME));
		sql.append(", ");
		sql.append(TM_OBJECT.getColumn(DMSObjectMetaModel.ATR_STATUSCODE));
		sql.append(", ");
		sql.append(TM_OBJECT.getColumn(DMSObjectMetaModel.ATR_BYTESIZE));
		sql.append(", ");
		sql.append("tru_typeclass");
		sql.append(" FROM ");
		qualifyTableName(sql, TM_OBJECT.getTableSpecifier());
		sql.append(WITH_NOLOCK);
		sql.append(" WHERE ");
		sql.append(TM_OBJECT.getPrimaryKeyColumn());
		sql.append(" IN ");
		prepareIn(sql, rowIds);
		params.addAll(rowIds);
		
		final List<DMSObjectHandle> result = new ArrayList<>(rowIds.size());
		doQuery(sql.release(), params, new ObjectHandleRowHandler(result));
		return result;
	}

	/**
	 * Locates child by selected property
	 * 
	 * If there is no such child, returns NULL.
	 * If there are multiple matches, returns NULL.
	 */
	protected DMSObjectHandle getObjectByProperty(final DMSFolderHandle folder,
			final DMSObjectType typeCode,
			final DMSObjectStatus statusCode,
			final DMSPropertyCode propertyCode,
			final Object propertyValue) throws SQLException {
		final List<? extends DMSObjectHandle> list = getChildrenByProperty(folder, typeCode, statusCode, propertyCode, propertyValue);
		switch (list.size()) {
			default:
				return null;
			case 1:
				return list.get(0);
		}
	}

	/**
	 * Locates children by selected property.
	 * 
	 * Note:
	 *  - a NULL rootFolder will find objects in the entire DMS
	 *  - a NULL typeCode parameter will find all object types
	 *  - a NULL statusCode parameter will find objects in any state
	 */
	protected List<? extends DMSObjectHandle> getChildrenByProperty(
			final DMSFolderHandle rootFolder,
			final DMSObjectType typeCode,
			final DMSObjectStatus statusCode,
			final DMSPropertyCode propertyCode,
			final Object propertyValue) throws SQLException {
		final StatementParameters params = new StatementParameters(5);

		final List<DMSObjectHandle> result = new ArrayList<>();

		final StringMaker sql = StringMaker.obtain(512);
		sql.append("SELECT O.");
		sql.append(TM_OBJECT.getPrimaryKeyColumn());
		sql.append(", O.");
		sql.append(TM_OBJECT.getColumn(DMSObjectMetaModel.ATR_TYPECODE));
		sql.append(", O.");
		sql.append(TM_OBJECT.getColumn(DMSObjectMetaModel.ATR_OBJECTNAME));
		sql.append(", O.");
		sql.append(TM_OBJECT.getColumn(DMSObjectMetaModel.ATR_STATUSCODE));
		sql.append(", O.");
		sql.append(TM_OBJECT.getColumn(DMSObjectMetaModel.ATR_BYTESIZE));
		sql.append(", O.");
		sql.append("tru_typeclass");
		sql.append(" FROM ");
		qualifyTableName(sql, TM_OBJECT.getTableSpecifier(), "O");
		sql.append(WITH_NOLOCK);
		sql.append(", ");
		qualifyTableName(sql, TM_PROPERTY.getTableSpecifier(), "P");
		sql.append(WITH_NOLOCK);
		sql.append(" WHERE P.");
		sql.append(TM_PROPERTY.getColumn(DMSPropertyMetaModel.ATR_OBJECT_ROWID));
		sql.append(" = O.");
		sql.append(TM_OBJECT.getPrimaryKeyColumn());
		
		if (rootFolder != null) {
			// only children of selected folder
			sql.append(" AND P.");
			sql.append(TM_PROPERTY.getColumn(DMSPropertyMetaModel.ATR_OBJECT_ROWID));
			sql.append(" IN (SELECT rowId FROM ");
			SQLFunctions.ALL_CHILDREN.appendCall(sql, this, "?");
			sql.append(")");
			params.add(rootFolder.getPKValue());
		}

		sql.append(" AND P.");
		sql.append(TM_PROPERTY.getColumn(DMSPropertyMetaModel.ATR_PROPERTYCODE));
		sql.append(" = ?");
		params.add(propertyCode);
		
		sql.append(" AND P.");
		sql.append(TM_PROPERTY.getColumn(DMSPropertyMetaModel.ATR_PROPERTYVALUE));
		{
			final String s = toStringPropertyvalue(propertyValue);
			if (s == null) {
				sql.append(" IS NULL");
			} else {
				sql.append(" = ?");
				params.add(s);
			}
		}

		if (!AbstractCode.isNull(typeCode)) {
			sql.append(" AND O.");
			sql.append(TM_OBJECT.getColumn(DMSObjectMetaModel.ATR_TYPECODE));
			sql.append(" = ?");
			params.add(typeCode);
		}

		if (!AbstractCode.isNull(statusCode)) {
			sql.append(" AND O.");
			sql.append(TM_OBJECT.getColumn(DMSObjectMetaModel.ATR_STATUSCODE));
			sql.append(" <= ?");
			params.add(statusCode);
		}

		doQuery(sql.release(), params, new ObjectHandleRowHandler(result));
		return result;
	}
	
	/**
	 * Utility methods to convert a property value to string
	 */
	public static String toStringPropertyvalue(final Object propertyValue) {
		if (propertyValue instanceof AbstractCode) {
			final AbstractCode code = (AbstractCode) propertyValue;
			return Strings.toString(code.getKey());
		}
		return Strings.toString(propertyValue);
	}
	
	/**
	 * Reduces the given number to the significant number part
	 */
	public static String getSignificantProjectnumber(final String projectnumber) throws DMSServiceException {
		if (projectnumber == null) {
			final String message = Msg.mk("Ungültige Projektnummer: {}", projectnumber);
			throw new DMSServiceException(DMSServiceResultCode.INVALID_PARAMETER, message);
		}
		try {
			return LogiwareUtil.getSigificantProjektnummer(projectnumber);
		} catch (final Exception e) {
			Logger.warn(e);
			final String message = Msg.mk("Ungültige Projektnummer {}: {}", projectnumber, e.getMessage());
			throw new DMSServiceException(DMSServiceResultCode.INVALID_PARAMETER, message);
		}
	}

	/**
	 * Finds objects by specified criteria
	 */
	public static List<? extends DMSObjectHandle> getObjectsByProperty(final DMSServer server, final DMSFolderHandle folder,
			final DMSObjectType typeCode, final DMSObjectStatus statusCode,
			final DMSPropertyCode propertyCode,	final Object propertyValue) throws DMSServiceException {
		final AtomicReference<List<? extends DMSObjectHandle>> result = new AtomicReference<>();
		final ObjectFinder source = new ObjectFinder(server) {
			@Override
			protected void loadImpl(final TransactionContext context) throws SQLException {
				final List<? extends DMSObjectHandle> list = getChildrenByProperty(folder, typeCode, statusCode, propertyCode, propertyValue);
				result.set(list);
			}
		};
		try {
			final TransactionContext context = new TransactionContext();
			context.load(source);
		} catch (final IOException e) {
			final String message = Msg.mk("Fehler beim laden von {} mit {} = '{}': {}",
					typeCode, propertyCode, propertyValue, e.getMessage());
			throw new DMSServiceException(DMSServiceResultCode.GENERIC_ERROR, message);
		}
		return result.get();
	}

	/**
	 * Finds object by specified criteria
	 */
	public static DMSObjectHandle getObjectByProperty(final DMSServer server, final DMSFolderHandle folder,
			final DMSObjectType typeCode, final DMSObjectStatus statusCode,
			final DMSPropertyCode propertyCode,	final Object propertyValue) throws DMSServiceException {
		final AtomicReference<List<? extends DMSObjectHandle>> result = new AtomicReference<>();
		final ObjectFinder source = new ObjectFinder(server) {
			@Override
			protected void loadImpl(final TransactionContext context) throws SQLException {
				final List<? extends DMSObjectHandle> list = getChildrenByProperty(folder, typeCode, statusCode, propertyCode, propertyValue);
				result.set(list);
			}
		};
		try {
			final TransactionContext context = new TransactionContext();
			context.load(source);
		} catch (final IOException e) {
			final String message = Msg.mk("Fehler beim laden von {} mit {} = '{}': {}",
					typeCode, propertyCode, propertyValue, e.getMessage());
			throw new DMSServiceException(DMSServiceResultCode.GENERIC_ERROR, message);
		}
		final List<? extends DMSObjectHandle> list = result.get();
		switch (list.size()) {
			case 0: {
				final String message = Msg.mk("Kein {} mit {} = '{}' gefunden",
						typeCode, propertyCode, propertyValue);
				throw new DMSServiceException(DMSServiceResultCode.OBJECT_NOT_FOUND, message);
			}
			case 1: return list.get(0);
			default: {
				final String message = Msg.mk("Mehr als ein {} mit {} = '{}' gefunden",
						typeCode, propertyCode, propertyValue);
				throw new DMSServiceException(DMSServiceResultCode.TOO_MANY_RESULTS, message);
			}
		}
	}

	/**
	 * Gets the contents of a folder. If null is passed, gets the root contents.
	 */
	public static List<DMSObjectHandle> getFolderContents(
			final DMSServer server,
			final DMSFolderHandle folder) throws DMSServiceException {
		final List<DMSObjectHandle> result = new ArrayList<>();
		final ObjectFinder source = new ObjectFinder(server) {
			@Override
			protected void loadImpl(final TransactionContext context) throws SQLException {
				final StatementParameters params = new StatementParameters(1);
				final StringMaker sql = StringMaker.obtain(512);
				sql.append("SELECT ");
				sql.append(TM_OBJECT.getPrimaryKeyColumn());
				sql.append(", ");
				sql.append(TM_OBJECT.getColumn(DMSObjectMetaModel.ATR_TYPECODE));
				sql.append(", ");
				sql.append(TM_OBJECT.getColumn(DMSObjectMetaModel.ATR_OBJECTNAME));
				sql.append(", ");
				sql.append(TM_OBJECT.getColumn(DMSObjectMetaModel.ATR_STATUSCODE));
				sql.append(", ");
				sql.append(TM_OBJECT.getColumn(DMSObjectMetaModel.ATR_BYTESIZE));
				sql.append(", tru_typeclass");
				sql.append(" FROM ");
				qualifyTableName(sql, TM_OBJECT.getTableSpecifier());
				sql.append(WITH_NOLOCK);
				sql.append(" WHERE ");
				sql.append(TM_OBJECT.getColumn(DMSObjectMetaModel.ATR_PARENT_ROWID));
				if (folder == null) {
					sql.append(" IS NULL");
				} else {
					sql.append(" = ?");
					params.add(folder.getPKValue());
				}
				doQuery(sql.release(), params, new ObjectHandleRowHandler(result));
			}
		};
		try {
			final TransactionContext context = new TransactionContext();
			context.load(source);
		} catch (final IOException e) {
			final String message = Msg.mk("Fehler beim Laden von Ordnerinhalt für rowId = {}: {}", folder, e.getMessage());
			throw new DMSServiceException(DMSServiceResultCode.GENERIC_ERROR, message);
		}
		result.trimToSize();
		return result;
	}

}


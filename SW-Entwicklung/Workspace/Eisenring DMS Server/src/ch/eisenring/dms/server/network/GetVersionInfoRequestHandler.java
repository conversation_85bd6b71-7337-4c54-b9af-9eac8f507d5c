package ch.eisenring.dms.server.network;

import java.sql.SQLException;

import ch.eisenring.app.server.model.ContextSource;
import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.dms.server.DMSServer;
import ch.eisenring.dms.shared.DMSConstants;
import ch.eisenring.dms.shared.model.api.DMSVersion;
import ch.eisenring.dms.shared.model.metaold.DMSVersionMetaModel;
import ch.eisenring.dms.shared.model.pojo.DMSVersionPojo;
import ch.eisenring.dms.shared.network.PacketGetVersionInfoReply;
import ch.eisenring.dms.shared.network.PacketGetVersionInfoRequest;
import ch.eisenring.jdbc.JDBCResultSet;
import ch.eisenring.jdbc.StatementWrapper;
import ch.eisenring.model.Model;
import ch.eisenring.model.TransactionContext;
import ch.eisenring.model.TransactionSource;
import ch.eisenring.model.engine.jdbc.SingleTableMapping;
import ch.eisenring.network.PacketSink;
import ch.eisenring.network.packet.AbstractPacket;

public final class GetVersionInfoRequestHandler extends AbstractDMSPacketHandler {

	private final static SingleTableMapping MAPPING = SingleTableMapping.get(DMSVersionMetaModel.METACLASS, DMSVersionMetaModel.TABLE);

	private GetVersionInfoRequestHandler(final DMSServer server) {
		super(server, PacketGetVersionInfoRequest.class);
	}

	@Override
	public void handle(final AbstractPacket abstractPacket, final PacketSink sink) {
		final PacketGetVersionInfoRequest request = (PacketGetVersionInfoRequest) abstractPacket;
		PacketGetVersionInfoReply reply;
		try {
			reply = handle(server, request);
		} catch (final Exception e) {
			reply = PacketGetVersionInfoReply.create(request, new ErrorMessage(e));
		}
		if (reply == null)
			reply = PacketGetVersionInfoReply.create(request, ErrorMessage.ERROR);
		sink.sendPacket(reply, false);
		
	}
	public static PacketGetVersionInfoReply handle(final DMSServer server, final PacketGetVersionInfoRequest request) throws Exception {
		final List<DMSVersion> versions = new ArrayList<DMSVersion>();
		final TransactionSource source = new ContextSource(DMSConstants.DMS_DATABASE) {
			@Override
			protected void loadImpl(final TransactionContext context) throws SQLException {
				final List<Long> objectRowIds = request.getObjectRowIds();
				final StringMaker sql = StringMaker.obtain(512);
				MAPPING.appendSelectSQL(sql, this);
				sql.append(" WHERE ");
				sql.append(MAPPING.getColumn(DMSVersionMetaModel.ATR_OBJECT_ROWID));
				sql.append(" IN ");
				prepareIn(sql, objectRowIds);
				final StatementWrapper select = prepareStatement(sql.release());
				try {
					for (int i=0; i<objectRowIds.size(); ++i) {
						select.setObject(i + 1, objectRowIds.get(i));
					}
					final JDBCResultSet resultSet = select.executeQuery();
					try {
						final List<Model> models = MAPPING.loadRows(context, resultSet);
						for (int i=0; i<models.size(); ++i) {
							final DMSVersion model = (DMSVersion) models.get(i);
							final DMSVersion version = DMSVersionPojo.create(model);
							versions.add(version);
						}
					} finally {
						closeSilent(resultSet);
					}
				} finally {
					closeSilent(select);
				}
			}
		};
		final TransactionContext context = new TransactionContext();
		context.load(source);
		return PacketGetVersionInfoReply.create(request, versions);
	}

}

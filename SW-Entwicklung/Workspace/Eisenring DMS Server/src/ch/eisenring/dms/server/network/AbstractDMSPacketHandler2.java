package ch.eisenring.dms.server.network;

import ch.eisenring.app.server.network.AbstractPacketHandler;
import ch.eisenring.dms.server.DMSServer;
import ch.eisenring.dms.shared.network.PacketObjectMessages;
import ch.eisenring.dms.shared.network.cmd.ObjectMessageSet;
import ch.eisenring.network.implementation.PacketDispatchMode;
import ch.eisenring.network.packet.AbstractPacket;

/**
 * <AUTHOR> href="mailto:<EMAIL>?subject=AbstractDMSPacketHandler2"><PERSON></a>
 * @version <table border="1" cellspacing="0" cellpadding="3"><tr><th> Datum </th><th> Bemerkungen </th></tr><tr><td> 25.02.2020 </td><td> created </td></tr></table>
 * @param <REQ> Type of Request
 */
abstract class AbstractDMSPacketHandler2<REQ extends AbstractPacket> extends AbstractPacketHandler<DMSServer, REQ> {

	protected AbstractDMSPacketHandler2(DMSServer server, Class<REQ> packetClass, PacketDispatchMode packetDispatchMode) {
		super(server, packetClass, packetDispatchMode);
	}

	protected AbstractDMSPacketHandler2(DMSServer server, Class<REQ> packetClass) {
		this(server, packetClass, PacketDispatchMode.ASYNCHRONOUS);
	}

	protected static void broadcastMessages(DMSServer server, ObjectMessageSet changeMessages, boolean waitForSend) {
		if (changeMessages != null && !changeMessages.isEmpty()) {
			PacketObjectMessages packet = PacketObjectMessages.create(changeMessages);
			server.broadcast(packet, waitForSend);
		}
	}

	protected void broadcastMessages(ObjectMessageSet changeMessages, boolean waitForSend) {
		broadcastMessages(server, changeMessages, waitForSend);
	}

	protected void broadcastMessages(ObjectMessageSet changeMessages) {
		broadcastMessages(changeMessages, false);
	}

}

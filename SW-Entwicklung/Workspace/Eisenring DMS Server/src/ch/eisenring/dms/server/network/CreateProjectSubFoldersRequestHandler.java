package ch.eisenring.dms.server.network;

import static ch.eisenring.dms.server.DMSMapping.TM_OBJECT;
import static ch.eisenring.dms.server.DMSMapping.TM_PROPERTY;

import java.sql.SQLException;
import java.util.Collection;
import java.util.Iterator;

import ch.eisenring.app.server.model.ContextSource;
import ch.eisenring.core.HashUtil;
import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.collections.Set;
import ch.eisenring.core.collections.impl.HashSet;
import ch.eisenring.core.datatypes.strings.Msg;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.dms.server.DMSServer;
import ch.eisenring.dms.server.functions.SQLFunctions;
import ch.eisenring.dms.server.util.ReplicationTemplate;
import ch.eisenring.dms.server.util.ReplicationTemplateSink;
import ch.eisenring.dms.service.DMSService;
import ch.eisenring.dms.service.codetables.DMSFolderType;
import ch.eisenring.dms.service.codetables.DMSObjectType;
import ch.eisenring.dms.service.codetables.DMSPropertyCode;
import ch.eisenring.dms.service.codetables.DMSSpecialFunctionCode;
import ch.eisenring.dms.service.proxy.DMSFolderHandle;
import ch.eisenring.dms.shared.model.meta.DMSObjectMeta;
import ch.eisenring.dms.shared.model.meta.DMSPropertyMeta;
import ch.eisenring.dms.shared.network.PacketCreateProjectSubFoldersReply;
import ch.eisenring.dms.shared.network.PacketCreateProjectSubFoldersRequest;
import ch.eisenring.dms.shared.network.cmd.ObjectMessageSet;
import ch.eisenring.dms.shared.util.FolderTemplateVariables;
import ch.eisenring.dms.shared.util.FolderVariableTemplateExpander;
import ch.eisenring.jdbc.StatementParameters;
import ch.eisenring.logiware.LWProjektKey;
import ch.eisenring.logiware.code.pseudo.AbwicklungsartCode;
import ch.eisenring.logiware.code.service.LogiwareService;
import ch.eisenring.logiware.code.soft.LWAuftragStatusCode;
import ch.eisenring.lw.condition.Factory;
import ch.eisenring.lw.meta.LWAuftragMeta;
import ch.eisenring.lw.model.navigable.LWAuftrag;
import ch.eisenring.lw.model.navigable.LWObjectCache;
import ch.eisenring.model.TransactionContext;
import ch.eisenring.network.PacketSink;
import ch.eisenring.network.packet.AbstractPacket;

final class CreateProjectSubFoldersRequestHandler extends AbstractDMSPacketHandler {

	CreateProjectSubFoldersRequestHandler(final DMSServer server) {
		super(server, PacketCreateProjectSubFoldersRequest.class);
	}

	@Override
	public PacketCreateProjectSubFoldersReply handle(final AbstractPacket packet) {
		final PacketCreateProjectSubFoldersRequest request = (PacketCreateProjectSubFoldersRequest) packet;
		final PacketCreateProjectSubFoldersReply reply = handle(request);
		return reply;
	}

	@Override
	public void handle(final AbstractPacket packet, final PacketSink sink) {
		final PacketCreateProjectSubFoldersRequest request = (PacketCreateProjectSubFoldersRequest) packet;
		final PacketCreateProjectSubFoldersReply reply = handle(request);
		if (reply.isValid()) {
			broadcastMessages(reply.getChangeSet());
		}
		sink.sendPacket(reply);
	}

	private PacketCreateProjectSubFoldersReply handle(final PacketCreateProjectSubFoldersRequest request) {
		try {
			final PacketCreateProjectSubFoldersReply reply = handleImpl(request);
			if (reply != null)
				return reply;
			return PacketCreateProjectSubFoldersReply.create(request, ErrorMessage.ERROR);
		} catch (final Exception e) {
			return PacketCreateProjectSubFoldersReply.create(request, new ErrorMessage(e));
		}
	}

	// --------------------------------------------------------------
	// ---
	// --- Helper state classes
	// ---
	// --------------------------------------------------------------
	static class FolderInfo {
		public long rowId;
		public final String subNummer;

		FolderInfo(final long rowId, final String subNummer) {
			this.rowId = rowId;
			this.subNummer = subNummer;
		}
	
		// ----------------------------------------------------------
		// ---
		// --- Object overrides
		// ---
		// ----------------------------------------------------------
		@Override
		public boolean equals(final Object o) {
			return o instanceof FolderInfo && Strings.equals(((FolderInfo) o).subNummer, subNummer);
		}
		
		@Override
		public int hashCode() {
			return Strings.hashCode(subNummer);
		}
	}

	static class ProjektInfo {
		public final String projektNummer;
		public final long folderRowIdProjekt;
		public long folderRowIdAusfuehrungsplaene;
		public final Set<FolderInfo> existingSubFolders = new HashSet<>();
		public final Set<FolderInfo> missingSubFolders = new HashSet<>();
	
		ProjektInfo(final long folderRowIdProjekt, final String projektNummer) {
			this.folderRowIdProjekt = folderRowIdProjekt;
			this.projektNummer = projektNummer;
		}
	
		// ----------------------------------------------------------
		// ---
		// --- Object overrides
		// ---
		// ----------------------------------------------------------
		@Override
		public boolean equals(final Object o) {
			return o instanceof ProjektInfo && ((ProjektInfo) o).folderRowIdProjekt == this.folderRowIdProjekt;
		}
		
		@Override
		public int hashCode() {
			return HashUtil.hashCode(folderRowIdProjekt);
		}
	}

	// --------------------------------------------------------------
	// ---
	// --- Handler implementation
	// ---
	// --------------------------------------------------------------
	private PacketCreateProjectSubFoldersReply handleImpl(final PacketCreateProjectSubFoldersRequest request) throws Exception {
		final long basisFolderId = getBasisFolderId(request.getBasisNummer());
		final Set<ProjektInfo> projekte = getProjekte(basisFolderId);
		detectMissingSubFolders(projekte, request.getAbwicklungsArten());
		final ObjectMessageSet changeSet = createMissingSubFolders(request, projekte);
		final PacketCreateProjectSubFoldersReply reply =
				PacketCreateProjectSubFoldersReply.create(request, changeSet);
		return reply;
	}

	// --------------------------------------------------------------
	// ---
	// --- Logiware Analysis
	// ---
	// --------------------------------------------------------------
	private void detectMissingSubFolders(final java.util.Collection<ProjektInfo> projekte,
			final java.util.Collection<AbwicklungsartCode> abwicklungsArten) throws Exception {
		if (projekte.isEmpty())
			return;
		final LWObjectCache cache; {
			final LogiwareService service = server.locateService(LogiwareService.class);
			cache = service.createObjectCache();
		}
		for (final ProjektInfo info : projekte) {
			final Collection<LWAuftrag> auftraege = cache.load(LWAuftrag.class,
					Factory.ne(LWAuftragMeta.ATR_STATUSCODE, LWAuftragStatusCode.STORNIERT),
					Factory.in(LWAuftragMeta.ATR_ABWICKLUNGSART, abwicklungsArten),
					Factory.or(
							Factory.like(LWAuftragMeta.ATR_PROJEKTNUMMER, Strings.concat(info.projektNummer, "-_")),
							Factory.like(LWAuftragMeta.ATR_PROJEKTNUMMER, Strings.concat(info.projektNummer, "-__"))
						)
				);
			for (final LWAuftrag auftrag : auftraege) {
				final String mainNumber = LWProjektKey.getSignificantProjektnummer(auftrag.getProjektnummer());
				final ProjektInfo projektInfo = get(projekte, mainNumber);
				if (projektInfo == null)
					continue;
				final String subNumber = LWProjektKey.getProjektnummerExtension(auftrag.getProjektnummer());
				final FolderInfo folderInfo = new FolderInfo(0, subNumber);
				if (projektInfo.existingSubFolders.contains(folderInfo))
					continue;
				projektInfo.missingSubFolders.add(folderInfo);
			}
		}
	}

	private static ProjektInfo get(final java.util.Collection<ProjektInfo> projektInfos, final String projektNummer) {
		for (final ProjektInfo projektInfo : projektInfos) {
			if (Strings.equals(projektInfo.projektNummer, projektNummer))
				return projektInfo;
		}
		return null;
	}

	// --------------------------------------------------------------
	// ---
	// --- Existing Structure Analysis
	// ---
	// --------------------------------------------------------------
	private long getBasisFolderId(final String basisNummer) throws Exception {
		final DMSService service = server.locateService(DMSService.class);
		final DMSFolderHandle folder = service.getBasisFolder(basisNummer);
		if (folder == null)
			throw new RuntimeException(Msg.mk("Kein Basisordner mit Objektnummer {} gefunden", basisNummer));
		return folder.getPKValue();
	}

	/**
	 * Analyzes existing Basis structure and finds the existing projects
	 * and their relevant sub-folders.
	 */
	static Set<ProjektInfo> getProjekte(final long basisFolderId) throws Exception {
		final Set<ProjektInfo> projekte = new HashSet<>();
		final ContextSource source = new ContextSource(TM_PROPERTY.getTableSpecifier().getDatabase()) {
			@Override
			protected void loadImpl(final TransactionContext context) throws SQLException {
				final StatementParameters params = new StatementParameters();
				{ // collect project folders
					params.clear();
					final StringMaker sql = StringMaker.obtain();
					sql.append("SELECT C." + SQLFunctions.ALL_CHILDREN_ROWID + ", P.");
					sql.append(TM_PROPERTY.getColumn(DMSPropertyMeta.ATR_PROPERTYVALUE));
					sql.append(" FROM ");
					SQLFunctions.ALL_CHILDREN.appendCall(sql, this, "?");
					params.add(basisFolderId);
					sql.append(" C, ");
					qualifyTableName(sql, TM_PROPERTY.getTableSpecifier());
					sql.append(" P WHERE C.");
					sql.append(SQLFunctions.ALL_CHILDREN_ROWID);
					sql.append(" = P.");
					sql.append(TM_PROPERTY.getColumn(DMSPropertyMeta.ATR_OBJECT_ROWID));
					sql.append(" AND C." + SQLFunctions.ALL_CHILDREN_TYPECODE + " = ? AND P.");
					params.add(DMSObjectType.FOLDER);
					sql.append(TM_PROPERTY.getColumn(DMSPropertyMeta.ATR_PROPERTYCODE));
					sql.append(" = ? AND P.");
					params.add(DMSPropertyCode.PROJECTNUMBER);
					sql.append(TM_PROPERTY.getColumn(DMSPropertyMeta.ATR_PROPERTYVALUE));
					sql.append(" IS NOT NULL");
					doQuery(sql.release(), params, (resultSet) -> {
						final long rowId = resultSet.getLong(1);
						final String projectNumber = resultSet.getString(2);
						final ProjektInfo project = new ProjektInfo(rowId, projectNumber);
						projekte.add(project);
					});
				}
				{ // find "Ausführungspläne" sub-folder
					final String select; {
						final StringMaker sql = StringMaker.obtain();
						sql.append("SELECT C." + SQLFunctions.ALL_CHILDREN_ROWID + " FROM ");
						SQLFunctions.ALL_CHILDREN.appendCall(sql, this, "?");
						sql.append(" C, ");
						qualifyTableName(sql, TM_PROPERTY.getTableSpecifier());
						sql.append(" P WHERE C." + SQLFunctions.ALL_CHILDREN_ROWID + "= P.");
						sql.append(TM_PROPERTY.getColumn(DMSPropertyMeta.ATR_OBJECT_ROWID));
						sql.append(" AND P.");
						sql.append(TM_PROPERTY.getColumn(DMSPropertyMeta.ATR_PROPERTYCODE));
						sql.append(" = ? AND P.");
						sql.append(TM_PROPERTY.getColumn(DMSPropertyMeta.ATR_PROPERTYVALUE));
						sql.append(" = ?");
						select = sql.release();
					}
					final Iterator<ProjektInfo> i = projekte.iterator();
					// find "Ausführungspläne" sub-folder
					while (i.hasNext()) {
						final ProjektInfo projektInfo = i.next();
						params.clear();
						params.add(projektInfo.folderRowIdProjekt);
						params.add(DMSPropertyCode.FOLDERTYPE);
						params.add(Strings.toString(DMSFolderType.AUSFUEHRUNGSPLAENE.getKey()));
						doQuery(select, params, (resultSet) -> {
							final long rowId = resultSet.getLong(1);
							projektInfo.folderRowIdAusfuehrungsplaene = rowId;
						});
						// forget projects without pläne folder
						if (projektInfo.folderRowIdAusfuehrungsplaene == 0)
							i.remove();
					}
				}
				{ // find existing sub-folders
					final String select; {
						final StringMaker sql = StringMaker.obtain();
						sql.append("SELECT O.");
						sql.append(TM_OBJECT.getPrimaryKeyColumn());
						sql.append(", P.");
						sql.append(TM_PROPERTY.getColumn(DMSPropertyMeta.ATR_PROPERTYVALUE));
						sql.append(" FROM ");
						qualifyTableName(sql, TM_OBJECT.getTableSpecifier());
						sql.append(" O, ");
						qualifyTableName(sql, TM_PROPERTY.getTableSpecifier());
						sql.append(" P WHERE O.");
						sql.append(TM_OBJECT.getColumn(DMSObjectMeta.ATR_PARENT_ROWID));
						sql.append(" = ? AND O.");
						sql.append(TM_OBJECT.getPrimaryKeyColumn());
						sql.append(" = P.");
						sql.append(TM_PROPERTY.getColumn(DMSPropertyMeta.ATR_OBJECT_ROWID));
						sql.append(" AND P.");
						sql.append(TM_PROPERTY.getColumn(DMSPropertyMeta.ATR_PROPERTYCODE));
						sql.append(" = ? AND P.");
						sql.append(TM_PROPERTY.getColumn(DMSPropertyMeta.ATR_PROPERTYVALUE));
						sql.append(" IS NOT NULL");
						select = sql.release();
					}
					for (final ProjektInfo projektInfo : projekte) {
						params.clear();
						params.add(projektInfo.folderRowIdAusfuehrungsplaene);
						params.add(DMSPropertyCode.SUBPROJECTNUMBER);
						doQuery(select, params, (resultSet) -> {
							final long rowId = resultSet.getLong(1);
							final String subNumber = resultSet.getString(2);
							final FolderInfo folderInfo = new FolderInfo(rowId, subNumber);
							projektInfo.existingSubFolders.add(folderInfo);
						});
					}
				}
			}
		};
		final TransactionContext context = new TransactionContext();
		context.load(source);
		return projekte;
	}

	static int countExistingFolders(final java.util.Collection<ProjektInfo> projekte) {
		int result = 0;
		for (final ProjektInfo projektInfo : projekte) {
			result += projektInfo.existingSubFolders.size();
		}
		return result;
	}

	// --------------------------------------------------------------
	// ---
	// --- Creates missing folders
	// ---
	// --------------------------------------------------------------
	private ObjectMessageSet createMissingSubFolders(
			final PacketCreateProjectSubFoldersRequest request,
			final java.util.Collection<ProjektInfo> projekte) throws Exception {
		final ReplicationTemplate template = ReplicationTemplate.loadTemplate(
				server, DMSSpecialFunctionCode.AUSFUEHRUNGSPLAENE_SUBTEMPLATE);
		final ReplicationTemplateSink sink = new ReplicationTemplateSink(server, request.getUser());
		for (final ProjektInfo projektInfo : projekte) {
			for (final FolderInfo folderInfo : projektInfo.missingSubFolders) {
				final FolderVariableTemplateExpander expander =
						FolderTemplateVariables.AusfuehrungsPlaeneSubFolder.getExpander(folderInfo.subNummer);
				sink.addBatch(template, projektInfo.folderRowIdAusfuehrungsplaene, expander);
			}
		}
		return sink.executeBatch();
	}
	
}

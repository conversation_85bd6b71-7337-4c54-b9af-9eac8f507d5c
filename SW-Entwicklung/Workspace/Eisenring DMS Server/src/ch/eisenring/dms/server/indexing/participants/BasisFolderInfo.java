package ch.eisenring.dms.server.indexing.participants;

import java.io.IOException;
import java.sql.SQLException;

import ch.eisenring.app.server.model.ContextSource;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.datatypes.primitives.Primitives;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.dms.server.DMSServer;
import ch.eisenring.dms.server.dao.DMSModelMapping;
import ch.eisenring.dms.service.codetables.DMSPropertyCode;
import ch.eisenring.dms.shared.DMSConstants;
import ch.eisenring.dms.shared.model.metaold.DMSPropertyMetaModel;
import ch.eisenring.jdbc.JDBCResultSet;
import ch.eisenring.jdbc.StatementWrapper;
import ch.eisenring.model.TransactionContext;
import ch.eisenring.model.TransactionSource;

/**
 * Holds basic info about base folder to be updated.
 * 
 * This is basically a super lightweight representation of
 * DMSProperty with type DMSPropertyCode.BASISPROJEKTNUMMER.
 * 
 * (It needs to be lightweight because all are loaded into
 *  memory at the same time!).
 */
public final class BasisFolderInfo {

	private final long folderRowId;
	private final String basisprojektnummer;

	public BasisFolderInfo(final long folderRowId,
			               final String basisprojektnummer) {
		this.folderRowId = folderRowId;
		this.basisprojektnummer = basisprojektnummer;
	}
	
	// --------------------------------------------------------------
	// ---
	// --- Getters
	// ---
	// --------------------------------------------------------------
	/**
	 * Gets the rowId of the DMS folder that carries the 
	 * property "Basisprojektnummer".
	 */
	public long getFolderRowId() {
		return folderRowId;
	}

	/**
	 * Gets the value of the folders "Basisprojektnummer" property.
	 */
	public String getBasisprojektnummer() {
		return basisprojektnummer;
	}

	// --------------------------------------------------------------
	// ---
	// --- Object overrides
	// ---
	// --------------------------------------------------------------
	@Override
	public int hashCode() {
		return ((int) (folderRowId >> 32)) ^ ((int) folderRowId); 
	}

	@Override
	public boolean equals(final Object o) {
		return o instanceof BasisFolderInfo && ((BasisFolderInfo) o).folderRowId == folderRowId;
	}

	@Override
	public String toString() {
		return Strings.concat(Primitives.getSimpleName(getClass()), "(", folderRowId, ",", basisprojektnummer, ")");
	}

	// --------------------------------------------------------------
	// ---
	// --- Helper methods
	// ---
	// --------------------------------------------------------------
	/**
	 * Retrieves all BasisFolderReference from the DMS database.
	 * 
	 * This is potentially a very large collection, it will contain
	 * one object for every Basisprojekt that the DMS handles.
	 * (Note: Einzelküche counts as Basisprojekt too.
	 *  Since this object is small, about 10000 should eat up only one MB,
	 *  and the DB will contain less than one million of these records,
	 *  so peak memory consumption should be considerably less than 100MB
	 *  in the absolute worst case.
	 *  Realistically we expect less than 25000 in 5 years). 
	 */
	public static List<BasisFolderInfo> getAll(final DMSServer server) throws IOException {
		final List<BasisFolderInfo> folderList = new ArrayList<BasisFolderInfo>(2048);
		final TransactionSource source = new ContextSource(DMSConstants.DMS_DATABASE) {
			@Override
			protected void loadImpl(final TransactionContext context) throws SQLException {
				final StringMaker sql = StringMaker.obtain(256);
				sql.append("SELECT ");
				sql.append(DMSModelMapping.TM_PROPERTY.getColumn(DMSPropertyMetaModel.ATR_OBJECT_ROWID));
				sql.append(", ");
				sql.append(DMSModelMapping.TM_PROPERTY.getColumn(DMSPropertyMetaModel.ATR_PROPERTYVALUE));
				sql.append(" FROM ");
				qualifyTableName(sql, DMSModelMapping.TM_PROPERTY.getTableSpecifier());
				sql.append(" WHERE ");
				sql.append(DMSModelMapping.TM_PROPERTY.getColumn(DMSPropertyMetaModel.ATR_PROPERTYCODE));
				sql.append(" = ? AND ");
				sql.append(DMSModelMapping.TM_PROPERTY.getColumn(DMSPropertyMetaModel.ATR_PROPERTYVALUE));
				sql.append(" IS NOT NULL");
				final StatementWrapper statement = prepareStatement(sql.release());
				try {
					statement.setObject(1, DMSPropertyCode.BASEPROJECTNUMBER);
					final JDBCResultSet resultSet = statement.executeQuery();
					try {
						while (resultSet.next()) {
							final long folderRowId = resultSet.getLong(1);
							final String basisprojektnummer = resultSet.getString(2);
							final BasisFolderInfo folderRef = new BasisFolderInfo(folderRowId, basisprojektnummer);
							folderList.add(folderRef);
						}
					} finally {
						closeSilent(resultSet);
					}
				} finally {
					closeSilent(statement);
				}
			}
		};
		final TransactionContext context = new TransactionContext();
		context.load(source);
		return folderList;
	}

}

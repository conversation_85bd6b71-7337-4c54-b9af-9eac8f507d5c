package ch.eisenring.dms.server.network;

import java.sql.SQLException;
import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.dms.server.DMSServer;
import ch.eisenring.dms.service.codetables.DMSPropertyCode;
import ch.eisenring.dms.shared.network.PacketCheckProjectNumberReply;
import ch.eisenring.dms.shared.network.PacketCheckProjectNumberRequest;
import ch.eisenring.jdbc.StatementParameters;
import ch.eisenring.logiware.LWContextSource;
import ch.eisenring.model.TransactionContext;
import ch.eisenring.model.engine.jdbc.DatabaseSpecifier;
import ch.eisenring.network.PacketSink;

interface Dms {
	DatabaseSpecifier DATABASE = DatabaseSpecifier.get("DMS");
}

interface DmsProp {
	String DMSPROPERTY_TABLE = "HEAG_DMS_Produktiv.dbo.DMSProperty";
	String OBJECT_ROW_ID = "Object_rowId";
	String PROPERTY_CODE = "Propertycode";
	String PROPERTY_VALUE = "Propertyvalue";
	int CODE_BASIS = DMSPropertyCode.BASEPROJECTNUMBER.getId();
	int CODE_PROJECT = DMSPropertyCode.PROJECTNUMBER.getId();
	int CODE_SPECIAL_FUNCTION = DMSPropertyCode.SPECIALFUNCTIONCODE.getId();
}

interface DmsObj {
	String DMSOBJECT_TABLE = "HEAG_DMS_Produktiv.dbo.DMSObject";
	String ROW_ID = "rowId";
	String PARENT_ROW_ID = "Parent_rowId";
}

public class CheckProjectNumberRequestHandler extends AbstractDMSPacketHandler2<PacketCheckProjectNumberRequest> {

	public CheckProjectNumberRequestHandler(DMSServer server) {
		super(server, PacketCheckProjectNumberRequest.class);
	}

	@Override
	public void handle(PacketCheckProjectNumberRequest request, PacketSink sink) {
		try {
			PacketCheckProjectNumberReply reply = PacketCheckProjectNumberReply.create(request, ErrorMessage.OK);
			new TransactionContext().load(new LWContextSource(Dms.DATABASE) {
				@Override
				protected void loadImpl(TransactionContext context) throws SQLException {
					StringBuilder sql = new StringBuilder();
					sql.append("SELECT ").append(DmsProp.PROPERTY_VALUE);
					sql.append(" FROM ").append(DmsProp.DMSPROPERTY_TABLE);
					sql.append(" WHERE (").append(DmsProp.PROPERTY_CODE).append(" in (").append(DmsProp.CODE_BASIS).append(",").append(DmsProp.CODE_PROJECT).append(")");
					sql.append(" AND ").append(DmsProp.PROPERTY_VALUE).append("='").append(request.getProjectNumber()).append("')");
					sql.append(" OR (").append(DmsProp.PROPERTY_CODE).append(" in (").append(DmsProp.CODE_BASIS).append(",").append(DmsProp.CODE_PROJECT).append(")");
					sql.append(" AND ").append(DmsProp.PROPERTY_VALUE).append("='").append(request.getBasisProjectNumber()).append("'");
					sql.append(" AND ").append(DmsProp.OBJECT_ROW_ID).append(" not in (");
					sql.append("SELECT bo.").append(DmsObj.ROW_ID);
					sql.append(" FROM ").append(DmsObj.DMSOBJECT_TABLE).append(" bo");
					sql.append(" JOIN ").append(DmsObj.DMSOBJECT_TABLE).append(" fo ON fo.").append(DmsObj.ROW_ID).append("=bo.").append(DmsObj.PARENT_ROW_ID);
					sql.append(" JOIN ").append(DmsProp.DMSPROPERTY_TABLE).append(" fp ON fp.").append(DmsProp.OBJECT_ROW_ID).append("=fo.").append(DmsObj.ROW_ID);
					sql.append(" WHERE fp.").append(DmsProp.PROPERTY_CODE).append("=").append(DmsProp.CODE_SPECIAL_FUNCTION);
					sql.append(" AND fp.").append(DmsProp.PROPERTY_VALUE).append("=").append(request.getSpecialFunction().getId());
					sql.append("))");
					StatementParameters params = null;
					doQuery(sql.toString(), params, rst -> {
						String projectNumber = rst.getString(DmsProp.PROPERTY_VALUE);
						if (projectNumber.equals(request.getBasisProjectNumber())) {
							reply.setBasisProjectNumberExists(true);
						} else if (projectNumber.equals(request.getProjectNumber())) {
							reply.setProjectNumberExists(true);
						}
					});
				}
			});
			sink.sendPacket(reply);

		} catch (Exception e) {
			Logger.error(e);
			PacketCheckProjectNumberReply reply = PacketCheckProjectNumberReply.create(request, ErrorMessage.ERROR);
			sink.sendPacket(reply);
		}
	}

}

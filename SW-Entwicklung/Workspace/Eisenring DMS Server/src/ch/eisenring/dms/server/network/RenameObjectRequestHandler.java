package ch.eisenring.dms.server.network;

import java.sql.SQLException;
import java.util.Iterator;

import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.dms.server.DMSServer;
import ch.eisenring.dms.server.audit.AuditTrailBatch;
import ch.eisenring.dms.server.dao.DMSContextSink;
import ch.eisenring.dms.shared.codetables.DMSAuditCode;
import ch.eisenring.dms.shared.model.DAOResult;
import ch.eisenring.dms.shared.model.record.DMSObjectModel;
import ch.eisenring.dms.shared.network.PacketRenameObjectReply;
import ch.eisenring.dms.shared.network.PacketRenameObjectRequest;
import ch.eisenring.dms.shared.network.cmd.ObjectMessageConstants;
import ch.eisenring.dms.shared.network.cmd.ObjectMessageSet;
import ch.eisenring.model.Model;
import ch.eisenring.model.TransactionContext;
import ch.eisenring.network.PacketSink;
import ch.eisenring.network.packet.AbstractPacket;


public final class RenameObjectRequestHandler extends AbstractDMSPacketHandler {

	private RenameObjectRequestHandler(final DMSServer server) {
		super(server, PacketRenameObjectRequest.class);
	}

	@Override
	public void handle(final AbstractPacket abstractPacket, final PacketSink sink) {
		final PacketRenameObjectRequest packet = (PacketRenameObjectRequest) abstractPacket;
		// rename & reload object
		final ObjectMessageSet changeMessages = new ObjectMessageSet();
		final DMSContextSink contextSink = new DMSContextSink(server) {
			@Override
			public void storeImpl(final Iterator<Model> modelItr, final TransactionContext context) throws SQLException {
				final Long objectRowId = packet.getRowId();
				long rowCount;
				
				// load the existing object
				DMSObjectModel object = loadObject(context, objectRowId, true, false);
				final String valueOld = object.getObjectname();
				
				// check if there is already an object with the target name
				if (!checkObjectname(packet.getObjectname(), object.getParentRowId(), object.getPKValue())) {
					throw new SQLException(Strings.concat("Es existiert bereits ein Objekt mit dem Namen \"", packet.getObjectname(), "\""));
				}
				
				rowCount = update(
						"UPDATE ${DMSObject} SET ${Objectname} = ? WHERE ${PK} = ?",
						DMSObjectModel.METACLASS,
						packet.getObjectname(), objectRowId);
				if (rowCount != 1) {
					throw new SQLException(Strings.concat("UPDATE returned unexpected affected row count: ", rowCount, " (expected: 1)"));
				}
				object = loadObject(context, objectRowId, true, true);
				changeMessages.addFlags(object.getPKValue(), ObjectMessageConstants.ATTRIBUTES);
				changeMessages.addFlags(object.getParentRowId(), ObjectMessageConstants.CHILDREN);
				final String valueNew = object.getObjectname();
				final AuditTrailBatch auditBatch = new AuditTrailBatch();
				auditBatch.add(packet.getRowId(), null,
						DMSAuditCode.RENAME, packet.getUser(),
						valueOld, valueNew);
				auditBatch.insertBatch(this);
			}
		};
		final TransactionContext context = new TransactionContext();
		try {
			context.store(contextSink);
			server.searchIndexUpdater.invalidate();
			final PacketRenameObjectReply reply = PacketRenameObjectReply.create(packet, new DAOResult(context));
			sink.sendPacket(reply, false);
			broadcastMessages(changeMessages);
		} catch (final Exception e) {
			final PacketRenameObjectReply reply = PacketRenameObjectReply.create(packet, new DAOResult(e));
			sink.sendPacket(reply, false);
		}
	}

}

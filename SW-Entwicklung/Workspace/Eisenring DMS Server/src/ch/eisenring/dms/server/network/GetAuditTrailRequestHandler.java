package ch.eisenring.dms.server.network;

import static ch.eisenring.dms.server.dao.DMSModelMapping.TM_AUDIT;

import java.io.IOException;
import java.sql.SQLException;

import ch.eisenring.app.server.model.ContextSource;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.dms.server.DMSServer;
import ch.eisenring.dms.shared.DMSConstants;
import ch.eisenring.dms.shared.model.DAOResult;
import ch.eisenring.dms.shared.model.metaold.DMSAuditTrailMetaModel;
import ch.eisenring.dms.shared.network.PacketGetAuditTrailReply;
import ch.eisenring.dms.shared.network.PacketGetAuditTrailRequest;
import ch.eisenring.jdbc.JDBCResultSet;
import ch.eisenring.jdbc.RowHandler;
import ch.eisenring.jdbc.StatementParameters;
import ch.eisenring.model.TransactionContext;
import ch.eisenring.model.TransactionSource;
import ch.eisenring.model.engine.jdbc.TableMapping;
import ch.eisenring.network.PacketSink;
import ch.eisenring.network.packet.AbstractPacket;

public final class GetAuditTrailRequestHandler extends AbstractDMSPacketHandler {

	private GetAuditTrailRequestHandler(final DMSServer server) {
		super(server, PacketGetAuditTrailRequest.class);
	}
	
	@Override
	public void handle(final AbstractPacket abstractPacket, final PacketSink sink) {
		final PacketGetAuditTrailRequest request = (PacketGetAuditTrailRequest) abstractPacket;
		final TransactionSource source = new ContextSource(DMSConstants.DMS_DATABASE) {
			@Override
			protected void loadImpl(final TransactionContext context) throws SQLException {
				final StringMaker sql = StringMaker.obtain(256);
				TM_AUDIT.appendSelectSQL(sql, this, TableMapping.NO_TABLE_ALIAS);
				sql.append(" WHERE ");
				sql.append(TM_AUDIT.getColumn(DMSAuditTrailMetaModel.ATR_OBJECT_ROWID));
				sql.append(" = ?");
				final StatementParameters params = new StatementParameters();
				params.add(request.getRowId());
				doQuery(sql.release(), params, new RowHandler() {
					@Override
					public void handleRow(final JDBCResultSet resultSet) throws SQLException {
						// Model m = 
						TM_AUDIT.loadRow(context, resultSet);
					}
				});
			}
		};
		DAOResult result;
		try {
			final TransactionContext context = new TransactionContext();
			context.load(source);
			result = new DAOResult(context);
		} catch (final IOException e) {
			result = new DAOResult(e);
		}		
		final PacketGetAuditTrailReply reply = PacketGetAuditTrailReply.create(request, result);
		sink.sendPacket(reply, false);
	}

}

package ch.eisenring.dms.server.network;

import static ch.eisenring.lw.LWMapping.TM_AUFTRAG;
import static ch.eisenring.lw.LWMapping.TM_PROJEKT;

import java.sql.SQLException;

import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.collections.Set;
import ch.eisenring.core.collections.impl.ArraySet;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.dms.server.DMSServer;
import ch.eisenring.dms.shared.network.PacketFindProjectAWAReply;
import ch.eisenring.dms.shared.network.PacketFindProjectAWARequest;
import ch.eisenring.jdbc.JDBCResultSet;
import ch.eisenring.jdbc.RowHandler;
import ch.eisenring.jdbc.StatementParameters;
import ch.eisenring.logiware.LWContextSource;
import ch.eisenring.lw.LWConstants;
import ch.eisenring.lw.meta.LWAuftragMeta;
import ch.eisenring.lw.meta.LWProjektMeta;
import ch.eisenring.model.TransactionContext;
import ch.eisenring.network.PacketSink;
import ch.eisenring.network.packet.AbstractPacket;

class FindProjectAWARequestHandler extends AbstractDMSPacketHandler {

	public FindProjectAWARequestHandler(final DMSServer server) {
		super(server, PacketFindProjectAWARequest.class);
	}

	@Override
	public void handle(final AbstractPacket packet, final PacketSink sink) {
		final PacketFindProjectAWARequest request = (PacketFindProjectAWARequest) packet;
		final PacketFindProjectAWAReply reply = handle(server, request);
		sink.sendPacket(reply, false);
	}

	public static PacketFindProjectAWAReply handle(final DMSServer server, final PacketFindProjectAWARequest request) {
		try {
			final PacketFindProjectAWAReply reply = handleImpl(server, request);
			if (reply != null)
				return reply;
			return PacketFindProjectAWAReply.create(request, ErrorMessage.ERROR);
		} catch (final Exception e) {
			return PacketFindProjectAWAReply.create(request, new ErrorMessage(e));
		}
	}

	private static PacketFindProjectAWAReply handleImpl(final DMSServer server, final PacketFindProjectAWARequest request) throws Exception {
		final Set<String> abwicklungsArtenKeys = new ArraySet<>();
		final LWContextSource source = new LWContextSource(LWConstants.VERTRIEB) {
			@Override
			protected void loadImpl(final TransactionContext context) throws SQLException {
				final StringMaker sql = StringMaker.obtain(512);
				final StatementParameters params = new StatementParameters(1);
				params.add(request.getProjectnumber());
				sql.append("SELECT DISTINCT A.");
				sql.append(TM_AUFTRAG.getColumn(LWAuftragMeta.ATR_ABWICKLUNGSART));
				sql.append(" FROM ");
				qualifyTableName(sql, TM_PROJEKT.getTableSpecifier());
				sql.append(" P, ");
				qualifyTableName(sql,  TM_AUFTRAG.getTableSpecifier());
				sql.append(" A WHERE P.");
				sql.append(TM_PROJEKT.getColumn(LWProjektMeta.ATR_BASISPROJEKTNUMMER));
				sql.append(" = ? AND P.");
				sql.append(TM_PROJEKT.getColumn(LWProjektMeta.ATR_PROJEKTNUMMER));
				sql.append(" = A.");
				sql.append(TM_AUFTRAG.getColumn(LWAuftragMeta.ATR_PROJEKTNUMMER));
				sql.append(" AND P.");
				sql.append(TM_PROJEKT.getColumn(LWProjektMeta.ATR_GSE_PROJEKT));
				sql.append(" = A.");
				sql.append(TM_AUFTRAG.getColumn(LWAuftragMeta.ATR_GSE_PROJEKT));
				sql.append(" AND A.");
				sql.append(TM_AUFTRAG.getColumn(LWAuftragMeta.ATR_STATUSCODE));
				sql.append(" NOT IN ('099')");
				doQuery(sql.release(), params, new RowHandler() {
					@Override
					public void handleRow(final JDBCResultSet resultSet) throws SQLException {
						final String key = resultSet.getString(1);
						if (!Strings.isEmpty(key))
							abwicklungsArtenKeys.add(key);
					}
				});
				
			}
		};
		final TransactionContext context = new TransactionContext();
		context.load(source);
		return PacketFindProjectAWAReply.create(request, abwicklungsArtenKeys);
	}

}

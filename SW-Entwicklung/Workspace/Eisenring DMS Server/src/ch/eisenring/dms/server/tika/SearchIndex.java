package ch.eisenring.dms.server.tika;

import java.util.Collections;
import java.util.Comparator;

import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.collections.impl.HashSet;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.dms.shared.constants.SearchIndexConstants;

/**
 * Helper class to build a search index.
 * 
 * A search index instance can be fed all the relevant strings,
 * it will process them according to the current index creation
 * rules and store them internally.
 * After all strings have been added to the index, the canonical
 * form of the index can be retrieved with getSearchIndex().
 */
public final class SearchIndex {

	private final Comparator<String> COMPARATOR = new Comparator<String>() {
		@Override
		public int compare(final String s1, final String s2) {
			return Strings.compare(s1, s2);
		}
	};

	private final HashSet<String> wordSet = new HashSet<String>(64, 8F);

	/**
	 * Create a new, empty search index
	 */
	public SearchIndex() {
	}

	/**
	 * Create a new search index and add the supplied strings to it
	 */
	public SearchIndex(final String ... strings) {
		for (final String string : strings) {
			addString(string);
		}
	}

	/**
	 * Prepares this index for reuse. After clearing, the
	 * index is in the same state as a new index created
	 * with the default constructor is.
	 */
	public void clear() {
		wordSet.clear();
	}
	
	/**
	 * Adds a string to this search index.
	 * The string will be encoded, split into words
	 * and the words are added to the index.
	 */
	public void addString(final String string) {
		final String encoded = toSearchEncoding(string, false);
		final List<String> wordList = toWordList(encoded);
		for (final String word : wordList) {
			this.wordSet.add(word);
		}
		
	}

	/**
	 * Gets the contents of the search index as string
	 */
	public String getSearchIndex() {
		if (wordSet.isEmpty())
			return null;
		final List<String> wordList = new ArrayList<String>(wordSet);
		Collections.sort(wordList, COMPARATOR);
		final int l = wordList.size();
		final StringMaker b = StringMaker.obtain(l << 3);
		b.append(' ');
		for (int i=0; i<l; ++i) {
			final String word = wordList.get(i);
			b.append(word);
			b.append(' ');
		}
		return b.release();
	}

	@Override
	public String toString() {
		return getSearchIndex();
	}

	// --------------------------------------------------------------
	// ---
	// --- Static helpers
	// ---
	// --------------------------------------------------------------
	/**
	 * Takes a string and converts it to search encoding.
	 * 
	 * Characters not accepted will be either transformed into
	 * their base form (é -> e, ä -> ae), or if no transformation
	 * is known, replaced by spaces.
	 */
	public static String toSearchEncoding(final String string, final boolean keepWildCards) {
		if (string == null)
			return null;
		if (!keepWildCards)
			return SearchIndexConstants.SUBSTITUTOR.substitute(string);
		final int l = string.length();
		final StringMaker b = StringMaker.obtain(l);
		for (int i = 0; i < l; ++i) {
			final char x = string.charAt(i);
			if (x == '*' || x == '%') {
				b.append('%');
				continue;
			} else if (x == '?' || x == '_') {
				b.append('_');
				continue;
			}
			SearchIndexConstants.SUBSTITUTOR.appendTo(b, x);
		}
		return b.release();
	}

	/**
	 * Takes a string and extracts all words.
	 * No character conversion are applied.
	 * The words will be in the order they had in the string,
	 * and duplicates are still there.
	 */
	public static List<String> toWordList(final String string) {
		if (string == null || string.length() <= 0)
			return new ArrayList<String>();
		final int l = string.length();
		final List<String> list = new ArrayList<String>(l >> 3);
		final StringMaker b = StringMaker.obtain(64);
		int i = 0;
		while (i < l) {
			final char c = string.charAt(i++);
			if (Strings.isWhitespace(c)) {
				if (b.length() > 0) {
					list.add(b.toString());
					b.setLength(0);
				}
			} else {
				b.append(c);
			}
		}
		if (b.length() > 0)
			list.add(b.toString());
		b.releaseSilent();
		return list;
	}

}

package ch.eisenring.dms.server.network;

import static ch.eisenring.dms.server.DMSMapping.OM_BLOB;
import static ch.eisenring.dms.server.DMSMapping.TM_BLOB;

import java.sql.SQLException;
import java.util.Iterator;

import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.dms.server.DMSServer;
import ch.eisenring.dms.server.dao.DMSContextSink;
import ch.eisenring.dms.shared.model.meta.DMSBlobMeta;
import ch.eisenring.dms.shared.model.record.DMSBlob;
import ch.eisenring.dms.shared.network.PacketBlobStoreReply;
import ch.eisenring.dms.shared.network.PacketBlobStoreRequest;
import ch.eisenring.jdbc.StatementParameters;
import ch.eisenring.model.Model;
import ch.eisenring.model.TransactionContext;
import ch.eisenring.network.PacketSink;
import ch.eisenring.network.packet.AbstractPacket;

public final class BlobStoreRequestHandler extends AbstractDMSPacketHandler {

	BlobStoreRequestHandler(final DMSServer server) {
		super(server, PacketBlobStoreRequest.class);
	}

	@Override
	public void handle(final AbstractPacket packet, final PacketSink sink) {
		final PacketBlobStoreRequest request = (PacketBlobStoreRequest) packet;
		final PacketBlobStoreReply reply = handle(server, request);
		sink.sendPacket(reply, false);
	}

	public static PacketBlobStoreReply handle(final DMSServer server, final PacketBlobStoreRequest request) {
		try {
			final PacketBlobStoreReply reply = handleImpl(server, request);
			if (reply != null)
				return reply;
			return PacketBlobStoreReply.create(request, ErrorMessage.ERROR);
		} catch (final Exception e) {
			return PacketBlobStoreReply.create(request, new ErrorMessage(e));
		}
	}

	private static PacketBlobStoreReply handleImpl(final DMSServer server, final PacketBlobStoreRequest request) throws Exception {
		final DMSContextSink sink = new DMSContextSink(server) {
			@Override
			protected void storeImpl(final Iterator<Model> modelItr, final TransactionContext context) throws SQLException {
				final DMSBlob blob = request.getBlob();
				{ // try update
					final StatementParameters params = new StatementParameters();
					params.add(blob.getData());
					params.add(blob.getKeyValue());
					params.add(blob.getKeyType());
					final StringMaker sql = StringMaker.obtain(512);
					sql.append("UPDATE ");
					qualifyTableName(sql, TM_BLOB.getTableSpecifier());
					sql.append(" SET ");
					sql.append(TM_BLOB.getColumn(DMSBlobMeta.ATR_BLOBDATA));
					sql.append(" = ? WHERE ");
					sql.append(TM_BLOB.getColumn(DMSBlobMeta.ATR_BLOBKEYVALUE));
					sql.append(" = ? AND ");
					sql.append(TM_BLOB.getColumn(DMSBlobMeta.ATR_BLOBKEYTYPE));
					sql.append(" = ?");
					int rowCount = doUpdate(sql.release(), params);
					if (rowCount == 1)
						return;
				} { // try insert
					OM_BLOB.insert(this, blob);
				}
			}
		};
		final TransactionContext context = new TransactionContext();
		context.store(sink);
		return PacketBlobStoreReply.create(request, ErrorMessage.OK);
	}

}

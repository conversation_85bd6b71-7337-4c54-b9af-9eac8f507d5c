package ch.eisenring.dms.server.service;

import static ch.eisenring.dms.server.dao.DMSModelMapping.TM_BINARY;
import static ch.eisenring.dms.server.dao.DMSModelMapping.TM_OBJECT;
import static ch.eisenring.dms.server.dao.DMSModelMapping.TM_PROPERTY;

import java.io.IOException;
import java.sql.SQLException;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;

import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.Set;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.collections.impl.ArraySet;
import ch.eisenring.core.datatypes.strings.Msg;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.io.binary.BinaryHolder;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.core.util.file.FileUtil;
import ch.eisenring.dms.server.DMSServer;
import ch.eisenring.dms.server.functions.SQLFunctions;
import ch.eisenring.dms.service.DMSServiceException;
import ch.eisenring.dms.service.DMSServiceResultCode;
import ch.eisenring.dms.service.api.DMSDocumentImage;
import ch.eisenring.dms.service.api.DMSObjectIdentity;
import ch.eisenring.dms.service.codetables.DMSDocumentType;
import ch.eisenring.dms.service.codetables.DMSObjectStatus;
import ch.eisenring.dms.service.codetables.DMSObjectType;
import ch.eisenring.dms.service.codetables.DMSPropertyCode;
import ch.eisenring.dms.service.proxy.DMSDocumentHandle;
import ch.eisenring.dms.service.proxy.DMSFolderHandle;
import ch.eisenring.dms.service.proxy.DMSObjectHandle;
import ch.eisenring.dms.shared.codetables.DMSCompressionCode;
import ch.eisenring.dms.shared.codetables.DMSDocumentTypeProperties;
import ch.eisenring.dms.shared.model.metaold.DMSBinaryMetaModel;
import ch.eisenring.dms.shared.model.metaold.DMSObjectMetaModel;
import ch.eisenring.dms.shared.model.metaold.DMSPropertyMetaModel;
import ch.eisenring.jdbc.JDBCResultSet;
import ch.eisenring.jdbc.RowHandler;
import ch.eisenring.jdbc.StatementParameters;
import ch.eisenring.model.TransactionContext;
import ch.eisenring.model.TransactionSource;

abstract class DocumentFinder extends ObjectFinder {

	DocumentFinder(final DMSServer server) {
		super(server);
	}

	/**
	 * Loads the active version of the specified objects. There is no checking that the
	 * specified object id's really exist or if they are really documents.
	 */
	protected List<DMSDocumentImage> getDocumentImagesByRowId(final java.util.Collection<Long> objectRowIds) throws SQLException {
		if (objectRowIds == null || objectRowIds.isEmpty())
			return new ArrayList<DMSDocumentImage>();
		final List<DMSDocumentImage> result = new ArrayList<>();
		final StringMaker sql = StringMaker.obtain(512);
		sql.append("SELECT B.");
		sql.append(TM_BINARY.getColumn(DMSBinaryMetaModel.ATR_OBJECT_ROWID));
		sql.append(", B.");
		sql.append(TM_BINARY.getPrimaryKeyColumn());
		sql.append(", O.");
		sql.append(TM_OBJECT.getColumn(DMSObjectMetaModel.ATR_OBJECTNAME));
		sql.append(", B.");
		sql.append(TM_BINARY.getColumn(DMSBinaryMetaModel.ATR_FILEEXTENSION));
		sql.append(", B.");
		sql.append(TM_BINARY.getColumn(DMSBinaryMetaModel.ATR_BINARYDATA));
		sql.append(", B.");
		sql.append(TM_BINARY.getColumn(DMSBinaryMetaModel.ATR_MADEON));
		sql.append(", B.");
		sql.append(TM_BINARY.getColumn(DMSBinaryMetaModel.ATR_DCMCODE));
		sql.append(", O.");
		sql.append(TM_OBJECT.getColumn(DMSObjectMetaModel.ATR_STATUSCODE));
		sql.append(" FROM ");
		qualifyTableName(sql, TM_BINARY.getTableSpecifier());
		sql.append(" B JOIN ");
		qualifyTableName(sql, TM_OBJECT.getTableSpecifier());
		sql.append(" O ON (B.");
		sql.append(TM_BINARY.getColumn(DMSBinaryMetaModel.ATR_OBJECT_ROWID));
		sql.append(" = O.");
		sql.append(TM_OBJECT.getPrimaryKeyColumn());
		sql.append(") WHERE B.");
		sql.append(TM_BINARY.getPrimaryKeyColumn());
		sql.append(" IN (");
		for (int i=0; i<objectRowIds.size(); ++i) {
			if (i > 0)
				sql.append(", ");
			SQLFunctions.ACTIVE_OBJECT_VERSION.appendCall(sql, this, "?");
		}
		sql.append(")");
		final StatementParameters parameters = new StatementParameters();
		parameters.addAll(objectRowIds);
		doQuery(sql.release(), parameters, new RowHandler() {
			@Override
			public void handleRow(final JDBCResultSet resultSet) throws SQLException {
				final long objectRowId = resultSet.getLong(1);
				final long versionRowId = resultSet.getLong(2);
				final String objectName = resultSet.getString(3);
				final String extension = resultSet.getString(4);
				final BinaryHolder rawData = resultSet.getBinary(5);
				final long lastModified = resultSet.getLongTimestamp(6);
				final DMSCompressionCode compressionCode = resultSet.getCode(7, DMSCompressionCode.class);
				final DMSObjectStatus status = resultSet.getCode(8, DMSObjectStatus.class);
				final BinaryHolder fileData;
				try {
					fileData = compressionCode.decompress(rawData);
				} catch (final IOException e) {
					throw new SQLException(e.getMessage(), e);
				}
				final DMSDocumentImage document = new DMSDocumentImage(objectRowId, versionRowId,
						FileUtil.addFileExtension(objectName, extension), fileData, lastModified, status);
				result.add(document);
			}
		});
		return result;
	}

	protected List<DMSDocumentImage> getDocumentImages(final java.util.Collection<DMSDocumentHandle> handles) throws SQLException {
		if (handles == null || handles.isEmpty())
			return new ArrayList<DMSDocumentImage>();
		final List<Long> documentRowIds = new ArrayList<>(handles.size());
		for (final DMSDocumentHandle handle : handles) {
			documentRowIds.add(handle.getPKValue());
		}
		return getDocumentImagesByRowId(documentRowIds);
	}

	public static List<DMSDocumentImage> getDocumentImages(final DMSServer server, final java.util.Collection<? extends DMSObjectIdentity> handles) throws DMSServiceException {
		if (handles.isEmpty())
			return new ArrayList<DMSDocumentImage>();
		final List<DMSDocumentImage> result = new ArrayList<>(handles.size());
		final TransactionSource source = new DocumentFinder(server) {
			@Override
			protected void loadImpl(final TransactionContext context) throws SQLException {
				if (handles.isEmpty())
					return;
				final List<Long> documentRowIds = new ArrayList<>(handles.size());
				for (final DMSObjectIdentity documentId : handles) {
					documentRowIds.add(documentId.getPKValue());
				}
				result.addAll(getDocumentImagesByRowId(documentRowIds));
			}
		};
		try {
			final TransactionContext context = new TransactionContext();
			context.load(source);
			result.trimToSize();
		} catch (final IOException e) {
			final String message = Msg.mk("Fehler: {}", e.getMessage());
			throw new DMSServiceException(DMSServiceResultCode.GENERIC_ERROR, message);
		}
		return result;
	}

	public static List<DMSDocumentImage> findDocuments(
			final DMSServer server,
			final String projectnumber,
			final DMSDocumentType documentType) throws DMSServiceException {
		final AtomicReference<List<DMSDocumentImage>> result = new AtomicReference<>();
		final TransactionSource source = new DocumentFinder(server) {
			@Override
			protected void loadImpl(final TransactionContext context) throws SQLException {
				// locate the root folder
				final DMSFolderHandle rootFolder = getRootFolderForType(projectnumber, documentType);
				if (rootFolder == null)
					return;
				// step 1: locate the documents to be loaded
				// TODO : rewrite to not rely on tru_typeclass 
				final StringMaker sql = StringMaker.obtain(128);
				sql.append("SELECT C.rowId FROM ");
				SQLFunctions.ALL_CHILDREN.appendCall(sql, this, "?");
				sql.append(" C WHERE C.tru_typeclass = ? AND C.Typecode = ? AND C.Statuscode < ?");
				final StatementParameters params = new StatementParameters(4);
				params.add(rootFolder.getPKValue());
				params.add(documentType);
				params.add(DMSObjectType.FILE);
				params.add(DMSObjectStatus.RETRACTED);
				final Set<Long> documentRowIds = new ArraySet<>();
				doQuery(sql.release(), params, new RowHandler() {
					@Override
					public void handleRow(final JDBCResultSet resultSet) throws SQLException {
						final Long documentRowId = resultSet.getLong(1);
						documentRowIds.add(documentRowId);
					}
				});
				// step 2: load the documents
				result.set(getDocumentImagesByRowId(documentRowIds));
			}
		};
		try {
			final TransactionContext context = new TransactionContext();
			context.load(source);
		} catch (final IOException e) {
			Logger.error(e);
			final String message = Msg.mk("Fehler: {}", e.getMessage());
			throw new DMSServiceException(DMSServiceResultCode.GENERIC_ERROR, message);
		}
		return result.get() == null ? new ArrayList<DMSDocumentImage>() : result.get();
	}

	/**
	 * Returns true if the given document type need to be searched starting
	 * from the base folder of the hierarchy.
	 */
	public static boolean isBaseSearchRequired(final DMSDocumentType documentType) {
		final DMSDocumentTypeProperties info = DMSDocumentTypeProperties.get(documentType); 
		return info.searchInBaseFolder();
	}

//	/**
//	 * Determines the base folder for number / document type
//	 */
//	public static Long selectRootFolder(final DMSServer server,
//			final String number, final DMSDocumentType documentType) throws DMSServiceException {
//		if (!isBaseSearchRequired(documentType)) {
//			// in case no base search is required, use the tagged folder
//			return FolderFinder.getFolderRowIdByNumber(server, number, DMSPropertyCode.PROJECTNUMBER);
//		}
//		// check if it is already the base number
//		final AtomicReference<Long> result = new AtomicReference<Long>();
//		try {
//			result.set(FolderFinder.getFolderRowIdByNumber(server, number, DMSPropertyCode.BASEPROJECTNUMBER));
//		} catch (final Exception e) {
//			result.set(null);
//		}
//		if (result.get() != null) {
//			// base folder found...
//			return result.get();
//		}
//		// assuming number is a project number, try determine the base folder
//		result.set(FolderFinder.getFolderRowIdByNumber(server, number, DMSPropertyCode.PROJECTNUMBER));
//		if (result.get() == null) {
//			// no way to locate the folder...
//			return null;
//		}
//		final DMSContextSource source = new DMSContextSource(server) {
//			@Override
//			protected void loadImpl(final TransactionContext context) throws SQLException {
//				final StringMaker sql = StringMaker.obtain();
//				sql.append("SELECT ");
//				sql.append(TM_PROPERTY.getColumn(DMSPropertyMetaClass.ATR_OBJECT_ROWID));
//				sql.append(" FROM ");
//				SQLFunctions.ALL_PROPERTIES.appendCall(sql, this, "?");
//				sql.append(" WHERE ");
//				sql.append(TM_PROPERTY.getColumn(DMSPropertyMetaClass.ATR_PROPERTYCODE));
//				sql.append(" = ?");
//				final List<Object> params = new ArrayList<>(2);
//				params.add(result.get());
//				params.add(DMSPropertyCode.BASEPROJECTNUMBER);
//				doQuery(sql.release(), params, new RowHandler() {
//					@Override
//					public void handleRow(final JDBCResultSet resultSet) throws SQLException {
//						result.set(resultSet.getLong(1));
//					}
//				});
//			}
//		};
//		try {
//			final DMSContext context = new DMSContext();
//			context.load(source);
//		} catch (final IOException e) {
//			return null;
//		}
//		return result.get();
//	}

	/**
	 * Loads all documents with the specified type from the specified folder
	 * (this will not descend into subfolders, and also retracted documents will not be part of the result).
	 */
	@SuppressWarnings("unchecked")
	static List<DMSDocumentImage> getDocuments(final DMSServer server, final DMSFolderHandle parentFolder,
			final DMSDocumentType documentType) throws DMSServiceException {
		final AtomicReference<List<DMSDocumentImage>> result = new AtomicReference<>();
		final Set<Long> documentRowIds = new ArraySet<>();
		final TransactionSource source = new DocumentFinder(server) {
			@Override
			protected void loadImpl(final TransactionContext context) throws SQLException {
				final StatementParameters params = new StatementParameters();
				final StringMaker sql = StringMaker.obtain(512);
				sql.append("SELECT O.");
				sql.append(TM_OBJECT.getPrimaryKeyColumn());
				sql.append(" FROM ");
				qualifyTableName(sql, TM_OBJECT.getTableSpecifier());
				sql.append(" O");
				if (!AbstractCode.isNull(documentType)) {
					sql.append(", ");
					qualifyTableName(sql, TM_PROPERTY.getTableSpecifier());
					sql.append(" P");
				}
				sql.append(" WHERE O.");
				sql.append(TM_OBJECT.getColumn(DMSObjectMetaModel.ATR_PARENT_ROWID));
				sql.append(" = ? AND O.");
				sql.append(TM_OBJECT.getColumn(DMSObjectMetaModel.ATR_TYPECODE));
				sql.append(" = ? AND O.");
				sql.append(TM_OBJECT.getColumn(DMSObjectMetaModel.ATR_STATUSCODE));
				sql.append(" <= ?");
				params.add(parentFolder.getPKValue());
				params.add(DMSObjectType.FILE);
				params.add(DMSObjectStatus.ARCHIVED);

				if (!AbstractCode.isNull(documentType)) {
					sql.append(" AND O.");
					sql.append(TM_OBJECT.getPrimaryKeyColumn());
					sql.append(" = P.");
					sql.append(TM_PROPERTY.getColumn(DMSPropertyMetaModel.ATR_OBJECT_ROWID));
					sql.append(" AND P.");
					sql.append(TM_PROPERTY.getColumn(DMSPropertyMetaModel.ATR_PROPERTYCODE));
					sql.append(" = ? AND P.");
					sql.append(TM_PROPERTY.getColumn(DMSPropertyMetaModel.ATR_PROPERTYVALUE));
					sql.append(" = ?");
					params.add(DMSPropertyCode.DOCTYPE);
					params.add(toStringPropertyvalue(documentType));
				}

				doQuery(sql.release(), params, new RowHandler() {
					@Override
					public void handleRow(final JDBCResultSet resultSet) throws SQLException {
						final Long documentRowId = resultSet.getLong(1);
						documentRowIds.add(documentRowId);
					}
				});
				
				result.set(getDocumentImagesByRowId(documentRowIds));
			}
		};
		// perform the load
		try {
			final TransactionContext context = new TransactionContext();
			context.load(source);
		} catch (final IOException e) {
			final String message = Msg.mk("Fehler: {}", e.getMessage());
			throw new DMSServiceException(DMSServiceResultCode.GENERIC_ERROR, message);
		}
		return result.get();
	}
	
	/**
	 * Gets the root folder for document type
	 * 
	 * Returns either the base folder, project folder or NULL if not exists/found.
	 */
	protected DMSFolderHandle getRootFolderForType(final String projectnumber, final DMSDocumentType documentType) throws SQLException {
		final String significant;
		try {
			significant = getSignificantProjectnumber(projectnumber);
		} catch (final DMSServiceException e) {
			throw new SQLException(e.getMessage());
		}
		final boolean baseSearch = isBaseSearchRequired(documentType); 

		if (!baseSearch) {
			// in case no base search is required, use the tagged folder
			final List<? extends DMSObjectHandle> list = getChildrenByProperty((DMSFolderHandle) null, DMSObjectType.FOLDER,
					DMSObjectStatus.ARCHIVED, DMSPropertyCode.PROJECTNUMBER, significant);
			switch (list.size()) {
				case 1:
					return (DMSFolderHandle) list.get(0);
				default:
					return null;
			}
		}
		
		if (baseSearch) {
			// check if it is already the base number, if yes return the folder
			final List<? extends DMSObjectHandle> list = getChildrenByProperty((DMSFolderHandle) null, DMSObjectType.FOLDER,
					DMSObjectStatus.ARCHIVED, DMSPropertyCode.BASEPROJECTNUMBER, significant);
			switch (list.size()) {
				case 1:
					return (DMSFolderHandle) list.get(0); 
			}
		}
		
		if (baseSearch) {
			// presuming number is a project number, find the base project folder
			final List<? extends DMSObjectHandle> list = getChildrenByProperty((DMSFolderHandle) null, DMSObjectType.FOLDER,
					DMSObjectStatus.ARCHIVED, DMSPropertyCode.PROJECTNUMBER, significant);
			final DMSFolderHandle projectFolder;
			switch (list.size()) {
				case 1:
					projectFolder = (DMSFolderHandle) list.get(0);
					break;
				default:
					return null;
			}
			
			// using the project folder, locate basis
			final StringMaker sql = StringMaker.obtain();
			final AtomicReference<Long> baseRowId = new AtomicReference<>();
			sql.append("SELECT ");
			sql.append(TM_PROPERTY.getColumn(DMSPropertyMetaModel.ATR_OBJECT_ROWID));
			sql.append(" FROM ");
			SQLFunctions.ALL_PROPERTIES.appendCall(sql, this, "?");
			sql.append(" WHERE ");
			sql.append(TM_PROPERTY.getColumn(DMSPropertyMetaModel.ATR_PROPERTYCODE));
			sql.append(" = ?");
			final StatementParameters params = new StatementParameters(2);
			params.add(projectFolder.getPKValue());
			params.add(DMSPropertyCode.BASEPROJECTNUMBER);
			doQuery(sql.release(), params, new RowHandler() {
				@Override
				public void handleRow(final JDBCResultSet resultSet) throws SQLException {
					baseRowId.set(resultSet.getLong(1));
				}
			});

			if (baseRowId.get() != null) {
				final ArrayList<Long> rowIds = new ArrayList<>(1);
				rowIds.add(baseRowId.get());
				final List<? extends DMSObjectHandle> baseList = getObjects(rowIds);
				if (baseList.size() == 1)
					return (DMSFolderHandle) baseList.get(0);
			}
		}

		return null;
	}

//	/**
//	 * Gets the possible root folders for document type
//	 * 
//	 * Folders are ordered as defined in the type properties. 
//	 */
//	protected List<DMSFolderHandle> getRootFoldersForType(
//			final String projectnumber, 
//			final DMSDocumentType documentType) throws SQLException {
//		final DMSDocumentTypeProperties typeProperties = DMSDocumentTypeProperties.get(documentType); 
//		final String significant;
//		try {
//			significant = getSignificantProjectnumber(projectnumber);
//		} catch (final DMSServiceException e) {
//			throw new SQLException(e.getMessage());
//		}
//	
//		final DMSFolderType[] folderTypes = typeProperties.getFolderTypes();
//		for (final DMSFolderType folderType : folderTypes) {
//			//if (folderType.)
//			
//		}
//		
//		final boolean baseSearch = isBaseSearchRequired(documentType); 
//
//		if (!baseSearch) {
//			// in case no base search is required, use the tagged folder
//			final List<? extends DMSObjectHandle> list = getChildrenByProperty((DMSFolderHandle) null, DMSObjectType.FOLDER,
//					DMSObjectStatus.ARCHIVED, DMSPropertyCode.PROJECTNUMBER, significant);
//			switch (list.size()) {
//				case 1:
//					return (DMSFolderHandle) list.get(0);
//				default:
//					return null;
//			}
//		}
//		
//		if (baseSearch) {
//			// check if it is already the base number, if yes return the folder
//			final List<? extends DMSObjectHandle> list = getChildrenByProperty((DMSFolderHandle) null, DMSObjectType.FOLDER,
//					DMSObjectStatus.ARCHIVED, DMSPropertyCode.BASEPROJECTNUMBER, significant);
//			switch (list.size()) {
//				case 1:
//					return (DMSFolderHandle) list.get(0); 
//			}
//		}
//		
//		if (baseSearch) {
//			// presuming number is a project number, find the base project folder
//			final List<? extends DMSObjectHandle> list = getChildrenByProperty((DMSFolderHandle) null, DMSObjectType.FOLDER,
//					DMSObjectStatus.ARCHIVED, DMSPropertyCode.PROJECTNUMBER, significant);
//			final DMSFolderHandle projectFolder;
//			switch (list.size()) {
//				case 1:
//					projectFolder = (DMSFolderHandle) list.get(0);
//					break;
//				default:
//					return null;
//			}
//			
//			// using the project folder, locate basis
//			final StringMaker sql = StringMaker.obtain();
//			final AtomicReference<Long> baseRowId = new AtomicReference<>();
//			sql.append("SELECT ");
//			sql.append(TM_PROPERTY.getColumn(DMSPropertyMetaClass.ATR_OBJECT_ROWID));
//			sql.append(" FROM ");
//			SQLFunctions.ALL_PROPERTIES.appendCall(sql, this, "?");
//			sql.append(" WHERE ");
//			sql.append(TM_PROPERTY.getColumn(DMSPropertyMetaClass.ATR_PROPERTYCODE));
//			sql.append(" = ?");
//			final StatementParameters params = new StatementParameters(2);
//			params.add(projectFolder.getRowId());
//			params.add(DMSPropertyCode.BASEPROJECTNUMBER);
//			doQuery(sql.release(), params, new RowHandler() {
//				@Override
//				public void handleRow(final JDBCResultSet resultSet) throws SQLException {
//					baseRowId.set(resultSet.getLong(1));
//				}
//			});
//
//			if (baseRowId.get() != null) {
//				final ArrayList<Long> rowIds = new ArrayList<>(1);
//				rowIds.add(baseRowId.get());
//				final List<? extends DMSObjectHandle> baseList = getObjects(rowIds);
//				if (baseList.size() == 1)
//					return (DMSFolderHandle) baseList.get(0);
//			}
//		}
//
//		return null;
//	}

	/**
	 * Counts the number of documents of type for project
	 */
	public static int countDocuments(
			final DMSServer server,
			final String projectnumber,
			final DMSDocumentType documentType) throws DMSServiceException {
		final AtomicInteger result = new AtomicInteger();
		final TransactionSource source = new DocumentFinder(server) {
			@Override
			protected void loadImpl(final TransactionContext context) throws SQLException {
				// count the documents
				final DMSFolderHandle rootFolder = getRootFolderForType(projectnumber, documentType);
				if (rootFolder == null)
					return;
				final StatementParameters params = new StatementParameters(4);
				params.add(rootFolder.getPKValue());
				params.add(documentType);
				params.add(DMSObjectType.FILE);
				params.add(DMSObjectStatus.ARCHIVED);
				final StringMaker sql = StringMaker.obtain(512);
				sql.append("SELECT COUNT(*) FROM ");
				SQLFunctions.ALL_CHILDREN.appendCall(sql, this, "?");
				sql.append(" C WHERE C.tru_typeclass = ? AND C.Typecode = ? AND C.Statuscode <= ?");
				doQuery(sql.release(), params, new RowHandler() {
					@Override
					public void handleRow(final JDBCResultSet resultSet) throws SQLException {
						final int count = resultSet.getInt(1);
						result.set(count);
					}
				});
			}
		};
		try {
			final TransactionContext context = new TransactionContext();
			context.load(source);
		} catch (final IOException e) {
			Logger.error(e);
			result.set(0);
		}
		return result.get();
	}

}

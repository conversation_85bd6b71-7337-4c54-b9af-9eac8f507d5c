package ch.eisenring.dms.server.service;

import ch.eisenring.core.collections.List;
import ch.eisenring.core.datatypes.strings.Msg;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.dms.server.DMSServer;
import ch.eisenring.dms.service.DMSService;
import ch.eisenring.dms.service.DMSServiceException;
import ch.eisenring.dms.service.DMSServiceResultCode;
import ch.eisenring.dms.service.api.DMSDocumentImage;
import ch.eisenring.dms.service.codetables.DMSDocumentType;
import ch.eisenring.dms.service.codetables.DMSFolderType;
import ch.eisenring.dms.service.codetables.DMSPropertyCode;
import ch.eisenring.dms.service.proxy.DMSFolderHandle;
import ch.eisenring.dms.service.util.LogiwareUtil;

public class Ausfuehrungsplaene {

	final DMSService service;
	final DMSServer server;
	
	public Ausfuehrungsplaene(final DMSService service, final DMSServer server) {
		this.server = server;
		this.service = service;
	}

	public List<DMSDocumentImage> getPlaene(final String rawProjectNumber) throws DMSServiceException {
		// determine project number (significant part)
		final String significantNumber;
		try {
			significantNumber = LogiwareUtil.getSigificantProjektnummer(rawProjectNumber);
		} catch (final Exception e) {
			final String message = Msg.mk("Ungültige Projektnummer: {}", rawProjectNumber);
			throw new DMSServiceException(DMSServiceResultCode.INVALID_PARAMETER, message);
		}
		// determine project number (extension part, may be NULL)
		final String extensionNumber;
		try {
			extensionNumber = LogiwareUtil.getExtensionNumber(rawProjectNumber);
		} catch (final Exception e) {
			final String message = Msg.mk("Ungültige Projektnummer-Erweiterung in Projektnummer: {}", rawProjectNumber);
			throw new DMSServiceException(DMSServiceResultCode.INVALID_PARAMETER, message);
		}

		final DMSFolderHandle projectFolder = service.getProjectFolder(significantNumber);
		// locate the folder "Ausführungspläne" for the project
		final DMSFolderHandle ausfuehrungsPlaeneFolder = service.getFolderByProperty(projectFolder,
				DMSPropertyCode.FOLDERTYPE.getId(), ObjectFinder.toStringPropertyvalue(DMSFolderType.AUSFUEHRUNGSPLAENE));
		// select the proper folder where to look for documents
		final DMSFolderHandle documentFolder;
		if (Strings.isEmpty(extensionNumber)) {
			documentFolder = ausfuehrungsPlaeneFolder;
		} else {
			documentFolder = service.getFolderByProperty(ausfuehrungsPlaeneFolder,
					DMSPropertyCode.SUBPROJECTNUMBER.getId(), extensionNumber);
		}

		return service.getDocumentImages(documentFolder, DMSDocumentType.NULL);
	}

}

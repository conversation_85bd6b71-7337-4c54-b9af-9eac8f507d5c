package ch.eisenring.dms.server.filewatch;

import java.io.File;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;

import ch.eisenring.app.server.filewatch.AbstractDirWatcher;
import ch.eisenring.app.server.filewatch.GenericFileWatchEntry;
import ch.eisenring.app.server.services.ErrorReportingService;
import ch.eisenring.app.shared.Configurable;
import ch.eisenring.app.shared.ServiceNotFoundException;
import ch.eisenring.core.application.Configuration;
import ch.eisenring.core.application.ConfigurationException;
import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.collections.Collection;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.Map;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.datatypes.strings.Msg;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.io.binary.BinaryHolder;
import ch.eisenring.core.io.binary.BinaryHolderUtil;
import ch.eisenring.core.io.file.FileItem;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.core.template.VariableResolver;
import ch.eisenring.core.util.file.FileImage;
import ch.eisenring.core.util.file.FileUtil;
import ch.eisenring.dms.server.DMSServer;
import ch.eisenring.dms.service.DMSService;
import ch.eisenring.dms.service.codetables.DMSDocumentType;
import ch.eisenring.dms.service.proxy.DMSDocumentHandle;
import ch.eisenring.dms.service.proxy.DMSFolderHandle;
import ch.eisenring.dms.shared.DMSDocumentVariables;
import ch.eisenring.dms.shared.codetables.DMSDocumentTypeProperties;
import ch.eisenring.dms.shared.util.KWVariableResolver;
import ch.eisenring.logiware.code.hard.LWFormularArt;
import ch.eisenring.logiware.code.pseudo.AbwicklungsartCode;
import ch.eisenring.logiware.code.service.LogiwareService;
import ch.eisenring.lw.condition.Factory;
import ch.eisenring.lw.meta.LWFormularMeta;
import ch.eisenring.lw.model.navigable.LWAuftrag;
import ch.eisenring.lw.model.navigable.LWFormular;
import ch.eisenring.lw.model.navigable.LWObjectCache;

public final class LogiwareDocumentWatcher 
	extends AbstractDirWatcher<DMSServer, GenericFileWatchEntry> implements Configurable {

	// --------------------------------------------------------------
	// ---
	// --- Mapping AWA to DMSDocumenType
	// ---
	// --------------------------------------------------------------
	private final static Map<AbwicklungsartCode, DMSDocumentType> AWA2DOCTYPE = Map.of(
			AbwicklungsartCode.AWA115, DMSDocumentType.MONTAGESCHEIN_115,
			AbwicklungsartCode.AWA118, DMSDocumentType.MONTAGESCHEIN_118,
			AbwicklungsartCode.AWA132, DMSDocumentType.MONTAGESCHEIN_132,
			AbwicklungsartCode.AWA133, DMSDocumentType.MONTAGESCHEIN_133,
			AbwicklungsartCode.AWA134, DMSDocumentType.MONTAGESCHEIN_134,
			AbwicklungsartCode.AWA135, DMSDocumentType.MONTAGESCHEIN_135,
			AbwicklungsartCode.AWA137, DMSDocumentType.MONTAGESCHEIN_137
		);
//	static {
//		final Map<AbwicklungsartCode, DMSDocumentType> m = new HashMap<>();
//		m.put(AbwicklungsartCode.AWA115, DMSDocumentType.MONTAGESCHEIN_115);
//		m.put(AbwicklungsartCode.AWA118, DMSDocumentType.MONTAGESCHEIN_118);
//		m.put(AbwicklungsartCode.AWA132, DMSDocumentType.MONTAGESCHEIN_132);
//		m.put(AbwicklungsartCode.AWA133, DMSDocumentType.MONTAGESCHEIN_133);
//		m.put(AbwicklungsartCode.AWA134, DMSDocumentType.MONTAGESCHEIN_134);
//		m.put(AbwicklungsartCode.AWA135, DMSDocumentType.MONTAGESCHEIN_135);
//		m.put(AbwicklungsartCode.AWA137, DMSDocumentType.MONTAGESCHEIN_137);
//		m.trimToSize();
//		AWA2DOCTYPE = m;
//	}
	
	private final static String CONFBASE = "DMSLogiwareDocumentImport:";
	private final static String DEFAULT_USER = "Logiware";

	private final AtomicBoolean deleteAfterImport = new AtomicBoolean();
	private final AtomicInteger scanInterval = new AtomicInteger();
	private final AtomicReference<String> errorMailAddress = new AtomicReference<>();
	private final AtomicReference<String> watchDir = new AtomicReference<>();

	public LogiwareDocumentWatcher(final DMSServer server) {
		super(server);
	}

	// --------------------------------------------------------------
	// ---
	// --- ServerJob implementation
	// ---
	// --------------------------------------------------------------
	@Override
	protected long getTickDelay() {
		return scanInterval.get() & 0xFFFFFFFFL;
	}

	// --------------------------------------------------------------
	// ---
	// --- DirWatcher implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public boolean isEnabled() {
		return enabled.get() && !Strings.isEmpty(watchDir.get());
	}

	@Override
	protected File getWatchRoot() {
		if (!isEnabled())
			return null;
		final String watchRoot = this.watchDir.get();
		return Strings.isEmpty(watchRoot) ? null : new File(watchRoot);
	}
	
	@Override
	protected boolean accepts(final File file) {
		// accept anything
		return true;
	}

	@Override
	protected GenericFileWatchEntry createEntry(final String absolutePath) {
		return new GenericFileWatchEntry(absolutePath);
	}

	@Override
	protected void processEntry(final GenericFileWatchEntry entry) {
		final List<ErrorMessage> errors = new ArrayList<ErrorMessage>();
		try {
			Logger.info(String.format("LogiwareDocumentWatcher: Execute file '%s'", entry.getFile()));
			processEntryImpl2(entry);
		} catch (final Exception e) {
			// can't process
			errors.add(new ErrorMessage(e));
			Logger.error(e);
		}
		if (!errors.isEmpty()) {
			// send problem notification mail
			sendErrorMail(entry, errors);
		}

		if (deleteAfterImport.get())
			entry.delete();
		errors.clear();
	}

//	private void processEntryImpl1(final GenericFileWatchEntry entry) throws Exception {
//		final String absolutePath = entry.getAbsolutePath();
//		final String fileExtension = FileUtil.getFileExtension(absolutePath);
//		final String fileName = FileUtil.removeFileExtension(FileUtil.getFileName(absolutePath));
//		final int formnr = Strings.parseInt(fileName);
//
//		// projektnummer aus logi feststellen
//		final AtomicReference<String> projektnummer = new AtomicReference<>();
//		final AtomicReference<String> user = new AtomicReference<>();
//		final LWContextSource source = new LWContextSource(LWConstants.VERTRIEB) {
//			@Override
//			protected void loadImpl(final TransactionContext context) throws SQLException {
//				final String sql = "SELECT PAuftrNr, add_sabe FROM FORMULAR WHERE FormNr = ? AND FormArt = '204'";
//				final StatementWrapper select = prepareStatement(sql);
//				try {
//					select.setObject(1, formnr);
//					final JDBCResultSet resultSet = select.executeQuery();
//					try {
//						if (!resultSet.next())
//							throw new SQLException(Strings.concat("Keine Montageschein Nr. ", formnr,
//									" in Logiware gefunden"));
//						projektnummer.set(resultSet.getString(1));
//						user.set(resultSet.getString(2));
//						while (resultSet.next()) {
//							final String pauftrnr2 = resultSet.getString(1);
//							if (!Strings.equals(projektnummer.get(), pauftrnr2)) {
//								throw new SQLException(Strings.concat("Montageschein Nr. ", formnr,
//										" ist nicht eindeutig einem Projekt zuzuordnen"));
//							}
//						}
//						if (resultSet.next())
//							throw new SQLException("Mehr als ein Montageschein");
//					} finally {
//						closeSilent(resultSet);
//					}
//				} finally {
//					closeSilent(select);
//				}
//			}
//		};
//		try {
//			final TransactionContext context = new TransactionContext();
//			context.load(source);
//		} catch (final IOException e) {
//			throw e;
//		}
//
//		if (Strings.isEmpty(user.get()))
//			user.set(DEFAULT_USER);
//		
//		// Projektordner im DMS lokalisieren
//		final DMSService dmsService = server.locateService(DMSService.class);
//		final DMSFolderHandle folder = dmsService.getProjectContentFolder(projektnummer.get(), DMSFolderType.PROTOKOLLE);
//		if (folder == null) {
//			final String message = Strings.concat("Kein Projektordner für Projektnummer \"", projektnummer.get(), "\" gefunden.");
//			throw new RuntimeException(message);
//		}
//
//		final VariableResolver resolver = new VariableResolver() {
//			@Override
//			public String getReplacement(final CharSequence variable) throws Exception {
//				if (Strings.equalsIgnoreCase(DMSDocumentVariables.DOCVAR_PROJECTNUMBER, variable))
//					return projektnummer.get();
//				if (Strings.equalsIgnoreCase(DMSDocumentVariables.DOCVAR_USER, variable))
//					return Strings.isEmpty(user.get()) ? DEFAULT_USER : Strings.toUpper(user.get());
//				return KWVariableResolver.INSTANCE.getReplacement(variable);
//			}
//		};
//		final DMSDocumentTypeProperties typeProperties = DMSDocumentTypeProperties.get(DMSDocumentType.MONTAGESCHEIN_135);
//		final String objectname = Strings.concat(typeProperties.getDefaultName(resolver), ".", fileExtension);
//		final FileImage fileImage = FileImage.create(objectname, entry.getFileData());
//		final DMSDocumentHandle documentHandle = dmsService.putDocument(folder, user.get(), fileImage);
//
//		// Eigenschaft "Montageschein KüMo" vergeben.
//		dmsService.setDocumentType(documentHandle, DMSDocumentType.MONTAGESCHEIN_135, user.get());
//	}

	private void processEntryImpl2(final GenericFileWatchEntry entry) throws Exception {
		final String absolutePath = entry.getAbsolutePath();
		final String fileExtension = FileUtil.getFileExtension(absolutePath);
		final String fileName = FileUtil.removeFileExtension(FileUtil.getFileName(absolutePath));

		final LWObjectCache cache; {
			final LogiwareService service = server.locateService(LogiwareService.class);
			cache = service.createObjectCache();
		}

		final int formnr = Strings.parseInt(fileName);
		final LWFormular formular;
		{ // Locate form in Logiware
			final Collection<LWFormular> formulare = cache.load(LWFormular.class, 
					Factory.eq(LWFormularMeta.ATR_FORMULARART, LWFormularArt.F204),
					Factory.eq(LWFormularMeta.ATR_FORMULARNUMMER, formnr));
			if (formulare.isEmpty()) 
				throw new IllegalStateException(Msg.mk("Kein Formular Nr. {} in Logiware!", formnr));
			if (formulare.size() > 1)
				throw new IllegalStateException(Msg.mk("Mehr als ein Formular Nr. {} in Logiware!", formnr));
			formular = formulare.iterator().next();
		}
		
		// Detect proper document type
		final AbwicklungsartCode abwicklungsArt = formular.getAuftrag().getAbwicklungsart();
		final DMSDocumentType documentType = AWA2DOCTYPE.get(abwicklungsArt);
		if (documentType == null)
			throw new IllegalStateException(Msg.mk("Formular Nr. {}, der Abwicklungsart {} ist kein Dokumenttyp zugeordnet",
					formnr, abwicklungsArt.getKey()));
		
		// Detect change user
		final String user = Strings.toUpper(
				Strings.isEmpty(formular.getAddSaBe()) ? DEFAULT_USER : formular.getAddSaBe());
		final DMSDocumentTypeProperties typeProperties = DMSDocumentTypeProperties.get(documentType);

		// projektnummer aus Logi feststellen
		final LWAuftrag auftrag = formular.getAuftrag();
		final String projektnummer = auftrag.getProjektnummer();
		
		// Projektordner im DMS lokalisieren
		final DMSService dmsService = server.locateService(DMSService.class);
		final DMSFolderHandle folder = dmsService.getProjectContentFolder(
				projektnummer, typeProperties.getProjectFolderType());
		if (folder == null) {
			final String message = Msg.mk("Kein Projektordner für Projektnummer '{}' gefunden.", projektnummer);
			throw new RuntimeException(message);
		}

		final VariableResolver resolver = new VariableResolver() {
			@Override
			public String getReplacement(final CharSequence variable) throws Exception {
				if (Strings.equalsIgnoreCase(DMSDocumentVariables.DOCVAR_BASENUMBER, variable))
					return projektnummer;
				if (Strings.equalsIgnoreCase(DMSDocumentVariables.DOCVAR_PROJECTNUMBER, variable))
					return projektnummer;
				if (Strings.equalsIgnoreCase(DMSDocumentVariables.DOCVAR_USER, variable))
					return user;
				return KWVariableResolver.INSTANCE.getReplacement(variable);
			}
		};
		final String objectname = Strings.concat(typeProperties.getDefaultName(resolver), ".", fileExtension);
		final FileImage fileImage = FileImage.create(objectname, entry.getFileData());
		final DMSDocumentHandle documentHandle = dmsService.putDocument(folder, user, fileImage);

		// Dokumenttyp im DMS setzen
		dmsService.setDocumentType(documentHandle, documentType, user);
	}

	// --------------------------------------------------------------
	// ---
	// --- Send error mail
	// ---
	// --------------------------------------------------------------
	private void sendErrorMail(final GenericFileWatchEntry entry, final List<ErrorMessage> errors) {
		final List<FileImage> files = new ArrayList<FileImage>();
		try {
			final FileItem fileItem = FileItem.create(entry.getAbsolutePath());
			final BinaryHolder binary = BinaryHolderUtil.create(fileItem, true);
			final FileImage image = FileImage.create(fileItem.getName(), binary);
			files.add(image);
		} catch (final Exception e) {
			errors.add(new ErrorMessage(Strings.concat("Dateifehler: ", e.getMessage())));
			Logger.error(e);
		}
		try {
			final ErrorReportingService service = server.locateService(ErrorReportingService.class);
			final List<String> recipients = new ArrayList<>(1);
			recipients.add(errorMailAddress.get());
			service.reportError("DMS/LW Verarbeitungsfehler", "Fehler beim Import Montageschein aus Logiware!",
					errors, files, recipients);
		} catch (final ServiceNotFoundException e) {
			Logger.fatal(e);
		}
	}

	// --------------------------------------------------------------
	// ---
	// --- Configurable implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public void getConfigurationFileNames(final Collection<String> fileNames) {
		// nothing
	}

	@Override
	public void configure(final Configuration configuration) throws ConfigurationException {
		// disable while configuring
		enabled.set(false);
		final Configuration c = configuration.subConfiguration(CONFBASE);
		
		final String watchDir = c.getString("WatchDir", null);
		final String errorMailAddress = c.getString("ErrorMailAddress", null);
		final int scanInterval = c.getInteger("ScanInterval", 1000, 86400, 16667);
		final boolean deleteAfterImport = c.getBoolean("DeleteAfterImport", false);
		final boolean enabled = c.getBoolean("Enabled", false);
	
		this.watchDir.set(watchDir);
		this.errorMailAddress.set(errorMailAddress);
		this.scanInterval.set(scanInterval);
		this.deleteAfterImport.set(deleteAfterImport);
		this.enabled.set(enabled);
	}

}

package ch.eisenring.dms.server.nsi;

import ch.eisenring.core.sort.Comparator;

/**
 * NSI ExtraActions can be added to an NSIDocument to perform actions
 * at certain points of the document processing.
 */
public abstract class NSIExtraAction {
	
	private final int actionPriority;
	private final String actionName;
	
	protected NSIExtraAction(final int actionPriority, final String actionName) {
		this.actionPriority = actionPriority;
		this.actionName = actionName;
	}

	/**
	 * Gets the priority of the action
	 */
	public final int getActionPriority() {
		return actionPriority;
	}
	
	/**
	 * Gets the name of the action
	 */
	public final String getActionName() {
		return actionName;
	}

	/**
	 * Executes the action
	 */
	public abstract void performAction(final NSIDocument document);

	
	public static class Order {
		public final static Comparator<NSIExtraAction> ActionPriority = new Comparator<NSIExtraAction>() {
			@Override
			public int compare(final NSIExtraAction a1, final NSIExtraAction a2) {
				return compareSigned(a1.getActionPriority(), a2.getActionPriority());
			}
		};
	}

	// --------------------------------------------------------------
	// ---
	// --- Object overrides
	// ---
	// --------------------------------------------------------------
	@Override
	public String toString() {
		return getActionName();
	}

}

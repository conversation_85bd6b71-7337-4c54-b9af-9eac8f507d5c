package ch.eisenring.dms.server.network;

import ch.eisenring.app.server.model.ContextSource;
import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.collections.Collection;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.Lookup;
import ch.eisenring.core.collections.Map;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.collections.impl.ArrayLookup;
import ch.eisenring.core.collections.impl.HashMap;
import ch.eisenring.core.datatypes.strings.Msg;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.dms.server.DMSMapping;
import ch.eisenring.dms.server.DMSServer;
import ch.eisenring.dms.service.codetables.DMSFolderStructureType;
import ch.eisenring.dms.service.codetables.DMSFolderType;
import ch.eisenring.dms.service.codetables.DMSPropertyCode;
import ch.eisenring.dms.service.util.LogiwareUtil;
import ch.eisenring.dms.shared.DMSConstants;
import ch.eisenring.dms.shared.logiware.DMSAbwicklungsartPrecendence;
import ch.eisenring.dms.shared.logiware.DMSLogiwareAuftrag;
import ch.eisenring.dms.shared.model.meta.DMSPropertyMeta;
import ch.eisenring.dms.shared.network.PacketProjectStructureQueryReply;
import ch.eisenring.dms.shared.network.PacketProjectStructureQueryRequest;
import ch.eisenring.jdbc.StatementParameters;
import ch.eisenring.logiware.LWProjektKey;
import ch.eisenring.logiware.code.pseudo.AbwicklungsartCode;
import ch.eisenring.logiware.code.service.LogiwareService;
import ch.eisenring.logiware.code.soft.LWBasisobjektCode;
import ch.eisenring.logiware.code.soft.LWGranit2Code;
import ch.eisenring.logiware.code.soft.LWProjektStatusCode;
import ch.eisenring.lw.condition.Factory;
import ch.eisenring.lw.meta.LWProjektMeta;
import ch.eisenring.lw.model.navigable.LWAuftrag;
import ch.eisenring.lw.model.navigable.LWAuftragKopf;
import ch.eisenring.lw.model.navigable.LWObjectCache;
import ch.eisenring.lw.model.navigable.LWProjekt;
import ch.eisenring.model.TransactionContext;
import ch.eisenring.model.TransactionSource;
import ch.eisenring.model.engine.jdbc.ColumnSpecifier;
import ch.eisenring.model.engine.jdbc.SingleTableMapping;
import ch.eisenring.network.PacketSink;
import ch.eisenring.network.packet.AbstractPacket;

import java.io.IOException;
import java.sql.SQLException;
import java.util.Collections;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;

/**
 * Looks for the structure of a project in Logiware.
 * 
 * Given a Projektnummer, it will fetch that Auftrag.
 * If no record is found, error is returned.
 * Otherwise, fetch first result record and take its "PAuftrNr_B".
 * If there are more records, fetch each and check if their PAuftrNr_B is identical.
 * If difference is found, error is returned.
 * 
 * Take AuftrNr_B as Basisprojektnummer.
 * 
 * With the PAuftrNr_B obtained, select all Auftrag that have that PAuftrNr_B and
 * AbwicklungsArt 001, 135.
 */
final class ProjectStructureQueryRequestHandler extends AbstractDMSPacketHandler {

	ProjectStructureQueryRequestHandler(final DMSServer server) {
		super(server, PacketProjectStructureQueryRequest.class);
	}

	@Override
	public PacketProjectStructureQueryReply handle(final AbstractPacket packet) {
		final PacketProjectStructureQueryRequest request = (PacketProjectStructureQueryRequest) packet;
		return handle(server, request);
	}

	@Override
	public void handle(final AbstractPacket packet, final PacketSink sink) {
		sink.sendPacket(handle(packet));
	}

	/**
	 * Statically accessible handling method
	 */
	public static PacketProjectStructureQueryReply handle(final DMSServer server, final PacketProjectStructureQueryRequest request) {
		PacketProjectStructureQueryReply reply;
		try {
			final ProjectStructureQueryRequestHandlerImpl impl =
				new ProjectStructureQueryRequestHandlerImpl(server, request);
			reply = impl.handle();
		} catch (final Exception e) {
			final ErrorMessage error = new ErrorMessage(e);
			reply = PacketProjectStructureQueryReply.create(request, error);
		}
		return reply;
	}

}

final class ProjectStructureQueryRequestHandlerImpl {

	final DMSServer server;
	final LogiwareService service;
	final PacketProjectStructureQueryRequest request;
	final PacketProjectStructureQueryReply structure;
	
	public ProjectStructureQueryRequestHandlerImpl(
			final DMSServer server,
			final PacketProjectStructureQueryRequest request) throws Exception {
		this.server = server;
		this.request = request;
		this.service = server.locateService(LogiwareService.class);
		this.structure = PacketProjectStructureQueryReply.create(request, ErrorMessage.OK);
	}

	public PacketProjectStructureQueryReply handle() throws Exception {
		final LWObjectCache cache = service.createObjectCache();

		detectStructure2(cache, structure);
		if (structure.getAuftraege().isEmpty()) {
			throw new Exception("Die Basisprojektnummer " + structure.getBasisprojektnummer() + " enthält keine Aufträge."
					+ "\nOrdner können erst erstellt werden, wenn mindestens ein konkreter Auftrag/Offerte vorhanden ist.");
		}
		detectExistingFolders(structure);
		
		// detect which folders already exist
		PacketProjectStructureQueryReply reply = structure;
//		if (reply != null && reply.isValid()) {
//			reply = detectExistingFolders(reply, request);
//		}
		if (reply != null && reply.isValid()) {
			reply = detectBasisFolderStructure(reply, request);
		}

		// if no reply was created yet, it must be a weird error
		if (reply == null) {
			reply = PacketProjectStructureQueryReply.create(request, ErrorMessage.ERROR);
		}

		return reply;
	}

	@SuppressWarnings("unchecked")
	private void detectExistingFolders(final PacketProjectStructureQueryReply structure) throws Exception {
		final TransactionSource source = new ContextSource(DMSConstants.DMS_DATABASE) {
			@Override
			protected void loadImpl(final TransactionContext context) throws SQLException {
				final List<String> projektNummern = new ArrayList<>();
				final List<DMSLogiwareAuftrag> auftraege = structure.getAuftraege();
				for (final DMSLogiwareAuftrag auftrag : auftraege) {
					final String projektnummer = auftrag.getSignificantProjektnummer();
					projektNummern.add(projektnummer);
				}
				final SingleTableMapping TM = DMSMapping.TM_PROPERTY;
				final ColumnSpecifier colPropObjRowId = TM.getColumn(DMSPropertyMeta.ATR_OBJECT_ROWID);
				final ColumnSpecifier colPropCode = TM.getColumn(DMSPropertyMeta.ATR_PROPERTYCODE);
				final ColumnSpecifier colPropVal = TM.getColumn(DMSPropertyMeta.ATR_PROPERTYVALUE);
				final StatementParameters params = new StatementParameters();
				final StringMaker sql = StringMaker.obtain();
				sql.append("SELECT ");
				sql.append(colPropObjRowId);
				sql.append(", ");
				sql.append(colPropCode);
				sql.append(", ");
				sql.append(colPropVal);
				sql.append(" FROM ");
				qualifyTableName(sql, TM.getTableSpecifier());
				sql.append(" WHERE (");
				sql.append(colPropCode);
				sql.append(" = ? AND ");
				params.add(DMSPropertyCode.PROJECTNUMBER);
				sql.append(colPropVal);
				sql.append(" IN ");
				prepareIn(sql, projektNummern);
				params.addAll(projektNummern);
				sql.append(") OR (");
				sql.append(colPropCode);
				sql.append(" = ? AND ");
				params.add(DMSPropertyCode.BASEPROJECTNUMBER);
				sql.append(colPropVal);
				sql.append(" = ?)");
				params.add(structure.getBasisprojektnummer());
				doQuery(sql.release(), params, resultSet -> {
					final Long folderRowId = resultSet.getLong(colPropObjRowId);
					final DMSPropertyCode code = resultSet.getCode(colPropCode, DMSPropertyCode.class);
					final String projektnummer = resultSet.getString(colPropVal);
					if (DMSPropertyCode.BASEPROJECTNUMBER.equals(code)) {
						if (structure.getBasisFolderRowId() != null)
							throw new SQLException(Msg.mk("Es gibt mehr als einen Ordner dem die Basisprojektnummer {} zugeordnet ist."
									+ "\nEs darf nur einen Ordner mit dieser Eigenschaft geben.", structure.getBasisprojektnummer()));
						structure.setBasisFolderRowId(folderRowId);
					} else if (DMSPropertyCode.PROJECTNUMBER.equals(code)) {
						boolean found = false;
						for (DMSLogiwareAuftrag auftrag : structure.getAuftraege()) {
							if (Strings.equals(auftrag.getSignificantProjektnummer(), projektnummer)) {
								found = true;
								if (auftrag.getFolderRowId() != null)
									throw new SQLException(Msg.mk("Es gibt mehr als einen Ordner dem die Projektnummer {} zugeordnet ist."
											+ "\nEs darf nur einen Ordner mit dieser Eigenschaft geben.", projektnummer));
								auftrag.setFolderRowId(folderRowId);
								break;
							}
						}
						if (!found)
							throw new SQLException(Msg.mk("Für den Ordner mit der Projektnummer {} konnte kein Auftrag gefunden werden.", projektnummer));
					} else {
						throw new SQLException(Msg.mk("Nicht erwarteter EIngenschafts-Code: {}", code));
					}
				});
			}
		};
		final TransactionContext context = new TransactionContext();
		context.load(source);
	}

	// --------------------------------------------------------------
	// ---
	// --- Look up all the folders in reply
	// ---
	// --------------------------------------------------------------

	// --------------------------------------------------------------
	// ---
	// --- Looks up the structure type of Basis-folder in DMS
	// ---
	// --------------------------------------------------------------
	@SuppressWarnings("unchecked")
	private PacketProjectStructureQueryReply detectBasisFolderStructure(
			final PacketProjectStructureQueryReply reply, final PacketProjectStructureQueryRequest request) {
		final Long folderRowId = reply.getBasisFolderRowId();
		if (folderRowId == null)
			return reply;
		final AtomicInteger resultCount = new AtomicInteger();
		final AtomicReference<DMSFolderStructureType> resultType = new AtomicReference<>();
		final SingleTableMapping TM = DMSMapping.TM_PROPERTY;
		final TransactionSource source = new ContextSource(TM.getTableSpecifier()) {
			@Override
			protected void loadImpl(final TransactionContext context) throws SQLException {
				final StringMaker sql = StringMaker.obtain();
				sql.append("SELECT ");
				sql.append(TM.getColumn(DMSPropertyMeta.ATR_PROPERTYVALUE));
				sql.append(" FROM ");
				qualifyTableName(sql, TM.getTableSpecifier());
				sql.append(" WHERE ");
				sql.append(TM.getColumn(DMSPropertyMeta.ATR_PROPERTYCODE));
				sql.append(" = ? AND ");
				sql.append(TM.getColumn(DMSPropertyMeta.ATR_OBJECT_ROWID));
				sql.append(" = ?");
				final StatementParameters params = new StatementParameters();
				params.add(DMSPropertyCode.FOLDERTYPE);
				params.add(folderRowId);
				doQuery(sql.release(), params, resultSet -> {
					final String rawValue = resultSet.getString(1);
					final DMSFolderType value = (DMSFolderType) DMSPropertyCode.FOLDERTYPE.getObjectValue(rawValue);
					final DMSFolderStructureType structure = DMSFolderStructureType.getByFolderType(value);
					resultType.set(structure);
					resultCount.incrementAndGet();
				});
			}
		};
		try {
			final TransactionContext context = new TransactionContext();
			context.load(source);
		} catch (final IOException e) {
			return PacketProjectStructureQueryReply.create(request, new ErrorMessage(e.getMessage()));
		}
		switch (resultCount.get()) {
			case 0:
				// There was no property found in DMS
				return reply;
			case 1:
				reply.setStructureType(resultType.get());
				return reply;
			default:
				return PacketProjectStructureQueryReply.create(request, new ErrorMessage(
						"Eigenschaft " + DMSPropertyCode.STRUCTURETYPE + " mehrfach vorhanden!"));
		}
	}

	private String buildBasisErrorMessage(final Throwable t, final String projektnummer, final String basisnummer) {
		return Msg.mk("Projektnummer {}, Basisprojektnummer ({}) ist fehlerhaft:\n{}"
			+ "\nBitte Projektbild {0} (in Logiware), Feld Basis-Komnr. korrigieren.",
				projektnummer, basisnummer, t.getMessage());
	}

	private void detectStructure2(final LWObjectCache cache,
								  final PacketProjectStructureQueryReply structure) throws Exception {
		final LWProjekt basisProjekt;
		String basisNummer = LogiwareUtil.getSigificantBasisnummer(request.getProjektnummer());

		{ // attempt to find Basisprojekt with given projektnummer
			final Collection<LWProjekt> projekte1 = cache.load(LWProjekt.class,
					Factory.ne(LWProjektMeta.ATR_STATUS, LWProjektStatusCode.STORNIERT),
					Factory.or(
							Factory.eq(LWProjektMeta.ATR_PROJEKTNUMMER, basisNummer),
							Factory.like(LWProjektMeta.ATR_PROJEKTNUMMER, Strings.concat(basisNummer, "-_%"))
					)
			);
			if (projekte1.isEmpty()) {
				throw new Exception(Msg.mk("Projektnummer {} nicht gefunden."
								+ "\n(Falsche Nummer, keine Aufträge mit eröffnungsfähiger Abwicklungsart oder Storniert?)",
						request.getProjektnummer()));
			} else {
				basisProjekt = projekte1.iterator().next();
				basisNummer = basisProjekt.getBasisnummer();
				try {
					basisNummer = LogiwareUtil.getSigificantBasisnummer(basisNummer);
				} catch (final Exception e) {
					final String message = buildBasisErrorMessage(e, basisProjekt.getProjektnummer(), basisNummer);
					throw new Exception(message);
				}
				for (final LWProjekt projekt : projekte1) {
					String basis2 = projekt.getBasisnummer();
					try {
						basis2 = LogiwareUtil.getSigificantBasisnummer(basis2);
					} catch (final Exception e) {
						final String message = buildBasisErrorMessage(e, projekt.getProjektnummer(), basis2);
						throw new Exception(message);
					}
					if (!Strings.equals(basisNummer, basis2)) {
						throw new Exception(
								Msg.mk("Projektnummer {} - Die Verknüpfung Basis/Aufträge ist nicht eindeutig:"
												+ "\n({}  verweist auf Basis {}, {}  hingegen auf {})"
												+ "\nBitte Projektbilder (in Logiware) kontrollieren und korrigieren.",
										request.getProjektnummer(), basisProjekt.getProjektnummer(), basisProjekt.getBasisnummer(),
										projekt.getProjektnummer(), projekt.getBasisnummer()));
					}
				}
			}
		}

		// finde sub-projekte
		final Collection<LWProjekt> projekte2 = cache.load(LWProjekt.class,
				Factory.and(
					Factory.ne(LWProjektMeta.ATR_STATUS, LWProjektStatusCode.STORNIERT),
					Factory.or(
						Factory.eq(LWProjektMeta.ATR_BASISPROJEKTNUMMER, basisNummer),
						Factory.like(LWProjektMeta.ATR_BASISPROJEKTNUMMER, Strings.concat(basisProjekt, "-_%"))
					)
				)
			);

		structure.setBasisprojektnummer(basisNummer);
		final Map<String, DMSLogiwareAuftrag> auftragMap = new HashMap<>();
		for (final LWProjekt projekt : projekte2) {
			final DMSLogiwareAuftrag auftrag1 = create(projekt);
			final String projektnummer = auftrag1.getSignificantProjektnummer();
			final DMSLogiwareAuftrag auftrag2 = auftragMap.get(projektnummer);
			if (auftrag2 != null) {
				if (auftrag2.getAbwicklungsArtPrecendence() <
						auftrag1.getAbwicklungsArtPrecendence()) {
					auftragMap.put(projektnummer, auftrag1);
				}
			} else {
				auftragMap.put(projektnummer, auftrag1);
			}
		}
		
		final List<DMSLogiwareAuftrag> list2 = new ArrayList<DMSLogiwareAuftrag>(auftragMap.values());
		Collections.sort(list2, DMSLogiwareAuftrag.Order.Projektnummer);

		final boolean removeBasis = list2.size() > 1;
		final boolean deselectBasis = AbstractCode.equals(basisProjekt.getBasisobjektCode(), LWBasisobjektCode.OBJEKT_BASIS_001) || AbstractCode.equals(basisProjekt.getBasisobjektCode(),LWBasisobjektCode.OBJEKT_KLEINOBJEKT_051);
		for (final DMSLogiwareAuftrag auftrag : list2) {
			final String projektNr = LogiwareUtil.getSigificantProjektnummer(auftrag.getProjektnummer());
			final boolean equalsBasis = Strings.equals(projektNr, basisNummer);
			if (equalsBasis) {
				if (deselectBasis)
					auftrag.setCreateFlag(false);
				if (removeBasis)
					continue;
			}
			structure.addAuftrag(auftrag);
		}

	}

	private final AtomicInteger ARTIFICIAL_AUFTRNR_GENERATOR = new AtomicInteger();

	public DMSLogiwareAuftrag create(final LWProjekt projekt) {
		if (projekt == null)
			return null;
		final DMSLogiwareAuftrag result = new DMSLogiwareAuftrag();
		result.setCreateFlag(true);
		result.setProjektnummer(projekt.getProjektnummer());
		result.setBasisprojektnummer(projekt.getBasisnummer());
		result.setBauherr(projekt.getProjektbezeichnung());
		result.setBasisobjektCode(AbstractCode.getId(projekt.getBasisobjektCode(), 0));
		final LWAuftrag auftrag = getHighestPrecedenceAuftrag(projekt);
		if (auftrag == null) {
			result.setAuftragnummer(ARTIFICIAL_AUFTRNR_GENERATOR.decrementAndGet());
			result.setAbwicklungsArt("-");
			result.setObjekttyp(getObjektTyp(projekt, LWGranit2Code.NULL));
		} else {
			final LWAuftragKopf kopf = auftrag.getAuftragKopf();
			result.setAuftragnummer(auftrag.getAuftragnummer());
			result.setAbwicklungsArt(Strings.toString((Object) AbstractCode.getKey(auftrag.getAbwicklungsart(), null)));
			result.setObjekttyp(getObjektTyp(projekt, kopf.getGranit2()));
		}
		return result;
	}

	static LWAuftrag getHighestPrecedenceAuftrag(final LWProjekt projekt) {
		final Collection<LWAuftrag> auftraege = projekt.getAuftraege();
		LWAuftrag bestAuftrag = null;
		int bestPrecedence = DMSAbwicklungsartPrecendence.getPrecendence(null);
		for (final LWAuftrag auftrag : auftraege) {
			if (auftrag.isStorniert())
				continue;
			final AbwicklungsartCode awa = auftrag.getAbwicklungsart();
			final int precedence = DMSAbwicklungsartPrecendence.getPrecendence(awa);
			if (precedence > bestPrecedence) {
				bestPrecedence = precedence;
				bestAuftrag = auftrag;
			}
		}
		return bestAuftrag;
	}

	// --------------------------------------------------------------
	// ---
	// --- Object Type derivation/guessing
	// ---
	// --------------------------------------------------------------
	private final static Lookup<LWGranit2Code, DMSFolderStructureType> LOOKUP;
	static {
		final Lookup<LWGranit2Code, DMSFolderStructureType> l = new ArrayLookup<>(4);
		l.put(LWGranit2Code.EIGENTUM, DMSFolderStructureType.EIGENTUM);
		l.put(LWGranit2Code.EINZEL_B, DMSFolderStructureType.EINZEL);
		l.put(LWGranit2Code.EINZEL_C, DMSFolderStructureType.EINZEL);
		l.put(LWGranit2Code.MIET, DMSFolderStructureType.MIET);
		LOOKUP = l;
	}

	static DMSFolderStructureType getObjektTyp(final LWProjekt projekt, final LWGranit2Code g2Code) {
		DMSFolderStructureType result = LOOKUP.get(g2Code);
		result = result == null ? DMSFolderStructureType.NULL : result;
		if (DMSFolderStructureType.EINZEL.equals(result)) {
			// check for Kleinobjekt
			for (final LWProjekt child : projekt.getChildProjekte()) {
				final String prjNr = child.getProjektnummer();
				final String sigNr = LWProjektKey.getSignificantProjektnummer(prjNr);
				if (Strings.equals(prjNr, sigNr)) {
					// must be Kleinobjekt
					result = DMSFolderStructureType.KLEINOBJEKT;
					break;
				}
			}
		}
		return result;
	}

}

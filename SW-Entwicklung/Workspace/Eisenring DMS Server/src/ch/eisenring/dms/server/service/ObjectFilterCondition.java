//package ch.eisenring.dms.server.service;
//
//public interface ObjectFilterCondition {
//
//	/**
//	 * Default filter condition:
//	 * Status is ACTIVE or ARCHIVED (hides RETRACTED)
//	 */
//	ObjectFilterCondition DEFAULT = new ObjectFilterCondition() {
//		
//	};
//
//	/**
//	 * Only objects in status ACTIVE
//	 */
//	ObjectFilterCondition ACTIVE = new ObjectFilterCondition() {
//		
//	};
//
//	/**
//	 * Only objects of type FOLDER
//	 */
//	
//	/**
//	 * Only objects of type FILE
//	 */
//	
//	/**
//	 * Combines multiple filter conditions into a compound condition.
//	 * The conditions are combined to create a more exclusive condition (AND).
//	 */
//	
//	/**
//	 * Combines multiple filter conditions into a compound condition.
//	 * The conditions are combined to create a more relaxed condition (OR). 
//	 */
//
//}

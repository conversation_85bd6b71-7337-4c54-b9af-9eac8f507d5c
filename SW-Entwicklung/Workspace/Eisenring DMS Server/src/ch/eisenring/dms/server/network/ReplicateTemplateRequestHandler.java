package ch.eisenring.dms.server.network;

import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.dms.server.DMSServer;
import ch.eisenring.dms.server.util.ReplicationTemplate;
import ch.eisenring.dms.server.util.ReplicationTemplateSink;
import ch.eisenring.dms.shared.network.PacketObjectMessages;
import ch.eisenring.dms.shared.network.PacketReplicateTemplateReply;
import ch.eisenring.dms.shared.network.PacketReplicateTemplateRequest;
import ch.eisenring.dms.shared.network.cmd.ObjectMessageConstants;
import ch.eisenring.dms.shared.network.cmd.ObjectMessageSet;
import ch.eisenring.dms.shared.util.FolderVariableTemplateExpander;
import ch.eisenring.network.PacketSink;
import ch.eisenring.network.packet.AbstractPacket;

public class ReplicateTemplateRequestHandler extends AbstractDMSPacketHandler {

	ReplicateTemplateRequestHandler(final DMSServer server) {
		super(server, PacketReplicateTemplateRequest.class);
	}

	@Override
	public void handle(final AbstractPacket packet, final PacketSink sink) {
		final PacketReplicateTemplateRequest request = (PacketReplicateTemplateRequest) packet;
		final PacketReplicateTemplateReply reply = handle(server, request);
		sink.sendPacket(reply, true);
		if (reply.isValid()) {
			// notify all clients that there is a change to target folder...
			final ObjectMessageSet messages = new ObjectMessageSet(request.getTargetFolderId(), ObjectMessageConstants.CHILDREN);
			PacketObjectMessages broadcast = PacketObjectMessages.create(messages);
			server.broadcast(broadcast, false);
		}
	}

	public static PacketReplicateTemplateReply handle(
			final DMSServer server,
			final PacketReplicateTemplateRequest request) {
		PacketReplicateTemplateReply reply = null;
		try {
			reply = handleImpl(server, request);
		} catch (final Exception e) {
			reply = PacketReplicateTemplateReply.create(request, null, new ErrorMessage(e));
		}
		if (reply == null)
			reply = PacketReplicateTemplateReply.create(request, null, ErrorMessage.ERROR);
		return reply;
	}
	
	private static PacketReplicateTemplateReply handleImpl(
			final DMSServer server,
			final PacketReplicateTemplateRequest request) throws Exception {
		final FolderVariableTemplateExpander expander = new FolderVariableTemplateExpander(request.getTemplateParameters());
		final ReplicationTemplate template = ReplicationTemplate.loadTemplate(server, request.getTemplateId());
		final ReplicationTemplateSink replicator = new ReplicationTemplateSink(server, request.getUser());
		final Long createdFolderRowId = replicator.replicate(template, request.getTargetFolderId(), expander);
		return PacketReplicateTemplateReply.create(request, createdFolderRowId, ErrorMessage.OK);
	}

}

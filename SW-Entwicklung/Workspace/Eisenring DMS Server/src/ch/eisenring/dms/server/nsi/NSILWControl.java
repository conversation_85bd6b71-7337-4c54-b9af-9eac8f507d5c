package ch.eisenring.dms.server.nsi;

import java.io.IOException;
import java.io.InputStream;

import ch.eisenring.core.application.DataFile;
import ch.eisenring.core.application.DataFileParser;
import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.csv.CSVFile;
import ch.eisenring.core.csv.CSVLine;
import ch.eisenring.core.csv.CSVOptions;
import ch.eisenring.core.csv.CSVReader;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.dms.server.DMSServer;
import ch.eisenring.dms.shared.codetables.DMSDocumentTypeProperties;
import ch.eisenring.logiware.code.hard.LWFormularArt;
import ch.eisenring.logiware.code.hard.LWMontagescheinSubTyp;
import ch.eisenring.logiware.code.pseudo.AbwicklungsartCode;
import ch.eisenring.logiware.code.soft.GSECode;

public final class NSILWControl implements DataFileParser<List<NSILWControlEntry>> {

	private final static String MATCHALL = "*";

	protected final DMSServer server;

	protected final DataFile<List<NSILWControlEntry>> dataFile =
		new DataFile<List<NSILWControlEntry>>("../data/nsi/LW_DocumentTypeMapping.csv", this);

	public NSILWControl(final DMSServer server) {
		this.server = server;
	}

	public NSILWControlEntry lookup(final LWFormularArt formularArt,
			final AbwicklungsartCode abwicklungsArt,
			final GSECode gse,
			final LWMontagescheinSubTyp msSubTyp) {
		final List<NSILWControlEntry> controlData;
		try {
			controlData = dataFile.getData();
		} catch (final IOException e) {
			throw new RuntimeException(e.getMessage(), e);
		}
		for (final NSILWControlEntry entry : controlData) {
			if (entry.matches(formularArt, abwicklungsArt, gse, msSubTyp))
				return entry;
		}
		return null;
	}

	@Override
	public List<NSILWControlEntry> parseFile(final DataFile<List<NSILWControlEntry>> dataFile) throws IOException, Exception {
		final CSVFile csvFile;
		try (final InputStream dataIn = dataFile.getInputStream()) {
			csvFile = CSVReader.readCSV(dataIn, new CSVOptions());
		}
		if (csvFile == null)
			throw new IOException(Strings.concat("Datei: \"", dataFile, "\" konnte nicht gelesen werden"));
		final int rowCount = csvFile.getLineCount();
		final List<NSILWControlEntry> result = new ArrayList<NSILWControlEntry>(rowCount);
		for (int rowIndex = 0; rowIndex < rowCount; ++rowIndex) {
			final CSVLine line = csvFile.getLine(rowIndex);
			final LWFormularArt formularArt = AbstractCode.getByKey(line.getColumn(0), LWFormularArt.class);
			final AbwicklungsartCode abwicklungsArt; {
				final String key = line.getColumn(1);
				if (Strings.equals(MATCHALL, key)) {
					abwicklungsArt = null;
				} else {
					abwicklungsArt = AbstractCode.getByKey(key, AbwicklungsartCode.class);
				}
			}
			final GSECode gse; {
				final String key = line.getColumn(2);
				if (Strings.equals(MATCHALL, key)) {
					gse = null;
				} else {
					gse = AbstractCode.getByKey(key, GSECode.class);
				}
			}
			final LWMontagescheinSubTyp msSubTyp; {
				final String key = line.getColumn(3);
				if (Strings.equals(MATCHALL, key)) {
					msSubTyp = null;
				} else {
					msSubTyp = AbstractCode.getByKey(key, LWMontagescheinSubTyp.class);
				}
			}
			final DMSDocumentTypeProperties documentCode = DMSDocumentTypeProperties.getByNSIType(line.getColumn(4));
			final NSIKWTyp kwTyp; {
				final String colValue = line.getColumn(5);
				if (Strings.isEmpty(colValue)) {
					kwTyp = NSIKWTyp.NULL;
				} else {
					final String key = colValue.substring(0, 1);
					kwTyp = AbstractCode.getByKey(key, NSIKWTyp.class);
				}
			}
			if (AbstractCode.isNull(formularArt) || AbstractCode.isNull(documentCode)) {
				// skip this line
				continue;
			}
			final NSILWControlEntry entry = new NSILWControlEntry(formularArt, abwicklungsArt, gse, msSubTyp, documentCode, kwTyp);
			result.add(entry);
		}
		result.trimToSize();
		return result;
	}
	
}

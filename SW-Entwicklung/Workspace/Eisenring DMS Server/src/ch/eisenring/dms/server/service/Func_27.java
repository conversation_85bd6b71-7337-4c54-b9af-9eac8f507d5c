package ch.eisenring.dms.server.service;

import static ch.eisenring.dms.server.DMSMapping.TM_OBJECT;

import java.sql.SQLException;
import java.util.concurrent.atomic.AtomicLong;

import ch.eisenring.app.server.model.ContextSource;
import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.core.datatypes.strings.Msg;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.dms.service.DMSServiceException;
import ch.eisenring.dms.service.DMSServiceResultCode;
import ch.eisenring.dms.service.api.DMSObjectIdentity;
import ch.eisenring.dms.service.codetables.DMSObjectStatus;
import ch.eisenring.dms.service.codetables.DMSObjectType;
import ch.eisenring.dms.shared.DMSConstants;
import ch.eisenring.dms.shared.model.meta.DMSObjectMeta;
import ch.eisenring.jdbc.StatementParameters;
import ch.eisenring.model.TransactionContext;

final class Func_27 {

	static long getMaxDocumentDate(final DMSObjectIdentity folder) throws DMSServiceException {
		return getDocumentDate(folder, "MAX");
	}

	static long getMinDocumentDate(final DMSObjectIdentity folder) throws DMSServiceException {
		return getDocumentDate(folder, "MIN");
	}

	private static long getDocumentDate(final DMSObjectIdentity folder, final String sqlFunction) throws DMSServiceException {
		if (folder == null) {
			final String message = Msg.mk("Ungültige Ordner-Identität: {}", folder);
			throw new DMSServiceException(DMSServiceResultCode.INVALID_PARAMETER, message);
		}
		final AtomicLong result = new AtomicLong(TimestampUtil.NULL_TIMESTAMP);
		final ContextSource source = new ContextSource(DMSConstants.DMS_DATABASE) {
			@Override
			protected void loadImpl(final TransactionContext context) throws SQLException {
				final StatementParameters params = new StatementParameters();
				final StringMaker sql = StringMaker.obtain();
				sql.append("SELECT ");
				sql.append(sqlFunction);
				sql.append("(");
				sql.append(TM_OBJECT.getColumn(DMSObjectMeta.ATR_LASTCHANGED));
				sql.append(") FROM ");
				sql.append(TM_OBJECT.getTableSpecifier());
				sql.append(" WHERE ");
				sql.append(TM_OBJECT.getColumn(DMSObjectMeta.ATR_PARENT_ROWID));
				sql.append(" = ? AND ");
				sql.append(TM_OBJECT.getColumn(DMSObjectMeta.ATR_TYPECODE));
				sql.append(" = ? AND ");
				sql.append(TM_OBJECT.getColumn(DMSObjectMeta.ATR_STATUSCODE));
				sql.append(" <= ?");
				params.add(folder.getPKValue());
				params.add(DMSObjectType.FILE);
				params.add(DMSObjectStatus.ARCHIVED);
				doQuery(sql.release(), params, (resultSet) -> {
					final long timestamp = resultSet.getLongTimestamp(1);
					result.set(timestamp);
				});
			}
		};
		try {
			final TransactionContext context = new TransactionContext();
			context.load(source);
		} catch (final Exception e) {
			final String message = Msg.mk("Fehler: {}", e.getMessage());
			throw new DMSServiceException(DMSServiceResultCode.INTERNAL_ERROR, message);
		}
		return result.get();
	}

}

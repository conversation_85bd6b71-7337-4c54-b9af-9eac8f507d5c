//package ch.eisenring.dms.server.test;
//
//import java.io.IOException;
//import java.sql.SQLException;
//import java.util.Iterator;
//
//import ch.eisenring.core.collections.List;
//import ch.eisenring.core.datatypes.strings.Strings;
//import ch.eisenring.core.logging.Logger;
//import ch.eisenring.core.resource.image.ImageReducer;
//import ch.eisenring.core.resource.image.ImageScaleRule;
//import ch.eisenring.core.util.file.FileImage;
//import ch.eisenring.core.util.file.FileUtil;
//import ch.eisenring.dms.server.DMSServer;
//import ch.eisenring.dms.server.audit.AuditTrailBatch;
//import ch.eisenring.dms.server.dao.DMSContextSink;
//import ch.eisenring.dms.shared.codetables.DMSAuditCode;
//import ch.eisenring.dms.shared.model.record.DMSBinaryModel;
//import ch.eisenring.dms.shared.model.record.DMSObjectModel;
//import ch.eisenring.model.Model;
//import ch.eisenring.model.TransactionContext;
//
//public class ForcedImageReduction extends UpdateBase {
//
//	public ForcedImageReduction(final DMSServer server) {
//		super(server);
//	}
//
//	@Override
//	public void doUpdate() {
//		List<Long> list;
//		try {
//			list = getImageObjectRowIds();
//		} catch (final Exception e) {
//			Logger.error(e);
//			server.shutdown(e);
//			throw new RuntimeException(e.getMessage(), e);
//		}
//		list.sort(null);
//		
//		long count = 0;
//		for (long objectRowId : list) {
//			// process list item
//			try {
//				processObject(objectRowId);
//				System.out.println("Processed[" + count + "]: " + objectRowId);
//			} catch (final Exception e) {
//				Logger.warn("objectRowId(" + objectRowId + "): " + e.getMessage());
//			}
//			++count;
//			if (count > 5000)
//				break;
//		}
//		
//		server.getCore().shutdown(true);
//	}
//
//	/**
//	 * Process single object
//	 */
//	void processObject(final long objectRowId) throws Exception {
//		// determine the binary rowId
//		final long binaryRowId;
//		{
//			final List<Long> binaryRowIds = getObjectRowIds("SELECT rowId FROM DMSBinary where object_rowId = " + objectRowId);
//			if (binaryRowIds.size() != 1)
//				throw new RuntimeException("Version count != 1");
//			binaryRowId = binaryRowIds.get(0);
//		}
//		// retrieve the binary
//		final DMSContextSink sink = new DMSContextSink(server) {
//			@Override
//			protected void storeImpl(final Iterator<Model> modelItr, final TransactionContext context) throws SQLException {
//				// fetch the binary
//				final DMSObjectModel object = loadObject(context, objectRowId, true, true);
//				final DMSBinaryModel binary = loadBinary(context, objectRowId, binaryRowId, true);
//				AuditTrailBatch auditTrail = new AuditTrailBatch();
//				final ImageReducer reducer = new ImageReducer(ImageScaleRule.DEFAULT_RULE);
//				// reduce the image
//				try {
//					FileImage orgFile = FileImage.create(FileUtil.addFileExtension("x", binary.getFileExtension()), binary.getBinaryData());
//					FileImage redFile = reducer.reduceImage(orgFile);
//					// do nothing if file size stays the same or gets larger
//					if (redFile == null || redFile.getFiledata().size() >= orgFile.getFiledata().size())
//						return;
//					binary.setBinaryData(redFile.getFiledata());
//					binary.setFileExtension(redFile.getFileExtension());
//					object.setFileExtension(redFile.getFileExtension());
//					object.setBytesize(redFile.getFileSize());
//					auditTrail.add(objectRowId, binaryRowId, DMSAuditCode.IMAGE_SCALED,
//							"System", Strings.toString(orgFile.getFileSize()), Strings.toString(redFile.getFileSize()));
//					// store all changed data
//					update(binary);
//					update(object);
//					executeBatchInserts();
//					executeBatchUpdates();
//					executeBatchDeletes();
//					auditTrail.insertBatch(this);
//				} catch (final IOException e) {
//					throw new SQLException(e.getMessage(), e);
//				}
//			}
//		};
//		TransactionContext context = new TransactionContext();
//		context.store(sink);
//	}
//	
//	/**
//	 * Selects the documents to scale down
//	 */
//	List<Long> getImageObjectRowIds() throws Exception {
//		String sql = "SELECT OB.rowId FROM"
//				+    " DMSObject AS OB,"
//				+    " DMSProperty AS PR "
//				+	 "WHERE"
//				+	 " PR.Object_rowId = OB.Parent_rowId AND"
//				+    " PR.Propertycode = 7 AND PR.Propertyvalue IN ('11', '12', '13', '14', '15', '17', '40') AND"
//				+	 " OB.FileExtension = 'jpg' AND OB.Bytesize > 524288";
//		return getObjectRowIds(sql);
//	}
//
//}

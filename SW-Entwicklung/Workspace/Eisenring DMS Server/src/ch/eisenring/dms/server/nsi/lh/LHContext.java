package ch.eisenring.dms.server.nsi.lh;

import heag.nsi.server.api.NSIFileWatchEntry;
import heag.nsi.server.api.NSIProcessingContext;
import ch.eisenring.PlaintextExtractor;
import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.collections.Collection;
import ch.eisenring.core.collections.Map;
import ch.eisenring.core.collections.impl.HashMap;
import ch.eisenring.core.datatypes.encoded.LHBarcodeData;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.io.binary.BinaryHolder;
import ch.eisenring.dms.service.codetables.DMSDocumentType;
import ch.eisenring.logiware.code.service.LogiwareService;
import ch.eisenring.lw.condition.Factory;
import ch.eisenring.lw.meta.LWAuftragMeta;
import ch.eisenring.lw.model.navigable.LWAuftrag;
import ch.eisenring.lw.model.navigable.LWObjectCache;

public final class LHContext {

	public final NSIProcessingContext nsiContext;
	public final NSIFileWatchEntry nsiEntry;
	public final LHBarcodeData barcodeData;
	
	public DMSDocumentType dmsDocumentType;
	public String dmsDocumentName;
	public String projektNummer;
	
	public final Map<String, String> documentParameters = new HashMap<>();
	
	public LHContext(final NSIProcessingContext nsiContext,
			final NSIFileWatchEntry nsiEntry,
			final LHBarcodeData barcodeData) {
		this.nsiContext = nsiContext;
		this.nsiEntry = nsiEntry;
		this.barcodeData = barcodeData;
	}

	public void addParameter(final String parameterName, final String parameterValue) {
		documentParameters.put(parameterName, parameterValue);
	}

	public Map<String, String> getParameters() {
		return new HashMap<>(documentParameters);
	}

	// --------------------------------------------------------------
	// ---
	// --- Logiware cache access
	// ---
	// --------------------------------------------------------------
	private LWObjectCache lwCache;
	
	public LWObjectCache getLWCache() throws Exception {
		if (lwCache == null) {
			final LogiwareService service = nsiContext.server.locateService(LogiwareService.class);
			lwCache = service.createObjectCache();
		}
		return lwCache;
	}

	public LWAuftrag getLWAuftrag(final int auftragNummer) throws Exception {
		final LWObjectCache cache = getLWCache();
		final Collection<LWAuftrag> auftraege = cache.load(LWAuftrag.class, 
						Factory.eq(LWAuftragMeta.ATR_AUFTRAGNUMMER, auftragNummer));
		final int count = auftraege.size();
		if (count <= 0) {
			throw new RuntimeException("Kein Aufrag mit Belegenummer " + auftragNummer + " gefunden");
		} else if (count > 1) {
			throw new RuntimeException("Die Belegenummer " + auftragNummer + " ist nicht eindeutig");
		}
		final LWAuftrag auftrag = auftraege.iterator().next();
		return auftrag;
	}

	// --------------------------------------------------------------
	// ---
	// --- Error reporting
	// ---
	// --------------------------------------------------------------
	public void addError(final ErrorMessage error) {
		nsiContext.addError(error);
	}

	public void addError(final CharSequence error) {
		addError(new ErrorMessage(error));
	}

	// --------------------------------------------------------------
	// ---
	// --- Plaintext access
	// ---
	// --------------------------------------------------------------
	private final static String PLAINTEXT_INVALID = "\0Invalid\0";
	
	private String plainText = PLAINTEXT_INVALID;
	
	/**
	 * Gets the plain text for the document
	 */
	public String getPlainText() throws Exception {
		String result = plainText;
		if (Strings.equals(plainText, PLAINTEXT_INVALID)) {
			final BinaryHolder binary = nsiEntry.getDocumentData();
			result = PlaintextExtractor.extract(binary);
			this.plainText = result;
		}
		return result;		
	}

}

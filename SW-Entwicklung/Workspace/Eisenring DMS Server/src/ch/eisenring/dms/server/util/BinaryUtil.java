package ch.eisenring.dms.server.util;

import static ch.eisenring.dms.shared.model.metaold.DMSBinaryMetaModel.ATR_BINARYDATA;
import static ch.eisenring.dms.shared.model.metaold.DMSVersionMetaModel.ATR_DCMCODE;
import static ch.eisenring.dms.shared.model.metaold.DMSVersionMetaModel.ATR_RAWSIZE;

import java.io.IOException;

import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.io.binary.BinaryHolder;
import ch.eisenring.core.io.binary.BinaryHolderCompressor;
import ch.eisenring.core.io.binary.BinaryHolderDEFL;
import ch.eisenring.core.io.binary.BinaryHolderUtil;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.core.util.ConcurrentTransformer;
import ch.eisenring.core.util.file.FileUtil;
import ch.eisenring.dms.service.codetables.DMSBinaryStatus;
import ch.eisenring.dms.shared.DMSConstants;
import ch.eisenring.dms.shared.codetables.DMSCompressionCode;
import ch.eisenring.dms.shared.codetables.DMSCompressionPolicy;
import ch.eisenring.dms.shared.io.binary.BinaryHolderLZMA2;
import ch.eisenring.dms.shared.model.metaold.DMSBinaryMetaModel;
import ch.eisenring.dms.shared.model.record.DMSBinaryModel;
import ch.eisenring.dms.shared.util.ThumbnailUtil;
import ch.eisenring.model.PrimaryKey;
import ch.eisenring.model.TransactionContext;

public abstract class BinaryUtil extends ThumbnailUtil {

	/**
	 * Helper class that implements compression in a separate thread
	 */
	static final class ConcurrentCompressor extends ConcurrentTransformer<BinaryHolder, BinaryHolder>{
		
		protected final DMSCompressionCode dcmCode;

		public ConcurrentCompressor(final BinaryHolder uncompressed,
				                    final DMSCompressionCode dcmCode,
				                    final BinaryHolderCompressor compressor) {
			super(uncompressed, compressor);
			this.dcmCode = dcmCode;
		}

		@Override
		protected void onSuccess() {
			super.onSuccess();
			final BinaryHolder source = getSource();
			final BinaryHolder result = getResult();
			if (result != null) {
				if (!BinaryUtil.verify(source, result, dcmCode)) {
					setError(new ErrorMessage("Compressed data verification failure"));
					onFailure();
				}
			}
		}

		@Override
		protected void onFailure() {
			super.onFailure();
			final BinaryHolder result = getResult();
			BinaryHolderUtil.dispose(result);
		}

	}

	private final static double WEIGTHING_FACTOR = 1.025D;

	protected BinaryUtil() {
	}

	// --------------------------------------------------------------
	// ---
	// --- Create Factory
	// ---
	// --------------------------------------------------------------
	public static DMSBinaryModel createBinary(final BinaryHolder binaryHolder, final String objectname) {
		return createBinary(binaryHolder, objectname, DMSCompressionPolicy.AGGRESIVE);
	}

	/**
	 * Creates a binary and initializes it using the provided
	 * binary data. The status will be set to active.
	 * The plain text version will be set to invalid.
	 * Note that the object row id will still be NULL and
	 * needs to be set by the caller.
	 * 
	 * The binary will be created in its own private context.
	 */
	public static DMSBinaryModel createBinary(final BinaryHolder binaryHolder,
										 final String objectname,
										 final DMSCompressionPolicy compressionPolicy) {
		final TransactionContext context = new TransactionContext();
		final DMSBinaryModel binary = (DMSBinaryModel) context.createModel(DMSBinaryMetaModel.METACLASS, (PrimaryKey) null);
		binary.setStatusCode(DMSBinaryStatus.ACTIVE);
		binary.setBinaryData(binaryHolder);
		binary.setMadeOn(System.currentTimeMillis());
		binary.setFileExtension(FileUtil.getFileExtension(objectname));
		binary.setBinaryData(binaryHolder);
		
		// create compressed version
		if (compressionPolicy != null && compressionPolicy.getImmediateLimit() >= binary.getBytesize()) {
			compressBinary(binary);
		}

		// prepare plain text later
		binary.setPlaintext(null);
		binary.setPlainversion(DMSConstants.PLAINTEXT_VERSION_INVALID);
		return binary;
	}

	/**
	 * Compresses binary with the compression that yields the smallest
	 * binary data. Performs multiple compressions concurrently.
	 */
	public static void compressBinary(final DMSBinaryModel binary) {
		final BinaryHolder uncompressed = binary.getBinaryData();
		BinaryHolder bestBinary = uncompressed;
		DMSCompressionCode bestCode = binary.getDCMCode();

		try {
			ConcurrentCompressor ccLZMA2 = new ConcurrentCompressor(uncompressed, DMSCompressionCode.LZMA2, BinaryHolderLZMA2.COMPRESSOR);
			ConcurrentCompressor ccDEFL = new ConcurrentCompressor(uncompressed, DMSCompressionCode.DEFL, BinaryHolderDEFL.COMPRESSOR);
			
			ccDEFL.waitForCompletion();
			ccLZMA2.waitForCompletion();
			
			// check which compression is best
			BinaryHolder compressed;
			compressed = ccDEFL.getResult();
			if (compressed != null && compressed.size() < bestBinary.size()) {
				bestCode = DMSCompressionCode.DEFL;
				bestBinary = compressed;
			}
			ccDEFL = null;

			compressed = ccLZMA2.getResult();
			if (compressed != null && compressed.size() < bestBinary.size()) {
				bestCode = DMSCompressionCode.LZMA2;
				bestBinary = compressed;
			}
			ccLZMA2 = null;
			compressed = null;

			// --- check if compression was worth it, otherwise 
			//     just use compression NONE
			if (bestBinary.size() * WEIGTHING_FACTOR > uncompressed.size()) {
				bestCode = DMSCompressionCode.NONE1;
				bestBinary = uncompressed;
			}
		} catch (final Exception e) {
			// abort compression attempt
		} catch (final Error e) {
			// abort compression attempt
		}
		// if no compression was NONE0, bump to NONE1
		// (this indicates that compression was attempted and NONE is best)
		if (DMSCompressionCode.NONE0.equals(bestCode))
			bestCode = DMSCompressionCode.NONE1;
		
		try {
			final long rawSize = bestBinary.size();
			ATR_RAWSIZE.setMember(binary, rawSize);
			ATR_BINARYDATA.setMember(binary, bestBinary);
			ATR_DCMCODE.setMember(binary, bestCode);
		} catch (final IOException e) {
			// ignore
		}
	}

	/**
	 * Verifies that the compressed data uncompresses properly.
	 * This may seem paranoid, but since we have no reliable guarantees
	 * about the maturity of the code of some of the compression
	 * implementations we want to use we need some kind of verification
	 * that they actually work as intended.
	 */
	public static boolean verify(final BinaryHolder uncompressed,
			                      final BinaryHolder rawCompressed,
			                      final DMSCompressionCode compressionMode) {
		try {
			final BinaryHolder decompressed = compressionMode.decompress(rawCompressed);
			return decompressed.equals(uncompressed);
		} catch (final Exception e) {
			Logger.warn("BinaryUtil.verify(): " + e.getMessage());
			Logger.warn(e);
			return false;
		}
	}

}

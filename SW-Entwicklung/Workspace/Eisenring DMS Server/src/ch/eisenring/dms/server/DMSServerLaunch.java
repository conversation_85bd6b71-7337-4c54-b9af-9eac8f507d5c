package ch.eisenring.dms.server;

import static ch.eisenring.dms.server.DMSMapping.OM_LOCK;
import static ch.eisenring.dms.server.DMSMapping.TM_LOCK;

import java.io.IOException;
import java.sql.SQLException;

import ch.eisenring.app.server.model.ContextSource;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.dms.shared.DMSConstants;
import ch.eisenring.dms.shared.locking.DMSObjectLock;
import ch.eisenring.dms.shared.locking.DMSObjectLockSet;
import ch.eisenring.jdbc.JDBCResultSet;
import ch.eisenring.jdbc.RowHandler;
import ch.eisenring.model.TransactionContext;
import ch.eisenring.model.TransactionSource;
import ch.eisenring.model.engine.jdbc.TableMapping;

final class DMSServerLaunch {

	protected final DMSServer server;

	public DMSServerLaunch(final DMSServer server) {
		this.server = server;
	}

	public void launch() {
		loadLocks();
	}

	/**
	 * Loads all existing locks from the database.
	 * This ensures that locks that remained when the server has restarted
	 * are still recognized.
	 */
	private void loadLocks() {
		final DMSObjectLockSet lockSet = server.OBJECTLOCKSET.get();
		final TransactionSource source = new ContextSource(DMSConstants.DMS_DATABASE) {
			@Override
			protected void loadImpl(final TransactionContext context) throws SQLException {
				final StringMaker sql = StringMaker.obtain();
				TM_LOCK.appendSelectSQL(sql, this, TableMapping.NO_TABLE_ALIAS);
				doQuery(sql, null, new RowHandler() {
					@Override
					public void handleRow(final JDBCResultSet resultSet) throws SQLException {
						final DMSObjectLock lock = OM_LOCK.loadRow(context, resultSet);
						lockSet.add(lock);
					}
				});
			}
		};
		try {
			final TransactionContext context = new TransactionContext();
			context.load(source);
			Logger.info(Strings.concat("loaded ", context.size(), " DMSLocks from database"));
		} catch (final IOException e) {
			Logger.error("error loading DMSLocks from database: " + e.getMessage());
			Logger.error(e);
		}
	}

}

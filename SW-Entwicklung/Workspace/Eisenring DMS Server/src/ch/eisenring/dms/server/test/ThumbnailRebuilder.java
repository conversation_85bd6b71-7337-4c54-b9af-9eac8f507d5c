package ch.eisenring.dms.server.test;

import java.io.IOException;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Iterator;

import ch.eisenring.app.shared.CoreTickListener;
import ch.eisenring.core.io.binary.BinaryHolder;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.dms.server.DMSServer;
import ch.eisenring.dms.server.dao.DMSContextSink;
import ch.eisenring.dms.shared.DMSConstants;
import ch.eisenring.dms.shared.model.record.DMSBinaryModel;
import ch.eisenring.dms.shared.util.ThumbnailUtil;
import ch.eisenring.jdbc.JDBCResultSet;
import ch.eisenring.jdbc.StatementWrapper;
import ch.eisenring.model.Model;
import ch.eisenring.model.TransactionContext;

/**
 * Rebuilds every thumbnail in the DB.
 * Takes naturally a very very long time to do so.
 */
public class ThumbnailRebuilder extends UpdateBase implements CoreTickListener {

	public ThumbnailRebuilder(final DMSServer server) {
		super(server);
	}

	public void doUpdate() {
		DMSContextSink sink = new DMSContextSink(server) {
			@Override
			protected void storeImpl(final Iterator<Model> modelItr, final TransactionContext context) throws SQLException {
				final ArrayList<Long> rowIdList = findObjects();
				final String sql = "UPDATE DMSObject SET Thumbnail = ? WHERE rowId = ?";
				final StatementWrapper statement = prepareStatement(sql);
				// switch to auto commit
				getConnection().setAutoCommit(true);
				try {
					for (final Long rowId : rowIdList) {
						final DMSBinaryModel binary = loadBinary(new TransactionContext(), rowId, null, false);
						if (binary == null)
							continue;
						final BinaryHolder holder = binary.getBinaryData();
						final BinaryHolder thumb = ThumbnailUtil.createThumbnail(holder, DMSConstants.THUMBNAIL_SIZE);
						if (thumb == null)
							continue;
						statement.setObject(1, thumb);
						statement.setObject(2, rowId);
						statement.executeUpdate();
					}
				} finally {
					closeSilent(statement);
				}
			}

			private ArrayList<Long> findObjects() throws SQLException {
				final ArrayList<Long> result = new ArrayList<Long>(1024);
				String sql = "SELECT rowId FROM DMSObject WHERE Typecode = 2 AND Iconcode = 0 AND Thumbnail IS NULL AND LastChanged > Cast('2018-09-21' as Datetime)";
				final StatementWrapper statement = prepareStatement(sql);
				try {
					final JDBCResultSet resultSet = statement.executeQuery();
					try {
						while (resultSet.next()) {
							final long rowId = resultSet.getLong(1);
							result.add(Long.valueOf(rowId));
						}
					} finally {
						closeSilent(resultSet);
					}
				} finally {
					closeSilent(statement);
				}

				return result;
			}
		};
		try {
			new TransactionContext().store(sink);
		} catch (final IOException e) {
			Logger.fatal(e);
		}
	}

	@Override
	public String getName() {
		return "ThumbnailRebuilder";
	}

	@Override
	public void tick(final long now) {
		doUpdate();
	}
}

package ch.eisenring.dms.server.network;

import ch.eisenring.app.shared.ServiceNotFoundException;
import ch.eisenring.core.util.file.FileImage;
import ch.eisenring.dms.server.DMSServer;
import ch.eisenring.dms.service.DMSService;
import ch.eisenring.dms.service.DMSServiceException;
import ch.eisenring.dms.service.DMSServiceResultCode;
import ch.eisenring.dms.service.api.DMSObjectIdentity;
import ch.eisenring.dms.service.codetables.DMSDocumentTemplateCode;
import ch.eisenring.dms.service.codetables.DMSDocumentType;
import ch.eisenring.dms.service.codetables.DMSFolderType;
import ch.eisenring.dms.service.network.PacketDMSServiceReply;
import ch.eisenring.dms.service.network.PacketDMSServiceRequest;
import ch.eisenring.dms.service.proxy.DMSDocumentHandle;
import ch.eisenring.dms.service.proxy.DMSFolderHandle;
import ch.eisenring.network.PacketSink;
import ch.eisenring.network.packet.AbstractPacket;

final class DMSServiceRequestHandler extends AbstractDMSPacketHandler {

	DMSServiceRequestHandler(final DMSServer server) {
		super(server, PacketDMSServiceRequest.class);
	}

	@Override
	public PacketDMSServiceReply handle(final AbstractPacket packet) {
		final PacketDMSServiceRequest request = (PacketDMSServiceRequest) packet;
		final PacketDMSServiceReply reply = handle(server, request);
		return reply;
	}

	@Override
	public void handle(final AbstractPacket packet, final PacketSink sink) {
		final PacketDMSServiceRequest request = (PacketDMSServiceRequest) packet;
		final PacketDMSServiceReply reply = handle(server, request);
		sink.sendPacket(reply, false);
	}

	private static PacketDMSServiceReply handle(final DMSServer server, final PacketDMSServiceRequest request) {
		DMSServiceException error;
		try {
			final PacketDMSServiceReply reply = handleImpl(server, request);
			if (reply != null)
				return reply;
			error = new DMSServiceException(DMSServiceResultCode.INTERNAL_ERROR, null);
		} catch (final DMSServiceException e) {
			error = e;
		} catch (final Exception e) {
			error = new DMSServiceException(DMSServiceResultCode.INTERNAL_ERROR, e.getMessage());
		}
		return PacketDMSServiceReply.create(request, error, null);
	}

	@SuppressWarnings("unchecked")
	private static PacketDMSServiceReply handleImpl(final DMSServer server, final PacketDMSServiceRequest request) throws Exception {
		final DMSService service;
		try {
			service = server.locateService(DMSService.class);
		} catch (final ServiceNotFoundException e) {
			final DMSServiceException error = new DMSServiceException(DMSServiceResultCode.SERVICE_UNAVAILABLE, null);
			return PacketDMSServiceReply.create(request, error, null);
		}
		final Object[] args = request.getArgs();
		final Object result;
		switch (request.getMethodId()) {
			default:
				final DMSServiceException error = new DMSServiceException(DMSServiceResultCode.OPERATION_NOT_SUPPORTED, null);
				return PacketDMSServiceReply.create(request, error, null);
			case 1:
				if (args.length != 1)
					return invalidArgs(request);
				result = service.getDocumentImage((DMSDocumentHandle) args[0]);
				break;
			case 2:
				if (args.length != 1)
					return invalidArgs(request);
				result = service.getDocumentImages((java.util.List<DMSDocumentHandle>) args[0]);
				break;
			case 3:
				if (args.length != 1)
					return invalidArgs(request);
				result = service.getBasisFolder((String) args[0]);
				break;
			case 4:
				if (args.length != 1)
					return invalidArgs(request);
				result = service.getProjectFolder((String) args[0]);
				break;
			case 5:
				if (args.length != 3)
					return invalidArgs(request);
				result = service.getFolderByProperty((DMSFolderHandle) args[0], (int) args[1], args[2]);
				break;
			case 6:
				if (args.length != 3)
					return invalidArgs(request);
				result = service.getFoldersByProperty((DMSFolderHandle) args[0], (int) args[1], args[2]);
				break;
			case 7:
				if (args.length != 2)
					return invalidArgs(request);
				result = service.getDocumentFolder((String) args[0], (DMSDocumentType) args[1]);
				break;
			case 8:
				if (args.length != 2)
					return invalidArgs(request);
				result = service.getDocumentImages((String) args[0], (DMSDocumentType) args[1]);
				break;
			case 9:
				if (args.length != 2)
					return invalidArgs(request);
				result = service.getDocumentImages((DMSFolderHandle) args[0], (DMSDocumentType) args[1]);
				break;
			case 10:
				if (args.length != 2)
					return invalidArgs(request);
				result = service.getProjectContentFolder((String) args[0], (DMSFolderType) args[1]);
				break;
			case 11:
				if (args.length != 3)
					return invalidArgs(request);
				result = service.putDocument((DMSFolderHandle) args[0], (String) args[1], (FileImage) args[2]);
				break;
			case 12:
				if (args.length != 2)
					return invalidArgs(request);
				result = service.countDocuments((String) args[0], (DMSDocumentType) args[1]);
				break;
			case 13:
				if (args.length != 2)
					return invalidArgs(request);
				result = service.countFolderContents((java.util.Collection<String>) args[0], (DMSFolderType) args[1]);
				break;
			case 14:
				if (args.length != 1)
					return invalidArgs(request);
				result = service.getAusfuehrungsplaene((String) args[0]);
				break;
			case 15:
				if (args.length != 1)
					return invalidArgs(request);
				result = service.getDocumentImage((DMSDocumentTemplateCode) args[0]);
				break;
			case 16:
				if (args.length != 3)
					return invalidArgs(request);
				service.setDocumentType((DMSDocumentHandle) args[0], (DMSDocumentType) args[1], (String) args[2]);
				result = null;
				break;
			case 17:
				if (args.length != 3)
					return invalidArgs(request);
				result = service.getDocumentByProperty((DMSFolderHandle) args[0], (int) args[1], args[2]);
				break;
			case 18:
				if (args.length != 3)
					return invalidArgs(request);
				result = service.getDocumentsByProperty((DMSFolderHandle) args[0], (int) args[1], args[2]);
				break;
			case 19:
				if (args.length != 3)
					return invalidArgs(request);
				result = service.getFolderContents((DMSFolderHandle) args[0]);
				break;
			case 20:
				if (args.length != 1)
					return invalidArgs(request);
				result = service.getUserPermissions((String) args[0]);
				break;
			case 21:
				if (args.length != 2)
					return invalidArgs(request);
				result = service.countFolderContents((String) args[0], (DMSFolderType) args[1]);
				break;
			case PacketDMSServiceRequest.FUNC_ID_LOCATEBASISFOLDER: 
				if (args.length != 1)
					return invalidArgs(request);
				result = service.locateBasisFolder((String) args[0]);
				break;
			case PacketDMSServiceRequest.FUNC_ID_LOCATEPROJECTFOLDER:
				if (args.length != 1)
					return invalidArgs(request);
				result = service.locateProjectFolder((String) args[0]);
				break;
			case PacketDMSServiceRequest.FUNC_ID_LOCATECONTENTFOLDER:
				if (args.length != 2)
					return invalidArgs(request);
				result = service.locateProjectContentFolder((String) args[0], (DMSFolderType) args[1]);
				break;
			case PacketDMSServiceRequest.FUNC_ID_GETOBJECT:
				if (args.length != 1)
					return invalidArgs(request);
				result = service.getObject((DMSObjectIdentity) args[0]);
				break;
			case PacketDMSServiceRequest.FUNC_ID_GETOBJECTS:
				if (args.length != 1)
					return invalidArgs(request);
				result = service.getObjects((java.util.Collection<? extends DMSObjectIdentity>) args[0]);
				break;
			case PacketDMSServiceRequest.FUNC_ID_GETMAXDOCUMENTDATE:
				if (args.length != 1)
					return invalidArgs(request);
				result = service.getMaxDocumentDate((DMSObjectIdentity) args[0]);
				break;
			case PacketDMSServiceRequest.FUNC_ID_GETMINDOCUMENTDATE:
				if (args.length != 1)
					return invalidArgs(request);
				result = service.getMinDocumentDate((DMSObjectIdentity) args[0]);
				break;
		}
		return PacketDMSServiceReply.create(request, (DMSServiceException) null, result);
	}

	private static PacketDMSServiceReply invalidArgs(final PacketDMSServiceRequest request) {
		final DMSServiceException error = new DMSServiceException(DMSServiceResultCode.INVALID_PARAMETER, null);
		return PacketDMSServiceReply.create(request, error, null);
	}

}

//package ch.eisenring.dms.server.service;
//
//import java.io.IOException;
//
//import ch.eisenring.core.io.binary.BinaryHolder;
//import ch.eisenring.dms.server.DMSServer;
//
//public class BinaryHolderBlob implements BinaryHolder {
//
//	private final DMSServer server;
//	private final long binaryRowId;
//	
//	public BinaryHolderBlob(
//			final DMSServer server,
//			final long binaryRowId) {
//		this.server = server;
//		this.binaryRowId = binaryRowId;
//	}
//
//	@Override
//	public void dispose() {
//		// NO-OP
//	}
//
//	@Override
//	public BinaryHolder getImmutableCopy() throws IOException {
//		return this;
//	}
//
//}

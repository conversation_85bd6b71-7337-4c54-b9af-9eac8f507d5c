package ch.eisenring.dms.server.test;

import static ch.eisenring.dms.server.DMSMapping.OM_OBJECT;
import static ch.eisenring.dms.server.DMSMapping.TM_OBJECT;
import static ch.eisenring.dms.server.DMSMapping.TM_PROPERTY;

import java.io.IOException;
import java.sql.SQLException;
import java.util.Iterator;
import java.util.concurrent.atomic.AtomicReference;

import ch.eisenring.app.server.model.ContextSource;
import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.Set;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.collections.impl.HashSet;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.jdbc.Column;
import ch.eisenring.dms.server.DMSServer;
import ch.eisenring.dms.server.audit.AuditTrailBatch;
import ch.eisenring.dms.server.dao.DMSContextSink;
import ch.eisenring.dms.service.codetables.DMSObjectType;
import ch.eisenring.dms.service.codetables.DMSPropertyCode;
import ch.eisenring.dms.shared.DMSConstants;
import ch.eisenring.dms.shared.codetables.DMSAuditCode;
import ch.eisenring.dms.shared.model.meta.DMSObjectMeta;
import ch.eisenring.dms.shared.model.meta.DMSPropertyMeta;
import ch.eisenring.dms.shared.model.pojo.DMSObjectPojo;
import ch.eisenring.jdbc.JDBCBase;
import ch.eisenring.jdbc.JDBCResultSet;
import ch.eisenring.jdbc.RowHandler;
import ch.eisenring.jdbc.StatementParameters;
import ch.eisenring.model.Model;
import ch.eisenring.model.TransactionContext;
import ch.eisenring.model.TransactionSource;

/**
 * Helper methods for updating DMS content
 */
public abstract class UpdateBase {

	protected final static String USER = "Update";
	
	public final static long TEMPLATE_KUECHE_EINZEL = 1184609348;
	public final static long TEMPLATE_KUECHE_EIGENTUM = 1184639075;
	public final static long TEMPLATE_KUECHE_MIET = 1184639114;
	
	public final static long TEMPLATE_BASIS_EIGENTUM = 1184639068L;
	public final static long TEMPLATE_BASIS_MIET = 1184639107L;
	
	protected final DMSServer server;
	
	public UpdateBase(final DMSServer server) {
		this.server = server;
	}

	/**
	 * Applies the update
	 */
	public abstract void doUpdate();

	/**
	 * Gets a list of all objects selected by query.
	 * (Query must select rowId as column #1, other columns are ignored);
	 */
	public List<Long> getObjectRowIds(final String sql) throws Exception {
		return getObjectRowIds(sql, null);
	}

	/**
	 * Gets list of all objects id's in the template folder.
	 */
	public Set<Long> getTemplateExclusionSet() throws Exception {
		final String sql = "SELECT rowId FROM dbo.DMSAllChildren(1184609337)";
		return new HashSet<>(getObjectRowIds(sql));
	}

	/**
	 * Gets a list of all objects selected by query.
	 * (Query must select rowId as column #1, other columns are ignored);
	 */
	public List<Long> getObjectRowIds(final String sql, final StatementParameters params) throws Exception {
		final List<Long> result = new ArrayList<>(1024);
		final TransactionSource source = new ContextSource(DMSConstants.DMS_DATABASE) {
			@Override
			protected void loadImpl(final TransactionContext context) throws SQLException {
				doQuery(sql, params, new RowHandler() {
					@Override
					public void handleRow(final JDBCResultSet resultSet) throws SQLException {
						final long rowId = resultSet.getLong(1);
						if (resultSet.wasNull())
							return;
						result.add(Long.valueOf(rowId));
					}
				});
			}
		};
		final TransactionContext context = new TransactionContext();
		context.load(source);
		result.trimToSize();
		return result;
	}

	/**
	 * Renames DMSObject, includes creating audit trail entry
	 */
	public void renameObject(final long rowId, final String newName) throws IOException {
		DMSContextSink sink = new DMSContextSink(server) {
			@Override
			protected void storeImpl(Iterator<Model> modelItr, TransactionContext context) throws SQLException {
				// load existing object
				AtomicReference<DMSObjectPojo> objectRef = new AtomicReference<>();
				final StringMaker select = StringMaker.obtain();
				OM_OBJECT.appendSelectSQL(select, this);
				select.append(" WHERE ");
				select.append(TM_OBJECT.getColumn(DMSObjectMeta.ATR_ROWID));
				select.append(" = ?");
				StatementParameters params = new StatementParameters(1);
				params.add(rowId);
				doQuery(select.release(), params, new RowHandler() {
					@Override
					public void handleRow(final JDBCResultSet resultSet) throws SQLException {
						objectRef.set(OM_OBJECT.loadRow(context, resultSet));
					}
				});

				// update the object
				final DMSObjectPojo object = objectRef.get();
				String oldName = object.getObjectname();
				if (Strings.equals(oldName, newName)) {
					// no need to rename, abort
					return;
				}
				object.setObjectname(newName);
				OM_OBJECT.update(this, object);
				AuditTrailBatch auditTrail = new AuditTrailBatch();
				auditTrail.add(object.getPKValue(), null, DMSAuditCode.RENAME,
						"Update", oldName, newName);
				auditTrail.insertBatch(this);
			}
		};
		TransactionContext context = new TransactionContext();
		context.store(sink);
	}

	/**
	 * Selects all Folders with the given structure types
	 */
	public List<Long> getTypedFolders(final DMSPropertyCode property, final AbstractCode ... values) throws Exception {
		final StringMaker sql = StringMaker.obtain();
		final Column oRowId = TM_OBJECT.getColumn(DMSObjectMeta.ATR_ROWID);
		sql.append("SELECT O.", oRowId, " FROM ");
		sql.append(TM_OBJECT.getTableSpecifier(), " O, ");
		sql.append(TM_PROPERTY.getTableSpecifier(), " P ");
		sql.append("WHERE");
		sql.append(" O.", oRowId, " = P.", TM_PROPERTY.getColumn(DMSPropertyMeta.ATR_OBJECT_ROWID), " AND");
		sql.append(" O.", TM_OBJECT.getColumn(DMSObjectMeta.ATR_TYPECODE), " = ? AND");
		sql.append(" P.", TM_PROPERTY.getColumn(DMSPropertyMeta.ATR_PROPERTYCODE), " = ? AND");
		sql.append(" P.", TM_PROPERTY.getColumn(DMSPropertyMeta.ATR_PROPERTYVALUE), " IN ");
		JDBCBase.prepareIn(sql, values.length);
		final StatementParameters params = new StatementParameters();
		params.add(DMSObjectType.FOLDER);
		params.add(property);
		for (final AbstractCode code : values) {
			params.add(Strings.toString(code.getKey()));
		}
		return getObjectRowIds(sql.release(), params);
	}

}

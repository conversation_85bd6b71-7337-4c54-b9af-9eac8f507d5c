package ch.eisenring.dms.server;

import ch.eisenring.dms.shared.DMSConstants;
import ch.eisenring.dms.shared.locking.DMSObjectLock;
import ch.eisenring.dms.shared.model.meta.DMSBinaryMeta;
import ch.eisenring.dms.shared.model.meta.DMSBlobMeta;
import ch.eisenring.dms.shared.model.meta.DMSObjectLockMeta;
import ch.eisenring.dms.shared.model.meta.DMSObjectMeta;
import ch.eisenring.dms.shared.model.meta.DMSPropertyMeta;
import ch.eisenring.dms.shared.model.metaold.DMSAuditTrailMetaModel;
import ch.eisenring.dms.shared.model.pojo.DMSBinaryPojo;
import ch.eisenring.dms.shared.model.pojo.DMSObjectPojo;
import ch.eisenring.dms.shared.model.pojo.DMSPropertyPojo;
import ch.eisenring.dms.shared.model.record.DMSBlob;
import ch.eisenring.model.engine.jdbc.JDBCObjectMapper;
import ch.eisenring.model.engine.jdbc.SingleTableMapping;
import ch.eisenring.model.engine.jdbc.TableMapping;
import ch.eisenring.model.mapping.ObjectMapping;
import ch.eisenring.model.mapping.POJOMapping;

/**
 * DMS Pojo mapping
 */
public interface DMSMapping {
	
	// --------------------------------------------------------------
	// ---
	// --- DMSObject mappings
	// ---
	// --------------------------------------------------------------
	SingleTableMapping TM_OBJECT = TableMapping.get(DMSObjectMeta.METACLASS, DMSObjectMeta.TABLE);
	ObjectMapping<DMSObjectPojo> PM_OBJECT = POJOMapping.get(DMSObjectMeta.METACLASS, DMSObjectPojo.class);
	JDBCObjectMapper<DMSObjectPojo> OM_OBJECT = JDBCObjectMapper.get(TM_OBJECT, PM_OBJECT);

	// --------------------------------------------------------------
	// ---
	// --- DMSProperty mappings
	// ---
	// --------------------------------------------------------------
	SingleTableMapping TM_PROPERTY = TableMapping.get(DMSPropertyMeta.METACLASS, DMSConstants.DMS_DATABASE);
	ObjectMapping<DMSPropertyPojo> PM_PROPERTY = POJOMapping.get(DMSPropertyMeta.METACLASS, DMSPropertyPojo.class);
	JDBCObjectMapper<DMSPropertyPojo> OM_PROPERTY = JDBCObjectMapper.get(TM_PROPERTY, PM_PROPERTY);

	// --------------------------------------------------------------
	// ---
	// --- DMSBinary mappings
	// ---
	// --------------------------------------------------------------
	SingleTableMapping TM_BINARY = TableMapping.get(DMSBinaryMeta.METACLASS, DMSConstants.DMS_DATABASE);
	ObjectMapping<DMSBinaryPojo> PM_BINARY = POJOMapping.get(DMSBinaryMeta.METACLASS, DMSBinaryPojo.class);
	JDBCObjectMapper<DMSBinaryPojo> OM_BINARY = JDBCObjectMapper.get(TM_BINARY, PM_BINARY);

	// --------------------------------------------------------------
	// ---
	// --- DMSBlob mappings
	// ---
	// --------------------------------------------------------------
	SingleTableMapping TM_BLOB = TableMapping.get(DMSBlobMeta.METACLASS, DMSConstants.DMS_DATABASE);
	ObjectMapping<DMSBlob> PM_BLOB = POJOMapping.get(DMSBlobMeta.METACLASS, DMSBlob.class);
	JDBCObjectMapper<DMSBlob> OM_BLOB = JDBCObjectMapper.get(TM_BLOB, PM_BLOB);

	// --------------------------------------------------------------
	// ---O
	// --- DMSObjectLock mappings
	// ---
	// --------------------------------------------------------------
	SingleTableMapping TM_LOCK = TableMapping.get(DMSObjectLockMeta.METACLASS, DMSConstants.DMS_DATABASE);
	ObjectMapping<DMSObjectLock> PM_LOCK = POJOMapping.get(DMSObjectLockMeta.METACLASS, DMSObjectLock.class);
	JDBCObjectMapper<DMSObjectLock> OM_LOCK = JDBCObjectMapper.get(TM_LOCK, PM_LOCK);

	// --------------------------------------------------------------
	// ---
	// --- AuditTrail mappings
	// ---
	// --------------------------------------------------------------
	SingleTableMapping TM_AUDIT = TableMapping.get(DMSAuditTrailMetaModel.METACLASS, DMSAuditTrailMetaModel.TABLE);

}

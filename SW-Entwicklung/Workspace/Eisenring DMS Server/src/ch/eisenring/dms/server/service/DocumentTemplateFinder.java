package ch.eisenring.dms.server.service;

import java.io.IOException;
import java.sql.SQLException;
import java.util.concurrent.atomic.AtomicReference;

import ch.eisenring.app.server.model.ContextSource;
import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.datatypes.strings.Msg;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.io.binary.BinaryHolder;
import ch.eisenring.core.util.file.FileUtil;
import ch.eisenring.dms.server.DMSServer;
import ch.eisenring.dms.server.functions.SQLFunctions;
import ch.eisenring.dms.service.DMSServiceException;
import ch.eisenring.dms.service.DMSServiceResultCode;
import ch.eisenring.dms.service.api.DMSDocumentImage;
import ch.eisenring.dms.service.codetables.DMSDocumentTemplateCode;
import ch.eisenring.dms.service.codetables.DMSObjectStatus;
import ch.eisenring.dms.service.codetables.DMSPropertyCode;
import ch.eisenring.dms.shared.DMSConstants;
import ch.eisenring.dms.shared.codetables.DMSCompressionCode;
import ch.eisenring.dms.shared.model.metaold.DMSBinaryMetaModel;
import ch.eisenring.dms.shared.model.metaold.DMSObjectMetaModel;
import ch.eisenring.dms.shared.model.metaold.DMSPropertyMetaModel;
import ch.eisenring.jdbc.JDBCResultSet;
import ch.eisenring.jdbc.StatementWrapper;
import ch.eisenring.model.TransactionContext;
import ch.eisenring.model.TransactionSource;
import ch.eisenring.model.engine.jdbc.SingleTableMapping;
import ch.eisenring.model.engine.jdbc.TableMapping;

class DocumentTemplateFinder {

	final static SingleTableMapping OBJECT = TableMapping.get(DMSObjectMetaModel.METACLASS, DMSObjectMetaModel.TABLE);
	final static SingleTableMapping PROPERTY = TableMapping.get(DMSPropertyMetaModel.METACLASS, DMSPropertyMetaModel.TABLE);
	final static SingleTableMapping BINARY = TableMapping.get(DMSBinaryMetaModel.METACLASS, DMSBinaryMetaModel.TABLE);

	protected final DMSServer server;

	public DocumentTemplateFinder(final DMSServer server) {
		this.server = server;
	}

	public DMSDocumentImage getTemplate(final DMSDocumentTemplateCode code) throws DMSServiceException {
		if (AbstractCode.isNull(code)) {
			final String message = Msg.mk("{} ist kein gültiger Template-Code", code);
			throw new DMSServiceException(DMSServiceResultCode.INVALID_PARAMETER, message);
		}
		final AtomicReference<DMSDocumentImage> result = new AtomicReference<DMSDocumentImage>();
		final AtomicReference<DMSServiceException> error = new AtomicReference<DMSServiceException>();
		final TransactionSource source = new ContextSource(DMSConstants.DMS_DATABASE) {
			@Override
			protected void loadImpl(final TransactionContext context) throws SQLException {
				final StringMaker sql = StringMaker.obtain(512);
				sql.append("SELECT O.");
				sql.append(OBJECT.getPrimaryKeyColumn());
				sql.append(", B.");
				sql.append(BINARY.getPrimaryKeyColumn());
				sql.append(", O.");
				sql.append(OBJECT.getColumn(DMSObjectMetaModel.ATR_OBJECTNAME));
				sql.append(", B.");
				sql.append(BINARY.getColumn(DMSBinaryMetaModel.ATR_FILEEXTENSION));
				sql.append(", B.");
				sql.append(BINARY.getColumn(DMSBinaryMetaModel.ATR_BINARYDATA));
				sql.append(", B.");
				sql.append(BINARY.getColumn(DMSBinaryMetaModel.ATR_MADEON));
				sql.append(", B.");
				sql.append(BINARY.getColumn(DMSBinaryMetaModel.ATR_DCMCODE));
				sql.append(", O.");
				sql.append(OBJECT.getColumn(DMSObjectMetaModel.ATR_STATUSCODE));
				sql.append(" FROM ");
				qualifyTableName(sql, PROPERTY.getTableSpecifier()); sql.append(" P");
				sql.append(" LEFT JOIN ");
				qualifyTableName(sql, OBJECT.getTableSpecifier());
				sql.append(" O ON (O.");
				sql.append(OBJECT.getPrimaryKeyColumn());
				sql.append(" = P.");
				sql.append(PROPERTY.getColumn(DMSPropertyMetaModel.ATR_OBJECT_ROWID));
				sql.append(") LEFT JOIN ");
				qualifyTableName(sql, BINARY.getTableSpecifier());
				sql.append(" B ON (B.");
				sql.append(BINARY.getPrimaryKeyColumn());
				sql.append(" = ");
				SQLFunctions.ACTIVE_OBJECT_VERSION.appendCall(sql, this,
						Strings.concat("P.", PROPERTY.getColumn(DMSPropertyMetaModel.ATR_OBJECT_ROWID)));
				sql.append(") WHERE P.");
				sql.append(PROPERTY.getColumn(DMSPropertyMetaModel.ATR_PROPERTYCODE));
				sql.append(" = ? AND P.");
				sql.append(PROPERTY.getColumn(DMSPropertyMetaModel.ATR_PROPERTYVALUE));
				sql.append(" = ?");
				final StatementWrapper select = prepareStatement(sql.release());
				try {
					select.setObject(1, DMSPropertyCode.DOCUMENT_TEMPLATE);
					select.setObject(2, Strings.toString((Object) AbstractCode.getKey(code, null)));
					final JDBCResultSet resultSet = select.executeQuery();
					try {
						if (resultSet.next()) {
							final long objectRowId = resultSet.getLong(1);
							final long versionRowId = resultSet.getLong(2);
							final String objectname = resultSet.getString(3);
							final String extension = resultSet.getString(4);
							BinaryHolder filedata = resultSet.getBinary(5);
							final long lastModified = resultSet.getLongDate(6);
							final DMSCompressionCode dcmCode = resultSet.getCode(7, DMSCompressionCode.class);
							final DMSObjectStatus status = resultSet.getCode(8, DMSObjectStatus.class);
							final String filename = FileUtil.addFileExtension(objectname, extension);
							try {
								filedata = dcmCode.decompress(filedata);
								result.set(new DMSDocumentImage(objectRowId, versionRowId, filename, filedata, lastModified, status));
								if (resultSet.next()) {
									final String message = Msg.mk("Mehr als ein Template-Dokument für code {} gefunden", code);
									error.set(new DMSServiceException(DMSServiceResultCode.TOO_MANY_RESULTS, message));
								}
							} catch (final IOException e) {
								final String message = Msg.mk("Fehler bei Laden für Template-Code {}: {}", code, e.getMessage());
								error.set(new DMSServiceException(DMSServiceResultCode.INTERNAL_ERROR, message));
							}
						} else {
							final String message = Msg.mk("Kein Template für {} gefunden", code);
							error.set(new DMSServiceException(DMSServiceResultCode.OBJECT_NOT_FOUND, message));
						}
							
					} finally {
						closeSilent(resultSet);
					}
				} finally {
					closeSilent(select);
				}
			}
		};
		try {
			final TransactionContext context = new TransactionContext();
			context.load(source);
		} catch (final IOException e) {
			final String message = Msg.mk("Fehler bei Laden für Template-Code {}: {}", code, e.getMessage());
			error.set(new DMSServiceException(DMSServiceResultCode.GENERIC_ERROR, message));
		}
		if (error.get() != null)
			throw error.get();
		return result.get();
	}

}

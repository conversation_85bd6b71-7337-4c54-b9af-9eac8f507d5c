package ch.eisenring.dms.server.network;

import static ch.eisenring.dms.server.dao.DMSModelMapping.TM_OBJECT;
import static ch.eisenring.dms.shared.network.cmd.ObjectMessageConstants.ATTRIBUTES;
import static ch.eisenring.dms.shared.network.cmd.ObjectMessageConstants.CHILDREN;

import java.sql.SQLException;
import java.util.Iterator;

import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.dms.server.DMSServer;
import ch.eisenring.dms.server.audit.AuditTrailBatch;
import ch.eisenring.dms.server.dao.DMSContextSink;
import ch.eisenring.dms.service.codetables.DMSObjectStatus;
import ch.eisenring.dms.service.codetables.DMSObjectType;
import ch.eisenring.dms.shared.codetables.DMSAuditCode;
import ch.eisenring.dms.shared.model.metaold.DMSObjectMetaModel;
import ch.eisenring.dms.shared.model.record.DMSObjectModel;
import ch.eisenring.dms.shared.network.PacketSetObjectStatusReply;
import ch.eisenring.dms.shared.network.PacketSetObjectStatusRequest;
import ch.eisenring.dms.shared.network.cmd.ObjectMessageConstants;
import ch.eisenring.dms.shared.network.cmd.ObjectMessageSet;
import ch.eisenring.jdbc.JDBCResultSet;
import ch.eisenring.jdbc.StatementWrapper;
import ch.eisenring.model.Model;
import ch.eisenring.model.TransactionContext;
import ch.eisenring.model.engine.jdbc.ColumnHandler;
import ch.eisenring.model.engine.jdbc.ColumnSpecifier;
import ch.eisenring.model.engine.jdbc.TableSpecifier;
import ch.eisenring.network.PacketSink;
import ch.eisenring.network.packet.AbstractPacket;

public final class SetObjectStatusRequestHandler extends AbstractDMSPacketHandler {

	final static TableSpecifier TABLE_OBJ = TM_OBJECT.getTableSpecifier();
	final static ColumnSpecifier COLUMN_OBJ_ROWID = TM_OBJECT.getPrimaryKeyColumn();
	final static ColumnSpecifier COLUMN_OBJ_TYPE = TM_OBJECT.getColumn(DMSObjectMetaModel.ATR_TYPECODE);
	final static ColumnSpecifier COLUMN_OBJ_STATUS = TM_OBJECT.getColumn(DMSObjectMetaModel.ATR_STATUSCODE);
	final static ColumnSpecifier COLUMN_OBJ_PARENTID = TM_OBJECT.getColumn(DMSObjectMetaModel.ATR_PARENT_ROWID);
	
	public SetObjectStatusRequestHandler(final DMSServer server) {
		super(server, PacketSetObjectStatusRequest.class);
	}
	
	@Override
	public void handle(final AbstractPacket abstractPacket, final PacketSink sink) {
		final PacketSetObjectStatusRequest request = (PacketSetObjectStatusRequest) abstractPacket;
		final ObjectMessageSet changeMessages = new ObjectMessageSet();
		// change status of object and all its children
		final TransactionContext context = new TransactionContext();
		final DMSContextSink contextSink = new DMSContextSink(server) {
			@Override
			public void storeImpl(final Iterator<Model> modelItr, final TransactionContext context) throws SQLException {
				final AuditTrailBatch auditBatch = new AuditTrailBatch();
				try {
					for (final Long objectRowId : request.getRowIdList()) {
						// find the object
						final DMSObjectStatus valueNew = request.getObjectStatus();
						// load the existing object
						DMSObjectModel object = loadObject(context, objectRowId, true, false);
						final DMSObjectStatus valueOld = object.getObjectStatus();
						if (valueNew.equals(valueOld)) {
							throw new SQLException(Strings.concat("Status of DMSObject[", objectRowId, "] already up to date"));
						}
						
						// update the status & reload the object
						updateObject(objectRowId, valueOld, valueNew, auditBatch);
						object = loadObject(context, objectRowId, true, true);
		
						changeMessages.addFlags(object.getParentRowId(), ObjectMessageConstants.CHILDREN);
						changeMessages.addFlags(object.getPKValue(), ObjectMessageConstants.ATTRIBUTES);
						
						// update the parent(s) where required
						updateParent(object.getParentRowId(), valueNew, auditBatch);
						if (DMSObjectType.FOLDER.equals(object.getType())) {
							// update the children as required
							updateChildren(objectRowId, valueNew, request.getPromoteChildren(), auditBatch);
						}
					}
				} finally {
					closeSilent(selectParent);
					// execute all updates
					updateObject.executeBatch();
					auditBatch.insertBatch(this);
					closeSilent(updateObject);
				}
			}

			/**
			 * Updates all children that are in a lower state than the new status
			 * and if promoteChildren is true, also all children in a higher status.
			 */
			private void updateChildren(final Long parentRowId,
					                    final DMSObjectStatus newStatus,
					                    final boolean promoteChildren,
					                    final AuditTrailBatch auditBatch) throws SQLException {
				// build select statement
				final StringMaker sql = StringMaker.obtain(256);
				sql.append("SELECT ");
				sql.append(COLUMN_OBJ_ROWID);
				sql.append(", ");
				sql.append(COLUMN_OBJ_STATUS);
				sql.append(", ");
				sql.append(COLUMN_OBJ_TYPE);
				sql.append(" FROM ");
				qualifyTableName(sql, TABLE_OBJ);
				sql.append(" WHERE ");
				sql.append(COLUMN_OBJ_PARENTID);
				sql.append(" = ?");
				final StatementWrapper selectChildren = prepareStatement(sql.release());
				try {
					selectChildren.setObject(1, parentRowId);
					final JDBCResultSet resultSet = selectChildren.executeQuery();
					try {
						while (resultSet.next()) {
							final Long objectRowId = resultSet.getLong(1);
							final DMSObjectStatus oldStatus = (DMSObjectStatus) ColumnHandler.getColumnStatic(resultSet, DMSObjectMetaModel.ATR_STATUSCODE, 2);
							final DMSObjectType type = (DMSObjectType) ColumnHandler.getColumnStatic(resultSet, DMSObjectMetaModel.ATR_TYPECODE, 3);
							if (oldStatus.isLower(newStatus)) {
								// demote this child
								updateObject(objectRowId, oldStatus, newStatus, auditBatch);
								changeMessages.addFlags(objectRowId, ATTRIBUTES);
								changeMessages.addFlags(parentRowId, CHILDREN);
							} else if (promoteChildren && oldStatus.isHigher(newStatus)) {
								// promote this child
								updateObject(objectRowId, oldStatus, newStatus, auditBatch);
								changeMessages.addFlags(objectRowId, ATTRIBUTES);
								changeMessages.addFlags(parentRowId, CHILDREN);
							}
							if (DMSObjectType.FOLDER.equals(type)) {
								// also update the children of the child
								updateChildren(objectRowId, newStatus, promoteChildren, auditBatch);
							}
						}
					} finally {
						closeSilent(resultSet);
					}
				} finally {
					closeSilent(selectChildren);
				}
			}
			private StatementWrapper selectParent;

			/**
			 * Updates all parents that are in a state higher than the new status
			 */
			private void updateParent(Long parentRowId, final DMSObjectStatus newStatus, final AuditTrailBatch auditBatch) throws SQLException {
				if (parentRowId == null)
					return;
				if (selectParent == null) {
					final StringMaker sql = StringMaker.obtain(128);
					sql.append("SELECT ");
					sql.append(COLUMN_OBJ_STATUS);
					sql.append(", ");
					sql.append(COLUMN_OBJ_PARENTID);
					sql.append(" FROM ");
					qualifyTableName(sql, TABLE_OBJ);
					sql.append(" WHERE ");
					sql.append(COLUMN_OBJ_ROWID);
					sql.append(" = ?");
					selectParent = prepareStatement(sql.release());
				}
				selectParent.setObject(1, parentRowId);
				final JDBCResultSet resultSet = selectParent.executeQuery();
				try {
					if (!resultSet.next())
						throw new SQLException("Es existiert kein Objekt mit der Id " + parentRowId);
					final DMSObjectStatus parentStatus = (DMSObjectStatus) ColumnHandler.getColumnStatic(resultSet, DMSObjectMetaModel.ATR_STATUSCODE, 1); 
					final boolean updated;
					if (parentStatus.isHigher(newStatus)) {
						// update the parent status
						updateObject(parentRowId, parentStatus, newStatus, auditBatch);
						updated = true;
					} else {
						updated = false;
					}
					// get the parent of the parent...
					parentRowId = (Long) ColumnHandler.getColumnStatic(resultSet, DMSObjectMetaModel.ATR_PARENT_ROWID, 2);
					if (updated)
						changeMessages.addFlags(parentRowId, CHILDREN);						
				} finally {
					closeSilent(resultSet);
				}
				updateParent(parentRowId, newStatus, auditBatch);
			}

			// ------------------------------------------------------
			private StatementWrapper updateObject;
			
			private void updateObject(final Long objectRowId,
					                  final DMSObjectStatus oldStatus,
					                  final DMSObjectStatus newStatus,
					                  final AuditTrailBatch auditBatch) throws SQLException {
				if (objectRowId == null)
					return;
				if (updateObject == null) {
					// build update statement
					final StringMaker sql = StringMaker.obtain(128);
					sql.append("UPDATE "); qualifyTableName(sql, TABLE_OBJ);
					sql.append(" SET ");
					sql.append(COLUMN_OBJ_STATUS);
					sql.append(" = ? WHERE ");
					sql.append(COLUMN_OBJ_ROWID);
					sql.append(" = ?");
					updateObject = prepareStatement(sql.release());
				}
				updateObject.setObject(1, newStatus);
				updateObject.setObject(2, objectRowId);
				updateObject.addBatch();
				changeMessages.addFlags(objectRowId, ATTRIBUTES | CHILDREN);
				auditBatch.add(objectRowId, null, DMSAuditCode.STATUS, request.getUser(), oldStatus, newStatus);
			}
		};
		try {
			context.store(contextSink);
			final PacketSetObjectStatusReply reply = PacketSetObjectStatusReply.create(request, changeMessages);
			sink.sendPacket(reply, false);
			broadcastMessages(reply.getMessages());
		} catch (final Exception e) {
			final PacketSetObjectStatusReply reply = PacketSetObjectStatusReply.create(request, new ErrorMessage(e));
			sink.sendPacket(reply, false);
		}
	}

}

package ch.eisenring.dms.server.network;

import static ch.eisenring.dms.server.DMSMapping.OM_OBJECT;
import static ch.eisenring.dms.server.DMSMapping.OM_PROPERTY;
import static ch.eisenring.dms.server.DMSMapping.TM_OBJECT;
import static ch.eisenring.dms.server.DMSMapping.TM_PROPERTY;

import java.sql.SQLException;
import java.util.concurrent.atomic.AtomicReference;

import ch.eisenring.app.server.model.ContextSource;
import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.dms.server.DMSServer;
import ch.eisenring.dms.server.functions.SQLFunctions;
import ch.eisenring.dms.service.api.DMSObject;
import ch.eisenring.dms.shared.DMSConstants;
import ch.eisenring.dms.shared.model.api.DMSProperty;
import ch.eisenring.dms.shared.model.meta.DMSPropertyMeta;
import ch.eisenring.dms.shared.model.pojo.DMSPropertyAccessorPojo;
import ch.eisenring.dms.shared.network.PacketGetPropertiesReply;
import ch.eisenring.dms.shared.network.PacketGetPropertiesRequest;
import ch.eisenring.jdbc.JDBCResultSet;
import ch.eisenring.jdbc.RowHandler;
import ch.eisenring.jdbc.StatementParameters;
import ch.eisenring.model.TransactionContext;
import ch.eisenring.model.TransactionSource;
import ch.eisenring.model.engine.jdbc.ColumnSpecifier;
import ch.eisenring.network.PacketSink;
import ch.eisenring.network.packet.AbstractPacket;

/**
 * Collects all properties for an object
 */
public final class GetPropertiesRequestHandler extends AbstractDMSPacketHandler {

	final static ColumnSpecifier ROWID = TM_PROPERTY.getPrimaryKeyColumn();
	final static ColumnSpecifier OBJECTID = TM_PROPERTY.getColumn(DMSPropertyMeta.ATR_OBJECT_ROWID);
	final static ColumnSpecifier CODE = TM_PROPERTY.getColumn(DMSPropertyMeta.ATR_PROPERTYCODE);
	final static ColumnSpecifier VALUE = TM_PROPERTY.getColumn(DMSPropertyMeta.ATR_PROPERTYVALUE);
	final static ColumnSpecifier LEVEL = ColumnSpecifier.get(TM_PROPERTY.getTableSpecifier(), "Level");
	
	private GetPropertiesRequestHandler(final DMSServer server) {
		super(server, PacketGetPropertiesRequest.class);
	}
	
	@Override
	public PacketGetPropertiesReply handle(final AbstractPacket packet) {
		final PacketGetPropertiesRequest request = (PacketGetPropertiesRequest) packet;
		return handle(server, request);
	}

	@Override
	public void handle(final AbstractPacket packet, final PacketSink sink) {
		final PacketGetPropertiesRequest request = (PacketGetPropertiesRequest) packet;
		final PacketGetPropertiesReply reply = handle(server, request);
		sink.sendPacket(reply);
	}
	
	private static PacketGetPropertiesReply handle(final DMSServer server, final PacketGetPropertiesRequest request) {
		try {
			final PacketGetPropertiesReply reply = handleImpl(server, request);
			if (reply != null)
				return reply;
			return PacketGetPropertiesReply.create(request, ErrorMessage.ERROR);
		} catch (final Exception e) {
			return PacketGetPropertiesReply.create(request, new ErrorMessage(e));
		}
	}

	private static PacketGetPropertiesReply handleImpl(final DMSServer server,
			final PacketGetPropertiesRequest request) throws Exception {
		final long objectRowId = request.getObjectRowId();
		final AtomicReference<DMSObject> sourceObject = new AtomicReference<>();
		final List<DMSProperty> properties = new ArrayList<>();
		final TransactionSource source = new ContextSource(DMSConstants.DMS_DATABASE) {
			@Override
			protected void loadImpl(final TransactionContext context) throws SQLException {
				final StatementParameters params = new StatementParameters(1);
				params.add(objectRowId);
				{ // determine the object type
					final StringMaker sql = StringMaker.obtain();
					OM_OBJECT.appendSelectSQL(sql, this);
					sql.append(" WHERE ");
					sql.append(TM_OBJECT.getPrimaryKeyColumn());
					sql.append(" = ?");
					doQuery(sql.release(), params, new RowHandler() {
						@Override
						public void handleRow(final JDBCResultSet resultSet) throws SQLException {
							final DMSObject object = OM_OBJECT.loadRow(context, resultSet);
							sourceObject.set(object);
						}
					});
				}
				{ // select properties using stored function!
					final StringMaker sql = StringMaker.obtain();
					sql.append("SELECT ");
					sql.append(ROWID);
					sql.append(", ");
					sql.append(OBJECTID);
					sql.append(", ");
					sql.append(CODE);
					sql.append(", ");
					sql.append(VALUE);
					sql.append(", ");
					sql.append(LEVEL);
					sql.append(" FROM ");
					SQLFunctions.ALL_PROPERTIES.appendCall(sql, this, "?");
					sql.append(" ORDER BY ");
					sql.append(LEVEL);
					sql.append(" DESC");
					doQuery(sql.release(), params, new RowHandler() {
						@Override
						public void handleRow(final JDBCResultSet resultSet) throws SQLException {
							final DMSProperty property = OM_PROPERTY.loadRow(context, resultSet);
							properties.add(property);
						}
					});

				}
			}
		};
		final TransactionContext context = new TransactionContext();
		context.load(source);
		// create accessor and reply
		final DMSPropertyAccessorPojo accessor = DMSPropertyAccessorPojo.create(
				sourceObject.get(), properties);
		return PacketGetPropertiesReply.create(request, accessor);
	}

}

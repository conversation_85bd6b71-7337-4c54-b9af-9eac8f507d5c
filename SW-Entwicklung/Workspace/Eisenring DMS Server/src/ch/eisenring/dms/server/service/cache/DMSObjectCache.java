//package ch.eisenring.dms.server.service.cache;
//
//import static ch.eisenring.dms.server.DMSMapping.OM_OBJECT;
//
//import java.sql.SQLException;
//import java.util.Iterator;
//import java.util.concurrent.atomic.AtomicLong;
//import java.util.concurrent.atomic.AtomicReference;
//
//import ch.eisenring.app.server.model.ContextSource;
//import ch.eisenring.app.shared.CoreTickListener;
//import ch.eisenring.core.HashUtil;
//import ch.eisenring.core.collections.Map;
//import ch.eisenring.core.collections.impl.HashMap;
//import ch.eisenring.core.datatypes.strings.StringMaker;
//import ch.eisenring.core.threading.ThreadCoreLocal;
//import ch.eisenring.dms.service.api.DMSObject;
//import ch.eisenring.dms.shared.DMSConstants;
//import ch.eisenring.jdbc.StatementParameters;
//import ch.eisenring.model.TransactionContext;
//import ch.eisenring.model.TransactionSource;
//
///**
// * Caches DMSObjects
// */
//public final class DMSObjectCache implements CoreTickListener {
//
//	/**
//	 * The maximum age a cache entry can be before being flushed.
//	 * Measured in milliseconds.
//	 */
//	public final static int MAX_CACHED_AGE = 60_000;
//
//	/**
//	 * The maximum number of objects that may be cached.
//	 */
//	private int maxSize;
//
//	/**
//	 * The size to trim the cache down to when the cache gets full.
//	 */
//	private int trimSize;
//
//	/**
//	 * Entry id generator
//	 */
//	private final static AtomicLong ENTRY_ID_GENERATOR = new AtomicLong(Long.MIN_VALUE);
//	
//	// --------------------------------------------------------------
//	// ---
//	// --- Key by row id 
//	// ---
//	// --------------------------------------------------------------
//	static class RowId {
//		long rowId;
//		
//		RowId() {}
//		
//		RowId(RowId rowId) { this.rowId = rowId.rowId; }
//
//		@Override
//		public int hashCode() {
//			return HashUtil.hashCode(rowId);
//		}
//	
//		@Override
//		public boolean equals(final Object o) {
//			return o instanceof RowId && ((RowId) o).rowId == rowId;
//		}
//	}
//	
//	static class Entry {
//		/** Timestamp when the entry was last updated */
//		long timestamp;
//
//		/** entry id */
//		long entryId;
//		
//		/** The cached object, or null if this entry represents a db miss */
//		DMSObject object;
//
//		public Entry(final DMSObject object) {
//			update(object);
//		}
//
//		void update(final DMSObject object) {
//			this.timestamp = System.currentTimeMillis();
//			this.object = object;
//		}
//
//		public boolean isValid(final int maxAgeMillis) {
//			return maxAgeMillis > 0 && System.currentTimeMillis() <= timestamp + maxAgeMillis;
//		}
//	}
//
//	final static ThreadCoreLocal<RowId> ROWID_LOOKUP =
//			new ThreadCoreLocal<>(() -> new RowId());
//
//	public DMSObjectCache() {
//		setSize(16 << 10);
//	}
//
//	private static RowId getLookupKey(final long rowId) {
//		final RowId key = ROWID_LOOKUP.get();
//		key.rowId = rowId;
//		return key;
//	}
//
//	// --------------------------------------------------------------
//	// ---
//	// --- API 
//	// ---
//	// --------------------------------------------------------------
//	/**
//	 * Sets the caches maximum size. If the cache is larger than
//	 * the new size this will immediately trim the cache.
//	 */
//	public void setSize(final int size) {
//		synchronized (cacheMap) {
//			final int oldMax = this.maxSize;
//			this.maxSize = Math.max(size, 0);
//			this.trimSize = Math.max((int) (size * 0.9), 0);
//			if (oldMax > this.maxSize) {
//				trim();
//			}
//		}
//	}
//	
//	/**
//	 * Gets or loads the DMSObject identified by rowId.
//	 * 
//	 * The cache decides the maximum age the object can have.
//	 */
//	public DMSObject get(final long rowId) {
//		return get(rowId, MAX_CACHED_AGE);
//	}
//	
//	/**
//	 * Gets or loads the DMSObject identified by rowId.
//	 * 
//	 * A maxAgeMillis parameter of 0 indicates that the cache
//	 * is to be bypassed unconditionally, meaning the object
//	 * will be loaded from the database in any case, even if
//	 * a cached object is available.
//	 * 
//	 * Otherwise, the cached object will be returned if the
//	 * age is less or equal to the given maxAgeMillis.
//	 */
//	public DMSObject get(final long rowId, final int maxAgeMillis) {
//		final RowId key = getLookupKey(rowId);
//		if (maxAgeMillis > 0) {
//			// lookup in cache
//			synchronized (cacheMap) {
//				final Entry entry = cacheMap.get(key);
//				if (entry != null && entry.isValid(maxAgeMillis))
//					return entry.object;
//			}
//		}
//		// not in cache or no longer valid
//		return load(key);
//	}
//	
//	/**
//	 * Puts the given object into the cache.
//	 */
//	public void put(final DMSObject object) {
//		if (object == null || maxSize <= 0)
//			return;
//		final RowId key = getLookupKey(object.getPKValue());
//		synchronized (cacheMap) {
//			Entry entry = cacheMap.get(key);
//			if (entry == null) {
//				cacheMap.put(new RowId(key), new Entry(object));
//			} else {
//				entry.update(object);
//			}
//			checkTrim();
//		}
//	}
//
//	/**
//	 * Remove given rowId from the cache
//	 */
//	public void flush(final long rowId) {
//		if (rowId == 0)
//			return;
//		final RowId key = getLookupKey(rowId);
//		synchronized (cacheMap) {
//			cacheMap.remove(key);
//		}
//	}
//
//	// --------------------------------------------------------------
//	// ---
//	// --- Internal cache 
//	// ---
//	// --------------------------------------------------------------
//	/**
//	 * Map associating keys with objects
//	 */
//	private final Map<RowId, Entry> cacheMap = new HashMap<>();
//
//	/**
//	 * Load object and update cache
//	 */
//	private DMSObject load(final RowId key) {
//		final DMSObject object = loadImpl(key.rowId);
//		if (maxSize > 0) {
//			synchronized (cacheMap) {
//				// update the cache with the object
//				Entry entry = cacheMap.get(key);
//				if (entry == null) {
//					cacheMap.put(new RowId(key), new Entry(object));
//				} else {
//					entry.update(object);
//				}
//				checkTrim();
//			}
//		}
//		return object;
//	}
//
//	/**
//	 * Checks if the cache needs to be trimmed and trims as needed.
//	 * Must only be called while holding cache monitor!
//	 */
//	private void checkTrim() {
//		if (cacheMap.size() > maxSize)
//			trim();
//	}
//
//	/**
//	 * Trims the cache down and purges expired age entries.
//	 * Must only be called while holding cache monitor!
//	 */
//	private void trim() {
//		// drop expired age entries
//		final long ageBoundary = System.currentTimeMillis() - MAX_CACHED_AGE;
//		final long idBoundary = ENTRY_ID_GENERATOR.get() - trimSize;
//		final Iterator<Map.Entry<RowId, Entry>> i = cacheMap.entrySet().iterator();
//		while (i.hasNext()) {
//			final Map.Entry<RowId, Entry> e = i.next();
//			final Entry entry = e.getValue();
//			if (entry.timestamp < ageBoundary || entry.entryId < idBoundary)
//				i.remove();
//		}
//	}
//	
//	// --------------------------------------------------------------
//	// ---
//	// --- Database access
//	// ---
//	// --------------------------------------------------------------
//	private static DMSObject loadImpl(final long rowId) {
//		final AtomicReference<DMSObject> result = new AtomicReference<>();
//		final TransactionSource source = new ContextSource(DMSConstants.DMS_DATABASE) {
//			@Override
//			protected void loadImpl(final TransactionContext context) throws SQLException {
//				final StringMaker sql = StringMaker.obtain();
//				OM_OBJECT.appendSelectSQL(sql, this);
//				sql.append(" WHERE ");
//				sql.append(OM_OBJECT.getTableMapping().getPrimaryKeyColumn());
//				sql.append(" = ?");
//				final StatementParameters params = new StatementParameters(1);
//				params.add(rowId);
//				doQuery(sql.release(), params, (resultSet) -> {
//					final DMSObject object = OM_OBJECT.loadRow(context,  resultSet);
//					result.set(object);
//				});
//			}
//		};
//		try {
//			final TransactionContext context = new TransactionContext();
//			context.load(source);
//			return result.get();
//		} catch (final Exception e) {
//			return null;
//		}
//	}
//
//	// --------------------------------------------------------------
//	// ---
//	// --- CoreTickListener implementation
//	// ---
//	// --------------------------------------------------------------
//	private long nextDue = Long.MIN_VALUE;
//	
//	@Override
//	public String getName() {
//		return "DMSObjectCache";
//	}
//
//	@Override
//	public void tick() {
//		final long now = System.currentTimeMillis();
//		if (now < nextDue)
//			return;
//		nextDue = now;
//		synchronized (cacheMap) {
//			trim();
//		}
//	}
//
//}

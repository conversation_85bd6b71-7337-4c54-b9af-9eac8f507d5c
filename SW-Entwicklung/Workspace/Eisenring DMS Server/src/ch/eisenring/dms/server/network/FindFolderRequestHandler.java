package ch.eisenring.dms.server.network;

import static ch.eisenring.dms.server.dao.DMSModelMapping.TM_OBJECT;

import java.io.IOException;
import java.sql.SQLException;
import java.util.concurrent.atomic.AtomicInteger;

import ch.eisenring.app.server.model.ContextSource;
import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.dms.server.DMSServer;
import ch.eisenring.dms.service.api.DMSObject;
import ch.eisenring.dms.service.codetables.DMSObjectStatus;
import ch.eisenring.dms.service.codetables.DMSObjectType;
import ch.eisenring.dms.shared.DMSConstants;
import ch.eisenring.dms.shared.model.metaold.DMSObjectMetaModel;
import ch.eisenring.dms.shared.model.record.DMSObjectModel;
import ch.eisenring.dms.shared.network.PacketFindFolderReply;
import ch.eisenring.dms.shared.network.PacketFindFolderRequest;
import ch.eisenring.jdbc.JDBCUtil;
import ch.eisenring.jdbc.StatementParameters;
import ch.eisenring.model.TransactionContext;
import ch.eisenring.model.TransactionSource;
import ch.eisenring.model.engine.jdbc.TableMapping;
import ch.eisenring.network.PacketSink;
import ch.eisenring.network.packet.AbstractPacket;

public final class FindFolderRequestHandler extends AbstractDMSPacketHandler {

	private FindFolderRequestHandler(final DMSServer server) {
		super(server, PacketFindFolderRequest.class);
	}

	@Override
	public void handle(final AbstractPacket abstractPacket, final PacketSink sink) {
		final PacketFindFolderRequest request = (PacketFindFolderRequest) abstractPacket;
		final PacketFindFolderReply reply = handle(server, request);
		sink.sendPacket(reply, false);
	}

	public static PacketFindFolderReply handle(final DMSServer server, final AbstractPacket abstractPacket) {
		final PacketFindFolderRequest request = (PacketFindFolderRequest) abstractPacket;
		final AtomicInteger hiddenCount = new AtomicInteger(0);
		final TransactionSource contextSource = new ContextSource(DMSConstants.DMS_DATABASE) {
			@Override
			protected void loadImpl(final TransactionContext context) throws SQLException {
				String pattern = request.getPattern();
				if (Strings.isEmpty(pattern)) {
					throw new SQLException("Das Suchmuster darf nicht leer sein");
				} else if (Strings.length(pattern) - JDBCUtil.countWildcards(pattern) < 3) {
					throw new SQLException("Das Suchmuster muss mindestens 3 Zeichen enhalten die keine Platzhalter sind");
				} else if (JDBCUtil.containsWildcards(pattern)) {
					pattern = JDBCUtil.replaceWildcards(pattern);
				} else {
					pattern = Strings.concat(pattern, "%");
				}
				final DMSObjectStatus objectStatus = request.getObjectStatus();
				{ // select the folders with matching name
					final StringMaker sql = StringMaker.obtain(256);
					TM_OBJECT.appendSelectSQL(sql, this, TableMapping.NO_TABLE_ALIAS);
					sql.append(WITH_NOLOCK);
					sql.append(" WHERE ");
					sql.append(TM_OBJECT.getColumn(DMSObjectMetaModel.ATR_TYPECODE));
					sql.append(" = ? AND ");
					sql.append(TM_OBJECT.getColumn(DMSObjectMetaModel.ATR_STATUSCODE));
					sql.append(" <= ? AND ");
					sql.append(TM_OBJECT.getColumn(DMSObjectMetaModel.ATR_OBJECTNAME));
					sql.append(" LIKE ?");
					final StatementParameters params = new StatementParameters();
					params.add(DMSObjectType.FOLDER);
					params.add(objectStatus);
					params.add(pattern);
					doQuery(sql.release(), params, (resultSet) -> 
						{ TM_OBJECT.loadRow(context, resultSet); });
				}
				if (DMSObjectStatus.RETRACTED.isHigher(objectStatus)) {
					// count the folders with matching name which are hidden by their status
					final StringMaker sql = StringMaker.obtain(256);
					sql.append("SELECT COUNT(*) FROM ");
					qualifyTableName(sql, TM_OBJECT.getTableSpecifier());
					sql.append(WITH_NOLOCK);
					sql.append(" WHERE ");
					sql.append(TM_OBJECT.getColumn(DMSObjectMetaModel.ATR_TYPECODE));
					sql.append(" = ? AND ");
					sql.append(TM_OBJECT.getColumn(DMSObjectMetaModel.ATR_STATUSCODE));
					sql.append(" > ? AND ");
					sql.append(TM_OBJECT.getColumn(DMSObjectMetaModel.ATR_OBJECTNAME));
					sql.append(" LIKE ?");
					final StatementParameters params = new StatementParameters();
					params.add(DMSObjectType.FOLDER);
					params.add(objectStatus);
					params.add(pattern);
					doQuery(sql.release(), params, (resultSet) -> 
						{ hiddenCount.set(resultSet.getInt(1)); });
				} else {
					// no object can be hidden in this case
					hiddenCount.set(0);
				}
			}
		};
		try {
			final TransactionContext context = new TransactionContext();
			context.load(contextSource);
			final PacketFindFolderReply reply = PacketFindFolderReply.create(request, hiddenCount.get());
			final List<DMSObjectModel> objects = context.getModels(DMSObjectModel.class);
			for (final DMSObject object : objects) {
				reply.addObject(object);
			}
			return reply;
		} catch (final IOException e) {
			return PacketFindFolderReply.create(request, new ErrorMessage(e));
		}
	}

}

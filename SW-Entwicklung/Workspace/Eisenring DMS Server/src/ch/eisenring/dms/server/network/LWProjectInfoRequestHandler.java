package ch.eisenring.dms.server.network;

import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.collections.Map;
import ch.eisenring.core.collections.Set;
import ch.eisenring.core.collections.impl.ArraySet;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.dms.server.DMSServer;
import ch.eisenring.dms.shared.network.PacketLWProjectInfoReply;
import ch.eisenring.dms.shared.network.PacketLWProjectInfoRequest;
import ch.eisenring.logiware.LWSubjektDAO;
import ch.eisenring.logiware.code.pseudo.AVORTeamCode;
import ch.eisenring.logiware.code.pseudo.AbwicklungsartCode;
import ch.eisenring.logiware.code.service.LogiwareService;
import ch.eisenring.logiware.model.LWSubjektInfo;
import ch.eisenring.lw.condition.Factory;
import ch.eisenring.lw.meta.LWProjektMeta;
import ch.eisenring.lw.model.navigable.LWAuftrag;
import ch.eisenring.lw.model.navigable.LWObjectCache;
import ch.eisenring.lw.model.navigable.LWProjekt;
import ch.eisenring.lw.model.navigable.LWSubjekt;
import ch.eisenring.network.PacketSink;
import ch.eisenring.network.packet.AbstractPacket;

import java.sql.SQLException;
import java.util.Iterator;
import java.util.Optional;

public final class LWProjectInfoRequestHandler extends AbstractDMSPacketHandler {

    public LWProjectInfoRequestHandler(final DMSServer server) {
        super(server, PacketLWProjectInfoRequest.class);
    }

    @Override
    public void handle(final AbstractPacket packet, final PacketSink sink) {
        final PacketLWProjectInfoRequest request = (PacketLWProjectInfoRequest) packet;
        final PacketLWProjectInfoReply reply = handle(server, request);
        sink.sendPacket(reply, false);
    }

    public static PacketLWProjectInfoReply handle(final DMSServer server, final PacketLWProjectInfoRequest request) {
        PacketLWProjectInfoReply reply;
        try {
            reply = handleImpl(server, request);
            if (reply != null)
                return reply;
            reply = PacketLWProjectInfoReply.create(request, ErrorMessage.ERROR);
        } catch (final Exception e) {
            Logger.warn(e);
            reply = PacketLWProjectInfoReply.create(request, new ErrorMessage(e));
        }
        return reply;
    }

    protected static PacketLWProjectInfoReply handleImpl(final DMSServer server, final PacketLWProjectInfoRequest request) throws Exception {
        final PacketLWProjectInfoReply reply = PacketLWProjectInfoReply.create(request, ErrorMessage.OK);
        loadProjectInfo(server, reply);

        // only load subjekte if requested
        if ((reply.getFlags() & PacketLWProjectInfoReply.FLAGS_SUBJEKTE) != 0) {
            loadSubjektInfos(server, reply);
        }

        return reply;
    }

    protected static void loadProjectInfo(final DMSServer server, final PacketLWProjectInfoReply reply) throws Exception {
        final LogiwareService service = server.locateService(LogiwareService.class);
        final LWObjectCache cache = service.createObjectCache();
        final Set<LWProjekt> projekte = cache.load(LWProjekt.class,
                Factory.eq(LWProjektMeta.ATR_PROJEKTNUMMER, reply.getProjectNumber()));
        final Iterator<LWProjekt> itr = projekte.iterator();
        if (!itr.hasNext())
            throw new SQLException(Strings.concat("Kein Projekt \"", reply.getProjectNumber(), "\" in Logiware gefunden"));
        final LWProjekt projekt = itr.next();
        if (itr.hasNext())
            throw new SQLException(Strings.concat("Mehrere Projekte \"", reply.getProjectNumber(), "\" in Logiware gefunden"));
        // take data from projekt
        reply.setIdSubjektVerkaeufer(projekt.getVerkaueferSubjektId());
        reply.setIdSubjektAG(getSubjektId(projekt.getSubjektAG()));
        reply.setIdSubjektA(getSubjektId(projekt.getSubjektA()));
        reply.setIdSubjektB(getSubjektId(projekt.getSubjektB()));
        reply.setIdSubjektI(getSubjektId(projekt.getSubjektI()));
        reply.setIdSubjektK(getSubjektId(projekt.getSubjektK()));
        reply.setObjektbetreuer(projekt.getObjektbetreuerCode());
        reply.setKundenberater(projekt.getKundenberater());
        reply.setKundenberater2(getKundenberater2(projekt));
        reply.setProjectDescription(projekt.getProjektbezeichnung());
        reply.setAddressOverrideB(projekt.getManuelleBauherrAdresse());
        reply.setMarketing(projekt.getMarketingCode());
        reply.setAnzahlKuechen(projekt.getObjektkuechenAnzahl());
        reply.setProjektPLZ(projekt.getProjektPLZ());
        reply.setProjektOrt(projekt.getProjektOrt());
        reply.setProjektStrasse(projekt.getProjektStrasse());
    }

    private static Integer getSubjektId(final LWSubjekt subjekt) {
        return subjekt == null ? null : subjekt.getSubjektId();
    }

    private static AVORTeamCode getKundenberater2(LWProjekt projekt) {
        AVORTeamCode kundenberater2 = null;
        Set<LWAuftrag> auftraege = projekt.getAuftraege();
        Optional<LWAuftrag> kueche = auftraege.stream().filter(a -> !a.isStorniert() && (AbwicklungsartCode.AWA135.equals(a.getAbwicklungsart()) || AbwicklungsartCode.AWA001.equals(a.getAbwicklungsart()))).findFirst();
        if (kueche.isPresent()) {
            kundenberater2 = kueche.get().getAVORTeam();
        } else {
            Optional<LWAuftrag> garderobe = auftraege.stream().filter(a -> !a.isStorniert() && (AbwicklungsartCode.AWA132.equals(a.getAbwicklungsart()) || AbwicklungsartCode.AWA002.equals(a.getAbwicklungsart()))).findFirst();
            if (garderobe.isPresent()) {
                kundenberater2 = garderobe.get().getAVORTeam();
            }
        }
        return kundenberater2;
    }

    protected static void loadSubjektInfos(final DMSServer server, final PacketLWProjectInfoReply reply) throws Exception {
        final Set<Integer> set = new ArraySet<>(6);
        set.add(reply.getIdSubjektVerkauefer());
        set.add(reply.getIdSubjektAG());
        set.add(reply.getIdSubjektA());
        set.add(reply.getIdSubjektB());
        set.add(reply.getIdSubjektI());
        set.add(reply.getIdSubjektK());
        set.remove(null);
        final LWSubjektDAO dao = new LWSubjektDAO(server);
        final Map<Integer, LWSubjektInfo> map = dao.loadSubjektInfos(set);
        reply.setSubjektVerkaeufer(map.get(reply.getIdSubjektVerkauefer()));
        reply.setSubjektAG(map.get(reply.getIdSubjektAG()));
        reply.setSubjektA(map.get(reply.getIdSubjektA()));
        reply.setSubjektB(map.get(reply.getIdSubjektB()));
        reply.setSubjektI(map.get(reply.getIdSubjektI()));
        reply.setSubjektK(map.get(reply.getIdSubjektK()));
    }

}

package ch.eisenring.dms.server.network;

import java.io.IOException;
import java.sql.SQLException;

import ch.eisenring.app.shared.ServiceNotFoundException;
import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.dms.server.DMSServer;
import ch.eisenring.dms.service.DMSService;
import ch.eisenring.dms.service.api.DMSDocumentImage;
import ch.eisenring.dms.service.codetables.DMSDocumentType;
import ch.eisenring.dms.shared.creepingevil.LightweightLogiAuftrag;
import ch.eisenring.dms.shared.creepingevil.MailTemplateContext;
import ch.eisenring.dms.shared.network.PacketMailActionReply;
import ch.eisenring.dms.shared.network.PacketMailActionRequest;
import ch.eisenring.email.EMailAttachment;
import ch.eisenring.jdbc.JDBCResultSet;
import ch.eisenring.jdbc.StatementWrapper;
import ch.eisenring.logiware.LWContextSource;
import ch.eisenring.lw.LWConstants;
import ch.eisenring.model.TransactionContext;
import ch.eisenring.network.PacketSink;
import ch.eisenring.network.packet.AbstractPacket;

class MailActionRequestHandler extends AbstractDMSPacketHandler {

	public MailActionRequestHandler(final DMSServer server) {
		super(server, PacketMailActionRequest.class);
	}

	@Override
	public void handle(final AbstractPacket abstractPacket, final PacketSink sink) {
		final PacketMailActionRequest request = (PacketMailActionRequest) abstractPacket;
		final MailTemplateContext mailContext = new MailTemplateContext();
		// Get auftraege from logiware
		final LWContextSource logiSource = new LWContextSource(LWConstants.VERTRIEB) {
			@Override
			protected void loadImpl(final TransactionContext context) throws SQLException {
				final List<String> projects = request.getProjektnummern();
				final StringMaker b = StringMaker.obtain(128);
				for (final String prjnr : projects) {
					if (b.length() > 0)
						b.append(", ");
					b.append('\'');
					b.append(prjnr);
					b.append('\'');
				}
				final String sql = 
					"SELECT " + 
					  "A.AuftrNr, " +
					  "P.PAuftrNr, " +
					  "P.PBez, " +
					  "K.MDatum1, " +
					  "P.Reserve9, " +
					  "P.Reserve10," +
					  "P.PFaktPerCd " +
					"FROM " +
					  "PAUFTRAG P " + WITH_NOLOCK +
					"LEFT JOIN AUFTRAG A " + WITH_NOLOCK + " ON (P.PAuftrNr = A.PAuftrNr AND A.AbwArt = '135' AND A.Status NOT IN ('090', '099')) " + 
					"LEFT JOIN AuftragsKopfZusatz K " + WITH_NOLOCK + " ON (A.AuftrNr = K.AuftrNr) " +
					"WHERE " +
					  "P.PAuftrNr IN (" + b.release() + ")";
				final StatementWrapper statement = prepareStatement(sql);
				try {
					final JDBCResultSet resultSet = statement.executeQuery();
					try {
						while (resultSet.next()) {
							final LightweightLogiAuftrag auftrag = new LightweightLogiAuftrag();
							auftrag.auftragnummer = resultSet.getInt(1);
							auftrag.projektnummer = resultSet.getString(2);
							auftrag.bauherr = resultSet.getString(3);
							auftrag.abschlussDatum = resultSet.getLongDate(4);
							auftrag.nameBaufuehrer = resultSet.getString(5);
							auftrag.mailBaufuehrer = resultSet.getString(6);
							auftrag.objektbetreuer = resultSet.getString(7);
							mailContext.auftraege.add(auftrag);
							projects.remove(auftrag.projektnummer);
						}
					} finally {
						closeSilent(resultSet);
					}
				} finally {
					closeSilent(statement);
				}
				for (final String projektnummer : projects) {
					mailContext.errors.add(new ErrorMessage("Keine Küchenmontage zu Projektnummer " + projektnummer + " gefunden"));
				}
			}
		};
		try {
			final TransactionContext logiContext = new TransactionContext();
			logiContext.load(logiSource);
		} catch (final IOException e) {
			mailContext.errors.add(new ErrorMessage(e));
		}

		// Get binaries from DMS
		final DMSService service;
		try {
			service = server.locateService(DMSService.class);
		} catch (final ServiceNotFoundException e) {
			throw new RuntimeException(e.getMessage(), e);
		}
		for (final String projektnummer : request.getProjektnummern()) {
			try {
				List<DMSDocumentImage> list = service.getDocumentImages(projektnummer, DMSDocumentType.STATUSRAPPORT);
				switch (list.size()) {
					case 0:
						mailContext.errors.add(new ErrorMessage("Es existiert kein Statusrapport zu Projektnummer " + projektnummer));
						break;
					case 1:
						final EMailAttachment attachment = new EMailAttachment(list.get(0));
						mailContext.attachments.add(attachment);
						break;
					default:
						mailContext.errors.add(new ErrorMessage("Es existieren mehrere Statusrapporte zu Projektnummer " + projektnummer));
						break;
				}
			} catch (final Exception e) {
				mailContext.errors.add(new ErrorMessage(e));
			}
		}
		
		final PacketMailActionReply reply = PacketMailActionReply.create(request, mailContext);
		sink.sendPacket(reply, false);
	}

}

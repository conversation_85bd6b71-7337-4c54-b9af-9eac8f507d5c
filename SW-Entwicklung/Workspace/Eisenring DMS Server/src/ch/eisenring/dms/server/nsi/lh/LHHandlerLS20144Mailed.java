package ch.eisenring.dms.server.nsi.lh;

import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.datatypes.strings.parse.PlaintextParser;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.dms.service.codetables.DMSDocumentType;
import ch.eisenring.dms.shared.DMSDocumentVariables;
import ch.eisenring.logiware.code.pseudo.AbwicklungsartCode;
import ch.eisenring.lw.model.navigable.LWAuftrag;

import static ch.eisenring.core.datatypes.strings.parse.PlaintextParser.*;

/**
 * Lieferschein ELux (Elektronische Version)
 */
class LHHandlerLS20144Mailed implements LHHandler {

    @Override
    public boolean accepts(final LHContext context) {
        try {
            if (!LHHandler.equalsControlCode(context, 1))
                return false;
            if (!LHHandler.equalsReceiverCode(context, 1))
                return false;
            return getBelegnummer(context) != 0
                    && parseABNr(context) != null;
        } catch (final Exception e) {
            return false;
        }

    }

    @Override
    public void process(final LHContext context) throws Exception {
        final int belegNummer = getBelegnummer(context);
        final LWAuftrag auftrag;
        try {
            auftrag = context.getLWAuftrag(belegNummer);
        } catch (final Exception e) {
            context.addError(e.getMessage());
            return;
        }
        if (auftrag.isStorniert()) {
            context.addError(Strings.concat("Der Auftrag Belegnummer ", belegNummer, " ist storniert"));
            return;
        }
        if (!AbwicklungsartCode.FILTER_BESTELLUNG_LIEFERANT.accepts(auftrag.getAbwicklungsart())) {
            context.addError(Strings.concat("Der Auftrag Belegnummer ", belegNummer, " ist keine aktuelle Bestellung"));
            return;
        }
        final String abNr = parseABNr(context);
        if (Strings.isEmpty(abNr)) {
            context.addError("Die Auftrag-Nr. ELux konnte nicht erkannt werden");
            return;
        }

        // fill context with information needed to store the document to DMS
        context.projektNummer = auftrag.getProjektnummer();
        context.dmsDocumentType = DMSDocumentType.LIEFERSCHEIN_LIEFERANT;
        context.addParameter(DMSDocumentVariables.DOCVAR_ABNR, abNr);
        context.addParameter(DMSDocumentVariables.DOCVAR_SUBJECTID, "20144");
        context.addParameter(DMSDocumentVariables.DOCVAR_SUBJECTSHORT, "Electrol");
    }

    private static int getBelegnummer(final LHContext context) throws Exception {
        String rawRefData = context.barcodeData.getReceiverReference();
        int end = rawRefData.length();
        int start = end;
        while (--start >= 0) {
            final char c = rawRefData.charAt(start);
            if (!Strings.isASCIIDigit(c))
                break;
        }
        ++start;
        return Strings.parseInt(rawRefData, start, end);
    }

    protected String parseABNr(final LHContext context) throws Exception {
        Logger.debug("LHHandlerLS20144Mailed: Parsing document.");
        final String plainText = context.getPlainText();
        final PlaintextParser parser = new PlaintextParser(plainText);
        parser.nextMatch("CHE-105.969.699");
        parser.nextMatch("LIEFERSCHEIN");
        parser.nextMatch("Auftrag Nr.");
        parser.nextMatch("Versand Datum");
        parser.move(WHITESPACE, MISMATCH, Direction.FORWARD);
        String abNrLief = parser.extract(NONWHITESPACE, MATCH, Direction.FORWARD);
        return parser.isSuccess() ? Strings.clean(abNrLief) : null;
    }

}

package ch.eisenring.dms.server.indexing;

import java.sql.SQLException;
import java.util.Iterator;
import java.util.concurrent.atomic.AtomicInteger;

import ch.eisenring.PlaintextExtractor;
import ch.eisenring.app.shared.timing.PeriodicTimer;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.datatypes.date.TimeClassifier;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.io.binary.BinaryHolder;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.dms.server.DMSServer;
import ch.eisenring.dms.server.dao.DMSContextSink;
import ch.eisenring.dms.server.dao.DMSModelMapping;
import ch.eisenring.dms.shared.DMSConstants;
import ch.eisenring.dms.shared.model.metaold.DMSBinaryMetaModel;
import ch.eisenring.dms.shared.model.record.DMSBinaryModel;
import ch.eisenring.jdbc.JDBCResultSet;
import ch.eisenring.jdbc.JDBCUtil;
import ch.eisenring.jdbc.StatementWrapper;
import ch.eisenring.model.Model;
import ch.eisenring.model.TransactionContext;
import ch.eisenring.model.engine.jdbc.SingleTableMapping;

/**
 * Daemon to update objects where the plain text version
 * is invalid. 
 */
public class PlaintextIndexUpdater extends AbstractIndexUpdater {

	public PlaintextIndexUpdater(final DMSServer server) {
		super(server, 1, 16, new PeriodicTimer(3600));
	}

	@Override
	protected boolean isDue(final long now) {
		// do not run indexing during office hours
		final TimeClassifier timeClass = server.getCurrentTimeClassifier();
		return !timeClass.isWorktime() && super.isDue(now);
	}
	
	/**
	 * Called regularly
	 */
	@SuppressWarnings("unchecked")
	protected void updateIndexImpl() throws Exception {
		final AtomicInteger updateCount = new AtomicInteger(0);
		do {
			updateCount.set(0);
			// use the normal mechanisms to access the database
			final DMSContextSink sink = new DMSContextSink(server) {
				@Override
				protected void storeImpl(final Iterator<Model> modelItr,
						                 final TransactionContext context) throws SQLException {
					final SingleTableMapping MAPPING = DMSModelMapping.TM_BINARY;
					// find the objects which are out of date
					final StringMaker selectSQL = StringMaker.obtain(512);
					MAPPING.appendSelectSQL(selectSQL, this);
					selectSQL.append(" WHERE ");
					selectSQL.append(MAPPING.getColumn(DMSBinaryMetaModel.ATR_PLAINVERSION));
					selectSQL.append(" != ?");
					final StatementWrapper select = prepareStatement(limitSelectToTransactionSize(selectSQL.release()));
					try {
						final StringMaker updateSQL = StringMaker.obtain(256);
						updateSQL.append("UPDATE ");
						qualifyTableName(updateSQL, MAPPING.getTableSpecifier());
						updateSQL.append(" SET ");
						updateSQL.append(MAPPING.getColumn(DMSBinaryMetaModel.ATR_PLAINTEXT));
						updateSQL.append(" = ?");
						updateSQL.append(", ");
						updateSQL.append(MAPPING.getColumn(DMSBinaryMetaModel.ATR_PLAINVERSION));
						updateSQL.append(" = ? WHERE ");
						updateSQL.append(MAPPING.getPrimaryKeyColumn());
						updateSQL.append(" = ?");
						final StatementWrapper update = prepareStatement(updateSQL.release());
						try {
							try {
								select.setObject(1, DMSConstants.PLAINTEXT_VERSION_VALID);
								final JDBCResultSet resultSet = select.executeQuery();
								// load the objects to be updated
								final List<DMSBinaryModel> list = (List) MAPPING.loadRows(context, resultSet, getTransactionSize());
								if (Logger.isTraceEnabled())
									Logger.trace(Strings.concat("Selected ", list.size(), " objects for plaintext index update"));
								for (final DMSBinaryModel object : list) {
									updatePlaintext(object);
									update.setObject(1, object.getPlaintext());
									update.setObject(2, object.getPlainversion());
									update.setObject(3, object.getPKValue());
									update.addBatch();
									updateCount.incrementAndGet();
								}
							} finally {
								JDBCUtil.closeSilent(select);
							}
							update.executeBatch();
						} finally {
							JDBCUtil.closeSilent(update);
						}
					} finally {
						JDBCUtil.closeSilent(select);
					}
					executeBatchUpdates();
				}
			};
			final TransactionContext context = new TransactionContext();
			context.store(sink);
			updateTransactionSize(true);
			if (Logger.isInfoEnabled())
				Logger.info(Strings.concat("Sucessfully updated ", updateCount.get(), " plaintext entries"));
		} while (updateCount.get() > 0);
	}

	/**
	 * Updates a single objects search index
	 */
	public static void updatePlaintext(final DMSBinaryModel object) throws SQLException {
		final BinaryHolder holder = object.getBinaryData();
		Logger.debug("Updating PlainText for object " + object.getObjectRowId());
		final String plaintext = PlaintextExtractor.extract(holder);
		object.setPlaintext(plaintext);
		object.setPlainversion(DMSConstants.PLAINTEXT_VERSION_VALID);
	}

}

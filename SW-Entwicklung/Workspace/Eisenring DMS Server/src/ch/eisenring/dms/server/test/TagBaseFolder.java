//package ch.eisenring.dms.server.test;
//
//import java.sql.SQLException;
//import java.util.List;
//import java.util.concurrent.atomic.AtomicReference;
//
//import ch.eisenring.app.server.model.ContextSink;
//import ch.eisenring.app.server.model.ContextSource;
//import ch.eisenring.core.codetype.AbstractCode;
//import ch.eisenring.core.datatypes.strings.StringMaker;
//import ch.eisenring.core.datatypes.strings.Strings;
//import ch.eisenring.core.threading.ThreadCore;
//import ch.eisenring.dms.server.DMSServer;
//import ch.eisenring.dms.server.audit.AuditTrailBatch;
//import ch.eisenring.dms.server.audit.AuditTrailBatchEntry;
//import ch.eisenring.dms.server.dao.DMSMapping;
//import ch.eisenring.dms.server.functions.SQLFunctions;
//import ch.eisenring.dms.server.util.FolderVariableTemplateExpander;
//import ch.eisenring.dms.server.util.ReplicationTemplate;
//import ch.eisenring.dms.server.util.ReplicationTemplateSink;
//import ch.eisenring.dms.service.codetables.DMSFolderType;
//import ch.eisenring.dms.service.codetables.DMSObjectType;
//import ch.eisenring.dms.shared.DMSConstants;
//import ch.eisenring.dms.shared.codetables.DMSAuditCode;
//import ch.eisenring.dms.shared.codetables.DMSPropertyCode;
//import ch.eisenring.dms.shared.model.meta.DMSObjectMeta;
//import ch.eisenring.dms.shared.model.meta.DMSPropertyMeta;
//import ch.eisenring.dms.shared.model.pojo.DMSPropertyPojo;
//import ch.eisenring.jdbc.JDBCBase;
//import ch.eisenring.jdbc.JDBCResultSet;
//import ch.eisenring.jdbc.RowHandler;
//import ch.eisenring.jdbc.StatementParameters;
//import ch.eisenring.model.TransactionContext;
//import ch.eisenring.model.TransactionSink;
//import ch.eisenring.model.TransactionSource;
//
//public class TagBaseFolder extends UpdateBase {
//
//	public TagBaseFolder(final DMSServer server) {
//		super(server);
//	}
//
//	public void doUpdate() {
//		try {
//			final List<Long> folderList = getBasisFolders(DMSFolderType.BASIS_MIET, DMSFolderType.BASIS_EIGENTUM);
//			folderList.removeAll(getTemplateExclusionSet());
//			folderList.sort(null);
//			for (final Long rowId : folderList) {
//				System.out.println(rowId);
//				try {
//					Long f1 = getSubFolder(rowId, "Käuferunterlagen");
//					if (f1 != null)
//						addProperty(f1, DMSPropertyCode.FOLDERTYPE, DMSFolderType.BASIS_KAUEFERUNTERLAGEN);
//				} catch (final Exception e) {
//					// ignore
//				}
//			}
//		} catch (final Exception e) {
//			e.printStackTrace();
//		}
//		ThreadCore.sleep(1000);
//		server.getCore().shutdown(true);
//	}
//
//	/**
//	 * Adds a property to the specified object
//	 */
//	void addProperty(final long rowId, final DMSPropertyCode code, final AbstractCode value) throws Exception {
//		final TransactionSink sink = new ContextSink(DMSConstants.DMS_DATABASE) {
//			@Override
//			protected void storeImpl(final TransactionContext context) throws SQLException {
//				final DMSPropertyPojo property = DMSPropertyPojo.create();
//				property.setObjectRowId(rowId);
//				property.setPropertyCode(code);
//				property.setPropertyValue(value);
//				DMSMapping.OM_PROPERTY.insert(this, property);
//				AuditTrailBatch audit = new AuditTrailBatch();
//				audit.add(rowId, null, DMSAuditCode.PROPERTY_ADD, USER, property.getPropertyCode(), property.getPropertyValue());
//				audit.insertBatch(this);
//			}
//		};
//		final TransactionContext context = new TransactionContext();
//		context.store(sink);
//	}
//
//	List<Long> getBasisFolders(final DMSFolderType ... types) throws Exception {
//		final StringMaker sql = StringMaker.obtain();
//		final StatementParameters params = new StatementParameters();
//		sql.append("SELECT O.");
//		sql.append(DMSMapping.TM_OBJECT.getPrimaryKeyColumn());
//		sql.append(" FROM ");
//		sql.append(DMSMapping.TM_OBJECT.getTableSpecifier());
//		sql.append(" O, ");
//		sql.append(DMSMapping.TM_PROPERTY.getTableSpecifier());
//		sql.append(" P1, ");
//		sql.append(DMSMapping.TM_PROPERTY.getTableSpecifier());
//		sql.append(" P2 WHERE O.");
//		sql.append(DMSMapping.TM_OBJECT.getPrimaryKeyColumn());
//		sql.append(" = P1.");
//		sql.append(DMSMapping.TM_PROPERTY.getColumn(DMSPropertyMeta.ATR_OBJECT_ROWID));
//		sql.append(" AND O.");
//		sql.append(DMSMapping.TM_OBJECT.getPrimaryKeyColumn());
//		sql.append(" = P2.");
//		sql.append(DMSMapping.TM_PROPERTY.getColumn(DMSPropertyMeta.ATR_OBJECT_ROWID));
//		sql.append(" AND P1.");
//		sql.append(DMSMapping.TM_PROPERTY.getColumn(DMSPropertyMeta.ATR_PROPERTYCODE));
//		sql.append(" = ? AND P2.");
//		params.add(DMSPropertyCode.BASEPROJECTNUMBER);
//		sql.append(DMSMapping.TM_PROPERTY.getColumn(DMSPropertyMeta.ATR_PROPERTYCODE));
//		sql.append(" = ? AND P2.");
//		params.add(DMSPropertyCode.FOLDERTYPE);
//		sql.append(DMSMapping.TM_PROPERTY.getColumn(DMSPropertyMeta.ATR_PROPERTYVALUE));
//		sql.append(" IN ");
//		JDBCBase.prepareIn(sql, types.length);
//		for (final DMSFolderType type : types) {
//			params.add(Strings.toString(type.getKey()));
//		}
//		return getObjectRowIds(sql.release(), params);
//	}
//
//	Long getSubFolder(final long baseFolder, final String name) throws Exception {
//		final AtomicReference<Long> result = new AtomicReference<>();
//		final TransactionSource source = new ContextSource(DMSConstants.DMS_DATABASE) {
//			@Override
//			protected void loadImpl(final TransactionContext context) throws SQLException {
//				final StringMaker sql = StringMaker.obtain();
//				final StatementParameters params = new StatementParameters();
//				sql.append("SELECT ");
//				sql.append(DMSMapping.TM_OBJECT.getPrimaryKeyColumn());
//				sql.append(" FROM ");
//				sql.append(DMSMapping.TM_OBJECT.getTableSpecifier());
//				sql.append(" WHERE ");
//				sql.append(DMSMapping.TM_OBJECT.getColumn(DMSObjectMeta.ATR_TYPECODE));
//				sql.append(" = ? AND ");
//				params.add(DMSObjectType.FOLDER);
//				sql.append(DMSMapping.TM_OBJECT.getColumn(DMSObjectMeta.ATR_OBJECTNAME));
//				sql.append(" = ? AND ");
//				params.add(name);
//				sql.append(DMSMapping.TM_OBJECT.getPrimaryKeyColumn());
//				sql.append(" IN (SELECT ");
//				sql.append(DMSMapping.TM_OBJECT.getPrimaryKeyColumn());
//				sql.append(" FROM ");
//				SQLFunctions.ALL_CHILDREN.appendCall(sql, this, "?");
//				sql.append(")");
//				params.add(baseFolder);
//				doQuery(sql.release(), params, new RowHandler() {
//					@Override
//					public void handleRow(final JDBCResultSet resultSet) throws SQLException {
//						final Long rowId = resultSet.getLong(1);
//						result.set(rowId);
//					}
//				});
//			}
//		};
//		final TransactionContext context = new TransactionContext();
//		context.load(source);
//		return result.get();
//	}
//
//	// Propertycodes:
//	// 1: Projektnummer
//	// 2: Basisnummer
//	// 3: Subproj-Nummer (-1)
//	// 5: Dokumentart
//	// 6: Specialfunction
//	// 7: Foldertype
//	// 8: Structuretype (1 Einzel, 2 Eigentum, 3 Miet)
//	List<Long> getKuechenFolders() throws Exception {
//		final String sql = "SELECT O.rowId"
//				+ " FROM DMSObject O, DMSProperty P"
//				+ "	WHERE"
//				+ " P.Propertycode = 8 AND P.PropertyValue IN ('3')"
//				+ " AND O.Typecode = 1"
//				+ " AND O.rowId = P.Object_rowId";
//		return getObjectRowIds(sql);
//	}
//
//	List<Long> getFotoFolders() throws Exception {
//		final String sql = "SELECT F.rowId"
//				+ " FROM DMSObject O, DMSProperty P, DMSObject F"
//				+ "	WHERE"
//				+ " P.Propertycode = 8 AND P.PropertyValue IN ('1', '2', '3')" // 1 = Einzel, 2 = Eigentum, 3 = Miet
//				+ " AND O.Typecode = 1"
//				+ " AND O.rowId = P.Object_rowId"
//				+ " AND O.rowId NOT IN ("
//					+ TEMPLATE_KUECHE_EINZEL
////					+ ", " + TEMPLATE_KUECHE_EIGENTUM
////					+ ", " + TEMPLATE_KUECHE_MIET
//				+ ")"  // exclude template folders
//				+ " AND O.rowId = F.parent_rowId"
//				+ " AND F.tru_typeclass = 16";
//		return getObjectRowIds(sql);
//	}
//
//	/**
//	 * Returns a list of project sub folders by type id
//	 */
//	List<Long> getProjectSubFolders(final DMSFolderType folderType) throws Exception {
//		final String sql = "SELECT F.rowId"
//				+ " FROM DMSObject O, DMSProperty P, DMSObject F"
//				+ "	WHERE"
//				+ " P.Propertycode = 8 AND P.PropertyValue IN ('1', '2', '3')" // 1 = Einzel, 2 = Eigentum, 3 = Miet
//				+ " AND O.Typecode = 1"
//				+ " AND O.rowId = P.Object_rowId"
////				+ " AND O.rowId NOT IN ("
////					+ TEMPLATE_KUECHE_EINZEL
////					+ ", " + TEMPLATE_KUECHE_EIGENTUM
////					+ ", " + TEMPLATE_KUECHE_MIET
////				+ ")"  // exclude template folders
//				+ " AND O.rowId = F.parent_rowId"
//				+ " AND F.tru_typeclass = " + folderType.getKey();
//		return getObjectRowIds(sql);
//	}
//
//	List<Long> getBasisFolders() throws Exception {
//		final String sql = "SELECT O.rowId"
//				+ " FROM DMSObject O"
//				+ "	WHERE"
//				+ " O.Typecode = 1"
//				+ " AND O.tru_typeclass = 23" // 23 = Basis Objektorganisation
//				+ " AND O.rowId NOT IN (1184639072, 1184639113)"; // exclude template folders
//		return getObjectRowIds(sql);
//	}
//
//	private void addFolder(final List<Long> targetFolders, final long templateRowId) throws Exception {
//		final FolderVariableTemplateExpander expander = new FolderVariableTemplateExpander();
//		final ReplicationTemplate template = ReplicationTemplate.loadTemplate(server, templateRowId);
//		final ReplicationTemplateSink sink = new ReplicationTemplateSink(server, USER);
//		int updateCount = 0;
//		for (final Long folder : targetFolders) {
//			String result = null;
//			try {
//				result = "Ok";
//				sink.replicate(template, folder, expander);
//			} catch (final Exception e) {
//				result = e.getMessage();
//				System.out.println("Error Folder: " + folder + " = " + result);
//			}
//			++updateCount;
//			if ((updateCount % 1000) == 0) {
//				System.out.println("Updated " + updateCount + " folders of " + targetFolders.size());
//			}
//		}
//		System.out.println("Done (" + targetFolders.size() + " folders)");
//	}
//
//}

package ch.eisenring.dms.server.test;

import java.util.List;

import ch.eisenring.core.collections.Set;
import ch.eisenring.core.threading.ThreadCore;
import ch.eisenring.dms.server.DMSServer;
import ch.eisenring.dms.server.util.ReplicationTemplate;
import ch.eisenring.dms.server.util.ReplicationTemplateSink;
import ch.eisenring.dms.service.codetables.DMSFolderType;
import ch.eisenring.dms.service.codetables.DMSPropertyCode;
import ch.eisenring.dms.shared.util.FolderVariableTemplateExpander;

public class AddKuechenFolder extends UpdateBase {

	public AddKuechenFolder(final DMSServer server) {
		super(server);
	}

	public void doUpdate() {
		try {
			final Set<Long> exclusionSet = getTemplateExclusionSet();
			final List<Long> folderList = getTypedFolders(DMSPropertyCode.FOLDERTYPE,
					DMSFolderType.BASIS_OBJEKTORGANISATION); 
					//getProjectSubFolders(DMSFolderType.GRANITPLAENE); 
					//getFotoFolders(); //getKuechenFolders();
		
			folderList.removeAll(exclusionSet);
			folderList.sort(null);
//System.out.println(folderList);			
			addFolder(folderList, 1662212314L);
			
			//final List<Long> basisList = getBasisFolders();
			//addFolder(basisList, 1581955273L);
		} catch (final Exception e) {
			e.printStackTrace();
		}
		ThreadCore.sleep(1000);
		server.getCore().shutdown(true);
	}

	// Propertycodes:
	// 1: Projektnummer
	// 2: Basisnummer
	// 3: Subproj-Nummer (-1)
	// 5: Dokumentart
	// 6: Specialfunction
	// 7: Foldertype
	// 8: Structuretype (1 Einzel, 2 Eigentum, 3 Miet)
	List<Long> getKuechenFolders() throws Exception {
		final String sql = "SELECT O.rowId"
				+ " FROM DMSObject O, DMSProperty P"
				+ "	WHERE"
				+ " P.Propertycode = 8 AND P.PropertyValue IN ('3')"
				+ " AND O.Typecode = 1"
				+ " AND O.rowId = P.Object_rowId";
		return getObjectRowIds(sql);
	}

	List<Long> getFotoFolders() throws Exception {
		final String sql = "SELECT F.rowId"
				+ " FROM DMSObject O, DMSProperty P, DMSObject F"
				+ "	WHERE"
				+ " P.Propertycode = 8 AND P.PropertyValue IN ('1', '2', '3')" // 1 = Einzel, 2 = Eigentum, 3 = Miet
				+ " AND O.Typecode = 1"
				+ " AND O.rowId = P.Object_rowId"
				+ " AND O.rowId NOT IN ("
					+ TEMPLATE_KUECHE_EINZEL
//					+ ", " + TEMPLATE_KUECHE_EIGENTUM
//					+ ", " + TEMPLATE_KUECHE_MIET
				+ ")"  // exclude template folders
				+ " AND O.rowId = F.parent_rowId"
				+ " AND F.tru_typeclass = 16";
		return getObjectRowIds(sql);
	}

	/**
	 * Returns a list of project sub folders by type id
	 */
	List<Long> getProjectSubFolders(final DMSFolderType folderType) throws Exception {
		final String sql = "SELECT F.rowId"
				+ " FROM DMSObject O, DMSProperty P, DMSObject F"
				+ "	WHERE"
				+ " P.Propertycode = 8 AND P.PropertyValue IN ('1', '2', '3')" // 1 = Einzel, 2 = Eigentum, 3 = Miet
				+ " AND O.Typecode = 1"
				+ " AND O.rowId = P.Object_rowId"
//				+ " AND O.rowId NOT IN ("
//					+ TEMPLATE_KUECHE_EINZEL
//					+ ", " + TEMPLATE_KUECHE_EIGENTUM
//					+ ", " + TEMPLATE_KUECHE_MIET
//				+ ")"  // exclude template folders
				+ " AND O.rowId = F.parent_rowId"
				+ " AND F.tru_typeclass = " + folderType.getKey();
		return getObjectRowIds(sql);
	}

	List<Long> getBasisFolders() throws Exception {
		final String sql = "SELECT O.rowId"
				+ " FROM DMSObject O"
				+ "	WHERE"
				+ " O.Typecode = 1"
				+ " AND O.tru_typeclass = 23" // 23 = Basis Objektorganisation
				+ " AND O.rowId NOT IN (1184639072, 1184639113)"; // exclude template folders
		return getObjectRowIds(sql);
	}

	private void addFolder(final List<Long> targetFolders, final long templateRowId) throws Exception {
		final FolderVariableTemplateExpander expander = new FolderVariableTemplateExpander();
		final ReplicationTemplate template = ReplicationTemplate.loadTemplate(server, templateRowId);
		final ReplicationTemplateSink sink = new ReplicationTemplateSink(server, USER);
		int updateCount = 0;
		for (final Long folder : targetFolders) {
			String result = null;
			try {
				result = "Ok";
				sink.replicate(template, folder, expander);
			} catch (final Exception e) {
				result = e.getMessage();
				System.out.println("Error Folder: " + folder + " = " + result);
			}
			++updateCount;
			if ((updateCount % 1000) == 0) {
				System.out.println("Updated " + updateCount + " folders of " + targetFolders.size());
			}
		}
		System.out.println("Done (" + targetFolders.size() + " folders)");
	}

}

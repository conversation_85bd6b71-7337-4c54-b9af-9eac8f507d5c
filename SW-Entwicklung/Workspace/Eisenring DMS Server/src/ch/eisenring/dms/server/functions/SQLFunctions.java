package ch.eisenring.dms.server.functions;

import ch.eisenring.jdbc.SQLFunction;

public interface SQLFunctions {
	
	public final static SQLFunction ALL_CHILDREN =
			new SQLFunction("DMSAllChildren", 1);
	// ALL_CHILDREN column names
	public final static String ALL_CHILDREN_ROWID = "rowId";
	public final static String ALL_CHILDREN_PARENTROWID = "Parent_rowId";
	public final static String ALL_CHILDREN_TYPECODE = "Typecode";
	public final static String ALL_CHILDREN_STATUSCODE = "Statuscode";
	public final static String ALL_CHILDREN_TRU_TYPECLASS = "tru_typeclass";
	
	public final static SQLFunction ACTIVE_OBJECT_VERSION =
			new SQLFunction("DMSActiveObjectVersion", 1);

	public final static SQLFunction ALL_PROPERTIES =
			new SQLFunction("DMSAllProperties", 1);

	public final static SQLFunction OBJECT_CHILDLEVEL =
			new SQLFunction("DMSObjectChildLevel", 2);

}

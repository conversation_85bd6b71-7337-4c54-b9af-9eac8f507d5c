package ch.eisenring.dms.server.indexing.participants;

import ch.eisenring.core.collections.Lookup;
import ch.eisenring.core.collections.impl.HashMap;
import ch.eisenring.core.datatypes.primitives.Primitives;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.dms.shared.codetables.ParticipantRole;

/**
 * Holder class for participant information of a project folder.
 * This differs from the base project folder in that each participant
 * role is exclusive (there can be only ONE participant for each).
 */
public final class ProjectFolderInfo {

	/**
	 * Projektnummer
	 */
	private final String projektnummer;
	
	/**
	 * Holds all the participants of the project
	 */
	private final Lookup<ParticipantRole, String> participants = new HashMap<>(); 

	/**
	 * Folder rowId of the project.
	 * May be NULL if the project was not imported into the DMS yet.
	 */
	private Long folderRowId;
	
	public ProjectFolderInfo(final String projektnummer) {
		this.projektnummer = projektnummer;
	}

	// --------------------------------------------------------------
	// ---
	// --- Getters/Setters
	// ---
	// --------------------------------------------------------------
	public String getProjektnummer() {
		return projektnummer;
	}

	public void setFolderRowId(final Long folderRowId) {
		this.folderRowId = folderRowId;
	}

	public Long getFolderRowId() {
		return folderRowId;
	}

	// --------------------------------------------------------------
	// ---
	// --- Participant manipulation
	// ---
	// --------------------------------------------------------------
	/**
	 * Adds the given user as participant for role.
	 * If participant is null, the role will be removed.
	 */
	public void addParticipant(final String participant, final ParticipantRole role) {
		if (role == null)
			return;
		if (participant == null) {
			participants.remove(role);
		} else {
			participants.put(role, participant);
		}
	}

	/**
	 * Gets the user that is participant for role.
	 * Can be NULL (not set or not resolvable from Logiware).
	 */
	public String getParticipant(final ParticipantRole role) {
		return participants.get(role);
	}

	// --------------------------------------------------------------
	// ---
	// --- Object overrides
	// ---
	// --------------------------------------------------------------
	@Override
	public int hashCode() {
		return projektnummer.hashCode();
	}

	@Override
	public boolean equals(final Object o) {
		return o instanceof ProjectFolderInfo && ((ProjectFolderInfo) o).projektnummer.equals(projektnummer);
	}	

	@Override
	public String toString() {
		return Strings.concat(Primitives.getSimpleName(getClass()), "(", projektnummer, ")");
	}

}

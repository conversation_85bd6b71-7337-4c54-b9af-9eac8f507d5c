package ch.eisenring.dms.server.indexing;

import java.sql.SQLException;
import java.util.Iterator;
import java.util.concurrent.atomic.AtomicInteger;

import ch.eisenring.app.shared.timing.NightlyTimer;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.dms.server.DMSServer;
import ch.eisenring.dms.server.dao.DMSContextSink;
import ch.eisenring.dms.server.dao.DMSModelMapping;
import ch.eisenring.dms.server.util.BinaryUtil;
import ch.eisenring.dms.shared.codetables.DMSCompressionCode;
import ch.eisenring.dms.shared.model.metaold.DMSBinaryMetaModel;
import ch.eisenring.dms.shared.model.record.DMSBinaryModel;
import ch.eisenring.jdbc.JDBCResultSet;
import ch.eisenring.jdbc.JDBCUtil;
import ch.eisenring.jdbc.StatementWrapper;
import ch.eisenring.model.Model;
import ch.eisenring.model.TransactionContext;
import ch.eisenring.model.engine.jdbc.SingleTableMapping;

/**
 * Daemon to compress binaries to the smallest possible size.
 */
public class BinaryCompressionUpdater extends AbstractIndexUpdater {

	public BinaryCompressionUpdater(final DMSServer server) {
		super(server, 1, 1, new NightlyTimer());
	}

	/**
	 * Called regularly
	 */
	@SuppressWarnings("unchecked")
	protected void updateIndexImpl() throws Exception {
		Logger.debug("Starting BinaryCompressionUpdater");
		final AtomicInteger updateCount = new AtomicInteger(0);
		do {
			updateCount.set(0);
			// use the normal mechanisms to access the database
			final DMSContextSink sink = new DMSContextSink(server) {
				@Override
				protected void storeImpl(final Iterator<Model> modelItr,
										 final TransactionContext context) throws SQLException {
					final SingleTableMapping MAPPING = DMSModelMapping.TM_BINARY;
					// find the binaries which may need compression
					final StringMaker selectSQL = StringMaker.obtain(256);
					MAPPING.appendSelectSQL(selectSQL, this, SingleTableMapping.NO_TABLE_ALIAS);
					selectSQL.append(" WHERE ");
					selectSQL.append(MAPPING.getColumn(DMSBinaryMetaModel.ATR_DCMCODE));
					selectSQL.append(" = ?");
					StatementWrapper select = prepareStatement(limitSelectToTransactionSize(selectSQL.release()));
					try {
						final StringMaker updateSQL = StringMaker.obtain(256);
						updateSQL.append("UPDATE ");
						qualifyTableName(updateSQL, MAPPING.getTableSpecifier());
						updateSQL.append(" SET ");
						updateSQL.append(MAPPING.getColumn(DMSBinaryMetaModel.ATR_BINARYDATA));
						updateSQL.append(" = ?, ");
						updateSQL.append(MAPPING.getColumn(DMSBinaryMetaModel.ATR_DCMCODE));
						updateSQL.append(" = ?, ");
						updateSQL.append(MAPPING.getColumn(DMSBinaryMetaModel.ATR_RAWSIZE));
						updateSQL.append(" = ? WHERE ");
						updateSQL.append(MAPPING.getPrimaryKeyColumn());
						updateSQL.append(" = ?");
						StatementWrapper update = prepareStatement(updateSQL.release());
						try {
							try {
								select.setObject(1, DMSCompressionCode.NONE0);
								final JDBCResultSet resultSet = select.executeQuery();
								// load the objects to be updated
								final List<DMSBinaryModel> list = (List) MAPPING.loadRows(context, resultSet, getTransactionSize());
								Logger.debug(Strings.concat("Selected ", list.size(), " objects for compression optimization"));
								if (Logger.isTraceEnabled())
									Logger.trace(Strings.concat("Selected ", list.size(), " objects for compression optimization"));
								for (final DMSBinaryModel object : list) {
									BinaryUtil.compressBinary(object);
									update.setObject(1, DMSBinaryMetaModel.ATR_BINARYDATA.getMember(object));
									update.setObject(2, DMSBinaryMetaModel.ATR_DCMCODE.getMember(object));
									update.setObject(3, DMSBinaryMetaModel.ATR_RAWSIZE.getMember(object));
									update.setObject(4, object.getPKValue());
									update.addBatch();
									updateCount.incrementAndGet();
								}
							} finally {
								JDBCUtil.closeSilent(select);
							}
							update.executeBatch();
						} finally {
							JDBCUtil.closeSilent(update);
						}
					} finally {
						JDBCUtil.closeSilent(select);
					}
					executeBatchUpdates();
				}
			};
			final TransactionContext context = new TransactionContext();
			context.store(sink);
			updateTransactionSize(true);
			Logger.debug(Strings.concat("Sucessfully compressed ", updateCount, " binary versions"));
			if (Logger.isInfoEnabled())
				Logger.info(Strings.concat("Sucessfully compressed ", updateCount, " binary versions"));
		} while (updateCount.get() > 0);
	}

}

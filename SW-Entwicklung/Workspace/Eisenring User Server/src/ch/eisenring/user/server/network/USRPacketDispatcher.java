package ch.eisenring.user.server.network;

import ch.eisenring.network.ClassListPacketDispatcher;
import ch.eisenring.user.server.USRServer;

public final class USRPacketDispatcher extends ClassListPacketDispatcher {

	public USRPacketDispatcher(final USRServer server) {
		super(new Class<?>[] {
				RequestHandler.class,
				UpdateHandler.class,
				CheckLoginNameRequestHandler.class,
				DevKeyAllRequestHandler.class,
				DevKeyActionRequestHandler.class
		}, new Class<?>[] { USRServer.class }, new Object[] { server });
	}

}

package ch.eisenring.user.server.network;

import ch.eisenring.network.PacketSink;
import ch.eisenring.network.implementation.PacketDispatchMode;
import ch.eisenring.network.packet.AbstractPacket;
import ch.eisenring.user.server.USRServer;
import ch.eisenring.user.shared.network.PacketUpdate;

final class Update<PERSON><PERSON><PERSON> extends AbstractUSRPacketHandler {
	
	UpdateHandler(final USRServer server) {
		super(server, PacketUpdate.class, PacketDispatchMode.SYNCHRONOUS);
	}
	
	@Override
	public void handle(final AbstractPacket packet, final PacketSink sink) {
		final PacketUpdate request = (PacketUpdate) packet;
		server.updateContext(request);
	}

}

package ch.eisenring.user.server.network;

import ch.eisenring.core.logging.Logger;
import ch.eisenring.network.PacketSink;
import ch.eisenring.network.packet.AbstractPacket;
import ch.eisenring.user.server.USRServer;
import ch.eisenring.user.shared.model.UserContext;
import ch.eisenring.user.shared.network.PacketRequest;
import ch.eisenring.user.shared.network.PacketSupply;

final class <PERSON>quest<PERSON><PERSON><PERSON> extends AbstractUSRPacketHandler {

	RequestHandler(final USRServer server) {
		super(server, PacketRequest.class);
	}
	
	@Override
	public void handle(final AbstractPacket packet, final PacketSink sink) {
		final PacketRequest request = (PacketRequest) packet;
		final PacketSupply reply = handle(server, request);
		sink.sendPacket(reply, false);
	}

	public static PacketSupply handle(final USRServer server, final PacketRequest request) {
		try {
			final UserContext context = server.getContext();
			final PacketSupply reply = PacketSupply.create(context);
			return reply;
		} catch (final Exception e) {
			Logger.error(e);
			throw new RuntimeException(e.getMessage(), e);
		}
	}

}

package ch.eisenring.user.server.network;

import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.collections.Set;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.network.PacketSink;
import ch.eisenring.network.packet.AbstractPacket;
import ch.eisenring.user.server.USRServer;
import ch.eisenring.user.shared.network.PacketCheckLoginNameReply;
import ch.eisenring.user.shared.network.PacketCheckLoginNameRequest;
import ch.eisenring.user.shared.service.USRService;
import ch.eisenring.user.shared.service.UserFacade;

public class CheckLoginNameRequestHandler extends AbstractUSRPacketHandler {

	private final static Set<String> FORBIDDEN_LOGINS = Set.asSet(
		"ROOT", "ADMIN", "SCAN", "DBO", "SA", "EDI"
	);

	public CheckLoginNameRequestHandler(final USRServer server) {
		super(server, PacketCheckLoginNameRequest.class);
	}

	@Override
	public void handle(final AbstractPacket packet, final PacketSink sink) {
		final PacketCheckLoginNameRequest request = (PacketCheckLoginNameRequest) packet;
		final PacketCheckLoginNameReply reply = handle(server, request);
		sink.sendPacket(reply, false);
	}

	public static PacketCheckLoginNameReply handle(final USRServer server, final PacketCheckLoginNameRequest request) {
		try {
			final PacketCheckLoginNameReply reply = handleImpl(server, request);
			if (reply != null)
				return reply;
			return PacketCheckLoginNameReply.create(request, ErrorMessage.ERROR);
		} catch (final Exception e) {
			return PacketCheckLoginNameReply.create(request, new ErrorMessage(e));
		}
	}

	private static PacketCheckLoginNameReply handleImpl(final USRServer server, final PacketCheckLoginNameRequest request) throws Exception {
		boolean found;
		StringMaker result = StringMaker.obtain();
		found = checkHEAGUsers(server, request, result);
		result.append('\n');
		found |= checkPresento(server, request, result);
		result.append('\n');
		found |= checkLWSabe(server, request, result);
		result.append('\n');
		found |= checkLWOb(server, request, result);
		result.append('\n');
		found |= checkLWDisponent(server, request, result);
		result.append('\n');
		found |= checkLWMont(server, request, result);
		result.append('\n');
		found |= checkDSPMont(server, request, result);
		result.append('\n');
		found |= checkForbidden(server, request, result);
		if (found) {
			result.append('\n');
			result.append(request.getLoginName() + " ist/war in Gebrauch");
		}
		return PacketCheckLoginNameReply.create(request, result.release());
	}

	private static boolean checkHEAGUsers(final USRServer server, final PacketCheckLoginNameRequest request, final StringMaker target) throws Exception {
		boolean result;
		final USRService service = server.locateService(USRService.class);
		final UserFacade user = service.getUser(request.getLoginName());
		target.append("HEAG Benutzerverwaltung: ");
		if (user == null) {
			target.append("Kein HEAG-Benutzer gefunden");
			result = false;
		} else {
			target.append("Gefunden (");
			target.append(user.getFirstname());
			target.append(" ");
			target.append(user.getLastname());
			target.append(")");
			result = true;
		}
		return result;
	}

	private static boolean checkLWSabe(final USRServer server, final PacketCheckLoginNameRequest request, final StringMaker target) throws Exception {
		boolean result;
		target.append("Logiware (Sachbearbeiter): ");
		target.append("N/A");
		result = false;
		return result;
	}

	private static boolean checkLWOb(final USRServer server, final PacketCheckLoginNameRequest request, final StringMaker target) throws Exception {
		boolean result;
		target.append("Logiware (Objektbetreuer): ");
		target.append("N/A");
		result = false;
		return result;
	}

	private static boolean checkLWDisponent(final USRServer server, final PacketCheckLoginNameRequest request, final StringMaker target) throws Exception {
		boolean result;
		target.append("Logiware (Disponent): ");
		target.append("N/A");
		result = false;
		return result;
	}

	private static boolean checkLWMont(final USRServer server, final PacketCheckLoginNameRequest request, final StringMaker target) throws Exception {
		boolean result;
		target.append("Logiware (Monteur): ");
		target.append("N/A");
		result = false;
		return result;
	}
	
	private static boolean checkPresento(final USRServer server, final PacketCheckLoginNameRequest request, final StringMaker target) throws Exception {
		boolean result;
		target.append("Presento (Personalstamm): ");
		target.append("N/A");
		result = false;
		return result;
	}

	private static boolean checkDSPMont(final USRServer server, final PacketCheckLoginNameRequest request, final StringMaker target) throws Exception {
		boolean result;
		target.append("Dispomodul (Mitarbeiter): ");
		target.append("N/A");
		result = false;
		return result;
	}

	private static boolean checkForbidden(final USRServer server, final PacketCheckLoginNameRequest request, final StringMaker target) throws Exception {
		boolean result;
		target.append("Reservierte Namen: ");
		final String upper = Strings.toUpper(request.getLoginName());
		if (FORBIDDEN_LOGINS.contains(upper)) {
			target.append(request.getLoginName());
			target.append(" ist ein reservierter Login");
			result = true;
		} else {
			target.append("Ok");
			result = false;
		}
		return result;
	}

}

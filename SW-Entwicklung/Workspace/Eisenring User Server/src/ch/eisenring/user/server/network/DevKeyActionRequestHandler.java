package ch.eisenring.user.server.network;

import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.network.PacketSink;
import ch.eisenring.network.packet.AbstractPacket;
import ch.eisenring.user.server.USRServer;
import ch.eisenring.user.shared.api.DeviceKeyProvider;
import ch.eisenring.user.shared.api.USRDeviceKey;
import ch.eisenring.user.shared.codetables.DeviceKeyAction;
import ch.eisenring.user.shared.network.PacketDevKeyActionReply;
import ch.eisenring.user.shared.network.PacketDevKeyActionRequest;

public class DevKeyActionRequestHandler extends AbstractUSRPacketHandler {

	public DevKeyActionRequestHandler(final USRServer server) {
		super(server, PacketDevKeyActionRequest.class);
	}

	@Override
	public PacketDevKeyActionReply handle(final AbstractPacket packet) {
		final PacketDevKeyActionRequest request = (PacketDevKeyActionRequest) packet;
		final PacketDevKeyActionReply reply = handle(server, request);
		return reply;
	}

	@Override
	public void handle(final AbstractPacket packet, final PacketSink sink) {
		final PacketDevKeyActionRequest request = (PacketDevKeyActionRequest) packet;
		final PacketDevKeyActionReply reply = handle(server, request);
		sink.sendPacket(reply);
	}

	private static PacketDevKeyActionReply handle(final USRServer server, final PacketDevKeyActionRequest request) {
		try {
			final PacketDevKeyActionReply reply = handleImpl(server, request);
			if (reply != null)
				return reply;
			return PacketDevKeyActionReply.create(request, ErrorMessage.ERROR);
		} catch (final Exception e) {
			return PacketDevKeyActionReply.create(request, new ErrorMessage(e));
		}
	}

	private static PacketDevKeyActionReply handleImpl(final USRServer server,
			final PacketDevKeyActionRequest request) throws Exception {
		final DeviceKeyAction action = request.getAction();
		final USRDeviceKey deviceKey = request.getDeviceKey();
		// find the provider handling the key
		DeviceKeyProvider provider = null;
		for (final DeviceKeyProvider p : server.getFeatureLookup().getFeatures(DeviceKeyProvider.class)) {
			if (p.accepts(deviceKey)) {
				provider = p;
				break;
			}
		}
		if (provider == null) {
			return PacketDevKeyActionReply.create(request, new ErrorMessage("No provider for supplied key"));
		} else if (DeviceKeyAction.DELETE.equals(action)) {
			final ErrorMessage message = provider.deleteKey(deviceKey);
			return PacketDevKeyActionReply.create(request, message);
		} else if (DeviceKeyAction.UPDATE.equals(action)) {
			final ErrorMessage message = provider.updateKey(deviceKey);
			return PacketDevKeyActionReply.create(request, message);
		} else {
			return PacketDevKeyActionReply.create(request, new ErrorMessage("Invalid action requested"));
		}
	}

}

package ch.eisenring.user.server;

import ch.eisenring.app.server.AbstractServerComponent;
import ch.eisenring.app.shared.Configurable;
import ch.eisenring.core.application.Configuration;
import ch.eisenring.core.application.ConfigurationException;
import ch.eisenring.core.collections.Collection;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.feature.Feature;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.core.observable.Observable;
import ch.eisenring.core.platform.Platform;
import ch.eisenring.core.platform.PlatformPath;
import ch.eisenring.jdbc.ConnectionInfo;
import ch.eisenring.user.server.network.USRPacketDispatcher;
import ch.eisenring.user.shared.USRConstants;
import ch.eisenring.user.shared.model.UserContext;
import ch.eisenring.user.shared.network.PacketUpdate;

import java.io.File;

public final class USRServer extends AbstractServerComponent implements Configurable {

	// --------------------------------------------------------------
	// ---
	// --- Server state
	// ---
	// --------------------------------------------------------------
	/**
	 * Holds no useful value, but fires a state change
	 * whenever any permissions may have changed.
	 */
	public final Observable<Object> PERMISSIONS_CHANGED =
		Observable.create(true, "PermissonsChanged", null);
	
	// --------------------------------------------------------------
	// ---
	// --- UserSystem API
	// ---
	// --------------------------------------------------------------
	private UserContext context;
	
	public UserContext getContext() {
		synchronized (getLock()) {
			if (context == null) {
				final USRContextSource source = new USRContextSource();
				try {
					context = source.load();
					context.setUnchanged(true);
				} catch (final Exception e) {
					shutdown(e);
					throw new RuntimeException(e.getMessage(), e);
				}
				PERMISSIONS_CHANGED.fireValueChange();
			}
			return context;
		}
	}
	
	public void updateContext(final PacketUpdate packet) {
		if (packet == null)
			return;
		synchronized (getLock()) {
			final UserContext context = getContext();
			try {
				packet.updateContext(context);
				// store the context
				UserContextSink sink = new UserContextSink();
				context.store(sink);
				// notify all clients of the change
				final PacketUpdate reply = PacketUpdate.create(context);
				context.setUnchanged(true);
				broadcast(reply, false);
			} catch (final Exception e) {
				Logger.error(Strings.concat("error updating context: ", e.getMessage()));
				Logger.error(e);
				this.context = null;
			}
		}
		PERMISSIONS_CHANGED.fireValueChange();
	}
	
	// --------------------------------------------------------------
	// ---
	// --- Configurable implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public void getConfigurationFileNames(final Collection<String> fileNames) {
		fileNames.add("USR.cfg");
		fileNames.add(new File(Platform.getPlatform().getPath(PlatformPath.SETTINGS), "USRServerUser.cfg").getAbsolutePath());
	}

	@Override
	public void configure(final Configuration configuration) throws ConfigurationException {
		USRConstants.USR_DATABASE.setConnectionInfo(ConnectionInfo.create("UserSystem:", configuration));
	}


	// --------------------------------------------------------------
	// ---
	// --- Feature management
	// ---
	// --------------------------------------------------------------
	private final Feature[] features = {
			new USRPacketDispatcher(this),
			new USRServiceImpl(this),
			new USRAuthenticationProvider(this)
	};

	@Override
	public Feature[] getFeatures() {
		return features;
	}

}

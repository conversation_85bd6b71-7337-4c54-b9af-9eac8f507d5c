
CREATE TABLE HEAGUser (
  Rowid               BIGINT      NOT NULL,
  Loginname           VARCHAR(60) NOT NULL,
  StatusCode          INT         NOT NULL DEFAULT 1,
  Firstname           VARCHAR(30),
  Lastname            VARCHAR(30),
  Passwordtype        INT NOT NULL DEFAULT 0,
  Passwordhash        VARCHAR(200),
  Passwordsalt        VARCHAR(200),
  SigHTML             VARCHAR(2000),
  SigPLAIN            VARCHAR(2000),
  Tackonvalues        TEXT
);
ALTER TABLE HEAGUser ADD PRIMARY KEY(Rowid);
CREATE UNIQUE INDEX IDX_HEAGUser_Loginname ON HEAGUser (Loginname);
GRANT ALL ON HEAGUser TO PUBLIC;



CREATE TABLE HEAGRole (
  Rowid           BIGINT      NOT NULL,
  Displayname     VARCHAR(60) NOT NULL,
  TackonValues    TEXT
);
ALTER TABLE HEAGRole ADD PRIMARY KEY(Rowid);
GRANT ALL ON HEAGRole TO PUBLIC;



CREATE TABLE HEAGUserRole (
  User_Rowid BIGINT NOT NULL,
  Role_Rowid BIGINT NOT NULL
);
ALTER TABLE HEAGUserRole ADD FOREIGN KEY(User_Rowid) REFERENCES HEAGUser(Rowid);
ALTER TABLE HEAGUserRole ADD FOREIGN KEY(Role_Rowid) REFERENCES HEAGRole(Rowid);
GRANT ALL ON HEAGUserRole TO PUBLIC;



CREATE TABLE HEAGRight (
  Rowid           INT         NOT NULL,
  Displayname     VARCHAR(60) NOT NULL,
  Applicationname VARCHAR(60) NOT NULL,
  Categoryname    VARCHAR(60) NOT NULL,
  Description     VARCHAR(4000)
);
ALTER TABLE HEAGRight ADD PRIMARY KEY (Rowid);
GRANT ALL ON HEAGRight TO PUBLIC;



CREATE TABLE HEAGRoleRight (
  Role_Rowid    BIGINT NOT NULL,
  Right_Rowid   INT    NOT NULL
);
ALTER TABLE HEAGRoleRight ADD FOREIGN KEY(Role_Rowid) REFERENCES HEAGRole(Rowid);
ALTER TABLE HEAGRoleRight ADD FOREIGN KEY(Right_Rowid) REFERENCES HEAGRight(Rowid);
GRANT ALL ON HEAGRoleRight TO PUBLIC;



CREATE TABLE HEAGOption (
  Rowid              INT NOT NULL,
  Displayname        VARCHAR(60) NOT NULL,
  Applicationname    VARCHAR(60) NOT NULL,
  Description        VARCHAR(4000)
);
ALTER TABLE HEAGOption ADD PRIMARY KEY (Rowid);
GRANT ALL ON HEAGOption TO PUBLIC;



CREATE TABLE HEAGUserOption (
  User_Rowid    BIGINT NOT NULL,
  Option_Rowid  INT NOT NULL
);
ALTER TABLE HEAGUserOption ADD FOREIGN KEY (User_Rowid) REFERENCES HEAGUser(Rowid);
ALTER TABLE HEAGUserOption ADD FOREIGN KEY (Option_Rowid) REFERENCES HEAGOption(Rowid);
GRANT ALL ON HEAGUserOption TO PUBLIC;

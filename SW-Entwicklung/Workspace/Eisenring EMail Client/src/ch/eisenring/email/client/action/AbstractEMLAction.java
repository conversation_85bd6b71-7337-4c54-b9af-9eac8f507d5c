package ch.eisenring.email.client.action;

import ch.eisenring.app.client.AppCore;
import ch.eisenring.app.client.action.AbstractAPPAction;
import ch.eisenring.core.usersettings.UserSettings;
import ch.eisenring.email.client.MailServiceClient;
import ch.eisenring.email.misc.EMailSettings;

/**
 * Base class for actions
 */
abstract class AbstractEMLAction extends AbstractAPPAction<MailServiceClient> {

	protected final AppCore core;
	
	protected AbstractEMLAction(final AppCore appCore, final Object ... properties) {
		super((MailServiceClient) appCore.getComponent(MailServiceClient.class, false), properties);
		this.core = appCore;
		addPermissionObserver();
	}

	protected UserSettings getUserSettings() {
		if (client == null)
			return core.getUserSettings();
		return client.getUserSettings();
	}
	
	protected boolean getSetting(final String key) {
		return EMailSettings.restore(key, getUserSettings());
	}

	@Override
	protected boolean isEnabledImpl() {
		return super.isEnabledImpl() && (core != null || client != null);
	}

}

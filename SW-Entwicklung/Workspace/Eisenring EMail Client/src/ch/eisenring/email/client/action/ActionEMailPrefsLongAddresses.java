package ch.eisenring.email.client.action;

import ch.eisenring.app.client.AppCore;
import ch.eisenring.core.usersettings.UserSettings;
import ch.eisenring.email.misc.EMailSettings;

public class ActionEMailPrefsLongAddresses extends AbstractEMLAction {

	public ActionEMailPrefsLongAddresses(final AppCore appCore) {
		super(appCore, "Mail-Adressen vollständig darstellen");
	}
	
	@Override
	protected void performAction() {
		final UserSettings userSettings = getUserSettings();
		boolean check = !getSetting(EMailSettings.KEY_LONG_MAIL_ADDRESSES);
		userSettings.save(EMailSettings.KEY_LONG_MAIL_ADDRESSES, check);
		isChecked();
	}
	
	@Override
	protected boolean isCheckedImpl() {
		return getSetting(EMailSettings.KEY_LONG_MAIL_ADDRESSES);
	}

}

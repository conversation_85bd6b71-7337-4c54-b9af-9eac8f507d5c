package ch.eisenring.email.client.action;

import java.awt.Component;
import java.awt.image.BufferedImage;
import java.nio.charset.StandardCharsets;

import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.io.binary.BinaryHolder;
import ch.eisenring.core.io.binary.BinaryHolderUtil;
import ch.eisenring.core.tag.TagSet;
import ch.eisenring.core.util.ScreenShotUtil;
import ch.eisenring.email.EMailAttachment;
import ch.eisenring.email.client.MailServiceClient;
import ch.eisenring.email.client.MailServiceTags;
import ch.eisenring.email.client.resource.images.Images;
import ch.eisenring.gui.panels.JavaSystemPropertiesPanel;
import ch.eisenring.gui.panels.LogFileInfoPanel;
import ch.eisenring.gui.panels.MachineInfoPanel;
import ch.eisenring.gui.screenshot.ActionScreenshotAbstract;
import ch.eisenring.gui.window.DialogTags;

public class ActionBugReportEMail extends ActionScreenshotAbstract {

	private MailServiceClient client;
	
	public ActionBugReportEMail(final MailServiceClient client,
			                     final Component component) {
		super(component, "Fehlerreport versenden...", Images.EMAIL_WITH_ATTACHMENT);
		this.client = client;
	}

	@Override
	protected void performAction(final BufferedImage image, final String fileName) {
		final List<EMailAttachment> attachments = new ArrayList<EMailAttachment>();
		try {
			// create ScreenShot attachment
			final BinaryHolder data = ScreenShotUtil.toBinary(image, ScreenShotUtil.FORMAT_PNG);
			attachments.add(new EMailAttachment(data, fileName));
			
			// create settings text attachment
			final StringMaker b = StringMaker.obtain(25000);
			b.append("\n\n\n--- Computer Informationen -------------------------------------------\n");
			MachineInfoPanel.appendMachineInfoText(b);
			b.append("\n\n\n--- Java Informationen -----------------------------------------------\n");
			JavaSystemPropertiesPanel.appendPropertiesInfoText(b);
			b.append("\n\n\n--- Logfile Informationen --------------------------------------------\n");
			LogFileInfoPanel.appendLogFileInfoText(b);
			final BinaryHolder binary = BinaryHolderUtil.create(b.release(), StandardCharsets.ISO_8859_1);
			attachments.add(new EMailAttachment(binary, "Machine_info.txt"));

			final TagSet args = new TagSet();
			args.add(MailServiceTags.MAIL_ATTACHMENTS, attachments);
			args.add(MailServiceTags.MAIL_SUBJECT,
					Strings.concat("Fehlerreport (", TimestampUtil.DATETIME.format(System.currentTimeMillis()) + "): "));
			args.add(MailServiceTags.DIALOG_TITLE, "Fehlerreport senden");
			args.add(MailServiceTags.TO, "<EMAIL>");
			args.add(MailServiceTags.CC, "kija");
			args.add(DialogTags.DIALOG_OWNER, getWindow());
			ActionEMailer.showMailerDialog(args, client);
		} catch (final Throwable t) {
			showError("Fehler: " + t.getMessage());
		}
	}

}

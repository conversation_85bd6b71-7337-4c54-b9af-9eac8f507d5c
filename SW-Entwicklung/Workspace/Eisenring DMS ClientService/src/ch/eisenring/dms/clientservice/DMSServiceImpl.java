package ch.eisenring.dms.clientservice;

import java.util.Collection;

import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.Map;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.datatypes.strings.Msg;
import ch.eisenring.core.util.file.FileImage;
import ch.eisenring.dms.service.DMSService;
import ch.eisenring.dms.service.DMSServiceException;
import ch.eisenring.dms.service.DMSServiceResultCode;
import ch.eisenring.dms.service.api.DMSDocumentImage;
import ch.eisenring.dms.service.api.DMSObject;
import ch.eisenring.dms.service.api.DMSObjectIdentity;
import ch.eisenring.dms.service.api.DMSObjectSpecifier;
import ch.eisenring.dms.service.codetables.DMSDocumentTemplateCode;
import ch.eisenring.dms.service.codetables.DMSDocumentType;
import ch.eisenring.dms.service.codetables.DMSFolderType;
import ch.eisenring.dms.service.network.PacketDMSServiceReply;
import ch.eisenring.dms.service.network.PacketDMSServiceRequest;
import ch.eisenring.dms.service.obj.DMSPermissionSnapshot;
import ch.eisenring.dms.service.proxy.DMSDocumentHandle;
import ch.eisenring.dms.service.proxy.DMSFolderHandle;
import ch.eisenring.dms.service.proxy.DMSObjectHandle;

/**
 * Implements DMSService on the client side
 */
final class DMSServiceImpl implements DMSService {

	private final DMSClientService client;

	DMSServiceImpl(final DMSClientService client) {
		this.client = client;
	}

	// --------------------------------------------------------------
	// ---
	// --- Internal remote call implementation
	// ---
	// --------------------------------------------------------------
	private Object callRemote(final int methodId, final Object[] args) throws DMSServiceException {
		final PacketDMSServiceReply reply;
		try {
			final PacketDMSServiceRequest request = PacketDMSServiceRequest.create(methodId, args);
			reply = (PacketDMSServiceReply) client.sendAndWait(request);
		} catch (final Exception e) {
			throw new DMSServiceException(DMSServiceResultCode.GENERIC_ERROR, e.getMessage());
		}
		reply.throwOnError(); // does obviously not return normally if it throws
		return reply.getResult();
	}
	
	private static DMSObjectSpecifier ensureStreamable(
			final DMSObjectSpecifier specifier) throws DMSServiceException {
		if (specifier instanceof DMSObjectIdentity) {
			return (DMSObjectSpecifier) DMSObjectIdentity.create((DMSObjectIdentity) specifier);
		}
		return specifier;
	}

	private static List<DMSObjectSpecifier> ensureStreamable(
			final java.util.Collection<? extends DMSObjectSpecifier> specifiers) throws DMSServiceException {
		final List<DMSObjectSpecifier> result = new ArrayList<>(specifiers.size());
		for (final DMSObjectSpecifier specifier : specifiers) {
			result.add(ensureStreamable(specifier));
		}
		return result;
	}

	// --------------------------------------------------------------
	// ---
	// --- Service API implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public DMSDocumentImage getDocumentImage(final DMSObjectSpecifier documentSpecifier) throws DMSServiceException {
		final Object[] args = new Object[] { ensureStreamable(documentSpecifier) };
		return (DMSDocumentImage) callRemote(1, args);
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<DMSDocumentImage> getDocumentImages(final Collection<? extends DMSObjectSpecifier> documentSpecifiers) throws DMSServiceException {
		final Object[] args = new Object[] { ensureStreamable(documentSpecifiers) };
		return (List<DMSDocumentImage>) callRemote(2, args);
	}

	@Override
	public DMSFolderHandle getBasisFolder(final String basenumber) throws DMSServiceException {
		final Object[] args = new Object[] { basenumber };
		return (DMSFolderHandle) callRemote(3, args);
	}

	@Override
	public DMSFolderHandle getProjectFolder(final String projectnumber) throws DMSServiceException {
		final Object[] args = new Object[] { projectnumber };
		return (DMSFolderHandle) callRemote(4, args);
	}

	@Override
	public DMSFolderHandle getFolderByProperty(final DMSFolderHandle folder, final int propertyCode, final Object propertyValue) throws DMSServiceException {
		final Object[] args = new Object[] { folder, propertyCode, propertyValue };
		return (DMSFolderHandle) callRemote(5, args);
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<DMSFolderHandle> getFoldersByProperty(final DMSFolderHandle folder, final int propertyCode, final Object propertyValue) throws DMSServiceException {
		final Object[] args = new Object[] { folder, propertyCode, propertyValue };
		return (List<DMSFolderHandle>) callRemote(6, args);
	}

	@Override
	public DMSFolderHandle getDocumentFolder(final String projectnumber, final DMSDocumentType documentType) throws DMSServiceException {
		final Object[] args = new Object[] { projectnumber, documentType };
		return (DMSFolderHandle) callRemote(7, args);
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<DMSDocumentImage> getDocumentImages(final String projectnumber, final DMSDocumentType documentType) throws DMSServiceException {
		final Object[] args = new Object[] { projectnumber, documentType };
		return (List<DMSDocumentImage>) callRemote(8, args);
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<DMSDocumentImage> getDocumentImages(final DMSFolderHandle folder, final DMSDocumentType documentType) throws DMSServiceException {
		final Object[] args = { folder, documentType };
		return (List<DMSDocumentImage>) callRemote(9, args);
	}

	@Override
	public DMSFolderHandle getProjectContentFolder(final String projectnumber, final DMSFolderType folderType) throws DMSServiceException {
		final Object[] args = { projectnumber, folderType };
		return (DMSFolderHandle) callRemote(10, args);
	}

	@Override
	public DMSDocumentHandle putDocument(final DMSFolderHandle parentFolder, final String user, final FileImage document) throws DMSServiceException {
		final Object[] args = { parentFolder, user, document };
		return (DMSDocumentHandle) callRemote(11, args);
	}

	@Override
	public int countDocuments(final String projectnumber, final DMSDocumentType documentType) throws DMSServiceException {
		final Object[] args = { projectnumber, documentType };
		return (int) callRemote(12, args);
	}

	@SuppressWarnings("unchecked")
	@Override
	public Map<String, Integer> countFolderContents(final java.util.Collection<String> projects, final DMSFolderType folderType) throws DMSServiceException {
		final Object[] args = { projects, folderType };
		return (Map<String, Integer>) callRemote(13, args);
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<DMSDocumentImage> getAusfuehrungsplaene(final String projectnumber) throws DMSServiceException {
		final Object[] args = { projectnumber };
		return (List<DMSDocumentImage>) callRemote(14, args);
	}

	@Override
	public DMSDocumentImage getDocumentImage(final DMSDocumentTemplateCode templateCode) throws DMSServiceException {
		final Object[] args = { templateCode };
		return (DMSDocumentImage) callRemote(15, args);
	}

	@Override
	public void setDocumentType(final DMSDocumentHandle document, final DMSDocumentType documentType, final String user) throws DMSServiceException {
		final Object[] args = { document, documentType, user };
		callRemote(16, args);
	}

	@Override
	public DMSDocumentHandle getDocumentByProperty(final DMSFolderHandle folder, final int propertyCode, final Object propertyValue) throws DMSServiceException {
		final Object[] args = { folder, propertyCode, propertyValue };
		return (DMSDocumentHandle) callRemote(17, args);
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<DMSDocumentHandle> getDocumentsByProperty(final DMSFolderHandle folder, final int propertyCode, final Object propertyValue) throws DMSServiceException {
		final Object[] args = { folder, propertyCode, propertyValue };
		return (List<DMSDocumentHandle>) callRemote(18, args);
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<DMSObjectHandle> getFolderContents(final DMSFolderHandle folder) throws DMSServiceException {
		if (folder == null) {
			final String message = Msg.mk("Parameter 'Ordner' nicht gesetzt");
			throw new DMSServiceException(DMSServiceResultCode.INVALID_PARAMETER, message);
		}
		return (List<DMSObjectHandle>) callRemote(19, new Object[] { folder });
	}

	@Override
	public DMSPermissionSnapshot getUserPermissions(final String userName) throws DMSServiceException {
		return (DMSPermissionSnapshot) callRemote(20, new Object[] { userName });
	}

	@Override
	public int countFolderContents(final String projektNummer, final DMSFolderType folderType) throws DMSServiceException {
		final Object[] args = { projektNummer, folderType };
		return (Integer) callRemote(21, args);
	}

	// --------------------------------------------------------------
	// ---
	// --- Service API V2 implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public DMSObject locateBasisFolder(final String baseNumber) throws DMSServiceException {
		final Object[] args = new Object[] { baseNumber };
		return (DMSObject) callRemote(PacketDMSServiceRequest.FUNC_ID_LOCATEBASISFOLDER, args);
	}

	@Override
	public DMSObject locateProjectFolder(final String projectNumber) throws DMSServiceException {
		final Object[] args = new Object[] { projectNumber };
		return (DMSObject) callRemote(PacketDMSServiceRequest.FUNC_ID_LOCATEPROJECTFOLDER, args);
	}

	@Override
	public DMSObject locateProjectContentFolder(final String projectNumber, final DMSFolderType folderType) throws DMSServiceException {
		final Object[] args = new Object[] { projectNumber, folderType };
		return (DMSObject) callRemote(PacketDMSServiceRequest.FUNC_ID_LOCATECONTENTFOLDER, args);
	}

	@Override
	public DMSObject getObject(final DMSObjectIdentity objectId) throws DMSServiceException {
		final Object[] args = new Object[] { objectId };
		return (DMSObject) callRemote(PacketDMSServiceRequest.FUNC_ID_GETOBJECT, args);
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<DMSObject> getObjects(
			final java.util.Collection<? extends DMSObjectSpecifier> objectSpecifiers) throws DMSServiceException {
		final Object[] args = new Object[] { ensureStreamable(objectSpecifiers) };
		return (List<DMSObject>) callRemote(PacketDMSServiceRequest.FUNC_ID_GETOBJECTS, args);
	}

	@Override
	public long getMaxDocumentDate(final DMSObjectSpecifier folder) throws DMSServiceException {
		final Object[] args = new Object[] { ensureStreamable(folder) };
		return (long) callRemote(PacketDMSServiceRequest.FUNC_ID_GETMAXDOCUMENTDATE, args);
	}

	@Override
	public long getMinDocumentDate(final DMSObjectSpecifier folder) throws DMSServiceException {
		final Object[] args = new Object[] { ensureStreamable(folder) };
		return (long) callRemote(PacketDMSServiceRequest.FUNC_ID_GETMINDOCUMENTDATE, args);
	}

}

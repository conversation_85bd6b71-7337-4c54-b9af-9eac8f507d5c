package ch.eisenring.gui.util;

import java.awt.Color;

/**
 * RGB Color manipulation utility
 */
public class ColorRGB {

	public final static int ALPHA_MASK = 0xFF000000;

	protected ColorRGB() {
	}

	// --------------------------------------------------------------
	// ---
	// --- Static utility methods
	// ---
	// --------------------------------------------------------------
	/**
	 * Takes an int and clamps its value between 0 and 255.
	 */
	public final static int clampUnsigned(final int i) {
		return i > 255 ? 255 : (i < 0 ? 0 : i);
	}

	public final static int scaleUnsigned(final int i, final float f) {
		return clampUnsigned((int) ((i & 0xFF) * f));
	}

	/**
	 * Multiplies the R, G and B channel with factor, creating a new
	 * color. Alpha is not modified.
	 */
	public static int brightness(int argb, final float factor) {
		return (argb & ALPHA_MASK)
			 | scaleUnsigned(argb >> 16, factor) << 16
			 | scaleUnsigned(argb >>  8, factor) << 8
			 | scaleUnsigned(argb      , factor);
	}

	public static Color brightness(final Color color, final float factor) {
		return new Color(brightness(color.getRGB(), factor), true);
	}

	/**
	 * Returns a gray shade of the same brightness as the given RGB.
	 * Alpha is not modified.
	 */
	public static int toGray(final int argb) {
		final int l = (
				 ((argb >> 16) & 0xFF) * 30 
			   + ((argb >>  8) & 0xFF) * 59
		       + ((argb      ) & 0xFF) * 11
        ) / 100;
		return (argb & ALPHA_MASK) | (l << 16) | (l << 8) | l;
	}

	/**
	 * Alters the saturation of RGB by factor. Alpha is not modified.
	 * Factors >= 1.0 leave the color unchanged, a factor <= 0.0 returns a pure gray. 
	 */
	public static int saturation(final int argb, final float factor) {
		if (factor >= 1F)
			return argb;
		if (factor <= 0F)
			return toGray(argb);
		final int r = (argb >> 16) & 0xFF;
		final int g = (argb >>  8) & 0xFF;
		final int b = (argb      ) & 0xFF;
		final int l = (r*30 + g*59 + b*11) / 100;
		final float gray = (1F - factor) * l; 
		return (argb & ALPHA_MASK)
				| clampUnsigned((int) ((r * factor) + gray)) << 16
				| clampUnsigned((int) ((g * factor) + gray)) << 8
				| clampUnsigned((int) ((b * factor) + gray));
	}

	/**
	 * Replaces alpha with the given value
	 */
	public static Color alpha(final Color color, final int alpha) {
		return new Color((color.getRGB() & ~ALPHA_MASK) | (alpha & ALPHA_MASK), true);
	}

	/**
	 * Mixes two colors "c = (a + b) / 2"
	 */
	public static int mix(final int argb1, final int argb2) {
		final int h1 = argb1 >> 1, h2 = argb2 >> 1;
		final int a = ((h1 & 0x7F800000) + (h2 & 0x7F800000)) & 0xFF000000;
		final int r = ((h1 & 0x007F8000) + (h2 & 0x007F8000)) & 0x00FF0000;
		final int g = ((h1 & 0x00007F80) + (h2 & 0x00007F80)) & 0x0000FF00;
		final int b = ((argb1 & 0xFF) + (argb2 & 0xFF)) >> 1;
		return a | r | g | b;
	}

}

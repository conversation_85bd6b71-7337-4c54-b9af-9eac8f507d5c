package ch.eisenring.gui.resource.images;

import static ch.eisenring.core.resource.ImageResource.FOUR_STANDARD_SIZES;
import ch.eisenring.core.resource.Drawable;
import ch.eisenring.core.resource.ImageResource;
import ch.eisenring.core.resource.image.MIPImgResource;
import ch.eisenring.core.resource.image.VectorImageResource;

public interface Images extends ch.eisenring.core.resource.Images {

	public final static ImageResource TRANSPARENT =
			new MIPImgResource("transparent*.png", Images.class, 16);
	public final static ImageResource SCREENSHOT =
			new MIPImgResource("screenshot*.png", Images.class, 16, 24);
	public final static ImageResource REFRESH =
			new MIPImgResource("refresh*.png", Images.class, 16, 24, 32, 48);

	public final static ImageResource ADD =
			new MIPImgResource("add*.png", Images.class, FOUR_STANDARD_SIZES);
	public final static ImageResource COPY =
			new MIPImgResource("copy*.png", Images.class, 16, 24, 32, 48, 128);
	public final static ImageResource EDIT =
			new MIPImgResource("edit*.png", Images.class, 16, 32, 48, 64, 128);
	public final static ImageResource DELETE =
			new MIPImgResource("delete*.png", Images.class, FOUR_STANDARD_SIZES);
	public final static ImageResource SEARCH =
			new MIPImgResource("search*.png", Images.class, 16, 24, 32, 48, 64, 128);

	public final static ImageResource CANCEL =
			new MIPImgResource("cancel*.png", Images.class, 16, 22, 32, 48, 64, 128);
	public final static ImageResource QUESTION =
			new MIPImgResource("question*.png", Images.class, 16, 32, 128);
	public final static ImageResource STOP =
			new MIPImgResource("stop*.png", Images.class, 16, 22, 32, 48, 64, 128);
	public final static ImageResource COG =
			new MIPImgResource("cog*.png", Images.class, 16, 24, 32, 48, 128);

	public final static ImageResource DISK =
			new MIPImgResource("disk*.png", Images.class, 16, 24, 32, 256);
	public final static ImageResource EXCEL =
			new MIPImgResource("excel*.png", Images.class, FOUR_STANDARD_SIZES);
	public final static ImageResource FETCH_DATA =
			new MIPImgResource("fetch_data*.png", Images.class, FOUR_STANDARD_SIZES);
	public final static Drawable ARROW_EAST_VECTOR = new VectorImageResource(
			new double[] { 0, 1, 0 },
			new double[] { 0, 1, 2 },
			1.00D);

	public final static Drawable ARROW_WEST_VECTOR = new VectorImageResource(
			new double[] { 1, 0, 1 },
			new double[] { 0, 1, 2 },
			1.00D);

	public final static Drawable ARROW_NORTH_VECTOR = new VectorImageResource(
			new double[] { 0, 1, 2 },
			new double[] { 1, 0, 1},
			1.00D);

	public final static Drawable ARROW_SOUTH_VECTOR = new VectorImageResource(
			new double[] { 0, 2, 1 },
			new double[] { 0, 0, 1 },
			1.00D);

	public final static ImageResource ARROW_EAST =
			new MIPImgResource("arrow/east*.png", Images.class, 9, 11, 16);
	public final static ImageResource ARROW_WEST =
			new MIPImgResource("arrow/west*.png", Images.class, 9, 11, 16);
	public final static ImageResource ARROW_NORTH =
			new MIPImgResource("arrow/north*.png", Images.class, 9, 11, 16);
	public final static ImageResource ARROW_SOUTH =
			new MIPImgResource("arrow/south*.png", Images.class, 9, 11, 16);

	public final static ImageResource BACK =
			new MIPImgResource("back*.png", Images.class, 16, 24, 32, 64, 128);
	public final static ImageResource FORWARD =
			new MIPImgResource("forward*.png", Images.class, 16, 24, 32, 64, 128);
	public final static ImageResource CALENDAR =
			new MIPImgResource("calendar*.png", Images.class, 16, 22, 32, 48, 64, 128);

	public final static ImageResource OPENFOLDER =
			new MIPImgResource("gui_openfolder*.png", Images.class, 16, 32);
	
	public final static ImageResource FONT =
			new MIPImgResource("font*.png", Images.class, 16, 32, 128);
	public final static ImageResource WAIT =
			new MIPImgResource("wait*.png", Images.class, 16, 32);
	
	public final static ImageResource CHECKBOX_FALSE =
			new MIPImgResource("checkbox/false*.png", Images.class, 16, 128, 512);
	public final static ImageResource CHECKBOX_TRUE_DISABLED =
			new MIPImgResource("checkbox/true1-*.png", Images.class, 16, 128, 512);
	public final static ImageResource CHECKBOX_TRUE_ENABLED =
			new MIPImgResource("checkbox/true2-*.png", Images.class, 16, 128, 512);

	public final static ImageResource SPHERE_BASE =
			new MIPImgResource("sphere/base*.png", Images.class, 16, 128);
	public final static ImageResource SPHERE_RED =
			new MIPImgResource("sphere/red*.png", Images.class, 16, 32, 128);
	public final static ImageResource SPHERE_ORANGE =
			new MIPImgResource("sphere/orange*.png", Images.class, 16, 32, 128);
	public final static ImageResource SPHERE_YELLOW =
			new MIPImgResource("sphere/yellow*.png", Images.class, 16, 32, 128);
	public final static ImageResource SPHERE_GREEN =
			new MIPImgResource("sphere/green*.png", Images.class, 16, 32, 128);
	public final static ImageResource SPHERE_BLUE =
			new MIPImgResource("sphere/blue*.png", Images.class, 16, 32, 128);
	public final static ImageResource SPHERE_WHITE =
			new MIPImgResource("sphere/white*.png", Images.class, 16, 32, 128);
	public final static ImageResource SPHERE_BLACK =
			new MIPImgResource("sphere/black*.png", Images.class, 16, 32, 128);

	public final static ImageResource DIALOG_OK =
			new MIPImgResource("dialog_ok*.png", Images.class, 16, 32, 128);
	public final static ImageResource DIALOG_APPLY =
			new MIPImgResource("dialog_apply*.png", Images.class, 16, 32, 128);
	public final static ImageResource DIALOG_CANCEL =
			new MIPImgResource("dialog_cancel*.png", Images.class, 16, 32, 128);

	public final static ImageResource UNDO =
			new MIPImgResource("undo*.png", Images.class, 16, 32, 64);
	public final static ImageResource REDO =
			new MIPImgResource("redo*.png", Images.class, 16, 32, 64);

	/**
	 * Ascending sort order icon
	 */
	public final static ImageResource ASCENDING =
			new MIPImgResource("ascending*.png", Images.class, 16, 32);
	
	/**
	 * Descending sort order icon
	 */
	public final static ImageResource DESCENDING =
			new MIPImgResource("descending*.png", Images.class, 16, 32);

}

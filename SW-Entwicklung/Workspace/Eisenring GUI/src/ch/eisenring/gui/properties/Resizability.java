package ch.eisenring.gui.properties;

public final class Resizability implements GUIProperty {

	public final static Resizability TRUE = new Resizability(true);
	public final static Resizability FALSE = new Resizability(false);
	
	private final boolean value;
	
	public Resizability(final boolean value) {
		this.value = value;
	}
	
	public boolean booleanValue() {
		return value;
	}

	public static Resizability valueOf(final boolean value) {
		return value ? TRUE : FALSE;
	}

	// --------------------------------------------------------------
	// ---
	// --- Object overrides
	// ---
	// --------------------------------------------------------------
	@Override
	public int hashCode() {
		return value ? 1 : 0;
	}

	@Override
	public boolean equals(final Object o) {
		return o instanceof Resizability && ((Resizability) o).value == value;
	}
	
}

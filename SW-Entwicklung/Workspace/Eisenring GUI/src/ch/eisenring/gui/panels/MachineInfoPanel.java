package ch.eisenring.gui.panels;

import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.format.api.Formats;
import ch.eisenring.core.platform.Platform;
import ch.eisenring.core.platform.PlatformPath;

@SuppressWarnings("serial")
public class MachineInfoPanel extends InfoTextPanel {

	@Override
	public String getInfoText() {
		System.gc();
		return getMachineInfoText();
	}

	public static String getMachineInfoText() {
		final StringMaker b = StringMaker.obtain(256);
		appendMachineInfoText(b);
		return b.release();
	}

	public static void appendMachineInfoText(final StringMaker target) {
		final Runtime r = Runtime.getRuntime();
		final Platform p = Platform.getPlatform();

		target.append("Computer Informationen (");
		target.append(System.currentTimeMillis(), TimestampUtil.DATETIME);
		target.append(")\n");

		target.append("Benutzername: ");
		target.append(p.getLoginName());
		target.append("\nComputername: ");
		target.append(p.getLocalHostName());
		target.append("\n");
		
		target.append("\n");
		target.append("Java Runtime Version: ");
		target.append(System.getProperty("java.version"));
		target.append(" (");
		target.append(System.getProperty("java.vendor"));
		target.append(")\nJava Runtime Architektur: ");
		target.append(System.getProperty("sun.arch.data.model"));
		target.append(" bit\nComputer Architektur: ");
		target.append(System.getProperty("os.arch"));
		target.append("\n");
		
		target.append("\n");
		final long limit = r.maxMemory();
		final long allocated = r.totalMemory();
		final long free = r.freeMemory();
		target.append("Anzahl Prozessoren/Kerne: ");
		target.append(r.availableProcessors());
		target.append("\nArbeitsspeicher Limit: ");
		target.append(limit, Formats.BYTESIZE);
		target.append("\nArbeitsspeicher reserviert: ");
		target.append(allocated, Formats.BYTESIZE);
		target.append("\nArbeitsspeicher belegt: ");
		target.append(allocated - free, Formats.BYTESIZE);

		target.append("\n\nBetriebssystem: ");
		target.append(p.getPlatformName());
		target.append("\nPfad für temporäre Dateien: ");
		target.append(p.getPath(PlatformPath.APP_TEMP).getAbsolutePath());
		target.append("\nPfad für Benutzereinstellungen: ");
		target.append(p.getPath(PlatformPath.SETTINGS).getAbsolutePath());
	}

}

package ch.eisenring.gui.screenshot;

import java.awt.Component;
import java.awt.Window;
import java.awt.image.BufferedImage;

import ch.eisenring.core.util.ScreenShotUtil;
import ch.eisenring.gui.action.AbstractAction;
import ch.eisenring.gui.util.GUIUtil;

public abstract class ActionScreenshotAbstract extends AbstractAction {

	protected final Component component;

	public ActionScreenshotAbstract(final Component component, final Object ... properties) {
		super(properties);
		this.component = component;
	}

	/**
	 * Controls the scale of the screen shot taken (default is 1.0).
	 * Can be overwritten to create higher resolution screen shots (e.g. for printing)
	 */
	protected double getScale() {
		return 1D;
	}

	@Override
	protected final void performAction() {
		GUIUtil.invokeLater(new Runnable() {
			@Override
			public void run() {
				performAction(getScreenshotImage(), getScreenshotName());
			}
		});
	}

	protected abstract void performAction(final BufferedImage image, final String fileName);

	@Override
	protected boolean isEnabledImpl() {
		return component != null;
	}
	
	protected BufferedImage getScreenshotImage() {
		if (component == null) {
			showError("Interner Fehler beim Erstellen des Screenshots");
			return null;
		} else {
			final ScreenShotUtil util = new ScreenShotUtil();
			final BufferedImage screenShot = util.screenShot(component, getScale());
			if (screenShot == null) {
				showError("Interner Fehler beim Erstellen des Screenshots");
				return null;
			}
			return screenShot;
		}
	}

	protected String getScreenshotName() {
		final String name;
		final Window window = getWindow();
		if (window != null) {
			name = GUIUtil.getTitle(window); 
		} else if (component.getName() != null) {
			name = component.getName();
		} else {
			name = "Unbenannt";
		}
		final ScreenShotUtil util = new ScreenShotUtil();
		return util.toSuitableFileName(name, "png");
	}

	protected void showError(final String errorMessage) {
	}

	protected Window getWindow() {
		return GUIUtil.getWindowAncestor(component);
	}

}

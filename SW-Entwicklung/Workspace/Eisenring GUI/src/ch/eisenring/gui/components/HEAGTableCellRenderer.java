package ch.eisenring.gui.components;

import java.awt.Component;
import java.awt.Image;

import javax.swing.Icon;
import javax.swing.ImageIcon;
import javax.swing.JCheckBox;
import javax.swing.JLabel;
import javax.swing.JTable;
import javax.swing.UIManager;
import javax.swing.border.Border;
import javax.swing.border.EmptyBorder;
import javax.swing.plaf.UIResource;
import javax.swing.table.DefaultTableCellRenderer;
import javax.swing.table.TableCellRenderer;
import javax.swing.table.TableModel;

import ch.eisenring.core.collections.Lookup;
import ch.eisenring.core.collections.Map;
import ch.eisenring.core.collections.impl.HashMap;
import ch.eisenring.core.resource.Drawable;
import ch.eisenring.gui.util.ConversionUtil;

@SuppressWarnings("serial")
final class HEAGTableCellRenderer implements TableCellRenderer {

	static interface Renderer extends TableCellRenderer {
		public void setHorizontalAlignment(final int alignment);
	}

	static class GenericRenderer extends DefaultTableCellRenderer implements Renderer {
		@Override
		public Component getTableCellRendererComponent(final JTable table, final Object value,
				final boolean isSelected, final boolean hasFocus, final int row, final int column) {
			final JLabel l = (JLabel) super.getTableCellRendererComponent(table, value, isSelected, hasFocus, row, column);
			if (value instanceof Icon) {
				l.setText(null);
				l.setIcon((Icon) value);
			} else if (value instanceof Drawable) {
				final Icon icon = ((Drawable) value).getIcon(16);
				l.setText(null);
				l.setIcon(icon);
			} else if (value instanceof Image) {
				l.setText(null);
				l.setIcon(new ImageIcon((Image) value));
			} else if (value instanceof CharSequence) {
				// manage multi-line text (causes issues in legacy tables)
//				final CharSequence text = Strings.toCharSequence(value);
//				if (Strings.indexOf(text, '\n') >= 0) {
//					final StringMaker b = StringMaker.obtain();
//					b.append("<html>");
//					Strings.toHTMLEntities(text, b);
//					l.setText(b.release());
//				} else {
//					// already set
//				}
				l.setIcon(null);
			} else {
				l.setIcon(null);
			}
			return l;
		}
	}

	static class CheckBoxRenderer extends JCheckBox implements Renderer, UIResource {
        private static final Border NO_FOCUS_BORDER = new EmptyBorder(1, 1, 1, 1);

        {
            setBorderPainted(true);
        }
       
        public Component getTableCellRendererComponent(final JTable table, final Object value,
        		final boolean isSelected, final boolean hasFocus, final int row, final int column) {
		    final boolean b = ConversionUtil.convert(value, false);
		    setSelected(b);

		    if (isSelected) {
		        setForeground(table.getSelectionForeground());
		        setBackground(table.getSelectionBackground());
		    } else {
		        setForeground(table.getForeground());
		        setBackground(table.getBackground());
		    }

            setBorder(hasFocus ? UIManager.getBorder("Table.focusCellHighlightBorder") : NO_FOCUS_BORDER);
            return this;
        }
    }

	/**
	 * Renderer's available
	 */
	@SuppressWarnings("serial")
	final static Lookup<Class<?>, Class<?>> RENDERER_CLASSES = Map.of(
			Boolean.class, CheckBoxRenderer.class,
			boolean.class, CheckBoxRenderer.class,
			Object.class, GenericRenderer.class);

	/**
	 * Renderer's in use
	 */
	final Lookup<Class<?>, Renderer> RENDERERS = new HashMap<>();
	
	/**
	 * Alignment desired for this renderer
	 */
	final int alignment;
	
	HEAGTableCellRenderer(final int alignment) {
		this.alignment = alignment;
	}

	@Override
	public Component getTableCellRendererComponent(final JTable table, final Object value,
			final boolean isSelected, final boolean hasFocus, final int row, final int column) {
		final TableModel model = table.getModel();
		Class<?> theClass = model.getColumnClass(column);
		if (theClass == null || Object.class.equals(theClass)) {
			// select renderer by value's class
			if (value == null) {
				theClass = Object.class;
			} else {
				theClass = value.getClass();
			}
		}
		final Renderer renderer = getRenderer(theClass);
		return renderer.getTableCellRendererComponent(table, value, isSelected, hasFocus, row, column);
	}

	/**
	 * Gets the proper renderer for values of class
	 */
	public Renderer getRenderer(final Class<?> valueClass) {
		Renderer renderer = RENDERERS.get(valueClass);
		if (renderer != null)
			return renderer;
		Class<?> rendererClass = RENDERER_CLASSES.get(valueClass);
		if (rendererClass == null)
			rendererClass = GenericRenderer.class;
		try {
			renderer = (Renderer) rendererClass.getDeclaredConstructor().newInstance();
		} catch (final Exception e) {
			throw new RuntimeException(e.getMessage(), e);
		}
		renderer.setHorizontalAlignment(alignment);
		RENDERERS.put(valueClass,  renderer);
		return renderer;
	}

}

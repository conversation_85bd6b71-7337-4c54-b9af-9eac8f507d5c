package ch.eisenring.gui.components;

import java.awt.Dimension;
import java.awt.Graphics;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.InputEvent;
import java.awt.event.MouseEvent;
import java.awt.event.MouseListener;
import java.beans.PropertyChangeEvent;
import java.beans.PropertyChangeListener;
import java.util.concurrent.atomic.AtomicBoolean;

import javax.swing.Action;
import javax.swing.Icon;
import javax.swing.JComponent;

import ch.eisenring.core.resource.Drawable;
import ch.eisenring.gui.action.AbstractAction;
import ch.eisenring.gui.action.ActionUtil;
import ch.eisenring.gui.properties.Clickability;
import ch.eisenring.gui.util.GUIUtil;

/**
 * This class provides a pseudo-button, usually used to accompany a field
 * (Prime example is a date field with calendar button on the side).
 * 
 * Technically this component is not a button in the "Swing" sense, its really
 * a custom component that behaves like a button (the behavior of hacked over
 * buttons is not satisfactory in this case).
 * As such it can be customized in many ways, for example as drag source.
 * 
 * This component is not focusable by default and its background is transparent,
 * also it has no border. The appearance is mostly independent of L&F.
 * The look is well suited for tool bar buttons.
 */
@SuppressWarnings("serial")
public class HEAGFloatingButton extends JComponent {

    // --------------------------------------------------------------
	// ---
	// --- Keyboard management
	// ---
	// --------------------------------------------------------------
// TODO: debug why it doesn't work
//	protected final static KeyListener KEY_TRIGGER_LISTENER = new KeyAdapter() {
//		@Override
//		public void keyReleased(final KeyEvent event) {
//			if (!isEventValid(event))
//				return;
//			final boolean trigger;
//			switch (event.getKeyCode()) {
//				case KeyEvent.VK_ENTER:
//				case KeyEvent.VK_SPACE:
//					trigger = true;
//					break;
//				default:
//					trigger = false;
//					break;
//			}
//			if (!trigger)
//				return;
//			event.consume();
//			final HEAGFloatingButton button = (HEAGFloatingButton) event.getSource();
//			final ActionEvent actionEvent = new ActionEvent(
//					button, ActionEvent.ACTION_PERFORMED,
//					(String) null, event.getWhen(), event.getModifiers());
//			button.fireActionPerformed(actionEvent);
//		}
//	};

    // --------------------------------------------------------------
	// ---
	// --- Mouse management
	// ---
	// --------------------------------------------------------------
	protected final static MouseListener MOUSE_LISTENER = new MouseListener() {
		@Override
		public void mouseReleased(final MouseEvent event) {
			if (!isEventValid(event))
				return;
			final HEAGFloatingButton button = (HEAGFloatingButton) event.getSource();
			if (event.getButton() == MouseEvent.BUTTON1) {
				button.setPressed(false);
			}
			if (event.isPopupTrigger() && button.isEnabled()) {
				button.showContextMenu(event);
			}
		}
		@Override
		public void mousePressed(final MouseEvent event) {
			if (!isEventValid(event))
				return;
			final HEAGFloatingButton button = (HEAGFloatingButton) event.getSource();
			if (event.getButton() == MouseEvent.BUTTON1) {
				button.setPressed(true);
			}
			if (event.isPopupTrigger() && button.isEnabled()) {
				button.showContextMenu(event);
			}
		}
		
		@Override
		public void mouseExited(final MouseEvent event) {
			if (isEventValid(event) && event.getButton() == MouseEvent.BUTTON1) {
				((HEAGFloatingButton) event.getSource()).setPressed(false);
			}
		}
		@Override
		public void mouseEntered(final MouseEvent event) {
			if (isEventValid(event) && event.getButton() == MouseEvent.BUTTON1) {
				((HEAGFloatingButton) event.getSource()).setPressed(false);
			}
		}
	
		@Override
		public void mouseClicked(final MouseEvent event) {
			if (!isEventValid(event))
				return;
			final HEAGFloatingButton button = (HEAGFloatingButton) event.getSource();
			if (event.getButton() == MouseEvent.BUTTON1) {
				if (button.isEnabled()) {
					// generate action event
					final ActionEvent actionEvent = new ActionEvent(
							button, ActionEvent.ACTION_PERFORMED,
							(String) null, event.getWhen(), event.getModifiersEx());
					button.fireActionPerformed(actionEvent);
					// trigger click
					if (button.isClickable() && button.isEnabled())
						button.buttonClicked(event);
				}
				event.consume();
			}
		}
	};

	protected AbstractAction action;
	protected Drawable imageResource;
	protected int defaultSize;
	private PropertyChangeListener propertyChangeListener;

	public HEAGFloatingButton(final Drawable imageResource, final int size) {
		this.imageResource = imageResource;
		this.defaultSize = size;
		initComponent();
	}

	public HEAGFloatingButton(final Drawable imageResource, final int size, final boolean clickable) {
		this.imageResource = imageResource;
		this.defaultSize = size;
		setClickable(clickable);
		initComponent();
	}

	public HEAGFloatingButton(final AbstractAction action) {
		this(action, 16);
	}

	public HEAGFloatingButton(final AbstractAction action, final int size) {
		this.defaultSize = size;
		this.action = action;
		action.addPropertyChangeListener(getPropertyChangeListener());
		setClickable(true);
		initComponent();
	}
	
	private PropertyChangeListener getPropertyChangeListener() {
		if (propertyChangeListener == null) {
			propertyChangeListener = new PropertyChangeListener() {
				@Override
				public void propertyChange(final PropertyChangeEvent event) {
					ActionUtil.updateComponent(HEAGFloatingButton.this, action);
					if (isShowing())
						repaint();
				}
			};
		}
		return propertyChangeListener;
	}

	private void initComponent() {
		setOpaque(false);
		setBorder(null);
		setFocusable(false);
		addMouseListener(MOUSE_LISTENER);
		//addKeyListener(KEY_TRIGGER_LISTENER);
	}

	/**
	 * Checks if input event is not consumed and the source
	 * is an instance of HEAGFloatingButton.
	 */
	protected static boolean isEventValid(final InputEvent event) {
		return !event.isConsumed() && (event.getSource() instanceof HEAGFloatingButton);
	}

	protected Drawable getImageResource() {
		if (action != null) {
			final Drawable result = action.getIconResource();
			if (result != null)
				return result;
		}
		return imageResource;
	}

	protected Icon getIcon() {
		final Drawable resource = getImageResource();
		if (resource == null)
			return null;
		return resource.getIcon(defaultSize);
	}

	// --------------------------------------------------------------
	// ---
	// --- Action Listener management
	// ---
	// --------------------------------------------------------------
    /**
     * Adds an <code>ActionListener</code> to the button.
     * @param l the <code>ActionListener</code> to be added
     */
    public void addActionListener(final ActionListener l) {
        listenerList.add(ActionListener.class, l);
    }
    
    /**
     * Removes an <code>ActionListener</code> from the button.
     * If the listener is the currently set <code>Action</code>
     * for the button, then the <code>Action</code>
     * is set to <code>null</code>.
     *
     * @param l the listener to be removed
     */
    public void removeActionListener(final ActionListener l) {
	    listenerList.remove(ActionListener.class, l);
    }
	
    protected void fireActionPerformed(final ActionEvent event) {
        final Object[] listeners = listenerList.getListenerList();
        // Process the listeners last to first, notifying
        // those that are interested in this event
        for (int i = listeners.length-2; i>=0; i-=2) {
            if (listeners[i] == ActionListener.class) {
                ((ActionListener)listeners[i+1]).actionPerformed(event);
            }          
        }
        if (action != null) {
        	action.actionPerformed(event);
        }
    }

	// --------------------------------------------------------------
	// ---
	// --- Sizing
	// ---
	// --------------------------------------------------------------
	@Override
	public Dimension getPreferredSize() {
		if (isPreferredSizeSet())
			return super.getPreferredSize();
		final Icon icon = getIcon();
		final int w, h;
		if (icon == null) {
			w = defaultSize;
			h = defaultSize;
		} else {
			w = icon.getIconWidth() + 1;
			h = icon.getIconHeight() + 1;
		}
		return new Dimension(w, h);
	}

	@Override
	public Dimension getMinimumSize() {
		if (isMinimumSizeSet())
			return super.getMinimumSize();
		return getPreferredSize();
	}

	// --------------------------------------------------------------
	// ---
	// --- Clickable/Animation control
	// ---
	// --------------------------------------------------------------
	/**
	 * Controls if the button can be clicked.
	 * Setting this to false disables the click animation,
	 * as well as any calls to the buttonClicked() hook.
	 * (but it does not influence the context menu ability)
	 */
	private final AtomicBoolean clickable = new AtomicBoolean(true);
	
	/**
	 * Returns true if the button is "clickable"
	 */
	public final boolean isClickable() {
		return clickable.get();
	}
	

	/**
	 * Gets the clickable property 
	 */
	public final Clickability getClickable() {
		return Clickability.valueOf(clickable.get());
	}

	/**
	 * Controls if the button can be clicked.
	 */
	public final void setClickable(final boolean clickable) {
		if (this.clickable.compareAndSet(!clickable, clickable)) {
			setPressed(false);
		}
	}

	/**
	 * Controls if the button can be clicked.
	 */
	public final void setClickable(final Clickability clickable) {
		setClickable(Clickability.TRUE.equals(clickable));
	}

	public AbstractAction getAction() {
		return action;
	}

	public void setAction(final Action action) {
		final PropertyChangeListener listener = getPropertyChangeListener();
		final Action oldAction = getAction();
		if (oldAction != null)
			oldAction.removePropertyChangeListener(listener);
		if (action != null) {
			action.addPropertyChangeListener(listener);
			ActionUtil.updateComponent(this, action);
		}
	}

	// --------------------------------------------------------------
	// ---
	// --- Pressed state management
	// ---
	// --------------------------------------------------------------
	private final AtomicBoolean pressed = new AtomicBoolean();
	
	/**
	 * Returns true if the button is currently held down
	 * (the mouse pointer hover over the button and the left button is pressed)
	 */
	protected final boolean isPressed() {
		return pressed.get() && isEnabled();
	}

	/**
	 * Sets the "pressed" status of the button.
	 * If the state is different from the current state,
	 * this will trigger a repaint of the button.
	 * If the "clickable" property is set to false, calls to this
	 * method with a "true" argument will be silently converted into
	 * a "false" argument, that is the pressed property can not be set. 
	 */
	protected final void setPressed(final boolean pressed) {
		boolean newState = pressed && isClickable();
		if (this.pressed.compareAndSet(!newState, newState))
			repaint();
	}

	// --------------------------------------------------------------
	// ---
	// --- Painting
	// ---
	// --------------------------------------------------------------
	@Override
	public boolean isOpaque() {
		return false;
	}

	@Override
	protected void paintComponent(final Graphics g) {
		final Icon icon = getIcon();
		if (icon == null)
			return;
		final int offset = isPressed() ? 1 : 0;
		final int x = (getWidth() - icon.getIconWidth()) >> 1;
		final int y = (getHeight() - icon.getIconHeight()) >> 1;
		g.setPaintMode();
		GUIUtil.drawIcon(this, g, icon, x + offset, y + offset, isEnabled());
	}

	// --------------------------------------------------------------
	// ---
	// --- Customizing/Convenience hooks
	// ---
	// --------------------------------------------------------------
	/**
	 * Called when the button is clicked, no need to use an action listener
	 * when subclassing anyway. Just override the method and be done.
	 */
	public void buttonClicked(final MouseEvent event) {
		buttonClicked();
	}

	/**
	 * Called when the button is clicked, no need to use an action listener
	 * when subclassing anyway. Just override the method and be done.
	 */
	public void buttonClicked() {
	}

	/**
	 * Called when the button is clicked to show the context menu.
	 * When overwritten, take care to consume the event if the method
	 * responds to the event.
	 */
	public void showContextMenu(final MouseEvent event) {
	}

}

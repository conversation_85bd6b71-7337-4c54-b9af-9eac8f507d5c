package ch.eisenring.gui.components.code;

import java.awt.Dimension;
import java.awt.GridBagLayout;

import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.codetype.CodeSetCoder;
import ch.eisenring.core.collections.Set;
import ch.eisenring.core.tag.TagSet;
import ch.eisenring.core.tag.Tags;
import ch.eisenring.gui.GridBagConstraints;
import ch.eisenring.gui.resource.images.Images;
import ch.eisenring.gui.window.AbstractBaseDialog;
import ch.eisenring.gui.window.DialogTags;
import ch.eisenring.gui.window.SACButtonPanel;
import ch.eisenring.gui.window.WindowTags;

@SuppressWarnings("serial")
public abstract class HEAGCodeSelectionDialog extends AbstractBaseDialog {

	private Class<AbstractCode> codeTypeClass;
	private CodeSetCoder<AbstractCode> codeSetCoder;
	protected final HEAGCodeSelectionPane<AbstractCode> selectionPane;
	protected final SACButtonPanel buttonPanel = new SACButtonPanel();

	@SuppressWarnings("unchecked")
	protected HEAGCodeSelectionDialog(final Tags tags) {
		super(extendTags(tags));
		// determine codeTypeClass and/or CodeSetCoder
		{
			final CodeSetCoder<AbstractCode> coder = tags.get(HEAGCodeSelectionTags.CODE_CODER, (CodeSetCoder<AbstractCode>) null);
			if (coder == null) {
				codeTypeClass = tags.get(HEAGCodeSelectionTags.CODE_TYPECLASS, (Class<AbstractCode>) null); 
				codeSetCoder = new CodeSetCoder.Default<AbstractCode>(codeTypeClass);
			} else {
				codeSetCoder = (CodeSetCoder<AbstractCode>) coder;
				codeTypeClass = codeSetCoder.getCodeTypeClass();
			}
			if (codeTypeClass == null)
				throw new IllegalArgumentException("codeTypeClass and/or codeSetCoder tags missing");
		}
		this.selectionPane = new HEAGCodeSelectionPane<>(codeTypeClass, selectionTags(tags));
		initComponents();
		initLayout();
		pack();
	}

	private static Tags extendTags(final Tags baseTags) {
		final TagSet result = new TagSet(baseTags);
		result.add(WindowTags.LAYOUTMANAGER, new GridBagLayout());
		result.addMissing(WindowTags.MINIMUM_SIZE, new Dimension(400, 300));
		result.addMissing(WindowTags.TITLE, "Code-Auswahl");
		result.addMissing(WindowTags.ICON, Images.COG);
		result.addMissing(DialogTags.MODALITY, DialogTags.MODALITY_MODAL);		
		return result;
	}

	private static Tags selectionTags(final Tags tags) {
		final TagSet result = new TagSet(tags);
		result.addMissing(HEAGCodeSelectionTags.SHOW_NULLS, Boolean.TRUE);
		result.addMissing(HEAGCodeSelectionTags.SHOW_RETIRED, Boolean.TRUE);
		return result;
	}

	private void initComponents() {
		buttonPanel.configureSaveButton("Ok", "Auswahl übernehmen und Fenster schliessen");
		buttonPanel.configureApplyButton(null, null);
	}

	/**
	 * Provides the CodeSetCoder for this dialog.
	 * 
	 * This is either the once supplied by the tags to the constructor,
	 * or if none was supplied, a default constructed for the specified CodeTypeClass.
	 */
	protected CodeSetCoder<AbstractCode> getCodeSetCoder() {
		return codeSetCoder;
	}

	private void initLayout() {
		add(selectionPane, GridBagConstraints.field(0, 0).weight(1, 1).fill(GridBagConstraints.BOTH));
		add(buttonPanel, GridBagConstraints.field(0, 1).insets(0));
	}

	/**
	 * Gets the current selection
	 */
	public Set<AbstractCode> getSelection() {
		return selectionPane.getSelection();
	}

	/**
	 * Sets the current selection
	 */
	public void setSelection(final java.util.Collection<AbstractCode> selection) {
		selectionPane.setSelection(selection);
	}

}

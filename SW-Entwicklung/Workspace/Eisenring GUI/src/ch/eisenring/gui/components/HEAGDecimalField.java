package ch.eisenring.gui.components;

import static ch.eisenring.gui.util.TextFieldFocusAdapter.ON_GAIN_SELECTALL;

import java.awt.event.FocusEvent;
import java.awt.event.FocusListener;
import java.text.DecimalFormat;

import javax.swing.JComponent;
import javax.swing.JTextField;
import javax.swing.SwingConstants;

import ch.eisenring.core.datatypes.primitives.Primitives;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.util.CharacterFilter;
import ch.eisenring.gui.ValueEditorBinding;
import ch.eisenring.gui.interfaces.ValueEditor;
import ch.eisenring.gui.model.LimitingDocument;
import ch.eisenring.gui.util.ConversionUtil;
import ch.eisenring.gui.util.GUIUtil;
import ch.eisenring.gui.util.TextFieldFocusAdapter;

@SuppressWarnings("serial")
public class HEAGDecimalField extends JTextField implements ValueEditor {

	private final static String ALLOWED_UNSIGNED = "0123456789";
	private final static String ALLOWED_SIGNED = "+-0123456789";
	
	private final FocusListener focusListener = new TextFieldFocusAdapter(ON_GAIN_SELECTALL) {
		public void focusLost(final FocusEvent event) {
			super.focusLost(event);
			setValue(getValue());
		}
	};

	private final DecimalFormat format;

	public HEAGDecimalField() {
		this(10, 2);
	}
	
	public HEAGDecimalField(final int intDigits, final int fracDigits) {
		this(null, intDigits, fracDigits);
	}

	public HEAGDecimalField(final ValueEditorBinding binding, final int intDigits, final int fracDigits) {
		this.binding = binding;
		final int length = getMaxLength(intDigits, fracDigits);
		final StringMaker allowed = StringMaker.obtain();
		allowed.append(intDigits < 0 ? ALLOWED_SIGNED : ALLOWED_UNSIGNED);
		if (fracDigits != 0) {
			allowed.append('.');
		}
		setHorizontalAlignment(SwingConstants.TRAILING);
		final CharacterFilter filter = CharacterFilter.createAllowed(allowed.release());
		LimitingDocument document = new LimitingDocument(length, filter);
		setDocument(document);
		final String pattern = getFormatPattern(intDigits, fracDigits);
		format = new DecimalFormat(pattern);
		GUIUtil.setMinSizeForText(this, Strings.concat(pattern, "0"));
		addFocusListener(focusListener);
	}

	/**
	 * Gets the format string for the specified digits
	 */
	public static String getFormatPattern(final int intDigits, final int fracDigits) {
		final int i = Math.abs(intDigits);
		final int f = Math.abs(fracDigits);
		final StringMaker b = StringMaker.obtain(32);
		if (intDigits < 0) {
			b.append('-');
		}
		for (int j=1; j<i; ++j)
			b.append('#');
		b.append('0');
		if (fracDigits > 0) {
			b.append('.');
			for (int j=0; j<f; ++j)
				b.append('0');
		}
		return b.release();
	}

	public static int getMaxLength(final int intDigits, final int fracDigits) {
		return (intDigits < 0 ? 1 : 0) + 
			   Math.abs(intDigits) +
			   (fracDigits != 0 ? Math.abs(fracDigits) + 1 : 0);
	}

	// --------------------------------------------------------------
	// ---
	// --- ValueEditor implementation
	// ---
	// --------------------------------------------------------------
	private ValueEditorBinding binding;
	private boolean changed;

	/**
	 * Gets the value of the field.
	 * If the field is empty, returns defaultValue.
	 */
	public double getValue(final double defaultValue) {
		final Number value = (Number) getValue();
		return null==value ? defaultValue : value.doubleValue();
	}

	/**
	 * Sets the value of the field
	 */
	public void setValue(final Number value) {
		setText(value == null ? "" : format.format(value));
	}
	
	/**
	 * Sets the value of the field
	 */
	public void setValue(final double value) {
		setText(format.format(value));
	}

	@Override
	public Class<?> getValueClass() {
		return Double.class;
	}

	@Override
	public ValueEditorBinding getBinding() {
		return binding;
	}

	@Override
	public void updateView(final Object model) {
		ValueEditorBinding.Util.updateView(this, model);
	}
	
	@Override
	public void updateModel(final Object model) {
		ValueEditorBinding.Util.updateModel(this, model);
	}

	@Override
	public void evaluateChanged(Object model) {
		changed = ValueEditorBinding.Util.evaluateChanged(this, model);
	}

	@Override
	public boolean isViewChanged() {
		return changed;
	}

	@Override
	public boolean isViewOnly() {
		return !isEditable() || !isEnabled();
	}

	// --------------------------------------------------------------
	// ---
	// --- HEAGComponent implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public JComponent getBalloonTipComponent() {
		return this;
	}
	
	@Override
	public Object getValue() {
		final String text = getText();
		if (Strings.isEmpty(text))
			return null;
		try {
			double d = Double.parseDouble(text);
			// must format and parse again to avoid precision issues
			// with excess fractional digits.
			final String formatted = format.format(d);
			d = Double.parseDouble(formatted);
			return Primitives.valueOf(d);
		} catch (Exception e) {
			return null;
		}
	}

	@Override
	public void setValue(final Object value) {
		final Number number = ConversionUtil.convert(value, Number.class);
		setText(number == null ? "" : format.format(number));
	}

}

//package ch.eisenring.gui.components;
//
//import java.awt.event.FocusAdapter;
//import java.awt.event.FocusEvent;
//import java.awt.event.FocusListener;
//
//import ch.eisenring.core.datatypes.primitives.Primitives;
//import ch.eisenring.core.util.GPSUtil;
//
//@SuppressWarnings("serial")
//public final class GPSLaengeField extends AbstractGPSField {
//
//	private final FocusListener focusListener = new FocusAdapter() {
//		@Override
//		public void focusLost(FocusEvent e) {
//			setValue(getValue());
//		}
//	};
//	
//	public GPSLaengeField() {
//		addFocusListener(focusListener);
//	}
//	
//	@Override
//	protected String formatValue(final Double value) {
//		if (null==value) {
//			return "" ;
//		} else {
//			return GPSUtil.toStringLGMS(value);
//		}
//	}
//	
//	@Override
//	protected Double parseValue(final CharSequence string) {
//		if (null==string || string.length()<=0) {
//			return null;
//		} else {
//			double grad = GPSUtil.parseLaenge(string);
//			if (Double.isNaN(grad)) {
//				return null;
//			} else {
//				return Primitives.valueOf(grad);
//			}
//		}
//	}
//
//}

package ch.eisenring.gui.components.code;

import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.codetype.CodeSetCoder;
import ch.eisenring.core.iterators.Filter;
import ch.eisenring.core.tag.Tag;

public interface HEAGCodeSelectionTags {

	/**
	 * Indicates there should be control buttons to select everything or nothing
	 * True if not specified.
	 */
	public final static Tag<Boolean> BUTTONS = new Tag<>("Buttons", Boolean.class, Boolean.TRUE);

	/**
	 * Indicates that all codes shall be initially selected.
	 * False if not specified.
	 */
	public final static Tag<Boolean> SELECT_ALL = new Tag<>("SelectAll", Boolean.class, Boolean.FALSE);
	
	/**
	 * Indicates that retired codes shall be shown.
	 * False if not specified.
	 */
	public final static Tag<Boolean> SHOW_RETIRED = new Tag<>("ShowRetired", Boolean.class, Boolean.FALSE);

	/**
	 * Indicates that NULL code instances shall be shown.
	 * False if not specified.
	 */
	public final static Tag<Boolean> SHOW_NULLS = new Tag<>("ShowNulls", Boolean.class, Boolean.FALSE);

	/**
	 * Specifies the number of columns used for display.
	 * Defaults to 2.
	 */
	public final static Tag<Integer> COLUMN_COUNT = new Tag<>("ColumnCount", Integer.class, 2);

	/**
	 * Specifies a filter used to select which items are displayed.
	 */
	public final static Tag<Filter<?>> CODE_FILTER = new Tag<>("CodeFilter", Filter.class);

	/**
	 * Specifies a CodeItemRenderer used to display the code items.
	 */
	public final static Tag<CodeItemRenderer> CODE_RENDERER = new Tag<>("CodeItemRenderer", CodeItemRenderer.class);

	/**
	 * Specifies a CodeSetCoder used to convert selection from/to string.
	 * Providing this tag overrides CodeTypeClass.
	 */
	@SuppressWarnings("unchecked")
	public final static Tag<CodeSetCoder<AbstractCode>> CODE_CODER = (Tag<CodeSetCoder<AbstractCode>>) new Tag("CodeSetCoder", CodeSetCoder.class);
	
	/**
	 * Specifies the class of the codes processed.
	 * Only used if CodeSetCoder tag is not provided.
	 */
	@SuppressWarnings("unchecked")
	public final static Tag<Class<AbstractCode>> CODE_TYPECLASS = new Tag("CodeTypeClass", Class.class);

}

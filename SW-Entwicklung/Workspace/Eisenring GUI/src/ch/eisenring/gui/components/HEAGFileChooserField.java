package ch.eisenring.gui.components;

import java.awt.BorderLayout;
import java.awt.Dimension;
import java.awt.dnd.DropTargetDragEvent;
import java.awt.dnd.DropTargetDropEvent;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.io.File;

import javax.swing.JComponent;
import javax.swing.JFileChooser;
import javax.swing.border.Border;

import ch.eisenring.core.datatypes.primitives.Primitives;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.io.file.FileItem;
import ch.eisenring.gui.FileDrop;
import ch.eisenring.gui.FileDropAdvancedListener;
import ch.eisenring.gui.components.filefilter.FileFilter;
import ch.eisenring.gui.interfaces.HEAGComponent;
import ch.eisenring.gui.resource.images.Images;
import ch.eisenring.gui.util.ConversionUtil;
import ch.eisenring.gui.util.GUIUtil;

@SuppressWarnings("serial")
public class HEAGFileChooserField extends CompoundComponent implements HEAGComponent {

	public final static int MODE_OPEN = 0x0000;
	public final static int MODE_SAVE = 0x8000;
	public final static int FILES_ONLY = JFileChooser.FILES_ONLY;
	public final static int DIRECTORIES_ONLY = JFileChooser.DIRECTORIES_ONLY;
	public final static int FILES_AND_DIRECTORIES = JFileChooser.FILES_AND_DIRECTORIES;

	public final static FileFilter[] EMPTY_FILE_FILTER_ARRAY = Primitives.getEmptyArray(FileFilter.class);
	
	private final static int FILE_SELECTION_MODE_MASK = FILES_ONLY | FILES_AND_DIRECTORIES | DIRECTORIES_ONLY;
	
	protected final HEAGFloatingButton btnDialog = new HEAGFloatingButton(Images.OPENFOLDER, 16);
	
	protected final HEAGTextField txtFile = new HEAGTextField(255);
	protected final FileDrop fileDrop;

	private JFileChooser fileChooser;
	private int options;
	private FileFilter[] fileFilters = EMPTY_FILE_FILTER_ARRAY;
	
	private final ActionListener openFolderListener = new ActionListener() {
		@Override
		public void actionPerformed(final ActionEvent event) {
			if (!isEnabled())
				return;
			final File file = requestFile();
			if (file != null) {
				final String path = hookProcessChoosenFile(file.getAbsolutePath());
				txtFile.setText(path);
			}
		}
	};

	public FileItem requestFileItem() {
		final File file = requestFile();
		return file == null ? null : FileItem.create(file);
	}

	@Deprecated
	public File requestFile() {
		final JFileChooser fileChooser = getFileChooser();
		int result;
		if ((options & MODE_SAVE) != 0) {
			result = fileChooser.showSaveDialog(HEAGFileChooserField.this);
		} else {
			result = fileChooser.showOpenDialog(HEAGFileChooserField.this);
		}
		if (result == JFileChooser.APPROVE_OPTION) {
			final File file = fileChooser.getSelectedFile();
			return file;
		}
		return null;
	}

	public HEAGFileChooserField() {
		this(null, MODE_OPEN | FILES_AND_DIRECTORIES);
	}

	public HEAGFileChooserField(final String selectedFile, final int options) {
		this.options = options;
		this.fileDrop = new FileDrop(txtFile, (Border) null,  new FileDropAdvancedListener() {
			@Override
			public boolean acceptsDrop(final File[] files, final DropTargetDragEvent event) {
				return acceptsDrop(files);
			}
			@Override
			public boolean acceptsDrop(final File[] files, final DropTargetDropEvent event) {
				return acceptsDrop(files);
			}
			private boolean acceptsDrop(final File[] files) {
				if (!isEnabled())
					return false;
				if (files == null || files.length != 1)
					return false;
				final File file = files[0];
				switch (getFileSelectionMode()) {
					case FILES_ONLY:
						return file.isFile();
					case DIRECTORIES_ONLY:
						return file.isDirectory();
					default:
						return true;
				}
			}
			@Override
			public void filesDropped(final File[] files, final DropTargetDropEvent event) {
				if (files != null && files.length > 0) {
					txtFile.setText(files[0].getAbsolutePath());
				}
			}
		});
		initComponents();
		initLayout();
		setSelectedPath(selectedFile);
	}
	
	private void initComponents() {
		GUIUtil.setMinSizeForText(txtFile, "00000000000000000000");
		GUIUtil.setSizeForText(txtFile, "0000000000000000000000000000000000000000");
		Dimension d = txtFile.getPreferredSize();
		d.width = d.height;
		if (d.width < 20) {
			d.width = 20;
		}
		btnDialog.setMinimumSize(d);
		btnDialog.setPreferredSize(d);
		btnDialog.setMaximumSize(d);
		btnDialog.addActionListener(openFolderListener);
	}
	
	private void initLayout() {
		removeAll();
		setLayout(new BorderLayout());
		add(txtFile, BorderLayout.CENTER);
		add(btnDialog, BorderLayout.EAST);
	}

	public JFileChooser getFileChooser() {
		if (fileChooser == null) {
			fileChooser = new JFileChooser();
			fileChooser.setMultiSelectionEnabled(false);
		}
		// --- select a reasonable default path
		final File selected = getSelectedFile();
		if (selected != null) {
			fileChooser.setSelectedFile(selected);
		} else {
			final String home = System.getProperty("user.home");
			if (home != null && home.length() > 0) {
				fileChooser.setCurrentDirectory(new File(home));
			}
		}
		configureFileChooser(fileChooser);
		return fileChooser;
	}

	protected void configureFileChooser(JFileChooser fileChooser) {
		fileChooser.setFileHidingEnabled(true);
		fileChooser.setFileSelectionMode(getFileSelectionMode());
		fileChooser.resetChoosableFileFilters();
		if (fileFilters == null || fileFilters.length <= 0) {
			fileChooser.setAcceptAllFileFilterUsed(true);
		} else {
			for (int i=0; i<fileFilters.length; ++i) {
				final FileFilter filter = fileFilters[i];
				if (filter != null) {
					fileChooser.addChoosableFileFilter(filter);
				}
			}
			fileChooser.setAcceptAllFileFilterUsed(false);
		}
	}

	public int getFileSelectionMode() {
		return options & FILE_SELECTION_MODE_MASK;
	}
	
	public void setFileSelectionMode(final int fileSelectionMode) {
		switch (fileSelectionMode) {
			case FILES_AND_DIRECTORIES:
			case FILES_ONLY:
			case DIRECTORIES_ONLY:
				options = (options & ~FILE_SELECTION_MODE_MASK) | fileSelectionMode;
		}
	}

	/**
	 * Gets the selected File, or NULL if no selection
	 */
	public String getSelectedPath() {
		return Strings.clean(txtFile.getText());
	}
	
	/**
	 * Gets the selected File, or NULL if no selection
	 */
	public File getSelectedFile() {
		final String path = getSelectedPath();
		return path == null ? null : new File(path);
	}

	public void setSelectedPath(final String selectedFile) {
		final String s = Strings.trim(selectedFile);
		txtFile.setText(s);
	}

	public void setSelectedFile(final File file) {
		setSelectedPath(file == null ? null : file.getAbsolutePath());
	}
	
	public void setChoosableFileTypes(final FileFilter ... fileFilters) {
		this.fileFilters = fileFilters == null ? EMPTY_FILE_FILTER_ARRAY : fileFilters;
	}

	protected String hookProcessChoosenFile(final String filePath) {
		return filePath;
	}

	public static File requestFileSave(final File defaultFile) {
		return requestFileSave(defaultFile == null ? "" : defaultFile.getAbsolutePath());
	}

	public static File requestFileSave(final String defaultName) {
		HEAGFileChooserField field = new HEAGFileChooserField(defaultName, MODE_SAVE | FILES_ONLY);
		return field.requestFile();
	}

	public static File requestFileLoad(final File defaultFile) {
		final String defaultName = defaultFile == null ? null : defaultFile.getPath();
		return requestFileLoad(defaultName);
	}

	public static File requestFileLoad(final String defaultName) {
		HEAGFileChooserField field = new HEAGFileChooserField(defaultName, MODE_OPEN | FILES_ONLY);
		return field.requestFile();
	}

	// --------------------------------------------------------------
	// ---
	// --- HEAGComponent implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public JComponent getBalloonTipComponent() {
		return txtFile;
	}

	@Override
	public Object getValue() {
		return getSelectedPath();
	}
	
	@Override
	public void setValue(final Object value) {
		if (value instanceof File) {
			setSelectedPath(((File) value).getAbsolutePath());
		} else {
			final String s = ConversionUtil.convert(value, (String) null);
			setSelectedPath(s);
		}
	}

}

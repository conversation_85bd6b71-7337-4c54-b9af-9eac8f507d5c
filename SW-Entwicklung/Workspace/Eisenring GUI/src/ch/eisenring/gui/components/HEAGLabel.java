package ch.eisenring.gui.components;

import javax.swing.JComponent;
import javax.swing.JLabel;

import ch.eisenring.core.datatypes.primitives.ConversionUtil;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.gui.ValueEditorBinding;
import ch.eisenring.gui.interfaces.ValueEditor;

/**
 * Special label class for field labels.
 * This class appends a ':' to the text if the text
 * doesn't end with ':' to maintain a consistent look.
 */
@SuppressWarnings("serial")
public final class HEAGLabel extends JLabel implements ValueEditor {

	private boolean fixDoppelPunkt = true;
	
	public HEAGLabel() {
		super();
	}
	
	public HEAGLabel(final CharSequence text) {
		super(fixText(text, true));
	}

	public HEAGLabel(final CharSequence text, final boolean fixDoppelPunkt) {
		super(fixText(text, fixDoppelPunkt));
		this.fixDoppelPunkt = fixDoppelPunkt;
	}

	@Override
	public void setText(final String text) {
		super.setText(fixText(text, fixDoppelPunkt));
	}

	public void setText(final CharSequence text, final boolean fixDoppelPunkt) {
		super.setText(fixText(text, fixDoppelPunkt));
	}
	
	private static String fixText(final CharSequence text, final boolean fixDoppelPunkt) {
		if (text == null || text.length() <= 0)
			return "";
		if (!fixDoppelPunkt || text.charAt(text.length() - 1) == ':')
			return Strings.toString(text);
		return Strings.concat(text, ":");
	}

	// --------------------------------------------------------------
	// ---
	// --- ValueEditor implementation
	// ---
	// --------------------------------------------------------------
	private ValueEditorBinding binding;
	private boolean changed;

	@Override
	public Class<?> getValueClass() {
		return String.class;
	}
	
	@Override
	public void updateView(final Object model) {
		ValueEditorBinding.Util.updateView(this, model);
	}
	
	@Override
	public void updateModel(final Object model) {
		ValueEditorBinding.Util.updateModel(this, model);
	}

	@Override
	public void evaluateChanged(final Object model) {
		changed = ValueEditorBinding.Util.evaluateChanged(this, model);
		
	}

	@Override
	public boolean isViewChanged() {
		return changed;
	}

	@Override
	public boolean isViewOnly() {
		// is view only by its very nature
		return true;
	}
	
	@Override
	public void setEditable(final boolean editable) {
		// not implemented for this class
	}

	@Override
	public ValueEditorBinding getBinding() {
		return binding;
	}

	// --------------------------------------------------------------
	// ---
	// --- HEAGComponent implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public JComponent getBalloonTipComponent() {
		return this;
	}

	@Override
	public Object getValue() {
		return Strings.clean(getText());
	}

	@Override
	public void setValue(final Object value) {
		final String text = (String) ConversionUtil.convert(value, String.class);
		setText(text);
	}

}

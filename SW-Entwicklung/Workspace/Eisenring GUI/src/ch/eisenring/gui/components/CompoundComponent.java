package ch.eisenring.gui.components;

import javax.swing.JComponent;


/**
 * Base class for compound components
 * 
 * Implements many common calls (setEnabled etc.)
 * to delegate to all subcomponents to mimic the
 * behavior of a non-composite component.
 */
@SuppressWarnings("serial")
public abstract class CompoundComponent extends CompoundLinePanel {

	protected CompoundComponent() {
	}

	@Override
	public void setEnabled(final boolean enabled) {
		super.setEnabled(enabled);
		for (int i=getComponentCount()-1; i>=0; --i) {
			getComponent(i).setEnabled(enabled);
		}
	}
	
	@Override
	public void setFocusable(final boolean focusable) {
		super.setFocusable(focusable);
		for (int i=getComponentCount()-1; i>=0; --i) {
			getComponent(i).setFocusable(focusable);
		}
	}
	
	@Override
	public void setToolTipText(final String text) {
		super.setToolTipText(text);
		if (getComponentCount() > 0) {
			((JComponent) getComponent(0)).setToolTipText(text);
		}
	}

}

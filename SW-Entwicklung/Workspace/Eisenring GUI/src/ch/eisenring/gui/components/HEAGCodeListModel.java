package ch.eisenring.gui.components;

import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.collections.Collection;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;

/**
 * Implementation of ListModel that is based on a code type 
 * to supply the list contents.
 */
@SuppressWarnings("serial")
public class HEAGCodeListModel<T extends AbstractCode> extends HEAGListModel<T> {

	protected final Class<T> codeType;
	protected List<T> itemList = new ArrayList<T>();

	public HEAGCodeListModel(final Class<T> codeType) {
		this.codeType = codeType;
		this.itemList = getInstanceList();
	}

	protected List<T> getInstanceList() {
		final List<T> codeInstances = AbstractCode.getInstances(codeType);
		return filter(codeInstances);
	}
	
	/**
	 * Filters list of codes using the accepts() hook.
	 */
	protected List<T> filter(final Collection<T> codeInstances) {
		if (codeInstances == null || codeInstances.isEmpty())
			return new ArrayList<T>();
		final List<T> result = new ArrayList<T>(codeInstances.size());
		for (final T codeInstance : codeInstances) {
			if (accepts(codeInstance))
				result.add(codeInstance);
		}
		result.trimToSize();
		return result;
	}

	/**
	 * Internal hook that decides which code instances are shown.
	 * The default implementation is to show all but the NULL-instance.
	 */
	protected boolean accepts(final T codeInstance) {
		return !AbstractCode.isNull(codeInstance);
	}

	@Override
	public int getSize() {
		return itemList.size();
	}

	@Override
	public T getElementAt(final int index) {
		if (index < 0 || index >= itemList.size())
			return null;
		return itemList.get(index);
	}

}

package ch.eisenring.gui.components;

import java.awt.BorderLayout;
import java.awt.Color;
import java.awt.Component;
import java.awt.Dialog.ModalityType;
import java.awt.Dimension;
import java.awt.GridBagLayout;
import java.awt.Window;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.KeyAdapter;
import java.awt.event.KeyEvent;
import java.awt.event.KeyListener;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.awt.event.MouseListener;
import java.util.Calendar;

import javax.swing.JDialog;
import javax.swing.JLabel;
import javax.swing.JPanel;
import javax.swing.border.Border;
import javax.swing.border.LineBorder;

import ch.eisenring.core.datatypes.date.DateGranularityCode;
import ch.eisenring.core.datatypes.date.HEAGCalendar;
import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.core.datatypes.date.format.HEAGDateFormat;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.gui.resource.images.Images;
import ch.eisenring.gui.util.GUIUtil;
import ch.eisenring.gui.util.LayoutUtil;

@SuppressWarnings("serial")
public class MiniCalendar extends JPanel {

	final static Color BG_INSIDE = new Color(0xFFDDFFDD);
	final static Color BG_OUTSIDE = new Color(0xFFDDDDDD);
	final static Color BG_BORDER = new Color(0xFFAAAAAA);
	final static Color BG_PANEL = Color.WHITE;
	final static Color FG_LABEL = Color.BLACK;
	final static Color FG_INWEEK = Color.BLACK;
	final static Color FG_WEEKEND = new Color(0xFF990000);
	
	final static Color BG_HOVER = new Color(0xFFDDDDFF);
	
	final static Border OUTER = new LineBorder(BG_BORDER, 1);
	final static Border SELECTED = new LineBorder(Color.BLACK, 2);
	final static Border HOVER = new LineBorder(new Color(0xFF444488), 2);
	
	final static Dimension CELL_SIZE = new Dimension(21, 21);

	final MouseListener dayMouseListener = new MouseAdapter() {
		@Override
		public void mouseExited(final MouseEvent event) {
			final Component component = event.getComponent();
			if (component instanceof DayLabel) {
				final DayLabel label = (DayLabel) component;
				label.setBackground(label.bg);
				label.setBorder(label.border);
			}
		}
		
		@Override
		public void mouseEntered(final MouseEvent event) {
			final Component component = event.getComponent();
			if (component instanceof DayLabel) {
				final DayLabel label = (DayLabel) component;
				label.setBackground(BG_HOVER);
				label.setBorder(HOVER);
			}
		}
		
		@Override
		public void mouseReleased(final MouseEvent event) {
			final Component component = event.getComponent();
			if (component instanceof DayLabel) {
				final DayLabel label = (DayLabel) component;
				if (!event.isConsumed() && event.getButton() == MouseEvent.BUTTON1) {
					event.consume();
					dateSelected(label.timestamp);
				}
			}
		}
	};
	
	final KeyListener escapeListener = new KeyAdapter() {
		@Override
		public void keyReleased(final KeyEvent event) {
			if (event.getKeyCode() == KeyEvent.VK_ESCAPE) {
				event.consume();
				cancelDetected();
			}
		}
	};

	/**
	 * Label class for days
	 */
	class DayLabel extends JLabel {
		
		final long timestamp;
		final Color bg;
		final Color fg;
		final Border border;
		
		public DayLabel(final CharSequence label, final long timestamp, 
				        final long first, final long last,
				        final long selected) {
			super(Strings.concat("<html><center>", label, "</center>"));
			this.timestamp = timestamp;
			fg = getDayForeground(timestamp);
			bg = getDayBackground(timestamp);
			setOpaque(true);
			border = TimestampUtil.isSameDay(timestamp, selected) ? SELECTED : OUTER; 
			setBorder(border);
			setForeground(fg);
			setBackground(bg);
			setMinimumSize(CELL_SIZE);
			setPreferredSize(CELL_SIZE);
			setHorizontalAlignment(CENTER);
			addMouseListener(dayMouseListener);
		}
	}
	
	final static HEAGDateFormat FORMAT_TITLE = HEAGDateFormat.get("MMM yyyy");
	final static HEAGDateFormat FORMAT_ROW = HEAGDateFormat.get("ww");
	final static HEAGDateFormat FORMAT_CELL = HEAGDateFormat.get("d");
	
	private final JLabel lblCW = new JLabel("KW");
	private final JLabel lblMo = new JLabel("Mo");
	private final JLabel lblTu = new JLabel("Di");
	private final JLabel lblWe = new JLabel("Mi");
	private final JLabel lblTh = new JLabel("Do");
	private final JLabel lblFr = new JLabel("Fr");
	private final JLabel lblSa = new JLabel("Sa");
	private final JLabel lblSu = new JLabel("So");
	
	private final HEAGFloatingButton btnPrev = new HEAGFloatingButton(Images.BACK, 16);
	private final HEAGFloatingButton btnNext = new HEAGFloatingButton(Images.FORWARD, 16);
	private final HEAGFloatingButton btnExit = new HEAGFloatingButton(Images.CANCEL, 16);

	private final JLabel lblTitle = new JLabel("Oktober 2010");

	protected long showingTimestamp;
	protected long selectedTimestamp;
	protected long firstDayOfMonth;
	protected long lastDayOfMonth;
	
	private final ActionListener nextListener = new ActionListener() {
		@Override
		public void actionPerformed(final ActionEvent event) {
			final HEAGCalendar calendar = HEAGCalendar.obtain(showingTimestamp);
			calendar.add(Calendar.MONTH, 1);
			showingTimestamp = calendar.release().getTimeInMillis();
			buildCalendar(showingTimestamp, selectedTimestamp);
		}
	};
	
	private final ActionListener prevListener = new ActionListener() {
		@Override
		public void actionPerformed(final ActionEvent event) {
			final HEAGCalendar calendar = HEAGCalendar.obtain(showingTimestamp);
			calendar.add(Calendar.MONTH, -1);
			showingTimestamp = calendar.release().getTimeInMillis();
			buildCalendar(showingTimestamp, selectedTimestamp);
		}
	};

	private final ActionListener exitListener = new ActionListener() {
		@Override
		public void actionPerformed(final ActionEvent event) {
			cancelDetected();
		}
	};
	
	public MiniCalendar(final long selectedTimestamp) {
		this.selectedTimestamp = selectedTimestamp;
		this.showingTimestamp = firstDayOfMonth(TimestampUtil.isNull(selectedTimestamp)
				? System.currentTimeMillis() : selectedTimestamp);
		initComponents();
		buildCalendar(showingTimestamp, selectedTimestamp);
	}
	
	public MiniCalendar() {
		this(TimestampUtil.NULL_TIMESTAMP);
	}

	private void initComponents() {
		setBackground(BG_PANEL);
		setBorder(new LineBorder(Color.BLACK));
		btnNext.addActionListener(nextListener);
		btnPrev.addActionListener(prevListener);
		btnExit.addActionListener(exitListener);
		btnNext.setFocusable(false);
		btnPrev.setFocusable(false);
		btnPrev.setPreferredSize(CELL_SIZE);
		btnNext.setPreferredSize(CELL_SIZE);
		btnExit.setPreferredSize(CELL_SIZE);
		btnPrev.setToolTipText("Zum vorangegangenen Monat blättern");
		btnNext.setToolTipText("Zum nachfolgenden Monat blättern");
		btnExit.setToolTipText("Kalender schliessen");
		addListeners(btnNext, btnPrev, btnExit, lblTitle, 
				     lblCW, lblMo, lblTu, lblWe,
				     lblTh, lblFr, lblSa, lblSu);
	}

	protected void addListeners(final Component ... components) {
		for (Component component : components) {
			addListeners(component);
		}
	}

	protected void addListeners(final Component component) {
		GUIUtil.addKeyListener(component, escapeListener);
//		GUIUtil.addMouseMotionListener(component, motionListener);
	}

	protected void buildCalendar(final long timestamp, final long selectedTimestamp) {
		firstDayOfMonth = firstDayOfMonth(timestamp);
		lastDayOfMonth = lastDayOfMonth(timestamp);
		
		lblTitle.setText(FORMAT_TITLE.format(firstDayOfMonth)); 

		final LayoutUtil l = new LayoutUtil(8);
		removeAll();
		setLayout(new GridBagLayout());

		// title row (navigation + month)
		final boolean closeable = isCloseable();
		add(btnPrev, l.fixed(1));
		add(lblTitle, l.button(closeable ? 5 : 6));
		add(btnNext, l.fixed(1));
		if (closeable)
			add(btnExit, l.fixed(1));
		
		// header row (column labels)
		add(lblCW, l.button(1));
		add(lblMo, l.button(1));
		add(lblTu, l.button(1));
		add(lblWe, l.button(1));
		add(lblTh, l.button(1));
		add(lblFr, l.button(1));
		add(lblSa, l.button(1));
		add(lblSu, l.button(1));
		
		final HEAGCalendar calendar = HEAGCalendar.obtain(firstDayOfMonth);
		DateGranularityCode.KW.round(calendar, 0);
		int weekRows = 0;
		do {
			// row label
			final JLabel lblRow = new JLabel(FORMAT_ROW.format(calendar.getTimeInMillis()));
			add(lblRow, l.button(1));
			// iterate over the days of the week
			for (int i=0; i<7; ++i) {
				final DayLabel lblDay = new DayLabel(FORMAT_CELL.format(calendar.getTimeInMillis()),
						calendar.getTimeInMillis(), 
						firstDayOfMonth, lastDayOfMonth, selectedTimestamp);
				add(lblDay, l.button(1));
				calendar.add(Calendar.DAY_OF_YEAR, 1);
			}
			++weekRows;
		} while (/*!day.after(lastDayOfMonth) ||*/ weekRows < 6);
		calendar.release();
	}
	
	/**
	 * Called when the user clicked a date
	 */
	protected void dateSelected(final long timestamp) {
	}

	/**
	 * Called when an action that cancels the calendar is detected
	 */
	protected void cancelDetected() {
	}
	
//	private final static WindowListener WINDOW_LISTENER = new WindowAdapter() {
//		public void windowDeactivated(final WindowEvent event) {
//			final Window window = event.getWindow();
//			if (window != null)
//				window.setVisible(false);
//		}
//	};

	public static long doPopup(final Component invoker, final long selectedDate) {
		final JDialog dialog;
		final Window parent = GUIUtil.getWindowAncestor(invoker);
		if (parent == null) {
			dialog = new JDialog();
			dialog.setModalityType(ModalityType.APPLICATION_MODAL);
		} else {
			dialog = new JDialog(parent);
		}
		dialog.setModalityType(ModalityType.APPLICATION_MODAL);
		dialog.setUndecorated(true);
		dialog.setLayout(new BorderLayout());
		//dialog.addWindowListener(WINDOW_LISTENER);
		final MiniCalendar calendar = new MiniCalendar(selectedDate) {
			@Override
			protected void dateSelected(final long timestamp) {
				dialog.setVisible(false);
				this.selectedTimestamp = timestamp;
			}
			@Override
			protected void cancelDetected() {
				this.selectedTimestamp = TimestampUtil.NULL_TIMESTAMP;
				dialog.setVisible(false);
			}
		};
		dialog.add(calendar, BorderLayout.CENTER);
		dialog.pack();
		calendar.addListeners(dialog);
		if (invoker != null) {
			dialog.setLocation(invoker.getLocationOnScreen());
			GUIUtil.correctWindowLocationForScreen(dialog);
		}
		dialog.setVisible(true);
		return calendar.selectedTimestamp;
	}
	
	/**
	 * Gets the background color for a date cell
	 */
	protected Color getDayBackground(final long timestamp) {
		if (TimestampUtil.isNull(timestamp))
			return BG_PANEL;
		return (timestamp < firstDayOfMonth || timestamp >= lastDayOfMonth) ? BG_OUTSIDE : BG_INSIDE;
	}

	/**
	 * Gets the foreground color for a date cell
	 */
	protected static Color getDayForeground(final long timestamp) {
		if (TimestampUtil.isNull(timestamp)) 
			return FG_LABEL;
		final HEAGCalendar calendar = HEAGCalendar.obtain(timestamp);
		switch (calendar.release().get(Calendar.DAY_OF_WEEK)) {
			default:
				return FG_INWEEK;
			case Calendar.SATURDAY:
			case Calendar.SUNDAY:
				return FG_WEEKEND;
		}		
	}
	
	/**
	 * Determines if there shall be a close button
	 */
	protected boolean isCloseable() {
		return true;
	}

	// --------------------------------------------------------------
	// ---
	// --- Utility methods
	// ---
	// --------------------------------------------------------------
	/**
	 * Gets the first day in month
	 */
	public static long firstDayOfMonth(final long timestamp) {
		if (TimestampUtil.isNull(timestamp))
			return TimestampUtil.NULL_TIMESTAMP;
		final HEAGCalendar calendar = HEAGCalendar.obtain(timestamp);
		calendar.set(Calendar.DAY_OF_MONTH, 1);
		calendar.setTime(0, 0, 0, 0);
		return calendar.release().getTimeInMillis();
	}
	
	/**
	 * Gets the last day in month
	 */
	public static long lastDayOfMonth(final long timestamp) {
		if (TimestampUtil.isNull(timestamp))
			return timestamp;
		final HEAGCalendar calendar = HEAGCalendar.obtain(timestamp);
		calendar.set(Calendar.DAY_OF_MONTH, 1);
		calendar.add(Calendar.MONTH, 1);
		calendar.add(Calendar.DAY_OF_MONTH, -1);
		calendar.setTime(23, 59, 59, 0);
		return calendar.release().getTimeInMillis();
	}	
	
}

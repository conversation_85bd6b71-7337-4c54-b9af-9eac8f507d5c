package ch.eisenring.gui.components;

import java.awt.FontMetrics;
import java.awt.Graphics;
import java.awt.Insets;
import java.awt.event.KeyAdapter;
import java.awt.event.KeyEvent;
import java.awt.event.KeyListener;

import javax.swing.border.Border;

import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.collections.Collection;
import ch.eisenring.core.datatypes.money.CurrencyCode;
import ch.eisenring.core.datatypes.money.Money;

@SuppressWarnings("serial")
public class HEAGMoneyField extends HEAGDecimalField {

	private final KeyListener keyListener = new KeyAdapter() {
		public void keyReleased(final KeyEvent event) {
			keyPressed(event);
		}
		@Override
		public void keyPressed(final KeyEvent event) {
			if (event.isConsumed())
				return;
			final int keyCode = event.getKeyCode();
			final CurrencyCode currency;
			switch (keyCode) {
				default:
					currency = null;
					break;
				case KeyEvent.VK_B:
				case KeyEvent.VK_P:
					currency = CurrencyCode.BPD;
					break;
				case KeyEvent.VK_S: 
				case KeyEvent.VK_C:
					currency = CurrencyCode.CHF;
					break;
				case KeyEvent.VK_U:
					currency = CurrencyCode.USD;
					break;
				case KeyEvent.VK_E:
					currency = CurrencyCode.EUR;
					break;
				case KeyEvent.VK_Y:
					currency = CurrencyCode.YEN;
					break;
			}
			if (currency != null) {
				event.consume();
				setCurrency(currency);
			}
		}
	};

	public HEAGMoneyField(final int intDigits, final int fracDigits) {
		super(intDigits, fracDigits);
		addKeyListener(keyListener);
	}

	public HEAGMoneyField() {
		this(6, 2);
	}


	@Override
	protected void paintComponent(final Graphics g) {
		super.paintComponent(g);
		// figure out where to paint the currency symbol
		final FontMetrics fontMetrics = g.getFontMetrics();
		final Border border = getBorder();
		final Insets insets = border == null ? null : border.getBorderInsets(this);
		int h = getHeight();
		int y = 2;
		int x = 2;
		if (insets != null) {
			y += insets.top;
			h -= y + insets.bottom;
			x += insets.left;
		}
		y += (h >> 1) - fontMetrics.getAscent() + fontMetrics.getHeight();
		final String s = AbstractCode.getShortText(currency, "");
		g.setPaintMode();
		g.setColor(getForeground());
		g.drawString(s, x, y);
	}

	// --------------------------------------------------------------
	// ---
	// --- Special Getters and Setters
	// ---
	// --------------------------------------------------------------
	@Override
	public Object getValue() {
		return getMoneyValue();
	}

	@Override
	public void setValue(final Object value) {
		if (value instanceof Money) {
			final Money money = (Money) value;
			setCurrency(money.getCurrency());
			if (money.isNull()) {
				super.setValue(null);
			} else {
				super.setValue(money.getAmountMajor());
			}
		} else {
			super.setValue(value);
		}
	}

	public Money getMoneyValue() {
		// the super class gives us a number or NULL.
		final Number value = (Number) super.getValue();
		return Money.valueOfMajor(value, currency);
	}

	public void setMoneyValue(final Money moneyValue) {
		setValue(moneyValue);
	}

	// --------------------------------------------------------------
	// ---
	// --- Currency management
	// ---
	// --------------------------------------------------------------
	private final static CurrencyCode[] DEFAULT_ALLOWED = {
		CurrencyCode.CHF,
		CurrencyCode.EUR,
	};

	/**
	 * The currencies allowed for this field
	 */
	private CurrencyCode[] allowedCurrencies = DEFAULT_ALLOWED;
	
	/**
	 * The currency assigned to this field
	 */
	private CurrencyCode currency = CurrencyCode.CHF;

	/**
	 * Sets the currency assigned to this field.
	 * Returns true if the currency was applied (even if its the same as already active).
	 * Note that the currency will not change if the currency to be set is
	 * not allowed for this field.
	 */
	public boolean setCurrency(final CurrencyCode currency) {
		if (AbstractCode.equals(this.currency, currency))
			return true;
		if (!isAllowedCurrency(currency))
			return false;
		this.currency = currency;
		repaint();
		return true;
	}

	/**
	 * Returns true if the given currency is allowed for this field
	 */
	public boolean isAllowedCurrency(final CurrencyCode currency) {
		for (final CurrencyCode allowed : allowedCurrencies) {
			if (AbstractCode.equals(allowed, currency))
				return true;
		}
		return false;
	}
	
	public void setAllowedCurrencies(final Collection<CurrencyCode> currencies) {
		allowedCurrencies = Collection.toArray(currencies, CurrencyCode.class);
		if (!isAllowedCurrency(currency))
			setCurrency(allowedCurrencies[0]);
	}

	public void setAllowedCurrencies(final CurrencyCode ... currencies) {
		allowedCurrencies = currencies;
		if (!isAllowedCurrency(currency))
			setCurrency(allowedCurrencies[0]);
	}
	
	
//	public static void main(String[] argv) {
//		// force platform native look & feel
//		try {
//			final String className = UIManager.getSystemLookAndFeelClassName();
//			UIManager.setLookAndFeel(className);
//		} catch (final Exception e) {
//			// ignore
//		}
//		GUIUtil.invokeLater(new Runnable() {
//			@Override
//			public void run() {
//				testCode();
//			}
//		});
//	}
//
//	private static void testCode() {
//		JFrame f = new JFrame("Test");
//		HEAGMoneyField x = new HEAGMoneyField();
//		f.setLayout(new BorderLayout());
//		f.add(x, BorderLayout.CENTER);
//		f.pack();
//		f.setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);
//		f.setVisible(true);
//	}

}

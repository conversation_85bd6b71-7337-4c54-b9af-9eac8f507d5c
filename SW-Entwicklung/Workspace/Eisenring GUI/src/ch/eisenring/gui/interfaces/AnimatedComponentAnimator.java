package ch.eisenring.gui.interfaces;

import java.awt.Component;
import java.awt.event.HierarchyEvent;
import java.awt.event.HierarchyListener;

import ch.eisenring.core.threading.ThreadCore;
import ch.eisenring.core.threading.ThreadPool;

/**
 * Manages animating a component (calls repaint() in the intervals
 * the component desires, automatically starting/stopping the animation
 * threading as needed).
 */
final class AnimatedComponentAnimator implements HierarchyListener {

	private AnimatedComponent component;
	private Thread animationThread;
	
	@Override
	public void hierarchyChanged(final HierarchyEvent event) {
		hierarchyChanged();
	}

	AnimatedComponentAnimator(final AnimatedComponent component) {
		this.component = component;
		((Component) component).addHierarchyListener(this);
		hierarchyChanged();
	}

	/**
	 * Called when the components hierarchy has potentially changed
	 */
	void hierarchyChanged() {
		// manages animation, only animates when really showing
		if (!component.isActive()) {
			// stop animation
			synchronized (this) {
				animationThread = null;
			}
		} else {
			// start animation
			synchronized (this) {
				if (animationThread == null) {
					animationThread = ThreadPool.DEFAULT.startDaemon(this::animation<PERSON>oop, "ComponentAnimator");
				}
			}
		}
		
	}

	void animationLoop() {
		while (true) {
			synchronized (this) {
				if (animationThread != Thread.currentThread())
					break;
			}
			((Component) component).repaint();
			ThreadCore.sleep(component.getRepaintSleep());
		}
		synchronized (this) {
			if (animationThread == Thread.currentThread())
				animationThread = null;
		}
	}

}

package ch.eisenring.gui;

import java.awt.Component;
import java.awt.Insets;

import ch.eisenring.core.datatypes.strings.Strings;

@SuppressWarnings("serial")
public class GridBagConstraints extends java.awt.GridBagConstraints implements GUIConstants {

	public GridBagConstraints() {
		super();
	}

	public GridBagConstraints(
			final int gridX, final int gridY,
			final int gridWidth, final int gridHeight,
			final double weightX, final double weightY,
			final int anchor, final int fill,
			final Insets insets,
			final int iPadX, final int iPadY) {
		super(gridX, gridY, gridWidth, gridHeight, weightX, weightY, anchor, fill, insets, iPadX, iPadY);
	}

	// --------------------------------------------------------------
	// ---
	// --- Interfaces
	// ---
	// --------------------------------------------------------------
	public static interface Builder {
		/** Constructs constraints */
		public abstract GridBagConstraints build(final Component component, final int x, final int y);
	}

	public static interface Modifier {
		/** Modifies constraints */
		public abstract GridBagConstraints modify(final Component component, final GridBagConstraints constraints);
	}

	// --------------------------------------------------------------
	// ---
	// --- Predefined builders
	// ---
	// --------------------------------------------------------------
	public final static Builder FIELD = new Builder() {
		@Override
		public GridBagConstraints build(final Component component, final int x, final int y) {
			return new GridBagConstraints(
					x, y, 1, 1, 1D, 0D,
					WEST, HORIZONTAL,
					new Insets(y <= 0 ? VSPACE : 0, x <= 0 ? HSPACE : 0, VSPACE, HSPACE),
					0, 0);
		}
	};

	public final static Builder FIXED = new Builder() {
		@Override
		public GridBagConstraints build(final Component component, final int x, final int y) {
			return new GridBagConstraints(
					x, y, 1, 1, 0D, 0D,
					WEST, NONE,
					new Insets(y <= 0 ? VSPACE : 0, x <= 0 ? HSPACE : 0, VSPACE, HSPACE),
					0, 0);
		}
	};

	public final static Builder BUTTON = new Builder() {
		@Override
		public GridBagConstraints build(final Component component, final int x, final int y) {
			return new GridBagConstraints(
					x, y, 1, 1, 0D, 0D,
					CENTER, NONE,
					new Insets(y <= 0 ? VSPACE : 0, x <= 0 ? HSPACE : 0, VSPACE, HSPACE),
					0, 0);
		}
	};

	public final static Modifier INSETS0 = new Modifier() {
		@Override
		public GridBagConstraints modify(final Component component, final GridBagConstraints constraints) {
			final Insets i = constraints.insets;
			i.top = 0;
			i.left = 0;
			i.bottom = 0;
			i.right = 0;
			return constraints;
		}
	};

	// --------------------------------------------------------------
	// ---
	// --- Builder / Modifier application
	// ---
	// --------------------------------------------------------------
	/**
	 * Builds and modifies constraints
	 */
	public static GridBagConstraints get(final Component component, final int x, final int y, final Builder builder, final Modifier ... modifiers) {
		GridBagConstraints constraints = builder.build(component, x, y);
		if (modifiers != null) {
			for (final Modifier modifier : modifiers) {
				if (modifier == null)
					continue;
				constraints = modifier.modify(component, constraints);
			}
		}
		return constraints;
	}
	
	// --------------------------------------------------------------
	// ---
	// --- Chain call property setters
	// ---
	// --------------------------------------------------------------
	public GridBagConstraints anchor(final int anchor) {
		this.anchor = anchor;
		return this;
	}

	public GridBagConstraints fill(final int fill) {
		this.fill = fill;
		return this;
	}

	public GridBagConstraints gridWidth(final int gridWidth) {
		this.gridwidth = gridWidth;
		return this;
	}

	public GridBagConstraints gridWidthRemainder() {
		this.gridwidth = REMAINDER;
		return this;
	}
	
	public GridBagConstraints gridHeight(final int gridHeight) {
		this.gridheight = gridHeight;
		return this;
	}

	public GridBagConstraints gridSize(final int gridWidth, final int gridHeight) {
		this.gridwidth = gridWidth;
		this.gridheight = gridHeight;
		return this;
	}

	public GridBagConstraints weight(final double weightX, final double weightY) {
		this.weightx = weightX;
		this.weighty = weightY;
		return this;
	}

	public GridBagConstraints weightX(final double weightX) {
		this.weightx = weightX;
		return this;
	}
	
	public GridBagConstraints weightY(final double weightY) {
		this.weighty = weightY;
		return this;
	}

	public GridBagConstraints insets(final Insets insets) {
		this.insets = insets;
		return this;
	}

	/**
	 * Sets all insets to the given value
	 */
	public GridBagConstraints insets(final int value) {
		this.insets = new Insets(value, value, value, value);
		return this;
	}

	/**
	 * Replaces all non-zero insets with the given value 
	 */
	public GridBagConstraints insetsReplace(final int value) {
		final int t = insets.top == 0 ? 0 : value;
		final int l = insets.left == 0 ? 0 : value;
		final int b = insets.bottom == 0 ? 0 : value;
		final int r = insets.right == 0 ? 0 : value;
		this.insets = new Insets(t, l, b, r);
		return this;
	}

	public GridBagConstraints insets(final int top, final int left, final int bottom, final int right) {
		this.insets = new Insets(top, left, bottom, right);
		return this;
	}

	public GridBagConstraints insetTop(final int top) {
		this.insets.top = top;
		return this;
	}

	public GridBagConstraints insetBottom(final int bottom) {
		this.insets.bottom = bottom;
		return this;
	}

	public GridBagConstraints insetLeft(final int left) {
		this.insets.left = left;
		return this;
	}

	public GridBagConstraints insetRight(final int right) {
		this.insets.right = right;
		return this;
	}
	
	/**
	 * Helper method to change anchor and/or fill of the given GridBagConstraints.
	 * Values can be supplied in any order.
	 */
	protected GridBagConstraints overrideProperties(final int ... overrideProperties) {
		if (overrideProperties == null)
			return this;
		for (final int property : overrideProperties) {
			switch (property) {
				case CENTER:
				case NORTH:
				case NORTHEAST:
				case EAST:
				case SOUTHEAST:
				case SOUTH:
				case SOUTHWEST:
				case WEST:
				case NORTHWEST:
					this.anchor = property;
					break;
				case NONE:
					this.fill = property;
					this.weightx = 
					this.weighty = 0D;
					break;
				case BOTH:
					this.fill = property;
					this.weightx =
					this.weighty = 1D;
					break;
				case HORIZONTAL:
					this.fill = property;
					this.weightx = 1D;
					this.weighty = 0D;
					break;
				case VERTICAL:
					this.fill = property;
					this.weightx = 0D;
					this.weighty = 1D;
					break;
				default:
					throw new RuntimeException(Strings.concat("unsupported value: ", property));
			}
		}
		return this;
	}

	// --------------------------------------------------------------
	// ---
	// --- Factory methods
	// ---
	// --------------------------------------------------------------
	/**
	 * Create GridBagConstraints fitting for a label
	 */
	public static GridBagConstraints label(final int x, final int y) {
		return label(x, y, (int[]) null);
	}

	/**
	 * Create GridBagConstraints fitting for a label
	 */
	public static GridBagConstraints label(final int x, final int y, final int ... overrideProperties) {
		final GridBagConstraints c = new GridBagConstraints(
				x, y, 1, 1, 0D, 0D,
				EAST, NONE,
				new Insets(y <= 0 ? VSPACE : 0, x <= 0 ? HSPACE : 0, VSPACE, HSPACE),
				0, 0);
		return c.overrideProperties(overrideProperties);
	}

	/**
	 * Create GridBagConstraints fitting for a fixed size component
	 */
	public static GridBagConstraints fixed(final int x, final int y) {
		return fixed(x, y, (int[]) null);
	}
	
	/**
	 * Create GridBagConstraints fitting for a fixed size component
	 */
	public static GridBagConstraints fixed(final int x, final int y, final int ... overrideProperties) {
		final GridBagConstraints c = new GridBagConstraints(
				x, y, 1, 1, 0D, 0D,
				WEST, NONE,
				new Insets(y <= 0 ? VSPACE : 0, x <= 0 ? HSPACE : 0, VSPACE, HSPACE),
				0, 0);
		return c.overrideProperties(overrideProperties);
	}

	/**
	 * Creates GridBagConstraints fitting for a button
	 */
	public static GridBagConstraints button(final int x, final int y) {
		return button(x, y, (int[]) null);
	}
	
	/**
	 * Creates GridBagConstraints fitting for a button
	 */
	public static GridBagConstraints button(final int x, final int y, final int ... overrideProperties) {
		final GridBagConstraints c = new GridBagConstraints(
				x, y, 1, 1, 0D, 0D,
				CENTER, NONE,
				new Insets(y <= 0 ? VSPACE : 0, x <= 0 ? HSPACE : 0, VSPACE, HSPACE),
				0, 0);
		return c.overrideProperties(overrideProperties);
	}

	/**
	 * Create GridBagConstraints fitting for a field
	 */
	public static GridBagConstraints field(final int x, final int y) {
		return field(x, y, (int[]) null);
	}

	/**
	 * Create GridBagConstraints fitting for a field
	 */
	public static GridBagConstraints field(final int x, final int y, final int ... overrideProperties) {
		final GridBagConstraints c = new GridBagConstraints(
				x, y, 1, 1, 1D, 0D,
				WEST, HORIZONTAL,
				new Insets(y <= 0 ? VSPACE : 0, x <= 0 ? HSPACE : 0, VSPACE, HSPACE),
				0, 0);
		return c.overrideProperties(overrideProperties);
	}

	/**
	 * Create GridBagConstraints fitting for a separator
	 */
	public static GridBagConstraints separator(final int x, final int y) {
		return separator(x, y, (int[]) null);
	}

	/**
	 * Create GridBagConstraints fitting for a separator
	 */
	public static GridBagConstraints separator(final int x, final int y, final int ... overrideProperties) {
		final GridBagConstraints c = new GridBagConstraints(
				x, y, REMAINDER, 1, 1D, 0D,
				CENTER, HORIZONTAL,
				new Insets(y <= 0 ? VSPACE : 0, x <= 0 ? HSPACE : 0, VSPACE, HSPACE),
				0, 0);
		return c.overrideProperties(overrideProperties);
	}

	/**
	 * Create GridBagConstraints fitting for a decorator
	 */
	public static GridBagConstraints decorator(final int x, final int y) {
		return decorator(x, y, (int[]) null);
	}

	/**
	 * Create GridBagConstraints fitting for a decorator
	 */
	public static GridBagConstraints decorator(final int x, final int y, final int ... overrideProperties) {
		final GridBagConstraints c = new GridBagConstraints(
				x, y, REMAINDER, 1, 1D, 0D,
				WEST, HORIZONTAL,
				new Insets(y <= 0 ? VSPACE : 0, x <= 0 ? HSPACE : 0, VSPACE, HSPACE),
				0, 0);
		return c.overrideProperties(overrideProperties);
	}
	
	/**
	 * Create GridBagConstraints fitting for an area
	 * (area means a component that is resize-able but still need spacing) 
	 */
	public static GridBagConstraints area(final int x, final int y) {
		return area(x, y, (int[]) null);
	}
	
	/**
	 * Create GridBagConstraints fitting for an area
	 * (area means a component that is resize-able but still need spacing) 
	 */
	public static GridBagConstraints area(final int x, final int y, final int ... overrideProperties) {
		final GridBagConstraints c = new GridBagConstraints(
				x, y, 1, 1, 1D, 1D,
				CENTER, BOTH,
				new Insets(y <= 0 ? VSPACE : 0, x <= 0 ? HSPACE : 0, VSPACE, HSPACE),
				0, 0);
		return c.overrideProperties(overrideProperties);
	}

	/**
	 * Create GridBagConstraints fitting for a panel
	 * (panel means a component that is resize-able and does not require spacing) 
	 */
	public static GridBagConstraints panel(final int x, final int y) {
		return panel(x, y, (int[]) null);
	}
	
	/**
	 * Create GridBagConstraints fitting for a panel
	 * (panel means a component that is resize-able and does not require spacing) 
	 */
	public static GridBagConstraints panel(final int x, final int y, final int ... overrideProperties) {
		final GridBagConstraints c = new GridBagConstraints(
				x, y, 1, 1, 1D, 1D,
				CENTER, BOTH,
				new Insets(0, 0, 0, 0),
				0, 0);
		return c.overrideProperties(overrideProperties);
	}

	/**
	 * Create GridBagConstraints fitting for a spacer
	 */
	public static GridBagConstraints spacer(final int x, final int y) {
		return panel(x, y, (int[]) null);
	}

	/**
	 * Create GridBagConstraints fitting for a spacer
	 */
	public static GridBagConstraints spacer(final int x, final int y, final int ... overrideProperties) {
		return panel(x, y, overrideProperties);
	}

	/**
	 * Sets padding
	 */
	public GridBagConstraints pad(final int padX, final int padY) {
		this.ipadx = padX;
		this.ipady = padY;
		return this;
	}

}

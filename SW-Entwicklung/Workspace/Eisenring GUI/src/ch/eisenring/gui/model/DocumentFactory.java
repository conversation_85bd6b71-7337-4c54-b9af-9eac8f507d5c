package ch.eisenring.gui.model;

import javax.swing.text.Document;
import javax.swing.text.PlainDocument;

import ch.eisenring.core.util.CharacterFilter;

public abstract class DocumentFactory {

	protected DocumentFactory() {
	}

	// --------------------------------------------------------------
	// ---
	// --- Helper methods
	// ---
	// --------------------------------------------------------------
	/**
	 * Creates a document that limits the input to the given length.
	 * If the length is 0 (zero), the document will be unlimited.
	 */
	public static Document create(final int lengthLimit) {
		if (lengthLimit == 0) {
			// create unlimited document
			return new PlainDocument();
		} else {
			// create limited document
			return new LimitingDocument(lengthLimit);
		}
	}

	public static Document create(final int lengthLimit,
			                      final CharacterFilter characterFilter) {
		return new LimitingDocument(lengthLimit, characterFilter);
	}	

}

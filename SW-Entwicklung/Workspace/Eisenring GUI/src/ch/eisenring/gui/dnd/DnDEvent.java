package ch.eisenring.gui.dnd;

import java.awt.Point;
import java.awt.datatransfer.DataFlavor;
import java.awt.datatransfer.Transferable;
import java.awt.dnd.InvalidDnDOperationException;

import javax.swing.JComponent;

import ch.eisenring.core.codetable.ErrorMessage;

/**
 * Common event interface. Makes the API for working with all the Swing DnD
 * shit uniform for all events. Also, the implementation behind will manage
 * to deal with multiple listeners processing the same event.
 */
public interface DnDEvent {

	/**
	 * Gets the location of the event.
	 * Be prepared that this call returns NULL, 
	 * a location may not be available under some circumstances.
	 */
	public Point getLocation();

	/**
	 * Gets the component where the drag/drop takes place.
	 * May be NULL.
	 */
	public JComponent getComponent();

	/**
	 * Checks if the specified flavor is supported
	 */
	public boolean isDataFlavorSupported(final DataFlavor flavor);
	
	/**
	 * Gets the available data flavors
	 */
	public DataFlavor[] getDataFlavors();

	/**
	 * Gets the source actions
	 * (actions supported by the drag source)
	 */
	public int getSourceActions();

	/**
	 * Gets the selected action
	 */
	public int getSelectedAction();

	/**
	 * Rejects the drag/drop
	 * Undoes a previous accept(), if called by the same listener.
	 * Otherwise its a NO-OP. 
	 */
	public void reject();
	
	/**
	 * Accepts the drag/drop. 
	 * Calling this will consume the event.
	 */
	public void accept(final int action) throws InvalidDnDOperationException;

	/**
	 * Returns true if the drag/drop has already been accepted
	 * (by another DnDListener). Listeners must check and honor this,
	 * the easiest way to do so automagically is to return false
	 * from the listeners dispatchConsumed()-implementation.
	 */
	public boolean isConsumed();

	/**
	 * Gets the transfer data
	 */
	public Transferable getTransferable() throws InvalidDnDOperationException;
	
	/**
	 * Shows an error message
	 */
	public void message(final ErrorMessage message);

	/**
	 * Gets the DropTargetManager that manages this event
	 */
	public HEAGDropTargetManager getDropTargetManager();

}

package ch.eisenring.gui.dnd;

import java.awt.Point;
import java.awt.Rectangle;
import java.awt.event.InputEvent;
import java.awt.event.MouseEvent;
import java.awt.event.MouseMotionListener;
import java.lang.ref.WeakReference;

import javax.swing.JComponent;
import javax.swing.JList;
import javax.swing.JTree;
import javax.swing.Scrollable;
import javax.swing.SwingConstants;
import javax.swing.tree.TreePath;

import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.threading.ThreadPool;
import ch.eisenring.gui.util.GUIUtil;

/**
 * Provides Explorer-like mouse behavior when dragging over the component:
 * - expand node when mouse hovers over it for a time (for JTree)
 * - scroll if mouse is dragged to the very top/bottom
 *   of the visible component rectangle. (if in a ScrollPane)
 */
public final class DnDUsabilityEnhancer implements MouseMotionListener, DnDListener {

	private final Object expanderLock = new Object();
	
	private final static int expandDelay = 750;
	private final static int scrollZoneHeight = 8;
	
	private final JComponent component;
	private Object hoveredItem;
	private long hoverTimestamp;
	private boolean isDnDUnderway;

	public DnDUsabilityEnhancer(final JComponent component) {
		startExpanderThread();
		this.component = component;
		addExpander(this);
		component.addMouseMotionListener(this);
	}

	// --------------------------------------------------------------
	// ---
	// --- Manages DnD aware component
	// ---
	// --------------------------------------------------------------
	protected void setDnDStatus(final boolean dndUnderway) {
		if (!dndUnderway) {
			setHoveredItem(null);
		}
		this.isDnDUnderway = dndUnderway;
	}
	
	// --------------------------------------------------------------
	// ---
	// --- Hover management
	// ---
	// --------------------------------------------------------------
	protected void setHoveredItem(final Object item) {
		synchronized (expanderLock) {
			if (component instanceof JTree) {
				final TreePath path = (TreePath) item;
				if (path == null) {
					hoveredItem = null;
					hoverTimestamp = Long.MAX_VALUE;
				} else if (!path.equals(hoveredItem)) {
					hoveredItem = path;
					hoverTimestamp = System.currentTimeMillis();
				}
				if (isDnDUnderway) {
					if (path == null) {
						((JTree) component).clearSelection();
					} else {
						((JTree) component).setSelectionPath(path);
					}
				}
			} else if (component instanceof JList) {
				Integer index = (Integer) item;
				if (index == null) {
					hoveredItem = null;
					hoverTimestamp = Long.MAX_VALUE;
				} else if (!index.equals(hoveredItem)) {
					hoveredItem = index;
					hoverTimestamp = System.currentTimeMillis();
				}
				if (isDnDUnderway) {
					if (index == null) {
						((JList<?>) component).clearSelection();
					} else {
						((JList<?>) component).setSelectedIndex(index);
					}
				}
			}
		}
	}

	protected void tick() {
		Runnable r = null;
		if (component instanceof JTree) {
			synchronized (expanderLock) {
				final TreePath path = (TreePath) this.hoveredItem;
				if (path != null && System.currentTimeMillis() - expandDelay > hoverTimestamp) {
					hoverTimestamp = Long.MAX_VALUE;
					r = new Runnable() {
						@Override
						public void run() {
							((JTree) component).expandPath(path);
						}
					};
				}
			}
		}
		if (r != null)
			GUIUtil.invokeLater(r);
	}

	// --------------------------------------------------------------
	// ---
	// --- Static helpers
	// ---
	// --------------------------------------------------------------
	/**
	 * Detects if the given component is currently performing
	 * a DnD operation. Returns true if so.
	 */
	public static boolean isPerformingDnD(final JComponent component) {
		synchronized (timerLock) {
			for (int i=expanderRefs.size()-1; i>=0; --i) {
				final WeakReference<DnDUsabilityEnhancer> ref = expanderRefs.get(i);
				final DnDUsabilityEnhancer expander = ref.get();
				if (expander != null && expander.component == component)
					return expander.isDnDUnderway;
			}
		}
		return false;
	}

	// --------------------------------------------------------------
	// ---
	// --- Timer Thread
	// ---
	// --------------------------------------------------------------
	static Thread timerThread;
	final static Object timerLock = new Object();
	final static ArrayList<WeakReference<DnDUsabilityEnhancer>> expanderRefs =
			new ArrayList<WeakReference<DnDUsabilityEnhancer>>(4);

	static void startExpanderThread() {
		synchronized (timerLock) {
			if (timerThread != null)
				return;
			final Runnable task = new Runnable() {
				@Override
				public void run() {
					timerLoop();
				}
			}; 
			timerThread = ThreadPool.DEFAULT.startDaemon(task, "DnDUsabilityExpander", Thread.MAX_PRIORITY);
		}
	}
	
	static void timerLoop() {
		final ArrayList<DnDUsabilityEnhancer> activeExpanders = new ArrayList<DnDUsabilityEnhancer>(4);
		while (true) {
			synchronized (timerLock) {
				// find active expanders and forget dead ones
				for (int i=expanderRefs.size()-1; i>=0; --i) {
					final WeakReference<DnDUsabilityEnhancer> ref = expanderRefs.get(i);
					final DnDUsabilityEnhancer expander = ref.get();
					if (expander == null) {
						expanderRefs.remove(i);
					} else {
						activeExpanders.add(expander);
					}
				}
				try {
					if (activeExpanders.isEmpty()) {
						// wait till work appears
						timerLock.wait(2500);
					} else {
						// call back regularly
						timerLock.wait(33);
					}
				} catch (final InterruptedException e) {
					// not that we care...
					break;
				}
			}
			// notify all active expanders
			while (!activeExpanders.isEmpty()) {
				final DnDUsabilityEnhancer expander = activeExpanders.remove(0);
				try {
					expander.tick();
				} catch (final RuntimeException e) {
					// ignore
				}
			}
		}
	}

	static void addExpander(final DnDUsabilityEnhancer expander) {
		final WeakReference<DnDUsabilityEnhancer> ref = new WeakReference<DnDUsabilityEnhancer>(expander);
		synchronized (timerLock) {
			expanderRefs.add(ref);
			timerLock.notify();
		}
	}

	// --------------------------------------------------------------
	// ---
	// --- MouseMotionListener implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public void mouseMoved(final MouseEvent event) {
		if ((event.getModifiersEx() & InputEvent.BUTTON1_DOWN_MASK) == 0) {
			setDnDStatus(false);
			setHoveredItem(null);
		}
	}

	@Override
	public void mouseDragged(final MouseEvent event) {
		mouseDragged(event.getPoint());
	}

	private void mouseDragged(final Point location) {
		final Rectangle visibleRect = component.getVisibleRect();
		do {
			if (visibleRect == null)
				break;
			if (!visibleRect.contains(location)) {
				setHoveredItem(null);
				return;
			}
			if (visibleRect.y + scrollZoneHeight >= location.y && visibleRect.y > 0) {
				// positioned on upper border
				setHoveredItem(null);
				visibleRect.y = Math.max(visibleRect.y - getScrollDelta(-1), 0);
				component.scrollRectToVisible(visibleRect);
				return;
			} else if (visibleRect.y + visibleRect.height - scrollZoneHeight <= location.y) {
				// positioned on lower border
				setHoveredItem(null);
				visibleRect.y += getScrollDelta(1);
				component.scrollRectToVisible(visibleRect);
				return;
			}
		} while(false);
		if (component instanceof JTree) {
			final TreePath item = ((JTree) component).getPathForLocation(location.x, location.y);
			setHoveredItem(item);
		} else if (component instanceof JList) {
			final int index = ((JList<?>) component).locationToIndex(location);
			final Rectangle r = ((JList<?>) component).getCellBounds(index, index);
			if (r != null && r.contains(location)) {
				setHoveredItem(Integer.valueOf(index));
			} else {
				setHoveredItem(null);
			}
		}
	}

	/**
	 * Determines how far to scroll when mouse is hovering
	 * at the top/bottom border of the visible region.
	 */
	private int getScrollDelta(final int direction) {
		int delta = 20;
		if (component instanceof Scrollable) {
			final Rectangle visibleRect = component.getVisibleRect();
			delta = ((Scrollable) component).getScrollableUnitIncrement(
					visibleRect, SwingConstants.VERTICAL, direction);
		}
		return delta;
	}

	// --------------------------------------------------------------
	// ---
	// --- DnDListener implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public void dragEnter(final DnDEvent event) {
		setDnDStatus(true);
		mouseDragged(event.getLocation());
	}

	@Override
	public void dragOver(final DnDEvent event) {
		setDnDStatus(true);
		mouseDragged(event.getLocation());
	}

	@Override
	public void dropActionChanged(final DnDEvent event) {
		setDnDStatus(true);
		mouseDragged(event.getLocation());
	}

	@Override
	public void drop(final DnDEvent event) {
		setDnDStatus(false);
	}

	@Override
	public void dragExit(final DnDEvent event) {
		setHoveredItem(null);
		setDnDStatus(false);
	}

	@Override
	public boolean dispatchConsumed() {
		// we don't care if an event was already consumed,
		// we are only interested in the location (mouse position).
		return true;
	}

}

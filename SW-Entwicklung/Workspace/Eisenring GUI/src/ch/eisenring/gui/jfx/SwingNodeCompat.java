package ch.eisenring.gui.jfx;

/**
 * Compatibility layer for SwingNode functionality.
 * Handles the fact that SwingNode is not available in Java 21 due to
 * missing LightweightContentWrapper class.
 */
public class SwingNodeCompat {
    
    private static final boolean SWING_NODE_AVAILABLE;
    
    static {
        boolean available = false;
        try {
            // Check if the required internal class is available
            Class.forName("jdk.swing.interop.LightweightContentWrapper");
            // Also check if SwingNode itself is available
            Class.forName("javafx.embed.swing.SwingNode");
            available = true;
        } catch (ClassNotFoundException e) {
            System.out.println("SwingNode not available in this Java version: " + e.getMessage());
        }
        SWING_NODE_AVAILABLE = available;
    }
    
    /**
     * Checks if SwingNode functionality is available in the current Java version.
     * 
     * @return true if SwingNode can be used, false otherwise
     */
    public static boolean isSwingNodeAvailable() {
        return SWING_NODE_AVAILABLE;
    }
    
    /**
     * Gets a descriptive message about SwingNode availability.
     * 
     * @return A message describing the current state
     */
    public static String getAvailabilityMessage() {
        if (SWING_NODE_AVAILABLE) {
            return "SwingNode is available and can be used";
        } else {
            return "SwingNode is not available - falling back to pure Swing mode";
        }
    }
    
    /**
     * Checks if we should disable JavaFX features that depend on SwingNode.
     * 
     * @return true if JavaFX features should be disabled
     */
    public static boolean shouldDisableJavaFXFeatures() {
        return !SWING_NODE_AVAILABLE;
    }
}

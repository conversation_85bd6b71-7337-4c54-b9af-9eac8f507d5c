package ch.eisenring.gui.jfx.event;

import java.awt.Component;
import java.awt.Container;

import javafx.embed.swing.SwingNode;
import javafx.event.EventHandler;
import javafx.geometry.Point2D;
import javafx.scene.input.RotateEvent;
import javafx.scene.input.ScrollEvent;
import javafx.scene.input.SwipeEvent;
import javafx.scene.input.TouchEvent;
import javafx.scene.input.ZoomEvent;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.gui.util.GUIUtil;

/**
 * Dispatches FX events to swing components
 */
public final class FXEventDispatcher {

	/**
	 * The SwingNode this dispatcher works for
	 */
	final SwingNode swingNode;

	/**
	 * Root component in the swing hierarchy
	 * (the components used as content for SwingNode)
	 */
	Container rootComponent;
	
	/**
	 * Creates event dispatcher for swing node (must call this of FX thread).
	 * The dispatcher attaches itself to the node immediately.
	 */
	public FXEventDispatcher(final SwingNode swingNode) {
		this.swingNode = swingNode;
		this.rootComponent = swingNode.getContent();
		swingNode.addEventHandler(ScrollEvent.ANY, new FXMultiEventDispatcher<FXScrollEvent, ScrollEvent>(this) {
			@Override
			protected FXScrollEvent createSwingEvent(final ScrollEvent fxEvent) {
				return new FXScrollEvent(fxEvent);
			}
			
			@Override
			protected void dispatch(final FXEventHandler handler, final FXScrollEvent swingEvent) {
				handler.handleScroll(swingEvent);
			}
		});
		swingNode.addEventHandler(ZoomEvent.ANY, new FXMultiEventDispatcher<FXZoomEvent, ZoomEvent>(this) {
			@Override
			protected FXZoomEvent createSwingEvent(final ZoomEvent fxEvent) {
				return new FXZoomEvent(fxEvent);
			}
			
			@Override
			protected void dispatch(final FXEventHandler handler, final FXZoomEvent swingEvent) {
				handler.handleZoom(swingEvent);
			}
		});
		swingNode.addEventHandler(RotateEvent.ANY, new FXMultiEventDispatcher<FXRotateEvent, RotateEvent>(this) {
			@Override
			protected FXRotateEvent createSwingEvent(final RotateEvent fxEvent) {
				return new FXRotateEvent(fxEvent);
			}
			
			@Override
			protected void dispatch(final FXEventHandler handler, final FXRotateEvent swingEvent) {
				handler.handleRotate(swingEvent);
			}
		});
		swingNode.addEventHandler(TouchEvent.ANY, touchEventHandler);
		swingNode.addEventHandler(SwipeEvent.ANY, swipeEventHandler);
	}
	
	// --------------------------------------------------------------
	// ---
	// --- Swipe Events 
	// ---
	// --------------------------------------------------------------
	private EventHandler<TouchEvent> touchEventHandler = new EventHandler<TouchEvent>() {
		@Override
		public void handle(final TouchEvent fxEvent) {

			// TODO Auto-generated method stub
			
		}
	};
	
	private EventHandler<SwipeEvent> swipeEventHandler = new EventHandler<SwipeEvent>() {
		@Override
		public void handle(final SwipeEvent fxEvent) {
			// translate the event coordinates to node local coordinates
			if (rootComponent == null)
				return;
			final int x, y; {
				final Point2D p = swingNode.screenToLocal(fxEvent.getScreenX(), fxEvent.getScreenY());
				x = (int) p.getX();
				y = (int) p.getY();
			}
			// create and dispatch the event
			final FXSwipeEvent swingEvent = new FXSwipeEvent(fxEvent);
			GUIUtil.invokeAndWaitRuntime(new Runnable() {
				@Override
				public void run() {
					dispatchOnEDT(swingEvent, x, y);
				}
			});
		}
	};

	/**
	 * Dispatches FXSwipeEvent
	 */
	void dispatchOnEDT(final FXSwipeEvent event, final int x, final int y) {
		final List<Component> receivers = getReceiversAt(x, y);
		// dispatch event to all receivers in hierarchy
		if (receivers == null || receivers.isEmpty())
			return;
		for (final Component receiver : receivers) {
			// dispatch to component
			setEventComponent(event, x, y, receiver, rootComponent);
			try {
				final FXEventHandler handler = ((FXEventReceiver) receiver).getFXEventHandler();
				if (handler != null)
					handler.handleSwipe(event);
			} catch (final Exception e) {
				e.printStackTrace();
			}
		}
	}
	
	// --------------------------------------------------------------
	// ---
	// --- Helper methods 
	// ---
	// --------------------------------------------------------------
	/**
	 * Looks for FXEventReceivers in component hierarchy.
	 * 
	 * Returns an array of components that implement FXEventReceiver ordered
	 * childmost to topmost intersecting (x, y). 
	 */
	final List<Component> getReceiversAt(int x, int y) {
		if (rootComponent == null)
			return null;
		List<Component> result = null;
		Component component = rootComponent;
		while (component != null) {
			if (component instanceof FXEventReceiver) {
				if (result == null)
					result = new ArrayList<>();
				result.add(0, component);
			}
			Component child = component.getComponentAt(x, y);
			if (child == null || child == component)
				break;
			x -= child.getX();
			y -= child.getY();
			component = child;
		}
		return result;
	}

	static void setEventComponent(final FXGestureEvent event, int x, int y, Component c, final Component root) {
		event.component = c;
		while (c != null && c != root) {
			x -= c.getX();
			y -= c.getY();
			c = c.getParent();
		}
		event.x = x;
		event.y = y;
	}

	/**
	 * Detects if component is (still) a child of roots component hierarchy. 
	 */
	static boolean isChildOf(final Component root, final Component component) {
		if (root == null)
			return false;
		Component parent = component;
		while (parent != null) {
			if (parent == root)
				return true;
			parent = parent.getParent();
		}
		return false;
	}

}

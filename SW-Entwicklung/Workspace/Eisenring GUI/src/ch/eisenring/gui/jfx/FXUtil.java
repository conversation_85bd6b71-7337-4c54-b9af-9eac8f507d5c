package ch.eisenring.gui.jfx;

import javafx.application.Platform;
import javafx.embed.swing.JFXPanel;
import javafx.scene.paint.Color;
import ch.eisenring.gui.util.GUIUtil;

/**
 * Utility class for JavaFX integration with Swing applications.
 * Provides methods for initialization, thread management, and color conversion.
 * Compatible with both Java 8 and Java 21.
 */
public class FXUtil {

	final static double ONE255TH = 1D/255;

	// --------------------------------------------------------------
	// ---
	// --- Java Version Detection
	// ---
	// --------------------------------------------------------------
	private static class JavaVersion {
		private static final String VERSION = System.getProperty("java.version");
		private static final boolean IS_JAVA_8 = VERSION.startsWith("1.8");
		private static final boolean IS_JAVA_21_OR_LATER;

		static {
			boolean is21orLater = false;
			if (!IS_JAVA_8) {
				try {
					String version = VERSION.split("[-.]")[0];
					int majorVersion = Integer.parseInt(version);
					is21orLater = majorVersion >= 21;
				} catch (Exception e) {
					System.err.println("Could not determine Java version: " + e.getMessage());
				}
			}
			IS_JAVA_21_OR_LATER = is21orLater;
		}

		public static boolean isJava8() { return IS_JAVA_8; }
		public static boolean isJava21OrLater() { return IS_JAVA_21_OR_LATER; }
	}

	private static boolean isJava8() { return JavaVersion.isJava8(); }
	private static boolean isJava21OrLater() { return JavaVersion.isJava21OrLater(); }

	// --------------------------------------------------------------
	// ---
	// --- Initializes JavaFX
	// ---
	// --------------------------------------------------------------
	private static volatile int initFxDone = 0;

	/**
	 * Initializes JavaFX for use in a standard JavaSE/Swing environment.
	 * Calling initFx() multiple times is ok, and does not take excessive
	 * amounts of time.
	 *
	 * This method handles both Java 8 and Java 21+ environments.
	 */
	public static void initFx() {
		if (initFxDone == 0)
			initFxImpl();
	}

	private static synchronized void initFxImpl() {
		if (initFxDone != 0)
			return;
		try {
			// For Java 21+, we need to ensure JavaFX modules are properly loaded
			if (isJava21OrLater()) {
				// Log that we're using Java 21+ initialization
				System.out.println("Initializing JavaFX for Java 21+");
			} else if (isJava8()) {
				// Log that we're using Java 8 initialization
				System.out.println("Initializing JavaFX for Java 8");
			} else {
				// Log that we're using fallback initialization
				System.out.println("Initializing JavaFX for Java " + System.getProperty("java.version"));
			}

			GUIUtil.invokeAndWait(new Runnable() {
				@Override
				public void run() {
					Platform.setImplicitExit(false);
					// make sure the JIT does not get any funny ideas of optimizing this away
					initFxDone = new JFXPanel().hashCode() | 1;
				}
			});
		} catch (final Exception e) {
			System.err.println("Failed to initialize JavaFX: " + e.getMessage());
			e.printStackTrace();
			throw new RuntimeException("Failed to initialize JavaFX: " + e.getMessage(), e);
		}
	}

	// --------------------------------------------------------------
	// ---
	// --- Thread management
	// ---
	// --------------------------------------------------------------
	/**
	 * Invoke runnable on FX thread
	 *
	 * @param runnable The runnable to execute on the JavaFX application thread
	 */
	public static void invokeLater(final Runnable runnable) {
		initFx();
		try {
			Platform.runLater(runnable);
		} catch (Exception e) {
			System.err.println("Error invoking runnable on FX thread: " + e.getMessage());
			e.printStackTrace();
			throw new RuntimeException("Error invoking runnable on FX thread", e);
		}
	}

	/**
	 * Helper class used to implement invoke and wait functionality for FX
	 */
	static final class InvokeHelper implements Runnable {
		public final Runnable target;
		public Throwable throwable;

		public InvokeHelper(final Runnable target) {
			this.target = target;
		}

		@Override
		public void run() {
			try {
				target.run();
			} catch (final Throwable t) {
				this.throwable = t;
			}
			synchronized (this) {
				notifyAll();
			}
		}
	}

	/**
	 * Invoke runnable on FX thread, blocking calling thread until
	 * the runnable has finished.
	 *
	 * @param runnable The runnable to execute on the JavaFX application thread
	 */
	public static void invokeAndWait(final Runnable runnable) {
		initFx();
		try {
			if (Platform.isFxApplicationThread()) {
				// already in FX thread, just run it
				try {
					runnable.run();
				} catch (Exception e) {
					System.err.println("Error running runnable directly on FX thread: " + e.getMessage());
					e.printStackTrace();
					throw e;
				}
			} else {
				final InvokeHelper helper = new InvokeHelper(runnable);
				synchronized (helper) {
					try {
						Platform.runLater(helper);
					} catch (Exception e) {
						System.err.println("Error scheduling runnable on FX thread: " + e.getMessage());
						e.printStackTrace();
						throw new RuntimeException("Error scheduling runnable on FX thread", e);
					}

					try {
						helper.wait();
					} catch (final InterruptedException e) {
						System.err.println("Interrupted while waiting for FX thread: " + e.getMessage());
						e.printStackTrace();
						throw new RuntimeException("Interrupted while waiting for FX thread", e);
					}

					Throwable t = helper.throwable;
					if (t != null) {
						if (t instanceof RuntimeException) {
							throw (RuntimeException) t;
						} else {
							throw new RuntimeException(t.getMessage(), t);
						}
					}
				}
			}
		} catch (RuntimeException e) {
			throw e;
		} catch (Exception e) {
			throw new RuntimeException("Unexpected error in invokeAndWait", e);
		}
	}

	// --------------------------------------------------------------
	// ---
	// --- Color creation helpers
	// ---
	// --------------------------------------------------------------
	/**
	 * Factory method for FX color
	 */
	public static Color getColor(final int argb, final boolean hasAlpha) {
		final int r = (argb >> 16) & 0xFF;
		final int g = (argb >>  8) & 0xFF;
		final int b = (argb      ) & 0xFF;
		if (hasAlpha) {
			return Color.rgb(r, g, b,
					(argb >>> 24) * ONE255TH);
		} else {
			return Color.rgb(r, g, b);
		}
	}

	public static Color getColor(final int rgb) {
		return new Color(
				((rgb >> 16) & 255) * ONE255TH,
				((rgb >> 8) & 255) * ONE255TH,
				(rgb & 255) * ONE255TH, 1D);
	}

	// --------------------------------------------------------------
	// ---
	// --- JavaFX availability and version info
	// ---
	// --------------------------------------------------------------

	/**
	 * Checks if JavaFX is available and properly initialized.
	 *
	 * @return true if JavaFX is available and initialized, false otherwise
	 */
	public static boolean isJavaFXAvailable() {
		try {
			initFx();
			return initFxDone != 0;
		} catch (Exception e) {
			System.err.println("JavaFX is not available: " + e.getMessage());
			return false;
		}
	}

	/**
	 * Gets the Java version information.
	 *
	 * @return A string describing the Java version and JavaFX availability
	 */
	public static String getJavaVersionInfo() {
		StringBuilder sb = new StringBuilder();
		sb.append("Java version: ").append(JavaVersion.VERSION);
		sb.append(", JavaFX available: ").append(isJavaFXAvailable());
		sb.append(", Running on Java 8: ").append(isJava8());
		sb.append(", Running on Java 21+: ").append(isJava21OrLater());

		// Check SwingNode compatibility
		try {
			Class.forName("ch.eisenring.gui.jfx.SwingNodeCompat");
			sb.append(", SwingNode available: ").append(
				(Boolean) Class.forName("ch.eisenring.gui.jfx.SwingNodeCompat")
					.getMethod("isSwingNodeAvailable")
					.invoke(null));
		} catch (Exception e) {
			sb.append(", SwingNode status: unknown");
		}

		return sb.toString();
	}
}

package ch.eisenring.gui.desktop;

import java.awt.AWTException;
import java.awt.Component;
import java.awt.Container;
import java.awt.Dimension;
import java.awt.Image;
import java.awt.Point;
import java.awt.Rectangle;
import java.awt.SystemTray;
import java.awt.TrayIcon;
import java.awt.event.MouseEvent;
import java.awt.event.MouseListener;
import java.util.concurrent.atomic.AtomicBoolean;

import javax.swing.JPopupMenu;

import ch.eisenring.core.datatypes.primitives.Primitives;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.resource.ImageResource;
import ch.eisenring.core.threading.ThreadPool;
import ch.eisenring.gui.menu.MenuBuilder;
import ch.eisenring.gui.resource.images.Images;
import ch.eisenring.gui.util.GUIUtil;

public abstract class DesktopIntegrationBase {

	protected final SystemTray systemTray;

	protected ImageResource trayIconImage = Images.COG;
	protected TrayIcon trayIcon;
	protected String toolTip;

	protected DesktopIntegrationBase(final ImageResource iconImage) {
		this.systemTray = SystemTray.isSupported() ? SystemTray.getSystemTray() : null;
		setTrayIconImageImpl(iconImage);
	}

	// --------------------------------------------------------------
	// ---
	// --- Tray icon installation / removal
	// ---
	// --------------------------------------------------------------
	/**
	 * Attaches the tray icon to the system tray.
	 * Returns true if success, else false. False means
	 * usually the system does not support tray icons.
	 */
	protected final boolean attachTrayIconImpl() {
		if (!GUIUtil.isEventDispatchThread()) {
			final AtomicBoolean result = new AtomicBoolean();
			GUIUtil.invokeAndWaitSilent(new Runnable() {
				@Override
				public void run() {
					result.set(attachTrayIconImpl());
				}
			});
			return result.get();
		}
		if (trayIcon == null && systemTray != null) {
			final int size = SystemTray.getSystemTray().getTrayIconSize().width;
			trayIcon = new TrayIcon(trayIconImage.getImage(size));
			trayIcon.addMouseListener(trayIconMouseListener);
			try {
				systemTray.add(trayIcon);
			} catch (AWTException e) {
				trayIcon = null;
			}
			trayIcon.setToolTip(toolTip);
		}
		return trayIcon != null;
	}

	/**
	 * Removes the tray icon from the system tray.
	 * If no tray icon was attached or the system doesn't support
	 * tray icons at all, does nothing.
	 */
	protected final void removeTrayIconImpl() {
		if (!GUIUtil.isEventDispatchThread()) {
			GUIUtil.invokeAndWaitSilent(new Runnable() {
				@Override
				public void run() {
					removeTrayIconImpl();
				}
			});
			return;
		}
		if (trayIcon != null) {
			try {
				systemTray.remove(trayIcon);
			} catch (final Throwable e) {
			} finally {
				trayIcon = null;
			}
		}
	}
	
	/**
	 * Implements setting the tray icon image.
	 * Must be called from setTrayIconImage().
	 */
	protected final void setTrayIconImageImpl(final ImageResource imageResource) {
		if (!GUIUtil.isEventDispatchThread()) {
			GUIUtil.invokeAndWaitSilent(new Runnable() {
				@Override
				public void run() {
					setTrayIconImageImpl(imageResource);
				}
			});
			return;
		}
		final ImageResource newResource = imageResource == null ? Images.COG : imageResource;
		if (!Primitives.equals(trayIconImage, newResource)) {
			trayIconImage = newResource;
			if (trayIcon != null) {
				final int size = trayIcon.getSize().width;
				final Image image = trayIconImage.getImage(size);
				trayIcon.setImage(image);
			}
		}
	}

	public void setTrayIconImage(final ImageResource image) {
		setTrayIconImageImpl(image);
	}
	
	// --------------------------------------------------------------
	// ---
	// --- MouseListener implementation
	// ---
	// --------------------------------------------------------------
	private final MouseListener trayIconMouseListener = new MouseListener() {
		@Override
		public final void mouseClicked(final MouseEvent event) {
			if (event.isConsumed())
				return;
			switch (event.getButton()) {
				case MouseEvent.BUTTON1:
					if (event.getClickCount() == 1) {
						onLeftClick(event);
					}
					break;
			}
		}
	
		@Override
		public final void mouseEntered(final MouseEvent event) {}
		@Override
		public final void mouseExited(final MouseEvent event) {}
		@Override
		public final void mousePressed(final MouseEvent event) {
			if (event.isPopupTrigger()) {
				showTrayPopup(event);
			}
		}
		@Override
		public final void mouseReleased(final MouseEvent event) {
			if (event.isPopupTrigger()) {
				showTrayPopup(event);
			}
		}
	};

	/**
	 * Invoked when the tray menu should become visible
	 */
	private final void showTrayPopup(final MouseEvent event) {
		if (event.isConsumed() || !event.isPopupTrigger())
			return;

		final MenuBuilder trayContextMenu = getTrayContextMenu();
		if (trayContextMenu == null || trayContextMenu.isEmpty())
			return;

		event.consume();
		final JPopupMenu popup = trayContextMenu.populate(new TrayPopupMenuHack());
		popup.pack();
		final Dimension d = popup.getPreferredSize();
		final int x = event.getXOnScreen() - d.width + 1;
		final int y = event.getYOnScreen() - d.height + 1;
		popup.setInvoker(popup);
		popup.setLocation(x, y);
		popup.setVisible(true);
	}

	/**
	 * Updates tray icon tool tip with the specified text
	 * (NULL removes the tool tip).
	 */
	protected final void updateToolTipImpl(final CharSequence toolTip) {
		final TrayIcon trayIcon = this.trayIcon;
		if (trayIcon == null)
			return;
		if (Strings.equals(this.toolTip, toolTip))
			return;
		this.toolTip = Strings.toString(toolTip);
		trayIcon.setToolTip(this.toolTip);
	}

	// --------------------------------------------------------------
	// ---
	// --- Launch / Shutdown
	// ---
	// --------------------------------------------------------------
	public void attachTrayIcon() {
		attachTrayIconImpl();
	}

	public void removeTrayIcon() {
		removeTrayIconImpl();
	}

	// --------------------------------------------------------------
	// ---
	// --- Methods to be implemented by concrete sub classes
	// ---
	// --------------------------------------------------------------
	/**
	 * Override to handle click on tray icon with left mouse button
	 */
	protected abstract void onLeftClick(final MouseEvent event);

	/**
	 * Returns the context menu contents. Returns NULL or empty
	 * builder to disable context menu.
	 */
	protected abstract MenuBuilder getTrayContextMenu();
	
}

/**
 * Ugly hack to make the system tray menu behave reasonably, since
 * tray integration has it's issues with JPopupMenus (the alternative,
 * using AWT doesn't really appeal much either).
 * 
 * This makes the menu hide shortly after the mouse exits.
 * Correct behavior would be to hide as soon as the user clicks outside
 * the menu. But that event never reaches the menu as it seems. 
 */
@SuppressWarnings("serial")
final class TrayPopupMenuHack extends JPopupMenu implements MouseListener, Runnable {

	private final static int TIMEOUT = 1250; // milliseconds

	protected Thread thread;
	protected long lastExited = Long.MAX_VALUE;
	protected boolean visible;
	
	public TrayPopupMenuHack() {
	}

	@Override
	public void setVisible(boolean visible) {
		Thread thread = this.thread;
		if (this.visible != visible) {
			this.visible = visible;
			if (!visible) {
				lastExited = Long.MIN_VALUE;
				makeListeners(this, false);
				if (thread != null)
					thread.interrupt();
			} else {
				if (thread == null) {
					this.thread = ThreadPool.DEFAULT.startDaemon(this, "TrayPopupMenuHack", Thread.NORM_PRIORITY + 2);
					makeListeners(this, true);
				}
			}
			super.setVisible(visible);
		}
	}

	@Override
	public final void run() {
		if (GUIUtil.isEventDispatchThread()) {
			setVisible(false);
		} else {
			try {
				while (true) {
					Thread.sleep(100);
					if (lastExited < System.currentTimeMillis() - TIMEOUT) {
						break;
					}
				}
			} catch (InterruptedException e) {
			} finally {
				thread = null;
				GUIUtil.invokeLater(this);
			}
		}
	}

	@Override
	public final void mouseClicked(MouseEvent e) {}
	@Override
	public final void mousePressed(MouseEvent e) {}
	@Override
	public final void mouseReleased(MouseEvent e) {}
	
	@Override
	public final void mouseEntered(MouseEvent e) {
		lastExited = Long.MAX_VALUE;
	}

	@Override
	public final void mouseExited(MouseEvent e) {
		final int x = e.getXOnScreen();
		final int y = e.getYOnScreen();
		final Rectangle r = getBounds();
		final Point p = getLocationOnScreen();
		r.translate(p.x, p.y);
		if (!r.contains(x, y)) {
			lastExited = System.currentTimeMillis();
		}
	}

	protected final void makeListeners(final Component component, boolean add) {
		if (add) {
			component.addMouseListener(this);
		} else {
			component.removeMouseListener(this);
		}
		if (component instanceof Container) {
			Container container = (Container) component;
			for (int i=container.getComponentCount()-1; i>=0; --i) {
				makeListeners(container.getComponent(i), add);
			}
		}
	}

}

package ch.eisenring.gui.action;

import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.datatypes.primitives.Primitives;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.core.logging.Logger.LogLevel;
import ch.eisenring.core.threading.ThreadCore;
import ch.eisenring.core.threading.ThreadPool;
import ch.eisenring.gui.dialogs.BusyDialog;
import ch.eisenring.gui.util.GUIUtil;

public final class ActionQueue {

	private final static ActionQueue INSTANCE = new ActionQueue();
	
	public static ActionQueue getInstance() {
		return INSTANCE;
	}

	private final ArrayList<AbstractAction> actionQueue = new ArrayList<AbstractAction>();
	private final Runnable runLater = new Runnable() {
		@Override
		public void run() {
			performPendingActions();
		}
	};
	
	static class AsynchronousRunner implements Runnable {
		private final AbstractAction action;
		private final BusyDialog busyDialog;
		
		public AsynchronousRunner(final AbstractAction action) {
			this.action = action;
			this.busyDialog = action.isLongRunning() ? new BusyDialog() : null;
		}
		
		@Override
		public void run() {
			if (busyDialog != null)
				GUIUtil.invokeLater(() -> {	busyDialog.setVisible(true); });
			try {
				action.performAction();
			} catch (final Exception e) {
				Logger.error(Strings.concat("The action '", action, "' caused an exception:")); 
				Logger.error(e); 
			}
			if (busyDialog != null) {
				GUIUtil.invokeLater(new Runnable() {
					@Override
					public void run() {
						busyDialog.setVisible(false);
					}
				});
			}
		}
	}

	private ActionQueue() {
	}

	/**
	 * Enqueues an action for execution.
	 * 
	 * Beware that when called from any thread other that
	 * the event dispatch thread, this is a asynchronous operation!
	 */
	public void enqueue(final AbstractAction action) {
		if (action != null) {
			synchronized (actionQueue) {
				actionQueue.add(action);
			}
			if (Logger.isEnabled(LogLevel.DEBUG))
				Logger.debug(Strings.concat("action class ", Primitives.getSimpleName(action.getClass()), " added to queue"));
		}
		// make sure actions are always processed on the
		// event dispatch thread to avoid trouble with swing
		if (Logger.isDebugEnabled())
			Logger.debug("performing pending actions (asynchronous)");
		GUIUtil.invokeLater(runLater);
	}

	/**
	 * Process all actions in the queue
	 */
	void performPendingActions() {
		while (true) {
			final AbstractAction action;
			synchronized (actionQueue) {
				if (actionQueue.isEmpty()) {
					break;
				}
				action = actionQueue.remove(0);
			}
			// execution of the action is outside synchronized!
			if (Logger.isEnabled(LogLevel.DEBUG))
				Logger.debug(Strings.concat("performing action: ", Primitives.getSimpleName(action.getClass())));
			if (action.isAsynchronous()) {
				// perform action in its own thread
				final Runnable task = new AsynchronousRunner(action);
				ThreadPool.DEFAULT.start(task);
			} else {
				// perform action in EDT
				try {
					action.performAction();
				} catch (final Exception e) {
					Logger.error(Strings.concat("The action '", action, "' caused an exception:")); 
					Logger.error(e); 
				}
			}
		}
	}
	
	/**
	 * Checks if the action queue is currently empty.
	 * It only returns true, if no actions are pending for
	 * execution, nor an action is currently executing.
	 */
	public boolean isEmpty() {
		synchronized (actionQueue) {
			return actionQueue.isEmpty();
		}
	}
	
	/**
	 * Delays the current thread until the action queue
	 * is empty and no action is executing.
	 * This can't reliably ensure there are no pending
	 * actions or anything, but it will guarantee that
	 * all actions enqueued before calling this method
	 * have been performed.
	 */
	public void waitForEmpty() {
		if (GUIUtil.isEventDispatchThread()) {
			performPendingActions();
		} else {
			// this can only be done using polling
			// its not very elegant, but who cares.
			while (!isEmpty()) {
				Thread.yield();
				if (!isEmpty()) {
					ThreadCore.sleep(50);
				}
			}
		}
	}

}

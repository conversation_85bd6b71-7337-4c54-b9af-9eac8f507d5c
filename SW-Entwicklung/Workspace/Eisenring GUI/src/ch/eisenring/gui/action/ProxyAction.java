package ch.eisenring.gui.action;

import java.beans.PropertyChangeEvent;
import java.beans.PropertyChangeListener;

/**
 * Base class for forwarding actions
 */
public class ProxyAction extends AbstractAction {

	protected final AbstractAction sourceAction;
	
	private final PropertyChangeListener sourcePropertyListener = new PropertyChangeListener() {
		@Override
		public void propertyChange(final PropertyChangeEvent event) {
			final String propertyName = event.getPropertyName();
			putValue(propertyName, sourceAction.getValue(propertyName));
		}
	};

	public ProxyAction(final AbstractAction sourceAction) {
		this.sourceAction = sourceAction;
		sourceAction.addPropertyChangeListener(sourcePropertyListener);
		// copy properties from source action
		for (final String key : ALL_PROPERTIES) {
			copyProperty(key);
		}
	}

	protected void copyProperty(final String propertyName) {
		final Object value = sourceAction.getValue(propertyName);
		putValue(propertyName, value);
	}

	@Override
	protected boolean isEnabledImpl() {
		return sourceAction.isEnabled();
	}

	@Override
	protected void performAction() {
		sourceAction.performAction();
	}
	
}

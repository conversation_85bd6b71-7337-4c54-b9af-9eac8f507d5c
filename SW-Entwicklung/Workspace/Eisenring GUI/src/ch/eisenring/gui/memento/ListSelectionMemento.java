package ch.eisenring.gui.memento;

import java.awt.Component;

import javax.swing.JList;
import javax.swing.ListModel;

import ch.eisenring.core.collections.Collection;
import ch.eisenring.core.collections.impl.ArrayList;

public final class ListSelectionMemento implements SelectionMemento {

	private final Object[] selectedValues;
	
	public ListSelectionMemento(final JList<?> list) {
		final java.util.Collection<?> selection = list.getSelectedValuesList();
		selectedValues = Collection.toArray(selection, Object.class);
	}

	@Override
	public void applyTo(final Component component) {
		if (component instanceof JList)
			apply((JList<?>) component);
	}
	
	public void apply(final JList<?> list) {
		if (list == null)
			return;
		list.clearSelection();
		if (selectedValues == null)
			return;
		final ListModel<?> model = list.getModel();
		final ArrayList<Integer> indexList = new ArrayList<Integer>(selectedValues.length);
		for (int i=model.getSize()-1; i>=0; --i) {
			final Object object = model.getElementAt(i);
			if (object == null)
				continue;
			for (int j=selectedValues.length-1; j>=0; --j) {
				if (object.equals(selectedValues[j])) {
					indexList.add(Integer.valueOf(i));
				}
			}
		}
		final int[] indices = new int[indexList.size()];
		for (int i=indices.length-1; i>=0; --i)
			indices[i] = indexList.get(i).intValue();
		list.setSelectedIndices(indices);
	}

}

package ch.eisenring.dms.shared.model.data;

import java.io.IOException;

import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.Map;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.collections.impl.HashMap;
import ch.eisenring.core.datatypes.encoded.VarInt;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.Strings;

/**
 * Formats:
 * 
 * Version 1:
 * [1;DEC(long);DEC(int)]
 * 
 * Version 2:
 * [2VAR(long)VAR(int)]
 */
final class PermissionDescriptorEncoding {

	private final static int VX_EXPECT_OPEN = 0;
	private final static int VX_GET_VERSION = 1;
	private final static int V1_EXPECT_COLON = 2;
	private final static int V1_DECODE_ROWID = 3;
	private final static int V1_DECODE_VALUE = 4;
	private final static int V2_DECODE_ROWID = 5;
	private final static int V2_DECODE_VALUE = 6;
	private final static int V2_EXPECT_CLOSE = 7;

	private final static char OPEN = '[';
	private final static char CLOSE = ']';

	public static Map<Long, Integer> parse(final CharSequence descriptor) throws IllegalArgumentException {
		final Map<Long, Integer> map = new HashMap<>();
		if (descriptor == null)
			return map;
		final int length = descriptor.length();
		if (length <= 0)
			return map;
		int state = VX_EXPECT_OPEN;
		long rowId = 0;
		int value = 0;
		for (int i=0; i<length; ++i) {
			final char c = descriptor.charAt(i);
			switch (state) {
				case VX_EXPECT_OPEN: {
					if (c == OPEN) {
						state = VX_GET_VERSION;
						rowId = 0;
						value = 0;
					} else if (!Strings.isWhitespace(c)) {
						throwError(descriptor, i, c, Character.toString(OPEN));
					}
					break;
				}
				case VX_GET_VERSION: {
					if (c == '1') {
						state = V1_EXPECT_COLON;
					} else if (c == '2') {
						state = V2_DECODE_ROWID;
					} else {
						throwError(descriptor, i, c, "1|2");
					}
					break;
				}
				case V1_EXPECT_COLON: {
					if (c == ';') {
						state = V1_DECODE_ROWID;
					} else if (!Strings.isWhitespace(c)) {
						throwError(descriptor, i, c, ";");
					}
					break;
				}
				case V1_DECODE_ROWID: {
					if (Strings.isASCIIDigit(c)) {
						rowId = (rowId * 10) + (c - '0');
					} else if (c == ';') {
						state = V1_DECODE_VALUE;
					} else { 
						throwError(descriptor, i, c, "digit");
					}
					break;
				}
				case V1_DECODE_VALUE: {
					if (Strings.isASCIIDigit(c)) {
						value = (value * 10) + (c - '0');
					} else if (c == CLOSE) {
						map.put(rowId, value);
						state = VX_EXPECT_OPEN;
					} else {
						throwError(descriptor, i, c, "digit");
					}
					break;
				}
				case V2_DECODE_ROWID: {
					final int d = VarInt.decodeDigit(c);
					if (d < 0) {
						throwError(descriptor, i, c, "var-digit");
					} else if (d < 16) {
						// middle digit
						rowId = (rowId << 4) | d;
					} else {
						// last digit
						rowId = (rowId << 4) | (d & 0xF);
						state = V2_DECODE_VALUE;
					}
					break;
				}
				case V2_DECODE_VALUE: {
					final int d = VarInt.decodeDigit(c);
					if (d < 0) {
						throwError(descriptor, i, c, "var-digit");
					} else if (d < 16) {
						// middle digit
						value = (value << 4) | d;
					} else {
						// last digit
						value = (value << 4) | (d & 0xF);
						state = V2_EXPECT_CLOSE;
					}
					break;
				}
				case V2_EXPECT_CLOSE: {
					if (c == CLOSE) {
						map.put(rowId, value);
						state = VX_EXPECT_OPEN;
					} else {
						throwError(descriptor, i, c, Character.toString(CLOSE));
					}
					break;
				}
			}
		}
		return map;
	}

	private static void throwError(final CharSequence descriptor, final int index, final char c, final String expected) throws IllegalArgumentException {
		StringMaker b = StringMaker.obtain();
		b.append("Invalid character \"");
		b.append(c);
		b.append("\" @ ");
		b.append(index);
		if (expected != null) {
			b.append(" (expected \"");
			b.append(expected);
			b.append("\")");
		}
		b.append(" in \"");
		b.append(descriptor);
		b.append("\"");
		throw new IllegalArgumentException(b.release());
	}

	public static void appendTo(final Appendable target, final long rowId, final int value) throws IOException {
		target.append(OPEN);
		target.append('2');
		VarInt.appendTo(target, rowId);
		VarInt.appendTo(target, value);
		target.append(CLOSE);
	}

	public static String encode(final Map<Long, Integer> map, final int version) throws IllegalArgumentException {
		final StringMaker b = StringMaker.obtain(1024);
		final List<Long> rowIdList = new ArrayList<>(map.keySet());
		rowIdList.sort(null);
		final int length = rowIdList.size();
		for (int i=0; i<length; ++i) {
			if (i > 0)
				b.append(' ');
			final Long rowId = rowIdList.get(i);
			final int value;
			{
				Integer v = map.get(rowId);
				value = v == null ? 0 : v.intValue();
			}
			b.append(OPEN);
			switch (version) {
				default:
					throw new IllegalArgumentException(Strings.concat("unknown version: ", version));
				case 1: {
					b.append('1');
					b.append(';');
					b.append(rowId);
					b.append(';');
					b.append(value);
					break;
				}
				case 2: {
					b.append('2');
					try {
						VarInt.appendTo(b, rowId);
						VarInt.appendTo(b, value);
					} catch (final IOException e) {
						throw new RuntimeException(e.getMessage(), e);
					}
					break;
				}
			}
			b.append(CLOSE);
		}
		return b.toString();
	}

}

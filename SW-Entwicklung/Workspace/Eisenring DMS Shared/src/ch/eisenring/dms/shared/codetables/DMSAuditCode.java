package ch.eisenring.dms.shared.codetables;

import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.codetype.StaticCode;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.datatypes.strings.format.api.Formats;
import ch.eisenring.dms.service.codetables.DMSIconCode;
import ch.eisenring.dms.service.codetables.DMSObjectStatus;
import ch.eisenring.dms.service.codetables.DMSPropertyCode;
import ch.eisenring.dms.shared.model.api.DMSAuditTrail;

/**
 * Audit code is used to indicate the type of action
 * the audit entry was created for.
 */
public abstract class DMSAuditCode extends StaticCode {

	public final static DMSAuditCode RENAME = new DMSAuditCode(
			1, "Umbenannt") {
		@Override
		public String getDescription(final DMSAuditTrail auditEntry) {
			final StringMaker b = StringMaker.obtain();
			b.append("Umbenannt von \"");
			b.append(auditEntry.getValueOld(), Formats.TRIM);
			b.append("\" nach \"");
			b.append(auditEntry.getValueNew(), Formats.TRIM);
			b.append('"');
			return b.release();
		}
	};
	
	public final static DMSAuditCode KEYWORDS = new DMSAuditCode(
			2, "Schlüsselwörter") {
		@Override
		public String getDescription(final DMSAuditTrail auditEntry) {
			final StringMaker b = StringMaker.obtain(256);
			b.append("Schlüsselwörter geändert von \"");
			b.append(Strings.toSingleLine(Strings.trim(auditEntry.getValueOld())));
			b.append("\" nach \"");
			b.append(Strings.toSingleLine(Strings.trim(auditEntry.getValueNew())));
			b.append('"');
			return b.release();
		}
	};

	public final static DMSAuditCode CREATE = new DMSAuditCode(
			3, "Erstellt") {
		@Override
		public String getDescription(final DMSAuditTrail auditEntry) {
			return getLongText();
		}
	};

	public final static DMSAuditCode STATUS = new DMSAuditCode(
			4, "Archiviert") {
		public String getDescription(final DMSAuditTrail auditEntry) {
			try {
				final AbstractCode stOld = auditEntry.getCodeOld(DMSObjectStatus.class);
				final AbstractCode stNew = auditEntry.getCodeNew(DMSObjectStatus.class);
				return Strings.concat("Status geändert von ", stOld, " zu ", stNew);
			} catch (final Exception e) {
				return Strings.concat("Fehler: ", e.getMessage());
			}
		}
	};
	
	public final static DMSAuditCode ICON = new DMSAuditCode(
			5, "Symbol") {
		public String getDescription(final DMSAuditTrail auditEntry) {
			try {
				final AbstractCode stOld = auditEntry.getCodeOld(DMSIconCode.class);
				final AbstractCode stNew = auditEntry.getCodeNew(DMSIconCode.class);
				return Strings.concat("Symbol geändert von ", stOld, " zu ", stNew);
			} catch (final Exception e) {
				return Strings.concat("Fehler: ", e.getMessage());
			}
		}
	};

	public final static DMSAuditCode PROPERTY_CHANGE = new DMSAuditCode(
			6, "Eigenschaft") {
		public String getDescription(final DMSAuditTrail auditEntry) {
			try {
				final DMSPropertyCode code = auditEntry.getCodeOld(DMSPropertyCode.class);
				final String value = code.getDisplayValue(auditEntry.getValueNew()).toString();
				return Strings.concat("Eigenschaft \"", code, "\" auf Wert \"", value, "\" gesetzt");
			} catch (final Exception e) {
				return Strings.concat("Fehler: ", e.getMessage());
			}
		}
	};

	public final static DMSAuditCode PROPERTY_ADD = new DMSAuditCode(
			7, "Eigenschaft") {
		public String getDescription(final DMSAuditTrail auditEntry) {
			try {
				final DMSPropertyCode code = auditEntry.getCodeOld(DMSPropertyCode.class);
				final String value = code.getDisplayValue(auditEntry.getValueNew()).toString();
				return Strings.concat("Eigenschaft \"", code, "\", Wert \"", value, "\" hinzugefügt");
			} catch (final Exception e) {
				return Strings.concat("Fehler: ", e.getMessage());
			}
		}
	};

	public final static DMSAuditCode PROPERTY_REMOVE = new DMSAuditCode(
			8, "Eigenschaft") {
		public String getDescription(final DMSAuditTrail auditEntry) {
			try {
				final DMSPropertyCode code = auditEntry.getCodeOld(DMSPropertyCode.class);
				final String value = code.getDisplayValue(auditEntry.getValueNew()).toString();
				return Strings.concat("Eigenschaft \"", code, "\", Wert \"", value, "\" entfernt");
			} catch (final Exception e) {
				return Strings.concat("Fehler: ", e.getMessage());
			}
		}
	};

	public final static DMSAuditCode VERSION_CREATE = new DMSAuditCode(
			9, "Neue Version") {
		public String getDescription(final DMSAuditTrail auditEntry) {
			return Strings.concat("Neue Version Id ", auditEntry.getValueNew(), " erstellt");
		}
	};

	public final static DMSAuditCode VERSION_ACTIVATE = new DMSAuditCode(
			10, "Aktive Version") {
		public String getDescription(final DMSAuditTrail auditEntry) {
			return Strings.concat("Aktive Version festgelegt auf Version-Id ", auditEntry.getValueNew());
		}
	};

	public final static DMSAuditCode OBTAIN_LOCK = new DMSAuditCode(
			11, "Lock") {
		public String getDescription(final DMSAuditTrail auditEntry) {
			return "Zum Bearbeiten gesperrt";
		}
	};

	public final static DMSAuditCode RELEASE_LOCK = new DMSAuditCode(
			12, "Unlock") {
		public String getDescription(final DMSAuditTrail auditEntry) {
			return "Bearbeitungssperre aufgehoben";
		}
	};

	public final static DMSAuditCode MOVE_OBJECT = new DMSAuditCode(
			13, "Verschoben") {
		@Override
		public String getDescription(final DMSAuditTrail auditEntry) {
			return Strings.concat("Verschoben aus Ordner Id ", auditEntry.getValueOld(), " in Ordner Id ", auditEntry.getValueNew());
		}
	};

	public final static DMSAuditCode PARTICIPANTS = new DMSAuditCode(
			14, "Involvierte Personen") {
		@Override
		public String getDescription(final DMSAuditTrail auditEntry) {
			String valueOld = Strings.toString(auditEntry.getValueOld());
			String valueNew = Strings.toString(auditEntry.getValueNew());
			if (valueOld == null)
				valueOld = "Keine";
			if (valueNew == null)
				valueNew = "Keine";
			return Strings.concat("Involvierte Personen geändert von \"", valueOld, "\" zu  \"", valueNew, "\"");
		}
	};

	public final static DMSAuditCode PERMISSIONS = new DMSAuditCode(
			15, "Berechtigungen") {
		@Override
		public String getDescription(final DMSAuditTrail auditEntry) {
			String valueOld = Strings.toString(auditEntry.getValueOld());
			String valueNew = Strings.toString(auditEntry.getValueNew());
			if (valueOld == null)
				valueOld = "<Geerbt>";
			if (valueNew == null)
				valueNew = "<Geerbt>";
			return Strings.concat("Berechtigungen geändert von \"", valueOld, "\" zu \"", valueNew, "\"");
		}
	};

	public final static DMSAuditCode IMAGE_SCALED = new DMSAuditCode(
			16, "Bildgrössen-Reduktion") {
		@Override
		public String getDescription(final DMSAuditTrail auditEntry) {
			try {
				final StringMaker b = StringMaker.obtain();
				final long sizeOld = Strings.parseInt(auditEntry.getValueOld());
				final long sizeNew = Strings.parseInt(auditEntry.getValueNew());
				b.append("Bildgrösse reduziert von ");
				b.append(sizeOld, Formats.BYTESIZE);
				b.append(" auf ");
				b.append(sizeNew, Formats.BYTESIZE);
				return b.release();
			} catch (final Exception e) {
				return "Bildgrösse reduziert";
			}
		}
	};

	// --------------------------------------------------------------
	// ---
	// --- Basis Implementation
	// ---
	// --------------------------------------------------------------
	private DMSAuditCode(final int id, final String text) {
		super(id, Short.valueOf((short) id), text, text);
	}

	@Override
	public final Class<? extends AbstractCode> getTypeClass() {
		return DMSAuditCode.class;
	}

	/**
	 * Gets the description for audit entry.
	 * The description will depend code and value old/new.
	 */
	public abstract String getDescription(final DMSAuditTrail auditEntry);

	/**
	 * Converts object to DB value for old value.
	 */
	public String toDBValueOld(final Object value) {
		return toDBValue(value);
	}
	
	/**
	 * Converts object to DB value for new value.
	 */
	public String toDBValueNew(final Object value) {
		return toDBValue(value);
	}

	protected String toDBValue(final Object value) {
		if (value == null) {
			return null;
		} else if (value instanceof AbstractCode) {
			final Object key = AbstractCode.getKey((AbstractCode) value, null);
			return key == null ? null : key.toString();
		} else {
			return value.toString();
		}
	}

}

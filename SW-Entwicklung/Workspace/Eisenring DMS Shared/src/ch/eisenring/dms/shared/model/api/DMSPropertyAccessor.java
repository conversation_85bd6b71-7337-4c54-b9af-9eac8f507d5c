package ch.eisenring.dms.shared.model.api;

import ch.eisenring.core.collections.List;
import ch.eisenring.core.datatypes.primitives.Primitives;
import ch.eisenring.dms.service.api.DMSObject;
import ch.eisenring.dms.service.codetables.DMSPropertyCode;
import ch.eisenring.dms.shared.model.pojo.DMSPropertyAccessorPojo;

/**
 * Interface exposing access to an objects properties.
 * 
 * Also acts as a read-only DMSObject.
 */
public interface DMSPropertyAccessor extends DMSObject {

	public final static DMSPropertyAccessor[] EMPTY_ARRAY =
			Primitives.newArray(DMSPropertyAccessor.class, 0);

	public static enum Mode {
		/**
		 * Means only attributes directly belonging to object
		 */
		DIRECT,
		/**
		 * Means attributes belonging to object or inherited from parent
		 */
		REGULAR,
		/**
		 * All properties from entire object hierarchy, even those
		 * that are not applicable for the actual object.
		 */
		ALL
	};

	// --------------------------------------------------------------
	// ---
	// --- Factory methods
	// ---
	// --------------------------------------------------------------
	public static DMSPropertyAccessor create(final DMSObject sourceObject,
			final java.util.Collection<? extends DMSProperty> properties) {
		return DMSPropertyAccessorPojo.create(sourceObject, properties);
	}

	// --------------------------------------------------------------
	// ---
	// --- API Definition
	// ---
	// --------------------------------------------------------------
	/**
	 * Gets the DMSObject this accessor represents
	 */
	public DMSObject getObject();
	
	/**
	 * Gets the properties for the object that satisfy the mode condition.
	 */
	public List<DMSProperty> getProperties(final Mode mode);

	/**
	 * Gets the property specified by code (and mode).
	 * Returns NULL if no matching property exists.
	 */
	public DMSProperty getProperty(final DMSPropertyCode type, final Mode mode);

	/**
	 * Equivalent to getProperty(type, Mode.REGULAR);
	 */
	public default DMSProperty getProperty(final DMSPropertyCode type) {
		return getProperty(type, Mode.REGULAR);
	}

	/**
	 * Gets the raw value of the property specified by code (and mode).
	 * Returns NULL if no matching property exists.
	 */
	public default Object getPropertyValue(final DMSPropertyCode type, final Mode mode) {
		final DMSProperty property = getProperty(type, mode);
		return property == null ? null
				: property.getPropertyCode().getObjectValue(property.getPropertyValue());
	}

	/**
	 * Equivalent to getPropertyValue(type, Mode.REGULAR);
	 */
	public default Object getPropertyValue(final DMSPropertyCode type) {
		return getPropertyValue(type, Mode.REGULAR);
	}

	/**
	 * Checks if property applies to this accessors object
	 */
	public default boolean isApplicable(final DMSProperty property, final Mode mode) {
		if (property == null)
			return false;
		final DMSPropertyCode code = property.getPropertyCode();
		if (code == null)
			return false;
		final DMSObject object = getObject();
		switch (mode) {
			case ALL:
				return true;
			case REGULAR:
				return code.isApplicable(object.getType()) &&
						(property.getObjectRowId() == object.getPKValue() || code.isInheritable());
			case DIRECT:
				return property.getObjectRowId() == object.getPKValue();
			default:
				throw new IllegalArgumentException("invalid mode: " + mode);
		}
	}

}


package ch.eisenring.dms.shared.model.data;

import ch.eisenring.app.shared.ServiceNotFoundException;
import ch.eisenring.app.shared.SoftwareComponent;
import ch.eisenring.core.collections.Collection;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.Map;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.collections.impl.HashMap;
import ch.eisenring.core.datatypes.primitives.LongKey;
import ch.eisenring.dms.service.codetables.DMSPermissionCode;
import ch.eisenring.user.shared.service.RoleFacade;
import ch.eisenring.user.shared.service.USRService;
import ch.eisenring.user.shared.service.UserFacade;

public abstract class PermissionCache {

	protected final SoftwareComponent softwareComponent;
	private final Map<LongKey, PermissionDescriptor> PERMISSION_MAP = new HashMap<>();

	public final Object getLock() {
		return this;
	}

	public PermissionCache(final SoftwareComponent softwareComponent) {
		this.softwareComponent = softwareComponent;
	}

	/**
	 * Replaces the entire permission set in the cache 
	 */
	public final void setPermissions(final Collection<PermissionDescriptor> descriptors) {
		synchronized (getLock()) {
			PERMISSION_MAP.clear();
			for (final PermissionDescriptor descriptor : descriptors) {
				LongKey.put(PERMISSION_MAP, descriptor.getRowId(), descriptor);
			}
		}
		permissionsChanged();
	}

	/**
	 * Removes a permission from the cache.
	 */
	public final boolean removePermission(final PermissionDescriptor descriptor) {
		boolean result = false;
		if (descriptor == null)
			return false;
		synchronized (getLock()) {
			result = LongKey.remove(PERMISSION_MAP, descriptor.getRowId()) != null;
		}
		if (result)
			permissionsChanged();
		return result;
	}

	/**
	 * Adds a permission to the cache.
	 */
	public final boolean addPermission(final PermissionDescriptor descriptor) {
		if (descriptor == null)
			return false;
		synchronized (getLock()) {
			LongKey.put(PERMISSION_MAP, descriptor.getRowId(), descriptor);
		}
		permissionsChanged();
		return true;
	}

	/**
	 * Gets a list of all permissions
	 */
	public List<PermissionDescriptor> getAllPermissions(final boolean publicOnly) {
		final List<PermissionDescriptor> result;
		synchronized (getLock()) {
			result = new ArrayList<PermissionDescriptor>(PERMISSION_MAP.values());
		}
		if (publicOnly) {
			// remove all private permission descriptors
			for (int i=result.size()-1; i>=0; --i) {
				final PermissionDescriptor descriptor = result.get(i);
				if (descriptor.getPrivateFlag() != 0)
					result.remove(i);
			}
		}
		return result;
	}

	protected final void permissionsChanged() {
		// invalidate cached permissions
		flush();
		// notify overrides of change
		permissionsChangedImpl();
	}

	/**
	 * This hook is called when the permissions have changed.
	 */
	protected abstract void permissionsChangedImpl();

	/**
	 * Gets permission by rowId 
	 */
	public PermissionDescriptor getByRowId(final Long rowId) {
		if (rowId == null)
			return PermissionDescriptor.DEFAULT_PERMISSION;
		PermissionDescriptor result;
		synchronized (getLock()) {
			result = LongKey.get(PERMISSION_MAP, rowId);
		}
		return result == null ? PermissionDescriptor.MISSING_PERMISSION : result;
	}

	// --------------------------------------------------------------
	// ---
	// --- Permission evaluation cache
	// ---
	// --------------------------------------------------------------
	private final Map<PermissionCacheKey, Boolean> cacheMap = new HashMap<>(64);
	
	private final PermissionCacheKey lookupKey = new PermissionCacheKey(0L, 0, 0L);

	/**
	 * Evaluates permission for user (with caching of results)
	 */
	public final boolean isPermitted(final Long userRowId, final DMSPermissionCode permissionCode, final Long descriptorRowId) {
		// first, check the special cases
		if (permissionCode == null)
			return false;
		if (descriptorRowId == null)
			return true;

		Boolean result;
		synchronized (getLock()) {
			lookupKey.initialize(userRowId, permissionCode.getId(), descriptorRowId.longValue());
			result = cacheMap.get(lookupKey);
		}
		if (result != null)
			return result.booleanValue();
		// no entry in cache, check it the complicated way
		final PermissionDescriptor descriptor = getByRowId(descriptorRowId);
		do {
			// check if the permission is allowed for everybody
			// (this can bypass the more complicated tests in many cases)
			if (descriptor.isPermitted(permissionCode, PermissionDescriptor.EVERYBODY_ROWID)) {
				result = Boolean.TRUE;
				break;
			}

			// check if descriptor mentions the user directly
			if (descriptor.isPermitted(permissionCode, userRowId)) {
				result = Boolean.TRUE;
				break;
			}

			// the quick methods didn't turn up a result, now check
			// back with the user system.
			USRService userService;
			try {
				userService = softwareComponent.locateService(USRService.class);
			} catch (final ServiceNotFoundException e) {
				// user service not (yet?) available, deny permission
				break;
			}

			final UserFacade userFacade = userService.getUser(userRowId);
			if (userFacade == null)
				break;
			final List<RoleFacade> roles = userService.getRoles(userFacade);
			// check if any of the roles of the user have the permission
			for (final RoleFacade role : roles) {
				if (descriptor.isPermitted(permissionCode, role.getRowId())) {
					result = Boolean.TRUE;
					break;
				}
			}
		} while (false);
		
		// if we haven't found the permission until here, deny it
		if (result == null)
			result = Boolean.FALSE;

		// put the evaluation result into the cache for future use
		final PermissionCacheKey key = new PermissionCacheKey(userRowId, permissionCode.getId(), descriptorRowId);
		synchronized (getLock()) {
			// since we did not keep the cache locked while evaluating
			// the permission, another thread may already evaluated it.
			//
			// we do not take special care about double concurrent evaluation,
			// both should have evaluated the same result anyway, the only
			// effect be a little waste of CPU.
			cacheMap.put(key, result);
		}

		return result.booleanValue();
	}

	/**
	 * Flushes all cached permission evaluation data
	 */
	public void flush() {
		synchronized (getLock()) {
			cacheMap.clear();
		}
	}

}

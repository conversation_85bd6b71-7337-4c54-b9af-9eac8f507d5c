package ch.eisenring.dms.shared.model.api;

import java.awt.datatransfer.DataFlavor;
import java.io.IOException;
import java.io.Serializable;

import ch.eisenring.core.collections.Collection;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.io.StreamReader;
import ch.eisenring.core.io.StreamWriter;
import ch.eisenring.core.io.Streamable;
import ch.eisenring.dms.service.api.DMSObject;
import ch.eisenring.dms.service.codetables.DMSObjectType;
import ch.eisenring.dms.shared.codetables.SafetyQuestionCode;

public class VersionIdentifierList extends ArrayList<VersionIdentifier>
	implements Streamable, Serializable {

	private static final long serialVersionUID = 1L;

	public final static DataFlavor DATAFLAVOR = new DataFlavor(VersionIdentifierList.class, "DMSObjectReferenceCollection");
	
	// --------------------------------------------------------------
	// ---
	// --- Constructors
	// ---
	// --------------------------------------------------------------
	public VersionIdentifierList() {
		this(16);
	}
	
	public VersionIdentifierList(final int initialCapacity) {
		super(initialCapacity);
	}
	
	public VersionIdentifierList(final Collection<VersionIdentifier> collection) {
		super(collection);
	}

	// --------------------------------------------------------------
	// ---
	// --- Special API
	// ---
	// --------------------------------------------------------------
	public boolean add(final DMSObject object) {
		if (object == null)
			return false;
		final VersionIdentifier identifier = new VersionIdentifier(object, (Long) null);
		return contains(identifier) ? false : add(identifier);
	}

	// --------------------------------------------------------------
	// ---
	// --- List API
	// ---
	// --------------------------------------------------------------
	/**
	 * Gets the number of items of type
	 */
	public int size(final DMSObjectType type) {
		if (type == null) {
			return size();
		} else {
			int size = 0;
			for (int i=size()-1; i>=0; --i) {
				final VersionIdentifier v = get(i);
				if (type.equals(v.getType())) {
					++size;
				}
			}
			return size;
		}
	}

	public boolean requiresSafetyQuestion(final SafetyQuestionCode code) {
		if (SafetyQuestionCode.NEVER.equals(code)) {
			return false;
		} else if (SafetyQuestionCode.ALWAYS.equals(code)) {
			return true;
		} else {
			if (isEmpty()) {
				return false;
			} else if (size() > 1) {
				return true;
			} else {
				final VersionIdentifier i = get(0);
				return !DMSObjectType.FILE.equals(i.getType());
			}
		}
	}
	
	// --------------------------------------------------------------
	// ---
	// --- Get the object id's
	// ---
	// --------------------------------------------------------------
	public List<Long> getObjectIdList() {
		final List<Long> result = new ArrayList<Long>(size());
		for (final VersionIdentifier identifier : this) {
			final Long id = identifier.getObjectRowId();
			if (!result.contains(id)) {
				result.add(id);
			}
		}
		return result;
	}
	
	// --------------------------------------------------------------
	// ---
	// --- Streamable implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public void read(final StreamReader reader) throws IOException {
		final int size = reader.readInt();
		clear();
		ensureCapacity(size);
		for (int i=0; i<size; ++i) {
			final VersionIdentifier v = (VersionIdentifier) reader.readObject();
			add(v);
		}
	}
	
	@Override
	public void write(final StreamWriter writer) throws IOException {
		final int size = size();
		writer.writeInt(size);
		for (int i=0; i<size; ++i) {
			final VersionIdentifier v = get(i);
			writer.writeObject(v);
		}
	}

}

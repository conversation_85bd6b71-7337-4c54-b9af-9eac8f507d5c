package ch.eisenring.dms.shared.model.pojo;

import java.io.IOException;

import ch.eisenring.core.HashUtil;
import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.StringMakerFriendly;
import ch.eisenring.core.datatypes.strings.format.api.Formats;
import ch.eisenring.core.io.StreamReader;
import ch.eisenring.core.io.StreamWriter;
import ch.eisenring.core.io.Streamable;
import ch.eisenring.dms.service.StringCaches;
import ch.eisenring.dms.service.codetables.DMSBinaryStatus;
import ch.eisenring.dms.shared.codetables.DMSCompressionCode;
import ch.eisenring.dms.shared.model.api.DMSVersion;

/**
 * Read-only representation of document version
 */
public class DMSVersionPojo implements DMSVersion, Streamable, StringMakerFriendly {

	protected long rowId;
	protected long objectRowId;
	protected long byteSize;
	protected long rawSize;
	protected long madeOn = TimestampUtil.NULL_TIMESTAMP;
	protected DMSCompressionCode dcmCode;
	protected DMSBinaryStatus statusCode;
	protected String fileExtension; 
	protected String binaryHash;

	// --------------------------------------------------------------
	// ---
	// --- Creation
	// ---
	// --------------------------------------------------------------
	DMSVersionPojo() {
	}

	DMSVersionPojo(final DMSVersion source) {
		this.rowId = source.getPKValue();
		this.binaryHash = source.getBinaryhash();
		this.byteSize = source.getBytesize();
		this.dcmCode = source.getDCMCode();
		this.fileExtension = source.getFileExtension();
		this.madeOn = source.getMadeOn();
		this.objectRowId = source.getObjectRowId();
		this.rawSize = source.getRawsize();
		this.statusCode = source.getStatusCode();
	}

	/**
	 * Converts DMSBinaryInterface to DMSBinaryReadOnly (which implements
	 * DMSBinaryInterface as well). If the argument instance is already
	 * a DMSBinaryReadOnly, just casts the argument.
	 */
	public static DMSVersionPojo create(final DMSVersion source) {
		if (source == null) {
			return null;
		} else if (source instanceof DMSVersionPojo) {
			return (DMSVersionPojo) source;
		} else {
			return new DMSVersionPojo(source);
		}
	}

	// --------------------------------------------------------------
	// ---
	// --- DMSBinaryInterface implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public final long getPKValue() {
		return rowId;
	}

	@Override
	public final long getObjectRowId() {
		return objectRowId;
	}

	@Override
	public final long getBytesize() {
		return byteSize;
	}

	@Override
	public final long getRawsize() {
		return rawSize;
	}

	@Override
	public final DMSCompressionCode getDCMCode() {
		return dcmCode;
	}

	@Override
	public final DMSBinaryStatus getStatusCode() {
		return statusCode;
	}

	public final void setStatusCode(final DMSBinaryStatus statusCode) {
		this.statusCode = statusCode;
	}

	@Override
	public final String getFileExtension() {
		return fileExtension;
	}

	@Override
	public long getMadeOn() {
		return madeOn;
	}

	@Override
	public final String getBinaryhash() {
		return binaryHash;
	}

	// --------------------------------------------------------------
	// ---
	// --- Streamable implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public void read(final StreamReader reader) throws IOException {
		rowId = reader.readLong();
		objectRowId = reader.readLong();
		byteSize = reader.readLong();
		rawSize = reader.readLong();
		madeOn = reader.readLong();
		dcmCode = reader.readCode(DMSCompressionCode.class);
		statusCode = reader.readCode(DMSBinaryStatus.class);
		fileExtension = reader.readString(StringCaches.FILEEXT); 
		binaryHash = reader.readString();
	}

	@Override
	public void write(final StreamWriter writer) throws IOException {
		writer.writeLong(rowId);
		writer.writeLong(objectRowId);
		writer.writeLong(byteSize);
		writer.writeLong(rawSize);
		writer.writeLong(madeOn);
		writer.writeCode(dcmCode);
		writer.writeCode(statusCode);
		writer.writeString(fileExtension, StringCaches.FILEEXT); 
		writer.writeString(binaryHash);
	}

	// --------------------------------------------------------------
	// ---
	// --- Object overrides
	// ---
	// --------------------------------------------------------------
	@Override
	public final int hashCode() {
		return HashUtil.hashCode(rowId);
	}

	@Override
	public boolean equals(final Object o) {
		return o instanceof DMSVersionPojo
			&& ((DMSVersionPojo) o).rowId == rowId
			&& getClass() == o.getClass();
	}

	public String toString() {
		return StringMakerFriendly.toString(this);
	}

	@Override
	public void toStringMaker(final StringMaker target) {
		target.append(getClass(), Formats.SIMPLE_CLASSNAME);
		target.append('[');
		target.append(rowId);
		target.append(']');
	}

}

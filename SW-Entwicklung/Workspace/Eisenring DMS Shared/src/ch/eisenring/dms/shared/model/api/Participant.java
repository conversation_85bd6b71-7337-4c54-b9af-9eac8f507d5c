package ch.eisenring.dms.shared.model.api;

import java.io.IOException;
import java.util.Collections;
import java.util.Locale;

import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.collections.Collection;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.io.StreamReader;
import ch.eisenring.core.io.StreamWriter;
import ch.eisenring.core.io.Streamable;
import ch.eisenring.core.sort.Comparator;
import ch.eisenring.dms.shared.codetables.ParticipantRole;

public final class Participant implements Streamable {

	final static char PREFIX = '[';
	final static char SEPARATOR = ';';
	final static char POSTFIX = ']';

	public final static Comparator<Participant> DEFAULT_ORDER = new Comparator<Participant>() {
		@Override
		public int compare(final Participant p1, final Participant p2) {
			{
				final String n1 = p1.getParticipant();
				final String n2 = p2.getParticipant();
				final int r = n1.compareTo(n2);
				if (r != 0)
					return r;
			}{
				final int r1 = AbstractCode.getId(p1.getRole(), 0);
				final int r2 = AbstractCode.getId(p2.getRole(), 0);
				return r1 < r2 ? -1 : (r1 > r2 ? 1 : 0);
			}
		}
	};

	private CharSequence participant;
	private ParticipantRole role;

	public Participant(final CharSequence participant, final ParticipantRole role) {
		this();
		this.participant = Strings.toLower(participant);
		this.role = role == null ? ParticipantRole.DEFAULT : role;
	}

	public String getParticipant() {
		return Strings.toString(participant);
	}

	public ParticipantRole getRole() {
		return role;
	}

	// --------------------------------------------------------------
	// ---
	// --- Streamable implementation
	// ---
	// --------------------------------------------------------------
	private Participant() {
		// default constructor used by Streamable
	}

	@Override
	public void read(final StreamReader reader) throws IOException {
		participant = reader.readString();
		role = reader.readCode(ParticipantRole.class);
	}

	@Override
	public void write(final StreamWriter writer) throws IOException {
		writer.writeString(getParticipant());
		writer.writeCode(role);
	}

	public String toCanonical() {
		return PREFIX + getParticipant() + SEPARATOR + role.getId() + POSTFIX;
	}

	// --------------------------------------------------------------
	// ---
	// --- Object overrides
	// ---
	// --------------------------------------------------------------
	@Override
	public String toString() {
		return Strings.concat(getParticipant(), "[", role, "]");
	}

	@Override
	public int hashCode() {
		return Strings.hashCode(participant);
	}

	@Override
	public boolean equals(final Object o) {
		if (o instanceof Participant) {
			final Participant p = (Participant) o;
			if (!Strings.equals(p.participant, participant))
				return false;
			return role.equals(p.role);
		}
		return false;
	}

	// --------------------------------------------------------------
	// ---
	// --- Static utilities
	// ---
	// --------------------------------------------------------------
	public static List<Participant> fromCanonical(final String canonical) {
		if (canonical == null)
			return new ArrayList<Participant>();
		final List<Participant> result = new ArrayList<Participant>(canonical.length() >> 3);
		final StringMaker b = StringMaker.obtain();
		int i = 0;
		int state = 0;
		ParticipantRole role = null;
		String name = null;
		while (i < canonical.length()) {
			final char c = canonical.charAt(i++);
			switch (state) {
				default:
					throw new IllegalStateException();
				case 0:
					if (c == PREFIX) {
						b.setLength(0);
						++state;
						break;
					}
					throw new IllegalArgumentException();
				case 1:
					if (c == SEPARATOR || c == POSTFIX) {
						if (b.length() <= 0)
							throw new IllegalArgumentException();
						name = b.toString();
						b.setLength(0);
						if (c == SEPARATOR) {
							++state;
						} else {
							final Participant participant = new Participant(name, role);
							result.add(participant);
							state = 0;
						}
					} else {
						b.append(c);
					}
					break;
				case 2:
					if (c == POSTFIX) {
						try {
							int id = Strings.parseInt(b);
							role = (ParticipantRole) AbstractCode.getById(id, ParticipantRole.class);
							if (AbstractCode.isNull(role))
								throw new IllegalArgumentException();
							b.setLength(0);
							final Participant participant = new Participant(name, role);
							result.add(participant);
							state = 0;
							break;
						} catch (final Exception e) {
							throw new IllegalArgumentException(e);
						}
					} else {
						b.append(c);
					}
					break;
			}
		}
		b.releaseSilent();
		if (state != 0)
			throw new IllegalArgumentException();
		return result;
	}

	public static String toCanonical(final Collection<Participant> participants) {
		if (participants == null || participants.isEmpty())
			return null;
		final StringMaker b = StringMaker.obtain(128);
		final List<Participant> list = new ArrayList<Participant>(participants);
		Collections.sort(list, Participant.DEFAULT_ORDER);
		for (final Participant participant : list) {
			if (participant != null)
				b.append(participant.toCanonical());
		}
		return b.release((String) null);
	}

	/**
	 * Checks if this participant can be edited by the user.
	 * Participants with system provided roles can not be edited
	 * by the user. 
	 */
	public boolean isEditable() {
		return ParticipantRole.DEFAULT.equals(getRole());
	}

	public static boolean isVisibleFor(final String canonical, final String participant) {
		if (canonical == null || participant == null ||	participant.length() <= 0)
			return false;
		return Strings.indexOf(canonical, '*') >= 0
				|| containsImpl(canonical, participant);
	}

	public static boolean contains(final String canonical, final String participant) {
		if (canonical == null || participant == null ||	participant.length() <= 0)
			return false;
		return containsImpl(canonical, participant);
	}

	private static boolean containsImpl(final String canonical, final String participant) {
		final StringMaker b = StringMaker.obtain();
		b.append(PREFIX);
		b.append(Strings.toLower(participant));
		b.append(SEPARATOR);
		final int i = Strings.indexOf(canonical, b);
		b.releaseSilent();
		return i >= 0;
	}

	public static boolean isInvolved(final String canonical, final String participant) {
		if (canonical == null)
			return true;
		return contains(canonical, participant);
	}

	public static ParticipantRole getRole(final String canonical, final String participant) {
		if (canonical == null ||
				participant == null ||
				participant.length() <= 0)
			return ParticipantRole.NULL;
		final String lower = SEPARATOR + participant.toLowerCase(Locale.GERMAN) + POSTFIX;
		final int e = canonical.indexOf(lower);
		if (e <= 0)
			return ParticipantRole.NULL;
		int s = e;
		while (s >= 0) {
			if (canonical.charAt(s) == PREFIX)
				break;
			--s;
		}
		int id;
		try {
			id = Strings.parseInt(canonical, s + 1, e);
		} catch (final Exception ex) {
			return ParticipantRole.NULL;
		}
		return (ParticipantRole) AbstractCode.getById(id, ParticipantRole.class);
	}

	// --------------------------------------------------------------
	// ---
	// --- Sorting
	// ---
	// --------------------------------------------------------------
	public static class Order {
		public final static Comparator<Participant> Standard = Comparator.wrapNullSafe(
				new Comparator<Participant>() {
			@Override
			public int compare(final Participant p1, final Participant p2) {
				int r = Strings.compareIgnoreCase(p1.getParticipant(), p2.getParticipant());
				if (r != 0)
					return r;
				return compareSigned(AbstractCode.getId(p1.getRole(), 0), AbstractCode.getId(p2.getRole(), 0));
			}
		});

		public final static Comparator<Participant> User = Comparator.wrapNullSafe(
				new Comparator<Participant>() {
			@Override
			public int compare(final Participant p1, final Participant p2) {
				int r = Strings.compareIgnoreCase(p1.getParticipant(), p2.getParticipant());
				if (r != 0)
					return r;
				return compareSigned(AbstractCode.getId(p1.getRole(), 0), AbstractCode.getId(p2.getRole(), 0));
			}
		});

		public final static Comparator<Participant> Role = Comparator.wrapNullSafe(
				new Comparator<Participant>() {
			@Override
			public int compare(final Participant p1, final Participant p2) {
				int r = compareSigned(AbstractCode.getId(p1.getRole(), 0), AbstractCode.getId(p2.getRole(), 0));
				if (r != 0)
					return r;
				return Strings.compareIgnoreCase(p1.getParticipant(), p2.getParticipant());
			}
		});
	}

}

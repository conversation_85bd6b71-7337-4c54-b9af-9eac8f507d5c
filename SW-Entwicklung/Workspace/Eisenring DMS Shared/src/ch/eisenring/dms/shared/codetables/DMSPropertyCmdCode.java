package ch.eisenring.dms.shared.codetables;

import ch.eisenring.core.codetype.StaticCode;

/**
 * Command codes for property alterations 
 */
public final class DMSPropertyCmdCode extends StaticCode {

	public final static DMSPropertyCmdCode INSERT = new DMSPropertyCmdCode(1, "Insert");
	public final static DMSPropertyCmdCode UPDATE = new DMSPropertyCmdCode(2, "Update");
	public final static DMSPropertyCmdCode DELETE = new DMSPropertyCmdCode(3, "Delete");
	public final static DMSPropertyCmdCode INORUP = new DMSPropertyCmdCode(4, "InsertOrUpdate");
	
	protected DMSPropertyCmdCode(final int id, final String text) {
		super(id, Integer.valueOf(id), text, text);
	}

}

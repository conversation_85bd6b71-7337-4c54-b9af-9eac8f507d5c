package ch.eisenring.dms.shared;

import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.util.CharacterFilter;

public final class ObjectnameCharacterFilter implements CharacterFilter {

	public final static CharacterFilter INSTANCE = new ObjectnameCharacterFilter();
	
	protected ObjectnameCharacterFilter() {
	}

	@Override
	public boolean accepts(final char c) {
		if (c < 32) {
			return false;
		} else if (c > 255) {
			return false;
		} else if (c >= 127 && c < 160) {
			return false;
		}
		switch (c) {
			default:
				return true;
			case '*':
			case '?':
			case '/':
			case '"':
			case ':':
			case '|':
			case '<':
			case '>':
			case '\\':
				return false;
		}
	}
	
	@Override
	public CharSequence convert(final char c) {
		// no conversion by this filter
		return Strings.toString(c);
	}

}

package ch.eisenring.dms.shared.model.data;

import ch.eisenring.dms.service.codetables.DMSPermissionCode;

final class PermissionCacheKey {

	/**
	 * Precalculated hash code of this key
	 */
	protected int hashCode;
	
	/**
	 * The permission code this key is for
	 */
	protected int permissionCodeId;
	
	/**
	 * The permission descriptor this key is for
	 */
	protected long descriptorRowId;
	
	/**
	 * The user this key is for
	 */
	protected long userRowId;

	PermissionCacheKey(final Long user,
			           final DMSPermissionCode permissionCode,
			           final Long descriptorRowId) {
		this(user, permissionCode.getId(), descriptorRowId.longValue());
	}

	PermissionCacheKey(final Long user,
			           final int permissionCodeId,
			           final long descriptorRowId) {
		initialize(user, permissionCodeId, descriptorRowId);
	}

	void initialize(final Long user,
                    final int permissionCodeId,
                    final long descriptorRowId) {
		this.userRowId = user == null ? 0 : user.longValue();
		this.descriptorRowId = descriptorRowId;
		this.permissionCodeId = permissionCodeId;
		this.hashCode = calculateHashCode(this);
	}

	static int calculateHashCode(final PermissionCacheKey key) {
		int hash = ((int) (key.descriptorRowId)) ^ ((int) (key.descriptorRowId >> 32)); 
		hash = (hash * 31) ^ key.permissionCodeId;
		hash = (hash * 37) ^ ((int) key.userRowId) ^ ((int) (key.userRowId >> 32));
		return hash;
	}
	
	// --------------------------------------------------------------
	// ---
	// --- Object overrides
	// ---
	// --------------------------------------------------------------
	@Override
	public int hashCode() {
		return hashCode;
	}

	@Override
	public boolean equals(final Object o) {
		if (o instanceof PermissionCacheKey) {
			final PermissionCacheKey k = (PermissionCacheKey) o;
			return k.permissionCodeId == permissionCodeId &&
				   k.descriptorRowId == descriptorRowId &&
				   k.userRowId == userRowId;
		}
		return false;
	}

}

package ch.eisenring.dms.shared.model.api;

import ch.eisenring.core.io.binary.BinaryHolder;

/**
 * Common interface for binary object implementations
 */
public interface DMSBinary extends DMSVersion {

	/**
	 * Gets the version of the plain text extract.
	 */
	public int getPlainversion();

	/**
	 * Gets the plain text extract of this binary.
	 */
	public String getPlaintext();

	/**
	 * Gets the binary data (uncompressed)
	 */
	public BinaryHolder getBinaryData();

	/**
	 * Gets the raw binary data (compressed)
	 */
	public BinaryHolder getRawData();

}

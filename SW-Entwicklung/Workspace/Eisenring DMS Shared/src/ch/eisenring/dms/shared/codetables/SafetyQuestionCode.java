package ch.eisenring.dms.shared.codetables;

import java.util.Collection;

import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.codetype.StaticCode;
import ch.eisenring.dms.service.api.DMSObject;
import ch.eisenring.dms.service.codetables.DMSObjectType;

public abstract class SafetyQuestionCode extends StaticCode {

	public final static String SETTINGS_SAFETYQUESTIONMODE = "SafetyQuestionMode";
	
	public final static SafetyQuestionCode ALWAYS = new SafetyQuestionCode(0, null, "Immer") {
		public boolean requiresQuestion(final Collection<DMSObject> selection) {
			return true;
		}
	};

	public final static SafetyQuestionCode MULTIPLE = new SafetyQuestionCode(1, Integer.valueOf(1), "Bei Mehrfa<PERSON>uswahl") {
		public boolean requiresQuestion(final Collection<DMSObject> selection) {
			if (selection.isEmpty())
				return false;
			int count = 0;
			for (final DMSObject document : selection) {
				if (DMSObjectType.FOLDER.equals(document.getType())) {
					// folder counts as multiselection
					count = Integer.MAX_VALUE;
					break;
				}
				++count;
			}
			return count > 1;
		}
	};

	public final static SafetyQuestionCode NEVER = new SafetyQuestionCode(2, Integer.valueOf(2), "Nie") {
		public boolean requiresQuestion(final Collection<DMSObject> selection) {
			return false;
		}
	};

	SafetyQuestionCode(final int id, final Object key, final String text) {
		super(id, key, text, text);
	}

	@Override
	public Class<? extends AbstractCode> getTypeClass() {
		return SafetyQuestionCode.class;
	}

	public abstract boolean requiresQuestion(final Collection<DMSObject> selection);

}

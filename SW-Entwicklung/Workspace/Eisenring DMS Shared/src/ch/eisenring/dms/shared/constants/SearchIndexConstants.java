package ch.eisenring.dms.shared.constants;

import ch.eisenring.core.datatypes.strings.CharSubstitutor;
import ch.eisenring.core.datatypes.strings.CharSubstitutorTableBased;
import ch.eisenring.core.resource.ResourceIdentifier;

public interface SearchIndexConstants {

	public final static CharSubstitutor SUBSTITUTOR = CharSubstitutorTableBased.create(
			new ResourceIdentifier("Searchindex.charsubst", SearchIndexConstants.class));
	
//	// --------------------------------------------------------------
//	// ---
//	// --- Case, Accent and Umlaut conversion
//	// ---
//	// --------------------------------------------------------------
//	/**
//	 * Table of search index substitutions for ISO8859-1/Unicode
//	 */
//	String[] C_ISO8859_1 = {
//		null, null, null, null, null, null, null, null, // 0x0000
//		null, " ",  " ",  null, null, " ",  null, null,	// 0x0008
//		null, null, null, null, null, null, null, null, // 0x0010
//		null, null, null, null, null, null, null, null, // 0x0018
//		" ",  null, null, null, null, null, null, null, // 0x0020
//		null, null, null, null, null, null, null, null, // 0x0028
//		"0",  "1",  "2",  "3",  "4",  "5",  "6",  "7",  // 0x0030
//		"8",  "9",  null, null, null, null, null, null, // 0x0038
//		null, "a",  "b",  "c",  "d",  "e",  "f",  "g",	// 0x0040
//		"h",  "i",  "j",  "k",  "l",  "m",  "n",  "o",  // 0x0048
//		"p",  "q",  "r",  "s",  "t",  "u",  "v",  "w",  // 0x0050
//		"x",  "y",  "z",  null, null, null, null, null,	// 0x0058
//		null, "a",  "b",  "c",  "d",  "e",  "f",  "g",	// 0x0060
//		"h",  "i",  "j",  "k",  "l",  "m",  "n",  "o",  // 0x0068
//		"p",  "q",  "r",  "s",  "t",  "u",  "v",  "w",  // 0x0070
//		"x",  "y",  "z",  null, null, null, null, null,	// 0x0078
//		null, null, null, "f",  null, null, null, null, // 0x0080
//		null, null, "s",  null, "e",  null, "z",  null, // 0x0088
//		null, null, null, null, null, null, null, null, // 0x0090
//		null, null, "s",  null, "e",  null, "z",  "y",  // 0x0098
//		" ",  null, null, null, null, "y",  null, null, // 0x00A0
//		null, null, null, null, null, null, null, null, // 0x00A8
//		null, null, null, null, null, null, null, null, // 0x00B0
//		null, null, null, null, null, null, null, null, // 0x00B8
//		"a",  "a",  "a",  "a",  "ae", "a",  "a",  "c",	// 0x00C0
//		"e",  "e",  "e",  "e",	"i",  "i",  "i",  "i",	// 0x00C8
//		"d",  "n",  "o",  "o",  "o",  "o",  "oe", null,	// 0x00D0
//		"o",  "u",  "u",  "u",  "ue", "y",  null, "ss",	// 0x00D8
//		"a",  "a",  "a",  "a",  "ae", "a",  "a",  "c",	// 0x00E0
//		"e",  "e",  "e",  "e",  "i",  "i",  "i",  "i",	// 0x00E8
//		null, "n",  "o",  "o",  "o",  "o",  "oe", null,	// 0x00F0
//		null, "u",  "u",  "u",  "ue", "y",  null, "y",	// 0x00F8
//		"a",  "a",  "a",  "a",  "a",  "a",  "c",  "c",  // 0x0100
//		"c",  "c",  "c",  "c",  "c",  "c",  "d",  "d",  // 0x0108
//		"d",  "a",  "e",  "e",  "e",  "e",  "e",  "e",  // 0x0110
//		"e",  "e",  "e",  "e",  "g",  "g",  "g",  "g",  // 0x0118
//		"g",  "g",  "g",  "g",  "h",  "h",  "h",  "h",  // 0x0120
//		"i",  "i",  "i",  "i",  "i",  "i",  "i",  "i",  // 0x0128
//		"i",  "i",  "ij", "ij", "j",  "j",  "k",  "k",  // 0x0130
//		"k",  "l",  "l",  "l",  "l",  "l",  "l",  "l",  // 0x0138
//		"l",  "l",  "l",  "n",  "n",  "n",  "n",  "n",  // 0x0140
//		"n",  "n",  "n",  "n",  "o",  "o",  "o",  "o",  // 0x0148
//		"o",  "o",  null, "a",  "r",  "r",  "r",  "r",	// 0x0150
//		"r",  "r",  "s",  "s",  "s",  "s",  "s",  "s",  // 0x0158
//		"s",  "s",  "t",  "t",  "t",  "t",  "t",  "t",  // 0x0160
//		"u",  "u",  "u",  "u",  "u",  "u",  "u",  "u",  // 0x0168
//		"u",  "u",  "u",  "u",  "w",  "w",  "y",  "y",  // 0x0170
//		"y",  "z",  "z",  "z",  "z",  "z",  "z",  null,	// 0x0178
//		"b",  "b",  "b",  "b",  "b",  "b",  "c",  "c",  // 0x0180
//		"c",  "d",  "d",  "d",  "d",  "o",  "e",  "e",  // 0x0188
//		"e",  "f",  "f",  "g",  "y",  "h",  "i",  null, // 0x0190
//		"k",  "k",  null, null, "w",  "n",  "n",  null, // 0x0198
//		null, null, null, null, "p",  "p",  "r",  "s",	// 0x01A0
//		"s",  null, null, "t",  "t",  "t",  "t",  "u",  // 0x01A8
//		"u",  "u",  "u",  "y",  "y",  "z",  "z",  null,	// 0x01B0
//		null, null, null, "z",  "s",  "s",  null, null, // 0x01B8
//		null, null, null, null, "dz", "dz", "dz", "lj", // 0x01C0
//		"lj", "lj", "nj", "nj", "nj", "a",  "a",  "i",	// 0x01C8
//		"i",  "o",  "o",  "u",  "u",  "u",  "u",  "u",  // 0x01D0
//		"u",  "u",  "u",  "u",  "u",  null, "a",  "a",	// 0x01D8
//		"a",  "a",  "ae", "ae", "g",  "g",  "g",  "g",	// 0x01E0
//		"k",  "k",  "q",  "q",  "q",  "q",  null, null, // 0x01E8
//		"i",  "dz", "dz", "dz", "g",  "g",  "hj", null, // 0x01F0
//		"n",  "n",  "a",  "a",  "ae", "ae", "o",  "o",  // 0x01F8
//		"a",  "a",  "a",  "a",  "e",  "e",  "e",  "e",  // 0x0200
//		"i",  "i",  "i",  "i",  "o",  "o",  "o",  "o",  // 0x0208
//		"r",  "r",  "r",  "r",  "u",  "u",  "u",  "u",  // 0x0210
//		"s",  "s",  "t",  "t",  null, null, "h",  "h",  // 0x0218
//		null, null, null, null, "z",  "z",  "a",  "a",  // 0x0220
//		"e",  "e",  "o",  "o",  "o",  "o",  "o",  "o",  // 0x0228
//		"o",  "o",  "y",  "y",  null, null, null, null, // 0x0230
//		null, null, null, null, null, null, null, null, // 0x0238
//		null, null, null, null, null, null, null, null, // 0x0240
//		null, null, null, null, null, null, null, null, // 0x0248
//		"a",  "a",  "a",  "b",  "c",  "c",  "d",  "d",	// 0x0250
//		null, null, null, null, null, null, null, null, // 0x0258
//		"g",  "g",  "g",  "y",  "y",  "u",  "h",  "h",	// 0x0260
//		null, null, null, null, null, null, null, "w",	// 0x0268
//		"w",  "m",  "n",  "n",  "n",  null, "ce", "w",  // 0x0270
//		"w",  null, null, null, null, null, null, null, // 0x0278
//		"r",  null, "s",  "f",  "f",  "l",  "l",  null, // 0x0280
//		"u",  "u",  "v",  "v",  "m",  "l",  "y",  "z",  // 0x0288
//		"z",  "z",  "z",  null, null, null, null, null,	// 0x0290
//		"o",  "b",  null, null, "g",  "h",  "j",  "k",  // 0x0298
//		"q",  null, null, "dz", "dz", "dz", "ts", "tf",	// 0x02A0
//		"tg", "fn", "ls", "lz", "df", null, null, null  // 0x02A8
//	};
//
//	// --------------------------------------------------------------
//	// ---
//	// --- Unicode replacement map
//	// ---
//	// --------------------------------------------------------------
//	@SuppressWarnings("serial")
//	Lookup<Character, String> UNICODE_TO_SEARCH = new HashMap<Character, String>(256, 4F) {{
//		for (int i=0; i<C_ISO8859_1.length; ++i) {
//			final String r = C_ISO8859_1[i];
//			if (r == null)
//				continue;
//			put(Character.valueOf((char) (i & 0xFFFF)), r);
//		}
//	}};
	
}

package ch.eisenring.dms.shared.model.data;

import java.io.IOException;
import java.util.Collections;

import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.Map;
import ch.eisenring.core.collections.Set;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.collections.impl.ArraySet;
import ch.eisenring.core.collections.impl.HashMap;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.io.StreamReader;
import ch.eisenring.core.io.StreamUtil;
import ch.eisenring.core.io.StreamWriter;
import ch.eisenring.core.io.Streamable;
import ch.eisenring.dms.service.codetables.DMSPermissionCode;
import ch.eisenring.user.shared.service.RoleFacade;
import ch.eisenring.user.shared.service.USRService;
import ch.eisenring.user.shared.service.UserFacade;

/**
 * Permission description.
 * 
 * Describes a full set of permissions (for users and roles).
 */
public final class PermissionDescriptor implements Streamable {

	public final static Long EVERYBODY_ROWID = Long.valueOf(0L);
	public final static String EVERYBODY_NAME = "<Jeder>";

	/**
	 * Default permission descriptor: everybody, everything
	 */
	public final static PermissionDescriptor DEFAULT_PERMISSION =
			new PermissionDescriptor(0L, "<Standardberechtigung>",
					"[1;" + EVERYBODY_ROWID + ";255]");

	/**
	 * Missing permission: nobody, nothing
	 */
	public final static PermissionDescriptor MISSING_PERMISSION =
			new PermissionDescriptor(0L, "<Berechtigung nicht verfügbar>", (String) null);

	private long rowId;
	private String displayname;
	private int privateFlag;
	private final Map<Long, Integer> permissions;

	// --------------------------------------------------------------
	// ---
	// --- Constructors
	// ---
	// --------------------------------------------------------------
	/**
	 * Default constructor used by AutoStreamable
	 */
	PermissionDescriptor() {
		this.permissions = new HashMap<Long, Integer>();
	}

	/**
	 * Constructor for new PermissionDescriptor
	 */
	public PermissionDescriptor(final long rowId) {
		this(rowId, Strings.concat("Neue Berechtigung #", rowId), (String) null);
	}

	public PermissionDescriptor(final long rowId, final String displayName, final String canonicalPermissions) {
		this();
		this.rowId = rowId;
		this.displayname = displayName;
		setCanonicalPermissions(canonicalPermissions);
	}

	/**
	 * Copy constructor
	 */
	public PermissionDescriptor(final PermissionDescriptor descriptor) {
		this.rowId = descriptor.rowId;
		this.displayname = descriptor.displayname;
		this.permissions = new HashMap<Long, Integer>(descriptor.permissions);
	}

	// --------------------------------------------------------------
	// ---
	// --- Getters/Setters
	// ---
	// --------------------------------------------------------------
	public String getDisplayname() {
		return displayname;
	}

	public void setDisplayname(final String displayname) {
		this.displayname = displayname;
	}

	public long getRowId() {
		return rowId;
	}

	public void setRowId(final long rowId) {
		this.rowId = rowId;
	}

	/**
	 * Checks permission code for principal.
	 * This check only checks if the principal has the permission code explicitly,
	 * it does not check if the permission code is otherwise implicitly allowed.
	 */
	public boolean isPermitted(final DMSPermissionCode code, final Long principalRowId) {
		if (code == null)
			return false;
		return isPermitted(code.getId(), principalRowId);
	}

	public boolean isPermitted(final int codeId, final Long principalRowId) {
		if (principalRowId == null)
			return false;
		final Integer flags;
		synchronized (this) {
			flags = permissions.get(principalRowId);
		}
		if (flags == null)
			return false;
		return (flags.intValue() & codeId) != 0; 
	}

	/**
	 * Checks if this descriptor is identical to another descriptor.
	 * Identical means not only the same identity, but also the exact
	 * same state.
	 */
	public boolean isSame(final Object o) {
		if (o instanceof PermissionDescriptor) {
			final PermissionDescriptor other = (PermissionDescriptor) o;
			if (other.rowId != this.rowId)
				return false;
			if (!Strings.equals(other.getDisplayname(), this.getDisplayname()))
				return false;
			final String canonical0 = this.getCanonicalPermissions();
			final String canonical1 = other.getCanonicalPermissions();
			return Strings.equals(canonical0, canonical1);
		}
		return false;
	}

	/**
	 * Adds a permission
	 */
	public boolean addPermission(final DMSPermissionCode code, final Long rowId) {
		if (rowId == null || code == null)
			return false;
		synchronized (this) {
			Integer oldFlags = permissions.get(rowId);
			if (oldFlags == null) {
				permissions.put(rowId, Integer.valueOf(code.getId()));
				return true;
			} else {
				Integer newFlags = Integer.valueOf(code.getId() | oldFlags.intValue());
				permissions.put(rowId, newFlags);
				return !oldFlags.equals(newFlags);
			}
		}
	}

	/**
	 * Removes a permission
	 */
	public boolean remPermission(final DMSPermissionCode code, final Long rowId) {
		if (rowId == null || code == null)
			return false;
		synchronized (this) {
			Integer oldFlags = permissions.get(rowId);
			if (oldFlags == null)
				return false;
			Integer newFlags = Integer.valueOf(oldFlags.intValue() & ~code.getId());
			if (newFlags.intValue() == 0) {
				permissions.remove(rowId);
			} else {
				permissions.put(rowId, newFlags);
			}
			return !oldFlags.equals(newFlags);
		}
	}

	/**
	 * Gets permissions as string
	 */
	public String getCanonicalPermissions() {
		synchronized (this) {
			return PermissionDescriptorEncoding.encode(permissions, 2);
		}
	}

	/**
	 * Sets permissions from string.
	 * This will replace all permissions with permissions parsed from
	 * the canonical string.
	 */
	public void setCanonicalPermissions(final CharSequence canonical) {
		Map<Long, Integer> map = null;
		try {
			map = PermissionDescriptorEncoding.parse(canonical);
		} catch (final Exception e) {
			// ignore if parsing failed
		}
		synchronized (this) {
			permissions.clear();
			if (map != null) 
				permissions.putAll(map);
		}
	}

	/**
	 * Gets a human readable description of this permission descriptor.
	 */
	public String getHumanReadableDescription(final USRService userService) {
		if (userService == null)
			return "Beschreibung nicht verfügbar (Benutzerverwaltungs-Dienst nicht verfügbar)";
		final List<RoleFacade> roleList = userService.getAllRoles();
		final List<UserFacade> userList = userService.getAllUsers();
		final StringMaker b = StringMaker.obtain(1024);
		final List<DMSPermissionCode> codeList = AbstractCode.getInstances(DMSPermissionCode.class);
		for (final DMSPermissionCode code : codeList) {
			if (b.length() > 0)
				b.append("\n\n");
			b.append(code.getLongText());
			b.append(":\n");
			final StringMaker m = StringMaker.obtain(128);
			if (isPermitted(code, EVERYBODY_ROWID)) {
				m.append(EVERYBODY_NAME);
			}
			for (final RoleFacade role : roleList) {
				if (isPermitted(code, role.getRowId())) {
					if (m.length() > 0)
						m.append(", ");
					m.append(role.getDisplayName());
				}
			}
			final List<String> usernames = getPermittedUsernames(userService, code);
			if (!usernames.isEmpty()) {
				m.append("\n[");
				for (int i=0; i<usernames.size(); ++i) {
					if (i > 0)
						m.append(", ");
					m.append(usernames.get(i));
				}
				m.append("]");
			}
			for (final UserFacade user : userList) {
				if (isPermitted(code, user.getRowId())) {
					if (m.length() > 0)
						m.append(", ");
					m.append(user.getFullname());
					m.append(" (");
					m.append(user.getLoginname());
					m.append(")");
				}
			}
			if (m.length() > 0) {
				b.append(m);
			} else {
				b.append("<Niemand>");
			}
			m.releaseSilent();
		}
		return b.release();
	}
	
	/**
	 * Gets a list of all users currently permitted.
	 */
	public List<String> getPermittedUsernames(final USRService userService, final DMSPermissionCode code) {
		final List<String> result = new ArrayList<String>();
		if (userService != null) {
			final Set<UserFacade> users = new ArraySet<>();
			final List<RoleFacade> roleList = userService.getAllRoles();
			for (final RoleFacade role : roleList) {
				if (isPermitted(code, role.getRowId())) {
					users.addAll(userService.getUsers(role));
				}
			}
			final List<UserFacade> userList = userService.getAllUsers();
			for (final UserFacade user : userList) {
				if (isPermitted(code, user.getRowId())) {
					users.add(user);
				}
			}
			// convert users to strings
			for (final UserFacade user : users) {
				final String loginname = user.getLoginname();
				result.add(loginname);
			}
			Collections.sort(result, Strings.Order.NOCASE);
		}
		return result;
	}
	
	public void setPrivateFlag(final int privateFlag) {
		this.privateFlag = (privateFlag == 0 ? 0 : 1);
	}

	public int getPrivateFlag() {
		return privateFlag;
	}

	// --------------------------------------------------------------
	// ---
	// --- Streaming
	// ---
	// --------------------------------------------------------------
	@Override
	public void read(final StreamReader reader) throws IOException {
		this.rowId = reader.readLong();
		this.displayname = reader.readString();
		this.privateFlag = reader.readInt();
		StreamUtil.readMap(reader, permissions);
	}

	@Override
	public void write(final StreamWriter writer) throws IOException {
		writer.writeLong(rowId);
		writer.writeString(displayname);
		writer.writeInt(privateFlag);
		StreamUtil.writeMap(writer, permissions);
	}
	
	// --------------------------------------------------------------
	// ---
	// --- Object overrides
	// ---
	// --------------------------------------------------------------
	@Override
	public int hashCode() {
		return 0;
	}

	@Override
	public boolean equals(final Object o) {
		if (o instanceof PermissionDescriptor) {
			final PermissionDescriptor other = (PermissionDescriptor) o;
			return other.rowId == this.rowId;
		}
		return false;
	}

	@Override
	public String toString() {
		return Strings.trim(displayname);
	}

}

package ch.eisenring.dms.shared.codetables;

import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.codetype.StaticCode;
import ch.eisenring.dms.service.codetables.DMSObjectType;
import ch.eisenring.dms.service.codetables.DMSPermissionCode;

/**
 * Provides (DMS private) properties for DMSObjectType
 */
public final class DMSObjectTypeProperties extends StaticCode {

	public final static DMSObjectTypeProperties FOLDER = new DMSObjectTypeProperties(DMSObjectType.FOLDER,
			DMSPermissionCode.FOLDER_VIEW, DMSPermissionCode.FOLDER_CREATE, DMSPermissionCode.FOLDER_ALTER, DMSPermissionCode.FOLDER_STATUS);
	
	public final static DMSObjectTypeProperties FILE = new DMSObjectTypeProperties(DMSObjectType.FILE,
			DMSPermissionCode.FILE_VIEW, DMSPermissionCode.FILE_CREATE, DMSPermissionCode.FILE_ALTER, DMSPermissionCode.FILE_STATUS);

	private final DMSPermissionCode viewPermission;
	private final DMSPermissionCode createPermission;
	private final DMSPermissionCode alterPermission;
	private final DMSPermissionCode statusPermission;
	
	private DMSObjectTypeProperties(final DMSObjectType objectType,
			final DMSPermissionCode viewPermission,
			final DMSPermissionCode createPermission,
			final DMSPermissionCode alterPermission,
			final DMSPermissionCode statusPermission) {
		super(objectType.getId(), objectType.getKey(), objectType.getShortText(), objectType.getLongText());
		this.viewPermission = viewPermission;
		this.createPermission = createPermission;
		this.alterPermission = alterPermission;
		this.statusPermission = statusPermission;
	}

	/**
	 * Gets the properties for object type
	 */
	public static DMSObjectTypeProperties get(final DMSObjectType objectType) {
		if (objectType == null)
			return null;
		return AbstractCode.getById(objectType.getId(), DMSObjectTypeProperties.class);
	}

	public DMSPermissionCode getViewPermissionCode() {
		return viewPermission;
	}

	public DMSPermissionCode getCreatePermissionCode() {
		return createPermission;
	}

	public DMSPermissionCode getAlterPermissionCode() {
		return alterPermission;
	}

	public DMSPermissionCode getStatusPermissionCode() {
		return statusPermission;
	}

}

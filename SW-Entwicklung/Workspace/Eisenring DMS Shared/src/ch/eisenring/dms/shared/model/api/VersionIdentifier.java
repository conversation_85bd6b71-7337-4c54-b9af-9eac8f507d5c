package ch.eisenring.dms.shared.model.api;

import java.io.IOException;
import java.io.Serializable;

import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.datatypes.primitives.Primitives;
import ch.eisenring.core.io.StreamReader;
import ch.eisenring.core.io.StreamWriter;
import ch.eisenring.core.io.Streamable;
import ch.eisenring.dms.service.api.DMSObject;
import ch.eisenring.dms.service.codetables.DMSObjectType;

/**
 * Helper class to identify a specific version
 * of a specific object.
 */
public final class VersionIdentifier implements Streamable, Serializable {

	private static final long serialVersionUID = 2L;

	private int type;
	private long objectRowId;
	private Long binaryRowId;

	public VersionIdentifier(final DMSObject object,
							 final Long binaryRowId) {
		this(object.getPKValue(), binaryRowId, object.getType());
	}

	public VersionIdentifier(final long objectRowId,
			                 final Long binaryRowId,
			                 final DMSObjectType type) {
		this.objectRowId = objectRowId;
		this.binaryRowId = binaryRowId;
		this.type = AbstractCode.getId(type, 0);
	}

	public long getObjectRowId() {
		return objectRowId;
	}
	
	public Long getBinaryRowId() {
		return binaryRowId;
	}

	public DMSObjectType getType() {
		return AbstractCode.getById(type, DMSObjectType.class);
	}

	// --------------------------------------------------------------
	// ---
	// --- Streamable implementation
	// ---
	// --------------------------------------------------------------
	VersionIdentifier() {
		// default constructor for Streamable
	}

	@Override
	public void read(final StreamReader reader) throws IOException {
		type = reader.readInt();
		objectRowId = reader.readLong();
		binaryRowId = (Long) reader.readObject();
	}

	@Override
	public void write(final StreamWriter writer) throws IOException {
		writer.writeInt(type);
		writer.writeLong(objectRowId);
		writer.writeObject(binaryRowId);
	}

	// --------------------------------------------------------------
	// ---
	// --- Object override
	// ---
	// --------------------------------------------------------------
	@Override
	public int hashCode() {
		int h = ((int) objectRowId) ^ ((int) (objectRowId >> 32));
		if (binaryRowId != null)
			h ^= binaryRowId.hashCode();
		return h;
	}

	@Override
	public boolean equals(final Object o) {
		if (o instanceof VersionIdentifier) {
			final VersionIdentifier v = (VersionIdentifier) o;
			return v.objectRowId == objectRowId && Primitives.equals(v.binaryRowId, binaryRowId);
		}
		return false;
	}

}

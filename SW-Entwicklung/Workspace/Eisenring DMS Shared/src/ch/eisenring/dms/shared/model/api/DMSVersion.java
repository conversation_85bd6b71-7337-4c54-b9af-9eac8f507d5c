package ch.eisenring.dms.shared.model.api;

import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.sort.Comparator;
import ch.eisenring.dms.service.codetables.DMSBinaryStatus;
import ch.eisenring.dms.shared.codetables.DMSCompressionCode;

/**
 * Common interface for version object implementations
 */
public interface DMSVersion {

	/**
	 * Gets the primary key value for this object
	 */
	public abstract long getPKValue();
	
	/**
	 * Gets the object row id this version belongs to.
	 */
	public long getObjectRowId();

	/**
	 * Gets the status code of the binary version.
	 */
	public DMSBinaryStatus getStatusCode();

	/**
	 * Gets the size of the version in bytes (uncompressed)
	 */
	public long getBytesize();

	/**
	 * Gets the physical size of the version (compressed)
	 */
	public long getRawsize();

	/**
	 * Gets the compression type used for this binary
	 */
	public DMSCompressionCode getDCMCode();

	/**
	 * Gets the date when the version was created
	 */
	public long getMadeOn();

	/**
	 * Gets the hash of the uncompressed binary data
	 */
	public String getBinaryhash();

	/**
	 * Gets the file extension of the version
	 */
	public String getFileExtension();
	
	// --------------------------------------------------------------
	// ---
	// --- Ordering
	// ---
	// --------------------------------------------------------------
	public static class Order {
		public final static Comparator<DMSVersion> RowId = Comparator.wrapNullSafe(
				new Comparator<DMSVersion>() {
			@Override
			public int compare(final DMSVersion o1, final DMSVersion o2) {
				return compareSigned(o1.getPKValue(), o2.getPKValue());
			}
		});

		public final static Comparator<DMSVersion> Extension = Comparator.wrapNullSafe(
				new Comparator<DMSVersion>() {
			@Override
			public int compare(final DMSVersion o1, final DMSVersion o2) {
				return Strings.compareIgnoreCase(o1.getFileExtension(), o2.getFileExtension());
			}
		});

		public final static Comparator<DMSVersion> MadeOn = Comparator.wrapNullSafe(
				new Comparator<DMSVersion>() {
			@Override
			public int compare(final DMSVersion o1, final DMSVersion o2) {
				return TimestampUtil.compare(o1.getMadeOn(), o2.getMadeOn());
			}
		});

		public final static Comparator<DMSVersion> ByteSize = Comparator.wrapNullSafe(
				new Comparator<DMSVersion>() {
			@Override
			public int compare(final DMSVersion o1, final DMSVersion o2) {
				return compareUnsigned(o1.getBytesize(), o2.getBytesize());
			}
		});

		public final static Comparator<DMSVersion> Status = Comparator.wrapNullSafe(
				new Comparator<DMSVersion>() {
			@Override
			public int compare(final DMSVersion o1, final DMSVersion o2) {
				return compareShort(o1.getStatusCode(), o2.getStatusCode());
			}
		});	

		public final static Comparator<DMSVersion> Compression = Comparator.wrapNullSafe(
				new Comparator<DMSVersion>() {
			@Override
			public int compare(final DMSVersion o1, final DMSVersion o2) {
				int r = compareSigned(o1.getRawsize(), o2.getRawsize());
				if (r != 0)
					return r;
				return AbstractCode.Order.Id.compare(o1.getDCMCode(), o2.getDCMCode());
			}
		});
	}

}

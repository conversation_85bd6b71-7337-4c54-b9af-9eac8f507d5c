package ch.eisenring.dms.service;

import ch.eisenring.core.codetype.StaticCode;

/**
 * Collection of result codes returned by some service calls.
 */
public final class DMSServiceResultCode extends StaticCode {

	/**
	 * Result code indicating operation was successful
	 */
	public final static DMSServiceResultCode GENERIC_OK =
		new DMSServiceResultCode(0, "Ok");

	/**
	 * Result code indicating an error that could not be classified
	 */
	public final static DMSServiceResultCode GENERIC_ERROR =
		new DMSServiceResultCode(1, "Unbekannter Fehler");

	/**
	 * Result code indicating an invalid argument
	 */
	public final static DMSServiceResultCode INVALID_PARAMETER =
		new DMSServiceResultCode(2, "Ungültiger Parameter");

	/**
	 * Result code indicating a specified object was not found
	 */
	public final static DMSServiceResultCode OBJECT_NOT_FOUND =
		new DMSServiceResultCode(3, "Objekt nicht gefunden");

	/**
	 * Result code indicating there were more results generated
	 * than the operation allows.
	 */
	public final static DMSServiceResultCode TOO_MANY_RESULTS =
		new DMSServiceResultCode(4, "Zu viele Ergebnisse");

	/**
	 * Result code indicating an internal error condition, such
	 * as damaged database or misbehaved code.
	 */
	public final static DMSServiceResultCode INTERNAL_ERROR =
		new DMSServiceResultCode(5, "Interner Fehler");

	/**
	 * Result code indicating the service is currently not
	 * available (network or database not available, service disabled)
	 */
	public final static DMSServiceResultCode SERVICE_UNAVAILABLE =
		new DMSServiceResultCode(6, "Dienst nicht verfügbar");
			
	/**
	 * Result code indicating the service does not support the requested operation.
	 * Only encountered when service does not support all calls.
	 */
	public final static DMSServiceResultCode OPERATION_NOT_SUPPORTED =
		new DMSServiceResultCode(7, "Operation nicht unterstützt");

	private DMSServiceResultCode(final int id, final String text) {
		super(id, Integer.valueOf(id), text, text);
	}

	public boolean isError() {
		return getId() != 0;
	}

	public boolean isSuccess() {
		return getId() == 0;
	}

}

package ch.eisenring.dms.service.codetables;

import ch.eisenring.core.codetype.StaticCode;

public final class DMSPermissionCode extends StaticCode {

	public final static DMSPermissionCode FOLDER_VIEW =
		new DMSPermissionCode(0x01, "<PERSON><PERSON><PERSON>", "Ordn<PERSON> ansehen", DMSPermissionGroupCode.FOLDER);

	public final static DMSPermissionCode FOLDER_CREATE =
		new DMSPermissionCode(0x02, "<PERSON><PERSON><PERSON><PERSON>", "Ordner erstellen", DMSPermissionGroupCode.FOLDER);

	public final static DMSPermissionCode FOLDER_ALTER =
		new DMSPermissionCode(0x04, "Bearbeiten", "Ordner bearbeiten", DMSPermissionGroupCode.FOLDER);

	public final static DMSPermissionCode FOLDER_STATUS =
		new DMSPermissionCode(0x08, "Stornieren", "Ordner stornieren", DMSPermissionGroupCode.FOLDER);
	
	public final static DMSPermissionCode FILE_VIEW =
		new DMSPermissionCode(0x10, "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> ansehen", DMSPermissionGroupCode.FILE);

	public final static DMSPermissionCode FILE_CREATE =
		new DMSPermissionCode(0x20, "Erstellen", "Dateien erstellen", DMSPermissionGroupCode.FILE);

	public final static DMSPermissionCode FILE_ALTER =
		new DMSPermissionCode(0x40, "Bearbeiten", "Dateien bearbeiten", DMSPermissionGroupCode.FILE);

	public final static DMSPermissionCode FILE_STATUS =
		new DMSPermissionCode(0x80, "Stornieren", "Dateien stornieren", DMSPermissionGroupCode.FILE);

	private final DMSPermissionGroupCode groupCode;
	
	private DMSPermissionCode(final int id,
							  final String shortText,
							  final String longText,
							  final DMSPermissionGroupCode groupCode) {
		super(id, Integer.valueOf(id), shortText, longText);
		this.groupCode = groupCode;
	}

	public DMSPermissionGroupCode getGroup() {
		return groupCode;
	}

}

package ch.eisenring.dms.service.api;

import java.io.IOException;

import ch.eisenring.core.datatypes.strings.Msg;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.StringMakerFriendly;
import ch.eisenring.core.io.StreamReader;
import ch.eisenring.core.io.StreamWriter;
import ch.eisenring.core.io.Streamable;
import ch.eisenring.dms.service.DMSServiceException;
import ch.eisenring.dms.service.DMSServiceResultCode;
import ch.eisenring.dms.service.codetables.DMSFolderType;
import ch.eisenring.dms.service.util.LogiwareUtil;

/**
 * Specifies an object to be located in DMS.
 */
public interface DMSObjectSpecifier {

	/**
	 * Attempts to resolve the object, throws if there is a
	 * problem with finding the object.
	 */
	public DMSObjectIdentity resolveIdentity() throws DMSServiceException;

	// --------------------------------------------------------------
	// ---
	// --- Factory methods
	// ---
	// --------------------------------------------------------------
	/**
	 * Creates specifier for basis folder
	 */
	public static DMSObjectSpecifier baseFolder(final String baseNumber) {
		throw new UnsupportedOperationException();
	}

	/**
	 * Creates specifier for project folder
	 */
	public static DMSObjectSpecifier projectFolder(final String projectNumber) {
		throw new UnsupportedOperationException();
	}

	/**
	 * Creates specifier for project content folder.
	 */
	public static DMSObjectSpecifier projectContentFolder(final String projectNumber, final DMSFolderType folderType) {
		throw new UnsupportedOperationException();
	}

}

final class DMSObjectSpecifierImpl implements StringMakerFriendly, DMSObjectSpecifier, Streamable {

	public final static int MODE_BASE = 1;
	public final static int MODE_PROJECT = 2;
	public final static int MODE_CONTENT = 3;
	
	int mode;
	String mainNumber;
	String extNumber;
	DMSFolderType contentFolder;
	DMSObjectIdentity resolvedIdentity;
	
	// --------------------------------------------------------------
	// ---
	// --- Constructor
	// ---
	// --------------------------------------------------------------
	DMSObjectSpecifierImpl(final int mode, final String number, final DMSFolderType contentFolder) {
		this.mode = mode;
		this.mainNumber = mode == MODE_BASE
				? LogiwareUtil.getSigificantBasisnummer(number)
				: LogiwareUtil.getSigificantProjektnummer(number);
		this.extNumber = LogiwareUtil.getExtensionNumber(number);
		this.contentFolder = contentFolder;
	}

	// --------------------------------------------------------------
	// ---
	// --- Object overrides
	// ---
	// --------------------------------------------------------------
	@Override
	public String toString() {
		final StringMaker b = StringMaker.obtain();
		toStringMaker(b);
		return b.release();
	}

	@Override
	public void toStringMaker(final StringMaker target) {
		target.append("ObjektSpecifier[");
		target.append(mainNumber);
		target.append(';');
		target.append(extNumber);
		target.append(';');
		target.append(contentFolder);
		target.append(']');
	}

	// --------------------------------------------------------------
	// ---
	// --- Resolving implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public DMSObjectIdentity resolveIdentity() throws DMSServiceException {
		final String message = Msg.mk("Auflösen von Objektidentität {} nicht implementiert", this);
		throw new DMSServiceException(DMSServiceResultCode.GENERIC_ERROR, message);
	}

	// --------------------------------------------------------------
	// ---
	// --- Streamable implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public void read(final StreamReader reader) throws IOException {
		mode = reader.readInt();
		mainNumber = reader.readString();
		extNumber = reader.readString();
		contentFolder = reader.readCode(DMSFolderType.class);
		resolvedIdentity = (DMSObjectIdentity) reader.readObject();
	}

	@Override
	public void write(final StreamWriter writer) throws IOException {
		writer.writeInt(mode);
		writer.writeString(mainNumber);
		writer.writeString(extNumber);
		writer.writeCode(contentFolder);
		writer.writeObject(resolvedIdentity);
	}

}

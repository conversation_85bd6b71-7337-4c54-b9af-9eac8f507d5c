package ch.eisenring.dms.service.api;

import java.io.IOException;

import ch.eisenring.core.HashUtil;
import ch.eisenring.core.io.StreamReader;
import ch.eisenring.core.io.StreamWriter;
import ch.eisenring.core.io.Streamable;
import ch.eisenring.core.io.binary.BinaryHolder;
import ch.eisenring.core.util.file.FileImage;
import ch.eisenring.dms.service.codetables.DMSObjectStatus;

public final class DMSDocumentImage implements DMSObjectIdentity, FileImage, Streamable {

	private transient long objectRowId;
	private transient long versionRowId;
	private transient long lastModified;
	private transient String filename;
	private transient BinaryHolder filedata;
	private transient DMSObjectStatus status;

	/**
	 * Standard constructor
	 */
	public DMSDocumentImage(
			final long objectRowId, final long versionRowId,
			final String filename, final BinaryHolder filedata,
			final long lastModified,
			final DMSObjectStatus status) {
		this();
		this.objectRowId = objectRowId;
		this.versionRowId = versionRowId;
		this.filename = filename;
		this.filedata = filedata;
		this.lastModified = lastModified;
	}

	// --------------------------------------------------------------
	// ---
	// --- Getters & Setters
	// ---
	// --------------------------------------------------------------
	@Override
	public String getFilename() {
		return filename;
	}

	@Override
	public BinaryHolder getFiledata() {
		return filedata;
	}

	/**
	 * Gets the id that identifies the document in the DMS.
	 */
	public long getRowId() {
		return objectRowId;
	}

	/**
	 * Gets the id that identifies the version in the DMS.
	 */
	public long getVersionRowId() {
		return versionRowId;
	}

	/**
	 * Gets the date when the document was last modified
	 */
	public long getLastModified() {
		return lastModified;
	}

	// --------------------------------------------------------------
	// ---
	// --- DMSObjectIdentity implementation
	// ---
	// --------------------------------------------------------------
	public long getPKValue() {
		return objectRowId;
	}

	// --------------------------------------------------------------
	// ---
	// --- DMSObjectSpecifier implementation
	// ---
	// --------------------------------------------------------------
	public DMSObjectIdentity resolveIdentity() {
		return this;
	}

	// --------------------------------------------------------------
	// ---
	// --- Streamable implementation
	// ---
	// --------------------------------------------------------------
	DMSDocumentImage() {
		// constructor for AutoStreamable
	}

	@Override
	public void read(final StreamReader reader) throws IOException {
		objectRowId = reader.readLong();
		versionRowId = reader.readLong();
		lastModified = reader.readLong();
		status = reader.readCode(DMSObjectStatus.class);
		filename = reader.readString();
		filedata = reader.readBinaryHolder();
	}

	@Override
	public void write(final StreamWriter writer) throws IOException {
		writer.writeLong(objectRowId);
		writer.writeLong(versionRowId);
		writer.writeLong(lastModified);
		writer.writeCode(status);
		writer.writeString(filename);
		writer.writeBinaryHolder(filedata);
	}

	// --------------------------------------------------------------
	// ---
	// --- Object overrides
	// ---
	// --------------------------------------------------------------
	@Override
	public int hashCode() {
		return HashUtil.hashCode(versionRowId);
	}

	@Override
	public boolean equals(final Object o) {
		return o instanceof DMSDocumentImage && ((DMSDocumentImage) o).versionRowId == versionRowId;
	}

}

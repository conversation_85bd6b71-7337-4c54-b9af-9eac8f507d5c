package ch.eisenring.dms.service.api;

import java.io.IOException;

import ch.eisenring.core.HashUtil;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.StringMakerFriendly;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.io.StreamReader;
import ch.eisenring.core.io.StreamWriter;
import ch.eisenring.core.io.Streamable;

/**
 * Identifies an object in the DMS (can be either folder or file).
 */
public interface DMSObjectIdentity extends DMSObjectSpecifier {

	/**
	 * Gets the primary key value of this objects identity
	 */
	public long getPKValue();

	// ----------------------------------------------------
	// ---
	// --- Factory methods
	// ---
	// ----------------------------------------------------
	/**
	 * Converts any type of object identity into its smallest,
	 * streamable capable form.
	 */
	public static DMSObjectIdentity create(final DMSObjectIdentity identity) {
		if (identity == null)
			return null;
		if (identity instanceof DMSObjectIdentityImpl)
			return identity;
		return new DMSObjectIdentityImpl(identity.getPKValue());
	}

	/**
	 * Creates a generic DMSObjectIdentity with given PK value.
	 */
	public static DMSObjectIdentity create(final long rowId) {
		return new DMSObjectIdentityImpl(rowId);
	}

}

final class DMSObjectIdentityImpl implements DMSObjectIdentity, Streamable, StringMakerFriendly {
	
	private long rowId;

	DMSObjectIdentityImpl() {
	}
	
	DMSObjectIdentityImpl(final long rowId) {
		this.rowId = rowId;
	}

	// --------------------------------------------------------------
	// ---
	// --- DMSObjectIdentity implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public long getPKValue() {
		return rowId;
	}

	// --------------------------------------------------------------
	// ---
	// --- DMSObjectSpecifier implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public DMSObjectIdentity resolveIdentity() {
		// is already an identity, does not need to do anything
		return this;
	}

	// --------------------------------------------------------------
	// ---
	// --- Streamable implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public void read(final StreamReader reader) throws IOException {
		rowId = reader.readLong();
	}
	
	@Override
	public void write(final StreamWriter writer) throws IOException {
		writer.writeLong(rowId);
	}

	// --------------------------------------------------------------
	// ---
	// --- Object overrides
	// ---
	// --------------------------------------------------------------
	@Override
	public int hashCode() {
		return HashUtil.hashCode(rowId);
	}

	@Override
	public boolean equals(final Object o) {
		return o instanceof DMSObjectIdentityImpl && ((DMSObjectIdentityImpl) o).rowId == rowId;
	}

	@Override
	public String toString() {
		return Strings.toString(rowId);
	}

	@Override
	public void toStringMaker(final StringMaker target) {
		target.append(rowId);
	}
	
}
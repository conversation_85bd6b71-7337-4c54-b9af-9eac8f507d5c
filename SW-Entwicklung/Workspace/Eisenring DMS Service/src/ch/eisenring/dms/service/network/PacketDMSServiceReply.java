package ch.eisenring.dms.service.network;

import java.io.IOException;

import ch.eisenring.core.io.StreamReader;
import ch.eisenring.core.io.StreamWriter;
import ch.eisenring.dms.service.DMSServiceException;
import ch.eisenring.dms.service.DMSServiceResultCode;

/**
 * Packet used to reply to service request.
 * Only for use by the service implementation.
 */
public final class PacketDMSServiceReply extends AbstractDMSServicePacket {

	private int methodId;
	private DMSServiceResultCode errorCode;
	private String errorMessage;
	private Object result;

	private PacketDMSServiceReply() {
		super(CAPS_SEQUENCE_REPLY, PRIORITY_REPLY);
	}

	// --------------------------------------------------------------
	// ---
	// --- Factory methods
	// ---
	// --------------------------------------------------------------
	public static PacketDMSServiceReply create(final PacketDMSServiceRequest request, final DMSServiceResultCode errorCode, final Object result) {
		final PacketDMSServiceReply packet = new PacketDMSServiceReply();
		packet.setReplyId(request);
		packet.errorCode = DMSServiceResultCode.GENERIC_OK;
		packet.setResult(result);
		packet.methodId = request.getMethodId();
		return packet;
	}

	public static PacketDMSServiceReply create(final PacketDMSServiceRequest request, final DMSServiceException error, final Object result) {
		final PacketDMSServiceReply packet = new PacketDMSServiceReply();
		packet.setReplyId(request);
		if (error == null) {
			packet.errorCode = DMSServiceResultCode.GENERIC_OK;
			packet.errorMessage = null;
		} else {
			packet.errorCode = error.getResultCode();
			packet.errorMessage = error.getMessage();
		}
		packet.setResult(result);
		packet.methodId = request.getMethodId();
		return packet;
	}
	// --------------------------------------------------------------
	// ---
	// --- Packet API
	// ---
	// --------------------------------------------------------------
	private void setResult(final Object result) {
		this.result = result;
	}
	
	public DMSServiceResultCode getErrorCode() {
		return errorCode;
	}

	public String getErrorMessage() { return errorMessage; }

	public Object getResult() {
		return result;
	}

	public int getMethodId() {
		return methodId;
	}

	/**
	 * Generates the exception according to this reply's errorCode/errorMessage.
	 * If the errorCode is "Ok", returns NULL.
	 */
	public DMSServiceException getException() {
		return DMSServiceResultCode.GENERIC_OK.equals(errorCode)
				? null : new DMSServiceException(errorCode, errorMessage);
	}

	/**
	 * Checks if the reply indicates an error and throws DMSServiceException
	 * for the error. If the reply indicates "Ok", simply does nothing.
	 */
	public void throwOnError() throws DMSServiceException {
		if (DMSServiceResultCode.GENERIC_OK.equals(errorCode))
			return;
		throw new DMSServiceException(errorCode, errorMessage);
	}

	// --------------------------------------------------------------
	// ---
	// --- Streaming
	// ---
	// --------------------------------------------------------------
	@Override
	protected void readImpl(final StreamReader reader) throws IOException {
		super.readImpl(reader);
		methodId = reader.readInt();
		errorCode = reader.readCode(DMSServiceResultCode.class);
		errorMessage = reader.readString();
		result = reader.readObject();
	}

	@Override
	protected void writeImpl(final StreamWriter writer) throws IOException {
		super.writeImpl(writer);
		writer.writeInt(methodId);
		writer.writeCode(errorCode);
		writer.writeString(errorMessage);
		writer.writeObject(result);
	}

}

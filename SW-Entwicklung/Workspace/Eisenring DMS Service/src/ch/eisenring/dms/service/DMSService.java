package ch.eisenring.dms.service;

import ch.eisenring.app.shared.Service;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.Map;
import ch.eisenring.core.util.file.FileImage;
import ch.eisenring.dms.service.api.DMSDocumentImage;
import ch.eisenring.dms.service.api.DMSObject;
import ch.eisenring.dms.service.api.DMSObjectIdentity;
import ch.eisenring.dms.service.api.DMSObjectSpecifier;
import ch.eisenring.dms.service.codetables.DMSDocumentTemplateCode;
import ch.eisenring.dms.service.codetables.DMSDocumentType;
import ch.eisenring.dms.service.codetables.DMSFolderType;
import ch.eisenring.dms.service.obj.DMSPermissionSnapshot;
import ch.eisenring.dms.service.proxy.DMSDocumentHandle;
import ch.eisenring.dms.service.proxy.DMSFolderHandle;
import ch.eisenring.dms.service.proxy.DMSObjectHandle;

/**
 * Service API for use by applications other than DMS
 */
public interface DMSService extends Service {

	public default String getFeatureName() {
		return "DMSService";
	}	

	public default Class<? extends Service> getFeatureType() {
		return DMSService.class;
	}

	// --------------------------------------------------------------
	// ---
	// --- Retrieval by handle(s)
	// ---
	// --------------------------------------------------------------
	/**
	 * Gets the document represented by handle.
	 * Throws if not found.
	 */
	public DMSDocumentImage getDocumentImage(final DMSObjectSpecifier specifier) throws DMSServiceException;
	
	/**
	 * Gets collection of documents.
	 * Throws if any document not found.
	 */
	public List<DMSDocumentImage> getDocumentImages(final java.util.Collection<? extends DMSObjectSpecifier> documentHandles) throws DMSServiceException;

	/**
	 * Gets an object identified by identity.
	 */
	public DMSObject getObject(final DMSObjectIdentity objectId) throws DMSServiceException;
	
	/**
	 * Gets collection of objects identified by their identity.
	 */
	public List<DMSObject> getObjects(final java.util.Collection<? extends DMSObjectSpecifier> objectIds) throws DMSServiceException;
	
	/**
	 * Locates the folder tagged with property BASISPROJEKTNUMMER = argument
	 * Throws:
	 * - OBJECT_NOT_FOUND (if no folder found)
     * - TOO_MANY_RESULTS (if more than one folder)
     * - INVALID_PARAMETER (if there is something weird in the parameter number format)
	 */
	public DMSObject locateBasisFolder(final String baseNumber) throws DMSServiceException;

	/**
	 * Locates the folder tagged with property PROJEKTNUMMER = argument
	 * Throws:
	 * - OBJECT_NOT_FOUND (if no folder found)
     * - TOO_MANY_RESULTS (if more than one folder)
     * - INVALID_PARAMETER (if there is something weird in the parameter number format)
	 */
	public DMSObject locateProjectFolder(final String projectNumber) throws DMSServiceException;

	/**
	 * Locates a folder by project/base number and content type.
	 * 
	 * This call will take into account any special properties implied by the given type,
	 * e.g. subfolder structure and implied navigation to base structure.
	 * 
	 * Throws:
	 *   - OBJECT_NOT_FOUND (if no folder found)
     *   - TOO_MANY_RESULTS (if more than one folder)
     *   - INVALID_PARAMETER (if there is something weird in the parameters)
	 */
	public DMSObject locateProjectContentFolder(final String projectNumber, final DMSFolderType folderType) throws DMSServiceException;

	/**
	 * Locates folder for BasisNummer
	 * Throws:
	 *   - OBJECT_NOT_FOUND (if no folder found)
     *   - TOO_MANY_RESULTS (if more than one folder)
     *   - INVALID_PARAMETER (if there is something weird in the parameter number format)
	 */
	public DMSFolderHandle getBasisFolder(final String basenumber) throws DMSServiceException;
	
	/**
	 * Locates folder for ProjektNummer
	 * Throws:
	 *   - OBJECT_NOT_FOUND (if no folder found)
     *   - TOO_MANY_RESULTS (if nore than one folder)
     *   - INVALID_PARAMETER (if there is something weird in the parameter number format)
	 */
	public DMSFolderHandle getProjectFolder(final String projectnumber) throws DMSServiceException;

	/**
	 * Locates a sub-folder of the specified folder that has the given property/value.
	 * 
	 * In case no folder matches, OBJECT_NOT_FOUND is thrown, if there are multiple matches,
	 * TOO_MANY_RESULTS is thrown. 
	 */
	public DMSFolderHandle getFolderByProperty(final DMSFolderHandle folder, final int propertyCode, final Object propertyValue) throws DMSServiceException;

	/**
	 * Gets the contents of a folder
	 */
	public List<DMSObjectHandle> getFolderContents(final DMSFolderHandle folder) throws DMSServiceException;

	/**
	 * Locates all sub-folders of the specified folder that have the given property/value.
	 * 
	 * This method will not throw when there is no matching folder. Instead it returns an empty result list.
	 */
	public List<DMSFolderHandle> getFoldersByProperty(final DMSFolderHandle folder, final int propertyCode, final Object propertyValue) throws DMSServiceException;

	/**
	 * Locates a document that is a child of folder and has the given property/value
	 * 
	 * In case no document matches, OBJECT_NOT_FOUND is thrown, if there are multiple matches,
	 * TOO_MANY_RESULTS is thrown. 
	 */
	public DMSDocumentHandle getDocumentByProperty(final DMSFolderHandle folder, final int propertyCode, final Object propertyValue) throws DMSServiceException;

	/**
	 * Locates all documents that are children of the specified folder that have the given property/value.
	 * 
	 * This method will not throw when there are no matching documents. Instead it returns an empty result list.
	 */
	public List<DMSDocumentHandle> getDocumentsByProperty(final DMSFolderHandle folder, final int propertyCode, final Object propertyValue) throws DMSServiceException;

	/**
	 * Locates storage folder for document in project
	 */
	public DMSFolderHandle getDocumentFolder(final String projectnumber, final DMSDocumentType documentType) throws DMSServiceException;

	/**
	 * Retrieves the documents of the selected type for project number 
	 */
	public List<DMSDocumentImage> getDocumentImages(final String projectnumber, final DMSDocumentType documentType) throws DMSServiceException;

	/**
	 * Retrieves all documents from the selected folder that have the selected type.
	 * Passing NULL for the document type returns all documents in the folder.
	 */
	public List<DMSDocumentImage> getDocumentImages(final DMSFolderHandle folder, final DMSDocumentType documentType) throws DMSServiceException;
	
	/**
	 * Locates a folder by project number and content type.
	 * 
	 * Throws exception if project folder not found, but just returns null if the specified
	 * content folder is not found.
	 */
	public DMSFolderHandle getProjectContentFolder(final String projectnumber, final DMSFolderType folderType) throws DMSServiceException;

	/**
	 * Puts document into DMS, in the specified folder.
	 * If a document with the same name already exists, creates a new version.
	 * Returns the handle of the document.
	 */
	public DMSDocumentHandle putDocument(final DMSFolderHandle parentFolder, final String user, final FileImage document) throws DMSServiceException;
	
	/**
	 * Counts how many documents exist of the selected type for project number 
	 */
	public int countDocuments(final String projectnumber, final DMSDocumentType documentType) throws DMSServiceException;
	
	/**
	 * Counts how many documents exist in the given folder type for all given projects.
	 */
	public Map<String, Integer> countFolderContents(final java.util.Collection<String> projects,
			final DMSFolderType folderType) throws DMSServiceException;

	/**
	 * Counts the number of documents in the given folder type for project/base
	 */
	public int countFolderContents(final String projektNummer, final DMSFolderType folderType) throws DMSServiceException;

	/**
	 * Get Ausführungspläne for Projektnummer
	 */
	public List<DMSDocumentImage> getAusfuehrungsplaene(final String projectnumber) throws DMSServiceException;

	/**
	 * Get the specified document templates (by template id) 
	 */
	public DMSDocumentImage getDocumentImage(final DMSDocumentTemplateCode code) throws DMSServiceException;

	/**
	 * Sets the document type of the given object
	 */
	public void setDocumentType(final DMSDocumentHandle document, final DMSDocumentType documentType, final String user) throws DMSServiceException;

	/**
	 * Gets permissions pre-evaluated per user
	 */
	public DMSPermissionSnapshot getUserPermissions(final String userName) throws DMSServiceException;

	/**
	 * Gets maximum change date of documents in folder.
	 * Returns TIMESTAMP_NULL if no documents found.
	 */
	public long getMaxDocumentDate(final DMSObjectSpecifier folder) throws DMSServiceException;
	
	/**
	 * Gets minimum change date of documents in folder
	 * Returns TIMESTAMP_NULL if no documents found.
	 */
	public long getMinDocumentDate(final DMSObjectSpecifier folder) throws DMSServiceException;

}

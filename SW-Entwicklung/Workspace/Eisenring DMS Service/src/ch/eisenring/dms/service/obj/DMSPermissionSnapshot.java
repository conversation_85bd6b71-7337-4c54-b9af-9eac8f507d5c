package ch.eisenring.dms.service.obj;

import java.io.IOException;

import ch.eisenring.core.collections.Map;
import ch.eisenring.core.collections.impl.HashMap;
import ch.eisenring.core.io.StreamReader;
import ch.eisenring.core.io.StreamWriter;
import ch.eisenring.core.io.Streamable;
import ch.eisenring.dms.service.codetables.DMSPermissionCode;

/**
 * Snapshot of object permissions for a user.
 */
public class DMSPermissionSnapshot implements Streamable {

	private final Map<Long, Integer> map = new HashMap<>();
	private String userName;

	/**
	 * Checks if selected permission is given in the specified descriptor
	 */
	public boolean isPermitted(final Long descriptorId, final DMSPermissionCode permissionCode) {
		if (descriptorId == null || permissionCode == null)
			return false;
		final Integer bitSet = map.get(descriptorId);
		if (bitSet == null)
			return false;
		return (bitSet.intValue() & permissionCode.getId()) != 0;
	}

	/**
	 * Sets permissions for descriptor id
	 */
	public void setPermissions(final Long descriptorId, final int bitSet) {
		if (descriptorId == null)
			return;
		if (bitSet == 0) {
			map.remove(descriptorId);
		} else {
			map.put(descriptorId, Integer.valueOf(bitSet));
		}
	}

	public String getUsername() {
		return userName;
	}

	// --------------------------------------------------------------
	// ---
	// --- Streamable implementation
	// ---
	// --------------------------------------------------------------
	public DMSPermissionSnapshot() {
	}

	@Override
	public void read(final StreamReader reader) throws IOException {
		userName = reader.readString();
		{
			int count = reader.readInt();
			map.clear();
			map.ensureCapacity(count);
			while (--count >= 0) {
				long rowId = reader.readLong();
				int bitSet = reader.readInt();
				map.put(rowId, bitSet);
			}
		}
	}

	@Override
	public void write(final StreamWriter writer) throws IOException {
		writer.writeString(userName);
		{
			writer.writeInt(map.size());
			for (final Long rowId : map.keySet()) {
				writer.writeLong(rowId);
				writer.writeInt(map.get(rowId));
			}
		}
	}

}

package ch.eisenring.dms.service.codetables;

import ch.eisenring.core.codetype.StaticCode;

/**
 * The FolderContentType classifies the usage context of the contained documents
 */
public final class DMSFolderStructureType extends StaticCode {

	public static final DMSFolderStructureType NULL = new DMSFolderStructureType(
		0, "",
		DMSFolderType.NULL,
		DMSSpecialFunctionCode.NULL,
		DMSSpecialFunctionCode.NULL,
		DMSSpecialFunctionCode.NULL);

	public static final DMSFolderStructureType EINZEL = new DMSFolderStructureType(
		1, "Einzelküche",
		DMSFolderType.BASIS_EINZEL,
		DMSSpecialFunctionCode.EINZEL_FOLDER,
		DMSSpecialFunctionCode.NULL,
		DMSSpecialFunctionCode.EINZEL_KUECHE);

	public static final DMSFolderStructureType EIGENTUM = new DMSFolderStructureType(
		2, "Eigentumsobjekt",
		DMSFolderType.BASIS_EIGENTUM,
		DMSSpecialFunctionCode.EIGENTUM_FOLDER,
		DMSSpecialFunctionCode.EIGENTUM_OBJEKT,
		DMSSpecialFunctionCode.EIGENTUM_KUECHE);

	public static final DMSFolderStructureType MIET = new DMSFolderStructureType(
		3, "Mietobjekt",
		DMSFolderType.BASIS_MIET,
		DMSSpecialFunctionCode.MIET_FOLDER,
			DMSSpecialFunctionCode.MIET_OBJEKT,
			DMSSpecialFunctionCode.MIET_KUECHE);

	public static final DMSFolderStructureType KLEINOBJEKT = new DMSFolderStructureType(
			4, "Kleinobjekt",
			DMSFolderType.BASIS_KLEINOBJEKT,
			DMSSpecialFunctionCode.KLEINOBJEKT_FOLDER,
			DMSSpecialFunctionCode.KLEINOBJEKT_OBJEKT,
			DMSSpecialFunctionCode.KLEINOBJEKT_KUECHE);

	public static final DMSFolderStructureType ABACUSOBJEKT = new DMSFolderStructureType(
			5, "Abacusobjekt",
			DMSFolderType.BASIS_ABACUS,
			DMSSpecialFunctionCode.ABACUSOBJEKT_FOLDER,
			DMSSpecialFunctionCode.ABACUSOBJEKT_OBJEKT,
			DMSSpecialFunctionCode.ABACUSOBJEKT_KUECHE);

	public static final DMSFolderStructureType MERXPROJEKTE = new DMSFolderStructureType(
			6, "Merx-Projekte",
			DMSFolderType.BASIS_MERX,
			DMSSpecialFunctionCode.MERXOBJEKT_FOLDER,
			DMSSpecialFunctionCode.NULL,
			DMSSpecialFunctionCode.MERXOBJEKT_KUECHE);

	public static final DMSFolderStructureType DEMOPROJEKTE = new DMSFolderStructureType(
			7, "Demo-Projekte",
		DMSFolderType.BASIS_DEMO,
		DMSSpecialFunctionCode.DEMOOBJEKT_FOLDER,
		DMSSpecialFunctionCode.NULL,
		DMSSpecialFunctionCode.NULL);

	private final DMSFolderType basisFolderType;
	private final DMSSpecialFunctionCode activeFolder;
	private final DMSSpecialFunctionCode basisTemplate;
	private final DMSSpecialFunctionCode kuechenTemplate;

	private DMSFolderStructureType(final int id, final String text,
		final DMSFolderType basisFolderType,
		final DMSSpecialFunctionCode activeFolder,
		final DMSSpecialFunctionCode basisTemplate,
		final DMSSpecialFunctionCode kuechenTemplate) {
		super(id, id == 0 ? null : Integer.valueOf(id), text, text);
		this.basisFolderType = basisFolderType;
		this.activeFolder = activeFolder;
		this.basisTemplate = basisTemplate;
		this.kuechenTemplate = kuechenTemplate;
	}

	/**
	 * Gets the DMSFolderStructureType associated with the given folder type.
	 */
	public static DMSFolderStructureType getByFolderType(final DMSFolderType folderType) {
		for (final DMSFolderStructureType code : getInstances(DMSFolderStructureType.class)) {
			if (code.getBasisFolderType().equals(folderType))
				return code;
		}
		return NULL;
	}

	/**
	 * Returns true if this folder structure consists of only a single "Küche"
	 */
	public boolean isSingular() {
		return isNull(basisTemplate);
	}

	/**
	 * Returns true if this folder structure has a "Basis" in the hierarchy above the "Küche(n)"
	 */
	public boolean hasBasisStructure() {
		return !isNull(basisTemplate);
	}

	public DMSFolderType getBasisFolderType() {
		return basisFolderType;
	}

	public DMSSpecialFunctionCode getActiveFolder() {
		return activeFolder;
	}

	public DMSSpecialFunctionCode getBasisTemplate() {
		return basisTemplate;
	}

	public DMSSpecialFunctionCode getKuechenTemplate() {
		return kuechenTemplate;
	}

}

package ch.eisenring.network;

import static ch.eisenring.network.implementation.ConnectionStatusCode.CON_CLOSED;
import static ch.eisenring.network.implementation.ConnectionStatusCode.CON_CONNECTING;

import java.io.IOException;
import java.net.Socket;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;

import ch.eisenring.core.datatypes.primitives.Primitives;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.StringMakerFriendly;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.threading.ThreadPool;
import ch.eisenring.network.implementation.ConnectionStatusCode;

public abstract class AbstractConnection implements NetworkConnection, StringMakerFriendly {

	private final static AtomicLong ID_GENERATOR = new AtomicLong();

	protected final Object LOCK = new Object();
	protected final AtomicReference<ConnectionStatusCode> status = new AtomicReference<>(CON_CONNECTING);
	protected final ConnectionProperties properties = new ConnectionProperties();

	protected final Socket socket;

	protected volatile long lastActive;
	protected Thread inputThread;
	
	// connection identity
	private final long creationTimestamp;
	private final long id;
	private final String name;

	protected AbstractConnection(final Socket socket, final SocketOptions options) throws IOException {
		this.id = ID_GENERATOR.incrementAndGet();
		this.creationTimestamp = System.currentTimeMillis();
		this.socket = socket;
		options.configure(socket);
		this.name = Strings.concat(Primitives.getSimpleName(getClass()), " #", id);
	}

	/**
	 * Gets the status of this connection
	 */
	public final ConnectionStatusCode getStatus() {
		return status.get();
	}

	/**
	 * Gets the lock for this connection
	 */
	public final Object getLock() {
		return LOCK;
	}

	/**
	 * Gets the id of this connection. The id is unique for this connection.
	 */
	public final long getId() {
		return id;
	}

	/**
	 * Gets the creation time stamp for this connection
	 */
	public final long getCreationTimestamp() {
		return creationTimestamp;
	}

	/**
	 * Checks if this connection is handshaked (initialized)
	 */
	public final boolean isHandshaked() {
		return ConnectionStatusCode.CON_ACTIVE.equals(status.get());
	}

	/**
	 * Checks if this connection is still alive.
	 */
	public final boolean isAlive() {
		synchronized (getLock()) {
			if (!status.get().isDead()) {
				if (!socket.isClosed())
					return true;
				terminate(CON_CLOSED, "Verbindung getrennt", null);
			}
			return false;
		}
	}

	/**
	 * Gets the time when this connection was last active
	 */
	public final long getLastActive() {
		return lastActive;
	}

	/**
	 * Gets the properties for this connection
	 */
	@Override
	public final ConnectionProperties getConnectionProperties() {
		return properties;
	}

	/**
	 * Starts the I/O threads for this connection.
	 * 
	 * This is not done automatically from the constructor to
	 * avoid causing trouble with partially initialized instance.
	 * The inheriting class needs to decide when and how to call it.
	 */
	protected final void startIO() {
		synchronized (getLock()) {
			if (inputThread != null || !isAlive())
				return;
			this.lastActive = System.currentTimeMillis();
			// --- launch packet input thread
			inputThread = getThreadPool().startDaemon(() -> { inputLoop(); },
					Strings.concat(name, ".inputLoop()"), Thread.MAX_PRIORITY);
		}
	}

	/**
	 * ThreadPool that is used when a thread is needed
	 */
	protected ThreadPool getThreadPool() {
		return ThreadPool.DEFAULT;
	}
	
	/**
	 * Implements internal packet receiving
	 */
	protected abstract void inputLoop();

	// --------------------------------------------------------------
	// ---
	// --- Status / Activity tracking
	// ---
	// --------------------------------------------------------------
	private final static AtomicInteger COUNT_SENDING = new AtomicInteger(0);
	private final static AtomicInteger COUNT_RECEIVING = new AtomicInteger(0);

	/**
	 * Returns true if any connection is currently sending data
	 */
	public final static boolean isSending() {
		return COUNT_SENDING.get() > 0;
	}
	
	/**
	 * Returns true if any connection is currently receiving data
	 */
	public final static boolean isReceiving() {
		return COUNT_RECEIVING.get() > 0;
	}

	/**
	 * Called when the "isSending" status of this connection changes
	 */
	protected void setSending(final boolean sending) {
		if (sending) {
			COUNT_SENDING.incrementAndGet();
		} else {
			COUNT_SENDING.decrementAndGet();
		}
	}
	
	/**
	 * Called when the "isReceiving" status of this connection changes
	 */
	protected void setReceiving(final boolean receiving) {
		if (receiving) {
			COUNT_RECEIVING.incrementAndGet();
		} else {
			COUNT_RECEIVING.decrementAndGet();
		}
	}

	// --------------------------------------------------------------
	// ---
	// --- Termination callback
	// ---
	// --------------------------------------------------------------
	private final AtomicBoolean connectionTeminateCalled = new AtomicBoolean();
	
	protected final void callConnectionTerminated() {
		if (connectionTeminateCalled.compareAndSet(false, true)) {
			connectionTerminated();
		}
	}

	/**
	 * Child classes implement this method to handle termination
	 * of the connection. Call getStatus() to get an indication of
	 * why the connection was terminated.
	 */
	protected void connectionTerminated() {
	}
	
	// --------------------------------------------------------------
	// ---
	// --- Statistics management
	// ---
	// --------------------------------------------------------------
	protected final ConnectionStreamStatistics statisticsSent = new ConnectionStreamStatistics();
	protected final ConnectionStreamStatistics statisticsReceived = new ConnectionStreamStatistics();
	protected final PingStatistics pingStatistics = new PingStatistics();

	/**
	 * Adds this connections statistics to the given throughput
	 */
	public final void updateThroughputStatistics(final ThroughputStatistics throughput) {
		if (throughput == null)
			return;
		// this should be protected against race conditions,
		// but since its not vital we don't.
		throughput.addSent(statisticsSent);
		throughput.addReceived(statisticsReceived);
	}

	/**
	 * Gets the ping statistics for this connection
	 */
	public final PingStatistics getPingStatistics() {
		return new PingStatistics(pingStatistics);
	}

	/**
	 * Updates the ping statistics for this connection
	 */
	public final void updatePing(final long pingMillis) {
		pingStatistics.setPing(pingMillis);
	}

	// --------------------------------------------------------------
	// ---
	// --- Object overrides
	// ---
	// --------------------------------------------------------------
	@Override
	public boolean equals(final Object o) {
		return this == o;
	}
	
	@Override
	public int hashCode() {
		return ((int) (id >> 32)) ^ ((int) id);
	}

	@Override
	public final String toString() {
		return name;
	}

	@Override
	public void toStringMaker(final StringMaker maker) {
		maker.append(name);
	}

}

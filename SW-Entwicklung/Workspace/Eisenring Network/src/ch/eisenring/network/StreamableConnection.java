package ch.eisenring.network;

import static ch.eisenring.network.implementation.ConnectionStatusCode.CON_ACTIVE;
import static ch.eisenring.network.implementation.ConnectionStatusCode.CON_CLOSED;
import static ch.eisenring.network.implementation.ConnectionStatusCode.CON_CONNECTING;
import static ch.eisenring.network.implementation.ConnectionStatusCode.CON_ERROR;
import static ch.eisenring.network.implementation.ConnectionStatusCode.CON_FATAL;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.Socket;
import java.net.SocketAddress;
import java.security.NoSuchAlgorithmException;

import javax.net.SocketFactory;
import javax.net.ssl.SSLContext;

import ch.eisenring.core.datatypes.primitives.Primitives;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.io.StreamBaseInterface;
import ch.eisenring.core.io.StreamException;
import ch.eisenring.core.io.StreamFormat;
import ch.eisenring.core.io.StreamReader;
import ch.eisenring.core.io.StreamWriter;
import ch.eisenring.core.io.StreamableTags;
import ch.eisenring.core.io.stats.StreamStats;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.core.tag.TagSet;
import ch.eisenring.core.threading.ThreadCore;
import ch.eisenring.core.threading.ThreadPool;
import ch.eisenring.core.util.ReflectionUtil;
import ch.eisenring.network.implementation.ConnectionStatusCode;
import ch.eisenring.network.packet.AbstractPacket;
import ch.eisenring.network.security.NetworkSecurityManager;

/**
 * Provides basic connection implementation for Packet oriented communication.
 * 
 * Deals with sending and receiving AbstractPacket objects.
 */
public abstract class StreamableConnection extends AbstractConnection implements PacketSink {

	protected final StreamReader reader;
	protected final StreamWriter writer;
	
	protected final PacketOutputQueue outputQueue;

	/**
	 * Generic constructor
	 */
	protected StreamableConnection(final Socket socket, final SocketOptions options) throws IOException {
		super(socket, options);
		this.writer = createStreamWriter(socket, options);
		this.reader = createStreamReader(socket, options);
		this.outputQueue = new PacketOutputQueue(this, ThreadPool.DEFAULT);
	}

	/**
	 * Creates the StreamWriter used on socket. Can be overwritten to modify the default
	 * creation behavior.
	 */
	protected StreamWriter createStreamWriter(final Socket socket, final SocketOptions options) throws IOException {
		return StreamFormat.SOCKET.createWriter(new TagSet(
				StreamableTags.SINK, createOutputStream(socket),
				StreamableTags.BUFFERSIZE, options.outputBufferSize));
	}

	/**
	 * Creates the StreamReader used on socket. Can be overwritten to modify the default
	 * creation behavior.
	 */
	protected StreamReader createStreamReader(final Socket socket, final SocketOptions options) throws IOException {
		return StreamFormat.createReader(new TagSet(
				StreamableTags.SOURCE, createInputStream(socket),
				StreamableTags.BUFFERSIZE, options.inputBufferSize));
	}

	/**
	 * Client side constructor
	 */
	protected StreamableConnection(final SocketAddress address,
								 final SocketFactory factory,
								 final SocketOptions options) throws IOException {
		this(createSocket(factory, address, options), options);
	}

	private static Socket createSocket(final SocketFactory factory,
			 final SocketAddress address,
			 final SocketOptions options) throws IOException {
		final Socket socket = factory.createSocket();
		options.configure(socket);
		socket.connect(address);
		return socket;
	}

	protected final Socket getSocket() {
		return socket;
	}

	/**
	 * Gets the input stream used to read from the socket
	 * 
	 * This method is intended to be called by the setup of the connection only,
	 * and only once. By default it simply returns socket.getInputStream(),
	 * but it can be overwritten to plug a wrapper in between if needed.
	 */
	protected InputStream createInputStream(final Socket socket) throws IOException {
		return socket.getInputStream();
	}

	/**
	 * Gets the output stream used to write to the socket.
	 * 
	 * This method is intended to be called by the setup of the connection only,
	 * and only once. By default it simply returns socket.getOutputStream(),
	 * but it can be overwritten to plug a wrapper in between if needed.
	 */
	protected OutputStream createOutputStream(final Socket socket) throws IOException {
		return socket.getOutputStream();
	}

	/**
	 * The implementation must be prepared to handle
	 * calls from any thread context, as the method can
	 * be called from either input or output loop. 
	 */
	protected final void packetReceived(final AbstractPacket packet) {
		if (getSecurityManager().isPacketAcceptable(packet)) {
			dispatchPacket(packet);
		} else {
			// the packet has been rejected by security manager, terminate the connection immediately
			final String message = Strings.concat("Security Violation: ", Primitives.getSimpleName(packet.getClass()), " not allowed on ", this);
			Logger.fatal(message);
			terminate(ConnectionStatusCode.CON_FATAL, message, null);
		}
	}

	/**
	 * Dispatches the packet to the appropriate handler.
	 * This is only called after the packet has passed the
	 * security manager.
	 */
	protected abstract void dispatchPacket(final AbstractPacket packet);

	// --------------------------------------------------------------
	// ---
	// --- Input loop
	// ---
	// --------------------------------------------------------------
	/**
	 * Input loop. Runs in a separate thread.
	 */
	@SuppressWarnings("unchecked")
	@Override
	protected final void inputLoop() {
		Logger.debug(" started");
		try {
			while (isAlive()) {
				final Class<? extends AbstractPacket> packetClass;
				long physicalPosition = reader.getPosition();
				try {
					reader.flush();
					packetClass = (Class<? extends AbstractPacket>) reader.readClass();
				} catch (final IOException e) {
					// if there was an error reading
					// the packet id, assume its a normal disconnect
					terminate(CON_CLOSED, "Verbindung getrennt", null);
					break;
				}
				// receive the packet
				final AbstractPacket packet;
				setReceiving(true);
				try {
					lastActive = System.currentTimeMillis();
					packet = ReflectionUtil.newInstance(packetClass);
					if (!getSecurityManager().isPacketAcceptable(packet)) {
						throw new IOException(Strings.concat("Security Violation: ", Primitives.getSimpleName(packet.getClass()), " not allowed on #", getId()));
					}
					packet.read(reader);
				} catch (final Exception e) {
					Logger.fatal(e);
					terminate(CON_ERROR, Strings.concat("Netzwerkfehler: ", e.getMessage()), e);
					break;
				} finally {
					setReceiving(false);
				}
				final int phySize = (int) (reader.getPosition() - physicalPosition);
				packet.setPacketByteSize(phySize);
				statisticsReceived.update(phySize);
				packetReceived(packet);
			}
		} catch (final Exception e) {
			Logger.fatal(Strings.concat("*** Unexpected Exception in ", StreamableConnection.this, ": ", getStatus()));
			Logger.fatal(e);
			terminate(CON_FATAL, Strings.concat("Nicht behandelte Ausnahme: ", e.getMessage()), e);
		} finally {
			Logger.debug(" terminating");
		}
	}

	// --------------------------------------------------------------
	// ---
	// --- PacketSink implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public final StreamableConnection getConnection() {
		return this;
	}

	@Override
	public void sendPacket(final AbstractPacket packet) {
		try {
			sendPacket(packet, (PacketCallback) null, false);
		} catch (final IOException e) {
			throw new RuntimeException(e.getMessage(), e);
		}
	}

	@Override
	public final void sendPacket(final AbstractPacket packet, final PacketCallback callback) {
		try {
			sendPacket(packet, callback, false);
		} catch (final IOException e) {
			throw new RuntimeException(e.getMessage(), e);
		}
	}

	@Override
	public final void sendPacket(final AbstractPacket packet, final boolean waitForSend) {
		try {
			sendPacket(packet, (PacketCallback) null, waitForSend);
		} catch (final IOException e) {
			throw new RuntimeException(e.getMessage(), e);
		}
	}

	/**
	 * Sends a packet over this connection.
	 * 
	 * Depending of the waitForSend parameter, the call blocks
	 * until the packet has been sent completely or just enqueues
	 * the packet and sends it as soon as possible.
	 * 
	 * If the callback parameter is not null, the callback
	 * will be invoked when the packet has been sent (or not sent
	 * due to an error).
	 */
	public final void sendPacket(final AbstractPacket packet,
								 final PacketCallback callback,
								 final boolean waitForSend) throws IOException {
		// if the packet is null, just indicate the error to the callback,
		// but do not terminate the connection.
		if (packet == null) {
			if (callback != null) {
				final Throwable e = new NullPointerException("packet is null");
				e.fillInStackTrace();
				callback.packetError(null, this, e);
			}
			return;
		}
		
		// if the connection is not live any more, indicate error to the
		// callback, then throw on the callers thread as well.
		if (!isAlive()) {
			final StreamException e = new StreamException("connection already closed");
			e.fillInStackTrace();
			if (callback != null) {
				callback.packetError(packet, this, e);
			}
			throw e;
		}
		if (waitForSend) {
			// send packet synchronous (blocks until the entire packet has been written)
			// if an error occurs, an exception will be thrown on the callers thread.
			while (isAlive()) {
				if (outputQueue.isEmpty())
					break;
				ThreadCore.randomRetryWait();
			}
			writePacket(packet, callback, true);
		} else {
			outputQueue.enqueue(packet, callback);
		}
	}

	/**
	 * Writes the packet to the sockets output.
	 * This is a synchronous method (blocks while writing)
	 */
	protected boolean writePacket(final AbstractPacket packet,
			                      final PacketCallback callback,
			                      final boolean flushOutput) {
		boolean success = true;
		synchronized (getLock()) {
			try {
				try {
					setSending(true);
					final long phyPos = writer.getPosition();
					packet.write(writer);
					if (flushOutput)
						writer.flush();
					final int phySize = (int) (writer.getPosition() - phyPos);
					packet.setPacketByteSize(phySize);
					statisticsSent.update(phySize);
				} finally {
					lastActive = System.currentTimeMillis();
					setSending(false);
				}
				if (!packet.isSilent() && Logger.isDebugEnabled()) {
					Logger.debug(Strings.concat(" packet sent: ", packet, " on #", getId()));
				}
				if (callback != null) {
					try {
						callback.packetSent(packet, this);
					} catch (final Exception e) {
						// the callback caused an exception, terminate connection
						terminate(CON_ERROR, Strings.concat("Fehler: ", e.getMessage()), e);
						success = false;
					}
				}
			} catch (final Exception e) {
				try {
					if (callback != null)
						callback.packetError(packet, this, e);
				} finally {
					terminate(CON_ERROR, Strings.concat("Netzwerkfehler: ", e.getMessage()), e);
					success = false;
				}
			} finally {
				// trigger any actions mandated for post-send by packet capabilities
				AbstractPacket.postSend(packet);
			}
		}
		return success;
	}

	// --------------------------------------------------------------
	// ---
	// --- Status handling / termination
	// ---
	// --------------------------------------------------------------
	/**
	 * Advances the connection state to handshaked. This is only
	 * allowed if the connection is in state connecting.
	 * 
	 * If setActive() is called in the wrong state, it will
	 * terminate the connection (if its is even alive) 
	 * and throw IllegalStateException.
	 */
	public final void setHandshaked() throws IllegalStateException {
		if (!status.compareAndSet(CON_CONNECTING, CON_ACTIVE)) {
			final String message = Strings.concat("setActive() called in status ", status.get());
			final IllegalStateException error = new IllegalStateException(message);
			error.fillInStackTrace();
			terminate(CON_ERROR, message, error);
			throw error;
		}
	}

	/**
	 * Terminates the connection.
	 * 
	 * This will shut down the underlying network socket
	 * (more or less gracefully).
	 */
	@Override
	public final void terminate(final ConnectionStatusCode terminationCode,
			                    final String statusMessage,
			                    final Throwable terminationCause) {
		synchronized (getLock()) {
			boolean interruptPending = Thread.interrupted();
			final ConnectionStatusCode status = this.status.get();
			if (!CON_ACTIVE.equals(status) && !CON_CONNECTING.equals(status)) {
				// connection is already dead
				return;
			}
			
			this.status.set(terminationCode);
			
			// terminate all threads of this connection
			try {
				inputThread.interrupt();
			} catch (final Exception e) {}

			// flush remaining output and close streams/socket
			try {
				writer.flush();
			} catch (final Exception e) {}
			try {
				Thread.sleep(200);
			} catch (final InterruptedException e) {
				interruptPending = true;
			}
			try {
				socket.shutdownInput();
			} catch (final Exception e) {}
			try {
				writer.flush();
			} catch (final Exception e) {}
			try {
				socket.close();
			} catch (final Exception e) {}

			// invoke the callback for terminate
			try {
				callConnectionTerminated();
			} catch (final Exception e) {}

			// restore interrupt status
			if (interruptPending)
				Thread.currentThread().interrupt();
		}
	}

	/**
	 * Handles exception not caught by the client inputThread.
	 * Child classes may overwrite it to handle unexpected
	 * termination.
	 * 
	 * The default implementation logs the exception, then
	 * calls terminate with status fatal.
	 */
	protected void unexpectedException(final Thread t, final Throwable e) {
		Logger.fatal(Strings.concat("*** Unexpected Exception in ", this, ": ", getStatus()));
		Logger.fatal(e);
		terminate(CON_FATAL, Strings.concat("Nicht behandelte Ausnahme: ", e.getMessage()), e);
	}
	
	/**
	 * Gets the remote address this socket is connected to
	 */
	public final String getRemoteAddress() {
		try {
			if (socket.isBound()) {
				final SocketAddress address = socket.getRemoteSocketAddress();
				return address.toString();
			} else {
				return "<not connected>";
			}
		} catch (final Exception e) {
			return "<error>";
		}
	}

	// --------------------------------------------------------------
	// ---
	// --- Output handling
	// ---
	// --------------------------------------------------------------
	/**
	 * Checks if this connection has all given compatibility mask bits set
	 */
	public abstract boolean isCompatible(final int compatibilityMask);

	/**
	 * Checks if this connection is compatibly with the given packet
	 */
	public boolean isCompatible(final AbstractPacket packet) {
		if (packet == null)
			return false;
		return isCompatible(packet.getCompatibilityMask());
	}

	/**
	 * Gets the security manager for this connection
	 */
	public abstract NetworkSecurityManager getSecurityManager();

	/**
	 * Attempts to get SSLContext of specified algorithm(s).
	 * 
	 * The algorithms are tried from left to right, returning the first that is available,
	 * so more secure ones should be placed leftmost.
	 */
	public static SSLContext getSSLContext(final String ... algos) throws NoSuchAlgorithmException {
		for (int i=0; i<algos.length; ++i) {
			final String algo = algos[i];
			try {
				final SSLContext result = SSLContext.getInstance(algo);
				Logger.info("Created SSLContext using {}", algo);
				return result;
			} catch (final NoSuchAlgorithmException e) {
				// only throw if last algorithm
				if (i+1 >= algos.length)
					throw e;
			}
		}
		throw new NoSuchAlgorithmException("unreachable code");
	}

	// --------------------------------------------------------------
	// ---
	// --- Statistics handling
	// ---
	// --------------------------------------------------------------
	@Override
	public StreamStats getReadStatistics() {
		final StreamBaseInterface stream = this.reader;
		return stream == null ? null : stream.getStatistics();
	}

	@Override
	public StreamStats getWriteStatistics() {
		final StreamBaseInterface stream = this.writer;
		return stream == null ? null : stream.getStatistics();
	}

}

package ch.eisenring.network;

import ch.eisenring.core.collections.Collection;
import ch.eisenring.core.feature.FeatureLookup;
import ch.eisenring.network.handler.PacketHandler;
import ch.eisenring.network.packet.AbstractPacket;

/**
 * Packet dispatcher implementation based on feature lookup
 */
public final class FeaturePacketDispatcher implements PacketDispatcher {

	private final FeatureLookup featureLookup;

	public FeaturePacketDispatcher(final FeatureLookup featureLookup) {
		this.featureLookup = featureLookup;
	}

	@SuppressWarnings("unchecked")
	@Override
	public PacketHandler<AbstractPacket> getPacketHandler(final AbstractPacket packet) {
		final Class<? extends AbstractPacket> packetClass = packet.getClass();
		final Collection<PacketHandler> handlers = featureLookup.getFeatures(PacketHandler.class);
		for (final PacketHandler<AbstractPacket> handler : handlers) {
			if (packetClass.equals(handler.getPacketClass()))
				return handler;
		}
		return null;
	}
	
}

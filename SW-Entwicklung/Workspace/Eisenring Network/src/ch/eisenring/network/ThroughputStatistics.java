package ch.eisenring.network;

import java.io.IOException;

import ch.eisenring.core.io.StreamReader;
import ch.eisenring.core.io.StreamWriter;
import ch.eisenring.core.io.Streamable;

/**
 * Statistics object used for counting throughput.
 */
public class ThroughputStatistics implements Streamable {

	private long packetsSent;
	private long bytesSent;
	private long packetsReceived;
	private long bytesReceived;

	/**
	 * Default constructor. All statistic values set to zero.
	 */
	public ThroughputStatistics() {
	}
	
	/**
	 * Copy constructor
	 */
	public ThroughputStatistics(final ThroughputStatistics source) {
		if (source != null) {
			this.packetsSent = source.packetsSent;
			this.bytesSent = source.bytesSent;
			this.packetsReceived = source.packetsReceived;
			this.bytesReceived = source.bytesReceived;
		}
	}

	public ThroughputStatistics(final ConnectionStreamStatistics sent, final ConnectionStreamStatistics received) {
		if (sent != null) {
			this.packetsSent = sent.getPacketCount();
			this.bytesSent = sent.getByteCount();
		}
		if (received != null) {
			this.packetsReceived = received.getPacketCount();
			this.bytesReceived = received.getByteCount();
		}
	}

	public void addSent(final ConnectionStreamStatistics source) {
		if (source != null) {
			this.packetsSent += source.getPacketCount();
			this.bytesSent += source.getByteCount();
		}
	}

	public void addReceived(final ConnectionStreamStatistics source) {
		if (source != null) {
			this.packetsReceived += source.getPacketCount();
			this.bytesReceived += source.getByteCount();
		}
	}

	/**
	 * Adds the given TroughPutStatistics to this one.
	 */
	public void addThroughput(final ThroughputStatistics source) {
		if (source != null) {
			this.packetsSent += source.packetsSent;
			this.bytesSent += source.bytesSent;
			this.packetsReceived += source.packetsReceived;
			this.bytesReceived += source.bytesReceived;
		}
	}

	/**
	 * Resets all counters to zero.
	 */
	public void reset() {
		this.packetsSent = 0;
		this.bytesSent = 0;
		this.packetsReceived = 0;
		this.bytesReceived = 0;
	}

	// --------------------------------------------------------------
	// ---
	// --- Simple Getters
	// ---
	// --------------------------------------------------------------
	public final long getPacketsSent() {
		return packetsSent;
	}

	public final long getBytesSent() {
		return bytesSent;
	}

	public final long getPacketsReceived() {
		return packetsReceived;
	}

	public final long getBytesReceived() {
		return bytesReceived;
	}

	// --------------------------------------------------------------
	// ---
	// --- Streamable implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public void read(final StreamReader reader) throws IOException {
		packetsSent = reader.readLong();
		bytesSent = reader.readLong();
		packetsReceived = reader.readLong();
		bytesReceived = reader.readLong();
	}

	@Override
	public void write(final StreamWriter writer) throws IOException {
		writer.writeLong(packetsSent);
		writer.writeLong(bytesSent);
		writer.writeLong(packetsReceived);
		writer.writeLong(bytesReceived);
	}

}

package ch.eisenring.network;

import java.lang.reflect.Constructor;

import ch.eisenring.core.datatypes.primitives.Primitives;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.network.handler.PacketHandler;
import ch.eisenring.core.util.ReflectionUtil;
import ch.eisenring.network.packet.AbstractPacket;

public abstract class DynamicPacketDispatcher extends DynamicPacketDispatcherBase {

	protected final static String POSTFIX = "Handler";
	protected final String PREFIX;
	protected final Class<?>[] SIGNATURE = new Class[1];
	protected final Object[] ARGS = new Object[1];
	
	protected DynamicPacketDispatcher(final Object component) {
		PREFIX = getPackagePrefix(getClass());
		SIGNATURE[0] = component.getClass();
		ARGS[0] = component;
	}

	public DynamicPacketDispatcher(final Object component, final Class<?> locatorClass) {
		PREFIX = getPackagePrefix(locatorClass);
		SIGNATURE[0] = component.getClass();
		ARGS[0] = component;
	}
	
	private static String getPackagePrefix(final Class<?> aClass) {
		final String name = aClass.getName();
		final int i = Strings.lastIndexOf(name, '.');
		if (i < 0)
			return "";
		return Strings.subString(name, 0, i + 1);
	}

	@Override
	protected final PacketHandler<AbstractPacket> loadHandler(final Class<? extends AbstractPacket> packetClass) {
		// check if we can find a suitable handler 
		final String simpleName = Primitives.getSimpleName(packetClass);
		if (!simpleName.startsWith("Packet"))
			return null;
		final StringMaker className = StringMaker.obtain();
		className.append(PREFIX);
		className.append(simpleName, 6, simpleName.length());
		className.append(POSTFIX);
		return loadHandler(className.release(), SIGNATURE, ARGS);
	}	

	/**
	 * Attempts to load the specified class, returns null if not available.
	 */
	@SuppressWarnings("unchecked")
	protected final PacketHandler<AbstractPacket> loadHandler(final String handlerClassName,
			final Class<?>[] constructorSignature, final Object[] constructorArgs) {
		try {
			final Class<?> handlerClass = Primitives.classForName(handlerClassName);
			final Constructor<?> constructor = handlerClass.getDeclaredConstructor(constructorSignature);

			// Java 21 compatible accessibility check and setting
			if (!ReflectionUtil.isConstructorAccessible(constructor)) {
				constructor.setAccessible(true);
			}

			final PacketHandler<AbstractPacket> handler = (PacketHandler<AbstractPacket>) constructor.newInstance(constructorArgs);
			if (Logger.isTraceEnabled())
				Logger.trace(Strings.concat("loaded PacketHandler '", handlerClassName, "'"));
			return handler;
		} catch (final Exception e) {
			// discard any exception from a failed load of class silently
			return null;
		} catch (final LinkageError e) {
			// must catch this one separately as its not inherited from exception
			return null;
		}
	}

}

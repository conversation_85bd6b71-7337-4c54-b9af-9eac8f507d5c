package ch.eisenring.network.packet;

import ch.eisenring.network.security.SecurityDomain;

/**
 * This packet is sent when the server doesn't accept the
 * connection for whatever reason.
 * 
 * This packet contains no data.
 *
 * Only the server can send this packet.
 * If the client sends it, the server initiates
 * protocol violation (which terminates the connection, too).
 */
public final class PacketConnectionRejected extends AbstractPacket {

	private PacketConnectionRejected() {
		super(CAPS_SYNCHRONOUS, PRIORITY_MAX);
	}
	
	public static PacketConnectionRejected create() {
		return new PacketConnectionRejected();
	}

	// --------------------------------------------------------------
	// ---
	// --- AbstractPacket implementation & overrides
	// ---
	// --------------------------------------------------------------
	@Override
	public SecurityDomain getSecurityDomain() {
		return PUBLIC_DOMAIN;
	}

}

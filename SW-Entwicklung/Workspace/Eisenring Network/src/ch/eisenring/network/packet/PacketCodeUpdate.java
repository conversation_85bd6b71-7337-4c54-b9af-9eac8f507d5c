package ch.eisenring.network.packet;

import java.io.IOException;

import ch.eisenring.core.codetype.CodeType;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.io.StreamReader;
import ch.eisenring.core.io.StreamWriter;
import ch.eisenring.network.security.SecurityDomain;

/**
 * Used to transfer codes loaded from database to clients
 */
public final class PacketCodeUpdate extends AbstractPacket {

	private transient List<CodeType> codeList = new ArrayList<>();

	private PacketCodeUpdate() {
		super(CAPS_SYNCHRONOUS, PRIORITY_MAX);
	}

	public static PacketCodeUpdate create(final java.util.Collection<CodeType> typeList) {
		final PacketCodeUpdate packet = new PacketCodeUpdate();
		packet.codeList.addAll(typeList);
		return packet;
	}
	
	// --------------------------------------------------------------
	// ---
	// --- AbstractPacket implementation & overrides
	// ---
	// --------------------------------------------------------------
	@Override
	public SecurityDomain getSecurityDomain() {
		return PUBLIC_DOMAIN;
	}

	@Override
	protected void readImpl(final StreamReader reader) throws IOException {
		super.readImpl(reader);
		int count = reader.readInt();
		codeList.clear();
		codeList.ensureCapacity(count);
		while (--count >= 0) {
			final CodeType codeType = CodeType.read(reader, false);
			if (codeType != null)
				codeList.add(codeType);
		}
	}
	
	@Override
	protected void writeImpl(final StreamWriter writer) throws IOException {
		super.writeImpl(writer);
		final int count = codeList.size();
		writer.writeInt(count);
		for (int i=0; i <count; ++i) {
			final CodeType codeType = codeList.get(i);
			codeType.write(writer);
		}
	}

}

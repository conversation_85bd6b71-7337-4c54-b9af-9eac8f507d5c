package ch.eisenring.network.packet;

import java.io.IOException;
import java.util.concurrent.atomic.AtomicInteger;

import javax.security.auth.Destroyable;

import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.codetable.MessageClassCode;
import ch.eisenring.core.crypt.CryptUtil;
import ch.eisenring.core.datatypes.primitives.Primitives;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.StringMakerFriendly;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.datatypes.strings.format.api.Formats;
import ch.eisenring.core.interfaces.Disposable;
import ch.eisenring.core.io.StreamReader;
import ch.eisenring.core.io.StreamWriter;
import ch.eisenring.core.io.StreamableDescription;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.core.logging.Logger.LogLevel;
import ch.eisenring.core.util.ReflectionUtil;
import ch.eisenring.network.implementation.PacketDispatchMode;
import ch.eisenring.network.security.SecurityDomain;

public abstract class AbstractPacket implements StringMakerFriendly {

	/**
	 * Identifies the public domain (public domain is always allowed)
	 */
	public final static SecurityDomain PUBLIC_DOMAIN = SecurityDomain.get("PUBLIC");

	/**
	 * Identifies the administrative domain
	 */
	public final static SecurityDomain ADMIN_DOMAIN = SecurityDomain.get("ADMIN");

	/**
	 * Constant signifying no sequence number support
	 */
	protected final static int CAPS_NO_SEQUENCE_SUPPORT = 0;

	/**
	 * Constant signifying request id number support
	 */
	protected final static int CAPS_SEQUENCE_REQUEST = 1;

	/**
	 * Constant signifying reply id number support
	 */
	protected final static int CAPS_SEQUENCE_REPLY = 2;

	/** 
	 * Constant for error message support.
	 */
	protected final static int CAPS_ERRORMESSAGE = 4;

	/** Constant for a broadcast packet */
	protected final static int CAPS_BROADCAST = 0x10;

	/** Constant for a synchronous packet */
	protected final static int CAPS_SYNCHRONOUS = 0x20;

	/**
	 * Constant for packet that needs to be destroyed for security after sending/processing.
	 * 
	 * For this flag to take effect, the packet needs to implement java.security.auth.Destroyable.
	 * destroy() will be called when the packet has been sent, or on the receiving end after
	 * the dispatch chain has completed. Do not set this flag for broadcast packets.
	 */
	protected final static int CAPS_SECUREDESTROY = 0x40;

	/** Constant for silent (no logging) packet */
	protected final static int CAPS_SILENT = 0x80;

	/** 
	 * Indicates the packet shall be explicitly disposed
	 * immediately after sending. This can be used to ensure memory
	 * or other resources held by a heavyweight packet are freed.
	 * Packet must implement Disposable, or it will have no effect.
	 */
	protected final static int CAPS_DISPOSE_AFTER_SEND = 0x100;
	//protected final static int CAPS_DISPOSE_AFTER_DISPATCH = 0x200; <-- NOT IMPLEMENTED
	
	/**
	 * Basic standard packet priority 
	 */
	public final static int PRIORITY_DEFAULT = 0;
	
	public final static int PRIORITY_MIN = -128;
	
	public final static int PRIORITY_MAX = 127;
	
	/**
	 * Priority used by request packets 
	 */
	public final static int PRIORITY_REQUEST = 0;
	
	/**
	 * Priority used by notification packets 
	 */
	public final static int PRIORITY_NOTIFY = 5;

	/**
	 * Priority used by standard reply packets 
	 */
	public final static int PRIORITY_REPLY = 10;

	/**
	 * Stores the byte size the packet had on the network.
	 * For packets not yet sent, this is -1.
	 */
	private transient int packetByteSize = -1;

	/**
	 * Holds packet caps and packet priority. Must be set from the constructor
	 */
	private transient final int capabilities;

	/**
	 * Constructor
	 */
	protected AbstractPacket(final int caps, final int priority) {
		int p = Math.min(Math.max(priority, PRIORITY_MIN), PRIORITY_MAX);
		this.capabilities = (p << 24) | (caps & 0x00FF_FFFF);
		// ensure capabilities are matched with actual packet class
		if (false) {
			if ((capabilities & CAPS_SECUREDESTROY) != 0 && !(this instanceof Destroyable))
				throw new ExceptionInInitializerError("SecureDestroy packet must implement Destroyable");
			if ((capabilities & CAPS_DISPOSE_AFTER_SEND) != 0 && !(this instanceof Disposable))
				throw new ExceptionInInitializerError("DisposeAfterSend packet must implement Disposable");
		}
	}

	/**
	 * Gets the packets dispatch mode.
	 * The packet handler may choose a more conservative mode,
	 * but should never use a more relaxed policy.
	 * 
	 * Whenever possible, packets should specify the least restrictive
	 * dispatch mode: ASYNCHRONOUS, this allows the packet to be
	 * processed concurrently with other network traffic.
	 */
	public final PacketDispatchMode getPacketDispatchMode() {
		return (capabilities & CAPS_SYNCHRONOUS) == 0 ? PacketDispatchMode.ASYNCHRONOUS : PacketDispatchMode.SYNCHRONOUS;
	}

	/**
	 * Gets the packet name (for debugging purposes).
	 * 
	 * The default implementation returns the simple class name.
	 */
	public String getPacketName() {
		return Primitives.getSimpleName(getClass());
	}

	/**
	 * Returns true if this packet is a broadcast packet
	 */
	public final boolean isBroadcast() {
		return (capabilities & CAPS_BROADCAST) != 0;
	}

	/**
	 * Returns true if this packet is a secure packet.
	 */
	public final boolean isSecureDestroy() {
		return (capabilities & CAPS_SECUREDESTROY) != 0;
	}

	/**
	 * Returns true if this packet is to be treated silently
	 */
	public final boolean isSilent() {
		return (capabilities & CAPS_SILENT) != 0;
	}

	/**
	 * A priority value, the higher the more preference is given
	 * to the packet when sending.
	 * When the transport channel is busy and multiple packets
	 * are queued, packets will be sent according to their priority.
	 * Packets of the same priority are to be sent in the order
	 * in which they were enqueued (guaranteed).
	 * 
	 * This is only a hint, the transport layer is not required to
	 * honor it, its also perfectly acceptable if all packets are
	 * sent strictly in chronological order.
	 */
	public final int getPacketPriority() {
		return capabilities >> 24;
	}

	/**
	 * A compatibility flag mask, indicating to which application
	 * the packet belongs. Packets should only be delivered/broadcast
	 * to connections that have the compatibility bits of the
	 * packet set.
	 */
	public int getCompatibilityMask() {
		// 0 means compatible with all applications
		return 0;
	}

	/**
	 * Gets the byte size the packet had on the network.
	 * For packets not sent yet, this returns -1.
	 */
	public final int getPacketByteSize() {
		return packetByteSize;
	}

	/**
	 * Sets the byte size the packet had on the network.
	 * The value is intended to be set by the connection
	 * sending/receiving the packet.
	 */
	public final void setPacketByteSize(final int byteSize) {
		this.packetByteSize = byteSize;
	}
	
	/**
	 * Gets the SecurityDomain this packet belongs to 
	 */
	public abstract SecurityDomain getSecurityDomain();

	/**
	 * Implements writing the packet to the given writer.
	 * The method writes first some identification info
	 * that allows the receiver to determine what kind of
	 * packet it is and how it need to be read.
	 * Then, the packet specific data is appended using
	 * writeImpl(). 
	 */
	public final void write(final StreamWriter writer) throws IOException {
		writer.writeClass(getClass());
		// code blocks are needed to reduce local variable scope!
		{
			final int capabilities = getCapabilities();
			if ((capabilities & CAPS_SEQUENCE_REQUEST) != 0)
				writer.writeInt(getSequenceId());
			if ((capabilities & CAPS_SEQUENCE_REPLY) != 0)
				writer.writeInt(getReplyId());
			if ((capabilities & CAPS_ERRORMESSAGE) != 0) {
				// write error message (do not use writeObject for optimal stream size/performance)
				if (message == null) {
					writer.writeString(null);
				} else {
					writer.writeString(message.getText());
					writer.writeCode(message.getSeverity());
				}
			}
		} {
			final StreamableDescription description = StreamableDescription.getDescription(getClass());
			description.write(writer, this);
		}
		writeImpl(writer);
		writer.flushBuffers(); // important! bit-packing streams absolutely need this to work
	}

	/**
	 * Reads the packet from the given reader.
	 */
	public final void read(final StreamReader reader) throws IOException {
		// code blocks are needed to reduce local variable scope!
		{
			final int capabilities = getCapabilities();
			if ((capabilities & CAPS_SEQUENCE_REQUEST) != 0)
				sequenceId = reader.readInt();
			if ((capabilities & CAPS_SEQUENCE_REPLY) != 0)
				replyId = reader.readInt();
			if ((capabilities & CAPS_ERRORMESSAGE) != 0) {
				final String text = reader.readString(ErrorMessage.STRING_CACHE);
				if (text == null) {
					message = null;
				} else {
					final MessageClassCode severity = reader.readCode(MessageClassCode.class);
					message = new ErrorMessage(severity, text);
				}
			}
		} {
			final StreamableDescription description = StreamableDescription.getDescription(getClass());
			description.read(reader, this);
		}
		readImpl(reader);
		reader.flush(); // important! bit-packing streams absolutely need this to work
	}
	
	public final static <T extends AbstractPacket> T create(final Class<T> packetClass) throws IOException {
		try {
			return ReflectionUtil.newInstance(packetClass);
		} catch (final Exception e) {
			throw new IOException(e.getMessage(), e);
		}
	}

	/**
	 * Child classes overwrite this to write data specific
	 * for their class. May be left empty if the packet
	 * requires no specific data.
	 * Nested packet inheritance hierarchies need to take
	 * care when and if they need to call super.
	 */
	protected void writeImpl(final StreamWriter writer) throws IOException {
		// empty default implementation
	}

	/**
	 * Child classes overwrite this to read the data specific
	 * for their class. May be left empty if the packet
	 * requires no specific data.
	 * Nested packet inheritance hierarchies need to take
	 * care when and if they need to call super.
	 */
	protected void readImpl(final StreamReader reader) throws IOException {
		// empty default implementation
	}

	/**
	 * Handles post-send actions for a packet
	 */
	public static void postSend(final AbstractPacket packet) {
		if (packet == null)
			return;
		final int caps = packet.capabilities;
		// secure destroy if flagged
		if ((caps & CAPS_SECUREDESTROY) != 0) {
			if (Logger.isDebugEnabled())
				Logger.debug("destroying secure packet (postSend): {}", packet);
			CryptUtil.destroy(packet);
		}
		// dispose if flagged
		if ((caps & CAPS_DISPOSE_AFTER_SEND) != 0) {
			if (Logger.isDebugEnabled())
				Logger.debug("disposing (postSend): {}", packet);
			Disposable.dispose(packet);
		}
	}

	/**
	 * Destroys a packet, if its marked as SECURE
	 */
	public static void destroySecure(final AbstractPacket packet) {
		if (packet == null || (packet.capabilities & CAPS_SECUREDESTROY) == 0)
			return;
		if (Logger.isDebugEnabled())
			Logger.debug(Strings.concat("destroying secure packet: ", packet));
		CryptUtil.destroy(packet);
	}
	
//	/**
//	 * Called to describe the packet.
//	 * Each override is supposed to call super and then
//	 * add its own fields to the passed StreamableDescription.
//	 */
//	protected void describe(final StreamableDescription description) {
//		// no fields at this level
//	}

	// --------------------------------------------------------------
	// ---
	// --- Error message support
	// ---
	// --------------------------------------------------------------
	private final static ErrorMessage NULL_MESSAGE = new ErrorMessage(MessageClassCode.ERROR,
			"No error message set. Packet has probably not been initialized properly.");

	private transient ErrorMessage message;
	
	/**
	 * Gets the error message from this packet.
	 * 
	 * If the packet has not enabled error message support, this will return
	 * ErrorMessage.OK (pretending everything is fine). This allows error
	 * message checking code to work with packets that can not provide an
	 * error message.
	 * 
	 * If the message is NULL (has never been set), will return ErrorMessage.ERROR.
	 * This facilitates detection of partially/uninitialized packets, since they
	 * always signal a generic error.
	 */
	public final ErrorMessage getMessage() {
		// if the packet does not support error message, return OK
		if ((getCapabilities() & CAPS_ERRORMESSAGE) == 0)
			return ErrorMessage.OK;
		// if message was never set, signal generic error
		return message == null ? NULL_MESSAGE : message;
	}

	/**
	 * Sets the error message on this packet.
	 * Throws UsupportedOperationException if the concrete class has not enabled
	 * error message support (CAP_ERRORMESSAGE).
	 */
	public final void setMessage(final ErrorMessage message) throws UnsupportedOperationException {
		if ((getCapabilities() & CAPS_ERRORMESSAGE) == 0) {
			throw new UnsupportedOperationException(Strings.concat("packet ", this, " does not support error messages"));
		}
		this.message = message;
	}

	/**
	 * Returns true is this packet has an error message with class OK.
	 * If the packet does not support error messages, this always returns true 
	 * (pretends everything is ok). 
	 */
	public final boolean isValid() {
		return getMessage().isSuccess();
	}

	// --------------------------------------------------------------
	// ---
	// --- Sequence number support
	// ---
	// --------------------------------------------------------------
	private final static int INVALID_SEQUENCE_ID = 0;
	private final static AtomicInteger SEQUENCE_GENERATOR = new AtomicInteger();

	private static int getNextSequenceId() {
		int result;
		do {
			result = SEQUENCE_GENERATOR.getAndIncrement();
		} while (!isValidSequenceId(result));
		return result;
	}

	/**
	 * The sequence id assigned to this packet. The sequence id is a pseudo-unique
	 * number that identifies this packet. The id is lazily assigned, depending
	 * on if isSequenceNumberSupported() returns true.
	 */
	private transient volatile int sequenceId;
	
	/**
	 * The reply id of this packet. The reply id is the sequence number of the packet
	 * that triggered the creation of this packet. The packet handler for the triggering
	 * packet needs to assign that number explicitly to this packet, otherwise it will
	 * be zero (denoting this packet is not in reply to a specific packet).
	 */
	private transient volatile int replyId;

	/**
	 * Returns the packet capabilities.
	 * A child class may overwrite this to return a different bit set.
	 */
	public final int getCapabilities() {
		return capabilities;
	}

	/**
	 * Gets the sequence id of this packet.
	 * Sequence id's are pseudo-unique per JVM, but not across multiple
	 * JVM's. For a client/server relation this means that
	 * packets generated by the client may have the same sequence
	 * id as packets generated by the server.
	 * 
	 * Only packets that have CAPS_SEQUENCE_REQUEST have a valid sequence id.
	 * (the id can be checked with isValidSequenceId())
	 */
	public final int getSequenceId() {
		int sequenceId = this.sequenceId;
		if (!isValidSequenceId(sequenceId) && (getCapabilities() & CAPS_SEQUENCE_REQUEST) != 0) {
			this.sequenceId = sequenceId = getNextSequenceId();
		}
		return sequenceId;
	}
	
	/**
	 * Gets the sequence id of the packet that triggered
	 * the creation of this packet. Only packets sent in
	 * reply to another packet will have a valid reply id.
	 * 
	 * Only packets that have CAPS_SEQUENCE_REPLY have a valid sequence id.
	 * (the id can be checked with isValidSequenceId())
	 */
	public final int getReplyId() {
		return replyId;
	}

	/**
	 * Sets the reply id and message
	 */
	protected final void setReplyId(final AbstractPacket request, final ErrorMessage message) {
		setReplyId(request);
		setMessage(message);
	}
	
	/**
	 * Sets the reply id from a request packet.
	 */
	protected final void setReplyId(final AbstractPacket request) {
		// ensure the request supports CAPS_SEQUENCE_REQUEST
		// (and supplies really a valid id)
		if (request == null)
			throw new RuntimeException("request must not be NULL");
		if ((request.getCapabilities() & CAPS_SEQUENCE_REQUEST) == 0)
			throw new RuntimeException(Strings.concat(request, " does not supply an id"));
		final int id = request.getSequenceId();
		if (!isValidSequenceId(id))
			throw new RuntimeException(Strings.concat(request, " has an invalid sequence id"));
		// ensure this packet supports CAPS_SEQUENCE_REPLY
		if ((this.getCapabilities() & CAPS_SEQUENCE_REPLY) == 0)
			throw new RuntimeException(Strings.concat(this, " does not support CAPS_SEQUENCE_REPLY"));
		this.replyId = id;
	}

	/**
	 * Checks a reply id represents a valid id.
	 */
	public static boolean isValidSequenceId(final int replyId) {
		return replyId != INVALID_SEQUENCE_ID;
	}

	// --------------------------------------------------------------
	// ---
	// --- Compatibility allocation helper
	// ---
	// --------------------------------------------------------------
	private final static AtomicInteger NEXT_COMPATIBILITY_MASK = new AtomicInteger(0x80000000);

	/**
	 * Allocates a new bit in the packet's compatibility mask
	 * for exclusive use by an application.
	 * This simplifies server side handling, as compatibility
	 * mask bits can be assigned in an automated way instead
	 * of ugly (and error prone) magic constants everywhere.
	 * 
	 * Note that the mask bit allocated can vary between JVM's,
	 * so the compatibility mask is only useful within the VM
	 * it has been allocated in. Particularly client and server
	 * bit allocations will almost always be different, since
	 * the server usually contains more components.
	 */
	public static int allocateCompatibilityMaskBit() {
		int result, mask;
		do {
			result = NEXT_COMPATIBILITY_MASK.get();
			mask = result >>> 1;
			if (mask == 0) {
				// this should not happen in my lifetime...
				Logger.log(LogLevel.SYSTEM, "The system has exhausted all available packet compatibility bits. " +
						"\nThis indicates a general class loading fuckup or the use of too many server components.");
				System.exit(20);
			}
		} while (!NEXT_COMPATIBILITY_MASK.compareAndSet(result, mask));
		return result;
	}

	// --------------------------------------------------------------
	// ---
	// --- Object overrides
	// ---
	// --------------------------------------------------------------
	@Override
	public final String toString() {
		return StringMakerFriendly.toString(this);
	}

	@Override
	public void toStringMaker(final StringMaker maker) {
		maker.append(getPacketName());
		maker.append('[');
		if (packetByteSize > 0) {
			maker.append("Size=");
			maker.append(packetByteSize);
			maker.append(';');
		}
		maker.append("CAPS=0x");
		maker.append((byte) getCapabilities(), Formats.HEX_FULL);
		maker.append(";RequestId=");
		maker.append(getSequenceId());
		maker.append(";ReplyId=");
		maker.append(getReplyId());
		maker.append("]");
	}

}

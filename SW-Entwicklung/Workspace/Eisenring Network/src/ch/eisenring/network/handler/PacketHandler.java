package ch.eisenring.network.handler;

import ch.eisenring.core.datatypes.primitives.Primitives;
import ch.eisenring.core.feature.Feature;
import ch.eisenring.network.PacketSink;
import ch.eisenring.network.implementation.PacketDispatchMode;
import ch.eisenring.network.packet.AbstractPacket;

public interface PacketHandler<T extends AbstractPacket> extends Feature {

	/**
	 * Handles packet and returns the result packet.
	 * 
	 * Does not involve any network actions. Optional operation.  
	 */
	public default AbstractPacket handle(final T packet) throws UnsupportedOperationException {
		throw new UnsupportedOperationException("not supported");
	}

	/**
	 * Performs whatever actions are required for the packet
	 */
	public void handle(final T packet, final PacketSink sink);

	/**
	 * Gets the type of packet handled by this handler
	 */
	public Class<T> getPacketClass();
	
	/**
	 * Gets the dispatch mode this handler requires.
	 * 
	 * If the dispatch mode contradicts the mode specified
	 * by the packet, the more restrictive mode of the two is chosen.
	 */
	public PacketDispatchMode getPacketDispatchMode();

	// --------------------------------------------------------------
	// ---
	// --- Feature implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public default Class<?> getFeatureType() {
		return PacketHandler.class;
	}

	@Override
	public default String getFeatureName() {
		return Primitives.getSimpleName(getClass());
	}

}

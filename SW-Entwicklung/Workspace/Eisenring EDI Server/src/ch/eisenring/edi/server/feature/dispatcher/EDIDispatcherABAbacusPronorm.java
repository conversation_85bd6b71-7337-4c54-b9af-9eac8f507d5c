package ch.eisenring.edi.server.feature.dispatcher;

import ch.eisenring.core.collections.List;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.datatypes.strings.parse.PlaintextParser;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.edi.server.feature.EDIContext;
import ch.eisenring.edi.server.meta.DocumentProperties;

import static ch.eisenring.core.datatypes.strings.parse.PlaintextParser.*;

/**
 * Dispatcher for "Auftragsbestätigung Pronorm für Bestellungen aus Abacus"
 */
public class EDIDispatcherABAbacusPronorm extends EDIDispatcherABAbacus {

    @Override
    protected DocumentInfoAB parseImpl(final EDIContext context, final PlaintextParser parser) throws Exception {
        String mn = "EDIDispatcherBSPronorm: ";
        parser.setCursor(0);
        parser.nextMatch("Kundennummer");
        parser.nextMatch(":");
        if (!parser.peekMatch("18026/", "18045/", "18047/", "18095/", "18135/", "59010/", "59011")) {
            return null;
        }
        parser.nextMatch("Versandart");
        parser.nextMatch("@pronorm.de");

        parser.setCursor(0);
        parser.nextMatch("Bestelldatum");

        parser.setCursor(0);
        // Check for any eligible document title Auftragsbestätigung/Termin-Schreiben/-Fax/Rechnung
        if (!parser.peekMatch(EDIDispatcherBSLogiwarePronorm.PronormDocumentTitle.AUFTRAGSBESTAETIGUNG.getTitle())) {
            return null;
        }

        parser.setCursor(0);

        parser.nextMatch("AB-Nr.");
        parser.nextMatch(":");
        parser.move(WHITESPACE, MISMATCH, PlaintextParser.Direction.FORWARD);
        final String abNummer = parser.extract(WHITESPACE, MISMATCH, PlaintextParser.Direction.FORWARD);
        /**
         * TODO: Sollte KommissionNr sein:
         * KommissionNr : 152937/1498454
         * Kommission : Bütschwil 9606 Ottilienstr. Ottilienpark
         */
        parser.nextMatch("KommissionNr");
        parser.nextMatch(":");
        // Auftrag-/Belegnummer
        parser.move(WHITESPACE, MISMATCH, Direction.FORWARD);
        String data = parser.extract(WHITESPACE, MISMATCH, Direction.FORWARD);

        if (Strings.isEmpty(abNummer) || !parser.isSuccess()) {
            Logger.warn(mn + "ABNummer nicht gefunden");
            return null;
        }
        // Determine Belegnummer from candidates
        final int belegNummer = guessBelegnummer(context, List.asList(data));

        parser.nextMatch("Kommission");
        parser.nextMatch(":");
        parser.move(ASCIIDIGIT, MATCH, Direction.FORWARD);
        String projectNumber = parser.extract(WHITESPACE, MISMATCH, Direction.FORWARD);


        if (belegNummer == INVALID_BELEGNUMMER || !parser.isSuccess()) {
            Logger.warn(mn + "Belegnummer/Auftragsnummer nicht gefunden: " + belegNummer);
            return null;
        }
        // accepted
        final DocumentInfoAB info = new DocumentInfoAB(DocumentProperties.AB_PRONORM);
        info.setTextParsingIndex(parser.getCursor());
        info.abNummerLieferant = abNummer;
        info.belegNummer = belegNummer;
        info.projektNummer = projectNumber;
        return info;
    }

}

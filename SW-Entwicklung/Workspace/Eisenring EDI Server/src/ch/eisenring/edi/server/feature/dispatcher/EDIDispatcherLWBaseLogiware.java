package ch.eisenring.edi.server.feature.dispatcher;

import ch.eisenring.edi.server.feature.EDIContext;
import ch.eisenring.logiware.LWAuftragKey;
import ch.eisenring.lw.condition.Factory;
import ch.eisenring.lw.meta.LWAuftragMeta;
import ch.eisenring.lw.meta.LWDokumentMeta;
import ch.eisenring.lw.model.navigable.LWAuftrag;
import ch.eisenring.lw.model.navigable.LWDokument;
import ch.eisenring.lw.model.navigable.LWObject;
import ch.eisenring.lw.model.navigable.LWObjectCache;

abstract class EDIDispatcherLWBaseLogiware extends EDIDispatcherABLogiware {

	/**
	 * Gets LWAuftrag from BelegNummer, returns null if not found
	 */
	static LWAuftrag getAuftragByBelegNr(final EDIContext context, final int belegNr) throws Exception {
		final LWObjectCache lwCache = context.getLWCache();
		// fetch auftrag from Logiware
		final LWAuftrag auftrag = lwCache.loadSingle(LWAuftrag.class,
				Factory.eq(LWAuftragMeta.ATR_AUFTRAGNUMMER, belegNr));
		return validateAuftrag(auftrag);
	}

	/**
	 * Gets LWAuftrag from DokumentNummer, returns null if not found
	 */
	static LWAuftrag getAuftragByDokumentNr(final EDIContext context, final int dokumentNr) throws Exception {
		final LWObjectCache cache = context.getLWCache();
		final LWDokument dokument = cache.loadSingle(LWDokument.class, 
				Factory.eq(LWDokumentMeta.ATR_DOKUMENTID, dokumentNr));
		if (dokument == null)
			throw new RuntimeException("invalid dokument id");
		final LWAuftragKey aKey = dokument.getAuftragKey();
		return validateAuftrag(cache.getObject(aKey, LWAuftrag.class));
	}

	private static LWAuftrag validateAuftrag(LWAuftrag auftrag) {
		if (auftrag != null) {
			auftrag = LWObject.validateExists(auftrag);
			if (auftrag != null) {
				LWObject.validateExists(auftrag.getAuftragKopf());
				LWObject.validateExists(auftrag.getProjekt());
			}
		}
		return auftrag;
	}

}

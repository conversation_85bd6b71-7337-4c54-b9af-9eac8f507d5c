package ch.eisenring.edi.server.feature.receiver;

import ch.eisenring.core.codetable.Protocol;
import ch.eisenring.core.collections.Collection;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.edi.MessageSource;
import ch.eisenring.edi.server.config.EDIConfigUtil;
import ch.eisenring.edi.server.config.EDIConfiguration;
import ch.eisenring.edi.server.sources.MessageSourceImage;
import ch.eisenring.edi.util.MailMessageHelper;
import ch.eisenring.email.EMailAttachment;
import ch.eisenring.email.server.network.EMailPartToAttachment;

import javax.mail.*;
import java.io.IOException;
import java.util.Arrays;
import java.util.stream.Collectors;

/**
 * EDIReceiver implementation for accepting files from a POP3 account.
 */
public class EDIReceiverPOP3 implements EDIReceiver {

    private final static String CKEY_HOST = "ReceiverHost";
    private final static String CKEY_USER = "ReceiverUser";
    private final static String CKEY_PASSWORD = "ReceiverPassword";
    private final static String CKEY_DELETE = "ReceiverDeleteFromInbox";

    private final static int MAX_MULTIPART_DEPTH = 10;

    @Override
    public String getFeatureName() {
        return "POP3";
    }

    @Override
    public Protocol checkConfiguration(final EDIConfiguration configuration) {
        final Protocol result = new Protocol();
        EDIConfigUtil.checkStringParam(result, configuration, CKEY_HOST);
        EDIConfigUtil.checkStringParam(result, configuration, CKEY_USER);
        EDIConfigUtil.checkStringParam(result, configuration, CKEY_PASSWORD);
        return result;
    }

    // --------------------------------------------------------------
    // ---
    // --- Configuration validation
    // ---
    // --------------------------------------------------------------
    private POP3Connector getPOP3Info(final EDIConfiguration configuration) {
        final String host = configuration.getString(CKEY_HOST, null);
        final String user = configuration.getString(CKEY_USER, null);
        final String pass = configuration.getString(CKEY_PASSWORD, null);
        return new POP3Connector(host, user, pass);
    }

    @Override
    public List<MessageSource> receive(final EDIConfiguration configuration) throws Exception {
        final boolean deleteFromInbox = configuration.getBoolean(CKEY_DELETE, true);
        final List<MessageSource> result = new ArrayList<>();

        // Mail server connection
        final POP3Connector connector = getPOP3Info(configuration);

        Session session = null;
        Store store = null;
        Folder inbox = null;
        try {
            session = connector.createSession();
            store = connector.connectStore(session);
            // check if inbox is available
            inbox = connector.openInbox(store);

            // handle inbox messages
            final Message[] messages = inbox.getMessages();
            for (final Message message : messages) {
                final StringMaker msg = StringMaker.obtain();
                // Extract message details
                try {
                    msg.append("Received POP3 message, Subject \"");
                    msg.append(MailMessageHelper.getSubject(message));
                    msg.append("\", from <");
                    Address[] from = MailMessageHelper.getFrom(message);
                    msg.append(String.join(";",  Arrays.asList(from).stream().map(f -> f.toString()).collect(Collectors.toList())));
                    msg.append(">");
                    Logger.info("Message received: " + msg.release());
                } catch (final Exception e) {
                    Logger.info("Message received: " + msg.release());
                    Logger.error("EDI POP3 Message konnte nicht geladen werden " + e);
                }

                // Extract attachments
                try {
                    result.addAll(toMessageSources(message));
                } catch (final Exception e) { // Ensure that all fetched messages are processed
                    Logger.error("Die Attachments konnten nicht ermittelt werden für: " + msg.release() + " due to " + e.getMessage());
                }
            }

            // delete all messages previously processed
            if (deleteFromInbox) {
                for (final Message message : messages) {
                  //  Logger.info("Message received: " + messages.release());
                    message.setFlag(Flags.Flag.DELETED, true);
                }
            }
        } finally {
            if (inbox != null) {
                try {
                    inbox.close(true);
                } catch (final Exception e) {
                    Logger.error(Strings.concat("inbox.close(): ", e.getMessage()));
                    Logger.error(e);
                }
            }
            if (store != null) {
                try {
                    store.close();
                } catch (final Exception e) {
                    Logger.error(Strings.concat("store.close(): ", e.getMessage()));
                    Logger.error(e);
                }
            }
        }
        return result;
    }

    /**
     * Builds MessageSources from mail
     */
    private List<MessageSource> toMessageSources(final Message message) throws MessagingException, IOException {
        final Collection<EMailAttachment> attachments = new ArrayList<>();
        final Object content = message.getContent();
        if (content instanceof Multipart) {
            handleMultipart(attachments, (Multipart) content, 0);
        } else {
            handlePart(attachments, message);
        }
        final List<MessageSource> sources = new ArrayList<>();
        if (attachments.size() == 0) {
            Logger.warn("No attachment could be extracted for message {} from {} sentAt {}", MailMessageHelper.getSubject(message), MailMessageHelper.getFrom(message), MailMessageHelper.getSentDate(message));
            MessageSourceImage messageSourceImage = getMessageSourceImage(null, message);
            sources.add(messageSourceImage);
        } else {
            for (final EMailAttachment attachment : attachments) {
                MessageSourceImage messageSourceImage = getMessageSourceImage(attachment, message);
                sources.add(messageSourceImage);
            }
        }
        return sources;
    }

    private MessageSourceImage getMessageSourceImage(EMailAttachment attachment, Message message) {
        MessageSourceImage messageSourceImage = new MessageSourceImage(attachment);
        messageSourceImage.setEmailOriginator(MailMessageHelper.getFrom(message));
        messageSourceImage.setEmailRecipient(MailMessageHelper.getAllRecipients(message));
        messageSourceImage.setEmailTimeStamp(MailMessageHelper.getSentDate(message));
        messageSourceImage.setEmailSubject(MailMessageHelper.getSubject(message));
        return messageSourceImage;
    }

    /**
     * Recursively handles Multipart content as each part of a Multipart can again be of type Multipart
     *
     * @param attachments
     * @param content
     * @throws MessagingException
     * @throws IOException
     */
    private void handleMultipart(Collection<EMailAttachment> attachments, Multipart content, int depth) throws MessagingException, IOException {
        if (depth < MAX_MULTIPART_DEPTH) {
            final Multipart multipart = content;
            final int count = multipart.getCount();
            for (int i = 0; i < count; ++i) {
                Part part = multipart.getBodyPart(i);
                if (part.getContent() instanceof Multipart) {
                    depth++;
                    handleMultipart(attachments, (Multipart) part.getContent(), depth);
                } else {
                    handlePart(attachments, multipart.getBodyPart(i));
                }
            }
        } else {
            Logger.warn("Reached max depth when handling multipart messages and aborting.");
        }
    }

    /**
     * Handles a single Message/Part
     *
     * @param attachments
     * @param message
     * @throws IOException
     * @throws MessagingException
     */
    private void handlePart(Collection<EMailAttachment> attachments, Part message) throws IOException, MessagingException {
        final EMailAttachment attachment = EMailPartToAttachment.handlePart(message);
        if (attachment != null)
            attachments.add(attachment);
    }

}

package ch.eisenring.edi.server.feature.dispatcher;

import ch.eisenring.core.datatypes.strings.parse.PlaintextParser;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.edi.server.feature.EDIContext;
import ch.eisenring.edi.server.meta.DocumentProperties;

import static ch.eisenring.core.datatypes.strings.parse.PlaintextParser.*;

/**
 * Dispatcher for <b>Rechung</b> for <b>Wesco Logiware</b>
 */
public class EDIDispatcherRELogiware<PERSON>uethrich extends EDIDispatcherRELogiware {

    @Override
    protected DocumentInfoRE parseImpl(EDIContext context, PlaintextParser parser) throws Exception {

        //Hans <PERSON> AG
        //Frau Sabine S<PERSON>rig
        //Pumpwerkstrasse 4
        //8370 Sirnach
        //
        //Rechnung 12314 Datum: 11.05.2023
        //Objekt:  Pumpwerkstrasse 4, <PERSON><PERSON><PERSON> Ansprechpartner: <PERSON>
        //Auftragsnummer: 2500.288 Telefon: 052 550 51 82
        //Bezeichnung: B 1586638 / Kom. 170899 Reduitüre E-Mail: <EMAIL>
        //
        //  Unsere MWST Nr.: CHE-107.085.386 MWST
        //
        //  Arbeiten vom: 05.04.2023 - 04.05.2023

        parser.nextMatch("Rechnung");
        parser.nextMatch("@wuethrich-schreinerei.ch");
        parser.nextMatch("CHE-107.085.386");

        if (!parser.isSuccess()) {
            return null;
        }
        parser.setCursor(0);

        parser.nextMatch("Rechnung");
        parser.move(ASCIIDIGIT, MATCH, Direction.FORWARD);
        String rechnungsNr = parser.extract(DIGIT, MATCH, Direction.FORWARD);

        parser.nextMatch("Auftragsnummer:");
        parser.move(PlaintextParser.ASCIIDIGIT, PlaintextParser.MATCH, Direction.FORWARD);
        String abNr = parser.extract(WHITESPACE, MISMATCH, Direction.FORWARD);

        parser.nextMatch("Bezeichnung:");
        parser.move(ASCIIDIGIT, MATCH, Direction.FORWARD);
        final String auftragNr = parser.extract(NEWLINE, MATCH, Direction.FORWARD);

        int belegNummer = guessBelegnummer(context, auftragNr);

        if (belegNummer == INVALID_BELEGNUMMER || !parser.isSuccess()) {
            Logger.debug("Dokument scheint keine Wuethrich Logiware Rechnung zu sein");
            return null;
        }

        // accepted
        DocumentInfoRE info = new DocumentInfoRE(DocumentProperties.RE_WUETHRICH);
        info.belegNummer = belegNummer;
        info.rechnungsNummer = rechnungsNr;
        info.abNummerLieferant = abNr;
        if (info.belegNummer > 0)
            info.setTextParsingIndex(parser.getCursor()); // for choosing correct dispatcher
        return info;
    }

}

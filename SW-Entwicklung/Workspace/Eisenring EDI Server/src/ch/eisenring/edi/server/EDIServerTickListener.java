package ch.eisenring.edi.server;

import ch.eisenring.app.shared.CoreTickListenerAdapter;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.core.logging.Logger.LogLevel;
import ch.eisenring.edi.server.config.EDIConfiguration;

final class EDIServerTickListener extends CoreTickListenerAdapter {

	private long nextScanTimestamp = System.currentTimeMillis() + 3000;
	protected final EDIServer server;

	public EDIServerTickListener(final EDIServer server) {
		super(server, "EDIWatcher", ASYNCHRONOUS);
		this.server = server;
	}

	@Override
	protected boolean isDue(final long now) {
		if (now < nextScanTimestamp)
			return false;
		nextScanTimestamp = now + (server.SCAN_INTERVAL.get(Integer.valueOf(60)) * 1000);
		return true;
	}
	
	@Override
	protected LogLevel getLogLevel() {
		return LogLevel.TRACE;
	}

	@Override
	protected void tickImpl(final long now) {
		final List<EDIConfiguration> pipelines = server.PIPELINES.get();
//		Logger.trace("EDIServerTickListener - Starting EDI processing at " + now);
		if (pipelines == null || pipelines.isEmpty())
			return;
		for (final EDIConfiguration pipeline : pipelines) {
			try {
				if (pipeline.isActive()) {
					//Logger.trace("Processing pipeline " + pipeline.getName());
					pipeline.process();
				}
			} catch (final Exception e) {
				Logger.error("EDIServerTickListener - Error EDI processing '" + pipeline.getName() + "' from " + now + " with error " + e);
			}
		}
//		Logger.trace("EDIServerTickListener - Finished EDI processing from " + now);
	}

}

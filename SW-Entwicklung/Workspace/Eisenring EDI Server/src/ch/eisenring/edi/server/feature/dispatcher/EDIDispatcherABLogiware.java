package ch.eisenring.edi.server.feature.dispatcher;

import ch.eisenring.PlaintextExtractor;
import ch.eisenring.app.server.model.ContextSink;
import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.datatypes.date.DateGranularityCode;
import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.datatypes.strings.parse.PlaintextParser;
import ch.eisenring.core.io.binary.BinaryHolder;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.core.logging.Logger.LogLevel;
import ch.eisenring.core.sort.Comparator;
import ch.eisenring.core.util.file.FileUtil;
import ch.eisenring.edi.MessageSource;
import ch.eisenring.edi.server.feature.EDIContext;
import ch.eisenring.edi.server.meta.DocumentClass;
import ch.eisenring.logiware.code.pseudo.AbwicklungsartCode;
import ch.eisenring.logiware.code.soft.LWKundenberaterCode;
import ch.eisenring.lw.LWConstants;
import ch.eisenring.lw.LWMapping;
import ch.eisenring.lw.condition.Factory;
import ch.eisenring.lw.meta.LWAuftragMeta;
import ch.eisenring.lw.model.navigable.LWAuftrag;
import ch.eisenring.lw.model.navigable.LWObject;
import ch.eisenring.lw.model.navigable.LWObjectCache;
import ch.eisenring.model.TransactionContext;
import ch.eisenring.model.engine.jdbc.ColumnSpecifier;
import ch.eisenring.model.engine.jdbc.TableSpecifier;
import ch.eisenring.user.shared.codetables.UserTackOnCode;
import ch.eisenring.user.shared.service.USRService;
import ch.eisenring.user.shared.service.UserFacade;

import java.io.File;
import java.io.IOException;
import java.sql.SQLException;
import java.util.concurrent.atomic.AtomicBoolean;

public abstract class EDIDispatcherABLogiware extends EDIDispatcherABBase {

	/**
	 * Default implementation for accepts, just calls acceptsFileExtension() and then parse(). If both succeed, it accepts.
	 */
	@Override
	public int accepts(final EDIContext context) {
		try {
			final MessageSource source = context.getMessageSource();
			final String fileName = source.getName();
			final String fileExtension = FileUtil.getFileExtension(fileName);
			if (!acceptsFileExtension(fileExtension))
				return Integer.MIN_VALUE;
			// check the actual document contents
			final String plainText = getMessagePlaintext(context);
			final DocumentInfoAB info = parse(context, plainText);
			return info == null ? Integer.MIN_VALUE : info.getTextParsingIndex();
		} catch (final Exception e) {
			// reject this input
			return Integer.MIN_VALUE;
		}
	}

	@Override
	protected void updateERP(String action, EDIContext context, DocumentInfoAB abInfo, boolean isDuplicateABNr) {
		Logger.debug("Verarbeite abInfo für Beleg {}", abInfo.belegNummer);
		action = Strings.concat("Auftrag #", abInfo.getBelegNummer(), " in Logiware suchen");

		Logger.debug("abInfo erweitert für Beleg {} mit Projektnummer {}.", abInfo.belegNummer, abInfo.getProjektNummer());
		// Write ABNumber to Logiware

		if (DocumentClass.AB.equals(abInfo.getDocumentDescriptor().getDocumentClass())) {
			action = Strings.concat("Update Auftrag#", abInfo.getBelegNummer(), " Belegnummer in Logiware suchen");
			isDuplicateABNr = updateLogiwareABNumber(context, abInfo);
			context.addProtocolOk(action, null);
		}
	}

	/**
	 * Gets the plain text for message in context
	 */
	protected static String getMessagePlaintext(final EDIContext context) {
		String plainText = (String) context.getData(KEY_PLAINTEXT);
		if (plainText != null)
			return plainText;
		// try to extract the plain text
		try {
			final MessageSource source = context.getMessageSource();
			final BinaryHolder binary = source.getBinary();
			plainText = PlaintextExtractor.extract(binary);
			if (Strings.isEmpty(plainText))
				plainText = INVALID_PLAINTEXT;
		} catch (final Exception e) {
			plainText = INVALID_PLAINTEXT;
		}
		context.putData(KEY_PLAINTEXT, plainText);
		return plainText;
	}


	/**
	 * Implements parsing document, returns NULL on failure (or throws)
	 */
	protected abstract DocumentInfoAB parseImpl(final EDIContext context, final PlaintextParser parser) throws Exception;

	@Override
	protected void fattenDocumentInfo(final EDIContext context, final DocumentInfoAB info) throws Exception {
		final LWAuftrag auftrag = info.getAuftrag(context);
		final LWKundenberaterCode kbCode = auftrag.getKundenberater();
		if (AbstractCode.isNull(kbCode)) {
			context.addProtocolError("LogiWare", "Im Auftrag im Reiter \"Vertreter\" ist kein gültiger Wert im Feld \"Kundenberater 1\" eingetragen.");
		}
		info.setProjektNummer(auftrag.getProjektnummer());
		info.kundenberater = kbCode;
		// if email already set, keep the existing email
		if (!Strings.isEmpty(info.kundenberaterEmail))
			return;
		try {
			info.kundenberaterEmail = getKundenberaterEmail(context, kbCode);
		} catch (final Exception e) {
			info.kundenberaterEmail = null;
		}
		// if email address not found, set to standard address
		if (Strings.isEmpty(info.kundenberaterEmail)) {
			info.kundenberaterEmail = context.configuration.getString(CKEY_NOTIFYTO, null);
		}
	}

	private static String getKundenberaterEmail(final EDIContext context, final LWKundenberaterCode kbCode) throws Exception {
		final USRService service = context.server.locateService(USRService.class);
		final UserFacade user = service.getUser(UserTackOnCode.KUNDENBERATER, kbCode.getKey());
		if (user == null)
			throw new Exception("user not found");
		final String email = user.getEMailAddress();
		if (Strings.isEmpty(email))
			throw new Exception("email address is empty");
		return email;
	}

	protected static LWAuftrag getLWAuftrag(final EDIContext context, final int belegnummer) throws Exception {
		final LWObjectCache lwCache = context.getLWCache();
		// fetch auftrag from Logiware
		final LWAuftrag auftrag = lwCache.loadSingle(LWAuftrag.class,
				Factory.eq(LWAuftragMeta.ATR_AUFTRAGNUMMER, belegnummer));
		// prefetch LWProjekt and LWAuftragKopf
		/*final LWProjekt projekt =*/
		LWObject.validateExists(auftrag.getProjekt());
		/*final LWAuftragKopf kopf =*/
		LWObject.validateExists(auftrag.getAuftragKopf());
		return auftrag;
	}

	// --------------------------------------------------------------
	// ---
	// --- Sanity checking for Belegnummer / Bestellung
	// ---
	// --------------------------------------------------------------

	/**
	 * Performs some basic sanity checks on Belegnummer.
	 */
	public boolean isValidBelegnummer(final EDIContext context, final CharSequence belegNummer) {
		if (Strings.isEmpty(belegNummer) || Strings.length(belegNummer) < 6)
			return false;
		final int auftrNr;
		try {
			auftrNr = Strings.parseInt(belegNummer);
		} catch (final Exception e) {
			// Reject non-integer numbers
			return false;
		}
		// Reject anything that can't be a valid number
		if (auftrNr < 551385)
			return false;
		// check LW database
		try {
			return isValidBelegnummerLW(context, auftrNr);
		} catch (final Exception e) {
			return false;
		}
	}

	/**
	 * Checks list of candidates for "Belegnummer", returns the best match. Returns INVALID_BELEGNUMMER if no match.
	 */
	public int guessBelegnummer(final EDIContext context, final java.util.Collection<? extends CharSequence> candidates) {
		if (candidates != null) {
			for (final CharSequence belegNummer : candidates) {
				if (isValidBelegnummer(context, belegNummer)) {
					try {
						return Strings.parseInt(belegNummer);
					} catch (final Exception e) {
						// ignore and continue
					}
				}
			}
		}
		return INVALID_BELEGNUMMER;
	}

	@Override
	protected boolean isValidBelegNummer(final EDIContext context, final int belegNummer) throws Exception {
		return isValidBelegnummerLW(context, belegNummer);
	}

	private boolean isValidBelegnummerLW(final EDIContext context, final int belegNummer) throws Exception {
		final LogLevel logLevel = Logger.setLogLevel(LogLevel.WARN, Logger.SCOPE_THREAD);
		try {
			// Logiware based validation
			final LWAuftrag auftrag = getLWAuftrag(context, belegNummer);
			// Only accept if its a BESTELLUNG_LIEFERANT
			if (!AbwicklungsartCode.FILTER_BESTELLUNG_LIEFERANT.accepts(auftrag.getAbwicklungsart()))
				return false;
			// Reject if auftrag more than 20 weeks old?
			final long limit = DateGranularityCode.DAY.round(auftrag.getAuftragsDatum(), 140);
			return TimestampUtil.compare(limit, System.currentTimeMillis()) >= 0;
		} finally {
			Logger.setLogLevel(logLevel, Logger.SCOPE_THREAD);
		}
	}

	/**
	 * Inserts AB-Nummer into Auftrag, IF the document info is flagged for updating it and not error condition has yet occured in the EDIContext. Returns TRUE if the AB-Nummer was a duplicate, FALSE
	 * in all other cases.
	 */
	private boolean updateLogiwareABNumber(EDIContext context, DocumentInfoAB info) {
		AtomicBoolean isDuplicate = new AtomicBoolean(false);
		if (info.documentDescriptor.isAbgleichBestellNrLW() && context.isSuccess()) {
			try {
				final TransactionContext ctx = new TransactionContext();
				ctx.store(new ContextSink(LWConstants.VERTRIEB) {
					@Override
					protected void storeImpl(final TransactionContext context) throws SQLException {
						TableSpecifier tblAuftrag = LWMapping.TM_AUFTRAG.getTableSpecifier();
						ColumnSpecifier fldBestellNr = LWMapping.TM_AUFTRAG.getColumn(LWAuftragMeta.ATR_BESTELLNR);
						ColumnSpecifier fldAuftragNr = LWMapping.TM_AUFTRAG.getColumn(LWAuftragMeta.ATR_AUFTRAGNUMMER);
						// first, check if AB-Nummer is already set
						String sql = new StringBuilder()
								.append("SELECT ").append(fldBestellNr)
								.append(" FROM ").append(tblAuftrag)
								.append(" WHERE ").append(fldAuftragNr).append("=").append(info.belegNummer)
								.toString();
						doQuery(sql, null, resultSet -> {
							final String abNummer = resultSet.getString(1);
							isDuplicate.compareAndSet(false, !Strings.isEmpty(abNummer));
						});
						sql = new StringBuilder()
								.append("UPDATE ").append(tblAuftrag)
								.append(" SET ").append(fldBestellNr).append("=").append(quoted(info.abNummerLieferant))
								.append(" WHERE ").append(fldAuftragNr).append("=").append(info.belegNummer)
								.toString();
						doUpdate(sql, null);
					}
				});
			} catch (Exception e) {
				context.addProtocolError("Schreiben AB-Nr Logiware", e.getMessage());
			}
		}
		return isDuplicate.get();
	}

	/**
	 * Saves the text as a new {@link File} in the given <var>folder</var> with filename <tt>dispatchername_millis.txt</tt>
	 *
	 * @param folder the folder, must exist
	 * @param text   the text to save
	 * @throws IOException
	 */
	public void saveDocumentPlainText(File folder, String text) throws IOException {
		if (folder.exists()) {
			File file = new File(folder, getClass().getSimpleName() + "_" + System.currentTimeMillis() + ".txt");
			try (java.io.Writer out = new java.io.FileWriter(file)) {
				out.write(text);
			}
		}
	}

}

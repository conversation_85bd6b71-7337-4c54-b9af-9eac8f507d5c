package ch.eisenring.edi.server;

import ch.eisenring.app.server.AbstractServerComponent;
import ch.eisenring.app.shared.Configurable;
import ch.eisenring.core.application.Configuration;
import ch.eisenring.core.application.ConfigurationException;
import ch.eisenring.core.collections.Collection;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.Set;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.feature.Feature;
import ch.eisenring.core.io.file.FileItem;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.core.logging.Logger.LogLevel;
import ch.eisenring.core.observable.Observable;
import ch.eisenring.core.platform.Platform;
import ch.eisenring.core.platform.PlatformPath;
import ch.eisenring.edi.server.config.EDIConfiguration;
import ch.eisenring.edi.server.config.EDIPipeline;
import ch.eisenring.edi.server.data.EDIABServerData;
import ch.eisenring.edi.server.data.EDIREServerData;
import ch.eisenring.edi.server.feature.dispatcher.*;
import ch.eisenring.edi.server.feature.processing.EDIProcessorDispatcher;
import ch.eisenring.edi.server.feature.processing.EDIProcessorPassthrough;
import ch.eisenring.edi.server.feature.processing.EDIProcessorZIP;
import ch.eisenring.edi.server.feature.receiver.EDIReceiverFileSystem;
import ch.eisenring.edi.server.feature.receiver.EDIReceiverPOP3;
import ch.eisenring.edi.server.feature.transport.EDITransportEMail;
import ch.eisenring.edi.server.feature.transport.EDITransportNull;

import java.io.File;

public class EDIServer extends AbstractServerComponent implements Configurable {

    // --------------------------------------------------------------
    // ---
    // --- Server states
    // ---
    // --------------------------------------------------------------

    /**
     * Active pipeline configuration
     */
    public final Observable<List<EDIConfiguration>> PIPELINES = Observable.create(false, "Pipelines", null);

    /**
     * Interval (seconds) at which pipelines look for work
     */
    public final Observable<Integer> SCAN_INTERVAL = Observable.create(false, "ScanInterval", 60);

    /**
     * Status log file. This is the global log file all
     * pipelines will log into, if not configured individually.
     * If this is not set, logging is disabled.
     */
    public final Observable<FileItem> STATUS_LOGFILE = Observable.create(false, "StatusLogFile", (FileItem) null);

    /**
     * Data item log dir. Processed data items are logged into
     * that directory if this is set.
     */
    public final Observable<FileItem> ITEM_LOGDIR = Observable.create(false, "ItemLogDir", (FileItem) null);

    /**
     * User name used when adding documents to the DMS
     */
    public final Observable<String> DMS_USERNAME = Observable.create(false, "DMSUserName", "SYSTEM");

    /**
     * Preset attachment names to be ignored
     */
    public final Observable<Set<String>> GLOBAL_IGNORE = Observable.create(false, "DispatcherIgnore", Set.emptyReadonly(String.class));

	public final Observable<EDIABServerData> EDI_AB_SERVER_DATA = Observable.create(false, "EdiABServerData", (EDIABServerData) null);

    public final Observable<EDIREServerData> EDI_RE_SERVER_DATA = Observable.create(false, "EdiREServerData", (EDIREServerData) null);


    // --------------------------------------------------------------
    // ---
    // --- Startup/Shutdown
    // ---
    // --------------------------------------------------------------

    // no need for any custom launch / shutdown

    // --------------------------------------------------------------
    // ---
    // --- Feature management
    // ---
    // --------------------------------------------------------------

    private final Feature[] features = {
            // EDI Receivers
            new EDIReceiverFileSystem(),
            new EDIReceiverPOP3(),

            // EDI Processors
            new EDIProcessorPassthrough(),
            new EDIProcessorZIP(),
            new EDIProcessorDispatcher(),

            // EDI Transports
            new EDITransportEMail(),
            new EDITransportNull(),

            // EDI Dispatchers
            new EDIDispatcherEDI(),
            new EDIDispatcherABLogiwareBucher(),
            new EDIDispatcherABAbacusBucher(),
            new EDIDispatcherABLogiwareElectrolux(),
            new EDIDispatcherABAbacusElectrolux(),
            new EDIDispatcherABAbacusVZug(),
            new EDIDispatcherABAbacusSuter(),
            new EDIDispatcherABAbacusPronorm(),
            new EDIDispatcherABAbacusFranke(),
            new EDIDispatcherABLogiwareFranke(),
            new EDIPronormDispatcherABLogiware(),
            new EDIDispatcherEBLogiwarePronorm(),
            new EDIPronormDispatcherRELogiware(),
            new EDIDispatcherABLogiwareBSH(),
            new EDIDispatcherABLogiwareSuter(),
            new EDIDispatcherABLogiwareVZug(),
            new EDIDispatcherABLogiwareRWKKuhlmann(),
            new EDIMieleDispatcherABLogiware(),
            new EDIMieleDispatcherRELogiware(),
            new EDIDispatcherRELogiwareOpoOeschger(),
            new EDIDispatcherRELogiwareElectroluxGeraete(),
            new EDIDispatcherRELogiwareElectroluxService(),
            new EDIDispatcherRELogiwareElectroluxServiceErsatzteile(),
            new EDIDispatcherRELogiwareVeriset(),
            new EDIDispatcherRELogiwareKoch(),
            new EDIDispatcherRELogiwareBucher(),
            new EDIDispatcherRELogiwareSitag(),
            new EDIDispatcherRELogiwareWesco(),
            new EDIDispatcherRELogiwareVZug(),
            new EDIDispatcherRELogiwareFranke(),
            new EDIDispatcherRELogiwareSuter(),
            new EDIDispatcherRELogiwareBSH_Variante_Belegnummer(),
            new EDIDispatcherRELogiwareBSH_Variante_Bestelldaten(),
            new EDIDispatcherRELogiwareWuethrich(),
            new EDIDispatcherRELogiwareME(),
            new EDIDispatcherREAbacusPronorm(),
            new EDIDispatcherREAbacusKoch(),
            new EDIDispatcherREAbacusVeriset(),
            new EDIDispatcherREAbacusBucher(),
            new EDIDispatcherREAbacusWesco(),
            new EDIDispatcherREAbacusSprinz(),
            new EDIDispatcherREAbacusVZug(),
            new EDIDispatcherREAbacusVZug_Variante1(),
            new EDIDispatcherREAbacusVZug_Variante2(),
            new EDIDispatcherREAbacusFranke(),
            new EDIDispatcherREAbacusSuter(),
            new EDIDispatcherREAbacusBSH_Variante_Belegnummer(),
            new EDIDispatcherREAbacusBSH_Variante_Bestelldaten(),
            new EDIDispatcherREAbacusWuethrich(),
            new EDIDispatcherREAbacusME(),
            new EDIDispatcherREAbacusElectroluxService(),
            new EDIDispatcherREAbacusElbau(),
            new EDIDispatcherRELogiwareElbau(),
            new EDIDispatcherREAbacusGlasworld(),
            new EDIDispatcherREAbacusGlasworldES(),
            new EDIDispatcherIgnore(),

            // EDI LW Dispatchers
            new EDIDispatcherLWStrImportLogiware(),
            new EDIDispatcherLWStrIgnoreLogiware(),

            // Tick listener
            new EDIServerTickListener(this)
    };

    @Override
    public Feature[] getFeatures() {
        return features;
    }

    // --------------------------------------------------------------
    // ---
    // --- Configurable implementation
    // ---
    // --------------------------------------------------------------

    @Override
    public void getConfigurationFileNames(final Collection<String> fileNames) {
        fileNames.add("EDI.cfg");
        fileNames.add(new File(Platform.getPlatform().getPath(PlatformPath.SETTINGS), "EDIServerUser.cfg").getAbsolutePath());
    }

    @Override
    public void configure(final Configuration configuration) throws ConfigurationException {
        // configure server globals
        {
            final Configuration cfg = configuration.subConfiguration("EDIServer:");
            SCAN_INTERVAL.set(cfg.getInteger("ScanInterval", 10, 86400, 60));
            DMS_USERNAME.set(cfg.getString("DMSUserName", "SYSTEM"));
            STATUS_LOGFILE.set(cfg.getFile("StatusLogFile", (FileItem) null));
            ITEM_LOGDIR.set(cfg.getDirectory("ItemLogDir", (FileItem) null, true));
            GLOBAL_IGNORE.set(Set.asReadonlySet(
                    cfg.getStrings("GlobalIgnore", List.emptyReadOnly(String.class), ';')));
            EDI_AB_SERVER_DATA.set(new EDIABServerData(cfg.getDirectory("AbacusPOData", (FileItem) null, false)));
            EDI_RE_SERVER_DATA.set(new EDIREServerData(cfg.getDirectory("AbacusInvoiceData", (FileItem) null, false)));
        }
        // configures pipelines
        final List<EDIConfiguration> pipelines = new ArrayList<>();
        for (int i = 0; i <= 99; ++i) {
            final String section = Strings.concat("EDIPipeline", i);
            if (!configuration.keyExists(Strings.concat(section, ":Name")))
                continue;
            try {
                final EDIPipeline pipeline = new EDIPipeline(this, configuration, section);
                if (pipeline.isValid()) {
                    pipelines.add(pipeline);
                } else {
                    // dump configuration errors
                    Logger.error("*** Configuration problem in [{}] (section will be ignored)", section);
                    pipeline.getConfigProtocol().log(LogLevel.ERROR);
                    Logger.error("***");
                }
            } catch (final Exception e) {
                // ignore
                Logger.error("*** Configuration problem in [{}] (section will be ignored)", section);
            }
        }
        // replace configuration
        PIPELINES.set(pipelines);
        Logger.info("EDIServer: {} pipelines configured", pipelines.size());
    }

}

package ch.eisenring.edi.server.data;

import ch.eisenring.core.application.RollingDataFile;
import ch.eisenring.core.application.RollingDataFileParser;
import ch.eisenring.core.collections.Set;
import ch.eisenring.core.collections.impl.HashSet;
import ch.eisenring.core.csv.CSVFile;
import ch.eisenring.core.csv.CSVLine;
import ch.eisenring.core.csv.CSVReader;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.io.file.FileItem;

import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.Iterator;

public class EDIREServerData {

    final static Duration MAX_DATA_AGE = Duration.ofMinutes(1);

	/**
	 * Abacus file containing invoice info from Abacus together with the relevant backoffice user
	 */
	public static RollingDataFile<Set<AbacusOrderData>> RE_RECEPIENTS;


	public EDIREServerData(final FileItem source) {
		RE_RECEPIENTS = new RollingDataFile<>(source.getAbsolutePath(), MAX_DATA_AGE, RE_RECEPIENT_PARSER);
	}

	private final static RollingDataFileParser<Set<AbacusOrderData>> RE_RECEPIENT_PARSER = new RollingDataFileParser<Set<AbacusOrderData>>() {
		@Override
		public Set<AbacusOrderData> parseFile(final RollingDataFile<Set<AbacusOrderData>> dataFile) throws Exception {
			final CSVFile csvFile;
			try (final InputStream inputStream = dataFile.getInputStream()) {
				csvFile = CSVReader.readCSV(inputStream, StandardCharsets.ISO_8859_1, ',');
			}
			final Set<AbacusOrderData> result = new HashSet<>();
			final Iterator<CSVLine> i = csvFile.getLineIterator();
			while (i.hasNext()) {
				final CSVLine csvLine = i.next();
				if (csvLine.getColumnCount() <= 0)
					continue;
				final String purchaseOrderNumber = csvLine.getColumn(0);
				final String backlogNumber = csvLine.getColumn(1);
				final String subProjectNumber = csvLine.getColumn(2);
				final String projectNumber = csvLine.getColumn(3);
				final String recipientName = csvLine.getColumn(4);
				final String recipientEmail = csvLine.getColumn(5);
				if (Strings.isEmpty(purchaseOrderNumber))
					continue;
				result.add(new AbacusOrderData(purchaseOrderNumber, backlogNumber, subProjectNumber, projectNumber, recipientName, recipientEmail));
			}
			return result;
		}
	};

}

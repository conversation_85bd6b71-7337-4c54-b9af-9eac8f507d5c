package ch.eisenring.edi.server.feature.dispatcher;

import ch.eisenring.core.collections.List;
import org.junit.Test;

import static org.junit.Assert.assertTrue;

public class EDIDispatcherIgnoreTest {

	@Test
	public void shouldIgnoreFileName() {
		EDIDispatcherIgnore ignoreDispatcher = new EDIDispatcherIgnore();
		List<String> ignoreList = List.asList("bucher_d.png","signature.asc","unnamed.txt", "zeichnung.pdf");
		assertTrue(ignoreDispatcher.isInIgnoredFiles("bucher_d.png", ignoreList));
		assertTrue(ignoreDispatcher.isInIgnoredFiles("signature.asc", ignoreList));
		assertTrue(ignoreDispatcher.isInIgnoredFiles("unnamed.txt", ignoreList));
		assertTrue(ignoreDispatcher.isInIgnoredFiles("B.22.L.461643.zeichnung.pdf", ignoreList));
	}
}
package heag.nsi.server;

import ch.eisenring.app.server.filewatch.AbstractDirWatcher;
import ch.eisenring.app.server.services.ErrorReportingService;
import ch.eisenring.app.shared.ServiceNotFoundException;
import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.collections.Collection;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.io.binary.BinaryHolder;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.core.threading.ThreadCore;
import ch.eisenring.core.util.file.FileImage;
import ch.eisenring.core.util.file.FileUtil;
import ch.eisenring.user.shared.service.USRService;
import ch.eisenring.user.shared.service.UserFacade;
import heag.nsi.server.api.NSIDocumentHandler;
import heag.nsi.server.api.NSIFileWatchEntry;
import heag.nsi.server.api.NSIMetaDataKey;
import heag.nsi.server.api.NSIProcessingContext;
import heag.nsi.server.model.NSIDocumentProperty;
import heag.nsi.server.model.NSIFile;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Unmarshaller;
import java.io.File;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.Reader;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.Arrays;

import static ch.eisenring.core.codetable.MessageClassCode.INFO;

final class NSIDirWatcher extends AbstractDirWatcher<NSIServer, NSIFileWatchEntry> {

    public NSIDirWatcher(final NSIServer server) {
        super(server);
    }

    // --------------------------------------------------------------
    // ---
    // --- ServerJob implementation
    // ---
    // --------------------------------------------------------------
    @Override
    protected long getTickDelay() {
        return server.SCAN_INTERVAL.get() & 0xFFFFFFFFL;
    }

    // --------------------------------------------------------------
    // ---
    // --- DirWatcher implementation
    // ---
    // --------------------------------------------------------------
    @Override
    public boolean isEnabled() {
        return server.ENABLED.get() && !Strings.isEmpty(server.WATCH_DIR.get());
    }

    @Override
    protected File getWatchRoot() {
        if (!isEnabled())
            return null;
        final String watchRoot = server.WATCH_DIR.get();
        return Strings.isEmpty(watchRoot) ? null : new File(watchRoot);
    }

    @Override
    protected boolean accepts(final File file) {
        // accept anything
        if (file.isFile()) {
            final String name = file.getName();
            final String extension = FileUtil.getFileExtension(name);
            return Strings.equalsIgnoreCase(extension, "xml");
        }
        return true;
    }

    @Override
    protected NSIFileWatchEntry createEntry(final String absolutePath) {
        return new NSIFileWatchEntry(absolutePath);
    }

    @Override
    protected void processEntry(final NSIFileWatchEntry entry) {
        try {
            final NSIProcessingContext context = new NSIProcessingContext(server, entry);
            trace(entry);
            // load XML meta data
            try {
                readXML(entry);
            } catch (final Exception e) {
                final String message = Strings.concat("Fehler beim Einlesen der Metadaten von \"",
                        entry.getAbsolutePath(), "\": ", e.getMessage());
                context.addError(new ErrorMessage(message));
                Logger.error(message);
                Logger.error(e);
            }

            // determine the handler that accepts the entry
            final String documentType = entry.getMetaData(NSIMetaDataKey.DOCUMENT_TYPE);
            NSIDocumentHandler acceptingHandler = null;
            for (final NSIDocumentHandler handler : server.getFeatureLookup().getFeatures(NSIDocumentHandler.class)) {
                if (!handler.accepts(documentType, entry))
                    continue;
                if (acceptingHandler != null) {
                    // more than one handler accepts, error
                    context.addError(new ErrorMessage(Strings.concat("Dokumenttyp \"", documentType, "\" nicht eindeutig zuzuordnen")));
                } else {
                    acceptingHandler = handler;
                }
            }
            if (acceptingHandler == null) {
                context.addError(new ErrorMessage(Strings.concat("Kein Verarbeitungsweg für Dokumenttyp \"", documentType, "\" gefunden")));
            }

            // if no problems occured so far, let the handler do its job now
            if (context.isValid()) {
                try {
                    acceptingHandler.process(context, entry);
                } catch (final Exception e) {
                    final String message = Strings.concat("Unerwarteter Fehler: ", e.getMessage());
                    Logger.error(message);
                    Logger.error(e);
                    context.addError(new ErrorMessage(message));
                }
            }

            // if any errors occured report the problem(s)
            if (!context.isValid()) {
                final String user;
                user = entry.getMetaData(NSIMetaDataKey.USER_NAME);
                context.addError(new ErrorMessage(INFO, Strings.concat("Eingaben (Projektnummer): ", entry.getMetaData(NSIMetaDataKey.PROJECT_NUMBER))));
                context.addError(new ErrorMessage(INFO, Strings.concat("Eingaben (Benutzerkürzel): ", user)));
                context.addError(new ErrorMessage(INFO, Strings.concat("Eingaben (KW): ", entry.getMetaData(NSIMetaDataKey.KW))));
                context.addError(new ErrorMessage(INFO, Strings.concat("Dokumentart: ", entry.getMetaData(NSIMetaDataKey.DOCUMENT_TYPE))));
                context.addError(new ErrorMessage(INFO, Strings.concat("Barcode (Klartext): ", entry.getMetaData(NSIMetaDataKey.USER_SUPPLIED_BARCODE))));
                context.addError(new ErrorMessage(INFO, Strings.concat("Barcode (Gelesen): ", entry.getMetaData(NSIMetaDataKey.DOCUMENT_SUPPLIED_BARCODE))));
                // New
                context.addError(new ErrorMessage(INFO, Strings.concat("Barcode1 (Gelesen): ", entry.getMetaData(NSIMetaDataKey.BARCODE1))));
                context.addError(new ErrorMessage(INFO, Strings.concat("QRCode (Gelesen): ", entry.getMetaData(NSIMetaDataKey.QRCODE1))));
                // send error mail
                sendErrorMail(entry, context.getErrors(), user);
            }
        } finally {
            if (server.DELETE_AFTER_IMPORT.get()) {
                // delete both the XML and the PDF
                ThreadCore.sleep(500);
                try {
                    entry.delete();
                } catch (final Exception e) {
                    Logger.warn(e);
                }
            }
        }
    }

    // --------------------------------------------------------------
    // ---
    // --- Send error mail
    // ---
    // --------------------------------------------------------------
    private void sendErrorMail(final NSIFileWatchEntry entry, final java.util.Collection<ErrorMessage> errors, final String user) {
        final List<ErrorMessage> errorList = new ArrayList<>(errors);
        final List<FileImage> files = new ArrayList<>();
        try {
            final File file = new File(entry.getDocName());
            final BinaryHolder binary = entry.getDocumentData();
            final FileImage image = FileImage.create(file.getName(), binary);
            files.add(image);
        } catch (final Exception e) {
            errorList.add(new ErrorMessage(Strings.concat("Dateifehler: ", e.getMessage())));
            Logger.error(e);
        }
        try {
            final File file = new File(entry.getXMLName());
            final BinaryHolder binary = entry.getFileData();
            final FileImage image = FileImage.create(file.getName(), binary);
            files.add(image);
        } catch (final Exception e) {
            errorList.add(new ErrorMessage(Strings.concat("Dateifehler: ", e.getMessage())));
            Logger.error(e);
        }
        try {
            final List<String> recipients = new ArrayList<>(2);
            recipients.add(server.ERROR_MAIL_ADDRESS.get());
            addUserEMail(recipients, user);
            final ErrorReportingService errorService = server.locateService(ErrorReportingService.class);
            errorService.reportError("DMS/NSI Verarbeitungsfehler", "Gescanntes Dokument konnte nicht verarbeitet werden!",
                    errorList, files, recipients);
        } catch (final ServiceNotFoundException e) {
            Logger.fatal(e);
        }
    }

    // --------------------------------------------------------------
    // ---
    // --- XML processing
    // ---
    // --------------------------------------------------------------

    private void readXML(final NSIFileWatchEntry entry) throws Exception {
        final BinaryHolder xmlData = entry.getFileData();
        try (final InputStream input = xmlData.getInputStream();
             final Reader reader = new InputStreamReader(input, StandardCharsets.UTF_8)) {
            final NSIFile nsiFile = unmarshalNSIFile(reader);
            final java.util.List<NSIDocumentProperty> nsiDocumentProperties = nsiFile.getProperties();
            for (final NSIDocumentProperty property : nsiDocumentProperties) {
                // process by name
                final String attributeName = property.getName();
                final String value = Strings.clean(property.getValue());
                if (value == null)
                    continue;
                for (final NSIMetaDataKey key : NSIMetaDataKey.values()) {
                    if (Strings.equalsIgnoreCase(attributeName, key.getXmlAttributeName())) {
                        entry.putMetaData(key, value);
                        continue;
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    // --------------------------------------------------------------
    // ---
    // --- Add users email address to collection
    // ---
    // --------------------------------------------------------------
    protected boolean addUserEMail(final Collection<String> recipients, final String loginname) {
        try {
            final USRService service = server.locateService(USRService.class);
            final UserFacade user = service.getUser(loginname);
            if (user == null)
                return false;
            final String address = user.getEMailAddress();
            if (Strings.isEmpty(address))
                return false;
            return recipients.add(address);
        } catch (final Exception e) {
            Logger.warn(e);
        }
        return false;
    }

    // --------------------------------------------------------------
    // ---
    // --- Trace
    // ---
    // --------------------------------------------------------------

    private void trace(NSIFileWatchEntry entry) {
        try {
            server.getThreadPool().start(() -> traceEntry(entry));
        } catch (Exception e) {
            // do nothing
        }
    }

    private void traceEntry(NSIFileWatchEntry entry) {
        final Long timeStamp = System.currentTimeMillis();
        try {
            java.util.List<File> files = Arrays.asList(entry.getFile(), new File(entry.getDocName()));
            for (File file : files) {
                Path p = Paths.get(file.getAbsolutePath());
                File out = new File("../trace/nsi/");
                Path targetPath = Paths.get(out.getAbsolutePath() + "/" + timeStamp + "_" + file.getName());
                Files.copy(p, targetPath, StandardCopyOption.REPLACE_EXISTING);
            }
        } catch (Exception e) {
            // do nothing
        }
    }

    private NSIFile unmarshalNSIFile(Reader reader) throws JAXBException {
        JAXBContext context = JAXBContext.newInstance(NSIFile.class);
        Unmarshaller unmarshaller = context.createUnmarshaller();
        return (NSIFile) unmarshaller.unmarshal(reader);
    }

}



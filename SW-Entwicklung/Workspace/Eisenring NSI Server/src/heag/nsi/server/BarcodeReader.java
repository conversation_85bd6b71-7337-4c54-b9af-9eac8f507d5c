package heag.nsi.server;

import ch.eisenring.core.collections.Collection;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.Map;
import ch.eisenring.core.collections.Set;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.collections.impl.HashMap;
import ch.eisenring.core.collections.impl.HashSet;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.fileformat.FileFormats;
import ch.eisenring.core.io.binary.BinaryHolder;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.core.resource.image.ImageUtil;
import com.google.zxing.*;
import com.google.zxing.client.j2se.BufferedImageLuminanceSource;
import com.google.zxing.common.HybridBinarizer;
import com.google.zxing.multi.GenericMultipleBarcodeReader;
import com.google.zxing.oned.Code128Reader;
import com.google.zxing.qrcode.QRCodeReader;
import org.faceless.pdf2.PDF;
import org.faceless.pdf2.PDFParser;
import org.faceless.pdf2.PDFReader;
import org.faceless.pdf2.PagePainter;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.awt.image.BufferedImageOp;
import java.awt.image.ConvolveOp;
import java.awt.image.Kernel;
import java.io.InputStream;
import java.util.Arrays;

public abstract class BarcodeReader {

	private final static Kernel KERNEL1 = new Kernel(3, 3, new float[]{
			1F / 15F, 2F / 15F, 1F / 15F,
			2F / 15F, 3F / 15F, 2F / 15F,
			1F / 15F, 2F / 15F, 1F / 15F
	});

	private final static Kernel KERNEL2 = new Kernel(3, 3, new float[]{
			1F / 16F, 2F / 16F, 1F / 16F,
			2F / 16F, 4F / 16F, 2F / 16F,
			1F / 16F, 2F / 16F, 1F / 16F
	});

	private final static Kernel KERNEL3 = new Kernel(5, 5, new float[]{
			0F / 112F, 1F / 112F, 2F / 112F, 1F / 112F, 0F / 112F,
			1F / 112F, 4F / 112F, 8F / 112F, 4F / 112F, 1F / 112F,
			2F / 112F, 8F / 112F, 16F / 112F, 8F / 112F, 2F / 112F,
			1F / 112F, 4F / 112F, 8F / 112F, 4F / 112F, 1F / 112F,
			0F / 112F, 1F / 112F, 2F / 112F, 1F / 112F, 0F / 112F
	});

	private final static Kernel KERNEL4 = new Kernel(7, 7, new float[]{
			0.00000067F, 0.00002292F, 0.00019117F, 0.00038771F, 0.00019117F, 0.00002292F, 0.00000067F,
			0.00002292F, 0.00078633F, 0.00655965F, 0.01330373F, 0.00655965F, 0.00078633F, 0.00002292F,
			0.00019117F, 0.00655965F, 0.05472157F, 0.11098164F, 0.05472157F, 0.00655965F, 0.00019117F,
			0.00038771F, 0.01330373F, 0.11098164F, 0.22508352F, 0.11098164F, 0.01330373F, 0.00038771F,
			0.00019117F, 0.00655965F, 0.05472157F, 0.11098164F, 0.05472157F, 0.00655965F, 0.00019117F,
			0.00002292F, 0.00078633F, 0.00655965F, 0.01330373F, 0.00655965F, 0.00078633F, 0.00002292F,
			0.00000067F, 0.00002292F, 0.00019117F, 0.00038771F, 0.00019117F, 0.00002292F, 0.00000067F
	});

	private static BarcodeFormat[] barcodeFormats = {BarcodeFormat.CODE_39, BarcodeFormat.CODE_128};

	private static BarcodeFormat[] qrcodeFormats = {BarcodeFormat.QR_CODE};

//	private static float[] normalizeToFloat(final int[] data) {
//		float sum = 0F;
//		for (final int element : data) {
//			sum += element;
//		}
//		final float[] result = new float[data.length];
//		for (int i=0; i<result.length; ++i) {
//			result[i] = data[i] / sum;
//		}
//		return result;
//	}

	private final static Kernel[] KERNELS = {KERNEL1, KERNEL2, KERNEL3, KERNEL4};

	/**
	 * Parse barcode(s) from file (PDF or image)
	 */
	public static List<Result> readBarcodes(final BinaryHolder binary) {
		return readBarcodes(binary, 0);
	}

	/**
	 * Parse barcode(s) from file (PDF or image)
	 */
	public static List<Result> readBarcodes(final BinaryHolder binary, final int pageNo) {
		Image image = null;

		// attempt to render the file
		if (FileFormats.PDF.matches(binary)) {
			// convert PDF to image (first page only)
			try (InputStream input = binary.getInputStream()) {
				final PDF pdf = new PDF(new PDFReader(input));
				final PDFParser parser = new PDFParser(pdf);
				final PagePainter painter = parser.getPagePainter(pageNo);
				image = painter.getImage(300, PDFParser.GRAYSCALE);
			} catch (Exception e) {
				Logger.error(e);
			}
		} else {
			// attempt to read as image using ImageIO
			try (InputStream input = binary.getInputStream()) {
				image = ImageIO.read(input);
			} catch (Exception e) {
				Logger.error(e);
			}
		}

		// could not read image, return empty result
		if (image == null)
			return new ArrayList<>();

		return readBarcodes(image);
	}

	/**
	 * Parse barcode(s) from image
	 */
	public static List<Result> readBarcodes(final Image image) {
		List<Result> result;
		final BufferedImage buffered = ImageUtil.toBufferedImage(image);

		result = readBarcodesImpl(buffered);
		if (!result.isEmpty())
			return result;

		// if the original image did not work, try smoothed versions
		for (int k = 0; k < KERNELS.length; ++k) {
			final Kernel kernel = KERNELS[k];
			final BufferedImage smoothed = convolve(buffered, kernel);
			result = readBarcodesImpl(smoothed);
			if (Logger.isTraceEnabled())
				Logger.trace(Strings.concat("Kernel #", (k + 1), ": ", (result.size() > 0 ? "Success" : "Failed")));
			if (!result.isEmpty())
				return result;
		}

		// admit defeat...
		return result;
	}

	/**
	 * Parse barcode(s) from image
	 */
	protected static List<Result> readBarcodesImpl(final BufferedImage image) {
		final List<Result> result = new ArrayList<>();
		try {
			final List<BarcodeFormat> formats = new ArrayList<>(1);
			formats.addAll(barcodeFormats);
			formats.addAll(qrcodeFormats);

			final Map<DecodeHintType, Object> hints = new HashMap<>();
			hints.put(DecodeHintType.TRY_HARDER, Boolean.TRUE);
			hints.put(DecodeHintType.POSSIBLE_FORMATS, formats);

			final LuminanceSource source = new BufferedImageLuminanceSource(image);
			final BinaryBitmap bitmap = new BinaryBitmap(new HybridBinarizer(source));
			final GenericMultipleBarcodeReader reader = new GenericMultipleBarcodeReader(new MultiFormatReader());
			final Result[] readerResults = reader.decodeMultiple(bitmap, hints);
			for (final Result readerResult : readerResults) {
				result.add(readerResult);
			}
		} catch (final NotFoundException e) {
			// no barcodes found: empty result
		}
		return result;
	}

	/**
	 * Removes duplicate results from barcodes
	 */
	public static String[] removeDuplicateCodes(final String[] barcodes) {
		if (barcodes == null || barcodes.length <= 1)
			return barcodes;
		final Set<String> set = new HashSet<>(barcodes.length);
		for (final String barcode : barcodes)
			set.add(barcode);
		return Collection.toArray(set, String.class);
	}

	/**
	 * Removes duplicate results from barcodes.
	 * The first instance of each distinct barcode is kept.
	 * TODO: Write test
	 */
	public static List<Result> removeDuplicateCodes(final java.util.Collection<Result> barcodes) {
		final List<Result> result = new ArrayList<>();
		if (barcodes == null || barcodes.isEmpty())
			return result;
		for (final Result barcode : barcodes) {
			if (!result.stream().filter(r -> r.getText().equals(barcode.getText())).findFirst().isPresent())
				result.add(barcode);
		}
		return result;
	}

	private static BufferedImage convolve(final BufferedImage source, final Kernel kernel) {
		final int w = source.getWidth();
		final int h = source.getHeight();
		final BufferedImage gray = new BufferedImage(w, h, BufferedImage.TYPE_BYTE_GRAY);
		final BufferedImageOp op = new ConvolveOp(kernel);
		return op.filter(source, gray);
	}

//	public static void main(String[] argv) {
//		try {
//			BinaryHolder b = BinaryHolderUtil.create(new File("C:\\Test\\QR-Code_Testing.pdf"));
//			List<String> l = readBarcodes(b, 0);
//			for (String s : l) {
//				final String pct = HTTPUtil.percentEncode(s);
//				BinaryHolder h = BinaryHolderUtil.create(s, StandardCharsets.UTF_8);
//				try (OutputStream out = new FileOutputStream("C:\\Test\\utf8.txt")) {
//					h.writeTo(out);
//				}
//				System.out.println(pct);
//			}
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
//	}

	public static boolean isBarcode(Result code) {
		return Arrays.asList(barcodeFormats).contains(code.getBarcodeFormat());
	}

	public static boolean isQRCode(Result code) {
		return Arrays.asList(qrcodeFormats).contains(code.getBarcodeFormat());
	}

}

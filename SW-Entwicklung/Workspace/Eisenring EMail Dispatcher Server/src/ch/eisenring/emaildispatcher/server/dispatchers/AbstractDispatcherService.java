package ch.eisenring.emaildispatcher.server.dispatchers;

import static ch.eisenring.emaildispatcher.server.dispatchers.ServiceStatus.IDLE;
import static ch.eisenring.emaildispatcher.server.dispatchers.ServiceStatus.LAUNCHING;
import static ch.eisenring.emaildispatcher.server.dispatchers.ServiceStatus.OFFLINE;
import static ch.eisenring.emaildispatcher.server.dispatchers.ServiceStatus.RUNNING;
import static ch.eisenring.emaildispatcher.server.dispatchers.ServiceStatus.SHUTDOWN;

import java.util.concurrent.atomic.AtomicReference;

import ch.eisenring.app.shared.Configurable;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.threading.ThreadCore;
import ch.eisenring.emaildispatcher.server.EMailDispatcherServer;

public abstract class AbstractDispatcherService implements Configurable {

	protected final EMailDispatcherServer server;
	protected final String serviceName;

	private final AtomicReference<ServiceStatus> status = new AtomicReference<ServiceStatus>(OFFLINE);
	private Thread thread;
	
	// --------------------------------------------------------------
	// ---
	// --- Constructor
	// ---
	// --------------------------------------------------------------
	protected AbstractDispatcherService(final EMailDispatcherServer server,
			                            final String serviceName) {
		this.server = server;
		this.serviceName = serviceName;
	}
	
	public final String getServiceName() {
		return serviceName;
	}

	// --------------------------------------------------------------
	// ---
	// --- Startup/Shutdown
	// ---
	// --------------------------------------------------------------
	public final void launch() throws Exception {
		if (!status.compareAndSet(OFFLINE, LAUNCHING)) {
			throw new IllegalStateException(Strings.concat(getServiceName(), " is in invalid status for launch(): ", status.get()));
		}
		
		// initialize the service
		try {
			launchImpl();
		} catch (final Exception e) {
			// launch failed
			status.set(OFFLINE);
			throw e;
		}
		
		// launch thread for service
		final Runnable task = new Runnable() {
			@Override
			public void run() {
				mainLoop();
			}
		};
		thread = server.getThreadPool().start(task, getServiceName(), Thread.NORM_PRIORITY - 2);
		// wait for startup to complete
		final long timeout = System.currentTimeMillis() + 60000;
Loop:		while (true) {
			final ServiceStatus status = this.status.get();
			switch (status) {
				case IDLE:
				case RUNNING:
					break Loop;
				default:
					if (System.currentTimeMillis() > timeout)
						throw new RuntimeException(Strings.concat(
							getServiceName(), " failed to start in reasonable time (60 seconds)"));
					Thread.sleep(100);
					break;
			}
		}
	}
	
	/**
	 * The main loop runs until it catches an error from the
	 * service's tickImpl() method or it detects service shutdown.
	 */
	final void mainLoop() {
		// transition from LAUNCHING to IDLE
		if (!status.compareAndSet(ServiceStatus.LAUNCHING, ServiceStatus.IDLE)) {
			// state error
			server.shutdown(new IllegalStateException(Strings.concat(
				getServiceName(), ":mainLoop() status mismatch: ", LAUNCHING, " != ", status.get())));
			System.exit(20);
			throw new RuntimeException("WTF?");
		}
		
		Throwable error = null;
		while (true) {
			if (status.compareAndSet(SHUTDOWN, OFFLINE)) {
				// shutdown requested
				return;
			}
			try {
				final long delay = getTickDelay();
				Thread.sleep(delay);
			} catch (final InterruptedException e) {
				if (status.compareAndSet(SHUTDOWN, OFFLINE)) {
					// normal shutdown
					return;
				}
				// interrupted, but not in shutdown state...
				error = e;
				break;
			}
			if (status.compareAndSet(IDLE, RUNNING)) {
				try {
					tickImpl();
				} catch (final Exception e) {
					error = e;
					break;
				}
			} else {
				error = new IllegalStateException(Strings.concat(
					getServiceName(), ":mainLoop() status mismatch: ", IDLE, " != ", status.get()));
				break;
			}
			if (!status.compareAndSet(RUNNING, IDLE)) {
				error = new IllegalStateException(Strings.concat(
					getServiceName(), ":mainLoop() status mismatch: ", RUNNING, " != ", status.get()));
				break;
			}
		}
		status.set(OFFLINE);
		if (error != null) {
			server.shutdown(error);
			System.exit(20);
			throw new RuntimeException("WTF?");
		}
	}

	public final void shutdown() throws Exception {
		if (!status.compareAndSet(ServiceStatus.IDLE, ServiceStatus.SHUTDOWN) &&
			!status.compareAndSet(ServiceStatus.RUNNING, ServiceStatus.SHUTDOWN)) {
			throw new IllegalStateException(Strings.concat(
				getServiceName(), " is in invalid status for shutdown(): ", status));
		}

		// set the status to SHUTDOWN and
		// signal shutdown to the service thread
		status.set(ServiceStatus.SHUTDOWN);
		thread.interrupt();

		// wait for the service to shut down
		final long timeout = System.currentTimeMillis() + 10000;
		while (status.get() != ServiceStatus.OFFLINE) {
			ThreadCore.sleep(100);
			if (System.currentTimeMillis() > timeout) {
				// force the shutdown to continue
				status.set(ServiceStatus.OFFLINE);
				throw new RuntimeException(Strings.concat(getServiceName(), " is taking too long to shut down"));
			}
			thread.interrupt();
		}
	}

	// --------------------------------------------------------------
	// ---
	// --- Service implementation
	// ---
	// --------------------------------------------------------------
	/**
	 * Called before the service enters "running" state.
	 * If the method throws, the exception will bubble
	 * up to the server core (and abort the startup process)
	 */
	protected void launchImpl() throws Exception {
		// default implementation does nothing 
	}

	/**
	 * Called periodically from the services main loop.
	 * If the method throws, the service will be terminated.
	 */
	protected void tickImpl() throws Exception {
		// default implementation does nothing 
	}

	/**
	 * Called when the service is shut down.
	 * An exception will not stop the shutdown, it
	 * will just be logged and the shutdown continues.
	 */
	protected void shutdownImpl() throws Exception {
		// default implementation does nothing 
	}

	/**
	 * Gets the number of milliseconds until the next tick
	 * should occur. Default implementation is 5000 = 5 seconds.
	 */
	protected long getTickDelay() {
		return 5000;
	}

}

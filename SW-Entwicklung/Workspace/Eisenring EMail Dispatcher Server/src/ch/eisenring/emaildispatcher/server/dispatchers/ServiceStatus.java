package ch.eisenring.emaildispatcher.server.dispatchers;

public enum ServiceStatus {

	/**
	 * The service is not yet start or has been shut down
	 */
	OFFLINE,
	
	/**
	 * The service is in the process of starting up
	 */
	LAUNCHING,
	
	/**
	 * The service is up but idle (waiting)
	 */
	IDLE,
	
	/**
	 * The service is up and doing something
	 */
	RUNNING,
	
	/**
	 * The service is in the process of shutting down
	 */
	SHUTDOWN

}

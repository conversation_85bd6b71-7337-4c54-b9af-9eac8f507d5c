package ch.eisenring.logiware;

import java.sql.Connection;
import java.sql.SQLException;

import ch.eisenring.jdbc.ConnectionConfigurator;
import ch.eisenring.model.TransactionContext;
import ch.eisenring.model.TransactionSink;
import ch.eisenring.model.engine.jdbc.DatabaseSpecifier;

public abstract class LWContextSink extends LWTransactionBase implements TransactionSink {

	// --------------------------------------------------------------
	// ---
	// --- Constructors
	// ---
	// --------------------------------------------------------------
	public LWContextSink(final DatabaseSpecifier database) {
		super(database);
	}

	// --------------------------------------------------------------
	@Override
	public void configure(final Connection connection) throws SQLException {
		ConnectionConfigurator.WRITEABLE.configure(connection);
	}

	/**
	 * The sink must not be used as a source 
	 */
	@Override
	protected final void loadImpl(final TransactionContext context) throws SQLException {
		throw new SQLException("not supported");
	}

}

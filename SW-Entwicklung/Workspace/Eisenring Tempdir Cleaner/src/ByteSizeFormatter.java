

public class ByteSizeFormatter {

	private final static char[][] SIZE_UNITS = {
		{ ' ', 'B', 'y', 't', 'e' },
		{ ' ', 'K', 'B' },
		{ ' ', 'M', 'B' },
		{ ' ', 'G', 'B' },
		{ ' ', 'T', 'B' },
		{ ' ', 'P', 'B' },
		{ ' ', 'E', 'B' }
	};
	
	public static String toString(final long value) {
		final StringBuilder b = new StringBuilder();
		appendTo(b, value);
		return b.toString();
	}

	public static void appendTo(final StringBuilder target, final long value) {
		// shortcut for small values
		if (value < 1000 && value >= 0) {
			target.append(value);
			target.append(SIZE_UNITS[0]);
			return;
		}
		// determine magnitude of size value
		int order = 0;
		long sig = value;
		int decimalPlaces = 0;
		int rem = 0;
		do {
			++order;
			final int shift = order * 10;
			sig = value >>> shift;

			// calculate trailing fractional digits, round as necessary
			// (this is done here because it may trigger a rounding overflow to the next magnitude)
			if (sig < 1000) {
				rem = (int) ((value - (sig << shift)) >>> (shift - 10));
				rem = (rem * 1000) >>> 10;
				if (sig >= 100) {
					// zero decimal places
					decimalPlaces = 0;
					// round up if necessary
					if (rem >= 500)
						++sig;
				} else if (sig >= 10) {
					// one decimal place
					decimalPlaces = 1;
					if ((rem % 100) < 50) {
						rem /= 100;
					} else {
						rem = (rem / 100) + 1;
						if (rem >= 10) {
							// handle overflow into integer part
							rem = 0;
							++sig;
							if (sig >= 100)
								--decimalPlaces;
						}
					}
				} else {
					// two decimal places
					decimalPlaces = 2;
					if ((rem % 10) < 5) {
						rem /= 10;
					} else {
						rem = (rem / 10) + 1;
						if (rem >= 100) {
							// handle overflow into integer part
							rem = 0;
							++sig;
							if (sig >= 10)
								--decimalPlaces;
						}
					}
					
				}
			}
		} while (sig >= 1000);

		// format integer part
		if (sig >= 100)
			target.append((char) ((sig / 100) + 48));
		if (sig >= 10)
			target.append((char) (((sig % 100) / 10) + 48));
		target.append((char) ((sig % 10) +48));

		// format decimal part
		if (decimalPlaces > 0) {
			target.append('.');
			if (decimalPlaces > 1)
				target.append((char) ((rem / 10) + 48));
			target.append((char) ((rem % 10) + 48));
		}

		target.append(SIZE_UNITS[order]);
	}

}

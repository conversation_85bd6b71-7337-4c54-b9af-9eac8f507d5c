package ch.eisenring.jdbc.sqlbuilder;

import java.sql.SQLException;

import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.StringMakerFriendly;
import ch.eisenring.core.jdbc.Column;
import ch.eisenring.core.jdbc.Table;

public final class SQLColumn implements Column, SQLFragment, CharSequence, StringMakerFriendly {

	protected final String name;
	protected final Table table;
	
	public SQLColumn(final Column column) {
		this.name = column.getColumnName();
		this.table = column.getTable();
	}

	public SQLColumn(final String columnName, final Table table) {
		this.name = columnName;
		this.table = table;
	}

	public SQLColumn(final String columnName) {
		this(columnName, (Table) null);
	}

	@Override
	public String getColumnName() {
		return name;
	}

	@Override
	public Table getTable() {
		if (table == null)
			throw new UnsupportedOperationException();
		return table;
	}

	// --------------------------------------------------------------
	// --- 
	// --- SQLFragment implementation 
	// --- 
	// --------------------------------------------------------------
	@Override
	public void appendTo(final SQLBuilder builder) throws SQLException {
		builder.addSQL(getColumnName());
	}

	// --------------------------------------------------------------
	// ---
	// --- CharSequence
	// ---
	// -------------------------------------------------------------
	@Override
	public final int length() {
		return name.length();
	}

	@Override
	public final char charAt(final int index) {
		return name.charAt(index);
	}

	@Override
	public final CharSequence subSequence(final int start, final int end) {
		return name.substring(start, end);
	}

	// --------------------------------------------------------------
	// --- 
	// --- StringMakerFriendly implementation 
	// --- 
	// --------------------------------------------------------------
	@Override
	public void toStringMaker(final StringMaker maker) {
		maker.append(name);
	}
	
	// --------------------------------------------------------------
	// --- 
	// --- Object overrides 
	// --- 
	// --------------------------------------------------------------
	@Override
	public int hashCode() {
		return name.hashCode();
	}

	@Override
	public boolean equals(final Object o) {
		return o instanceof SQLColumn && name.equals(((SQLColumn) o).name);
	}

	@Override
	public String toString() {
		return name;
	}

}

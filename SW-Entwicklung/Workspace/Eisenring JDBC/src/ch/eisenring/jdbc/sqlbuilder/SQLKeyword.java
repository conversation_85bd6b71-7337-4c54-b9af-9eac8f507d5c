package ch.eisenring.jdbc.sqlbuilder;

import java.sql.SQLException;

import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.StringMakerFriendly;

/**
 * Provides all SQL keywords predefined.
 * 
 * The classes in this package recognize instances of this class
 * and may treat them specially depending on context.
 * The behavior achieved with the statically defined keyword instances
 * can NOT be emulated by placing custom string fragments into a statement. 
 */
public final class SQLKeyword implements SQLFragment, CharSequence, StringMakerFriendly {

	public final static SQLKeyword SELECT = new SQLKeyword("SELECT");
	public final static SQLKeyword INSERT = new SQLKeyword("INSERT");
	public final static SQLKeyword UPDATE = new SQLKeyword("UPDATE");
	public final static SQLKeyword DELETE = new SQLKeyword("DELETE");
	public final static SQLKeyword FROM = new SQLKeyword("FROM");
	public final static SQLKeyword WHERE = new SQLKeyword("WHERE");
	public final static SQLKeyword AND = new SQLKeyword("AND");
	public final static SQLKeyword OR = new SQLKeyword("OR");
	public final static SQLKeyword AS = new SQLKeyword("AS");
	public final static SQLKeyword ORDERBY = new SQLKeyword("ORDER BY");
	public final static SQLKeyword GROUPBY = new SQLKeyword("GROUP BY");
	public final static SQLKeyword ASC = new SQLKeyword("ASC");
	public final static SQLKeyword DESC = new SQLKeyword("DESC");
	public final static SQLKeyword SET = new SQLKeyword("SET");
	public final static SQLKeyword JOIN = new SQLKeyword("JOIN");
	public final static SQLKeyword ON = new SQLKeyword("ON");
	
	public final static SQLWhereCondition TRUE = new SQLTrueExpression();
	public final static SQLWhereCondition FALSE = new SQLFalseExpression();
	
	protected final String keyword;
	
	protected SQLKeyword(final String keyword) {
		this.keyword = keyword.toUpperCase();
	}

	// --------------------------------------------------------------
	// ---
	// --- SQLFragment implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public void appendTo(final SQLBuilder builder) throws SQLException {
		builder.addSQL(keyword);
	}

	// --------------------------------------------------------------
	// ---
	// --- CharSequence
	// ---
	// -------------------------------------------------------------
	@Override
	public final int length() {
		return keyword.length();
	}

	@Override
	public final char charAt(final int index) {
		return keyword.charAt(index);
	}

	@Override
	public final CharSequence subSequence(final int start, final int end) {
		return keyword.substring(start, end);
	}
	
	// --------------------------------------------------------------
	// --- 
	// --- StringMakerFriendly implementation 
	// --- 
	// --------------------------------------------------------------
	@Override
	public void toStringMaker(final StringMaker maker) {
		maker.append(keyword);
	}

	// --------------------------------------------------------------
	// --- 
	// --- Object overrides 
	// --- 
	// --------------------------------------------------------------
	@Override
	public int hashCode() {
		return keyword.hashCode();
	}

	@Override
	public boolean equals(final Object o) {
		return o instanceof SQLKeyword && keyword.equals(((SQLKeyword) o).keyword);
	}

	@Override
	public String toString() {
		return keyword;
	}
	
}

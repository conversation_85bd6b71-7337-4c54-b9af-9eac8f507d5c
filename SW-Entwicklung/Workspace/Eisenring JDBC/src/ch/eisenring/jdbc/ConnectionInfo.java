package ch.eisenring.jdbc;

import java.io.IOException;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.Iterator;

import ch.eisenring.core.application.Configuration;
import ch.eisenring.core.application.ConfigurationException;
import ch.eisenring.core.collections.Map;
import ch.eisenring.core.collections.impl.HashMap;
import ch.eisenring.core.datatypes.primitives.Primitives;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.io.StreamReader;
import ch.eisenring.core.io.StreamUtil;
import ch.eisenring.core.io.StreamWriter;
import ch.eisenring.core.io.Streamable;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.core.threading.ThreadCore;

/**
 * Database connection information holder.
 */
public final class ConnectionInfo implements ConnectionProvider, ConnectionPoolConfiguration, Streamable {

	public final static String KEY_DRIVER = "Driver";
	private final static String KEY_URL = "URL";
	public final static String KEY_SERVER = "Server";
	public final static String KEY_DATABASE = "Database";
	public final static String KEY_USER = "User";
	public final static String KEY_PASSWORD = "Password";
	private final static String KEY_SCHEMA = "Schema";
	private final static String KEY_POOLABLE = "Poolable";
	private final static String KEY_POOLSIZEMIN = "PoolSizeMin";
	private final static String KEY_POOLSIZEMAX = "PoolSizeMax";
	private final static String KEY_POOLIDLETIME = "PooledIdleTime";
	private final static String KEY_POOLLIFETIME = "PooledLifeTime";
	private final static String KEY_JDBC_LEVEL = "JDBCLevel";
	private final static String KEY_ISVALID = "IsValid";

	private final Map<String, Object> properties = new HashMap<>();
	
	/**
	 * Sets a property value
	 */
	public void setProperty(final String key, final String value) {
		if (value == null) {
			properties.remove(key);
		} else {
			properties.put(key, value);
		}
	}
	
	/**
	 * Gets a properties value
	 */
	public String getProperty(final String key) {
		final Object value = properties.get(key);
		return value == null ? null : value.toString();
	}

	/**
	 * Gets a property as integer
	 */
	public int getPropertyInteger(final String key, final int defaultValue) {
		final CharSequence value = getProperty(key);
		if (value != null) {
			try {
				return Strings.parseInt(value);
			} catch (final NumberFormatException e) {
				// ignore
			}
		}
		return defaultValue;
	}

	/**
	 * Loads the driver class specified for this connection info
	 */
	final void loadDriverClass() throws SQLException {
		final String className = getProperty(KEY_DRIVER);
		try {
			DriverLoader.loadDriver(className);
		} catch (final ClassNotFoundException e) {
			final String error = Strings.concat("jdbc driver class \"", className, "\" not found");
			Logger.error(error);
			throw new SQLException(error, e);
		}
	}

	/**
	 * Gets the JDBC URL used to connect to this DB
	 */
	public final String getURL() {
		final StringMaker url = StringMaker.obtain();
		url.append(getProperty(KEY_URL));
		url.replace("${Server}", getProperty(KEY_SERVER));
		url.replace(JDBCConstants.VAR_DATABASE, getProperty(KEY_DATABASE));
		url.replace("${User}", getProperty(KEY_USER));
		url.replace("${Password}", getProperty(KEY_PASSWORD));
		url.replace(JDBCConstants.VAR_SCHEMA, getProperty(KEY_SCHEMA));
		return url.release();
	}
	
	/**
	 * Opens a connection to this database.
	 * The connection will use the driver defaults for all settings,
	 * it is recommended that the user configures the connection
	 * to its specific needs before using it.
	 */
	public final JDBCConnection open(final int timeoutMillis) throws SQLException {
		return open(timeoutMillis, null);
	}

	/**
	 * Opens a connection to this database and configures it using
	 * the supplied ConnectionConfigurator.
	 * The connection MAY be pooled, this can be configured into the
	 * connection info (property POOLABLE = yes)
	 */
	public final JDBCConnection open(final int timeoutMillis, final ConnectionConfigurator configurator) throws SQLException {
		final JDBCConnection connection;
		if (Boolean.TRUE.equals(properties.get(KEY_POOLABLE))) {
			final ConnectionPoolManager manager = ConnectionPoolManager.getInstance();
			final ConnectionPool pool = manager.getConnectionPool(this);
			connection = pool.obtain();
//			if (Logger.isTraceEnabled())
//				Logger.trace(Strings.concat("obtained JDBC-Connection from pool: ", connection));			
		} else {
			connection = create(timeoutMillis);		
//			if (Logger.isTraceEnabled())
//				Logger.trace(Strings.concat("created new JDBC-Connection to: ", getDatabase()));			
		}
		if (configurator == null) {
			connection.setTransactionIsolation(Connection.TRANSACTION_READ_COMMITTED);
		} else {
			try {
				configurator.configure(connection);
			} catch (final Throwable t) {
				Logger.error(Strings.concat("configure() for connection ", connection, " failed"));
				Logger.error(t);
				if (connection != null) {
					try {
						connection.close();
					} catch (final SQLException e) {
						// ignore
					}
				}
				if (t instanceof SQLException)
					throw (SQLException) t;
				throw new SQLException(t.getMessage(), t);
			}
		}
		return connection;
	}

	@Override
	public String getDatabase() {
		return getProperty(KEY_DATABASE);
	}
	
	@Override
	public String getSchema() {
		return getProperty(KEY_SCHEMA);
	}

	// --------------------------------------------------------------
	// ---
	// --- Streamable implementation
	// ---
	// --------------------------------------------------------------
	private ConnectionInfo() {
	}

	@Override
	public void read(final StreamReader reader) throws IOException {
		StreamUtil.readMap(reader, properties);
	}

	@Override
	public void write(final StreamWriter writer) throws IOException {
		StreamUtil.writeMap(writer, properties);
	}

	// --------------------------------------------------------------
	// ---
	// --- Object overrides
	// ---
	// --------------------------------------------------------------
	private final static String[] EQUALITY_RELEVANT_PROPERTIES = {
		KEY_DATABASE,
		KEY_USER,
		KEY_PASSWORD,
		KEY_SCHEMA,
		KEY_URL,
		KEY_SERVER,
		KEY_DRIVER,
		KEY_POOLABLE
	};

	/**
	 * HashCode is cached and evaluated lazily (heavy cost)
	 */
	private int hashCode;
	
	@Override
	public int hashCode() {
		if (hashCode == 0) {
			int h = 0;
			for (final String propertyKey : EQUALITY_RELEVANT_PROPERTIES) {
				final Object rawValue = properties.get(propertyKey);
				final String propertyValue = rawValue == null ? null : rawValue.toString();
				h = (h * 31) ^ Strings.hashCode(propertyValue);
			}
			hashCode = h;
		}
		return hashCode;
	}

	@Override
	public boolean equals(final Object o) {
		if (o instanceof ConnectionInfo) {
			final ConnectionInfo i = (ConnectionInfo) o;
			if (hashCode() != i.hashCode())
				return false;
			for (final String propertyKey : EQUALITY_RELEVANT_PROPERTIES) {
				final Object v1 = properties.get(propertyKey);
				final Object v2 = i.properties.get(propertyKey);
				if (!Primitives.equals(v1, v2))
					return false;
			}
			return true;
		}
		return false;
	}

	// --------------------------------------------------------------
	// ---
	// --- ConnectionCreator implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public JDBCConnection create(final int timeoutMillis) throws SQLException {
		// load the database driver
		loadDriverClass();
		final String url = getURL();
		final String user = getProperty(KEY_USER);
		final String password = getProperty(KEY_PASSWORD);
		final long timeout = System.currentTimeMillis() + timeoutMillis;
		while (true) {
			try {
				final Connection connection = DriverManager.getConnection(url, user, password);
				return new JDBCConnection(connection, getDatabase(), getSchema());
			} catch (final SQLException e) {
				if (timeout < System.currentTimeMillis()) {
					Logger.warn(Strings.concat("connection to ", getProperty(KEY_DATABASE), " failed: ", e.getMessage()));
					Logger.warn(e);
					throw e;
				}
			}
			ThreadCore.sleep(200);
		}
	}
	
	// --------------------------------------------------------------
	// ---
	// --- ConnectionPoolConfiguration implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public int getPoolSizeMin() {
		final Object value = properties.get(KEY_POOLSIZEMIN);
		return value instanceof Number ? ((Number) value).intValue() : 0;
	}

	@Override
	public int getPoolSizeMax() {
		final Object value = properties.get(KEY_POOLSIZEMAX);
		return value instanceof Number ? ((Number) value).intValue() : 4;
	}

	@Override
	public long getPooledIdleTime() {
		final Object value = properties.get(KEY_POOLIDLETIME);
		return value instanceof Number ? ((Number) value).longValue() : 1800;
	}

	@Override
	public long getPooledLifeTime() {
		final Object value = properties.get(KEY_POOLLIFETIME);
		return value instanceof Number ? ((Number) value).longValue() : 21600;
	}

	@Override
	public int getJDBCLevel() {
		final Object value = properties.get(KEY_JDBC_LEVEL);
		return value instanceof Number ? ((Number) value).intValue() : 4;
	}

	@Override
	public String getIsValidStatement() {
		final Object value = properties.get(KEY_ISVALID);
		return Strings.clean(value);
	}

	// --------------------------------------------------------------
	// ---
	// --- Property parsing
	// ---
	// --------------------------------------------------------------
	public static ConnectionInfo create(final String baseKey,
			final Configuration configuration) throws ConfigurationException {
		final ConnectionInfo info = new ConnectionInfo();
		// copy all properties under the base key to the connection info
		final Map<String, String> propertyMap = configuration.getPropertyMap();
		final Iterator<String> keyItr = propertyMap.keySet().iterator();
		while (keyItr.hasNext()) {
			final String key = keyItr.next();
			if (key != null && key.startsWith(baseKey)) {
				final String value = propertyMap.get(key);
				final String subKey = Strings.subString(key, baseKey.length());
				info.setProperty(subKey, value);
			}
		}
		// get static defined properties
		final String driver = configuration.getString(Strings.concat(baseKey, KEY_DRIVER), null);
		final String url = configuration.getString(Strings.concat(baseKey, KEY_URL), null);
		final String server = configuration.getString(Strings.concat(baseKey, KEY_SERVER), null);
		final String database = configuration.getString(Strings.concat(baseKey, KEY_DATABASE), null);
		final String user = configuration.getString(Strings.concat(baseKey, KEY_USER), null);
		final String password = configuration.getString(Strings.concat(baseKey, KEY_PASSWORD), null);
		final String schema = configuration.getString(Strings.concat(baseKey, KEY_SCHEMA), null);
		final boolean poolable = configuration.getBoolean(Strings.concat(baseKey, KEY_POOLABLE), false);
		final int poolSizeMin = configuration.getInteger(Strings.concat(baseKey, KEY_POOLSIZEMIN), 0, 100000, 0);
		final int poolSizeMax = configuration.getInteger(Strings.concat(baseKey, KEY_POOLSIZEMAX), 1, 100000, 4);
		final int poolIdleTime = configuration.getInteger(Strings.concat(baseKey, KEY_POOLIDLETIME), 1, 1000000, 1800);
		final int poolLifeTime = configuration.getInteger(Strings.concat(baseKey, KEY_POOLLIFETIME), 1, 1000000, 21600);
		final int jdbcLevel = configuration.getInteger(Strings.concat(baseKey, KEY_JDBC_LEVEL), Integer.MIN_VALUE, Integer.MAX_VALUE, 4);
		final String isValid = configuration.getString(Strings.concat(baseKey, KEY_ISVALID), null);
		// validate and apply static defined properties
		if (Strings.isEmpty(driver))
			throw new ConfigurationException("missing/invalid configuration property: "+baseKey+KEY_DRIVER);
		info.properties.put(KEY_DRIVER, driver);
		if (Strings.isEmpty(url))
			throw new ConfigurationException("missing/invalid configuration property: "+baseKey+KEY_URL);
		info.properties.put(KEY_URL, url);
		if (Strings.isEmpty(server))
			throw new ConfigurationException("missing/invalid configuration property: "+baseKey+KEY_SERVER);
		info.properties.put(KEY_SERVER, server);
		if (Strings.isEmpty(database))
			throw new ConfigurationException("missing/invalid configuration property: "+baseKey+KEY_DATABASE);
		info.properties.put(KEY_DATABASE, database);
		if (Strings.isEmpty(user))
			throw new ConfigurationException("missing/invalid configuration property: "+baseKey+KEY_USER);
		info.properties.put(KEY_USER, user);
		if (Strings.isEmpty(password))
			throw new ConfigurationException("missing/invalid configuration property: "+baseKey+KEY_PASSWORD);
		info.properties.put(KEY_PASSWORD, password);
		if (Strings.isEmpty(schema))
			throw new ConfigurationException("missing/invalid configuration property: "+baseKey+KEY_SCHEMA);
		info.properties.put(KEY_SCHEMA, schema);
		// pool configuration
		if (poolSizeMin > poolSizeMax)
			throw new ConfigurationException(Strings.concat(
				baseKey, KEY_POOLSIZEMIN, " must not be larger than ", baseKey, KEY_POOLSIZEMAX));
		if (poolIdleTime > poolLifeTime)
			throw new ConfigurationException(Strings.concat(
				baseKey, KEY_POOLIDLETIME, " must not be larger than ", baseKey, KEY_POOLLIFETIME));
		switch (jdbcLevel) {
			default:
				throw new ConfigurationException(Strings.concat(
					baseKey, KEY_JDBC_LEVEL, " is invalid (only 3 and 4 are supported)"));
			case 3:
				if (poolable) {
					if (Strings.isEmpty(isValid))
						throw new ConfigurationException(Strings.concat(
							baseKey, KEY_JDBC_LEVEL, " value of 3 in conjunction with Poolable=yes requires IsValid property to be set"));
				}
				break;
			case 4:
				// with JDBC4 driver level, any combination of Poolable and IsValid is allowed
				break;
		}
		info.properties.put(KEY_POOLABLE, Boolean.valueOf(poolable));
		info.properties.put(KEY_POOLSIZEMIN, Integer.valueOf(poolSizeMin));
		info.properties.put(KEY_POOLSIZEMAX, Integer.valueOf(poolSizeMax));
		info.properties.put(KEY_POOLIDLETIME, Long.valueOf(poolIdleTime * 1000));
		info.properties.put(KEY_POOLLIFETIME, Long.valueOf(poolLifeTime * 1000));
		info.properties.put(KEY_JDBC_LEVEL, Integer.valueOf(jdbcLevel));
		info.properties.put(KEY_ISVALID, (String) isValid);
		return info;
	}

}

package ch.eisenring.jdbc;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.concurrent.TimeUnit;

import ch.eisenring.core.collections.Collection;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.core.logging.Logger.LogLevel;

final class ManagedConnectionPool implements ConnectionPool {

	private final int DEFAULT_JDBC_LEVEL = 3;

	protected final List<PoolableConnection> pool = new ArrayList<PoolableConnection>(16);

	protected final ConnectionPoolManager manager;
	protected final ConnectionProvider provider;

	protected int sizeMin;
	protected int sizeMax;
	protected long idleTime;
	protected long lifeTime;

	protected String isValidStatement;
	protected int jdbcLevel = DEFAULT_JDBC_LEVEL;
	
	public ManagedConnectionPool(final ConnectionProvider connectionProvider,
								 final ConnectionPoolManager manager) {
		this.provider = connectionProvider;
		this.manager = manager;
		configure((ConnectionPoolConfiguration) null);
	}

	// --------------------------------------------------------------
	// ---
	// --- Connection management
	// ---
	// --------------------------------------------------------------
	@Override
	public PoolableConnection obtain() throws SQLException {
		while (true) {
			PoolableConnection connection;
			synchronized (this) {
				connection = pool.isEmpty() ? null : pool.remove(0);
			}
			if (connection == null) {
				// the pool is exhausted, create a new connection
				return new PoolableConnection(provider.create(5000),
						provider.getDatabase(), provider.getSchema(), this);
			}
			try {
				if (!checkAge(connection)) {
					continue;
				}
				if (connection.reuse()) {
					return connection;
				} else {
					if (Logger.isEnabled(LogLevel.TRACE))
						Logger.trace(Strings.concat("JDBC-Connection reuse failed: ", connection));
					destroy(connection);
				}
			} catch (final Throwable e) {
				destroy(connection);
			}
		}
	}

	@Override
	public void release(final PoolableConnection connection) {
		if (connection == null)
			return;
		synchronized (this) {
			if (pool.contains(connection)) {
				Logger.error(Strings.concat("connection ", connection, " returned to pool, but was already in pool!"));
			} else {
				pool.add(connection);
			}
		}
//		if (Logger.isEnabled(LogLevel.TRACE))
//			Logger.trace(Strings.concat("JDBC-Connection returned to pool: ", connection));
	}

	@Override
	public void configure(final ConnectionPoolConfiguration poolConfiguration) {
		if (poolConfiguration == null) {
			// set default values
			sizeMin = 0;
			sizeMax = 10;
			idleTime = TimeUnit.MINUTES.toMillis(3);
			lifeTime = TimeUnit.MINUTES.toMillis(15);
			jdbcLevel = DEFAULT_JDBC_LEVEL;
			isValidStatement = null;
		} else {
			sizeMin = poolConfiguration.getPoolSizeMin();
			sizeMax = poolConfiguration.getPoolSizeMax();
			idleTime = poolConfiguration.getPooledIdleTime();
			lifeTime = poolConfiguration.getPooledLifeTime();
			jdbcLevel = poolConfiguration.getJDBCLevel();
			isValidStatement = poolConfiguration.getIsValidStatement();
		}
	}

	@Override
	public String getDatabase() {
		return provider.getDatabase();
	}

	@Override
	public String getSchema() {
		return provider.getSchema();
	}

	@Override
	public int getJDBCLevel() {
		return jdbcLevel;
	}

	@Override
	public String getIsValidStatement() {
		return isValidStatement;
	}

	/**
	 * Checks the age of connection.
	 * Return true if the connection is still usable.
	 * Otherwise, the connection is destroyed and false is returned. 
	 */
	public boolean checkAge(final PoolableConnection connection) throws SQLException {
		boolean result = true;
		if (connection.getAge() > lifeTime) {
			if (Logger.isEnabled(LogLevel.TRACE))
				Logger.trace(Strings.concat("JDBC-Connection exceeded life time: ", connection));
			result = false;
		}
		if (connection.getIdleTime() > idleTime) {
			if (Logger.isEnabled(LogLevel.TRACE))
				Logger.trace(Strings.concat("JDBC-Connection exceeded idle time: ", connection));
			result = false;
		}
		if (!result)
			destroy(connection);
		return result;
	}

	/**
	 * Destroys a connection. If the connection is in the pool,
	 * also removes it from the pool. 
	 */
	protected void destroy(final Connection connection) {
		if (connection == null)
			return;
		synchronized (this) {
			pool.remove(connection);
		}
		final Connection delegate;
		if (connection instanceof PoolableConnection) {
			delegate = ((PoolableConnection) connection).delegate;
		} else {
			delegate = connection;
		}
		try {
			delegate.close();
		} catch (final Throwable t) {
			Logger.warn(Strings.concat("error destroying pooled connection: ", t.getMessage()));
			Logger.warn(t);
		}
		if (Logger.isEnabled(LogLevel.TRACE))
			Logger.trace(Strings.concat("JDBC-Connection destroyed: ", connection));
	}

	@Override
	public void validate() {
		int destructionCount = 0;
		int creationCount = 0;
		synchronized (this) {
			final PoolableConnection[] connections;
			final int size = pool.size();
			if (size > 0) {
				connections = Collection.toArray(pool, PoolableConnection.class);
				for (int i=connections.length-1; i>=0; --i) {
					final PoolableConnection connection = connections[i];
					try {
						if (!checkAge(connection))
							++destructionCount;
					} catch (final SQLException e) {
					}
				}
			}
		}
		// check that pool size is in configured range
		while (true) {
			PoolableConnection connection = null;
			final int size;
			synchronized (this) {
				size = pool.size();
				if (size > sizeMax)
					connection = pool.remove(0);
			}
			if (connection != null) {
				// destroy connection just removed
				++destructionCount;
				destroy(connection);
			} else if (size < sizeMin) {
				// add a connection to the pool
				try {
					connection = new PoolableConnection(provider.create(5000),
							provider.getDatabase(), provider.getSchema(), this);
					synchronized (this) {
						pool.add(connection);
					}
					++creationCount;
				} catch (final SQLException e) {
					break;
				}
			} else {
				break;
			}
		};
		if (creationCount > 0 || destructionCount > 0) {
			if (Logger.isTraceEnabled() && creationCount > 0) {
				final int size;
				synchronized (this) {
					size = pool.size();
				}
				final StringMaker message = StringMaker.obtain(128);
				message.append(provider.getDatabase());
				message.append('.');
				message.append(provider.getSchema());
				message.append(" pool size ");
				message.append(size);
				message.append(" (+");
				message.append(creationCount);
				message.append("/-");
				message.append(destructionCount);
				message.append(')');
				Logger.trace(message);
				message.releaseSilent();
			}
		}
	}

}

package ch.eisenring.presento.server.api;

import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.codetype.StaticCode;

/**
 * Defines absence reasons for PRESENTO
 */
public class PresentoAbsenceCode extends StaticCode {

	public final static PresentoAbsenceCode NULL = new PresentoAbsenceCode(
			0, null, "", "");
	
	public final static PresentoAbsenceCode KBZ = new PresentoAbsenceCode(
			7, "KBZ", "Krankheit bezahlt");

	public final static PresentoAbsenceCode MIL = new PresentoAbsenceCode(
			9, "MIL", "Militär");

	public final static PresentoAbsenceCode ZIV = new PresentoAbsenceCode(
			10, "ZIV", "Zivilschutz");

	public final static PresentoAbsenceCode BUBZ = new PresentoAbsenceCode(
			11, "BUBZ", "Betriebsunfall bezahlt");

	public final static PresentoAbsenceCode SCH = new PresentoAbsenceCode(
			12, "SCH", "Schule Lehrl.");

	public final static PresentoAbsenceCode TBE = new PresentoAbsenceCode(
			13, "TBE", "Todesfall / Beerdigung");
	
	public final static PresentoAbsenceCode GLA = new PresentoAbsenceCode(
			15, "GLA", "GLA-Bezug");

	public final static PresentoAbsenceCode UNB = new PresentoAbsenceCode(
			16, "UNB", "unbezahlte Abs.");

	public final static PresentoAbsenceCode FAM = new PresentoAbsenceCode(
			17, "FAM", "Familie");

	public final static PresentoAbsenceCode FER = new PresentoAbsenceCode(
			19, "FER", "Ferien");

	public final static PresentoAbsenceCode FEI = new PresentoAbsenceCode(
			20, "FEI", "Feiertag");

	public final static PresentoAbsenceCode KSA = new PresentoAbsenceCode(
			21, "KSA", "Kompensation Samstag");
	
	public final static PresentoAbsenceCode UEB = new PresentoAbsenceCode(
			23, "UEB", "Kompensation");

	public final static PresentoAbsenceCode SBE = new PresentoAbsenceCode(
			86, "SBE", "Samstag Bezugstag");

	public final static PresentoAbsenceCode UFER = new PresentoAbsenceCode(
			96, "UFER", "Unbezahlte Ferien");

	public final static PresentoAbsenceCode WEB = new PresentoAbsenceCode(
			97, "WEB", "Weiterbildung");

	public final static PresentoAbsenceCode MUS = new PresentoAbsenceCode(
			102, "MUS", "Mutterschaftsurlaub");

	public final static PresentoAbsenceCode KST = new PresentoAbsenceCode(
			103, "KST", "Krankheit Std. bezahlt");

	public final static PresentoAbsenceCode BUS = new PresentoAbsenceCode(
			104, "BUS", "Betriebsunfall Stunden");

	public final static PresentoAbsenceCode NBS = new PresentoAbsenceCode(
			105, "NBS", "Nichtbetriebsunfall Std. bezahlt");

	public final static PresentoAbsenceCode WOW = new PresentoAbsenceCode(
			120, "WOW", "Wohnungswechsel");

	public final static PresentoAbsenceCode HOZ = new PresentoAbsenceCode(
			121, "HOZ", "Hochzeit");

	public final static PresentoAbsenceCode GEB = new PresentoAbsenceCode(
			122, "GEB", "Geburt");

	public final static PresentoAbsenceCode KNB = new PresentoAbsenceCode(
			123, "KNB", "Krankheit unbezahlt");

	public final static PresentoAbsenceCode NBBZ = new PresentoAbsenceCode(
			124, "NBBZ", "Nichtbetriebsunfall Bezahlt");

	protected PresentoAbsenceCode(final int id, final String shortText, final String longText) {
		super(id, Integer.valueOf(id), shortText, longText);
	}
	
	protected PresentoAbsenceCode(final int id, final Object key,
			    final String shortText, final String longText) {
		super(id, key, shortText, longText);
	}

	@Override
	public Class<? extends AbstractCode> getTypeClass() {
		return PresentoAbsenceCode.class;
	}

}

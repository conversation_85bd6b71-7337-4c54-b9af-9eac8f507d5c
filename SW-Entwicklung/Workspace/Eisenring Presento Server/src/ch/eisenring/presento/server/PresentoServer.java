package ch.eisenring.presento.server;

import ch.eisenring.app.server.AbstractServerComponent;
import ch.eisenring.app.shared.Configurable;
import ch.eisenring.core.application.Configuration;
import ch.eisenring.core.application.ConfigurationException;
import ch.eisenring.core.collections.Collection;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.Set;
import ch.eisenring.core.feature.Feature;
import ch.eisenring.core.observable.Observable;
import ch.eisenring.core.platform.Platform;
import ch.eisenring.core.platform.PlatformPath;
import ch.eisenring.jdbc.ConnectionInfo;
import ch.eisenring.presento.PresentoConstants;
import ch.eisenring.presento.server.impl.PresentoCacheSourceImpl;

import java.io.File;

/**
 * Presento Server
 */
public final class PresentoServer extends AbstractServerComponent implements Configurable {
	
	// --------------------------------------------------------------
	// ---
	// --- Server privates
	// ---
	// --------------------------------------------------------------

	// --------------------------------------------------------------
	// ---
	// --- Server globals
	// ---
	// --------------------------------------------------------------
	public final Observable<Boolean> ENABLED =
			Observable.create(false, "Enabled", Boolean.FALSE);

	/**
	 * Max. age of cached data (in milliseconds)
	 */
	public final Observable<Long> MAX_CACHE_AGE =
			Observable.create(false, "MaxCacheAge", 86400000L);

	public final Observable<Collection<String>> GROUP_IDS_MONTAGE =
			Observable.create(false, "GroupIdsMontage", Set.emptyReadonly(String.class));

	public final Observable<Collection<String>> GROUP_IDS_CHAUFFEUR =
			Observable.create(false, "GroupIdsChauffeur", Set.emptyReadonly(String.class));

	// --------------------------------------------------------------
	// ---
	// --- Feature management
	// ---
	// --------------------------------------------------------------
	private final Feature[] features = {
			new PresentoCacheSourceImpl(this)
	};

	@Override
	public Feature[] getFeatures() {
		return features;
	}

	// --------------------------------------------------------------
	// ---
	// --- Launch / Shutdown
	// ---
	// --------------------------------------------------------------
	@Override
	public void launch() throws Exception {
		super.launch();
	}
	
	@Override
	public void shutdown() throws Exception {
		super.shutdown();
	}

	// --------------------------------------------------------------
	// ---
	// --- Configurable implementation
	// ---
	// --------------------------------------------------------------
	final static String CONFIG_BASEKEY = "Presento:";

	@Override
	public void getConfigurationFileNames(final Collection<String> fileNames) {
		fileNames.add("Presento.cfg");
		fileNames.add(new File(Platform.getPlatform().getPath(PlatformPath.SETTINGS), "PresentoServerUser.cfg").getAbsolutePath());
	}

	@Override
	public void configure(final Configuration configuration) throws ConfigurationException {
		final Configuration c = configuration.subConfiguration(CONFIG_BASEKEY);
		// disable while altering configuration
		ENABLED.set(false);
		PresentoConstants.PRESENTO_DATABASE.setConnectionInfo(ConnectionInfo.create(CONFIG_BASEKEY, configuration));
		GROUP_IDS_MONTAGE.set(c.getStrings("GroupIdsMontage",
				List.asList("170", "185", "220"), ','));
		GROUP_IDS_CHAUFFEUR.set(c.getStrings("GroupIdsChauffeur",
				List.asList("165"), ','));
		MAX_CACHE_AGE.set(c.getLong("MaxCacheAge", 1L, 1000000L, 1800L) * 1000);
		ENABLED.set(c.getBoolean("Enabled", false));
	}

}

package ch.eisenring.presento.server.impl;

import java.util.Iterator;

import ch.eisenring.core.collections.Collection;
import ch.eisenring.core.collections.Set;
import ch.eisenring.core.collections.impl.HashSet;
import ch.eisenring.core.datatypes.date.DateGranularityCode;
import ch.eisenring.core.datatypes.date.Period;
import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.presento.server.api.PresentoEmployment;

public final class PresentoEmploymentImpl implements PresentoEmployment {

	/**
	 * Most persons only have one employment period, so an initial size
	 * of 4 should be plenty for 90% of the cases
	 */
	private final Set<Period> employmentPeriods = new HashSet<>(4);

	/**
	 * Adds a period from-upto (both boundaries inclusive)
	 */
	public void addEmploymentPeriod(final long from, final long upto) {
		final long begin, end;
		if (TimestampUtil.isNull(from)) {
			begin = TimestampUtil.NULL_TIMESTAMP;
		} else if (from < TimestampUtil.MIN_TIMESTAMP) {
			begin = TimestampUtil.MIN_TIMESTAMP;
		} else if (from > TimestampUtil.MAX_TIMESTAMP) {
			begin = TimestampUtil.MAX_TIMESTAMP;
		} else {
			begin = from;
		}

		if (TimestampUtil.isNull(upto)) {
			end = TimestampUtil.NULL_TIMESTAMP;
		} else if (upto < TimestampUtil.MIN_TIMESTAMP) {
			end = DateGranularityCode.DAY.round(TimestampUtil.MIN_TIMESTAMP, 1);
		} else if (begin > TimestampUtil.MAX_TIMESTAMP) {
			end = DateGranularityCode.DAY.round(TimestampUtil.MAX_TIMESTAMP, 1);
		} else {
			end = DateGranularityCode.DAY.round(upto, 1);
		}
		employmentPeriods.add(Period.create(begin, end));
	}		
	
	
	@Override
	public boolean isEverEmployed() {
		return !employmentPeriods.isEmpty();
	}

	@Override
	public boolean isEmployed(final long dayTimestamp) {
		for (final Period period : employmentPeriods) {
			if (period.intersects(dayTimestamp))
				return true;
		}
		return false;
	}

	@Override
	public boolean isEmployedWithin(final Period period) {
		for (final Period employmentPeriod : employmentPeriods) {
			if (employmentPeriod.intersects(period))
				return true;
		}
		return false;
	}

	/**
	 * Not public
	 */
	public Collection<Period> getPeriods() {
		return Set.asReadonlySet(employmentPeriods);
	}

	@Override
	public Iterator<Period> iterator() {
		return employmentPeriods.iterator();
	}

}

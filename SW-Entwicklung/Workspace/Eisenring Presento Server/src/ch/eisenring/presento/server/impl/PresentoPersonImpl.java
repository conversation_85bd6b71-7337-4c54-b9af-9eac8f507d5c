package ch.eisenring.presento.server.impl;

import java.util.Collections;

import ch.eisenring.core.HashUtil;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.alterableview.AlterableView;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.datatypes.date.Period;
import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.sort.Comparator;
import ch.eisenring.presento.server.api.PresentoAbsence;
import ch.eisenring.presento.server.api.PresentoEmployment;
import ch.eisenring.presento.server.api.PresentoPerson;
import ch.eisenring.presento.server.api.PresentoRoleCode;

/**
 * Represents a person read from PRESENTO DB
 */
public final class PresentoPersonImpl implements PresentoPerson {

	private long presentoId;
	private long birthTimestamp = TimestampUtil.NULL_TIMESTAMP;
	private int glzSaldo;
	private String personalNr;
	private String lastName;
	private String firstName;
	private String shortName;
	private String mobile;
	private PresentoRoleCode role = PresentoRoleCode.NULL;
	
	/**
	 * Absences of this person
	 */
	private final List<PresentoAbsence> absences = new ArrayList<>();
	
	/**
	 * Employment periods of this person. Each period describes a timespan
	 * where this person is/was/will be employed.
	 */
	private final PresentoEmploymentImpl employment = new PresentoEmploymentImpl();

	public PresentoPersonImpl(final long presentoId) {
		this.presentoId = presentoId;
	}

	public void trimToSize() {
		final Comparator<Period> order = Comparator.reverse(Period.Order.Begin);
		Collections.sort(absences, order);
		absences.trimToSize();
	}

	public long getPresentoId() {
		return presentoId;
	}

	/**
	 * Gets "Personalnummer" as assigned in Presento
	 */
	public String getPersonalNummer() {
		return personalNr;
	}

	void setPersonalNummer(String personalNr) {
		this.personalNr = Strings.trim(personalNr);
	}

	public String getLastName() {
		return lastName;
	}

	void setLastName(String lastName) {
		this.lastName = Strings.trim(lastName);
	}

	public String getFirstName() {
		return firstName;
	}

	void setFirstName(String firstName) {
		this.firstName = Strings.trim(firstName);
	}

	public String getShortName() {
		return shortName;
	}

	void setShortName(String shortName) {
		this.shortName = Strings.trim(shortName);
	}

	public long getBirthDate() {
		return birthTimestamp;
	}

	void setBirthDate(final long birthDate) {
		this.birthTimestamp = birthDate;
	}

	public PresentoRoleCode getRole() {
		return role;
	}

	void setRole(final PresentoRoleCode role) {
		this.role = role;
	}

	public String getMobile() {
		return mobile;
	}

	void setMobile(String mobile) {
		this.mobile = Strings.trim(mobile);
	}

	/**
	 * Gets the full (display) name of the person
	 */
	public String getName() {
		return Strings.concat(getFirstName(), " ", getLastName());
	}

	/**
	 * Adds an absence to this person
	 */
	void addAbsence(PresentoAbsence absence) {
		if (absence != null && !absences.contains(absence))
			absences.add(absence);
	}

	/**
	 * Returns a list of the absences of the person.
	 * The list should be treated as read-only. 
	 */
	public List<PresentoAbsence> getAbsences() {
		return AlterableView.of(absences);
	}

	/**
	 * Adds an employment period to this person
	 */
	void addEmploymentPeriod(final long from, final long upto) {
		employment.addEmploymentPeriod(from, upto);
	}


	/**
	 * Checks if the person was employed in the given period
	 */
	public boolean isEmployedWithin(final Period period) {
		return employment.isEmployedWithin(period);
	}

	/**
	 * Check if the person is employed *and* not absent on the given day.
	 */
	public boolean isWorking(final long day) {
		boolean employed = employment.isEmployed(day);
		if (!employed || absences == null || absences.isEmpty())
			return employed;
		for (int i=absences.size()-1; i>=0; --i) {
			final Period absence = absences.get(i);
			if (absence.intersects(day))
				return false;
		}
		return true;
	}

	@Override
	public PresentoEmployment getEmployment() {
		return employment;
	}

	@Override
	public int getGLZSaldo() {
		return glzSaldo;
	}

	void setGLZSadlo(final int glzSaldo) {
		this.glzSaldo = glzSaldo;
	}

	// --------------------------------------------------------------
	// ---
	// --- Object overrides
	// ---
	// --------------------------------------------------------------
	@Override
	public int hashCode() {
		return HashUtil.hashCode(presentoId);
	}

	@Override
	public boolean equals(final Object o) {
		return o instanceof PresentoPersonImpl && ((PresentoPersonImpl) o).presentoId == presentoId;
	}

	public String toString() {
		final StringMaker b = StringMaker.obtain();
		b.append(getPresentoId());
		b.append('/');
		b.append(getShortName());
		b.append('/');
		b.append(getFirstName());
		b.append(' ');
		b.append(getLastName());
		return b.release();
	}
	
}

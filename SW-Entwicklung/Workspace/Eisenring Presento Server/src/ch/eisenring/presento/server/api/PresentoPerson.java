package ch.eisenring.presento.server.api;

import ch.eisenring.core.collections.List;
import ch.eisenring.core.datatypes.strings.Strings;

public interface PresentoPerson {

	/**
	 * Gets the id of this person assigned by Presento
	 */
	public long getPresentoId();

	/**
	 * Gets the "Personalnummer" assigned to this person in Presento.
	 * May be null.
	 */
	public String getPersonalNummer();

	/**
	 * Gets the birth date (as timestamp) for this person.
	 * May end up as Timestamp.NULL_TIMESTAMP.
	 */
	public long getBirthDate();

	/**
	 * Gets the first name of this person
	 */
	public String getFirstName();

	/**
	 * Gets the last name of this person
	 */
	public String getLastName();

	/**
	 * Gets the short name of this person (aka "<PERSON><PERSON><PERSON><PERSON><PERSON>" in Presento)
	 */
	public String getShortName();
	
	/**
	 * Gets the full name of this person (firstName + lastName)
	 */
	public default String getName() {
		return Strings.concat(getFirstName(), " ", getLastName());
	}

	/**
	 * Gets the mobile phone number assigned in Presento
	 */
	public String getMobile();

	/**
	 * Gets the roles of this person
	 */
	public PresentoRoleCode getRole();

	/**
	 * Gets the employment data of this person
	 */
	public PresentoEmployment getEmployment();

	/**
	 * Determines if person is working on given day.
	 * This method takes into account both employment and absences.
	 */
	public boolean isWorking(final long dayTimestamp);

	/**
	 * Gets the current GL Saldo (Gleitzeit-Saldo)
	 * Time is measured in minutes, negative means under nominal time. 
	 */
	public int getGLZSaldo();

	/**
	 * Returns a list of the absences of the person.
	 * The list should be treated as read-only. 
	 */
	public List<PresentoAbsence> getAbsences();

}

package ch.eisenring.presento.server.impl;

import java.util.concurrent.atomic.AtomicReference;

import ch.eisenring.presento.server.PresentoServer;
import ch.eisenring.presento.server.api.PresentoCache;
import ch.eisenring.presento.server.api.PresentoAccess;

/**
 * Implements PresentoCacheSource
 */
public final class PresentoCacheSourceImpl implements PresentoAccess {

	/**
	 * Holds the latest valid cache instance
	 */
	private final AtomicReference<PresentoCache> currentCacheInstance = new AtomicReference<>();
	
	private final PresentoServer server;

	public PresentoCacheSourceImpl(final PresentoServer server) {
		this.server = server;
	}

	// --------------------------------------------------------------
	// ---
	// --- PresentoCacheSource implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public PresentoCache getPresentoCache() {
		while (true) {
			final long maxAge = System.currentTimeMillis() - server.MAX_CACHE_AGE.get(); 
			final PresentoCache expect = currentCacheInstance.get();
			if (expect != null && expect.getCreationTimestamp() >= maxAge)
				return expect;
			final PresentoCache update = createCache();
			if (update != null && currentCacheInstance.compareAndSet(expect, update))
				return update;
		}
	}

	/**
	 * Creates a new cache instance
	 */
	private PresentoCache createCache() {
		PresentoCache result = null;
		if (server.ENABLED.get()) {
			final PresentoCacheLoader loader = new PresentoCacheLoader(server);
			try {
				result = loader.createCache();
			} catch (final Exception e) {
				result = null; 
			}
		}
		// if cache could not be loaded, create empty dummy instance
		if (result == null)
			result = new PresentoCacheImpl();
		
		// return the new instance
		return result;
	}

}

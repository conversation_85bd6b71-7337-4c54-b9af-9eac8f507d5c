package ch.eisenring.presento.server.api;

import ch.eisenring.core.collections.Set;

public interface PresentoCache extends Iterable<PresentoPerson> {

	/**
	 * Returns the timestamp when this cache was created/updated.
	 */
	public long getCreationTimestamp();

	/**
	 * Looks for a person by presento id
	 */
	public PresentoPerson getPerson(final long presentoId);

	/**
	 * Returns all persons present in the cache. May return an empty collection. 
	 */
	public Set<PresentoPerson> getPersons();

}

package heag.mbl.shared.pdf;

import java.io.InputStream;

import org.faceless.pdf2.PDF;
import org.faceless.pdf2.PDFReader;

import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.io.Streams;
import ch.eisenring.core.io.binary.BinaryHolder;
import ch.eisenring.core.io.binary.BinaryHolderUtil;
import ch.eisenring.core.io.memory.MemoryOutputStream;
import ch.eisenring.core.util.file.FileImage;

/**
 * Utility class to merge PDF pages
 */
public class PDFMerge {

	public static BinaryHolder mergeBinaries(final java.util.Collection<BinaryHolder> pdfBinaries) throws Exception {
		final PDF mergedPDF = new PDF();
		
		// combine all the pages
		for (final BinaryHolder partBinary : pdfBinaries) {
			try (final InputStream binaryIn = partBinary.getInputStream()) {
				final PDFReader reader = new PDFReader(binaryIn);
				final PDF partPDF = new PDF(reader);
				mergedPDF.getPages().addAll(partPDF.getPages());
			}
		}
		
		// create the merged PDF
		MemoryOutputStream out = new MemoryOutputStream();
		try {
			mergedPDF.render(out);
		} finally {
			Streams.closeSilent(out);
		}

		return BinaryHolderUtil.create(out, true);
	}

	public static BinaryHolder mergeFiles(final java.util.Collection<FileImage> pdfFiles) throws Exception {
		final List<BinaryHolder> data = new ArrayList<>();
		for (final FileImage fileImage : pdfFiles) {
			data.add(fileImage.getFiledata());
		}
		return mergeBinaries(data);
	}

}

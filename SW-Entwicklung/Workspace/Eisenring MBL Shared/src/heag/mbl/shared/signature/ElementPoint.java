package heag.mbl.shared.signature;

import java.awt.Graphics2D;
import java.awt.Rectangle;
import java.io.IOException;

import ch.eisenring.core.io.StreamReader;
import ch.eisenring.core.io.StreamWriter;

final class ElementPoint extends Element {

	private final static int VERSION = 1;

	transient int x, y;

	ElementPoint() {
	}
	
	@Override
	void draw(final Graphics2D g) {
		g.fillOval(x - 2, y - 2, 5, 5);			
	}

	@Override
	void updateBounds(final Rectangle r) {
		MBLSignature.updateBounds(r, x, y);
	}

	@Override
	int getWeight() {
		return 1;
	}

	// --------------------------------------------------------------
	// ---
	// --- Cloneable
	// ---
	// --------------------------------------------------------------
	@Override
	protected ElementPoint clone() {
		return (ElementPoint) super.clone();
	}

	// --------------------------------------------------------------
	// ---
	// --- Streamable
	// ---
	// --------------------------------------------------------------
	@Override
	public void read(final StreamReader reader) throws IOException {
		final int version = reader.readInt();
		if (version <= 0 || version > VERSION)
			throw new IOException("unknown version: " + version);
		this.x = reader.readInt();
		this.y = reader.readInt();
	}

	@Override
	public void write(final StreamWriter writer) throws IOException {
		writer.writeInt(VERSION);
		writer.writeInt(x);
		writer.writeInt(y);
	}
	
}

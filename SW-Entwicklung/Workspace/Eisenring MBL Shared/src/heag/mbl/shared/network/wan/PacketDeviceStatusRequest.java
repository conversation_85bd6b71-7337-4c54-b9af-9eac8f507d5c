package heag.mbl.shared.network.wan;

import java.util.UUID;

public final class PacketDeviceStatusRequest extends PacketDeviceStatusBase {

	PacketDeviceStatusRequest() {
		super(CAPS_SEQUENCE_REQUEST, PRIORITY_MAX);
	}

	// --------------------------------------------------------------
	// ---
	// --- Factory methods
	// ---
	// --------------------------------------------------------------
	public static PacketDeviceStatusRequest create(final UUID deviceId) {
		final PacketDeviceStatusRequest packet = new PacketDeviceStatusRequest();
		packet.deviceId = deviceId;
		return packet;
	}

}

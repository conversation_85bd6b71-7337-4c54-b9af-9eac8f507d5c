package heag.mbl.shared.network.wan;

/**
 * Returns the active versions for all documents in project
 */
public final class PacketActiveVersionsRequest extends PacketActiveVersionsBase {

	private PacketActiveVersionsRequest() {
		super(CAPS_SEQUENCE_REQUEST, PRIORITY_REQUEST - 1);
	}

	// --------------------------------------------------------------
	// ---
	// --- Factory methods
	// ---
	// --------------------------------------------------------------
	public static PacketActiveVersionsRequest create(final String baseProjectNumber) {
		final PacketActiveVersionsRequest packet = new PacketActiveVersionsRequest();
		packet.baseProjectNumber = baseProjectNumber;
		return packet;
	}

}

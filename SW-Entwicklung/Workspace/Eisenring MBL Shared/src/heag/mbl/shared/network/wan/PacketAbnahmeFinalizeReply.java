package heag.mbl.shared.network.wan;

import ch.eisenring.core.codetable.ErrorMessage;

public final class PacketAbnahmeFinalizeReply extends PacketAbnahmeFinalizeBase {

	private PacketAbnahmeFinalizeReply() {
		super(CAPS_SEQUENCE_REPLY | CAPS_ERRORMESSAGE, PRIORITY_REPLY);
	}

	// --------------------------------------------------------------
	// ---
	// --- Factory methods
	// ---
	// --------------------------------------------------------------
	public static PacketAbnahmeFinalizeReply create(final PacketAbnahmeFinalizeRequest request, final ErrorMessage error) {
		final PacketAbnahmeFinalizeReply packet = new PacketAbnahmeFinalizeReply();
		packet.setReplyId(request, error);
		packet.auftragKey = request.getAuftragKey();
		packet.projektKey = request.getProjektKey();
		return packet;
	}

	// --------------------------------------------------------------
	// ---
	// --- API
	// ---
	// --------------------------------------------------------------

	// none

	// --------------------------------------------------------------
	// ---
	// --- Streaming
	// ---
	// --------------------------------------------------------------

	// nothing

}

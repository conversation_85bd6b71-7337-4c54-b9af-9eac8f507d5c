package heag.mbl.shared.network.wan;

import java.io.IOException;

import ch.eisenring.core.io.StreamReader;
import ch.eisenring.core.io.StreamWriter;

public abstract class PacketHausUGetBase extends MBLWANPacket{

	protected transient String basisNummer;

	protected PacketHausUGetBase(final int caps, final int priority) {
		super(caps, priority);
	}

	// --------------------------------------------------------------
	// ---
	// --- API
	// ---
	// --------------------------------------------------------------
	public final String getBasisNummer() {
		return basisNummer;
	}

	// --------------------------------------------------------------
	// ---
	// --- Streamable
	// ---
	// --------------------------------------------------------------
	@Override
	protected void readImpl(final StreamReader reader) throws IOException {
		super.readImpl(reader);
		basisNummer = reader.readString();
	}

	@Override
	protected void writeImpl(final StreamWriter writer) throws IOException {
		super.writeImpl(writer);
		writer.writeString(basisNummer);
	}

}

package heag.mbl.shared.network.wan;

import ch.eisenring.core.crypt.password.AuthInitiate;

/**
 * Packet sent by the client to initiate authentication
 * dialog with the server.
 * 
 * The complete authentication control flow is as follows:
 * 
 * 1.) Client sends AuthInitiate
 * 2.) Server responds with AuthChallenge,
 *     if error is indicated in AuthChallenge, procedures ends with failure
 * 3.) Client sends AuthHandshake
 * 4.) Server responds with AuthResult,
 *     if error is indicated, procedure ends with failure,
 *     otherwise authentication success.
 */
public final class PacketAuthInitiate extends PacketAuthBase<AuthInitiate> {

	private PacketAuthInitiate() {
		super(CAPS_SEQUENCE_REQUEST, PRIORITY_MAX);
	}

	// --------------------------------------------------------------
	// ---
	// --- Factory methods
	// ---
	// --------------------------------------------------------------
	public static PacketAuthInitiate create(final AuthInitiate data) {
		final PacketAuthInitiate packet = new PacketAuthInitiate();
		packet.data = data;
		return packet;
	}

}

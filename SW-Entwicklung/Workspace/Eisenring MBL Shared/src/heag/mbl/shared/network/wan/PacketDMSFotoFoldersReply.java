package heag.mbl.shared.network.wan;

import java.io.IOException;

import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.collections.Map;
import ch.eisenring.core.collections.impl.HashMap;
import ch.eisenring.core.io.StreamReader;
import ch.eisenring.core.io.StreamUtil;
import ch.eisenring.core.io.StreamWriter;
import ch.eisenring.dms.service.codetables.DMSFolderType;
import ch.eisenring.dms.service.proxy.DMSFolderHandle;

public final class PacketDMSFotoFoldersReply extends PacketDMSFotoFoldersBase {

	transient Map<DMSFolderType, DMSFolderHandle> folderTypes = new HashMap<>();

	private PacketDMSFotoFoldersReply() {
		super(CAPS_SEQUENCE_REPLY | CAPS_ERRORMESSAGE, PRIORITY_REPLY);
	}

	// --------------------------------------------------------------
	// ---
	// --- Factory methods
	// ---
	// --------------------------------------------------------------
	public static PacketDMSFotoFoldersReply create(final PacketDMSFotoFoldersRequest request,
			final ErrorMessage error) {
		final PacketDMSFotoFoldersReply packet = new PacketDMSFotoFoldersReply();
		packet.setReplyId(request, error);
		packet.projectFolderRowId = request.getProjectFolderRowId();
		return packet;
	}

	// --------------------------------------------------------------
	// ---
	// --- API
	// ---
	// --------------------------------------------------------------
	public Map<DMSFolderType, DMSFolderHandle> getAllowedFolderTypes() {
		return new HashMap<>(this.folderTypes);
	}
	
	public void addAllowedFolderType(final DMSFolderType type, final DMSFolderHandle handle) {
		if (AbstractCode.isNull(type) || handle == null)
			return;
		this.folderTypes.put(type, handle);
	}

	// --------------------------------------------------------------
	// ---
	// --- Streamable
	// ---
	// --------------------------------------------------------------
	@Override
	protected void readImpl(final StreamReader reader) throws IOException {
		super.readImpl(reader);
		StreamUtil.readMap(reader, folderTypes);
	}

	@Override
	protected void writeImpl(final StreamWriter writer) throws IOException {
		super.writeImpl(writer);
		StreamUtil.writeMap(writer, folderTypes);
	}

}

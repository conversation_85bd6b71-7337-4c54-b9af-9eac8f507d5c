package heag.mbl.shared.network.wan;

import heag.dsp.pub.model.DSPMontageBewertung;

import java.io.IOException;

import ch.eisenring.core.io.StreamReader;
import ch.eisenring.core.io.StreamWriter;

abstract class PacketPutMontageBewertungBase extends MBLWANPacket {

	protected transient DSPMontageBewertung bewertung;

	protected PacketPutMontageBewertungBase(final int caps, final int priority) {
		super(caps, priority);
	}
	
	// --------------------------------------------------------------
	// ---
	// --- API
	// ---
	// --------------------------------------------------------------
	public final DSPMontageBewertung getBewertung() {
		return bewertung;
	}
	
	// --------------------------------------------------------------
	// ---
	// --- Streamable implementation
	// ---
	// --------------------------------------------------------------
	@Override
	protected void readImpl(final StreamReader reader) throws IOException {
		super.readImpl(reader);
		bewertung = (DSPMontageBewertung) reader.readObject();
	}

	@Override
	protected void writeImpl(final StreamWriter writer) throws IOException {
		super.writeImpl(writer);
		writer.writeObject(bewertung);
	}

}

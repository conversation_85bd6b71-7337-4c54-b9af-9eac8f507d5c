package heag.mbl.shared.network.wan;

import java.io.IOException;

import ch.eisenring.core.collections.Set;
import ch.eisenring.core.collections.impl.HashSet;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.io.StreamReader;
import ch.eisenring.core.io.StreamUtil;
import ch.eisenring.core.io.StreamWriter;
import ch.eisenring.core.iterators.Filter;
import ch.eisenring.logiware.code.pseudo.AbwicklungsartCode;
import ch.eisenring.lw.api.LWAuftragData;
import heag.mbl.shared.constants.MassaufnahmeConstants;

public abstract class PacketSearchAuftragInfoBase extends MBLWANPacket {

	protected transient String criteriaProjektNummer;
	protected transient Set<AbwicklungsartCode> criteriaAWA = new HashSet<>();
	protected transient String location;

	protected PacketSearchAuftragInfoBase(final int caps, final int priority) {
		super(caps, priority);
	}

	// --------------------------------------------------------------
	// ---
	// --- API
	// ---
	// --------------------------------------------------------------
	public final Set<AbwicklungsartCode> getAbwicklungsarten() {
		return new HashSet<>(criteriaAWA);
	}

	public final void setAbwicklungsarten(final java.util.Collection<AbwicklungsartCode> abwicklungsArten) {
		criteriaAWA.clear();
		criteriaAWA.addAll(abwicklungsArten);
	}

	public final String getProjektnummer() {
		return criteriaProjektNummer;
	}

	public final void setProjektnummer(final String projektnummerCriterion) {
		this.criteriaProjektNummer = projektnummerCriterion;
	}

	public final String getLocation() { return location;}

	public final void setLocation(final String location) {
		this.location = location;
	}

	private static class FilterImpl implements Filter<LWAuftragData> {
		final String projektNummer;
		final Set<AbwicklungsartCode> awa;


		FilterImpl(final String projektNummer, final Set<AbwicklungsartCode> awa) {
			this.projektNummer = projektNummer;
			this.awa = awa;
		}

		@Override
		public boolean accepts(final LWAuftragData auftrag) {
			return auftrag != null
					&& awa.contains(auftrag.getAbwicklungsart())
					&& (projektNummer == null || Strings.equals(projektNummer, auftrag.getProjektnummer()));
		}
	}

	private static class MassaufnahmeFilterImpl implements Filter<LWAuftragData> {
		final String projektNummer;
		final Set<AbwicklungsartCode> awa;

		MassaufnahmeFilterImpl(final String projektNummer, final Set<AbwicklungsartCode> awa) {
			this.projektNummer = projektNummer;
			this.awa = awa;
		}

		@Override
		public boolean accepts(final LWAuftragData auftrag) {
			return auftrag != null
					&& awa.contains(auftrag.getAbwicklungsart())
					&& (projektNummer == null || Strings.contains(auftrag.getProjektnummer(),projektNummer));
		}
	}



	/**
	 * Gets filter that accepts data matching this packets search criteria
	 */
	public final Filter<LWAuftragData> getFilter() {
		if(MassaufnahmeConstants.ACTIVITY_LABEL.equals(getLocation())) {
			return new MassaufnahmeFilterImpl(Strings.clean(getProjektnummer()), getAbwicklungsarten());
		}
		return new FilterImpl(Strings.clean(getProjektnummer()), getAbwicklungsarten());
	}

	// --------------------------------------------------------------
	// ---
	// --- Streamable implementation
	// ---
	// --------------------------------------------------------------
	@Override
	protected void readImpl(final StreamReader reader) throws IOException {
		super.readImpl(reader);
		criteriaProjektNummer = reader.readString();
		StreamUtil.readCollection(reader, criteriaAWA);
		location = reader.readString();
	}

	@Override
	protected void writeImpl(final StreamWriter writer) throws IOException {
		super.writeImpl(writer);
		writer.writeString(criteriaProjektNummer);
		StreamUtil.writeCollection(writer, criteriaAWA);
		writer.writeString(location);
	}

}
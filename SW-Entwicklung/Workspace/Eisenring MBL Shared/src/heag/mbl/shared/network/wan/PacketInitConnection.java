package heag.mbl.shared.network.wan;

import heag.mbl.shared.MBLConstants;
import heag.mbl.shared.network.AbstractMBLPacket;

import java.io.File;
import java.io.FileFilter;
import java.io.IOException;

import ch.eisenring.app.shared.network.PacketApplicationFingerPrint;

/**
 * Packet sent by the client to initialize the connection with the server.
 */
public final class PacketInitConnection extends PacketApplicationFingerPrint {

	private PacketInitConnection() {
		super(MBLConstants.APPNAME, MBLConstants.VERSION,
				AbstractMBLPacket.MBL_WAN_DOMAIN);
	}

	public static PacketInitConnection create(final File appPath, final FileFilter filter) throws IOException {
		final PacketInitConnection packet = new PacketInitConnection();
		packet.initFingerPrints(appPath, filter);
		return packet;
	}

}

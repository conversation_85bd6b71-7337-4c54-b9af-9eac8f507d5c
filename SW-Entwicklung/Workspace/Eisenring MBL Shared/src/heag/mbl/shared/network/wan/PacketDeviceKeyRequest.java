package heag.mbl.shared.network.wan;

import java.util.UUID;

/**
 * Requests the device key from the server.
 */
public final class PacketDeviceKeyRequest extends PacketDeviceKeyBase {

	private PacketDeviceKeyRequest() {
		super(CAPS_SEQUENCE_REQUEST, PRIORITY_MAX);
	}

	// --------------------------------------------------------------
	// ---
	// --- Factory methods
	// ---
	// --------------------------------------------------------------
	public static PacketDeviceKeyRequest create(final UUID deviceId) {
		final PacketDeviceKeyRequest packet = new PacketDeviceKeyRequest();
		packet.deviceId = deviceId;
		return packet;
	}

}

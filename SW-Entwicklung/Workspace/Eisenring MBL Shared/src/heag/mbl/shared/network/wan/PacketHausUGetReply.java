package heag.mbl.shared.network.wan;

import java.io.IOException;

import heag.huo.shared.pojo.HUOObjekt;
import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.io.StreamReader;
import ch.eisenring.core.io.StreamWriter;

public final class PacketHausUGetReply extends PacketHausUGetBase {

	private transient HUOObjekt objekt;

	private PacketHausUGetReply() {
		super(CAPS_SEQUENCE_REPLY | CAPS_ERRORMESSAGE, PRIORITY_REPLY);
	}

	// --------------------------------------------------------------
	// ---
	// --- Factory methods
	// ---
	// --------------------------------------------------------------
	public static PacketHausUGetReply create(final PacketHausUGetRequest request, final ErrorMessage error) {
		final PacketHausUGetReply packet = new PacketHausUGetReply();
		packet.setReplyId(request, error);
		packet.basisNummer = request.getBasisNummer();
		return packet;
	}

	public static PacketHausUGetReply create(final PacketHausUGetRequest request, final HUOObjekt objekt) {
		final PacketHausUGetReply packet = create(request, ErrorMessage.OK);
		packet.objekt = objekt;
		return packet;
	}

	// --------------------------------------------------------------
	// ---
	// --- API
	// ---
	// --------------------------------------------------------------
	public HUOObjekt getObjekt() {
		return objekt;
	}

	// --------------------------------------------------------------
	// ---
	// --- Streamable
	// ---
	// --------------------------------------------------------------
	@Override
	protected void readImpl(StreamReader reader) throws IOException {
		super.readImpl(reader);
		objekt = (HUOObjekt) reader.readObject();
	}

	@Override
	protected void writeImpl(final StreamWriter writer) throws IOException {
		super.writeImpl(writer);
		writer.writeObject(objekt);
	}

}

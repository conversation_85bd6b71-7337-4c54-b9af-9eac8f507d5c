package heag.mbl.shared.network.wan;

import ch.eisenring.core.crypt.password.AuthChallenge;

/**
 * Packet sent by the server in response to AuthInitiate.
 */
public final class PacketAuthChallenge extends PacketAuthBase<AuthChallenge> {

	private PacketAuthChallenge() {
		super(CAPS_SEQUENCE_REPLY | CAPS_SEQUENCE_REQUEST, PRIORITY_MAX);
	}

	// --------------------------------------------------------------
	// ---
	// --- Factory methods
	// ---
	// --------------------------------------------------------------
	public static PacketAuthChallenge create(final PacketAuthInitiate request, final AuthChallenge data) {
		final PacketAuthChallenge packet = new PacketAuthChallenge();
		packet.setReplyId(request);
		packet.data = data;
		return packet;
	}

}

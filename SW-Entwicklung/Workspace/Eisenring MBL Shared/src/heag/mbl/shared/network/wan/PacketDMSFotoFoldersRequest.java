package heag.mbl.shared.network.wan;

public final class PacketDMSFotoFoldersRequest extends PacketDMSFotoFoldersBase {

	private PacketDMSFotoFoldersRequest() {
		super(CAPS_SEQUENCE_REQUEST, PRIORITY_REQUEST);
	}

	// --------------------------------------------------------------
	// ---
	// --- Factory methods
	// ---
	// --------------------------------------------------------------
	public static PacketDMSFotoFoldersRequest create(final long projectFolderRowId) {
		final PacketDMSFotoFoldersRequest packet = new PacketDMSFotoFoldersRequest();
		packet.projectFolderRowId = projectFolderRowId;
		return packet;
	}

}

package heag.mbl.shared.codetables;

import ch.eisenring.core.codetype.StaticCode;

public final class MBLDampfabzugType extends StaticCode {

	public final static MBLDampfabzugType NULL = new MBLDampfabzugType(
			0, null, "", "");

	public final static MBLDampfabzugType UMLUFT_RUECKSTAUKLAPPE_OHNE = new MBLDampfabzugType(
			1, 1, "Uml<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>, keine <PERSON>");
	
	public final static MBLDampfabzugType ABLUFT_RUECKSTAUKLAPPE_OHNE = new MBLDampfabzugType(
			2, 2, "AblORüStKl", "Abluft, ohne Rückstauklappe");

	public final static MBLDampfabzugType ABLUFT_RUECKSTAUKLAPPE_MIT = new MBLDampfabzugType(
			3, 3, "MitRüStKl", "Ablu<PERSON>, mit Rückstauklappe");

	public final static MBLDampfabzugType ABLUFT_RUECKSTAUKLAPPE_ABKLAEREN = new MBLDampfabzugType(
			4, 4, "<PERSON>bk<PERSON><PERSON><PERSON>", "Abluft, Rückstauklappe abkl<PERSON><PERSON> durch ID");

	private MBLDampfabzugType(final int id, final Object key,
			final String shortText, final String longText) {
		super(id, key, shortText, longText);
	}

}

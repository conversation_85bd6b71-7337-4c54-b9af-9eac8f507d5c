package heag.mbl.shared.codetables;

import ch.eisenring.user.shared.codetables.RightCode;

public final class MBLRightCode extends RightCode {

	public final static MBLRightCode MONTAGE_BEWERTEN = new MBLRightCode(3001, "Montage bewerten");
	public final static MBLRightCode DMS_ABFRAGE = new MBLRightCode(3002, "DMS Abfrage");
	public final static MBLRightCode MASSAUFNAHME = new MBLRightCode(3003, "Massaufnahme");
	public final static MBLRightCode ABNAHME = new MBLRightCode(3004, "Abnahme");
	public final static MBLRightCode FOTORAPPORT = new MBLRightCode(3005, "Fotorapport");
	public final static MBLRightCode HAUSUEBERSICHT = new MBLRightCode(3006, "Hausübersicht");
	public final static MBLRightCode LOCALSTORAGE = new MBLRightCode(3007, "Offline Management");
	public final static MBLRightCode RESERVED3008 = new MBLRightCode(3008, "Reserved 3008");
	public final static MBLRightCode RESERVED3009 = new MBLRightCode(3009, "Reserved 3009");
	public final static MBLRightCode RESERVED3010 = new MBLRightCode(3010, "Reserved 3010");

	public final static MBLRightCode TEST = new MBLRightCode(10000, "Test");

	private MBLRightCode(final int id, final String text) {
		super(id, text);
	}

}

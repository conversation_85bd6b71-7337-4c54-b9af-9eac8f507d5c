package heag.mbl.client.gui.pdf.cache;

import java.awt.Image;

import org.faceless.pdf2.PagePainter;

final class AsyncRenderer implements Runnable {

	public final PDFCache cache;
	public final ImageKey key;

	AsyncRenderer(final PDFCache cache, final ImageKey key) {
		this.cache = cache;
		this.key = key;
	}

	@Override
	public void run() {
		try {
			final PagePainter painter = cache.getPagePainter(key.pageNumber);
			final Image image = painter.getImage(key.dpi.getDPI());
			cache.putPageImage(key, image);
		} catch (final Exception e) {
			// ignore
		} finally {
			cache.removeRequest(this);
		}
	}

	// --------------------------------------------------------------
	// ---
	// --- Object overrides
	// ---
	// --------------------------------------------------------------
	@Override
	public int hashCode() {
		return key.hashCode();
	}

	@Override
	public boolean equals(final Object o) {
		if (!(o instanceof AsyncRenderer))
			return false;
		final AsyncRenderer a = (AsyncRenderer) o;
		return a.cache.equals(cache) && a.key.equals(key);
	}

}

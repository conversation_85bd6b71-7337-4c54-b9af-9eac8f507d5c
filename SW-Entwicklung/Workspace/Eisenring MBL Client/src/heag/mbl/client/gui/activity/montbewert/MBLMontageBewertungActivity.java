package heag.mbl.client.gui.activity.montbewert;

import heag.dsp.pub.model.DSPMontageBewertung;
import heag.mbl.client.MBLClient;
import heag.mbl.client.gui.activity.ActivityDataObject;
import heag.mbl.client.gui.activity.MBLActivity;
import heag.mbl.client.gui.activity.MBLActivityBase;
import heag.mbl.client.gui.activity.MBLActivityFactory;
import heag.mbl.client.gui.activity.MBLActivityPK;
import heag.mbl.client.gui.activity.MBLActivityPanel;
import heag.mbl.client.gui.activity.MBLActivityProperties;
import heag.mbl.client.gui.activity.MBLActivityTerminationCode;
import heag.mbl.client.gui.activity.MBLActivityType;
import heag.mbl.client.gui.activity.auftragselect.MBLAuftragSelectActivity;
import heag.mbl.client.gui.activity.photo.MBLPhotoActivity;
import heag.mbl.client.gui.activity.photo.PhotoCollection;
import heag.mbl.client.gui.window.MBLMainPanel;
import heag.mbl.shared.MBLConstants;
import heag.mbl.shared.codetables.MBLRightCode;

import javax.swing.JComponent;

import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.resource.image.ImageReducer;
import ch.eisenring.core.util.file.FileImage;

public class MBLMontageBewertungActivity extends MBLActivityBase {

	public final static MBLActivityFactory FACTORY = new MBLActivityFactory(
			Constants.ACTIVITY_LABEL, Constants.ACTIVITY_ICON, MBLActivityType.DATAVIEW, MBLRightCode.MONTAGE_BEWERTEN) {
		@Override
		protected MBLActivity createImpl(final MBLClient client) {
			final MBLAuftragSelectActivity activity = new MBLAuftragSelectActivity(client);
			activity.setProperty(MBLActivityProperties.SYMBOL_ICON, Constants.ACTIVITY_ICON);
			activity.setProperty(MBLActivityProperties.FORWARDING_FACTORY, FORWARD_FACTORY);
			activity.setProperty(MBLActivityProperties.LOCATION, Constants.ACTIVITY_LABEL + "\nAuftrag wählen");
			activity.setAllowedAWA(Constants.AWA_ALLOWED);
			return activity;
		}
	};

	private final static MBLActivityFactory FORWARD_FACTORY =  new MBLActivityFactory(
			Constants.ACTIVITY_LABEL, Constants.ACTIVITY_ICON, MBLActivityType.WORKFLOW) {
		protected MBLActivity createImpl(final MBLClient client) {
			return new MBLMontageBewertungActivity(client);
		}
	};
	
	public MBLMontageBewertungActivity(final MBLClient client) {
		super(client);
		setPropertyDefault(MBLActivityProperties.ACTIVITY_TYPE, MBLActivityType.WORKFLOW);
		setPropertyDefault(MBLActivityProperties.SYMBOL_ICON, Constants.ACTIVITY_ICON);
		setPropertyDefault(MBLActivityProperties.SUSPENDABLE, Boolean.TRUE);
		setPropertyDefault(MBLActivityProperties.SAVEABLE, Boolean.TRUE);
		setPropertyDefault(MBLActivityProperties.ANIMATION_SLIDEOVER, MBLMainPanel.ANIMATE_IMMEDIATE);
	}

	// --------------------------------------------------------------
	// ---
	// --- Data object handling
	// ---
	// --------------------------------------------------------------
	@Override
	protected ActivityDataObject createDataObject() {
		return new DataObject();
	}

	@Override
	public DataObject getDataObject() {
		return (DataObject) super.getDataObject();
	}

	@Override
	public MBLActivityPK getActivityPK() {
		return MBLActivityPK.create(this);
	}

	// --------------------------------------------------------------
	// ---
	// --- Start/Stop processing
	// ---
	// --------------------------------------------------------------
	@Override
	protected boolean isModifiedImpl() {
		if (super.isModifiedImpl())
			return true;
		if (contentPanel == null)
			return false;
		return contentPanel.isModified();
	}

	@Override
	public void saveActivity() {
		if (contentPanel == null)
			return;
		if (!contentPanel.validateView(true))
			return;
		setBusy("Speichern wird vorbereitet...", false);
		final DataObject dataObject = getDataObject();
		dataObject.setBewertung(contentPanel.getMontageBewertung());
		startAsync(() -> {
			try {
				final StoreAction storeAction = getStoreAction();
				getClient().getStoreItemManager().addItem(storeAction);
				getActivityStack().stopActivity(MBLMontageBewertungActivity.this, MBLActivityTerminationCode.SAVED);
			} catch (final Exception e) {
				setError(new ErrorMessage(e), UIStates.NORMAL);
			}
		});
	}
	
	StoreAction getStoreAction() {
		final DataObject dataObject = getProperty(MBLActivityProperties.DATA_OBJECT);
		final DSPMontageBewertung bewertung = dataObject.getBewertung();
		final PhotoCollection photos = dataObject.getPhotos();
		final ImageReducer reducer = new ImageReducer(MBLConstants.IMAGE_SCALE_RULE);
		final String name = Strings.concat("Montagebewertung_", bewertung.getUser());
		final List<FileImage> fileImages = photos.reduceAndRename(reducer, name);
		return new StoreAction(bewertung, fileImages);
	}

	// --------------------------------------------------------------
	// ---
	// --- Child activity handling
	// ---
	// --------------------------------------------------------------
	public void childActivityStopped(final MBLActivity childActivity) {
		if (!dispatchChildActivityStopped(childActivity)) {
			setFatal(new ErrorMessage(Strings.concat("Fehler: ChildActivity ", childActivity.getClass(), " not handled")));
		}
	}

	void childActivityStopped(final MBLPhotoActivity photoActivity) {
		// nothing here
	}

	// --------------------------------------------------------------
	// ---
	// --- State handling
	// ---
	// --------------------------------------------------------------

	// none

	// --------------------------------------------------------------
	// ---
	// --- User interface
	// ---
	// --------------------------------------------------------------
	protected ActivityUI contentPanel;

	@Override
	protected JComponent createLocationLabel() {
		final Object location = getProperty(MBLActivityProperties.LOCATION);
		return createLocationLabel(location, Constants.ACTIVITY_LABEL);
	}

	@Override
	protected MBLActivityPanel createUserInterface() {
		contentPanel = new ActivityUI(this);
		final MBLActivityPanel activityPanel = new MBLActivityPanel(this);
		activityPanel.setContentPanel(contentPanel);
		return activityPanel;
	}

}

package heag.mbl.client.gui.activity.mangelaufnahme;

import heag.mbl.client.gui.activity.ActivityDataObject;
import heag.mbl.shared.network.obj.MBLMangelPosition;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.util.DeepCloneable;
import ch.eisenring.core.util.file.FileImage;
import ch.eisenring.lw.api.LWAuftragData;
import ch.eisenring.lw.api.LWAuftragPositionData;

public final class MangelDataObject extends ActivityDataObject {

	transient LWAuftragData auftrag;
	transient FileImage ausfPlaenePDF;
	transient List<MBLMangelPosition> maengel = new ArrayList<>();
	transient List<LWAuftragPositionData> auftragPositionen = new ArrayList<>();
	transient LWAuftragPositionData selectedAuftragPosition;

	// --------------------------------------------------------------
	// ---
	// --- Auftrag Info
	// ---
	// --------------------------------------------------------------
	public void setAuftrag(final LWAuftragData auftrag) {
		this.auftrag = auftrag;
	}

	public LWAuftragData getAuftrag() {
		return auftrag;
	}

	// --------------------------------------------------------------
	// ---
	// --- Mangel management
	// ---
	// --------------------------------------------------------------
	/**
	 * Creates and adds new Mangel to this object.
	 */
	public MBLMangelPosition createMangel() {
		final int ordinalPosition = maengel.size() + 1;
		final MBLMangelPosition mangel = MBLMangelPosition.create(ordinalPosition);
		mangel.setOrdinalPosition(ordinalPosition);
		final LWAuftragData auftrag = getAuftrag();
		if (auftrag != null) {
			mangel.setAuftragKey(auftrag.getAuftragKey());
			mangel.setProjektKey(auftrag.getProjektKey());
		}
		maengel.add(mangel);
		return mangel;
	}
	
	public List<MBLMangelPosition> getMaengel() {
		return new ArrayList<>(maengel);
	}

	public FileImage getAusfuehrungsPlaenePDF() {
		return ausfPlaenePDF;
	}

	public void setAusfuehrungsPlaenePDF(final FileImage fileImage) {
		this.ausfPlaenePDF = FileImage.create(fileImage);
	}

	public List<LWAuftragPositionData> getAuftragPositionen() {
		return new ArrayList<>(auftragPositionen);
	}

	public void setAuftragPositionen(final List<LWAuftragPositionData> auftragPositionen) {
		this.auftragPositionen.clear();
		this.auftragPositionen.addAll(auftragPositionen);
	}

	public void setSelectedAuftragPosition(final LWAuftragPositionData position) {
		this.selectedAuftragPosition = position;
	}

	public LWAuftragPositionData getSelectedAuftragPosition() {
		return selectedAuftragPosition;
	}

	// --------------------------------------------------------------
	// ---
	// --- Cloneable
	// ---
	// --------------------------------------------------------------
	@Override
	public MangelDataObject clone() {
		final MangelDataObject copy;
		try {
			copy = (MangelDataObject) super.clone();
			copy.maengel = DeepCloneable.deepClone(copy.maengel);
		} catch (final CloneNotSupportedException e) {
			// should never happen
			throw new RuntimeException(e.getMessage(), e);
		}
		return copy;
	}
	
}

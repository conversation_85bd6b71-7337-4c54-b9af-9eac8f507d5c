package heag.mbl.client.gui.activity.localstorage;

import heag.mbl.client.gui.activity.LayoutHandlerBase;
import heag.mbl.client.gui.activity.MBLUIState;


abstract class LHBase extends LayoutHandlerBase {

	protected LHBase(final ActivityUI activityUI, final MBLUIState uiState) {
		super(activityUI, uiState);
	}

	public final DataObject getDataObject() {
		return (DataObject) getActivity().getDataObject();
	}

}

package heag.mbl.client.gui.activity;

import ch.eisenring.app.shared.network.RPCContext;
import ch.eisenring.app.shared.network.RPCHandler;
import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.network.packet.AbstractPacket;

/**
 * Skeleton for asynchronous loading data into an activity
 * through a network request
 */
public abstract class MBLDataLoaderRPC implements RPCHandler {

	protected final MBLActivity activity;
	protected AbstractPacket request;

	protected MBLDataLoaderRPC(final MBLActivity activity) {
		this(activity, null);
	}

	protected MBLDataLoaderRPC(final MBLActivity activity, final AbstractPacket request) {
		this.activity = activity;
		this.request = request;
	}

	/**
	 * Starts processing for this loader. This consists of 
	 */
	public void start() {
		final Runnable task = new Runnable() {
			@Override
			public void run() {
				final AbstractPacket request = getRequest();
				activity.sendPacket(MBLDataLoaderRPC.this, request);
			}
		};
		// here we go
		activity.getClient().getCore().getThreadPool().start(task);
	}

	/**
	 * Default implementation generates a standard error message
	 * and sets the UI state to ERROR.
	 */
	@Override
	public void timeoutOccured(final RPCContext rpcContext) {
		final ErrorMessage message = new ErrorMessage("Netzwerk: Zeitüberschreitung");
		activity.setFatal(message);
	}

	/**
	 * Does absolutely nothing by default
	 */
	@Override
	public void requestSent(final RPCContext rpcContext) {
	}

	/**
	 * Default implementation checks if the reply is valid, if it isn't,
	 * sets the activities ERROR_MESSAGE to the reply's message and UI STATE to ERROR.
	 * 
	 * If valid, calls validReplyReceived().
	 */
	@Override
	public void replyReceived(final RPCContext rpcContext) {
		final AbstractPacket reply = rpcContext.getReply();
		if (reply.isValid()) {
			validReplyReceived(rpcContext);
		} else {
			activity.setFatal(reply.getMessage());
		}
	}

	/**
	 * Called to create the network request. The default implementation just
	 * returns what was supplied as the "request" argument to the constructor,
	 * or null if a constructor without the argument was used.
	 */
	protected AbstractPacket getRequest() {
		return request;
	}
	
	/**
	 * Called by default implementation when a valid reply was received.
	 */
	protected abstract void validReplyReceived(final RPCContext rpcContext);

	
}


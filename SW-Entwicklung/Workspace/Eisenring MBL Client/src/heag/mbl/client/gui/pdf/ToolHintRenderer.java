package heag.mbl.client.gui.pdf;

import java.awt.Graphics2D;

/**
 * Enables tools to add elements into the rendering chain
 */
public interface ToolHintRenderer {

	/**
	 * Called before elements on page are painted
	 */
	public default void paintBG(final Graphics2D g, final PageEditContent content, final float zoomFactorHint) {
		// nothing by default
	}

	/**
	 * Called after elements on page have been painted
	 */
	public default void paintFG(final Graphics2D g, final PageEditContent content, final float zoomFactorHint) {
		// nothing by default
	}

}

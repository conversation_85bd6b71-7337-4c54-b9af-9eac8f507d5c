package heag.mbl.client.gui.activity;

import ch.eisenring.core.datatypes.primitives.Primitives;

/**
 * Abstract representation of a primary key.
 * 
 * The key identifies a data item (or hierachy of items) that the activity
 * shows or edits.
 */
public interface MBLActivityPK {

	/**
	 * Compares two keys for equality
	 */
	public boolean equals(final Object o);

	/**
	 * Creates a PK based on object reference. Two keys of this type are
	 * equal if their object references agree with their equals methods.
	 */
	public static MBLActivityPK create(final Object object) {
		return new ObjectActivityPK(object);
	}

	/**
	 * Creates a PK based on activity reference.
	 * Keys of this type are considered equal if their activities
	 * have equal location properties.
	 */
	public static MBLActivityPK create(final MBLActivity activity) {
		return new GenericActivityPK(activity);
	}

}

/**
 * Generic implementation of key,
 * acts on equality of activity class and location property
 */
final class GenericActivityPK implements MBLActivityPK {
	
	private final MBLActivity activity;
	
	public GenericActivityPK(final MBLActivity activity) {
		this.activity = activity;
	}

	// --------------------------------------------------------------
	// ---
	// --- Object overrides
	// ---
	// --------------------------------------------------------------
	@Override
	public int hashCode() {
		return activity.getClass().hashCode();
	}

	@Override
	public boolean equals(final Object o) {
		if (!(o instanceof GenericActivityPK))
			return false;
		final GenericActivityPK key = (GenericActivityPK) o;
		if (!key.activity.getClass().equals(activity.getClass()))
			return false;
		final Object l1 = activity.getProperty(MBLActivityProperties.LOCATION);
		final Object l2 = key.activity.getProperty(MBLActivityProperties.LOCATION);
		return Primitives.equals(l1, l2);
	}

}

/**
 * Generic implementation of key, based on arbitrary object. 
 */
final class ObjectActivityPK implements MBLActivityPK {
	
	private final Object object;
	
	public ObjectActivityPK(final Object object) {
		this.object = object;
	}

	// --------------------------------------------------------------
	// ---
	// --- Object overrides
	// ---
	// --------------------------------------------------------------
	@Override
	public int hashCode() {
		return Primitives.hashCode(object);
	}

	@Override
	public boolean equals(final Object o) {
		if (!(o instanceof ObjectActivityPK))
			return false;
		final ObjectActivityPK key = (ObjectActivityPK) o;
		return object != null && key.object != null && key.object.equals(object);
	}

}

package heag.mbl.client.gui.activity.mangelaufnahme;

import heag.mbl.client.resources.images.Images;
import ch.eisenring.core.resource.ImageResource;

interface Constants {

	final static String ACTIVITY_LABEL = "Mangelaufnahme";

	final static ImageResource ACTIVITY_ICON = Images.EDIT;

	String[] MANGEL_CHOICES = {
			"<PERSON>", "Korpus", "Tablar", "<PERSON><PERSON>rtr<PERSON><PERSON>", "Sc<PERSON><PERSON><PERSON>", "Abdeckkappen", "Rückwan<PERSON>", "Beschläge"
	};

	String[] BESCHREIBUNG_CHOICES = {
		"Defekt", "<PERSON>gel<PERSON>", "<PERSON><PERSON> richten", "Griffbohrung"
	};

	String[] VORGEHEN_CHOICES = {
			"<PERSON>rsa<PERSON>", "Ausbessern"
		};

	String[] URSACHE_CHOICES = {
			"Nein", 
			"Falschlieferung",
			"Falsche Bohrung",
			"<PERSON><PERSON>ende Teile",
			"<PERSON>hlend<PERSON> Anschlussteile",
			"<PERSON><PERSON>hler",
			"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Fronten)",
			"Transportschaden"
		};

}

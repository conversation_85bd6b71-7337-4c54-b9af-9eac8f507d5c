package heag.mbl.client.gui;

public class GUIConstants implements ch.eisenring.gui.GUIConstants {

	/** 
	 * Flag indicating if the application operates in FX mode
	 */
	public static volatile boolean FX_MODE = true;

	/**
	 * Flag indicating if pen scrolling should invert horizontal axis
	 */
	public static volatile boolean SCROLLBAR_HORIZONTAL_INVERT;

	/**
	 * Flag indicating if pen scrolling should invert vertical axis
	 */
	public static volatile boolean SCROLLBAR_VERTICAL_INVERT;
	
}

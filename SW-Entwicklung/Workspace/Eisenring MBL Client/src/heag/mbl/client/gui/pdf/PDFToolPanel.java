package heag.mbl.client.gui.pdf;

import heag.mbl.client.MBLClient;
import heag.mbl.client.gui.activity.MBLAbstractActivityUI;
import heag.mbl.client.gui.activity.MBLActivityUIPanel;
import heag.mbl.client.gui.border.MBLScrollPaneBorder;
import heag.mbl.client.gui.componentapi.MBLComponent;
import heag.mbl.client.gui.components.choice.MBLChoiceField;
import heag.mbl.client.gui.components.text.MBLTextField;
import heag.mbl.client.gui.layout.MBLButtonSize;
import heag.mbl.client.gui.layout.MBLFontSize;
import heag.mbl.client.gui.zoomscroll.ZoomScrollEditListener;

import java.awt.Color;
import java.awt.Dimension;
import java.awt.GridBagLayout;
import java.awt.event.FocusAdapter;
import java.awt.event.FocusEvent;
import java.awt.event.FocusListener;

import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.Map;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.collections.impl.HashMap;
import ch.eisenring.core.datatypes.primitives.Primitives;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.gui.GridBagConstraints;

@SuppressWarnings("serial")
public class PDFToolPanel<T extends MBLAbstractActivityUI<?>> extends MBLActivityUIPanel<T>
	implements ZoomScrollEditListener {

	public final static Color BLACK = Color.BLACK;
	public final static Color RED = Color.RED;
	public final static Color GREEN = new Color(0xFF00AA00);
	public final static Color BLUE = Color.BLUE;
	public final static Color MAGENTA = new Color(0xFFEE00EE);
	//https://www.color-hex.com/color-palette/4699
	public final static Color ORANGE = new Color(0xFFEE7400);
	
	/**
	 * Colors the user can select
	 */
	private final static Color[] COLOR_CHOICES = { ORANGE, BLUE };

	private PDFEditTool textTool = new PDFEditToolText();
	private MBLChoiceField<PDFEditTool> toolChoice = new MBLChoiceField<>(PDFEditTool.class);

	private final PDFUndoButton btnUndo;
	private final PDFRedoButton btnRedo;

	/**
	 * Available tools
	 */
	private final List<PDFEditTool> tools = new ArrayList<>();
	
	private PDFEditPanel<T> editPanel;
	private MBLTextField textField = new MBLTextField(60);
	
	private int layoutX;

	private final FocusListener textFocusListener = new FocusAdapter() {
		@Override
		public void focusGained(final FocusEvent event) {
			if (event.isTemporary())
				return;
			setSelectedTool(textTool);
		}
	};

	private final MBLChoiceField.SelectionListener toolChoiceListener = new MBLChoiceField.SelectionListener() {
        @Override
        public void valueChanged(final MBLChoiceField<?> field) {
            final PDFEditTool tool = (PDFEditTool) field.getSelectedItem();
            setSelectedTool(tool);
        }
    };

	public PDFToolPanel(final PDFEditPanel<T> editPanel) {
		super(editPanel.getActivityUI(), new GridBagLayout());
		this.editPanel = editPanel;
		this.btnUndo = new PDFUndoButton(editPanel);
		this.btnRedo = new PDFRedoButton(editPanel);
		initTools(editPanel.getClient());
		editPanel.getPagePanel().addEditListener(this);
		initComponents();
	}

	private void initTools(final MBLClient client) {
		final List<PDFEditTool> l = this.tools;
		l.add(new PDFEditToolMove());
		l.add(new PDFEditToolEraser());
		l.add(new PDFEditToolRotate());
		l.add(new PDFEditToolPaint());
		l.add(new PDFEditToolTSize());
		l.add(new PDFEditToolPen());
		l.add(new PDFEditToolCheck());
		l.add(new PDFEditToolX());
		l.add(new PDFEditToolXA());
		l.add(new PDFEditToolLine());
		l.add(new PDFEditToolArrow());
		l.add(new PDFEditToolRect());
		l.add(new PDFEditToolEllipse());
    l.add(new PDFEditToolPlaceHolder("Elektroverteiler", RED));
		l.add(new PDFEditToolPlaceHolder("Heizkörper", MAGENTA));
    l.add(new PDFEditToolPlaceHolder("Heizverteiler", MAGENTA));
    l.add(new PDFEditToolPlaceHolder("Steigzone", RED));
    l.add(new PDFEditToolPlaceHolder(PDFEditToolPlaceHolder. TEXT_INPUT, null));
    l.add(new PDFEditToolAbluftrohr("Abluftrohr O->", 0, ORANGE));
		l.add(new PDFEditToolAbluftrohr("Abluftrohr <-O", 1, ORANGE));
		l.add(textTool);
		textField.setKeyboardStateKey("pdfToolTextField");
	}
	
	private boolean isUsingTextInput() {
		for (final PDFEditTool tool : tools) {
			if (tool.isUsingTextInput())
				return true;
		}
		return false;
	}

	private void initComponents() {
		setBorder(new MBLScrollPaneBorder());
		setMinimumSize(new Dimension(32, 32));
		textField.setFontSize(MBLFontSize.TEXT2);
		textField.setComponentSize(MBLButtonSize.PDF_TOOLBUTTON);
		textField.addFocusListener(textFocusListener);
        for (final PDFEditTool tool : tools) {
            if (tool.isExtraTool())
                toolChoice.addItem(tool);
        }
        toolChoice.setSelectedItem(null);
        toolChoice.addSelectionListener(toolChoiceListener);
		setSelectedTool(tools.get(0));
		setSelectedColor(COLOR_CHOICES[0]);
	}

	@Override
	public void updateLayout(final boolean isPortrait) {
		layoutX = 0;

		// Determine consistent height for all components (HiDPI compatible)
		int componentHeight = getConsistentComponentHeight();

		// add the tools
		for (final PDFEditTool tool : tools) {
			if (!tool.isExtraTool()) {
                final PDFButton button = getButton(tool);
                ensureComponentHeight(button, componentHeight);
                add(button, GridBagConstraints.button(layoutX++, 0).insets(2));
            }
		}
		if (isUsingTextInput()) {
			ensureComponentHeight(textField, componentHeight);
			add(textField, GridBagConstraints.field(layoutX++, 0).insets(2));
		}
		ensureComponentHeight(toolChoice, componentHeight);
        add(toolChoice, GridBagConstraints.button(layoutX++, 0).insets(2));
		for (final Color color : COLOR_CHOICES) {
			final PDFButton button = getButton(color);
			ensureComponentHeight(button, componentHeight);
			add(button, GridBagConstraints.button(layoutX++, 0).insets(2));
		}

		ensureComponentHeight(btnUndo, componentHeight);
		add(btnUndo, GridBagConstraints.button(layoutX++, 0).insets(2));
		ensureComponentHeight(btnRedo, componentHeight);
		add(btnRedo, GridBagConstraints.button(layoutX++, 0).insets(2));

		MBLComponent.fixupSize(this, 0, 0);
	}

	public String getTextToolInput() {
		return Strings.clean(textField.getText());
	}

	public void setTextToolInput(final CharSequence text) {
		textField.setText(Strings.toString(text));
	}

	/**
	 * Adds a tool to the list of available tools
	 */
	public void addTool(final PDFEditTool tool, final boolean selectIt) {
		if (tool == null || tools.contains(tool))
			return;
		tools.add(0, tool);
		if (selectIt)
			setSelectedTool(tool);
	}

	// --------------------------------------------------------------
	// ---
	// --- Color selection
	// ---
	// --------------------------------------------------------------
	private Color selectedColor;

	/**
	 * Gets the selected color (can be null)
	 */
	public Color getSelectedColor() {
		return selectedColor;
	}

	/**
	 * Sets the selected color (can be null);
	 */
	public void setSelectedColor(final Color color) {
		if (this.selectedColor == color)
			return;
		setSelected(selectedColor, false);
		this.selectedColor = color;
		setSelected(selectedColor, true);
	}

	// --------------------------------------------------------------
	// ---
	// --- Tool selection
	// ---
	// --------------------------------------------------------------
	private PDFEditTool selectedTool;

	public boolean isSelected(final PDFEditTool tool) {
		return Primitives.equals(selectedTool, tool);
	}

	public PDFEditTool getSelectedTool() {
		return selectedTool;
	}

	public void setSelectedTool(final PDFEditTool tool) {
		if (Primitives.equals(selectedTool, tool))
			return;
		if (selectedTool != null) {
			selectedTool.toolDeactivated(editPanel);
			if (selectedTool.isExtraTool() && toolChoice.getSelectedItem() != null) {
			    toolChoice.setSelectedItem(null);
            } else {
                setSelected(selectedTool, false);
            }
		}
		selectedTool = tool;
		if (selectedTool != null) {
			selectedTool.toolActivated(editPanel);
			if (selectedTool.isExtraTool() && toolChoice.getSelectedItem() != selectedTool) {
			    toolChoice.setSelectedItem(selectedTool);
            } else {
                setSelected(selectedTool, true);
            }
		}
		repaint();
	}

	@Override
	public void editClicked(final int x, final int y) {
		final PDFEditTool tool = getSelectedTool();
		if (tool == null)
			return;
		tool.editClicked(editPanel, x, y);
	}

	@Override
	public boolean editAcceptsDragFrom(final int x, final int y) {
		final PDFEditTool tool = getSelectedTool();
		if (tool == null)
			return false;
		return tool.editAcceptsDragFrom(editPanel, x, y);
	}

	@Override
	public boolean editDragged(final double deltaX, final double deltaY) {
		final PDFEditTool tool = getSelectedTool();
		if (tool == null)
			return false;
		return tool.editDragged(editPanel, deltaX, deltaY);
	}

	// --------------------------------------------------------------
	// ---
	// --- Tool / Color button management
	// ---
	// --------------------------------------------------------------
	private final Map<Object, PDFButton> buttonMap = new HashMap<>();

	private void setSelected(final Object key, final boolean selected) {
		final PDFButton button = buttonMap.get(key);
		if (button != null)
            button.setSelected(selected);
	}

	/**
	 * Gets or creates button for color
	 */
	private PDFButton getButton(final Color color) {
		PDFButton button = buttonMap.get(color);
		if (button == null) {
			button = new PDFColorButton(this, color);
			buttonMap.put(color, button);
			if (Primitives.equals(selectedColor, color))
				button.setSelected(true);
		}
		return button;
	}

	/**
	 * Gets or creates button for tool
	 */
	private PDFButton getButton(final PDFEditTool tool) {
		PDFButton button = buttonMap.get(tool);
		if (button == null) {
			button = new PDFToolButton(this, tool);
			buttonMap.put(tool, button);
			if (Primitives.equals(selectedTool, tool))
				button.setSelected(true);
		}
		return button;
	}

}

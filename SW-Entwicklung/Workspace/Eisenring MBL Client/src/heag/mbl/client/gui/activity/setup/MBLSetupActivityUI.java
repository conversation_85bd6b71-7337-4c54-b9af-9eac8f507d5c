package heag.mbl.client.gui.activity.setup;

import heag.mbl.client.action.MBLAction;
import heag.mbl.client.gui.activity.ActivityUILayoutHandler;
import heag.mbl.client.gui.activity.LayoutHandlerBase;
import heag.mbl.client.gui.activity.MBLAbstractActivityUI;
import heag.mbl.client.gui.componentapi.KeyboardSwitchMode;
import heag.mbl.client.gui.components.MBLLabel;
import heag.mbl.client.gui.components.choice.MBLChoiceField;
import heag.mbl.client.gui.components.choice.MBLCodeChoiceField;
import heag.mbl.client.gui.components.text.MBLTextField;
import heag.saggitarius.fs.BlockSizeCode;
import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.platform.Platform;
import ch.eisenring.gui.GridBagConstraints;
import ch.eisenring.gui.action.AbstractAction;
import ch.eisenring.gui.model.ValidationResults;
import ch.eisenring.gui.resource.images.Images;

@SuppressWarnings("serial")
public class MBLSetupActivityUI extends MBLAbstractActivityUI<MBLSetupActivity> {
	
	private MBLLabel lblDeviceName = new MBLLabel("Geräte-Name:");
	private MBLLabel lblBlockSize = new MBLLabel("Blockgrösse:");
	private MBLLabel lblBlockCount = new MBLLabel("Blockanzahl:");
	private MBLTextField txtDeviceName = new MBLTextField(30);
	private MBLChoiceField<BlockSizeCode> cmbBlockSize = new MBLCodeChoiceField<>(BlockSizeCode.class, null, AbstractCode.Order.Id);

	private MBLTextField txtBlockCount = new MBLTextField(16);

	MBLAction actionDoSetup = new MBLAction(getActivity(), "Weiter", Images.DIALOG_OK) {
		@Override
		protected void performAction() {
			if (!validateView(true))
				return;
			activity.deviceName.set(txtDeviceName.getText());
			activity.blockCount.set(getBlockCount());
			activity.blockSize.set(cmbBlockSize.getSelectedItem());
			activity.setup3();
		}
	};

	private final ActivityUILayoutHandler DATA_INPUT = new LayoutHandlerBase(this, UIStates.DATA_INPUT) {
		@Override
		public void initLayout(final boolean isPortrait) {
			add(lblDeviceName, GridBagConstraints.label(0, 1));
			add(txtDeviceName, GridBagConstraints.fixed(1, 1));
			
			add(lblBlockSize, GridBagConstraints.label(0, 2));
			add(cmbBlockSize, GridBagConstraints.fixed(1, 2));

			add(lblBlockCount, GridBagConstraints.label(0, 3));
			add(txtBlockCount, GridBagConstraints.fixed(1, 3));
		}
		
		@Override
		public AbstractAction getForwardAction() {
			return actionDoSetup;
		};

		@Override
		public void validateView(final ValidationResults results, final Object model) {
			super.validateView(results, model);
			if ((Strings.isEmpty(txtDeviceName.getText())))
				results.add("Gerätename ist Pflichtfeld", txtDeviceName);
			final long blockCount = getBlockCount();
			if (blockCount < 1000 || blockCount > 1_000_000_000)
				results.add("Block-Anzahl muss zwischen 1'000 und 1'000'000'000 liegen", txtBlockCount);
			final BlockSizeCode blockSize = cmbBlockSize.getSelectedItem();
			if (blockSize == null)
				results.add("Keine gültige Blockgrösse ausgewählt", cmbBlockSize);
		}
		
	};

	private long getBlockCount() {
		final String text = txtBlockCount.getText();
		try {
			return Strings.parseLong(text);
		} catch (final Exception e) {
			return -1;
		}
	}

	public MBLSetupActivityUI(final MBLSetupActivity activity) {
		super(activity);
		addLayoutHandler(DATA_INPUT);
		initComponents();
	}

	private void initComponents() {
		final Platform platform = Platform.getPlatform();
		txtDeviceName.setText(platform.getLocalHostName());
		cmbBlockSize.setSelectedItem(activity.blockSize.get());
		txtBlockCount.setText(Strings.toString(activity.blockCount.get()));
		txtBlockCount.setKeyboardMode(KeyboardSwitchMode.ONLY_NUMPAD);
	}

	// --------------------------------------------------------------
	
}

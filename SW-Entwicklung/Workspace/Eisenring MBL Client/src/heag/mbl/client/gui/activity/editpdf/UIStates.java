package heag.mbl.client.gui.activity.editpdf;

import static heag.mbl.client.gui.activity.MBLUIState.BLOCK_FORWARD_BACKWARD;
import static heag.mbl.client.gui.activity.MBLUIState.BLOCK_SUSPEND;
import heag.mbl.client.gui.activity.MBLUIState;

interface UIStates extends heag.mbl.client.gui.activity.UIStates {

	MBLUIState EDIT_PDF = MBLUIState.create("EditPDF", BLOCK_FORWARD_BACKWARD | BLOCK_SUSPEND);

}

package heag.mbl.client.gui.activity;

import heag.mbl.client.MBLClient;
import heag.mbl.client.gui.components.MBLBasePanel;
import heag.mbl.client.gui.components.MBLBusyPanel;
import heag.mbl.client.gui.components.MBLLabelPanel;
import heag.mbl.client.gui.components.MBLNetworkStatusIcon;

import java.awt.GridBagLayout;

import javax.swing.JComponent;

import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.gui.GridBagConstraints;

/**
 * Provides some standard UI component factories
 */
public interface MBLStandardUIFactory {

	// --------------------------------------------------------------
	// ---
	// --- Factory methods
	// ---
	// --------------------------------------------------------------
	/**
	 * Creates a standard busy-panel
	 */
	public static JComponent createBusy(final MBLClient client) {
		final MBLBasePanel contentPanel = new MBLBasePanel(new GridBagLayout());
		contentPanel.add(new MBLBusyPanel(), GridBagConstraints.panel(0, 0)
				.fill(GridBagConstraints.NONE)
				.anchor(GridBagConstraints.CENTER)
				.gridWidth(2));
		contentPanel.add(new MBLNetworkStatusIcon(client), GridBagConstraints.panel(0, 1)
				.fill(GridBagConstraints.NONE)
				.anchor(GridBagConstraints.SOUTHWEST));
		return contentPanel;
	}

	/**
	 * Creates an error panel for the given ErrorMessage
	 */
	public static JComponent createError(final MBLActivity activity) {
		final ErrorMessage message = activity.getProperty(PrivateProperties.ERROR_MESSAGE);
		return new MBLLabelPanel(message.getText(), message.getSeverity().getIcon());
	}

	/**
	 * Creates an error panel for the given ErrorMessage
	 */
	public static JComponent createError(final ErrorMessage message) {
		return new MBLLabelPanel(message.getText(), message.getSeverity().getIcon());
	}

}

package heag.mbl.client.gui.activity;

import heag.mbl.client.MBLClient;

import java.util.concurrent.atomic.AtomicReference;

import ch.eisenring.core.resource.ImageResource;
import ch.eisenring.gui.util.GUIUtil;
import ch.eisenring.user.shared.Permission;

public abstract class MBLActivityFactory {

	protected final String name;
	protected final ImageResource icon;
	protected final MBLActivityType type;
	protected final Permission requiredPermission;

	public MBLActivityFactory(final String name, final ImageResource icon, final MBLActivityType type) {
		this(name, icon, type, (Permission) null);
	}

	public MBLActivityFactory(final String name, final ImageResource icon, final MBLActivityType type, final Permission requiredPermission) {
		this.name = name;
		this.icon = icon;
		this.type = type;
		this.requiredPermission = requiredPermission;
	}

	/**
	 * Checks if the activity type is permitted for the current user.
	 */
	public boolean isPermitted() {
		return requiredPermission == null || requiredPermission.isPermitted();
	}

	/**
	 * Gets the name of this activity
	 */
	public final String getName() {
		return name;
	}

	/**
	 * Gets the icon of this activity
	 */
	public final ImageResource getIcon() {
		return icon;
	}

	/**
	 * Gets the type of activity
	 */
	public final MBLActivityType getType() {
		return type;
	}

	public final MBLActivity create(final MBLClient client) {
		if (GUIUtil.isEventDispatchThread())
			return createInternal(client);
		final AtomicReference<MBLActivity> result = new AtomicReference<>();
		try {
			GUIUtil.invokeAndWait(new Runnable() {
				@Override
				public void run() {
					result.set(createInternal(client));
				}
			});
		} catch (final Exception e) {
			throw new RuntimeException(e.getMessage(), e);
		}
		return result.get();
	}

	private MBLActivity createInternal(final MBLClient client) {
		final MBLActivity activity = createImpl(client);
		activity.setPropertyDefault(MBLActivityProperties.ACTIVITY_TYPE, type);
		activity.setPropertyDefault(MBLActivityProperties.SYMBOL_ICON, icon);
		activity.setPropertyDefault(MBLActivityProperties.LOCATION, name);
		return activity;
	}

	/**
	 * Implements activity creation. Guaranteed to be called on EDT only.
	 */
	protected abstract MBLActivity createImpl(final MBLClient client);

}


package heag.mbl.client.gui.activity.settings;

import heag.mbl.client.MBLClient;
import heag.mbl.client.gui.activity.ActivityDataObject;

public class SettingsDataObject extends ActivityDataObject {

	boolean scrollbarHorizontalInvert;
	boolean scrollbarVerticalInvert;
	int hausUebersichtBaseSize;
	
	public void readFrom(final MBLClient client) {
		// TODO Auto-generated constructor stub
	}

	public void applyTo(final MBLClient client) {
		
	}

}

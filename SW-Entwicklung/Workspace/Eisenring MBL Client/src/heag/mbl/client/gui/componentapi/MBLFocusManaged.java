package heag.mbl.client.gui.componentapi;

import heag.mbl.client.gui.components.text.MBLKeyboardManager;
import heag.mbl.client.gui.window.Layer;

import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.awt.event.MouseListener;

import javax.swing.JComponent;

import ch.eisenring.core.logging.Logger;
import ch.eisenring.gui.interfaces.HEAGComponent;

/**
 * Marker interface for components requiring popup display on focus
 * (that is keyboard, choice field etc.)
 */
public interface MBLFocusManaged extends HEAGComponent {

	public final static MouseListener SHOW_POPUP_MOUSELISTENER = new MouseAdapter() {
		@Override
		public void mousePressed(final MouseEvent event) {
			final Object source = event.getSource();
			if (!(source instanceof MBLFocusManaged))
				return;
			final MBLFocusManaged focusManaged = (MBLFocusManaged) source;
			if (focusManaged.isInPopupZone(event)) {
				focusManaged.showPopup();
			}
		}
	};

	/**
	 * Gets the swing component realizing this component
	 */
	@SuppressWarnings("unchecked")
	public default JComponent getSwingComponent() {
		return (JComponent) this;
	}
	
	/**
	 * Gets the layer for the popup for this component
	 */
	public Layer getPopupLayer();

	/**
	 * Gets the popup for this component
	 */
	public MBLPopup getPopup();

	/**
	 * Requests the popup to be shown for this component
	 */
	public default void showPopup() {
		if (!isEditable())
			return;
		try {
			final MBLKeyboardManager manager = MBLKeyboardManager.getInstance();
			if (manager != null)
				manager.showPopup(this);
		} catch (final Exception e) {
			Logger.warn(e);
		}
	}

	/**
	 * Returns true if this component can be modified by the user
	 */
	public default boolean isEditable() {
		return getSwingComponent().isEnabled();
	}

	public default boolean isInPopupZone(final MouseEvent event) {
		return true;
	}

}

package heag.mbl.client.gui.activity.montbewert;

import heag.dsp.pub.model.DSPMontageBewertung;
import heag.mbl.client.MBLClient;
import heag.mbl.client.gui.activity.MBLActivity;
import heag.mbl.client.gui.activity.MBLActivityProperties;
import heag.mbl.client.gui.activity.MBLActivityUIPanel;
import heag.mbl.client.gui.components.MBLLabel;
import heag.mbl.client.gui.components.choice.MBLChoiceField;
import heag.mbl.client.gui.components.choice.MBLCodeChoiceField;
import heag.mbl.client.gui.components.text.MBLTextArea;
import heag.mbl.client.gui.components.text.MBLTextField;

import java.awt.GridBagLayout;

import javax.swing.BorderFactory;

import ch.eisenring.core.codetable.RatingCode;
import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.collections.Set;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.gui.GridBagConstraints;
import ch.eisenring.gui.components.Separator;
import ch.eisenring.gui.model.ValidationResults;
import ch.eisenring.logiware.code.soft.LWMonteurSirCode;
import ch.eisenring.lw.api.LWAuftragData;

@SuppressWarnings("serial")
final class RatingPanel extends MBLActivityUIPanel<ActivityUI> {

	static class RatingLine {
		final MBLLabel label;
		final MBLChoiceField<RatingCode> choice = new MBLCodeChoiceField<>(RatingCode.class);
		final MBLTextField comment = new MBLTextField(120);
		
		RatingLine(final String label) {
			this.label = new MBLLabel(label);
		}
	}

	/**
	 * Set of entries that are not allowed for rating
	 */
	final static Set<LWMonteurSirCode> MONTEUR_REJECTED = Set.asReadonlySet(LWMonteurSirCode.MONT);
	
	final MBLLabel lblMonteur = new MBLLabel("Monteur:");
	final MBLLabel lblRating = new MBLLabel("Erwartungen:");
	final MBLLabel lblReason = new MBLLabel("Begründung:");
	final MBLLabel lblComment = new MBLLabel("Allgemeine Bemerkungen");
	final MBLTextArea txtComment = new MBLTextArea(4000);
	final MBLCodeChoiceField<LWMonteurSirCode> cmbMonteur = new MBLCodeChoiceField<>(LWMonteurSirCode.class);
	
	final RatingLine lArbeit = new RatingLine("Arbeitsqualität:");
	final RatingLine lSauberkeit = new RatingLine("Sauberkeit:");
	final RatingLine lAuftritt = new RatingLine("Auftritt:"); 
	final RatingLine lAdministration = new RatingLine("Administration:"); 
	final RatingLine lBauleitung = new RatingLine("Bauleitung:"); 

	final RatingLine[] lines = {
		lArbeit, lSauberkeit, lAuftritt, lAdministration, lBauleitung
	};
	
	public RatingPanel(final ActivityUI activityUI) {
		super(activityUI, new GridBagLayout());
		initComponents();
	}

	private void initComponents() {
		setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));
	}

	void doLayout(final boolean isPortrait) {
		// Add debugging tooltips to show component sizing information
		addLayoutDebuggingTooltips(isPortrait);

		int y = 0;
		addComponentTooltip(lblMonteur);
		add(lblMonteur, GridBagConstraints.label(0, y));
		addComponentTooltip(cmbMonteur);
		add(cmbMonteur, GridBagConstraints.field(1, y).gridWidthRemainder());
		++y;
		add(Separator.create(), GridBagConstraints.separator(0, y));
		++y;
		addComponentTooltip(lblRating);
		add(lblRating, GridBagConstraints.fixed(1, y));
		if (isPortrait) {
			addComponentTooltip(lblReason);
			add(lblReason, GridBagConstraints.fixed(0, y));
		} else {
			addComponentTooltip(lblReason);
			add(lblReason, GridBagConstraints.fixed(2, y));
		}
		for (final RatingLine line : lines) {
			++y;
			if (isPortrait) {
				addComponentTooltip(line.label);
				add(line.label, GridBagConstraints.label(0, y));
				addComponentTooltip(line.choice);
				add(line.choice, GridBagConstraints.fixed(1, y));
				++y;
				addComponentTooltip(line.comment);
				add(line.comment, GridBagConstraints.field(0, y).gridWidthRemainder());
			} else {
				addComponentTooltip(line.label);
				add(line.label, GridBagConstraints.label(0, y));
				addComponentTooltip(line.choice);
				add(line.choice, GridBagConstraints.fixed(1, y));
				addComponentTooltip(line.comment);
				add(line.comment, GridBagConstraints.field(2, y));
			}
		}
		addComponentTooltip(lblComment);
		add(lblComment, GridBagConstraints.field(0, ++y).gridWidthRemainder());
		addComponentTooltip(txtComment);
		add(txtComment, GridBagConstraints.area(0, ++y).gridWidthRemainder());
	}

	boolean isModified() {
		if (!Strings.isEmpty(txtComment.getText()))
			return true;
		for (final RatingLine line : lines) {
			if (!Strings.isEmpty(line.comment.getText()))
				return true;
			if (!AbstractCode.equals(line.choice.getSelectedItem(), RatingCode.NULL))
				return true;
		}
		return false;
	}

	@Override
	public void validateView(final ValidationResults results, final Object model) {
		super.validateView(results, model);
		// validate the lines
		int ratedCount = 0;
		for (final RatingLine line : lines) {
			final boolean hasComment = !Strings.isEmpty(line.comment.getText());
			final RatingCode code = line.choice.getSelectedItem();
			if (AbstractCode.equals(code, RatingCode.NULL)) {
				if (hasComment)
					results.add("Begründung nur erlaubt wenn Bewertet", line.comment);
			} else {
				++ratedCount;
				if (!hasComment && !AbstractCode.equals(code, RatingCode.C10))
					results.add("Bewertung ungleich 1.0 erfordert eine Begründung", line.comment);
			}
		}
		if (ratedCount <= 0)
			results.add("Mindestens eine Kategorie muss bewertet werden", lines[0].comment);
		final LWMonteurSirCode monteur = cmbMonteur.getSelectedItem();
		if (AbstractCode.isNull(monteur))
			results.add("Kein Monteur ausgewählt", cmbMonteur);
		if (MONTEUR_REJECTED.contains(monteur))
			results.add("Der Platzhalter-Monteur kann nicht bewertet werden", cmbMonteur);
	}

	public DSPMontageBewertung getMontageBewertung() {
		final DSPMontageBewertung bewertung = DSPMontageBewertung.create(cmbMonteur.getSelectedItem());
		final MBLActivity activity = activityUI.getActivity();
		final MBLClient client = activity.getClient();
		final LWAuftragData auftrag = (LWAuftragData) activity.getProperty(MBLActivityProperties.LOCATION);
		bewertung.setUser(client.CREDENTIALS_USER.get());
		bewertung.setAuftragKey(auftrag.getAuftragKey());
		bewertung.setProjektKey(auftrag.getProjektKey());
		// copy normal attributes from ui
		bewertung.setCommentGeneral(txtComment.getText());
		bewertung.setCommentArbeit(lArbeit.comment.getText());
		bewertung.setCommentSauberkeit(lSauberkeit.comment.getText());
		bewertung.setCommentAuftritt(lAuftritt.comment.getText());
		bewertung.setCommentAdministration(lAdministration.comment.getText());
		bewertung.setCommentBauleitung(lBauleitung.comment.getText());
		bewertung.setQualityArbeit(lArbeit.choice.getSelectedItem());
		bewertung.setQualitySauberkeit(lSauberkeit.choice.getSelectedItem());
		bewertung.setQualityAuftritt(lAuftritt.choice.getSelectedItem());
		bewertung.setQualityAdministration(lAdministration.choice.getSelectedItem());
		bewertung.setQualityBauleitung(lBauleitung.choice.getSelectedItem());
		bewertung.setMonteurSIR(cmbMonteur.getSelectedItem());
		return bewertung;
	}

	// --------------------------------------------------------------
	// ---
	// --- Size Debugging Tooltip Methods
	// ---
	// --------------------------------------------------------------

	/**
	 * Adds debugging tooltips to show layout and sizing information for the panel.
	 * Uses a dynamic approach to show actual rendered sizes.
	 */
	private void addLayoutDebuggingTooltips(final boolean isPortrait) {
		// Set up a dynamic tooltip for the panel that shows real-time information
		addMouseListener(new java.awt.event.MouseAdapter() {
			@Override
			public void mouseEntered(java.awt.event.MouseEvent e) {
				// Get panel's own size information
				java.awt.Dimension panelSize = getSize();
				java.awt.Dimension panelPreferred = getPreferredSize();

				// Get sample component sizes for comparison
				String monteurChoiceInfo = "N/A";
				String textFieldInfo = "N/A";
				String textAreaInfo = "N/A";
				String labelInfo = "N/A";

				if (cmbMonteur != null) {
					java.awt.Dimension size = cmbMonteur.getSize();
					monteurChoiceInfo = String.format("%d×%d px", size.width, size.height);
				}

				if (lines.length > 0 && lines[0].comment != null) {
					java.awt.Dimension size = lines[0].comment.getSize();
					textFieldInfo = String.format("%d×%d px", size.width, size.height);
				}

				if (txtComment != null) {
					java.awt.Dimension size = txtComment.getSize();
					textAreaInfo = String.format("%d×%d px", size.width, size.height);
				}

				if (lblMonteur != null) {
					java.awt.Dimension size = lblMonteur.getSize();
					labelInfo = String.format("%d×%d px", size.width, size.height);
				}

				// Create tooltip with actual rendered sizes including panel size
				String tooltip = String.format(
					"<html><b>RatingPanel Layout Info:</b><br>" +
					"<b>Panel Size: %d×%d px</b><br>" +
					"Panel Preferred: %d×%d px<br>" +
					"Portrait Mode: %s<br>" +
					"<hr>" +
					"Choice Size: %s<br>" +
					"TextField Size: %s<br>" +
					"TextArea Size: %s<br>" +
					"Label Size: %s<br>" +
					"DPI Scale: %.1fx<br>" +
					"<i>Hover over components for detailed info</i></html>",
					panelSize.width, panelSize.height,
					panelPreferred.width, panelPreferred.height,
					isPortrait ? "Yes" : "No",
					monteurChoiceInfo,
					textFieldInfo,
					textAreaInfo,
					labelInfo,
					calculateDPIScale()
				);

				setToolTipText(tooltip);
			}
		});
	}

	/**
	 * Adds a debugging tooltip to a component showing its size information.
	 * Uses a dynamic tooltip that shows actual rendered size when hovered.
	 * @param component The component to add tooltip to
	 */
	private void addComponentTooltip(java.awt.Component component) {
		if (component != null && component instanceof javax.swing.JComponent) {
			javax.swing.JComponent jComponent = (javax.swing.JComponent) component;

			// Get component type name for tooltip
			String componentType = component.getClass().getSimpleName();
			if (component instanceof MBLLabel) {
				componentType = "MBLLabel";
			} else if (component instanceof MBLTextField) {
				componentType = "MBLTextField";
			} else if (component instanceof MBLTextArea) {
				componentType = "MBLTextArea";
			} else if (component instanceof MBLChoiceField) {
				componentType = "MBLChoiceField";
			}

			// Create a dynamic tooltip that shows actual size when displayed
			final String finalComponentType = componentType;
			jComponent.setToolTipText("Loading size info...");

			// Override getToolTipText to provide real-time size information
			jComponent.setToolTipText(null); // Clear first
			jComponent.addMouseListener(new java.awt.event.MouseAdapter() {
				@Override
				public void mouseEntered(java.awt.event.MouseEvent e) {
					// Get actual rendered sizes
					java.awt.Dimension actualSize = component.getSize();
					java.awt.Dimension preferredSize = component.getPreferredSize();
					java.awt.Dimension minimumSize = component.getMinimumSize();

					String tooltip = String.format(
						"<html><b>%s Size Info:</b><br>" +
						"Actual Size: %d×%d px<br>" +
						"Preferred: %d×%d px<br>" +
						"Minimum: %d×%d px<br>" +
						"Square (actual): %s<br>" +
						"DPI Scale: %.1fx</html>",
						finalComponentType,
						actualSize.width, actualSize.height,
						preferredSize.width, preferredSize.height,
						minimumSize.width, minimumSize.height,
						(actualSize.width == actualSize.height) ? "Yes" : "No",
						calculateDPIScale()
					);

					// Preserve existing tooltip if any
					String existingTooltip = getOriginalTooltip(jComponent);
					if (existingTooltip != null && !existingTooltip.isEmpty()) {
						tooltip = existingTooltip + "<br><br>" + tooltip;
					}

					jComponent.setToolTipText(tooltip);
				}
			});
		}
	}

	/**
	 * Helper method to get the original tooltip text before we added size info.
	 * This is a simplified version - in practice you might want to store original tooltips.
	 */
	private String getOriginalTooltip(javax.swing.JComponent component) {
		// For now, return null - but you could enhance this to preserve original tooltips
		return null;
	}

	/**
	 * Calculates the current DPI scaling factor.
	 * @return The DPI scale factor (1.0 = 100%, 1.5 = 150%, etc.)
	 */
	private double calculateDPIScale() {
		try {
			java.awt.Toolkit toolkit = java.awt.Toolkit.getDefaultToolkit();
			int dpi = toolkit.getScreenResolution();
			return dpi / 96.0; // 96 DPI is considered 100% scaling
		} catch (Exception e) {
			return 1.0; // Default to 100% if calculation fails
		}
	}

}

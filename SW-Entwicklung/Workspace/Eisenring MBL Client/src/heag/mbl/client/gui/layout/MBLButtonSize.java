package heag.mbl.client.gui.layout;

import java.awt.Dimension;

public final class MBLButtonSize extends MBLSizeFraction implements MBLComponentSize {

	public final static MBLComponentSize LOCBAR = new MBLButtonSize("LocationBar", 16, 16);

	public final static MBLComponentSize CHECKBOX = new MBLButtonSize("CheckBox", 24, 12);

	public final static MBLComponentSize DIALOG_TITLE = new MBLButtonSize("DialogTitleIcon", 32, 16);

	public final static MBLComponentSize KEYBOARD_KEY = new MBLButtonSize("Keyboard Key", 12, 12, 16, 12);

	public final static MBLComponentSize OBJECT_BUTTON = new MBLButtonSize("DMSObjectButton", 12, 16);

	public final static MBLComponentSize THUMBNAIL = new MBLSizeFixed("Thumbnail", 192, 160);

	public final static MBLComponentSize THUMBNAIL_OPTION = new MBLSizeFixed("ThumbnailOption", 32, 32);

	public final static MBLComponentSize MAINMENUITEM = new MBLButtonSize("MainMenuItem", 12, 64);

	public final static MBLComponentSize PDF_TOOLBUTTON = new MBLButtonSize("PDFToolButton",
			32, 20, 32, 20);

	/**
	 * Minimum size for buttons of this type in landscape mode
	 */
	private final Dimension minimumDimensionLandscape;
	
	/**
	 * Minimum size of buttons of this type in portrait mode
	 */
	private final Dimension minimumDimensionPortrait;

	@Deprecated
	private MBLButtonSize(final String name, final int fraction, final int minSize) {
		this(name, fraction, minSize, fraction, minSize);
	}

	private MBLButtonSize(final String name,
			final int fractionLandscape, final int minSizeLandscape,
			final int fractionPortrait, final int minSizePortrait) {
		super(name, fractionLandscape, minSizeLandscape, fractionPortrait, fractionLandscape);
		this.minimumDimensionLandscape = new Dimension(minSizeLandscape, minSizeLandscape);
		this.minimumDimensionPortrait = new Dimension(minSizePortrait, minSizePortrait);
	}

	@Override
	public Dimension getPreferredSize(final MBLLayout layout) {
		final boolean portrait = layout.isPortrait();
		int size = Math.max(layout.getSmallSurfaceSize() / getFraction(portrait), getMinSize(portrait));

		// Apply DPI scaling correction for button sizes
		size = applyDPIScaling(size);

		return new Dimension(size, size);
	}

	/**
	 * Applies DPI scaling correction to button sizes.
	 * This fixes the issue where HiDPI displays get tiny buttons due to JavaFX logical vs physical pixel differences.
	 */
	private int applyDPIScaling(int baseSize) {
		try {
			// Get system DPI scale factor
			java.awt.Toolkit toolkit = java.awt.Toolkit.getDefaultToolkit();
			int dpi = toolkit.getScreenResolution();
			double dpiScale = dpi / 96.0; // 96 DPI is considered 100% scaling

			// Only apply scaling if we detect high DPI and the button is very small
			if (dpiScale > 1.2 && baseSize < 20) {
				// Scale up small buttons on high DPI displays
				return (int) Math.round(baseSize * dpiScale);
			}

			return baseSize;
		} catch (Exception e) {
			// If DPI detection fails, return original size
			return baseSize;
		}
	}

	@Override
	public Dimension getMinimumSize(final MBLLayout layout) {
		return layout.isPortrait() ? minimumDimensionPortrait : minimumDimensionLandscape;
	}

}

package heag.mbl.client.gui.components;

import java.awt.Component;
import java.awt.event.MouseEvent;

import javax.swing.JComponent;

import ch.eisenring.core.interfaces.Proxy;
import ch.eisenring.gui.action.AbstractAction;

/**
 * Standard implementation for button mouse listeners
 */
public final class MouseListenerAction extends MouseListenerBase<Component> {

	protected final Proxy<AbstractAction> actionProxy;

	protected MouseListenerAction(final JComponent component, final Proxy<AbstractAction> actionProxy) {
		super(component);
		this.actionProxy = actionProxy;
	}

	@Override
	protected boolean isEnabled(final MouseEvent event) {
		return actionProxy != null && actionProxy.get() != null;
	}

	@Override
	protected void onClick(final MouseEvent event) {
		actionProxy.get().fire();
	}

}

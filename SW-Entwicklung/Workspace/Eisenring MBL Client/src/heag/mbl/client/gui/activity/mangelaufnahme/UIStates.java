package heag.mbl.client.gui.activity.mangelaufnahme;

import static heag.mbl.client.gui.activity.MBLUIState.BLOCK_BACKWARD;
import static heag.mbl.client.gui.activity.MBLUIState.BLOCK_SAVE;
import heag.mbl.client.gui.activity.MBLUIState;

interface UIStates extends heag.mbl.client.gui.activity.UIStates {

	/**
	 * State for selecting order position
	 */
	public final static MBLUIState SELECT_ORDER_POSITION = MBLUIState.create("SelectOrderPosition",
			BLOCK_SAVE | BLOCK_BACKWARD);

	/**
	 * State for entering position data
	 */
	public final static MBLUIState EDIT_POSITION_DATA = MBLUIState.create("EditPositionData");

	/**
	 * State for marking position in PDF
	 */
	public final static MBLUIState MARK_POSITION = MBLUIState.create("MarkPosition",
			BLOCK_SAVE | BLOCK_BACKWARD);

}

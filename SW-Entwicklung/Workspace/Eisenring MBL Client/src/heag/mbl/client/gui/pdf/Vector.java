package heag.mbl.client.gui.pdf;

import java.awt.geom.Point2D;

final class Vector {

	private final double x, y, z;

	public Vector(final Point2D p) {
		this(p.getX(), p.getY(), 0);
	}

	public Vector(final double x, final double y) {
		this(x, y, 0);
	}

	public Vector(final double x, final double y, final double z) {
		this.x = x;
		this.y = y;
		this.z = z;
	}

	public double getX() {
		return x;
	}

	public double getY() {
		return y;
	}

	public double getZ() {
		return z;
	}

	/**
	 * Returns a new vector with all components multiplied by factor
	 */
	public Vector scale(final double factor) {
		return new Vector(x * factor, y * factor, z * factor);
	}

	/**
	 * Subtracts vector V from this vector, returns new vector.
	 */
	public Vector sub(final Vector v) {
		double xv = x - v.x;
		double yv = y - v.y;
		double zv = z - v.z;
		return new Vector(xv, yv, zv);
	}

	/**
	 * Adds vector V to this vector, returns new vector.
	 */
	public Vector add(final Vector v) {
		double xv = x + v.x;
		double yv = y + v.y;
		double zv = z + v.z;
		return new Vector(xv, yv, zv);
	}

	/**
	 * Calculates cross vector V x this, returns new vector
	 */
	public Vector cross(final Vector v) {
		double cx = y*v.z - z*v.y;
		double cy = z*v.x - x*v.z;
		double cz = x*v.y - y*v.x;
		return new Vector(cx, cy, cz);
	}

	/**
	 * Calculates the length of this vector.
	 */
	public double length() {
		double l = x * x + y * y + z * z;
		return l <= 0 ? 0 : Math.sqrt(l);
	}

	/**
	 * Calculates the cosine between this angle and another angle
	 */
	public double cos(final Vector v) {
		double ab = (x * v.x) + (y * v.y) + (z * v.z);
		double da = length();
		double db = v.length();
		return ab / (da * db);
	}

	/**
	 * Gives the cosine between this vector and the x-axis
	 */
	public double cosX() {
		return x / length();
	}

	/**
	 * Gives the cosine between this vector and the y-axis
	 */
	public double cosY() {
		return y / length();
	}

	/**
	 * Gives the cosine between this vector and the z-axis
	 */
	public double cosZ() {
		return z / length();
	}

}

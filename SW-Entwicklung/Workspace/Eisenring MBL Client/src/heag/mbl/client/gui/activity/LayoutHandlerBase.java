package heag.mbl.client.gui.activity;

import ch.eisenring.gui.interfaces.Validateable;
import ch.eisenring.gui.model.ValidationResults;
import heag.mbl.client.MBLClient;

public abstract class LayoutH<PERSON>lerBase implements ActivityUILayout<PERSON><PERSON><PERSON>, Validateable {

	protected final MBLAbstractActivityUI<?> activityUI;
	protected final MBLUIState uiState;
	
	protected LayoutHandlerBase(final MBLAbstractActivityUI<?> activityUI, final MBLUIState uiState) {
		this.activityUI = activityUI;
		this.uiState = uiState;
	}

	@Override
	public MBLActivity getActivity() {
		return activityUI.getActivity();
	}

	public final MBLClient getClient() {
		return activityUI.getClient();
	}

	@Override
	public final MBLUIState getUIState() {
		return uiState;
	}

	@Override
	public void validateView(final ValidationResults results, final Object model) {
		// default does nothing, needs to be overwritten by child class
	}

}

package heag.mbl.client.gui.components.dms;

import heag.mbl.client.action.MBLAction;
import heag.mbl.client.gui.ComponentStyle;
import heag.mbl.client.gui.componentapi.MBLCompoundComponent;
import heag.mbl.client.gui.components.MBLBasePanel;
import heag.mbl.client.gui.components.MBLImageButton;
import heag.mbl.client.gui.components.MBLLabel;
import heag.mbl.client.gui.layout.MBLButtonSize;
import heag.mbl.client.gui.layout.MBLFontSize;
import ch.eisenring.dms.service.api.DMSObject;
import ch.eisenring.dms.shared.client.util.DMSIconUtil;
import ch.eisenring.dms.shared.client.util.DMSObjectLabelFormatter;
import ch.eisenring.gui.BorderLayout;

/**
 * Standard implementation for representing an iconified/suspended Activity.
 */
@SuppressWarnings("serial")
public final class DMSObjectButton extends MBLBasePanel implements MBLCompoundComponent {

	protected final MBLAction action;
	protected final MBLImageButton button;
	protected final MBLLabel label;

	public DMSObjectButton(final MBLAction action) {
		super(new BorderLayout(0, 0));
		final DMSObject object = (DMSObject) action.getValue("DMSObject");
		this.action = action;
		this.button = new MBLImageButton(action);
		this.label = new MBLLabel(DMSObjectLabelFormatter.MAXDETAILED.format(object), MBLFontSize.OBJECT_LABEL);
		action.setIconResource(DMSIconUtil.getThumbnail(object));
		initComponents();
		initLayout();
	}

	private void initComponents() {
		final ComponentStyle style = ComponentStyle.PANEL;
		button.setStyle(style);
		label.setStyle(style);
		button.combineWith(label);
		button.setComponentSize(MBLButtonSize.OBJECT_BUTTON);
		label.setComponentSize(MBLButtonSize.OBJECT_BUTTON);
	}

	private void initLayout() {
		add(button, BorderLayout.WEST);
		add(label, BorderLayout.CENTER);
	}

}

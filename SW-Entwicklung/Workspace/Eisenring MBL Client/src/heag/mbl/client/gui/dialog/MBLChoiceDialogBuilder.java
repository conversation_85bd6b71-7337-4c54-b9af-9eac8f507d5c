package heag.mbl.client.gui.dialog;

import heag.mbl.client.MBLClient;
import heag.mbl.client.gui.componentapi.MBLComponent;
import heag.mbl.client.gui.components.MBLBasePanel;
import heag.mbl.client.gui.layout.MBLComponentSize;
import heag.mbl.client.gui.layout.MBLSizeFixed;

import java.awt.GridBagLayout;

import javax.swing.JComponent;

import ch.eisenring.gui.GridBagConstraints;
import ch.eisenring.gui.action.AbstractAction;

public final class MBLChoiceDialogBuilder extends MBLDialogBuilderBase {

	public MBLChoiceDialogBuilder(final MBLClient client) {
		super(client);
		titleCloseable = true;
		title = "Aktion wählen";
	}

	@Override
	protected JComponent getButtonPanel(final MBLDialog dialog) throws IllegalStateException {
		// this dialog type does not use a button panel
		return null;
	}

	private final static MBLComponentSize CHOICE_BUTTON_SIZE = new MBLSizeFixed("ChoiceButton", 128, 128);

	@Override
	protected JComponent getMainPanel(final MBLDialog dialog) throws IllegalStateException {
		final JComponent panel = new MBLBasePanel();
		panel.setLayout(new GridBagLayout());
		contentStyle.applyTo(panel);
		int x = -1;
		for (final AbstractAction action : actions) {
			final JComponent button = createButton(dialog, action);
			contentStyle.applyTo(button);
			((MBLComponent) button).setComponentSize(CHOICE_BUTTON_SIZE);
			panel.add(button, GridBagConstraints.button(++x, 0));
		}
		return panel;		
	}
	
}

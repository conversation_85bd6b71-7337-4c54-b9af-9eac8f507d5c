package heag.mbl.client.gui.components.text;

import heag.mbl.client.gui.ComponentStyle;
import heag.mbl.client.gui.MouseHover;
import heag.mbl.client.gui.componentapi.MBLComponent;
import heag.mbl.client.gui.components.MBLBasePanel;
import heag.mbl.client.gui.layout.MBLButtonSize;
import heag.mbl.client.gui.layout.MBLComponentSize;
import heag.mbl.client.gui.layout.MBLFontSize;
import heag.mbl.client.gui.layout.MBLLayout;
import heag.mbl.client.gui.layout.MBLLayoutable;

import java.awt.Color;
import java.awt.Component;
import java.awt.Font;
import java.awt.Graphics;
import java.awt.Graphics2D;
import java.awt.event.HierarchyEvent;
import java.awt.event.HierarchyListener;
import java.util.concurrent.atomic.AtomicInteger;

import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.resource.Drawable;
import ch.eisenring.core.util.GFXUtil;

/**
 * Use to represent a keyboard key
 */
@SuppressWarnings("serial")
public class MBLKeyButton extends MBLBasePanel implements MBLComponent, MBLLayoutable {

	/**
	 * This modifier is used when not connected to a keyboard
	 */
	private final static AtomicInteger DEFAULT_MODIFIER = new AtomicInteger(KeyModifier.NORMAL);
	
	private final static HierarchyListener HIERARCHY_LISTENER = new HierarchyListener() {
		@Override
		public void hierarchyChanged(final HierarchyEvent event) {
			final Component component = event.getComponent();
			if (!(component instanceof MBLKeyButton))
				return;
			final MBLKeyButton keyButton = (MBLKeyButton) component;
			Component parent = keyButton.getParent();
			while (parent != null && !(parent instanceof MBLKeyboardPanel)) {
				parent = parent.getParent();
			}
			if (parent == null) {
				keyButton.keyBoardPanel = null;
				keyButton.modifier = DEFAULT_MODIFIER;
			} else {
				final MBLKeyboardPanel keyBoardPanel = (MBLKeyboardPanel) parent;
				keyButton.keyBoardPanel = keyBoardPanel;
				keyButton.modifier = keyBoardPanel.modifier;
			}
		}
	};

	protected KeyReceiver keyReceiver;
	protected KeyFunction keyFunction;
	protected MBLFontSize fontSize = MBLFontSize.KEYBOARD_KEY;
	protected MBLComponentSize componentSize = MBLButtonSize.KEYBOARD_KEY;
	protected MBLKeyboardPanel keyBoardPanel;
	protected AtomicInteger modifier = DEFAULT_MODIFIER;
	
	public MBLKeyButton(final KeyReceiver keyReceiver, final String keyLabel) {
		this(keyReceiver, KeyFunction.create(keyLabel));
	}

	public MBLKeyButton(final KeyReceiver keyReceiver, final KeyFunction keyFunction) {
		setKeyReceiver(keyReceiver);
		deriveTinyFont();
		this.keyFunction = keyFunction;
		ComponentStyle.KEYBOARD_KEY.applyTo(this);
		setOpaque(false);
		setFocusable(false);
		addMouseListener(new MouseListenerKey(this));
		addHierarchyListener(HIERARCHY_LISTENER);
	}

	public void setKeyReceiver(final KeyReceiver keyReceiver) {
		this.keyReceiver = keyReceiver == null ? KeyReceiver.NULL : keyReceiver;
	}

	public KeyReceiver getKeyReceiver() {
		return keyReceiver;
	}

	// --------------------------------------------------------------
	// ---
	// --- MBLComponent implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public void setComponentSize(final MBLComponentSize componentSize) {
		this.componentSize = componentSize;
	}

	@Override
	public void setFontSize(final MBLFontSize fontSize) {
		this.fontSize = fontSize;
	}

	// --------------------------------------------------------------
	// ---
	// --- MBLLayoutable implementation
	// ---
	// --------------------------------------------------------------
	private Font tinyFont;
	private Color tinyColor;

	@Override
	public void layout(final MBLLayout layout) {
		layout.applySize(this, componentSize);
		layout.applyFont(this, fontSize);
		deriveTinyFont();
	}

	private void deriveTinyFont() {
		Font f = getFont();
		tinyFont = f.deriveFont(f.getSize2D() * 0.35F);
	}

	private boolean isActive() {
		return MouseHover.isHovered(this) || keyFunction.isActive(modifier.get()); 
	}

	@Override
	public void setForeground(final Color fg) {
		super.setForeground(fg);
		tinyColor = deriveTinyColor(fg);
	}

	private static Color deriveTinyColor(final Color color) {
		if (color == null)
			return null;
		int argb = color.getRGB();
		int rgb = argb & 0x00FF_FFFF;
		int a = (argb >>> 25) << 24;
		return new Color(a | rgb, true);
	}

	// --------------------------------------------------------------
	// ---
	// --- JComponent overrides
	// ---
	// --------------------------------------------------------------
	@Override
	protected void paintChildren(final Graphics g) {
		// intentionally empty
	}

	@Override
	protected void paintComponent(final Graphics g) {
		final Graphics2D g2 = MBLComponent.toGraphics2D(g, this, false);
		final Color fg = getForeground();
		final Color bg = getBackground();
		final int w = getWidth();
		final int h = getHeight();
		// determine arc size to 25% or a set minimum
		final int arcSize = GFXUtil.getArcRadius(this);
		if (isActive()) {
			g2.setColor(fg);
			g2.fillRoundRect(1, 1, w - 2, h - 2, arcSize, arcSize);
			g2.setColor(bg);
		} else {
			g2.setColor(bg);
			g2.fillRoundRect(1, 1, w - 2, h - 2, arcSize, arcSize);
			g2.setColor(fg);
		}
		g2.drawRoundRect(0, 0, w - 1, h - 1, arcSize, arcSize);
		// render the key
		final Drawable image = keyFunction.getKeyImage();
		if (image != null) {
			GFXUtil.iconBox(g2, image, 0.8F, GFXUtil.CENTER, this);
		} else {
			final int normMod = modifier.get();
			final int tinyMod = normMod == KeyModifier.NORMAL ? KeyModifier.SHIFT : KeyModifier.NORMAL;
			final String normStr = keyFunction.getString(normMod);
			final String tinyStr = keyFunction.getString(tinyMod);
			if (normStr != null)
				GFXUtil.textBox(g2, normStr, GFXUtil.CENTER, this);
			// paint the alternate key function
			if (tinyStr != null && !Strings.equals(tinyStr, normStr)) {
				if (tinyColor != null)
					g2.setColor(tinyColor);
				g2.setFont(tinyFont);
				GFXUtil.textBox(g2, tinyStr, GFXUtil.CENTER, 0, 0, w / 3, h / 3);
			}
		}
	}

}

package heag.mbl.client.gui.activity.photo;

import heag.mbl.client.gui.zoomscroll.PaintableContent;
import heag.mbl.client.gui.zoomscroll.ZoomScrollPanel;

import java.awt.Color;
import java.awt.Dimension;

@SuppressWarnings("serial")
public class PhotoViewPanel extends ZoomScrollPanel<ActivityUI> {

	MBLPhoto photo;

	public PhotoViewPanel(final ActivityUI activityUI) {
		super(activityUI);
		setBackground(Color.BLACK);
		setMinimumSize(new Dimension(128, 128));
		setPreferredSize(new Dimension(128, 128));
	}

	public void setPhoto(final MBLPhoto photo) {
		this.photo = photo;
		setContent(PaintableContent.create(photo.getImage()), ZoomScrollPanel.FIT_FULL);
		relayout();
	}

	public MBLPhoto getPhoto() {
		return photo;
	}

}

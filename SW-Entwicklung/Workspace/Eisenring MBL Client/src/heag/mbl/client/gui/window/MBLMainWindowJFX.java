package heag.mbl.client.gui.window;

import heag.mbl.client.MBLClient;
import heag.mbl.client.gui.layout.MBLLayout;
import heag.mbl.shared.MBLConstants;

import java.awt.Dimension;
import java.util.concurrent.atomic.AtomicReference;

import javafx.beans.value.ChangeListener;
import javafx.beans.value.ObservableValue;
import javafx.embed.swing.SwingNode;
import javafx.scene.Scene;
import javafx.scene.input.KeyCombination;
import javafx.scene.layout.BorderPane;
import javafx.scene.paint.Color;
import javafx.stage.Stage;
import javafx.stage.StageStyle;
import ch.eisenring.core.threading.ThreadCore;
import ch.eisenring.core.threading.ThreadPool;
import ch.eisenring.gui.jfx.FXUtil;
import ch.eisenring.gui.jfx.event.FXEventDispatcher;
import ch.eisenring.gui.util.GUIUtil;

/**
 * Implements client main window using javaFX stage.
 */
public final class MBLMainWindowJFX extends MBLMainWindow {

	private volatile boolean initialized;
	private volatile boolean isVisible;
	private Stage stage;
	private Scene scene;
	private BorderPane root;
	private SwingNode swingNode;

	// keep a reference to prevent GC
	@SuppressWarnings("unused")
	private FXEventDispatcher eventDispatcher;

	private final AtomicReference<MBLLayout> surfaceLayout = new AtomicReference<>(MBLLayout.create(960, 540));

	public MBLMainWindowJFX(final MBLClient client) {
		super(client);
		isFXMode = true;
		FXUtil.initFx();
		init();
	}

	/**
	 * Initializes the window
	 */
	private void init() {
		if (initialized)
			return;
		GUIUtil.invokeAndWaitRuntime(new Runnable() {
			@Override
			public void run() {
				mainPanel = new MBLMainPanel(MBLMainWindowJFX.this);
			}
		});
		FXUtil.invokeAndWait(new Runnable() {
			@Override
			public void run() {
				stage = new Stage(StageStyle.UNDECORATED);
				stage.setTitle(MBLConstants.APPNAME);
				stage.setFullScreen(true);
				stage.setFullScreenExitHint("");
				stage.setFullScreenExitKeyCombination(KeyCombination.NO_MATCH);
				swingNode = new SwingNode();
				swingNode.setContent(mainPanel);
				root = new BorderPane(swingNode);
				scene = new Scene(root, Color.GRAY);
				stage.setScene(scene);
				scene.widthProperty().addListener(sceneSizeListener);
				scene.heightProperty().addListener(sceneSizeListener);
				eventDispatcher = new FXEventDispatcher(swingNode);
				root.autosize();
			}
		});
		initialized = true;
	}

	/**
	 * Awaits completion of window initialization
	 */
	private void ensureInitialized() {
		while (!initialized) {
			ThreadCore.randomRetryWait();
		}
	}

	@Override
	public void setVisible(final boolean visible) {
		ensureInitialized();
		FXUtil.invokeAndWait(new Runnable() {
			@Override
			public void run() {
				MBLMainWindowJFX.this.isVisible = visible;
				if (visible) {
					stage.show();
					swingNode.toFront();
					updateLayoutJFX();
				} else {
					stage.hide();
				}
			}
		});
		if (!visible)
			return;
		ThreadPool.DEFAULT.start(new Runnable() {
			public void run() {
				ThreadCore.sleep(100);
				GUIUtil.invokeLater(updateSwingLayoutTrue);
			}
		});
	}

	@Override
	public boolean isVisible() {
		return isVisible;
	}

	// --------------------------------------------------------------
	// ---
	// --- Layout handling
	// ---
	// --------------------------------------------------------------
	private final ChangeListener<Number> sceneSizeListener = new ChangeListener<Number>() {
		@Override
		public void changed(final ObservableValue<? extends Number> observable,
				final Number oldValue, final Number newValue) {
			updateLayoutJFX();
		}
	};

	protected void updateLayoutJFX() {
		// Get DPI-aware actual pixel dimensions instead of logical dimensions
		final int w = getDPIAwareWidth();
		final int h = getDPIAwareHeight();
		if (!surfaceLayout.get().matches(w, h)) {
			// update the layout
			surfaceLayout.set(MBLLayout.create(w, h));
			GUIUtil.invokeLater(updateSwingLayoutTrue);

			// FX hack: layout again after 150 ms
//			final Runnable r = new Runnable() {
//				@Override
//				public void run() {
//					ThreadCore.sleep(150);
//					GUIUtil.invokeLater(updateSwingLayoutTrue);
//				}
//			};
//			ThreadPool.DEFAULT.start(r);
		}
	}

	/**
	 * Gets the actual pixel width, accounting for DPI scaling.
	 * Compatible with both JavaFX 8 and JavaFX 21.
	 */
	private int getDPIAwareWidth() {
		try {
			double logicalWidth = scene.widthProperty().doubleValue();
			double scaleX = getDPIScaleX();
			return (int) Math.round(logicalWidth * scaleX);
		} catch (Exception e) {
			// Fallback to logical dimensions if DPI detection fails
			return scene.widthProperty().intValue();
		}
	}

	/**
	 * Gets the actual pixel height, accounting for DPI scaling.
	 * Compatible with both JavaFX 8 and JavaFX 21.
	 */
	private int getDPIAwareHeight() {
		try {
			double logicalHeight = scene.heightProperty().doubleValue();
			double scaleY = getDPIScaleY();
			return (int) Math.round(logicalHeight * scaleY);
		} catch (Exception e) {
			// Fallback to logical dimensions if DPI detection fails
			return scene.heightProperty().intValue();
		}
	}

	/**
	 * Gets the DPI scale factor for X axis, compatible with JavaFX 8 and JavaFX 21.
	 * @return Scale factor (1.0 = no scaling, 1.5 = 150% scaling, etc.)
	 */
	private double getDPIScaleX() {
		try {
			// Try JavaFX 9+ method first
			java.lang.reflect.Method getOutputScaleX = scene.getWindow().getClass().getMethod("getOutputScaleX");
			return (Double) getOutputScaleX.invoke(scene.getWindow());
		} catch (Exception e) {
			// JavaFX 8 fallback: Use system DPI detection
			return getSystemDPIScale();
		}
	}

	/**
	 * Gets the DPI scale factor for Y axis, compatible with JavaFX 8 and JavaFX 21.
	 * @return Scale factor (1.0 = no scaling, 1.5 = 150% scaling, etc.)
	 */
	private double getDPIScaleY() {
		try {
			// Try JavaFX 9+ method first
			java.lang.reflect.Method getOutputScaleY = scene.getWindow().getClass().getMethod("getOutputScaleY");
			return (Double) getOutputScaleY.invoke(scene.getWindow());
		} catch (Exception e) {
			// JavaFX 8 fallback: Use system DPI detection
			return getSystemDPIScale();
		}
	}

	/**
	 * Gets the system DPI scale factor using AWT/Swing APIs (JavaFX 8 compatible).
	 * @return Scale factor (1.0 = no scaling, 1.5 = 150% scaling, etc.)
	 */
	private double getSystemDPIScale() {
		try {
			// Use AWT to get system DPI
			java.awt.Toolkit toolkit = java.awt.Toolkit.getDefaultToolkit();
			int dpi = toolkit.getScreenResolution();
			return dpi / 96.0; // 96 DPI is considered 100% scaling
		} catch (Exception e) {
			return 1.0; // Default to no scaling if detection fails
		}
	}

	protected void updateSwingLayout(final boolean force) {
		final MBLLayout layout = surfaceLayout.get();
		final Dimension surfaceSize = layout.getSurfaceSize();
		if (force || mainPanel.getWidth() != surfaceSize.width || mainPanel.getHeight() != surfaceSize.height) {
			mainPanel.setBounds(0, 0, surfaceSize.width, surfaceSize.height);
			mainPanel.layout(layout);
			mainPanel.validate();
		}
		GUIUtil.invokeLater(forceRelayout);
	}

	@Override
	public MBLLayout getLayout() {
		return surfaceLayout.get();
	}

	@Override
	public void iconify() {	
		setVisible(false);
		GUIUtil.invokeAndWaitRuntime(new Runnable() {
			@Override
			public void run() {
				final IconifiedFX iconified = new IconifiedFX(MBLMainWindowJFX.this);
				iconified.show();
			}
		});
	}

}

package heag.mbl.client.gui.window;

import heag.mbl.client.MBLClient;
import heag.mbl.client.gui.layout.MBLLayout;
import heag.mbl.shared.MBLConstants;

import java.awt.Dimension;
import java.util.concurrent.atomic.AtomicReference;

import javafx.beans.value.ChangeListener;
import javafx.beans.value.ObservableValue;
import javafx.embed.swing.SwingNode;
import javafx.scene.Scene;
import javafx.scene.input.KeyCombination;
import javafx.scene.layout.BorderPane;
import javafx.scene.paint.Color;
import javafx.stage.Stage;
import javafx.stage.StageStyle;
import ch.eisenring.core.threading.ThreadCore;
import ch.eisenring.core.threading.ThreadPool;
import ch.eisenring.gui.jfx.FXUtil;
import ch.eisenring.gui.jfx.event.FXEventDispatcher;
import ch.eisenring.gui.util.GUIUtil;

/**
 * Implements client main window using javaFX stage.
 */
public final class MBLMainWindowJFX extends MBLMainWindow {

	private volatile boolean initialized;
	private volatile boolean isVisible;
	private Stage stage;
	private Scene scene;
	private BorderPane root;
	private SwingNode swingNode;

	// keep a reference to prevent GC
	@SuppressWarnings("unused")
	private FXEventDispatcher eventDispatcher;

	private final AtomicReference<MBLLayout> surfaceLayout = new AtomicReference<>(MBLLayout.create(960, 540));

	public MBLMainWindowJFX(final MBLClient client) {
		super(client);
		isFXMode = true;
		FXUtil.initFx();
		init();
	}

	/**
	 * Initializes the window
	 */
	private void init() {
		if (initialized)
			return;
		GUIUtil.invokeAndWaitRuntime(new Runnable() {
			@Override
			public void run() {
				mainPanel = new MBLMainPanel(MBLMainWindowJFX.this);
			}
		});
		FXUtil.invokeAndWait(new Runnable() {
			@Override
			public void run() {
				stage = new Stage(StageStyle.UNDECORATED);
				stage.setTitle(MBLConstants.APPNAME);
				stage.setFullScreen(true);
				stage.setFullScreenExitHint("");
				stage.setFullScreenExitKeyCombination(KeyCombination.NO_MATCH);
				swingNode = new SwingNode();
				swingNode.setContent(mainPanel);
				root = new BorderPane(swingNode);
				scene = new Scene(root, Color.GRAY);
				stage.setScene(scene);
				scene.widthProperty().addListener(sceneSizeListener);
				scene.heightProperty().addListener(sceneSizeListener);
				eventDispatcher = new FXEventDispatcher(swingNode);
				root.autosize();
			}
		});
		initialized = true;
	}

	/**
	 * Awaits completion of window initialization
	 */
	private void ensureInitialized() {
		while (!initialized) {
			ThreadCore.randomRetryWait();
		}
	}

	@Override
	public void setVisible(final boolean visible) {
		ensureInitialized();
		FXUtil.invokeAndWait(new Runnable() {
			@Override
			public void run() {
				MBLMainWindowJFX.this.isVisible = visible;
				if (visible) {
					stage.show();
					swingNode.toFront();
					updateLayoutJFX();
				} else {
					stage.hide();
				}
			}
		});
		if (!visible)
			return;
		ThreadPool.DEFAULT.start(new Runnable() {
			public void run() {
				ThreadCore.sleep(100);
				GUIUtil.invokeLater(updateSwingLayoutTrue);
			}
		});
	}

	@Override
	public boolean isVisible() {
		return isVisible;
	}

	// --------------------------------------------------------------
	// ---
	// --- Layout handling
	// ---
	// --------------------------------------------------------------
	private final ChangeListener<Number> sceneSizeListener = new ChangeListener<Number>() {
		@Override
		public void changed(final ObservableValue<? extends Number> observable,
				final Number oldValue, final Number newValue) {
			updateLayoutJFX();
		}
	};

	protected void updateLayoutJFX() {
		// Use logical dimensions for layout (original behavior)
		final int w = scene.widthProperty().intValue();
		final int h = scene.heightProperty().intValue();
		if (!surfaceLayout.get().matches(w, h)) {
			// update the layout
			surfaceLayout.set(MBLLayout.create(w, h));
			GUIUtil.invokeLater(updateSwingLayoutTrue);

			// FX hack: layout again after 150 ms
//			final Runnable r = new Runnable() {
//				@Override
//				public void run() {
//					ThreadCore.sleep(150);
//					GUIUtil.invokeLater(updateSwingLayoutTrue);
//				}
//			};
//			ThreadPool.DEFAULT.start(r);
		}
	}



	protected void updateSwingLayout(final boolean force) {
		final MBLLayout layout = surfaceLayout.get();
		final Dimension surfaceSize = layout.getSurfaceSize();
		if (force || mainPanel.getWidth() != surfaceSize.width || mainPanel.getHeight() != surfaceSize.height) {
			mainPanel.setBounds(0, 0, surfaceSize.width, surfaceSize.height);
			mainPanel.layout(layout);
			mainPanel.validate();
		}
		GUIUtil.invokeLater(forceRelayout);
	}

	@Override
	public MBLLayout getLayout() {
		return surfaceLayout.get();
	}

	@Override
	public void iconify() {	
		setVisible(false);
		GUIUtil.invokeAndWaitRuntime(new Runnable() {
			@Override
			public void run() {
				final IconifiedFX iconified = new IconifiedFX(MBLMainWindowJFX.this);
				iconified.show();
			}
		});
	}

}

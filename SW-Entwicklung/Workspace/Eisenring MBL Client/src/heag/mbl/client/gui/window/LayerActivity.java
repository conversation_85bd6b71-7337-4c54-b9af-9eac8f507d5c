package heag.mbl.client.gui.window;

import heag.mbl.client.gui.layout.MBLLayout;

import java.awt.Rectangle;

import javax.swing.JComponent;

class LayerActivity extends LayerBase {

	LayerActivity(final int layerZ) {
		super(layerZ);
	}

	@Override
	public void layout(final MBLMainPanel mainPanel, final MBLLayout layout,
			final JComponent content) {
		final Rectangle bounds = mainPanel.getBounds();
		content.setBounds(bounds);
		mainPanel.applyLayout(content);
	}

}

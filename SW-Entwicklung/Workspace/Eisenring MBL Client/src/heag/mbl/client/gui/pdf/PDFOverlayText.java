package heag.mbl.client.gui.pdf;

import java.awt.Graphics2D;
import java.awt.geom.AffineTransform;
import java.awt.geom.Rectangle2D;

import org.faceless.pdf2.PDFFont;
import org.faceless.pdf2.PDFPage;
import org.faceless.pdf2.PDFStyle;
import org.faceless.pdf2.StandardFont;

import ch.eisenring.core.util.GFXUtil;

final class PDFOverlayText extends PDFOverlayBase {

	private String text;

	public PDFOverlayText(final String text, final float size) {
		super(getTextWidth(text, size), size);
		this.text = text;
	}

	@Override
	public void paintContent(final Graphics2D g, final float zoomFactorHint) {
		final Rectangle2D bounds = getBounds();
		final float w = (orientation & 1) == 0 ? getWidth() : getHeight();
		final float h = (orientation & 1) == 0 ? getHeight() : getWidth();
		final float s = Math.round((orientation & 1) == 0 ? getHeight() : getWidth());
		final AffineTransform tbak = g.getTransform();
		try {
			AffineTransform t = new AffineTransform(tbak);
			t.translate(bounds.getCenterX(), bounds.getCenterY());
			t.rotate(orientation * Math.PI * 0.5);
			t.translate(w * -0.5, h * -0.5);
			g.setTransform(t);
			g.setColor(getColor());
			g.setFont(getDefaultFont(s));
			GFXUtil.textBox(g, text, GFXUtil.CENTER, 0, 0, w, h);
		} finally {
			g.setTransform(tbak);
		}
	}

	@Override
	public void insert(final PDFPage page) {
		page.save();
		try {
			final Rectangle2D bounds = getBounds();
			float cx = toPageX((float) bounds.getCenterX(), page);
			float cy = toPageY((float) bounds.getCenterY(), page);
			float h = (orientation & 1) == 0 ? getHeight() : getWidth();
		
			final PDFFont font = new StandardFont(StandardFont.HELVETICABOLD);
			final PDFStyle s = new PDFStyle();
			s.setFillColor(getColor());
			s.setLineColor(getColor());
			s.setTextAlign(PDFStyle.TEXTALIGN_CENTER);
			s.setFont(font, h);
	
			page.seekEnd();
			page.setStyle(s);
			page.rotate(cx, cy, orientation * 90F);
			page.drawText(text, cx, cy - h * 0.35F);
		} finally {
			page.restore();
		}
	}

	@Override
	public void rotate(final boolean counter) {
		final int rotate = counter ? -1 : 1;
		orientation = (orientation + rotate) & 3;
		float w = bounds.width;
		float h = bounds.height;
		double dx = (float) this.bounds.getCenterX();
		double dy = (float) this.bounds.getCenterY();
		bounds.width = h;
		bounds.height = w;
		dx -= this.bounds.getCenterX();
		dy -= this.bounds.getCenterY();
		bounds.x += dx;
		bounds.y += dy;
	}

	// --------------------------------------------------------------
	// ---
	// --- Undo / Redo support
	// ---
	// --------------------------------------------------------------
	@Override
	public PDFOverlayText clone() {
		return (PDFOverlayText) super.clone();
	}

}

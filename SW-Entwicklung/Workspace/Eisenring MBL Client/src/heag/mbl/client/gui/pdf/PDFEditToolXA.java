package heag.mbl.client.gui.pdf;

import heag.mbl.client.resources.images.Images;
import ch.eisenring.core.resource.Drawable;

final class PDFEditToolXA extends PDFEditToolOverlay {

	@Override
	public Drawable getIcon() {
		return Images.EDIT_XA;
	}

	@Override
	public boolean isUsingTextInput() {
		return true;
	}

	@Override
	protected PDFOverlay createOverlay(final PDFEditPanel<?> editor) {
		final PDFToolPanel<?> toolPanel = editor.getToolPanel();
		final String text = toolPanel.getTextToolInput();
		PDFOverlay shape = new PDFOverlayX(15, text);
		//toolPanel.setTextToolInput("");
		return shape;
	}

	@Override
	public void toolActivated(final PDFEditPanel<?> editor) {
		editor.getToolPanel().setSelectedColor(PDFToolPanel.ORANGE);
	}

}

package heag.mbl.client.gui.dialog;

import heag.mbl.client.MBLClient;
import heag.mbl.client.action.MBLAction;
import heag.mbl.client.gui.ComponentStyle;
import heag.mbl.client.gui.activity.MBLActivityStack;
import heag.mbl.client.gui.components.MBLBasePanel;
import heag.mbl.client.gui.components.MBLIcon;
import heag.mbl.client.gui.components.MBLImageButton;
import heag.mbl.client.gui.components.MBLLabel;
import heag.mbl.client.gui.layout.MBLButtonSize;
import heag.mbl.client.gui.layout.MBLFontSize;
import heag.mbl.client.resources.images.Images;

import java.awt.GridBagLayout;
import java.util.concurrent.atomic.AtomicReference;

import javax.swing.JComponent;

import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.resource.Drawable;
import ch.eisenring.core.resource.ImageResource;
import ch.eisenring.gui.BorderLayout;
import ch.eisenring.gui.GridBagConstraints;
import ch.eisenring.gui.action.AbstractAction;
import ch.eisenring.gui.util.GUIUtil;

public abstract class MBLDialogBuilderBase {

	protected final MBLClient client;

	MBLDialogBuilderBase(final MBLClient client) {
		this.client = client;
	}


	// --------------------------------------------------------------
	// ---
	// --- Dialog button utility methods
	// ---
	// --------------------------------------------------------------
	protected ComponentStyle buttonStyle = ComponentStyle.DIALOG_BUTTONS;

	protected JComponent createButton(final MBLDialog dialog, final AbstractAction action) {
		MBLImageButton button;
		if (action instanceof MBLDialogAction) {
			button = new MBLImageButton(action);
		} else {
			button = new MBLImageButton(new MBLDialogAction(dialog, action));			
		}
		buttonStyle.applyTo(button);
		return button;
	}
	
	// --------------------------------------------------------------
	// ---
	// --- Dialog action management
	// ---
	// --------------------------------------------------------------
	protected final List<AbstractAction> actions = new ArrayList<>();

	/**
	 * Adds an action to this builder
	 */
	public void addAction(final AbstractAction action) {
		if (action != null && !actions.contains(action))
			actions.add(action);
	}

	/**
	 * Adds an "Exit Program" action to this builder
	 */
	public void addExitAction() {
		final MBLAction action = new MBLAction(client, "Beenden", Images.DIALOG_CANCEL) {
			@Override
			protected void performAction() {
				client.closeClient();
			}
		};
		addAction(action);
	}

	/**
	 * Adds a close (NO-OP) action to this builder
	 * (it closes the dialog, but does nothing else)
	 */
	public void addCloseAction(final String label, final ImageResource icon) {
		final MBLAction action = new MBLAction(client,
				Strings.isEmpty(label) ? "Ok" : label,
				icon == null ? Images.DIALOG_APPLY : icon) {
			@Override
			protected void performAction() {
				// does nothing
			}
		};
		addAction(action);
	}

	// --------------------------------------------------------------
	// ---
	// --- Dialog title component
	// ---
	// --------------------------------------------------------------
	protected ComponentStyle titleStyle = ComponentStyle.DIALOG_TITLE;
	protected Drawable titleIcon = Images.COG;
	protected String title = "Kein Titel";
	protected boolean titleCloseable;

	/**
	 * Sets the dialogs title
	 */
	public void setTitle(final CharSequence title) {
		this.title = Strings.toString(title);
	}

	/**
	 * Sets the title icon
	 */
	public void setTitleIcon(final Drawable titleIcon) {
		this.titleIcon = titleIcon;
	}

	/**
	 * Selects if the title contains a close button
	 * (false by default)
	 */
	public void setTitleCloseable(final boolean closeable) {
		this.titleCloseable = closeable;
	}

	protected JComponent getTitlePanel(final MBLDialog dialog) throws IllegalStateException {
		final JComponent panel = new MBLBasePanel();
		titleStyle.applyTo(panel);
		int x = -1;
		if (titleIcon != null) {
			panel.setLayout(new GridBagLayout());
			final MBLIcon icon = new MBLIcon(titleIcon);
			icon.setSize(MBLButtonSize.DIALOG_TITLE, MBLFontSize.DIALOG_TITLE);
			titleStyle.applyTo(icon);
			panel.add(icon, GridBagConstraints.button(++x, 0));
		}
		if (title != null) {
			final MBLLabel label = new MBLLabel(title);
			label.setSize(MBLButtonSize.DIALOG_TITLE, MBLFontSize.DIALOG_TITLE);
			titleStyle.applyTo(label);
			panel.add(label, GridBagConstraints.field(++x, 0));
		}
		if (titleCloseable) {
			final AbstractAction closeAction = new MBLAction(client, Images.DIALOG_CANCEL) {
				@Override
				protected void performAction() {
					// no action required
				}
			};
			final JComponent button = createButton(dialog, closeAction);
			panel.add(button, GridBagConstraints.button(++x, 0));
		}
		return panel;
	}

	protected MBLDialogAction createDialogAction(final MBLDialog dialog, final AbstractAction targetAction) {
		return new MBLDialogAction(dialog, targetAction);
	}

	// --------------------------------------------------------------
	// ---
	// --- Main panel component
	// ---
	// --------------------------------------------------------------
	protected ComponentStyle contentStyle = ComponentStyle.DIALOG_MAIN;

	protected abstract JComponent getMainPanel(final MBLDialog dialog) throws IllegalStateException;

	// --------------------------------------------------------------
	// ---
	// --- Button panel component
	// ---
	// --------------------------------------------------------------
	protected abstract JComponent getButtonPanel(final MBLDialog dialog) throws IllegalStateException;

	// --------------------------------------------------------------
	// ---
	// --- Show dialog
	// ---
	// --------------------------------------------------------------
	/**
	 * Builds dialog using current settings and makes the dialog visible.
	 * 
	 * May throw if the current configuration is invalid.
	 */
	public MBLDialog showDialog() throws IllegalStateException {
		if (GUIUtil.isEventDispatchThread())
			return showDialogImpl();
		final AtomicReference<Object> result = new AtomicReference<>();
		GUIUtil.invokeAndWaitRuntime(() -> {
			try {
				result.set(showDialogImpl());
			} catch (final Throwable t) {
				result.set(t);
			}
		});
		Object o = result.get();
		if (o instanceof MBLDialog)
			return (MBLDialog) o;
		if (o instanceof Throwable) 
			throw (IllegalStateException) o;
		return null;
	}

	private MBLDialog showDialogImpl() throws IllegalStateException {
		final MBLActivityStack activityStack = client.getActivityStack();
		final MBLBasePanel dialogPanel = new MBLBasePanel();
		final MBLDialog dialog = new MBLDialog(activityStack, dialogPanel);
		dialogPanel.setLayout(new BorderLayout());
		final JComponent titlePanel = getTitlePanel(dialog);
		if (titlePanel != null)
			dialogPanel.add(titlePanel, BorderLayout.NORTH);
		final JComponent mainPanel = getMainPanel(dialog);
		if (mainPanel != null)
			dialogPanel.add(mainPanel, BorderLayout.CENTER);
		final JComponent buttonPanel = getButtonPanel(dialog);
		if (buttonPanel != null)
			dialogPanel.add(buttonPanel, BorderLayout.SOUTH);
		dialog.setVisible(true);
		return dialog;
	}

}

package heag.mbl.client.gui.componentapi;

public final class KeyboardSwitchMode {

	public final static KeyboardSwitchMode NO_KEYBOARD = new KeyboardSwitchMode(KeyboardType.NONE);
	public final static KeyboardSwitchMode ONLY_FULL = new KeyboardSwitchMode(KeyboardType.FULL);
	public final static KeyboardSwitchMode ONLY_NUMPAD = new KeyboardSwitchMode(KeyboardType.NUMPAD);

	public final static KeyboardSwitchMode PREFER_FULL = new KeyboardSwitchMode(KeyboardType.FULL, KeyboardType.NUMPAD);
	public final static KeyboardSwitchMode PREFER_NUMPAD = new KeyboardSwitchMode(KeyboardType.NUMPAD, KeyboardType.FULL);

	private final KeyboardType[] allowedTypes;

	private KeyboardSwitchMode(final KeyboardType defaultType) {
		allowedTypes = new KeyboardType[] { defaultType };
	}

	private KeyboardSwitchMode(final KeyboardType defaultType, final KeyboardType alternateType) {
		allowedTypes = new KeyboardType[] { defaultType, alternateType };
	}

	// --------------------------------------------------------------
	// ---
	// ---
	// ---
	// --------------------------------------------------------------
	/**
	 * Gets the keyboard type to be shown by default
	 */
	public KeyboardType getDefaultKeyboardType() {
		return allowedTypes[0];
	}

	public boolean isSwitchableTo(final KeyboardType type) {
		for (final KeyboardType allowedType : this.allowedTypes) {
			if (allowedType == type)
				return true;
		}
		return false;
	}

}

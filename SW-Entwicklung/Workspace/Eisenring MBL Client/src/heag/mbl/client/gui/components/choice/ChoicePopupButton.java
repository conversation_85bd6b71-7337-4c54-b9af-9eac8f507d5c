package heag.mbl.client.gui.components.choice;

import heag.mbl.client.gui.componentapi.MBLChoiceComponent;
import heag.mbl.client.gui.componentapi.MBLComponent;
import heag.mbl.client.gui.componentapi.MBLPopup;
import heag.mbl.client.gui.components.MBLBasePanel;
import heag.mbl.client.gui.components.MBLRenderingTarget;
import heag.mbl.client.gui.window.Layer;
import heag.mbl.client.resources.images.Images;

import java.awt.Dimension;
import java.awt.Graphics;

import javax.swing.ListCellRenderer;
import javax.swing.ListModel;

import ch.eisenring.core.util.GFXUtil;

@SuppressWarnings("serial")
class ChoicePopupButton<T> extends MBLBasePanel implements MBLChoiceComponent<T> {

	protected final MBLChoiceComponent<T> choiceField;

	public ChoicePopupButton(final MBLChoiceComponent<T> choiceField) {
		this.choiceField = choiceField;
		addMouseListener(SHOW_POPUP_MOUSELISTENER);
	}

	@Override
	public Dimension getPreferredSize() {
		return new Dimension(8, 8);
	}

	@Override
	protected void paintChildren(Graphics g) {
		// intentionally empty
	}

	@Override
	protected void paintComponent(final Graphics g) {
		MBLComponent.toGraphics2D(g, this, true);
		final MBLRenderingTarget target = MBLRenderingTarget.create(g, this, false);
		// render the drop-down icon on top
		GFXUtil.iconBox(target.getGraphics(), Images.CHOICE, 1F, GFXUtil.CENTER, target.getComponent());
	}

	// --------------------------------------------------------------
	// ---
	// --- FocusManaged implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public MBLPopup getPopup() {
		return choiceField.getPopup();
	}

	@Override
	public Layer getPopupLayer() {
		return choiceField.getPopupLayer();
	}

	@Override
	public Class<?> getValueClass() {
		return choiceField.getValueClass();
	}

	@Override
	public Object getValue() {
		return choiceField.getValue();
	}

	@Override
	public void setValue(final Object value) {
		choiceField.setValue(value);
	}

	@Override
	public ListModel<T> getModel() {
		return choiceField.getModel();
	}

	@Override
	public void setModel(final ListModel<T> model) {
		choiceField.setModel(model);
	}

	@Override
	public void setSelectedItem(final T item) {
		choiceField.setSelectedItem(item);
	}

	@Override
	public T getSelectedItem() {
		return choiceField.getSelectedItem();
	}

	@Override
	public boolean isValidItem(final Object item) {
		return choiceField.isValidItem(item);
	}

	@Override
	public ListCellRenderer<T> getRenderer() {
		return choiceField.getRenderer();
	}

	@Override
	public Dimension getPreferredItemSize() {
		return choiceField.getPreferredItemSize();
	}
	
}
package heag.mbl.client.gui.components.choice;

import heag.mbl.client.gui.MouseHover;
import heag.mbl.client.gui.componentapi.MBLChoiceComponent;
import heag.mbl.client.gui.components.MBLBasePanel;
import heag.mbl.client.gui.components.MouseListenerBase;

import java.awt.Component;
import java.awt.Graphics;
import java.awt.event.MouseEvent;
import java.awt.event.MouseListener;

import javax.swing.ListCellRenderer;

@SuppressWarnings("serial")
class ChoiceItem extends MBLBasePanel {

	private final static MouseListener MOUSE_LISTENER = new MouseListenerBase<Component>(null) {
		@Override
		protected void onClick(MouseEvent event) {
			try {
				final ChoiceItem item = (ChoiceItem) event.getComponent();
				item.popup.select(item.item);
			} catch (final Exception e) {
				// ignore
			}
		}
	};
	
	
	final ChoicePopup popup;
	final MBLChoiceComponent<?> choiceField;
	final Object item;
	final int itemIndex;
	final boolean selected;
	
	public ChoiceItem(final ChoicePopup popup,
			final Object item, final int itemIndex, final boolean selected) {
		this.popup = popup;
		this.choiceField = (MBLChoiceComponent<?>) popup.getOwnerComponent();
		this.item = item;
		this.itemIndex = itemIndex;
		this.selected = selected;
		addMouseListener(MOUSE_LISTENER);
	}

	// --------------------------------------------------------------
	// ---
	// --- JComponent overrides
	// ---
	// --------------------------------------------------------------
	@Override
	protected void paintChildren(Graphics g) {
		// intentionally empty
	}

	@SuppressWarnings("unchecked")
	@Override
	protected void paintComponent(final Graphics g) {
		final ListCellRenderer<Object> renderer = (ListCellRenderer<Object>) choiceField.getRenderer();
		final boolean hovered = MouseHover.isHovered(this);
		final Component component = renderer.getListCellRendererComponent(popup.jList, item, itemIndex, selected, hovered);
		component.setBounds(getBounds());
		component.paint(g);
	}

}

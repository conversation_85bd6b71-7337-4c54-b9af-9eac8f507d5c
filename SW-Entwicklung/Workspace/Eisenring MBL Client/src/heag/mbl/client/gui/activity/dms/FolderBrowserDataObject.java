package heag.mbl.client.gui.activity.dms;

import heag.mbl.client.gui.activity.ActivityDataObject;
import ch.eisenring.dms.service.api.DMSObject;
import ch.eisenring.dms.shared.model.api.DMSObjectCache;

final class FolderBrowserDataObject extends ActivityDataObject {

	private transient DMSObjectCache objectCache;
	
	public void setObjects(final DMSObjectCache objects) {
		objectCache = objects;
	}

	/**
	 * Gets the current object set
	 */
	public DMSObjectCache getObjects() {
		return objectCache;
	}

	/**
	 * Gets the root object of the object hierarchy
	 */
	public DMSObject getRoot() {
		final DMSObjectCache cache = getObjects();
		DMSObject root = null;
		if (cache != null) {
			final DMSObject[] roots = cache.getRoots();
			if (roots != null && roots.length > 0) {
				root = roots[0];
			}
		}
		return root;
	}

}

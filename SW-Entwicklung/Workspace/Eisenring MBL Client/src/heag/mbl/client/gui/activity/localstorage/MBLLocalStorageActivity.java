package heag.mbl.client.gui.activity.localstorage;

import heag.mbl.client.MBLClient;
import heag.mbl.client.gui.activity.MBLActivity;
import heag.mbl.client.gui.activity.MBLActivityBase;
import heag.mbl.client.gui.activity.MBLActivityFactory;
import heag.mbl.client.gui.activity.MBLActivityPanel;
import heag.mbl.client.gui.activity.MBLActivityProperties;
import heag.mbl.client.gui.activity.MBLActivityTerminationCode;
import heag.mbl.client.gui.activity.MBLActivityType;
import heag.mbl.client.gui.activity.dms.selectproject.DMSSelectProjectActivity;
import heag.mbl.client.gui.location.LocationLabel;
import heag.mbl.client.network.MBLOperationMode;
import heag.mbl.client.offline.DataAccessHandler;
import heag.mbl.shared.codetables.MBLRightCode;
import heag.mbl.shared.network.obj.MBLSyncItemSet;
import ch.eisenring.app.shared.CoreTickListener;
import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.datatypes.primitives.Primitives;
import ch.eisenring.dms.service.api.DMSObject;
import ch.eisenring.gui.util.GUIUtil;

public class MBLLocalStorageActivity extends MBLActivityBase {

	public final static MBLActivityFactory FACTORY = new MBLActivityFactory(
			Constants.ACTIVITY_LABEL, Constants.ACTIVITY_ICON, MBLActivityType.WORKFLOW, MBLRightCode.LOCALSTORAGE) {
		@Override
		protected MBLActivity createImpl(final MBLClient client) {
			final MBLLocalStorageActivity activity = new MBLLocalStorageActivity(client);
			return activity;
		}
	};

	public MBLLocalStorageActivity(final MBLClient client) {
		super(client);
		setPropertyDefault(MBLActivityProperties.SUSPENDABLE, Boolean.FALSE);
		setPropertyDefault(MBLActivityProperties.SAVEABLE, Boolean.TRUE);
		setPropertyDefault(MBLActivityProperties.CANCELABLE, Boolean.FALSE);
		setPropertyDefault(MBLActivityProperties.SYMBOL_ICON, Constants.ACTIVITY_ICON);
		setPropertyDefault(MBLActivityProperties.LOCATION, Constants.ACTIVITY_LABEL_LONG);
		// this activity is always in mode CLIENT
		setPropertyDefault(MBLActivityProperties.OPERATION_MODE, MBLOperationMode.CLIENT); 
	}

	// --------------------------------------------------------------
	// ---
	// --- Data object handling
	// ---
	// --------------------------------------------------------------
	@Override
	protected DataObject createDataObject() {
		final DataObject data = new DataObject();
		final DataAccessHandler dataHandler = getClient().getDataHandler();
		final MBLSyncItemSet itemSet = dataHandler.getSyncItemSet().clone();
		data.setSyncItemSet(itemSet);
		return data;
	}

	@Override
	public DataObject getDataObject() {
		return (DataObject) super.getDataObject();
	}

	// --------------------------------------------------------------
	// ---
	// --- Start/Stop processing
	// ---
	// --------------------------------------------------------------
	@Override
	protected boolean isModifiedImpl() {
		if (super.isModifiedImpl())
			return true;
		if (contentPanel == null)
			return false;
		return contentPanel.isModified();
	}

	@Override
	public void saveActivity() {
		final SaveActivityTask task = new SaveActivityTask(this);
		task.doSave();
	}

	private final CoreTickListener tickListener = new CoreTickListener() {
		@Override
		public void tick(final long now) {
			GUIUtil.invokeLater(() -> {
				final MBLActivityPanel mainPanel = getUserInterface();
				final ActivityUI ui = (ActivityUI) mainPanel.getContentPanel();
				ui.updateView();
			});
		}
		
		@Override
		public String getName() {
			return Primitives.getSimpleName(MBLLocalStorageActivity.this.getClass());
		}
	};

	@Override
	public void activityStarted() {
		super.activityStarted();
		getClient().getCore().addTickListener(tickListener);
	}
	

	@Override
	public void activityStopped() {
		super.activityStopped();
		getClient().getCore().removeTickListener(tickListener);
	}

	@Override
	public void childActivityStopped(final MBLActivity childActivity) {
		//final MBLActivityTerminationCode terminationCode = childActivity.getTerminationCode();
		if (childActivity instanceof DMSSelectProjectActivity) {
			childActivityStopped((DMSSelectProjectActivity) childActivity);
		} else {
			setFatal(new ErrorMessage("Invalid Child activity"));
		}
	}

	private void childActivityStopped(final DMSSelectProjectActivity childActivity) {
		if (!MBLActivityTerminationCode.SAVED.equals(childActivity.getTerminationCode()))
			return;
		final DMSObject dmsObject = childActivity.getSelectedObject();
		final AddSyncItemTask adder = new AddSyncItemTask(this);
		adder.addSyncItem(dmsObject);
	}

	// --------------------------------------------------------------
	// ---
	// --- State handling
	// ---
	// --------------------------------------------------------------

	// none

	// --------------------------------------------------------------
	// ---
	// --- User interface
	// ---
	// --------------------------------------------------------------
	protected ActivityUI contentPanel;

	@Override
	protected LocationLabel createLocationLabel() {
		return LocationLabel.create(this, Constants.ACTIVITY_LABEL);
	}

	@Override
	protected MBLActivityPanel createUserInterface() {
		contentPanel = new ActivityUI(this);
		final MBLActivityPanel activityPanel = new MBLActivityPanel(this);
		activityPanel.setContentPanel(contentPanel);
		return activityPanel;
	}

}

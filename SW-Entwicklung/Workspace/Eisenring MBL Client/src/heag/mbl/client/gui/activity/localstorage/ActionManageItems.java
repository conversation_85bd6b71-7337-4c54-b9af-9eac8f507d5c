package heag.mbl.client.gui.activity.localstorage;

import heag.mbl.client.action.MBLAction;
import heag.mbl.client.gui.activity.MBLActivityProperties;
import heag.mbl.client.resources.images.Images;

class ActionManageItems extends MBLAction {

	public ActionManageItems(final ActivityUI activityUI) {
		super(activityUI, Images.SETTINGS, "Verwalten");
	}

	@Override
	protected void performAction() {
		getActivity().setProperty(MBLActivityProperties.UI_STATE, UIStates.MANAGE_SYNC_ITEMS);
	}

}

package heag.mbl.client.gui.components.choice;

import heag.mbl.client.gui.ComponentStyle;
import heag.mbl.client.gui.componentapi.MBLComponent;
import heag.mbl.client.gui.componentapi.MBLPopup;
import heag.mbl.client.gui.components.MBLBasePanel;
import heag.mbl.client.gui.components.MBLImageButton;
import heag.mbl.client.gui.dialog.KeyboardActionHide;

import java.awt.GridBagLayout;

import ch.eisenring.gui.GridBagConstraints;

@SuppressWarnings("serial")
final class ChoiceTitlePanel extends MBLBasePanel {

	private final MBLImageButton btnHide = new MBLImageButton(new KeyboardActionHide());

	public ChoiceTitlePanel(final MBLPopup popup) {
		super(new GridBagLayout());
		initComponents();
		initLayout();
	}

	private void initComponents() {
		
	}

	private void initLayout() {
		add(btnHide, GridBagConstraints.fixed(0, 0).anchor(GridBagConstraints.EAST).weightX(1).insets(0));
	}

	// --------------------------------------------------------------
	// ---
	// --- MBLComponent overrides
	// ---
	// --------------------------------------------------------------
	@Override
	public void setStyle(final ComponentStyle style) {
		super.setStyle(style);
		style.applyTo(btnHide);
	}

}

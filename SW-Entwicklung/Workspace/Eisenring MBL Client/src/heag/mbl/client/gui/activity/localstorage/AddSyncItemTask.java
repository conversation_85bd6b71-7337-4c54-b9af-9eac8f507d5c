package heag.mbl.client.gui.activity.localstorage;

import heag.mbl.shared.network.obj.MBLSyncItem;
import heag.mbl.shared.network.wan.PacketObjectPropertiesReply;
import heag.mbl.shared.network.wan.PacketObjectPropertiesRequest;
import ch.eisenring.app.shared.network.RPCContext;
import ch.eisenring.app.shared.network.RPCHandlerEDT;
import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.codetable.MessageClassCode;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.dms.service.api.DMSObject;
import ch.eisenring.dms.service.codetables.DMSPropertyCode;
import ch.eisenring.dms.shared.model.api.DMSPropertyAccessor;

final class AddSyncItemTask implements RPCHandlerEDT {

	final MBLLocalStorageActivity activity;
	
	public AddSyncItemTask(final MBLLocalStorageActivity activity) {
		this.activity = activity;
	}

	public void addSyncItem(final DMSObject object) {
		final PacketObjectPropertiesRequest request = PacketObjectPropertiesRequest.create(object.getPKValue());
		activity.getClient().sendPacket(this, request);
	}

	@Override
	public void requestSent(final RPCContext rpcContext) {
		activity.setBusy("Objekt wird hinzugefügt...", false);
	}

	@Override
	public void timeoutOccured(final RPCContext rpcContext) {
		activity.setError(ErrorMessage.TIMEOUT, UIStates.MANAGE_SYNC_ITEMS);
	}

	@Override
	public void replyReceived(final RPCContext rpcContext) {
		final PacketObjectPropertiesReply reply = (PacketObjectPropertiesReply) rpcContext.getReply();
		if (reply.isValid()) {
			// add the item
			final DMSPropertyAccessor accessor = reply.getAccessor();
			final String basisNummer = Strings.toString(
					accessor.getPropertyValue(DMSPropertyCode.BASEPROJECTNUMBER));
			if (Strings.isEmpty(basisNummer)) {
				activity.setError(new ErrorMessage("Objekt konnte nicht hinzugefügt werden"),
						UIStates.MANAGE_SYNC_ITEMS);
			} else {
				final String user = activity.getClient().getLoginName();
				final String name = accessor.getObject().getObjectname();
				final MBLSyncItem syncItem = MBLSyncItem.create(user, basisNummer, name);
				final DataObject dataObject = activity.getDataObject();
				if (dataObject.getSyncItemSet().add(syncItem)) {
					// added
					activity.setUIState(UIStates.MANAGE_SYNC_ITEMS);
				} else {
					// already there
					activity.setError(new ErrorMessage(MessageClassCode.WARN,
							"Dieses Objekt ist bereits zur Synchronisation ausgewählt"), UIStates.MANAGE_SYNC_ITEMS);
				}
			}
		} else {
			activity.setError(reply.getMessage(), UIStates.MANAGE_SYNC_ITEMS);
		}
	}

	
}

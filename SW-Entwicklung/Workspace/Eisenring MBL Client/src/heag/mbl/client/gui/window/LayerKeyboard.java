package heag.mbl.client.gui.window;

import heag.mbl.client.gui.componentapi.MBLFocusManaged;
import heag.mbl.client.gui.componentapi.MBLPopup;
import heag.mbl.client.gui.layout.MBLLayout;

import java.awt.Dimension;
import java.awt.Rectangle;

import javax.swing.JComponent;

import ch.eisenring.gui.util.GUIUtil;

final class LayerKeyboard implements Layer {

	@Override
	public int getLayerZ() {
		return 0x7FFF_FFFF;
	}

	@Override
	public boolean isSensitive() {
		return true;
	}

	@Override
	public void layout(final MBLMainPanel mainPanel, final MBLLayout layout,
			final JComponent keyBoard) {
		mainPanel.applyLayout(keyBoard);
		final Rectangle areaBounds = layout.getBounds();
		final Dimension size = keyBoard.getMinimumSize();
		final int x = ((areaBounds.width - size.width) >> 1) + areaBounds.x;
		final int y = ((areaBounds.height + areaBounds.y) - size.height);
		final Rectangle keyBounds = new Rectangle(x, y, size.width, size.height);
		if (keyBounds.width > areaBounds.width) {
			keyBounds.width = areaBounds.width;
			keyBounds.x = areaBounds.x;
		}
		if (keyBounds.height > areaBounds.height) {
			keyBounds.height = areaBounds.height;
			keyBounds.y = areaBounds.y;
		}
		// get the component this keyboard belongs to (so we can avoid overlapping it)
		JComponent textComponent = null;
		if (keyBoard instanceof MBLPopup) {
			MBLFocusManaged owner = ((MBLPopup) keyBoard).getOwnerComponent();
			if (owner != null)
				textComponent = owner.getSwingComponent();
		}
		if (textComponent != null) {
			Rectangle compBounds = GUIUtil.getBoundsIn(textComponent, mainPanel);
			boolean intersects = compBounds.intersects(keyBounds);
			if (intersects) {
				// keyboard overlaps text component
				keyBounds.y = 0;
			}
		}
		keyBoard.setBounds(keyBounds);
	}

	@Override
	public void componentAdded(final MBLMainPanel mainPanel, final MBLLayout layout, final JComponent component) {
		// ensure popup is hidden
		mainPanel.setLayerComponent(null, Layer.POPUP);
	}

}

package heag.mbl.client.gui.window;

import heag.mbl.client.MBLClient;
import heag.mbl.client.gui.layout.MBLLayout;
import heag.mbl.shared.MBLConstants;

import java.awt.Dimension;
import java.awt.event.ComponentAdapter;
import java.awt.event.ComponentEvent;
import java.awt.event.ComponentListener;
import java.awt.event.WindowAdapter;
import java.awt.event.WindowEvent;
import java.awt.event.WindowListener;
import java.util.concurrent.atomic.AtomicReference;

import javax.swing.JFrame;

import ch.eisenring.commons.resource.images.Images;
import ch.eisenring.core.threading.ThreadCore;
import ch.eisenring.gui.BorderLayout;
import ch.eisenring.gui.util.GUIUtil;

/**
 * Implements client main window using javaFX stage.
 */
public final class MBLMainWindowJFrame extends MBLMainWindow {

	private volatile boolean initialized;
	private JFrame frame;
	
	private final AtomicReference<MBLLayout> surfaceLayout = new AtomicReference<>(MBLLayout.create(960, 540));

	public MBLMainWindowJFrame(final MBLClient client) {
		super(client);
		isFXMode = false;
		init();
	}

	/**
	 * Initializes the window
	 */
	private void init() {
		if (initialized)
			return;
		GUIUtil.invokeAndWaitSilent(new Runnable() {
			@Override
			public void run() {
				frame = new JFrame(MBLConstants.WINDOW_TITLE);
				frame.setMinimumSize(new Dimension(640, 480));
				frame.setLayout(new BorderLayout());
				frame.addWindowListener(windowListener);
				mainPanel = new MBLMainPanel(MBLMainWindowJFrame.this);
				frame.add(mainPanel, BorderLayout.CENTER);
				frame.pack();
				frame.setBounds(0, 0, 1920, 1080);
				frame.setIconImages(Images.MBL.getImageList());
				mainPanel.addComponentListener(componentListener);
			}
		});
		initialized = true;
	}

	/**
	 * Awaits completion of window initialization
	 */
	private void ensureInitialized() {
		while (!initialized) {
			ThreadCore.randomRetryWait();
		}
	}

	@Override
	public void setVisible(final boolean visible) {
		ensureInitialized();
		GUIUtil.invokeAndWaitSilent(new Runnable() {
			@Override
			public void run() {
				frame.setVisible(visible);
			}
		});
		GUIUtil.invokeLater(updateSwingLayoutTrue);
	}

	@Override
	public boolean isVisible() {
		return frame.isVisible();
	}

	// --------------------------------------------------------------
	// ---
	// --- Layout handling
	// ---
	// --------------------------------------------------------------
	private final WindowListener windowListener = new WindowAdapter() {
		@Override
		public void windowClosing(final WindowEvent event) {
			windowClosed(event);
		}
		
		@Override
		public void windowClosed(final WindowEvent event) {
			client.closeClient();
		}
	};

	private final ComponentListener componentListener = new ComponentAdapter() {
		@Override
		public void componentResized(final ComponentEvent event) {
			updateSwingLayout(true);
		}
	};

	@Override
	protected void updateSwingLayout(final boolean force) {
		final int w = mainPanel.getWidth();
		final int h = mainPanel.getHeight();
		if (force || !surfaceLayout.get().matches(w, h)) {
			// update the layout
			surfaceLayout.set(MBLLayout.create(w, h));
			mainPanel.layout(surfaceLayout.get());
			mainPanel.invalidate();
			mainPanel.repaint();
		}
	}

	@Override
	public MBLLayout getLayout() {
		return surfaceLayout.get();
	}

	@Override
	public void iconify() {
		frame.setState(JFrame.ICONIFIED);
	}

}

package heag.mbl.client.gui.components;

import heag.mbl.client.gui.layout.MBLButtonSize;
import heag.mbl.client.gui.layout.MBLFontSize;
import heag.mbl.client.gui.layout.MBLLayout;
import heag.mbl.client.gui.layout.MBLLayoutable;

import java.awt.Dimension;

import ch.eisenring.gui.components.HEAGCheckBox;
import ch.eisenring.gui.resource.images.Images;

@SuppressWarnings("serial")
public class MBLCheckBox extends HEAGCheckBox implements MBLLayoutable {

	public MBLCheckBox() {
		initComponent();
	}

	public MBLCheckBox(final String label) {
		super(label);
		initComponent();
	}

	private void initComponent() {
		setFocusable(false);
	}

	@Override
	public void layout(final MBLLayout layout) {
		setFont(layout.getFont(MBLFontSize.TEXT));
		final Dimension size = layout.getPreferredSize(MBLButtonSize.CHECKBOX);
		setIcon(Images.CHECKBOX_FALSE.getIcon(size.width));
		setSelectedIcon(Images.CHECKBOX_TRUE_ENABLED.getIcon(size.width));
	}
	
}

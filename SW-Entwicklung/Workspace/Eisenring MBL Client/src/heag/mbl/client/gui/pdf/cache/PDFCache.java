package heag.mbl.client.gui.pdf.cache;

import java.awt.Image;
import java.io.InputStream;
import java.lang.ref.SoftReference;

import org.faceless.pdf2.PDF;
import org.faceless.pdf2.PDFPage;
import org.faceless.pdf2.PDFParser;
import org.faceless.pdf2.PDFReader;
import org.faceless.pdf2.PagePainter;

import ch.eisenring.core.collections.Map;
import ch.eisenring.core.collections.Set;
import ch.eisenring.core.collections.impl.HashMap;
import ch.eisenring.core.collections.impl.HashSet;
import ch.eisenring.core.io.Streams;
import ch.eisenring.core.io.binary.BinaryHolder;
import ch.eisenring.core.resource.image.ImageUtil;
import ch.eisenring.core.threading.ThreadPool;

/**
 * Cache for PDF derived data to speed up editing
 */
public class PDFCache {

	final static DPISpec DPI_THUMB = new DPISpec(-1, true);
	final static DPISpec DPI_SMALL = new DPISpec(72, false);
	final static DPISpec DPI_MEDIUM = new DPISpec(144, true);
	final static DPISpec DPI_LARGE = new DPISpec(288, false);

	final static boolean USE_LEVEL_OF_DETAIL = true;

	private BinaryHolder binaryData;

	/**
	 * Cache locking object
	 */
	public final Object getLock() {
		return this;
	}

	public PDFCache() {
	}

	public boolean setBinaryData(final BinaryHolder binaryHolder) {
		final boolean changed;
		synchronized (getLock()) {
			if (changed = this.binaryData != binaryHolder) {
				this.binaryData = binaryHolder;
				// flush all cached data
				pdf = null;
				pageCache.clear();
				pageImages.clear();
				pagePainters.clear();
				thumbnails.clear();
			}
		}
		return changed;
	}

	// --------------------------------------------------------------
	// ---
	// --- PDF cache
	// ---
	// --------------------------------------------------------------
	private PDF pdf;

	/**
	 * Gets the PDF for this cache
	 */
	public PDF getPDF() throws Exception {
		PDF result;
		synchronized (getLock()) {
			result = this.pdf;
			if (result == null) {
				// read/parse the original
				InputStream input = null;
				PDFReader reader = null;
				try {
					input = binaryData.getInputStream();
					reader = new PDFReader(input);
					reader.load();
					result = new PDF(reader);
				} finally {
					Streams.closeSilent(input);
				}
				this.pdf = result;
			}
		}
		return result;
	}

	// --------------------------------------------------------------
	// ---
	// --- Page cache
	// ---
	// --------------------------------------------------------------
	private final Map<Integer, PDFPage> pageCache = new HashMap<>();

	public PDFPage getPage(final int pageNumber) throws Exception {
		final Integer key = Integer.valueOf(pageNumber);
		PDFPage page;
		synchronized (getLock()) {
			if ((page = pageCache.get(key)) == null) {
				page = getPDF().getPage(key);
				pageCache.put(key, page);
			}
		}
		return page;
	}

	// --------------------------------------------------------------
	// ---
	// --- PagePainter cache
	// ---
	// --------------------------------------------------------------
	private final Map<Integer, PagePainter> pagePainters = new HashMap<>();

	/**
	 * Gets a PagePainter for the given page
	 */
	public PagePainter getPagePainter(final int pageNumber) throws Exception {
		final Integer key = Integer.valueOf(pageNumber);
		PagePainter result;
		synchronized (getLock()) {
			result = pagePainters.get(key);
			if (result == null) {
				final PDFPage page = getPage(pageNumber);
				final PDFParser parser = new PDFParser(page.getPDF());
				result = parser.getPagePainter(page);
				pagePainters.put(key, result);
			}
		}
		return result;
	}

	// --------------------------------------------------------------
	// ---
	// --- PageImage cache
	// ---
	// --------------------------------------------------------------
	/**
	 * Caches pre-rendered images for each page
	 */
	private final Map<ImageKey, SoftReference<Image>> pageImages = new HashMap<>();

	/**
	 * Gets a pre-rendered image of the given page
	 */
	public Image getPageImage(final int pageNumber, final float zoomFactorHint) throws Exception {
		return getImageImpl(pageNumber, selectImageDPI(zoomFactorHint));
	}

	private Image getImageImpl(final int pageNumber, final DPISpec dpi) throws Exception {
		ImageKey key;
		Image image;
		// try to get cached image with requested DPI
		key = new ImageKey(pageNumber, dpi);
		image = getImageImpl(key);
		if (image != null)
			return image;
		if (!dpi.isImmediate())
			addRenderingRequest(key);
		// try to get cached image with medium DPI
		key = new ImageKey(pageNumber, DPI_MEDIUM);
		image = getImageImpl(key);
		if (image != null)
			return image;
		// immediately render at medium DPI
		final PagePainter painter= getPagePainter(pageNumber);
		image = painter.getImage(key.dpi.getDPI());
		putPageImage(key, image);
		return image;
	}

	/**
	 * Looks for cached image by key.
	 */
	private Image getImageImpl(final ImageKey key) {
		synchronized (getLock()) {
			SoftReference<Image> ref = pageImages.get(key);
			return ref == null ? null : ref.get();
		}
	}

	/**
	 * Puts an image into the cache.
	 */
	void putPageImage(final ImageKey key, final Image image) {
		synchronized (getLock()) {
			putPageImageImpl(key, image);
		}
	}

	private void putPageImageImpl(final ImageKey key, final Image image) {
		if (image == null) {
			pageImages.remove(key);
		} else {
			pageImages.put(key, new SoftReference<>(image));
		}
	}

	private DPISpec selectImageDPI(final float zoomFactor) {
		if (!USE_LEVEL_OF_DETAIL) { 
			return DPI_MEDIUM;
		} else if (zoomFactor < 1F) {
			return DPI_SMALL;
		} else if (zoomFactor < 2F) {
			return DPI_MEDIUM;
		} else {
			return DPI_LARGE; 
		}
	}

	// --------------------------------------------------------------
	// ---
	// --- Thumbnail cache
	// ---
	// --------------------------------------------------------------
	private final Map<PDFPage, Image> thumbnails = new HashMap<>();
	
	/**
	 * Gets a thumbnail for the given page
	 */
	public Image getThumbnail(final int pageNumber, final int width, final int height,
			final boolean createIfNotPresent) throws Exception {
		final ImageKey key = new ImageKey(pageNumber, DPI_THUMB);
		Image image = getImageImpl(key);
		if (image != null || !createIfNotPresent)
			return image;
		image = getImageImpl(pageNumber, DPI_MEDIUM);
		image = ImageUtil.scaleImage(image, width, height, false);
		putPageImage(key, image);
		return image;
	}

	// --------------------------------------------------------------
	// ---
	// --- Delayed page image rendering
	// ---
	// --------------------------------------------------------------
	private final Set<AsyncRenderer> renderingRequests = new HashSet<>();

	private void addRenderingRequest(final ImageKey key) {
		final AsyncRenderer request = new AsyncRenderer(this, key);
		boolean run;
		synchronized (getLock()) {
			run = renderingRequests.add(request);
		}
		if (run)
			ThreadPool.DEFAULT.start(request, "PDFCacheAsync", Thread.MIN_PRIORITY);
	}

	void removeRequest(final AsyncRenderer request) {
		synchronized (getLock()) {
			renderingRequests.remove(request);
		}
	}
	
}

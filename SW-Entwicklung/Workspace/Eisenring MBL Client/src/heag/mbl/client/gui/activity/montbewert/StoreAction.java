package heag.mbl.client.gui.activity.montbewert;

import heag.dsp.pub.model.DSPMontageBewertung;
import heag.mbl.client.offline.pending.StoreItemBase;
import heag.mbl.client.offline.pending.StoreItemContext;
import heag.mbl.shared.network.wan.PacketPutMontageBewertungRequest;

import java.io.IOException;

import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.io.StreamReader;
import ch.eisenring.core.io.StreamUtil;
import ch.eisenring.core.io.StreamWriter;
import ch.eisenring.core.util.file.FileImage;
import ch.eisenring.network.packet.AbstractPacket;

final class StoreAction extends StoreItemBase {

	transient DSPMontageBewertung bewertung;
	transient List<FileImage> photos = new ArrayList<>();

	/**
	 * Must provide a default constructor for Streamable interface
	 */
	StoreAction() {
	}

	StoreAction(final DSPMontageBewertung bewertung, 
			final java.util.Collection<FileImage> photos) {
		super(0);
		this.bewertung = bewertung;
		this.photos = FileImage.create(photos);
	}

	// --------------------------------------------------------------
	// ---
	// --- Implementation
	// ---
	// --------------------------------------------------------------
	@Override
	protected AbstractPacket getRequest(final StoreItemContext context) {
		return PacketPutMontageBewertungRequest.create(bewertung, photos);
	}

	@Override
	public String getDisplayName() {
		return Strings.concat("Montagebewertung ", bewertung.getProjektnummer());
	}

	@Override
	public String getDescription() {
		StringMaker b = StringMaker.obtain();
		b.append("Montagebewertung ");
		b.append(bewertung.getProjektnummer());
		b.append(", ");
		b.append(bewertung.getMonteurSIR());
		return b.release();
	}

	// --------------------------------------------------------------
	// ---
	// --- Streamable
	// ---
	// --------------------------------------------------------------
	@Override
	public void read(final StreamReader reader) throws IOException {
		super.read(reader);
		bewertung = (DSPMontageBewertung) reader.readObject();
		StreamUtil.readCollection(reader, photos);
	}

	@Override
	public void write(final StreamWriter writer) throws IOException {
		super.write(writer);
		writer.writeObject(bewertung);
		StreamUtil.writeCollection(writer, photos);
	}

}

package heag.mbl.client.gui.activity.photo;

import java.awt.Image;

import ch.eisenring.core.resource.image.ImageReducer;
import ch.eisenring.core.resource.image.ImageScaleRule;
import ch.eisenring.core.util.file.FileImage;

final class PhotoReducer {

	private final ImageReducer reducer;
	
	public PhotoReducer(final ImageScaleRule scaleRule) {
		this(new ImageReducer(scaleRule));
	}

	public PhotoReducer(final ImageReducer reducer) {
		if (reducer == null)
			throw new NullPointerException();
		this.reducer = reducer;
	}

	/**
	 * Reduces a photo.
	 * 
	 * Potentially more efficient than reduceImage(), because if the image
	 * has already been decoded for the photo, it doesn't need to be decoded again.
	 */
	public FileImage reducePhoto(final MBLPhoto photo) {
		final FileImage source = photo.getFileImage();
		final Image image = photo.getImage();
		return reducer.reduceImage(source, image);
	}

}

package heag.mbl.client.daemon;

import static heag.mbl.client.daemon.SyncDaemonStatus.BUSY;
import static heag.mbl.client.daemon.SyncDaemonStatus.IDLE;
import static heag.mbl.client.daemon.SyncDaemonStatus.NOCON;
import static heag.mbl.client.daemon.SyncDaemonStatus.PAUSED;
import heag.mbl.client.MBLClient;
import heag.mbl.client.daemon.sync.SyncCleanup;
import heag.mbl.client.offline.DataAccessHandler;
import heag.mbl.shared.network.obj.MBLSyncItem;
import heag.mbl.shared.network.obj.MBLSyncItemSet;

import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;

import ch.eisenring.app.client.AppCore;
import ch.eisenring.app.shared.network.RPCContext;
import ch.eisenring.app.shared.network.RPCManager;
import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.core.feature.Feature;
import ch.eisenring.core.feature.FeatureCardinality;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.core.threading.ThreadCore;
import ch.eisenring.core.threading.ThreadPool;
import ch.eisenring.network.StreamableConnection;
import ch.eisenring.network.packet.AbstractPacket;

/**
 * Manages and controls the synchronization of offline data.
 */
public class SyncDaemon implements Feature {

	public final MBLClient client;

	/**
	 * This flag is internally set when the synchronization is
	 * currently active.
	 */
	private final AtomicReference<SyncDaemonStatus> status = new AtomicReference<>(NOCON);

	/**
	 * Holds the thread that executes the daemons main loop
	 */
	private final AtomicReference<Thread> thread = new AtomicReference<>();

	public SyncDaemon(final MBLClient client) {
		this.client = client;
		final Thread thread = ThreadPool.DEFAULT.start(this::mainLoopEntry, "SyncDaemon", Thread.MIN_PRIORITY + 2);
		this.thread.set(thread);
	}

	// --------------------------------------------------------------
	// ---
	// --- Feature implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public java.lang.Class<?> getFeatureType() {
		return SyncDaemon.class;
	}

	@Override
	public FeatureCardinality getFeatureCardinality() {
		return FeatureCardinality.SINGLE;
	}

	@Override
	public String getFeatureName() {
		return "SyncDaemon";
	}

	// --------------------------------------------------------------
	// ---
	// --- Main loop
	// ---
	// --------------------------------------------------------------
	private void mainLoopEntry() {
		if (this.thread.get() != Thread.currentThread()) {
			// spurious start, terminate
			return;
		}
		try {
			if (!awaitLogon())
				return;
			Logger.info("logon completed, entering normal operation");
			while (true) {
				if (Thread.interrupted() || !client.isRunning())
					break;
				if (Thread.currentThread() != this.thread.get())
					break;
				mainLoop();
			}
		} finally {
			this.thread.compareAndSet(Thread.currentThread(), null);
			Logger.info("terminated");
		}
	}

	private SyncWorkItem workItem;

	/**
	 * Called continuously while in normal operation
	 */
	private void mainLoop() {
		// pause request pending?
		final long timestamp = getPausedUntil();
		if (!TimestampUtil.isNull(timestamp)) {
			ThreadCore.sleep(2_000);
			return;
		}
		// work pending?
		final SyncWorkItem workItem = this.workItem;
		if (workItem != null) {
			// connection available?
			setStatus(BUSY);
			try {
				// keep calling the current workItem until it return null (or throws)
				this.workItem = workItem.process(this);
			} catch (final Throwable t) {
				// don't care about errors
				this.workItem = null;
			}
			setStatus(this.workItem == null ? IDLE : BUSY);
			return;
		} 
		// look for work
		final DataAccessHandler dataHandler = client.getDataHandler();
		if (dataHandler != null) {
			final MBLSyncItemSet itemSet = dataHandler.getSyncItemSet();
			final MBLSyncItem syncItem = itemSet == null ? null : itemSet.getOldestItem();
			if (syncItem != null && !syncItem.isValid()) {
				// sync this item
				this.workItem = new SyncCleanup(client, syncItem); 
				setStatus(BUSY);
				return;
			}
		}
		this.status.set(IDLE);
		// there was no work to do, sleep
		ThreadCore.sleep(2_000);
	}
	
	private boolean awaitLogon() {
		Logger.info("awaiting completion of logon");
		boolean clientStarted = false;
		while (true) {
			if (Thread.interrupted())
				return false;
			final boolean running = client.isRunning();
			clientStarted |= running;
			// break if client has shut down before logon completed
			if (!running && clientStarted)
				return false;
			// if logon is complete, done
			if (client.isLogonComplete())
				return true;
			ThreadCore.sleep(1000);
		}
	}

	// --------------------------------------------------------------
	// ---
	// --- Public API
	// ---
	// --------------------------------------------------------------
	/**
	 * Gets the current status of the daemon. This call is
	 * non-blocking and returns only a best guess.
	 */
	public SyncDaemonStatus getStatus() {
		final SyncDaemonStatus result = this.status.get();
		return !TimestampUtil.isNull(getPausedUntil()) ? PAUSED : result;
	}

	void setStatus(final SyncDaemonStatus status) {
		SyncDaemonStatus newStatus = status;
		if (BUSY.equals(status)
				&& client.getConnection(false) == null) {
			newStatus = NOCON;
		}
		this.status.set(newStatus);
	}

	// --------------------------------------------------------------
	// ---
	// --- Pause control
	// ---
	// --------------------------------------------------------------
	/**
	 * Signals that the daemon should pause until timestamp is reached.
	 * The daemon will enter pause mode as soon as it can and it will
	 * not resume activity until timestamp is passed.
	 */
	private final AtomicLong pauseUntilTimestamp = new AtomicLong(TimestampUtil.NULL_TIMESTAMP);

	/**
	 * Requests the daemon to pause until timestamp.
	 * The request is ignored if timestamp is null or in the past,
	 * if the daemon has already been requested to pause the higher
	 * timestamp (this one or the previous) will take effect.
	 */
	public void pauseUntil(final long timestamp) {
		if (TimestampUtil.isNull(timestamp))
			return;
		while (true) {
			final long expect = this.pauseUntilTimestamp.get();
			long update = Math.max(expect, timestamp);
			if (update < System.currentTimeMillis())
				update = TimestampUtil.NULL_TIMESTAMP;
			if (this.pauseUntilTimestamp.compareAndSet(expect, update))
				return;
		}
	}
	
	/**
	 * Utility method to request pausing for a given time starting
	 * immediately.
	 */
	public static void pauseFor(final MBLClient client, final long pauseMillis) {
		if (pauseMillis <= 0)
			return;
		try {
			// request the sync daemon to pause its activity
			final SyncDaemon daemon = client.getFeatureLookup().getFeature(SyncDaemon.class);
			daemon.pauseUntil(System.currentTimeMillis() + pauseMillis);
		} catch (final Exception e) {
			// ignore
		}		
	}

	/**
	 * Returns the timestamp until the daemon is requested to pause.
	 * This will return either a timestamp in the future (subject to
	 * threading delays, can be perceived in the past), or NULL_TIMESTAMP
	 * if the daemon is not paused.
	 * @return
	 */
	public long getPausedUntil() {
		while (true) {
			long timestamp = pauseUntilTimestamp.get();
			if (TimestampUtil.isNull(timestamp)) {
				return TimestampUtil.NULL_TIMESTAMP;
			} else if (timestamp < System.currentTimeMillis()) {
				if (pauseUntilTimestamp.compareAndSet(timestamp, TimestampUtil.NULL_TIMESTAMP))
					return TimestampUtil.NULL_TIMESTAMP;
			} else {
				return timestamp;
			}
		}
	}

	/**
	 * Returns true if the daemon is currently paused or a pause request
	 * is pending. Does not accurately reflect the status, as the pause
	 * mode may not have been entered when this method returns true. 
	 */
	public boolean isPaused() {
		return !TimestampUtil.isNull(getPausedUntil());
	}

	/**
	 * Requests the daemon to shut down.
	 */
	public void terminate() {
		while (true) {
			final Thread thread = this.thread.get();
			if (thread == null)
				return;
			thread.interrupt();
			ThreadCore.sleep(100);
		}
	}

	// --------------------------------------------------------------
	// ---
	// --- Utility API for SyncWorkItems
	// ---
	// --------------------------------------------------------------
	public StreamableConnection getAuthenticatedConnection() throws Exception {
		try {
			final StreamableConnection connection = client.getAuthenticatedConnection();
			if (connection == null || !connection.isAlive())
				setStatus(NOCON);
			return connection;
		} catch (final Exception e) {
			setStatus(NOCON);
			throw e;
		}
	}

	/**
	 * Sends request packet and waits for reply
	 */
	public AbstractPacket sendAndWait(final AbstractPacket request) throws Exception {
		return sendAndWait(request, RPCContext.DEFAULT_TIMEOUT);
	}

	/**
	 * Sends request packet and waits for reply with selected timeout.
	 */
	public AbstractPacket sendAndWait(final AbstractPacket request, final int timeoutMillis) throws Exception {
		final AppCore core = client.getCore();
		final RPCManager manager = core.getRPCManager();
		StreamableConnection connection = null;
		try {
			connection = getAuthenticatedConnection();
			return manager.sendAndWait(connection, request, timeoutMillis);
		} catch (final Exception e) {
			if (connection == null || !connection.isAlive())
				setStatus(NOCON);
			throw e;
		}
	}

}

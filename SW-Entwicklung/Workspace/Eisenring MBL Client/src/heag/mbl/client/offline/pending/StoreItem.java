package heag.mbl.client.offline.pending;

import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.io.Streamable;

/**
 * Represents a pending store item
 * (usually the result of a workflow)
 */
public interface StoreItem extends Streamable {

	/**
	 * Gets the timestamp when this item was created
	 */
	public long getCreateTimestamp();

	/**
	 * Performs the store procedure for this item.
	 */
	public ErrorMessage storeItem(final StoreItemContext context);

	/**
	 * Gets a unique name for this item. This name is used to internally
	 * handle saving the item to disk if necessary. The name does not
	 * need to be user-readable.
	 */
	public String getUniqueName();

	/**
	 * Gets a short name to be used for displaying the item.
	 * The name should allow a user to identify what this item represents.
	 */
	public String getDisplayName();

	/**
	 * Gets a more detailed description of this item for display to the user.
	 */
	public default String getDescription() {
		return getDisplayName();
	}

}

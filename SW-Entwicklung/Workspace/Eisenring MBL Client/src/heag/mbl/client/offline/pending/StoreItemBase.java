package heag.mbl.client.offline.pending;

import heag.mbl.client.MBLClient;

import java.io.IOException;
import java.util.UUID;

import ch.eisenring.app.client.AppCore;
import ch.eisenring.app.shared.network.RPCContext;
import ch.eisenring.app.shared.network.RPCManager;
import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.format.api.Formats;
import ch.eisenring.core.io.StreamReader;
import ch.eisenring.core.io.StreamWriter;
import ch.eisenring.network.StreamableConnection;
import ch.eisenring.network.packet.AbstractPacket;

/**
 * Base class for implementing StoreItem
 */
public abstract class StoreItemBase implements StoreItem {

	private transient long createTimestamp;
	private transient int formatVersion;
	private transient long uuid0, uuid1;

	/**
	 * Default constructor for Streamable
	 */
	protected StoreItemBase() {
	}

	protected StoreItemBase(final int formatVersion) {
		this.formatVersion = formatVersion;
		this.createTimestamp = System.currentTimeMillis();
		UUID uuid = UUID.randomUUID();
		uuid0 = uuid.getMostSignificantBits();
		uuid1 = uuid.getLeastSignificantBits();
	}
	
	@Override
	public ErrorMessage storeItem(final StoreItemContext context) {
		final MBLClient client = context.getClient();
		final AbstractPacket request = getRequest(context);
		if (request == null)
			return new ErrorMessage("Error: StoreRequest is null");
		final AbstractPacket reply;
		try {
			final AppCore core = client.getCore();
			final RPCManager manager = core.getRPCManager();
			final StreamableConnection connection = client.getAuthenticatedConnection();
			reply = manager.sendAndWait(connection, request, RPCContext.DEFAULT_TIMEOUT);
		} catch (final Exception e) {
			return new ErrorMessage(e);
		}
		if (reply == null)
			return new ErrorMessage("Error: no reply from Server");
		final ErrorMessage result = reply.getMessage();
		if (!result.isSuccess()) {
			// server side error
			context.setServerResult(result);
		}
		return result;
	}

	// --------------------------------------------------------------
	// ---
	// --- Template methods
	// ---
	// --------------------------------------------------------------
	/**
	 * Gets the store request packet for this item
	 */
	protected abstract AbstractPacket getRequest(final StoreItemContext context); 

	// --------------------------------------------------------------
	// ---
	// --- Standard implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public final long getCreateTimestamp() {
		return createTimestamp;
	}

	@Override
	public String getUniqueName() {
		final StringMaker b = StringMaker.obtain();
		b.append('H');
		b.append(uuid0, Formats.HEX_FULL);
		b.append('-');
		b.append(uuid1, Formats.HEX_FULL);
		b.append('-');
		b.append(createTimestamp, Formats.HEX_FULL);
		b.append(".data");
		return b.release();
	}

	protected final int getFormatVersion() {
		return formatVersion;
	}

	// --------------------------------------------------------------
	// ---
	// --- Streamable
	// ---
	// --------------------------------------------------------------
	@Override
	public void read(final StreamReader reader) throws IOException {
		formatVersion = reader.readInt();
		uuid0 = reader.readLong();
		uuid1 = reader.readLong();
		createTimestamp = reader.readLong();
	}

	@Override
	public void write(final StreamWriter writer) throws IOException {
		writer.writeInt(formatVersion);
		writer.writeLong(uuid0);
		writer.writeLong(uuid1);
		writer.writeLong(createTimestamp);
	}

	// --------------------------------------------------------------
	// ---
	// --- Object overrides
	// ---
	// --------------------------------------------------------------
	@Override
	public int hashCode() {
		return TimestampUtil.hashCode(uuid0 ^ uuid1 ^ createTimestamp);
	}

	@Override
	public boolean equals(final Object o) {
		if (o == null || !o.getClass().equals(getClass()))
			return false;
		final StoreItemBase i = (StoreItemBase) o;
		return i.uuid0 == uuid0 && i.uuid1 == uuid1 && i.createTimestamp == createTimestamp;
	}

}

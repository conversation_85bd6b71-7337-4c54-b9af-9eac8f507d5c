package heag.mbl.client.offline.dms;

import static heag.mbl.shared.network.wan.PacketProjectFoldersRequest.BY_BASEPROJECTNUMBER;
import static heag.mbl.shared.network.wan.PacketProjectFoldersRequest.BY_PROJECTNUMBER;
import static heag.mbl.shared.network.wan.PacketProjectFoldersRequest.MATCH_START;
import heag.mbl.client.offline.LocalDirectorySet;
import heag.mbl.shared.dms.DMSObjectSet;

import java.io.IOException;

import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.io.StreamReader;
import ch.eisenring.core.io.StreamWriter;
import ch.eisenring.core.io.file.FileItem;
import ch.eisenring.dms.service.api.DMSObject;
import ch.eisenring.dms.service.codetables.DMSPropertyCode;
import ch.eisenring.dms.shared.model.api.DMSPropertyAccessor;
import ch.eisenring.dms.shared.model.pojo.DMSObjectPojo;

final class DMSStructureCacheEntry extends DMSCacheEntryBase {

	private final static int VERSION = 3;

	protected transient long cacheStorageId;
	protected transient long rootObjectRowId;
	protected transient String projectNumber;
	protected transient String baseProjectNumber;
	protected transient DMSObject rootObject;

	private DMSStructureCacheEntry() {
	}

	// --------------------------------------------------------------
	// ---
	// --- Factory methods
	// ---
	// --------------------------------------------------------------
	public static DMSStructureCacheEntry create(final DMSObjectSet objectSet) {
		final DMSObject rootObject = objectSet.getRoots()[0];
		return create(objectSet, rootObject.getPKValue());
	}

	public static DMSStructureCacheEntry create(final DMSObjectSet objectSet, final long rootObjectRowId) {
		final DMSObject rootObject = objectSet.get(rootObjectRowId);
		final DMSPropertyAccessor properties = objectSet.getProperties(rootObject);
		final DMSStructureCacheEntry entry = new DMSStructureCacheEntry();
		entry.initTimestamps();
		entry.cacheStorageId = objectSet.getRoots()[0].getPKValue();
		entry.rootObject = DMSObjectPojo.create(rootObject);
		entry.rootObjectRowId = rootObject.getPKValue();
		entry.baseProjectNumber = Strings.toString(properties.getPropertyValue(
				DMSPropertyCode.BASEPROJECTNUMBER, DMSPropertyAccessor.Mode.DIRECT));
		entry.projectNumber = Strings.toString(properties.getPropertyValue(
				DMSPropertyCode.PROJECTNUMBER, DMSPropertyAccessor.Mode.DIRECT));
		return entry;
	}

	
	// --------------------------------------------------------------
	// ---
	// --- API
	// ---
	// --------------------------------------------------------------
	/**
	 * Gets the FileItem where this entry is located
	 */
	@Override
	public FileItem getFileItem() {
		StringMaker b = StringMaker.obtain();
		b.append(LocalDirectorySet.DMS_PROJECTSTRCUTURE);
		b.append('/');
		b.append(cacheStorageId);
		b.append("_structure.data");
		return FileItem.create(b.release());
	}

	@Override
	public Long getEntryKey() {
		return rootObjectRowId;
	}

	public long getRootObjectRowId() {
		return rootObjectRowId;
	}

	public String getProjectNumber() {
		return projectNumber;
	}

	public String getBaseProjectNumber() {
		return baseProjectNumber;
	}

	public DMSObject getRootObject() {
		return rootObject;
	}

	public boolean matches(final String needle, final int matchMode) {
		if (needle == null)
			return false;
		if ((MATCH_START & matchMode) == 0) {
			// full match
			return ((BY_BASEPROJECTNUMBER & matchMode) != 0
					&& Strings.equalsIgnoreCase(needle, baseProjectNumber))
					||
					((BY_PROJECTNUMBER & matchMode) != 0
					&& Strings.equalsIgnoreCase(needle,  projectNumber));
		} else {
			// match startsWith
			return ((BY_BASEPROJECTNUMBER & matchMode) != 0
					&& Strings.startsWithIgnoreCase(baseProjectNumber, needle))
					|| 
					((BY_PROJECTNUMBER & matchMode) != 0
					&& Strings.startsWithIgnoreCase(projectNumber, needle));
		}
	}
	
	// --------------------------------------------------------------
	// ---
	// --- Streamable implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public void read(final StreamReader reader) throws IOException {
		super.read(reader);
		final int version = reader.readInt();
		switch (version) {
			default:
				throw new IOException("invalid stream version: " + version);
			case 3:
				cacheStorageId = reader.readLong();
				rootObjectRowId = reader.readLong();
				baseProjectNumber = reader.readString();
				projectNumber = reader.readString();
				rootObject = (DMSObject) reader.readObject();
				break;
		}
	}

	@Override
	public void write(final StreamWriter writer) throws IOException {
		super.write(writer);
		writer.writeInt(VERSION);
		writer.writeLong(cacheStorageId);
		writer.writeLong(rootObjectRowId);
		writer.writeString(baseProjectNumber);
		writer.writeString(projectNumber);
		writer.writeObject(rootObject);
	}

}

package heag.mbl.client.offline.dms;

import java.io.IOException;

import heag.mbl.client.MBLClient;
import heag.mbl.client.offline.LocalDirectorySet;
import heag.mbl.shared.dms.DMSObjectSet;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.Map;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.io.StreamFormat;
import ch.eisenring.core.io.StreamReader;
import ch.eisenring.core.io.StreamWriter;
import ch.eisenring.core.io.Streams;
import ch.eisenring.core.io.file.FileItem;
import ch.eisenring.dms.service.api.DMSObject;

public class DMSStructureCache extends DMSCacheBase<DMSStructureCacheEntry> {

	private final static String INDEX_FILE_NAME = LocalDirectorySet.DMS_PROJECTSTRCUTURE + "/StructureCache.index";
	private final static int VERSION = 3;
	
	public DMSStructureCache(final MBLClient client) {
		super(client);
	}

	// --------------------------------------------------------------
	// ---
	// --- Cache base implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public FileItem getIndexFileItem() {
		return FileItem.create(INDEX_FILE_NAME);
	}

	@Override
	public int getCacheVersion() {
		return VERSION;
	}

	// --------------------------------------------------------------
	// ---
	// --- Specific API
	// ---
	// --------------------------------------------------------------
	/**
	 * Add new entry or replace existing entry
	 */
	public boolean addEntry(final DMSObjectSet objectSet) {
		if (objectSet == null)
			return false;
		final DMSStructureCacheEntry mainEntry = DMSStructureCacheEntry.create(objectSet);
		// build sub entries
		final List<DMSStructureCacheEntry> entries = new ArrayList<>();
		entries.add(mainEntry);
		for (final DMSObject kueche : objectSet.getKuechenRoots()) {
			final DMSStructureCacheEntry entry = DMSStructureCacheEntry.create(objectSet, kueche.getPKValue());
			entries.add(entry);
		}
		synchronized (getLock()) {
			if (!storeData(objectSet, mainEntry.getFileItem()))
				return false;
			final Map<Long, DMSStructureCacheEntry> cacheMap = getCacheMap();
			// put all entries
			for (final DMSStructureCacheEntry entry : entries) {
				cacheMap.put(entry.getEntryKey(), entry);
			}
			return storeCache();
		}
	}

	/**
	 * Find matching items
	 */
	public List<DMSObject> find(final String number, final int matchMode) {
		final List<DMSObject> result = new ArrayList<>();
		synchronized (getLock()) {
			// check cached items for matches
			final Map<Long, DMSStructureCacheEntry> cacheMap = getCacheMap();
			for (final DMSStructureCacheEntry entry : cacheMap.values()) {
				if (entry.matches(number, matchMode))
					result.add(entry.getRootObject());
			}
		}
		return result;
	}

	/**
	 * Gets the structure for object
	 */
	public DMSObjectSet getStructure(final DMSObject rootObject) {
		return rootObject == null ? null : getStructure(rootObject.getPKValue());
	}

	/**
	 * Gets the structure for object
	 */
	public DMSObjectSet getStructure(final long rootObjectRowId) {
		if (rootObjectRowId == 0)
			return null;
		synchronized (getLock()) {
			final Map<Long, DMSStructureCacheEntry> cacheMap = getCacheMap();
			final DMSStructureCacheEntry entry = cacheMap.get(rootObjectRowId);
			// no entry found
			if (entry == null)
				return null;
			// load entry from disk
			final DMSObjectSet objectSet = loadData(entry.getFileItem());
			// only return the subset the item represents
			return objectSet.getSubSet(entry.getRootObjectRowId());
		}
	}

	// --------------------------------------------------------------
	// ---
	// --- Entry data store/load 
	// ---
	// --------------------------------------------------------------
	private boolean storeData(final DMSObjectSet objectSet, final FileItem file) {
		file.delete();
		StreamWriter writer = null;
		try {
			writer = StreamFormat.PERSISTENCE.createWriter(file, Streams.DEFAULT_BUFFER_SIZE);
			writer.writeInt(VERSION);
			writer.writeObject(objectSet);
			writer.close();
			return true;
		} catch (final Exception e) {
			Streams.closeSilent(writer);
			file.delete();
		}
		return false;
	}

	private DMSObjectSet loadData(final FileItem file) {
		StreamReader reader = null;
		try {
			reader = StreamFormat.createReader(file, Streams.DEFAULT_BUFFER_SIZE);
			final int version = reader.readInt();
			if (version != VERSION)
				throw new IOException("invalid version: " + version);
			final DMSObjectSet objectSet = (DMSObjectSet) reader.readObject();
			reader.close();
			return objectSet;
		} catch (final Exception e) {
			return null;
		} finally {
			Streams.closeSilent(reader);
		}
	}

}

package heag.mbl.client.offline.test;

import heag.mbl.client.MBLClient;
import heag.mbl.client.MBLCore;
import heag.saggitarius.fs.FSException;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.Arrays;

import ch.eisenring.app.client.AppStartup;
import ch.eisenring.core.datatypes.date.format.HEAGDateFormat;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.format.api.Formats;
import ch.eisenring.core.io.Streams;
import ch.eisenring.core.io.file.FileItem;
import ch.eisenring.core.io.file.FileItemProperties;
import ch.eisenring.core.io.util.PRNGInputStream;
import ch.eisenring.core.platform.Platform;

public class TestStandalone extends AppStartup {

	public static void main(String[] argv) {
		Platform.setTempSubPath("HEAG_MBL");
		Thread.currentThread().setName("TestStandAloneStartup");
		defaultStartupActions(argv);
		final MBLCore core = new MBLCore();
		core.setConnectAllowed(false);
		core.launch(argv);
		try {
			final MBLClient client = core.getComponent(MBLClient.class);
			StandaloneFS.setKey(client);
			client.getLocalStorage().getFileSystem();
			FileItem rootItem = FileItem.create("SGFS://");
			testReadWrite(rootItem);
		} catch (final Exception e) {
			throw new RuntimeException(e.getMessage(), e);
		}
		core.shutdown(true);
	}
	
	static InputStream getPRNGInput() {
		return new PRNGInputStream(123_456_789L, -1234567890L);
	}

	final static HEAGDateFormat dfFileItem = HEAGDateFormat.get("yyyy/MM/dd HH:mm:ss.SSS");

	static void dumpFSStructure(final FileItem dirItem) throws FSException {
		StringMaker b = StringMaker.obtain();
		b.append(getSpace(dirItem));
		b.append('\n');
		dumpFSStructure(b, dirItem, 0);
		System.out.println(b.release());
	}

	static void dumpFSStructure(final StringMaker b, final FileItem dirItem, int indent) throws FSException {
		FileItem[] contents = dirItem.listItems();
		if (contents == null)
			return;
		Arrays.sort(contents, FileItem.Order.Name);
		for (FileItem item : contents) {
			final FileItemProperties properties = item.getProperties();
			if (properties.isFile()) {
				b.append(' ', indent);
				int x = b.length() + 31;
				b.append(item.getName());
				b.append(' ', Math.max(x - b.length(), 0));
				b.append(properties.lastModified(), dfFileItem);
				b.append(' ');
				b.append(properties.length(), Formats.BYTESIZE);
				b.append('\n');
			} else if (properties.isDirectory()) {
				b.append(' ', indent);
				int x = b.length() + 31;
				b.append(item.getName());
				b.append(' ', Math.max(x - b.length(), 0));
				b.append(properties.lastModified(), dfFileItem);
				b.append(" [DIR]\n");
				dumpFSStructure(b, item, indent + 2);
			}
		}
	}

	static void testReadWrite(final FileItem whereDir) throws IOException {
		//sgfs.format();
		System.out.println(getSpace(whereDir));
		
		// create directory
		String testDirName = "Test Dir #2";
		System.out.println("*** create dir: " + testDirName);
		FileItem testDir = whereDir.createChild(testDirName); 
		testDir.mkdirs();
		dumpFSStructure(whereDir);
		System.out.println("*** delete dir: " + testDirName);
		testDir.delete();
		dumpFSStructure(whereDir);
		
		try {
			// create a file
			FileItem testFile = whereDir.createChild("Test FSOutputStream");
			// ensure file does not exist
			testFile.delete();
			long startTime = System.currentTimeMillis();
			System.out.println("*** create file: " + testFile.getName());
			OutputStream output = testFile.getOutputStream();
			long bytesWritten;
			{
				bytesWritten = Streams.copy(getPRNGInput(), output, 8192);
				output.close();
			}
			long duration = System.currentTimeMillis() - startTime;
			System.out.println("Written " + bytesWritten + " bytes in " + duration + "ms");
			dumpFSStructure(whereDir);
			
			// re-read file
			System.out.println("*** read file: " + testFile.getName());
			startTime = System.currentTimeMillis();
			InputStream input = testFile.getInputStream();
			boolean same;
			{
				same = Streams.verifySame(input, getPRNGInput());
				input.close();
			}
			duration = System.currentTimeMillis() - startTime;
			System.out.println("Verify stream:" + same + " in " + duration + "ms");

			System.out.println("*** delete file: " + testFile.getName());
			testFile.delete();
			dumpFSStructure(whereDir);
		} finally {
			// nothing
		}
	}

	static String getSpace(final FileItem fileItem) {
		final StringMaker b = StringMaker.obtain();
		b.append("Free Space: ");
		b.append(fileItem.getFreeSpace(), Formats.BYTESIZE);
		b.append(" of ");
		b.append(fileItem.getTotalSpace(), Formats.BYTESIZE);
		return b.release();
	}

}

package heag.mbl.client.offline.dms;

import heag.mbl.client.MBLClient;
import heag.mbl.client.offline.LocalDirectorySet;
import heag.mbl.shared.network.obj.MBLObjectVersionInfo;
import heag.mbl.shared.network.wan.PacketBinaryRequest;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.Map;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.core.io.StreamFormat;
import ch.eisenring.core.io.StreamReader;
import ch.eisenring.core.io.StreamWriter;
import ch.eisenring.core.io.Streams;
import ch.eisenring.core.io.file.FileItem;
import ch.eisenring.core.io.file.FileItemProperties;
import ch.eisenring.dms.service.api.DMSObject;
import ch.eisenring.dms.shared.model.api.DMSBinary;
import ch.eisenring.dms.shared.model.pojo.DMSBinaryPojo;

/**
 * Manages locally cached binary versions.
 */
public final class DMSVersionCache extends DMSCacheBase<DMSVersionCacheEntry> {

	private final static String INDEX_FILE_NAME = LocalDirectorySet.ROOT + "DMSVersionCache.index";
	private final static int VERSION = 1;

	public DMSVersionCache(final MBLClient client) {
		super(client);
	}
	
	public static List<FileItem> getDefaultDirectories() {
		return CacheOrg.getDefaultDirectories();
	}

	// --------------------------------------------------------------
	// ---
	// --- Cache base implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public FileItem getIndexFileItem() {
		return FileItem.create(INDEX_FILE_NAME); 
	}

	@Override
	public int getCacheVersion() {
		return VERSION;
	}

	// --------------------------------------------------------------
	// ---
	// --- Cache management
	// ---
	// --------------------------------------------------------------
	private static boolean isNullRowId(final Long rowId) {
		return rowId == null || rowId.longValue() == 0;
	}

	/**
	 * Gets the cached versionRowId for objectRowId.
	 * Return 0 if no cached version exists.
	 */
	public long getCachedVersionRowId(final Long objectRowId) {
		if (isNullRowId(objectRowId))
			return 0;
		synchronized (getLock()) {
			final Map<Long, DMSVersionCacheEntry> map = getCacheMap();
			final DMSVersionCacheEntry entry = map.get(objectRowId);
			if (entry == null)
				return 0;
			// ensure the version is still present on disk!
			final FileItem file = entry.getFileItem();
			final FileItemProperties properties = file.getProperties();
			return (properties.isFile() && properties.exists())
					? entry.getVersionRowId() : 0;
		}
	}

	/**
	 * Marks objects in cache as validated
	 */
	public void setValidated(final java.util.Collection<Long> objectRowIds) {
		if (objectRowIds == null || objectRowIds.isEmpty())
			return;
		synchronized (getLock()) {
			final long now = System.currentTimeMillis();
			final Map<Long, DMSVersionCacheEntry> map = getCacheMap();
			boolean changed = false;
			for (final Long key : objectRowIds) {
				if (isNullRowId(key))
					continue;
				final DMSVersionCacheEntry entry = map.get(key);
				if (entry == null)
					continue;
				changed |= entry.setValidationTimestamp(now);
			}
			// if any entries have changed, store the cache
			if (changed) {
				storeCache();
			}
		}
	}

	/**
	 * Removes the specified objects from cache, deletes the versions on disk.
	 */
	public void removeEntries(final java.util.Collection<Long> objectRowIds) {
		if (objectRowIds == null || objectRowIds.isEmpty())
			return;
		synchronized (getLock()) {
			boolean changed = false;
			final Map<Long, DMSVersionCacheEntry> map = getCacheMap();
			for (final Long key : objectRowIds) {
				if (isNullRowId(key))
					continue;
				final DMSVersionCacheEntry entry = map.remove(key);
				if (entry == null)
					continue;
				changed = true;
				final FileItem fileItem = entry.getFileItem();
				fileItem.delete();
			}
			// if any entries have changed, store the cache
			if (changed) {
				storeCache();
			}
		}
	}

	public List<Long> getEntriesOlderThan(final long timestamp) {
		final List<Long> result = new ArrayList<>();
		synchronized (getLock()) {
			final Map<Long, DMSVersionCacheEntry> map = getCacheMap();
			for (final Map.Entry<Long, DMSVersionCacheEntry> me : map.entrySet()) {
				final DMSVersionCacheEntry entry = me.getValue();
				if (TimestampUtil.compare(entry.getValidationTimestamp(), timestamp) < 0) {
					result.add(me.getKey());
				}
			}
		}
		return result;
	}

	/**
	 * Puts object into cache (synchronous)
	 */
	public void addCachedVersion(final DMSBinary binaryVersion) {
		if (binaryVersion == null)
			return;
		final long versionRowId = binaryVersion.getPKValue();
		final Long key = binaryVersion.getObjectRowId();
		synchronized (getLock()) {
			final Map<Long, DMSVersionCacheEntry> map = getCacheMap();
			DMSVersionCacheEntry entry = map.get(key);
			// replace entry?
			if (entry != null) {
				if (entry.getVersionRowId() == versionRowId) {
					// same version, nothing to do
					return;
				}
				// different version, delete the old version
				entry.getFileItem().delete();
			}
			// place new entry
			entry = DMSVersionCacheEntry.create(key, versionRowId);
			map.put(key, entry);
			// store cache
			if (storeCache()) {
				// write new cached version to disk
				final DMSBinaryPojo pojo = DMSBinaryPojo.create(binaryVersion);
				writeVersion(pojo);
			}
		}
	}

	/**
	 * Gets cached object version. Returns null if the requested version
	 * is not in the cache.
	 */
	public DMSBinary getCachedVersion(final long versionRowId) {
		final FileItem file = CacheOrg.getFileItem(versionRowId);
		StreamReader reader = null;
		try {
			reader = StreamFormat.createReader(file, Streams.DEFAULT_BUFFER_SIZE);
			final DMSBinary binary = (DMSBinary) reader.readObject();
			reader.close();
			return binary;
		} catch (final Exception e) {
			return null;
		} finally {
			Streams.closeSilent(reader);
		}
	}

	/**
	 * Gets the cached version for object or null if not present.
	 */
	public DMSBinary getCachedVersion(final DMSObject object) {
		if (object == null)
			return null;
		final long versionRowId;
		synchronized (getLock()) {
			final Map<Long, DMSVersionCacheEntry> map = getCacheMap();
			final DMSVersionCacheEntry entry = map.get(object.getPKValue());
			if (entry == null)
				return null;
			versionRowId = entry.versionRowId;
		}
		return getCachedVersion(versionRowId);
	}
	
	/**
	 * Creates a conditional binary request (for retrieving binary
	 * only if the cached version is outdated or does not exist).
	 */
	public PacketBinaryRequest createBinaryRequest(final long objectRowId) {
		final long versionRowId = getCachedVersionRowId(objectRowId);
		final MBLObjectVersionInfo info = MBLObjectVersionInfo.createRequest(
				objectRowId, versionRowId, MBLObjectVersionInfo.CONDITIONAL_BINARY);
		final PacketBinaryRequest request = PacketBinaryRequest.create(info);
		return request;
	}

	// --------------------------------------------------------------
	// ---
	// --- Cache put queue 
	// ---
	// --------------------------------------------------------------
	/**
	 * Puts object into cache (potentially asynchronous)
	 */
	public void postCachedVersion(final DMSBinary binaryVersion) {
		addCachedVersion(binaryVersion);
	}

	// --------------------------------------------------------------
	// ---
	// --- Version Store/Load 
	// ---
	// --------------------------------------------------------------
	private boolean writeVersion(final DMSBinary binaryVersion) {
		final FileItem file = CacheOrg.getFileItem(binaryVersion.getPKValue());
		// trust that the existing file is valid
		if (file.getProperties().isFile())
			return true;
		StreamWriter writer = null;
		try {
			writer = StreamFormat.PERSISTENCE.createWriter(file, Streams.DEFAULT_BUFFER_SIZE);
			writer.writeObject(binaryVersion);
			writer.close();
			return true;
		} catch (final Exception e) {
			Streams.closeSilent(writer);
			file.delete();
		}
		return false;
	}

	// --------------------------------------------------------------
	// ---
	// --- Version cache recovery 
	// ---
	// --------------------------------------------------------------
	/**
	 * Validates the entire cache against disk, and updates the 
	 * cache index with entries present on disk. This is potentially
	 * an extremely lengthy operation.
	 */
	public void validateCache() {
		throw new UnsupportedOperationException();
	}

}

package heag.mbl.client.offline.huo;

import heag.huo.shared.pojo.HUOObjekt;
import heag.mbl.client.offline.OfflineCacheEntry;
import heag.mbl.shared.network.wan.PacketHausUGetReply;

import java.io.IOException;

import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.io.StreamReader;
import ch.eisenring.core.io.StreamWriter;
import ch.eisenring.core.io.file.FileItem;

/**
 * Represents a cached Hausübersicht
 */
final class HUOEntry extends OfflineCacheEntry {

	private final static int VERSION = 2;

	private transient String basisNummer;

	private HUOEntry() {
	}

	// --------------------------------------------------------------
	// ---
	// --- Factory methods
	// ---
	// --------------------------------------------------------------
	public static HUOEntry create(final PacketHausUGetReply reply) {
		final HUOEntry entry = new HUOEntry();
		if (reply.isValid()) {
			final HUOObjekt objekt = reply.getObjekt();
			entry.basisNummer = objekt.getBasisProjekt().getProjektNummer();
			entry.setCreationTimestamp(objekt.getCreationTimestamp());
		} else {
			entry.basisNummer = reply.getBasisNummer();
			entry.setCreationTimestamp(System.currentTimeMillis());
		}
		return entry;
	}

	// --------------------------------------------------------------
	// ---
	// --- API
	// ---
	// --------------------------------------------------------------
	public final String getBasisNummer() {
		return basisNummer;
	}

	@Override
	public final FileItem getFileItem() {
		return CacheOrg.getFileItem(basisNummer);
	}

	// --------------------------------------------------------------
	// ---
	// --- Streamable
	// ---
	// --------------------------------------------------------------
	@Override
	public void read(final StreamReader reader) throws IOException {
		super.read(reader);
		final int version = reader.readInt();
		if (version != VERSION)
			throw new IOException("invalid version: " + version);
		basisNummer = reader.readString();
	}

	@Override
	public void write(final StreamWriter writer) throws IOException {
		super.write(writer);
		writer.writeInt(VERSION);
		writer.writeString(basisNummer);
	}

	// --------------------------------------------------------------
	// ---
	// --- Object overrides
	// ---
	// --------------------------------------------------------------
	@Override
	public int hashCode() {
		return Strings.hashCode(basisNummer);
	}

	@Override
	public boolean equals(final Object o) {
		if (!(o instanceof HUOEntry))
			return false;
		final HUOEntry e = (HUOEntry) o;
		return Strings.equals(e.basisNummer, basisNummer);
	}

}

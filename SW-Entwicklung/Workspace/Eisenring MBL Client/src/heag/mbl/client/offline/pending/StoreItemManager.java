package heag.mbl.client.offline.pending;

import heag.mbl.client.MBLClient;
import heag.mbl.client.daemon.SyncDaemon;

//import java.io.File;
import java.io.IOException;
import java.util.List;

import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.collections.Set;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.collections.impl.HashSet;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.io.file.FileItem;
import ch.eisenring.core.io.file.FileItemProperties;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.core.threading.ThreadCore;

public class StoreItemManager {

	public final MBLClient client;
	private final Object lock = new Object();

	public StoreItemManager(final MBLClient client) {
		this.client = client;
		client.getThreadPool().startDaemon(() -> main<PERSON>oop(), "StoreItemManager");
	}

	// --------------------------------------------------------------
	// ---
	// --- Listener management
	// ---
	// --------------------------------------------------------------
	private final Set<StoreItemListener> listeners = new HashSet<>();
	
	public void addListener(final StoreItemListener listener) {
		if (listener == null)
			return;
		synchronized (getLock()) {
			listeners.add(listener);
		}
	}
	
	public void removeListener(final StoreItemListener listener) {
		if (listener == null)
			return;
		synchronized (getLock()) {
			listeners.remove(listener);
		}
	}

	private StoreItemListener[] getListeners() {
		synchronized (getLock()) {
			return listeners.toArray(StoreItemListener.class);
		}
	}

	/**
	 * Recovers unsaved items from disk
	 */
	public void recoverItemsFromDisk() {
		try {
			final FileItem[] dirs = getDiskPathes();
			for (final FileItem dir : dirs) {
				final FileItem files[] = dir.listItems();
				for (final FileItem file : files) {
					try {
						final StoreItemEntry entry = StoreItemEntry.create(file);
						synchronized (getLock()) {
							entries.add(entry);
						}
					} catch (final Exception e) {
						// item not recoverable
						Logger.fatal(e);
						file.delete();
					}
				}
			}
		} catch (final Exception e) {
			Logger.fatal(e);
		}
		fireStatusChanged();
	}

	// --------------------------------------------------------------
	// ---
	// --- Utility methods
	// ---
	// --------------------------------------------------------------
	/**
	 * Gets the lock used by the manager. Synchronizing on the lock
	 * ensures the manager state is not modified by another thread.
	 */
	public final Object getLock() {
		return lock;
	}

	/**
	 * Gets the path where items are to be stored on disk
	 */
	public FileItem[] getDiskPathes() throws IOException {
		final String configuredPathes = client.UNSAVED_PATH.get();
		if (Strings.isEmpty(configuredPathes))
			throw new IOException("no unsaved item path specified");
		final String[] pathNames = Strings.explode(configuredPathes, ';');
		final ArrayList<FileItem> result = new ArrayList<>();
		for (final String path : pathNames) { 
			final FileItem dir = FileItem.create(path);
			final FileItemProperties properties = dir.getProperties();
			if (!properties.exists())
				continue;
			if (!properties.isDirectory())
				continue;
			result.add(dir);
		}
		return result.toArray(FileItem.class);
	}
	
	/**
	 * Gets the current client instance 
	 */
	public final MBLClient getClient() {
		return client;
	}

	/**
	 * Gets Status text for the manager
	 */
	public String getStatusText() {
		int itemCount, failCount;
		synchronized (getLock()) {
			itemCount = entries.size();
			failCount = failures.size();
		}
		final StringMaker b = StringMaker.obtain();
		switch (itemCount) {
			case 0: 
				b.append("Keine Daten");
				break;
			case 1:
				b.append("1 Datenobjekt");
				break;
			case 2:
				b.append(itemCount);
				b.append(" Datenobjekte");
				break;
		}
		b.append(" zu versenden");
		if (failCount > 0) {
			b.append(" (");
			b.append(failCount);
			b.append(" Fehler)");
		}
		return b.release();
	}

	private final List<StoreItemEntry> entries = new ArrayList<>();
	private final List<StoreItemEntry> failures = new ArrayList<>();
	
	public void addItem(final StoreItem item) throws Exception {
		final StoreItemEntry entry = StoreItemEntry.create(this, item);
		synchronized (getLock()) {
			// enqueue the item
			if (entries.contains(entry))
				throw new IllegalStateException("Duplicate store action item!");
			entries.add(entry);
		}
		fireStatusChanged();
	}

	/**
	 * Internal notification hook. Called when entries have changed.
	 */
	void fireStatusChanged() {
		for (final StoreItemListener listener : getListeners()) {
			try {
				listener.storeItemManagerStatusChanged(this);
			} catch (final Exception e) {
				// ignore listeners throwing
			}
		}
	}

	// --------------------------------------------------------------
	// ---
	// --- Background thread
	// ---
	// --------------------------------------------------------------
	void mainLoop() {
		while (true) {
			long delay = 5000;
			// check for work
			StoreItemEntry entry;
			if (client.isLogonComplete()) {
				synchronized (getLock()) {
					entry = entries.isEmpty() ? null : entries.iterator().next();
				}
			} else {
				entry = null;
			}
			if (entry == null) {
				// no work, keep quiet
			} else if (client.getConnection(false) == null) {
				// not connected, attempt connection
				try {
					if (client.getConnection() != null)
						delay = 500;
				} catch (final Exception e) {
					// connection failed
				}
			} else {
				// check if connection authenticated
				boolean authenticated;
				try {
					authenticated = (client.getAuthenticatedConnection() != null);
				} catch (final Exception e) {
					authenticated = false;
				}
				if (authenticated) {
					// attempt to process entry
					try {
						if (processEntry(entry))
							delay = 0;
					} catch (final Exception e) {
						// ignore error, will try again later
					}
				} else {
					// wait for authentication
					delay = 500;
				}
			}
			
			// sleep for a bit (limited to somewhat sane values)
			delay = Math.max(delay, 25);
			delay = Math.min(delay, 5000);
			try {
				ThreadCore.sleep(delay);
			} catch (final InterruptedException e) {
				// ignore
			}
		}
	}

	private boolean processEntry(final StoreItemEntry entry) throws Exception {
		SyncDaemon.pauseFor(client, 30_000);
		final StoreItemContext context = new StoreItemContext(this, entry);
		final ErrorMessage processResult = context.getItem().storeItem(context);
		final ErrorMessage serverResult = context.getServerResult();
		boolean result = false;
		if (processResult.isSuccess()) {
			synchronized (getLock()) {
				entries.remove(entry);
			}
			entry.deleteItem();
			fireStatusChanged();
			result = true;
		} else if (serverResult != null && !serverResult.isSuccess()) {
			// server returned an error for this item
			synchronized (getLock()) {
				entries.remove(entry);
				failures.add(entry);
			}
			fireStatusChanged();
		}
		return result;
	}

}

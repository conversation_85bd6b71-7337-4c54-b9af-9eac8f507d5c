package heag.mbl.client.offline;

import heag.mbl.client.MBLClient;
import heag.saggitarius.fs.BlockSizeCode;
import heag.saggitarius.fs.FSException;
import heag.saggitarius.fs.SaggitariusFileSystem;
import heag.saggitarius.fs.SaggitariusKey;
import heag.saggitarius.fs.impl.SGFileItemFactory;
import heag.saggitarius.storage.BlockDevice;
import heag.saggitarius.storage.crypt.EncryptingBlockDevice;
import heag.saggitarius.storage.file.FileBlockDevice;

import java.io.Closeable;
import java.io.File;
import java.io.IOException;
import java.util.UUID;

import ch.eisenring.app.shared.Configurable;
import ch.eisenring.core.application.Configuration;
import ch.eisenring.core.application.ConfigurationException;
import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.codetable.MessageClassCode;
import ch.eisenring.core.collections.Collection;
import ch.eisenring.core.crypt.CryptUtil;
import ch.eisenring.core.crypt.SecretTransport;
import ch.eisenring.core.datatypes.date.format.HEAGDateFormat;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.datatypes.strings.format.api.Formats;
import ch.eisenring.core.io.StreamFormat;
import ch.eisenring.core.io.StreamReader;
import ch.eisenring.core.io.StreamWriter;
import ch.eisenring.core.io.Streams;
import ch.eisenring.core.io.file.FileItem;
import ch.eisenring.core.io.file.FileItemFactoryProvider;
import ch.eisenring.core.io.file.FileItemProperties;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.core.reflect.ClassTransport;
import ch.eisenring.core.util.ReflectionUtil;

public class MBLLocalStorage implements Configurable, Closeable {

	protected final MBLClient client;

	private String containerFileName;
	private BlockSizeCode containerBlockSize;
	private long containerBlockCount;
	private int cacheBuffers = -1;

	private File containerFile;
	private FileBlockDevice rawBlockDevice;
	private BlockDevice cryptBlockDevice;
	private SaggitariusFileSystem fileSystem;

	/**
	 * Gets the local storage device id.
	 */
	public UUID getDeviceId() throws IOException {
		final BlockDevice rawBlockDevice = getRawBlockDevice();
		return rawBlockDevice.getUUID();
	}

	public synchronized SaggitariusFileSystem getFileSystem() throws IOException {
		if (fileSystem == null) {
			if (cryptBlockDevice == null)
				throw new IllegalStateException("key not set");
			try {
				fileSystem = new SaggitariusFileSystem(cryptBlockDevice);
			} catch (final Exception e) {
				throw new IOException(e.getMessage(), e);
			}
			if (cacheBuffers > 0)
				fileSystem.setCacheBuffers(cacheBuffers);
			// add the file item factory for this file system.
			// this makes the file system available through FileItem API
			final SGFileItemFactory factory = new SGFileItemFactory(fileSystem);
			FileItemFactoryProvider.addFactory(factory);
			initStorage();
		}
		return fileSystem;
	}

	private LocalStorageVersion getStorageVersion() {
		final FileItem versionFile = FileItem.create(LocalStorageVersion.VERSION_FILE_NAME);
		StreamReader reader = null;
		try {
			reader = StreamFormat.createReader(versionFile, Streams.DEFAULT_BUFFER_SIZE);
			return (LocalStorageVersion) reader.readObject();
		} catch (final IOException e) {
			// problem
			return null;
		} finally {
			Streams.closeSilent(reader);
		}
	}

	private void setStorageVersion(final LocalStorageVersion version) {
		final FileItem versionFile = FileItem.create(LocalStorageVersion.VERSION_FILE_NAME);
		versionFile.delete();
		StreamWriter writer = null;
		try {
			writer = StreamFormat.PERSISTENCE.createWriter(versionFile, Streams.DEFAULT_BUFFER_SIZE);
			writer.writeObject(version);
			writer.close();
		} catch (final IOException e) {
			// problem
		} finally {
			Streams.closeSilent(writer);
		}
	}

	/**
	 * Ensures the standard directory structure is created
	 */
	private void initStorage() {
		// detect storage version
		final LocalStorageVersion currentVersion = getStorageVersion();
		
		// determine if a format is needed and/or
		// if directory structure needs an update
		final boolean formatNeeded = currentVersion == null
				|| currentVersion.getStorageVersion() != LocalStorageVersion.VERSION;
		final boolean updateDirectories = formatNeeded 
				|| currentVersion == null
				|| currentVersion.getDirectoryVersion() != LocalDirectorySet.VERSION;
		
		if (formatNeeded) {
			Logger.info("LocalStorage version mismatch: Refomatting");
			try {
				getFileSystem().format();
			} catch (final IOException e) {
				// format failed
			}
		}
		if (updateDirectories) {
			Logger.info("LocalStorage: Creating default directory structure");
			// create the default directories
			final LocalDirectorySetImpl defaultPathes = new LocalDirectorySetImpl();
			defaultPathes.createDefaultDirectories();
			// write current version
			setStorageVersion(new LocalStorageVersion());
		}
	}

	/**
	 * Gets the preshared key
	 */
	public byte[] getPresharedKey() throws IOException {
		final FileBlockDevice rawBlockDevice = getRawBlockDevice();
		return rawBlockDevice.getPresharedKey();
	}
	
	public synchronized File getContainerFile() throws IOException {
		if (containerFile == null) {
			containerFile = new File(containerFileName);
		}
		return containerFile;
	}

	public synchronized FileBlockDevice getRawBlockDevice() throws IOException {
		if (rawBlockDevice == null) {
			final File file = getContainerFile();
			rawBlockDevice = new FileBlockDevice(file);
		}
		return rawBlockDevice;
	}

	public MBLLocalStorage(final MBLClient client) {
		this.client = client;
	}

	public ErrorMessage checkLocalStorage() {
		if (Strings.isEmpty(containerFileName)) {
			return new ErrorMessage("Konfiguration ungültig");
		}
		try {
			getRawBlockDevice();
			return ErrorMessage.OK;	
		} catch (Exception e) {
			try {
				final File file = getContainerFile();
				if (!file.exists()) {
					return new ErrorMessage(MessageClassCode.WARN, "Offline Container nicht eingerichtet");
				} else {
					return new ErrorMessage(Strings.concat("Offline Container\n\"",
							file.getAbsolutePath(), "\"\nkann nicht geöffnet werden:\n", e.getMessage()));
				}
			} catch (final Exception e2) {
				return new ErrorMessage(Strings.concat("Offline Container kann nicht geöffnet werden:\n", e2.getMessage()));
			}
		}
		
	}

	public ErrorMessage createContainerFile(final BlockSizeCode blockSize, final long blockCount) {
		try {
			final File file = getContainerFile();
			FileBlockDevice.create(file, blockSize.getLBNShift(), blockCount);
			Streams.closeSilent(this);
			return ErrorMessage.OK;
		} catch (final Exception e) {
			return new ErrorMessage(e);
		}
	}

	public synchronized ErrorMessage formatStorage() {
		final BlockDevice device = cryptBlockDevice;
		if (device == null)
			return new ErrorMessage("Device key not set");
		if (fileSystem != null)
			return new ErrorMessage("FileSystem already started");
		try {
			this.fileSystem = new SaggitariusFileSystem(device);
			fileSystem.format();
			return ErrorMessage.OK;
		} catch (final Exception e) {
			Logger.error(e);
			return new ErrorMessage(e.getMessage());
		} finally {
			Streams.closeSilent(this);
		}
		
	}

	/**
	 * Checks if the storage is fully accessible (file open and key set)
	 */
	public synchronized boolean isFullyAccessible() {
		return cryptBlockDevice != null;
	}

	/**
	 * Sets the key data for encrypted access
	 */
	public synchronized void setKey(final ClassTransport keyImpl, final SecretTransport secret) throws Exception {
		if (cryptBlockDevice != null)
			throw new IllegalStateException("key already set");
		if (getRawBlockDevice() == null)
			throw new IllegalStateException("container file not accessible");
// dump out the secret and keyimpl
//try {
//	
//	FileOutputStream fout = new FileOutputStream("C:\\Test\\Secret.dump");
//	StreamWriter writer = StreamFormat.PERSISTENCE.createWriter(fout);
//	writer.writeObject(keyImpl);
//	writer.writeObject(secret);
//	writer.close();
//	fout.close();
//}  catch (final Exception e) {
//	Logger.fatal(e);
//}
		byte[] iv = null;
		byte[] rawSecret = null;
		byte[] presharedKey = null;
		SaggitariusKey sgKey = null;
		try {
			iv = secret.getInitVector();
			rawSecret = secret.getSecret();
			presharedKey = getPresharedKey();
			byte[] decrypted = CryptUtil.decrpyt(rawSecret, presharedKey, iv);
			sgKey = (SaggitariusKey) ReflectionUtil.newInstance(keyImpl.getClassClass(), getDeviceId(), decrypted);
			cryptBlockDevice = new EncryptingBlockDevice(getRawBlockDevice(), sgKey);
		} catch (final Exception e) {
			CryptUtil.destroy(sgKey);
		} finally {
			CryptUtil.destroy(iv);
			CryptUtil.destroy(rawSecret);
			CryptUtil.destroy(presharedKey);
		}
	}

	// --------------------------------------------------------------
	// ---
	// --- Closeable implementation
	// ---
	// --------------------------------------------------------------
	public void close() throws IOException {
		try {
			Streams.closeSilent(fileSystem);
			Streams.closeSilent(cryptBlockDevice);
			Streams.closeSilent(rawBlockDevice);
		} finally {
			cryptBlockDevice = null;
			rawBlockDevice = null;
			containerFile = null;
		}
	}

	final static HEAGDateFormat dfFileItem = HEAGDateFormat.get("yyyy/MM/dd HH:mm:ss.SSS");

	// --------------------------------------------------------------
	// ---
	// --- Content listing (Test only)
	// ---
	// --------------------------------------------------------------
	public static void listFSContents(final FileItem dirItem) throws FSException {
		StringMaker b = StringMaker.obtain();
		b.append('\n');
		b.append(getSpace(dirItem));
		b.append('\n');
		dumpFSStructure(b, dirItem, 0);
		Logger.info(b.release());
	}

	static void dumpFSStructure(final StringMaker b, final FileItem dirItem, int indent) throws FSException {
		FileItem[] contents = dirItem.listItems();
		if (contents == null)
			return;
		for (FileItem item : contents) {
			final FileItemProperties properties = item.getProperties();
			if (properties.isFile()) {
				b.append(' ', indent);
				int x = b.length() + 31;
				b.append(item.getName());
				b.append(' ', Math.max(x - b.length(), 0));
				b.append(properties.lastModified(), dfFileItem);
				b.append(' ');
				b.append(properties.length(), Formats.BYTESIZE);
				b.append('\n');
			} else if (properties.isDirectory()) {
				b.append(' ', indent);
				int x = b.length() + 31;
				b.append(item.getName());
				b.append(' ', Math.max(x - b.length(), 0));
				b.append(properties.lastModified(), dfFileItem);
				b.append(" [DIR]\n");
				dumpFSStructure(b, item, indent + 2);
			}
		}
	}

	static String getSpace(final FileItem fileItem) {
		final StringMaker b = StringMaker.obtain();
		b.append("Free Space: ");
		b.append(fileItem.getFreeSpace(), Formats.BYTESIZE);
		b.append(" of ");
		b.append(fileItem.getTotalSpace(), Formats.BYTESIZE);
		return b.release();
	}

	// --------------------------------------------------------------
	// ---
	// --- Configuration access
	// ---
	// --------------------------------------------------------------
	public BlockSizeCode getContainerBlockSize() {
		return containerBlockSize;
	}

	public long getContainerBlockCount() {
		return containerBlockCount;
	}

	// --------------------------------------------------------------
	// ---
	// --- Configurable implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public void getConfigurationFileNames(final Collection<String> fileNames) {
		// no files
	}

	@Override
	public void configure(final Configuration configuration) throws ConfigurationException {
		containerFileName = configuration.getString("ContainerFile", (String) null);
		final int blockSizeId = configuration.getInteger("BlockSize", 11, 16, 12);
		containerBlockSize = BlockSizeCode.getById(blockSizeId, BlockSizeCode.class);
		containerBlockCount = configuration.getLong("BlockCount", 512, Long.MAX_VALUE, 1 << 24);
		cacheBuffers = configuration.getInteger("CacheBuffers", 16, Integer.MAX_VALUE, 1024);
	}

}

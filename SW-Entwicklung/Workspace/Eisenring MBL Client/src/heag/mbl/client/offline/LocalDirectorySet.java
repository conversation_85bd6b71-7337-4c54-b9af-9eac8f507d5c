package heag.mbl.client.offline;

import ch.eisenring.core.collections.Set;

/**
 * Provides the default directory structure for offline storage
 */
public interface LocalDirectorySet {

	public final static int VERSION = 6;

	public final static String ROOT = "sgfs://";
	public final static String UNSAVED = ROOT + "unsaved";
	public final static String LW = ROOT + "lw";
	public final static String LW_AUFTRAG = LW + "/auftrag";
	
	public final static String DMS = ROOT + "dms";
	public final static String DMS_VERSIONCACHE = ROOT + "dmsVersionCache";
	public final static String DMS_PROJECTSTRCUTURE = ROOT + "dmsProjectStructure";
	
	public final static String HUO = ROOT + "huo";
	public final static String HUO_CACHE = HUO + "cache";

	/**
	 * Pathes to be creates by default
	 */
	public final static Set<String> DEFAULT_DIRECTORIES = Set.asReadonlySet(
			UNSAVED,
			LW, LW_AUFTRAG,
			DMS, DMS_VERSIONCACHE, DMS_PROJECTSTRCUTURE,
			HUO, HUO_CACHE
		);

}
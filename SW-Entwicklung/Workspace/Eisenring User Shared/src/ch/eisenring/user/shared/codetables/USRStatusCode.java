package ch.eisenring.user.shared.codetables;

import ch.eisenring.core.codetype.StaticCode;
import ch.eisenring.core.resource.ImageResource;
import ch.eisenring.user.shared.resource.Images;

/**
 * User status
 */
public final class USRStatusCode extends StaticCode {

	public final static USRStatusCode ACTIVE = new USRStatusCode(
			0, 0, "A", "Aktiv",
			Images.USR_STATUS_ACTIVE);
	
	public final static USRStatusCode LOCKED = new USRStatusCode(
			1, 1, "G", "Gesperrt",
			Images.USR_STATUS_LOCKED);

	public final static USRStatusCode INACTIVE = new USRStatusCode(
			2, 2, "I", "Inaktiv",
			Images.USR_STATUS_INACTIVE);

	private final ImageResource icon;

	USRStatusCode(final int id, final Object key,
			      final String shortText, final String longText,
			      final ImageResource icon) {
		super(id, key, shortText, longText);
		this.icon = icon;
	}

	@Override
	public ImageResource getIcon() {
		return icon;
	}

	public boolean isActive() {
		return ACTIVE.equals(this);
	}

}

package ch.eisenring.user.shared.network;

import java.io.IOException;

import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.collections.Set;
import ch.eisenring.core.collections.impl.HashSet;
import ch.eisenring.core.io.StreamReader;
import ch.eisenring.core.io.StreamUtil;
import ch.eisenring.core.io.StreamWriter;
import ch.eisenring.user.shared.api.USRDeviceKey;

public final class PacketDevKeyAllReply extends PacketDevKeyBase {

	private Set<USRDeviceKey> allKeys = new HashSet<>();

	private PacketDevKeyAllReply() {
		super(CAPS_SEQUENCE_REPLY | CAPS_ERRORMESSAGE, PRIORITY_REPLY);
	}

	// --------------------------------------------------------------
	// ---
	// --- Factory methods
	// ---
	// --------------------------------------------------------------
	public static PacketDevKeyAllReply create(final PacketDevKeyAllRequest request, final ErrorMessage message) {
		final PacketDevKeyAllReply packet = new PacketDevKeyAllReply();
		packet.setReplyId(request, message);
		return packet;
	}

	// --------------------------------------------------------------
	// ---
	// --- Packet specific API
	// ---
	// --------------------------------------------------------------
	public void setAllKeys(final java.util.Collection<? extends USRDeviceKey> allKeys) throws Exception {
		this.allKeys.clear();
		this.allKeys.addAll(allKeys);
	}

	public Set<USRDeviceKey> getAllKeys() {
		return new HashSet<>(allKeys);
	}

	// --------------------------------------------------------------
	// ---
	// --- Streaming
	// ---
	// --------------------------------------------------------------
	@Override
	protected void readImpl(final StreamReader reader) throws IOException {
		super.readImpl(reader);
		StreamUtil.readCollection(reader, allKeys);
	}

	@Override
	protected void writeImpl(final StreamWriter writer) throws IOException {
		super.writeImpl(writer);
		StreamUtil.writeCollection(writer, allKeys);
	}

}

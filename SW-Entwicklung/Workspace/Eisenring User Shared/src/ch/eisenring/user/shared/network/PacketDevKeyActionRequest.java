package ch.eisenring.user.shared.network;

import java.io.IOException;

import ch.eisenring.core.io.StreamReader;
import ch.eisenring.core.io.StreamWriter;
import ch.eisenring.user.shared.api.USRDeviceKey;
import ch.eisenring.user.shared.codetables.DeviceKeyAction;

public final class PacketDevKeyActionRequest extends PacketDevKeyBase {

	private USRDeviceKey deviceKey;
	private DeviceKeyAction action;

	private PacketDevKeyActionRequest() {
		super(CAPS_SEQUENCE_REQUEST, PRIORITY_REQUEST);
	}

	// --------------------------------------------------------------
	// ---
	// --- Factory methods
	// ---
	// --------------------------------------------------------------
	public static PacketDevKeyActionRequest create(final USRDeviceKey deviceKey, final DeviceKeyAction action) {
		final PacketDevKeyActionRequest packet = new PacketDevKeyActionRequest();
		packet.deviceKey = deviceKey;
		packet.action = action;
		return packet;
	}

	// --------------------------------------------------------------
	// ---
	// --- Packet specific API
	// ---
	// --------------------------------------------------------------
	public DeviceKeyAction getAction() {
		return action;
	}
	
	public USRDeviceKey getDeviceKey() {
		return deviceKey;
	}

	// --------------------------------------------------------------
	// ---
	// --- Streaming
	// ---
	// --------------------------------------------------------------
	@Override
	protected void readImpl(final StreamReader reader) throws IOException {
		super.readImpl(reader);
		action = reader.readCode(DeviceKeyAction.class);
		deviceKey = (USRDeviceKey) reader.readObject();
	}

	@Override
	protected void writeImpl(final StreamWriter writer) throws IOException {
		super.writeImpl(writer);
		writer.writeCode(action);
		writer.writeObject(deviceKey);
	}
	
}

package ch.eisenring.user.shared;


/**
 * Interface to be implemented by an object that provides
 * the decision if the current user is granted the right.
 * 
 * A client application needs to set an implementation of this interface (using
 * setEvaluator) to make UserRight.isPermitted() respond as the application requires.
 */
public interface PermissionEvaluator {

	/**
	 * Returns true if the current user is granted the right
	 */
	public abstract boolean isPermitted(final Permission permission);

	/**
	 * Sets the active permission evaluator.
	 * A null argument causes a DENY ALL evaluator to be set. 
	 */
	public static void setEvaluator(final PermissionEvaluator evaluator) {
		PermissionEvaluatorPrivate.INSTANCE = evaluator == null
				? PermissionEvaluatorDenyAll.INSTANCE : evaluator;
	}

	/**
	 * Gets the active permission evaluator (never returns NULL)
	 */
	public static PermissionEvaluator getEvaluator() {
		return PermissionEvaluatorPrivate.INSTANCE;
	}

}

/**
 * Holds the currently active instance
 */
final class PermissionEvaluatorPrivate {
	
	static volatile PermissionEvaluator INSTANCE = PermissionEvaluatorDenyAll.INSTANCE;

}

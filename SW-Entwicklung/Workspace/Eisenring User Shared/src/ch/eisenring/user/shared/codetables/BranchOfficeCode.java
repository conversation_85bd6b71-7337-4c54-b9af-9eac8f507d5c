package ch.eisenring.user.shared.codetables;

import ch.eisenring.core.codetype.StaticCode;

public final class BranchOfficeCode extends StaticCode {

	public final static BranchOfficeCode NULL = new BranchOfficeCode(0, null, "", "");

	public final static BranchOfficeCode KUECHENBAU = new BranchOfficeCode(2501, "Küchenbau Sirnach");
	public final static BranchOfficeCode NATURSTEINWERK =  new BranchOfficeCode(2502, "Natursteinwerk Matzingen");
	public final static BranchOfficeCode SERVICECENTER =  new BranchOfficeCode(2503, "Servicecenter Münchwilen");
	public final static BranchOfficeCode BAUARENA =  new BranchOfficeCode(2504, "Bauarena Volketswil");

	public final static BranchOfficeCode AUSSENLAGER = new BranchOfficeCode(25012, "Aussenlager Münchwilen");
	public final static BranchOfficeCode OENSINGEN = new BranchOfficeCode(2505, "Niederlassung Oensingen");

	BranchOfficeCode(final int id, final String text) {
		this(id, Integer.valueOf(id), text, text);
	}

	BranchOfficeCode(final int id, final Object key,
					 final String shortText, final String longText) {
		super(id, key, shortText, longText);
	}

}

package ch.eisenring.user.shared.network;

public final class PacketDev<PERSON><PERSON><PERSON>llRequest extends PacketDevKeyBase {

	private PacketDevKeyAllRequest() {
		super(CAPS_SEQUENCE_REQUEST, PRIORITY_REQUEST);
	}

	// --------------------------------------------------------------
	// ---
	// --- Factory methods
	// ---
	// --------------------------------------------------------------
	public static PacketDevKeyAllRequest create() {
		return new PacketDevKeyAllRequest();
	}

	// --------------------------------------------------------------
	// ---
	// --- Packet specific API
	// ---
	// --------------------------------------------------------------

	// nothing
	
	// --------------------------------------------------------------
	// ---
	// --- Streaming
	// ---
	// --------------------------------------------------------------

	// nothing
	
}

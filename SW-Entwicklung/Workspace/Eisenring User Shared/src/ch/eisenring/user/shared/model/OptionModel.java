package ch.eisenring.user.shared.model;

import static ch.eisenring.user.shared.metamodel.USROptionMeta.ATR_APPLICATION;
import static ch.eisenring.user.shared.metamodel.USROptionMeta.ATR_DESCRIPTION;
import static ch.eisenring.user.shared.metamodel.USROptionMeta.ATR_DISPLAYNAME;
import ch.eisenring.model.MetaClass;
import ch.eisenring.model.Model;
import ch.eisenring.model.PrimaryKey;
import ch.eisenring.model.TransactionContext;
import ch.eisenring.user.shared.metamodel.USROptionMeta;

public final class OptionModel extends Model {

	public final static MetaClass METACLASS = USROptionMeta.METACLASS;
	
	public OptionModel(final TransactionContext context,
		          final PrimaryKey pk) {
		super(context, pk);
	}

	public String getDisplayname() {
		return (String) ATR_DISPLAYNAME.getMember(this);
	}

	public String getApplicationname() {
		return (String) ATR_APPLICATION.getMember(this);
	}
	
	public String getDescription() {
		return (String) ATR_DESCRIPTION.getMember(this);
	}

}

package ch.eisenring.user.shared.codetables;

import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.codetype.StaticCode;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.user.shared.Permission;
import ch.eisenring.user.shared.PermissionEvaluator;

/**
 * Abstract base class specifically for application defined
 * user rights. Each application or application module should
 * inherit its own (static) right codes from this class.
 * The only things to look out for is that the id must be unique
 * and that the right should be inserted into the database.
 */
public abstract class RightCode extends StaticCode implements Permission {

	private static class PrivateRightCode extends RightCode {
		PrivateRightCode(final int id) {
			super(id, Strings.concat("Private Right Code #", id));
		}
	}

	protected RightCode(final int id,
			            final String text) {
		super(id, Integer.valueOf(id), text, text);
	}

	@Override
	public final Class<? extends AbstractCode> getTypeClass() {
		return RightCode.class;
	}

	// --------------------------------------------------------------
	// ---
	// --- Permission query helper
	// ---
	// --------------------------------------------------------------
	/**
	 * Gets a RightCode by id.
	 * The returned code will be only suitable for permission evaluation,
	 * but not for display.
	 */
	public static RightCode getById(final int id) {
		RightCode code;
		synchronized (PrivateRightCode.class) {
			code = (RightCode) AbstractCode.getById(id, RightCode.class);
			if (code == null) {
				code = new PrivateRightCode(id);
			}
		}
		return code;
	}
	
	// --------------------------------------------------------------
	// ---
	// --- Permission evaluation against current user
	// ---
	// --------------------------------------------------------------
	/**
	 * Returns true if the current user has this permission.
	 */
	public final boolean isPermitted() {
		return PermissionEvaluator.getEvaluator().isPermitted(this);
	}

	// --------------------------------------------------------------
	// ---
	// --- Permission implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public final String getDisplayname() {
		return getText();
	}

	
}

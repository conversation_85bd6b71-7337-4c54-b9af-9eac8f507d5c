package ch.eisenring.user.shared.model;

import static ch.eisenring.user.shared.metamodel.USRRightMeta.ATR_APPLICATION;
import static ch.eisenring.user.shared.metamodel.USRRightMeta.ATR_CATEGORY;
import static ch.eisenring.user.shared.metamodel.USRRightMeta.ATR_DESCRIPTION;
import static ch.eisenring.user.shared.metamodel.USRRightMeta.ATR_DISPLAYNAME;
import static ch.eisenring.user.shared.metamodel.USRRightMeta.REL_ROLES;

import java.util.Iterator;

import ch.eisenring.core.collections.List;
import ch.eisenring.model.MetaClass;
import ch.eisenring.model.Model;
import ch.eisenring.model.PrimaryKey;
import ch.eisenring.model.TransactionContext;
import ch.eisenring.user.shared.Permission;
import ch.eisenring.user.shared.PermissionEvaluator;
import ch.eisenring.user.shared.api.USRRight;
import ch.eisenring.user.shared.metamodel.USRRightMeta;

public class RightModel extends Model implements USRRight, Permission {

	public final static MetaClass METACLASS = USRRightMeta.METACLASS;
	
	public RightModel(final TransactionContext context,
			     final PrimaryKey pk) {
		super(context, pk);
	}
	
	// --------------------------------------------------------------
	// ---
	// --- Permission implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public int getId() {
		// this works because the key class uses the key value as hash code
		return getPrimaryKey().hashCode();
	}

	@Override
	public boolean isPermitted() {
		return PermissionEvaluator.getEvaluator().isPermitted(this);
	}

	// --------------------------------------------------------------
	// ---
	// --- Role relation access
	// ---
	// --------------------------------------------------------------
	public void addRole(final RoleModel role) {
		REL_ROLES.add(this, role);
	}
	
	public void removeRole(final RoleModel role) {
		REL_ROLES.remove(this, role);
	}
	
	@SuppressWarnings("unchecked")
	public Iterator<RoleModel> getRoleIterator() {
		return (Iterator) REL_ROLES.iterator(this);
	}
	
	@SuppressWarnings("unchecked")
	public List<RoleModel> getRoles() {
		return (List) REL_ROLES.list(this);
	}

	// --------------------------------------------------------------
	// ---
	// --- Getters & Setters
	// ---
	// --------------------------------------------------------------
	@Override
	public String getDisplayname() {
		return (String) ATR_DISPLAYNAME.getMember(this);
	}
	
	public void setDisplayname(final String displayname) {
		ATR_DISPLAYNAME.setMember(this, displayname);
	}
	
	@Override
	public String getApplication() {
		return (String) ATR_APPLICATION.getMember(this);
	}
	
	public void setApplication(final String application) {
		ATR_APPLICATION.setMember(this, application);
	}

	@Override
	public String getCategory() {
		return (String) ATR_CATEGORY.getMember(this);
	}

	public void setCategory(final String category) {
		ATR_CATEGORY.setMember(this, category);
	}
	
	@Override
	public String getDescription() {
		return (String) ATR_DESCRIPTION.getMember(this);
	}
	
	public void setDescription(final String description) {
		ATR_DESCRIPTION.setMember(this, description);
	}

}

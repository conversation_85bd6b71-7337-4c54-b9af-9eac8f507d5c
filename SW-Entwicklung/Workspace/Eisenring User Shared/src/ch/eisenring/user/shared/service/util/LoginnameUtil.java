package ch.eisenring.user.shared.service.util;

import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.Strings;

public abstract class LoginnameUtil {

	protected LoginnameUtil() {
	}

	/**
	 * Converts login name to canonical form (a-z converted to upper case).
	 */
	public static String getCanonicLoginname(final CharSequence loginName) {
		if (Strings.isEmpty(loginName))
			return null;
		final int length = loginName.length();
		final StringMaker b = StringMaker.obtain(length);
		for (int i=0; i<length; ++i) {
			char c = loginName.charAt(i);
			if (c >= 'a' && c <= 'z') {
				// to upper case (ASII only)
				c -= 32;
			}
			b.append(c);
		}
		return b.release();
	}

}

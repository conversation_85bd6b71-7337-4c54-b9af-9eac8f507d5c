package ch.eisenring.user.shared.codetables;

import ch.eisenring.core.codetype.StaticCode;

/**
 * Client device states
 */
public final class DeviceStatusCode extends StaticCode {

	public final static DeviceStatusCode NULL = new DeviceStatusCode(
			0, null, "", "");

	/**
	 * Indicates the device is not known
	 */
	public final static DeviceStatusCode UNREGISTERED = new DeviceStatusCode(
			1, "Unregistered");

	/**
	 * Indicates the device is known but not yet activated
	 */
	public final static DeviceStatusCode REGISTERED = new DeviceStatusCode(
			2, "Registered");

	/**
	 * Indicates the device is known and activated
	 */
	public final static DeviceStatusCode ACTIVATED = new DeviceStatusCode(
			3, "Activated");

	/**
	 * Indicates the device is known but has been shut out
	 */
	public final static DeviceStatusCode LOCKED = new DeviceStatusCode(
			4, "Locked");

	/**
	 * Indicates the device is known but marked for disposal
	 */
	public final static DeviceStatusCode POISONED = new DeviceStatusCode(
			5, "Poisoned");

	/**
	 * Indicates the device is known and has been killed
	 */
	public final static DeviceStatusCode DECEASED = new DeviceStatusCode(
			6, "Deceased");

	private DeviceStatusCode(final int id, final String text) {
		super(id, Integer.valueOf(id), text, text);
	}

	private DeviceStatusCode(final int id, final Object key,
			final String shortText, final String longText) {
		super(id, key, shortText, longText);
	}

	// --------------------------------------------------------------
	// ---
	// --- Extra API
	// ---
	// --------------------------------------------------------------
	public boolean isActive() {
		return ACTIVATED.equals(this);
	}

}
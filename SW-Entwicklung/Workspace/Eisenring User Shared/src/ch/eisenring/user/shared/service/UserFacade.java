package ch.eisenring.user.shared.service;

import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.crypt.password.PasswordMethodCode;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.sort.Comparator;
import ch.eisenring.user.shared.codetables.USRStatusCode;
import ch.eisenring.user.shared.codetables.UserTackOnCode;

/**
 * User interface exposed to public through UserService.
 * Unlike the user object used internally these appear
 * read only and are very lightweight.
 */
public interface UserFacade extends ObjectFacade {
	
	/**
	 * Gets the full name of this user.
	 */
	public String getFullname();

	/**
	 * Gets the first name of this user.
	 */
	public String getFirstname();

	/**
	 * Gets the last name of this user.
	 */
	public String getLastname();

	/**
	 * Gets the login name of this user.
	 */
	public String getLoginname();

	/**
	 * Gets the EMail address of this user.
	 */
	public String getEMailAddress();

	/**
	 * Gets the code value for tacked on code
	 */
	public Object getTackOnCode(final UserTackOnCode code);

	/**
	 * Gets the password hash for user
	 */
	public String getPasswordhash();

	/**
	 * Gets the salt for user
	 */
	public String getPasswordsalt();
	
	/**
	 * Gets the password method type
	 */
	public PasswordMethodCode getPasswordtype();
	
	/**
	 * Gets the user status code
	 */
	public USRStatusCode getStatusCode();

	/**
	 * Returns true if this user can log in
	 */
	public boolean isLoginAllowed();

	// --------------------------------------------------------------
	// ---
	// --- Ordering
	// ---
	// --------------------------------------------------------------
	public static class Order extends ObjectFacade.Order {
		public final static Comparator<UserFacade> LoginName = new Comparator<UserFacade>() {
			@Override
			public int compare(final UserFacade o1, final UserFacade o2) {
				return Strings.compareIgnoreCase(o1.getLoginname(), o2.getLoginname());
			}
		};
		
		public final static Comparator<UserFacade> FirstName = new Comparator<UserFacade>() {
			@Override
			public int compare(final UserFacade o1, final UserFacade o2) {
				return Strings.compareIgnoreCase(o1.getFirstname(), o2.getFirstname());
			}
		};

		public final static Comparator<UserFacade> LastName = new Comparator<UserFacade>() {
			@Override
			public int compare(final UserFacade o1, final UserFacade o2) {
				return Strings.compareIgnoreCase(o1.getLastname(), o2.getLastname());
			}
		};

		public final static Comparator<UserFacade> EMail = new Comparator<UserFacade>() {
			@Override
			public int compare(final UserFacade o1, final UserFacade o2) {
				return Strings.compareIgnoreCase(o1.getEMailAddress(), o2.getEMailAddress());
			}
		};

		public final static Comparator<UserFacade> RowId = new Comparator<UserFacade>() {
			@Override
			public int compare(final UserFacade o1, final UserFacade o2) {
				return compareUnsigned(o1.getRowId(), o2.getRowId());
			}
		};

		public final static Comparator<UserFacade> StatusCode = new Comparator<UserFacade>() {
			@Override
			public int compare(final UserFacade o1, final UserFacade o2) {
				return AbstractCode.Order.Id.compare(o1.getStatusCode(), o2.getStatusCode());
			}
		};
	}
	
}

package ch.eisenring.user.shared.model;

import static ch.eisenring.user.shared.metamodel.USRUserMeta.ATR_FIRSTNAME;
import static ch.eisenring.user.shared.metamodel.USRUserMeta.ATR_LASTNAME;
import static ch.eisenring.user.shared.metamodel.USRUserMeta.ATR_LOGINNAME;
import static ch.eisenring.user.shared.metamodel.USRUserMeta.ATR_PASSWORDHASH;
import static ch.eisenring.user.shared.metamodel.USRUserMeta.ATR_PASSWORDSALT;
import static ch.eisenring.user.shared.metamodel.USRUserMeta.ATR_PASSWORDTYPE;
import static ch.eisenring.user.shared.metamodel.USRUserMeta.ATR_SIGNATURE_HTML;
import static ch.eisenring.user.shared.metamodel.USRUserMeta.ATR_SIGNATURE_PLAIN;
import static ch.eisenring.user.shared.metamodel.USRUserMeta.ATR_STATUSCODE;
import static ch.eisenring.user.shared.metamodel.USRUserMeta.ATR_TACKONVALUES;
import static ch.eisenring.user.shared.metamodel.USRUserMeta.REL_OPTIONS;
import static ch.eisenring.user.shared.metamodel.USRUserMeta.REL_ROLES;

import java.util.Iterator;

import ch.eisenring.core.StringUtil;
import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.crypt.CryptUtil;
import ch.eisenring.core.crypt.Hash;
import ch.eisenring.core.crypt.password.PasswordMethodCode;
import ch.eisenring.core.datatypes.encoded.ByteEncoding;
import ch.eisenring.core.datatypes.primitives.Primitives;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.valueset.NamedValue;
import ch.eisenring.core.valueset.ValueSet;
import ch.eisenring.model.MetaClass;
import ch.eisenring.model.Model;
import ch.eisenring.model.PrimaryKey;
import ch.eisenring.model.TransactionContext;
import ch.eisenring.user.shared.Permission;
import ch.eisenring.user.shared.PermissionHolder;
import ch.eisenring.user.shared.codetables.BranchOfficeCode;
import ch.eisenring.user.shared.codetables.USRStatusCode;
import ch.eisenring.user.shared.codetables.UserTackOnCode;
import ch.eisenring.user.shared.metamodel.USRUserMeta;
import ch.eisenring.user.shared.service.UserFacade;

public final class UserModel extends Model implements UserFacade, PermissionHolder {

	public final static MetaClass METACLASS = USRUserMeta.METACLASS;
	
	private final static String KEY_DISPONENT_CODE = UserTackOnCode.DISPONENT.getKey().toString();
	private final static String KEY_OBJEKTBETREUER_CODE = UserTackOnCode.OBJEKTBETREUER.getKey().toString();
	private final static String KEY_KUNDENBERATER_CODE = UserTackOnCode.KUNDENBERATER.getKey().toString();
	private final static String KEY_KUNDENBERATERHISTORY_CODE = UserTackOnCode.KUNDENBERATER_HISTORY.getKey().toString();
	private final static String KEY_SUBJEKT_ID = UserTackOnCode.SUBJEKT.getKey().toString();
	private final static String KEY_ADMINCOMMENT = "AdminComment";
	private final static String KEY_EMAILADDRESS = "EMailAddress";
	private final static String KEY_PHONE_MOBILE_BUSINESS = "PhoneMobileBusiness";
	private final static String KEY_PHONE_FIXNET_BUSINESS = "PhoneFixnetBusiness";
	private final static String KEY_PHONE_RECEPTION_BUSINESS = "PhoneReceptionBusiness";
	private final static String KEY_PHONE_FAX_BUSINESS = "PhoneFaxBusiness";
	private final static String KEY_BUSINESS_ROLE1 = "BusinessRole1";
	private final static String KEY_BUSINESS_ROLE2 = "BusinessRole2";
	private final static String KEY_BRANCHOFFICE = "BranchOffice";
	private final static String KEY_PRESENTOID = "PresentoId";
	
	public UserModel(final TransactionContext context,
				final PrimaryKey pk) {
		super(context, pk);
	}

	// --------------------------------------------------------------
	// ---
	// --- Option access
	// ---
	// --------------------------------------------------------------
	@SuppressWarnings("unchecked")
	public boolean isOptionSelected(final int optionId) {
		synchronized (context.getLock()) {
			final Iterator<OptionModel> optionItr = (Iterator) REL_OPTIONS.iterator(this);
			while (optionItr.hasNext()) {
				final OptionModel option = optionItr.next();
				final Integer pk = (Integer) option.getPrimaryKey().getKeyValue();
				if (pk.intValue() == optionId)
					return true;
			}
		}
		return false;
	}

	// --------------------------------------------------------------
	// ---
	// --- PermissionHolder implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public boolean isPermitted(final Permission permission) {
		if (permission == null || !isLoginAllowed())
			return false;
		final Iterator<RoleModel> i = getRoleIterator();
		while (i.hasNext()) {
			final RoleModel role = i.next();
			if (role.isPermitted(permission)) {
				return true;
			}
		}
		return false;
	}

	@Override
	public boolean isLoginAllowed() {
		return USRStatusCode.ACTIVE.equals(getStatusCode());
	}

	// --------------------------------------------------------------
	// ---
	// --- Role relation access
	// ---
	// --------------------------------------------------------------
	public void addRole(final RoleModel role) {
		REL_ROLES.add(this, role);
	}
	
	public void removeRole(final RoleModel role) {
		REL_ROLES.remove(this, role);
	}

	@SuppressWarnings("unchecked")
	public Iterator<RoleModel> getRoleIterator() {
		return (Iterator) REL_ROLES.iterator(this);
	}

	@SuppressWarnings("unchecked")
	public List<RoleModel> getRoles() {
		return (List) REL_ROLES.list(this);
	}

	// --------------------------------------------------------------
	// ---
	// --- Derived helpers
	// ---
	// --------------------------------------------------------------
	public String getFirstnameUpper() {
		return Strings.toUpper(getFirstname());
	}

	public String getLastnameUpper() {
		return Strings.toUpper(getLastname());
	}

	public String getEMailAddressLower() {
		return Strings.toLower(getEMailAddress());
	}

	public String getPhoneBlock1() {
		return StringUtil.concat(getPhoneFaxBusiness(), "\n", getPhoneReceptionBusiness());
	}

	public String getPhoneBlock2() {
		return StringUtil.concat(getPhoneMobileBusiness(), "\n", getPhoneFixnetBusiness());
	}

	@Override
	public Object getTackOnCode(final UserTackOnCode code) {
		if (UserTackOnCode.DISPONENT.equals(code)) {
			return getDisponentCode();
		} else if (UserTackOnCode.OBJEKTBETREUER.equals(code)) {
			return getObjektbetreuerCode();
		} else if (UserTackOnCode.KUNDENBERATER.equals(code)) {
			return getKundenberaterCode();
		} else if (UserTackOnCode.SUBJEKT.equals(code)) {
			return getSubjektId();
		} else if (UserTackOnCode.KUNDENBERATER_HISTORY.equals(code)) {
			return getKundenberaterCodeHistory();
		}
		// code is invalid or not supported
		return null;
	}

	// --------------------------------------------------------------
	// ---
	// --- Tack-on Attribute access
	// ---
	// --------------------------------------------------------------
	/**
	 * Gets the tack on value set.
	 * This will lazily create the value set.
	 */
	private ValueSet getTackOnValueSet() {
		final String string = (String) ATR_TACKONVALUES.getMember(this);
		return ValueSet.fromString(string);
	}

	/**
	 * Call this method to add/update values in the tacked on set.
	 * The call will update the set and if required also the underlying
	 * string blob used to store the set.
	 */
	private void setTackOnValue(final NamedValue<?> value) {
		final ValueSet valueSet = getTackOnValueSet();
		if (valueSet.add(value)) {
			ATR_TACKONVALUES.setMember(this, valueSet.toString());
		}
	}
	
	/**
	 * Gets the logiware subjekt-id of this user
	 */
	public Integer getSubjektId() {
		final int result = getTackOnValueSet().getInt(KEY_SUBJEKT_ID, 0);
		return result <= 0 ? null : Integer.valueOf(result);
	}

	/**
	 * Sets the logiware subjekt-id of this user
	 */
	public void setSubjektId(final Integer subjektId) {
		final Integer i = subjektId == null || subjektId.intValue() <= 0 ? (Integer) null : subjektId;
		setTackOnValue(new NamedValue<Integer>(KEY_SUBJEKT_ID, i));
	}

	/**
	 * Gets the logiware disponent code of this user
	 */
	public String getDisponentCode() {
		return getTackOnValueSet().getString(KEY_DISPONENT_CODE, (String) null);
	}
	
	/**
	 * Sets the logiware disponent code of this user
	 */
	public void setDisponentCode(final String disponentCode) {
		setTackOnValue(new NamedValue<String>(KEY_DISPONENT_CODE, disponentCode));
	}

	/**
	 * Gets the logiware objektbetreuer code of this user
	 */
	public String getObjektbetreuerCode() {
		return getTackOnValueSet().getString(KEY_OBJEKTBETREUER_CODE, (String) null);
	}
	
	/**
	 * Sets the logiware objektbetreuer code of this user
	 */
	public void setObjektbetreuerCode(final String objektbetreuerCode) {
		setTackOnValue(new NamedValue<String>(KEY_OBJEKTBETREUER_CODE, objektbetreuerCode));
	}

	/**
	 * Gets the Logiware KundenberaterCode of this user
	 */
	public String getKundenberaterCode() {
		return getTackOnValueSet().getString(KEY_KUNDENBERATER_CODE, (String) null);
	}

	/**
	 * Sets the Logiware KundenberaterCode of this user
	 */
	public void setKundenberaterCode(final String kundenberaterCode) {
		setTackOnValue(new NamedValue<String>(KEY_KUNDENBERATER_CODE, kundenberaterCode));
	}

	/**
	 * Gets the Logiware KundenberaterCode of this user (History)
	 */
	public String getKundenberaterCodeHistory() {
		return getTackOnValueSet().getString(KEY_KUNDENBERATERHISTORY_CODE, (String) null);
	}

	/**
	 * Sets the Logiware KundenberaterCode of this user (History)
	 */
	public void setKundenberaterCodeHistory(final String kundenberaterCodeHistory) {
		setTackOnValue(new NamedValue<String>(KEY_KUNDENBERATERHISTORY_CODE, kundenberaterCodeHistory));
	}

	/**
	 * Gets the administrative comment
	 */
	public String getAdminComment() {
		return getTackOnValueSet().getString(KEY_ADMINCOMMENT, (String) null);
	}
	
	/**
	 * Sets the administrative comment
	 */
	public void setAdminComment(final String adminComment) {
		setTackOnValue(new NamedValue<String>(KEY_ADMINCOMMENT, adminComment));
	}

	/**
	 * Gets the EMail-address
	 */
	@Override
	public String getEMailAddress() {
		return getTackOnValueSet().getString(KEY_EMAILADDRESS, (String) null);
	}
	
	/**
	 * Sets the EMail-address
	 */
	public void setEMailAddress(final String emailAddress) {
		setTackOnValue(new NamedValue<String>(KEY_EMAILADDRESS, emailAddress));
	}

	/**
	 * Gets the business phone number of user
	 */
	public String getPhoneFixnetBusiness() {
		return getTackOnValueSet().getString(KEY_PHONE_FIXNET_BUSINESS, (String) null);
	}
	
	/**
	 * Sets the business phone number of user
	 */
	public void setPhoneFixnetBusiness(final String phone) {
		setTackOnValue(new NamedValue<String>(KEY_PHONE_FIXNET_BUSINESS, phone));
	}

	/**
	 * Gets the business mobile number of user
	 */
	public String getPhoneMobileBusiness() {
		return getTackOnValueSet().getString(KEY_PHONE_MOBILE_BUSINESS, (String) null);
	}
	
	/**
	 * Sets the business mobile number of user
	 */
	public void setPhoneMobileBusiness(final String phone) {
		setTackOnValue(new NamedValue<String>(KEY_PHONE_MOBILE_BUSINESS, phone));
	}

	/**
	 * Gets the business phone number of user
	 */
	public String getPhoneFaxBusiness() {
		return getTackOnValueSet().getString(KEY_PHONE_FAX_BUSINESS, (String) null);
	}
	
	/**
	 * Sets the business phone number of user
	 */
	public void setPhoneFaxBusiness(final String phone) {
		setTackOnValue(new NamedValue<String>(KEY_PHONE_FAX_BUSINESS, phone));
	}

	/**
	 * Gets the business phone number of user
	 */
	public String getPhoneReceptionBusiness() {
		return getTackOnValueSet().getString(KEY_PHONE_RECEPTION_BUSINESS, (String) null);
	}
	
	/**
	 * Sets the business phone number of user
	 */
	public void setPhoneReceptionBusiness(final String phone) {
		setTackOnValue(new NamedValue<String>(KEY_PHONE_RECEPTION_BUSINESS, phone));
	}

	/**
	 * Gets the business role #1 of the user
	 */
	public String getBusinessRole1() {
		return getTackOnValueSet().getString(KEY_BUSINESS_ROLE1, (String) null);
	}

	/**
	 * Sets the business role #1 of the user
	 */
	public void setBusinessRole1(final String role) {
		setTackOnValue(new NamedValue<String>(KEY_BUSINESS_ROLE1, role));
	}

	/**
	 * Gets the business role #2 of the user
	 */
	public String getBusinessRole2() {
		return getTackOnValueSet().getString(KEY_BUSINESS_ROLE2, (String) null);
	}

	/**
	 * Sets the business role #2 of the user
	 */
	public void setBusinessRole2(final String role) {
		setTackOnValue(new NamedValue<String>(KEY_BUSINESS_ROLE2, role));
	}

	/**
	 * Gets the branch office the user belongs to
	 */
	public BranchOfficeCode getBranchOffice() {
		final int id = (Integer) getTackOnValueSet().get(KEY_BRANCHOFFICE, Primitives.INT0);
		return (BranchOfficeCode) AbstractCode.getById(id, BranchOfficeCode.class);
	}

	/**
	 * Sets the branch office the user belongs to
	 */
	public void setBranchOffice(final BranchOfficeCode code) {
		final int id  = AbstractCode.getId(code, 0);
		setTackOnValue(new NamedValue<Integer>(KEY_BRANCHOFFICE, Integer.valueOf(id)));
	}

	/**
	 * Gets the presento id of this user
	 */
	public Integer getPresentoId() {
		final Object id = getTackOnValueSet().get(KEY_PRESENTOID, (Integer) null); 
		return id instanceof Number ? ((Number) id).intValue() : null;
	}

	/**
	 * Sets the presento id of this user
	 */
	public void setPresentoId(final Integer presentoId) {
		setTackOnValue(new NamedValue<Integer>(KEY_PRESENTOID, presentoId));
	}

	// --------------------------------------------------------------
	// ---
	// --- Getters & Setters
	// ---
	// --------------------------------------------------------------
	public String getLoginname() {
		return (String) ATR_LOGINNAME.getMember(this);
	}
	
	public void setLoginname(final String loginname) {
		ATR_LOGINNAME.setMember(this, loginname);
	}

	public String getFirstname() {
		return (String) ATR_FIRSTNAME.getMember(this);
	}
	
	public void setFirstname(final String firstname) {
		ATR_FIRSTNAME.setMember(this, firstname);
	}
	
	public String getLastname() {
		return (String) ATR_LASTNAME.getMember(this);
	}
	
	public void setLastname(final String lastname) {
		ATR_LASTNAME.setMember(this, lastname);
	}

	@Override
	public String getPasswordhash() {
		return (String) ATR_PASSWORDHASH.getMember(this);
	}
	
	public void setPasswordhash(final String hash) {
		ATR_PASSWORDHASH.setMember(this, hash);
	}

	@Override
	public String getPasswordsalt() {
		return (String) ATR_PASSWORDSALT.getMember(this);
	}
	
	public void setPasswordsalt(final String salt) {
		ATR_PASSWORDSALT.setMember(this, salt);
	}

	@Override
	public PasswordMethodCode getPasswordtype() {
		return (PasswordMethodCode) ATR_PASSWORDTYPE.getMember(this); 
	}

	public void setPasswordtype(final PasswordMethodCode type) {
		ATR_PASSWORDTYPE.setMember(this, type);
	}

	public String getSignatureHTML() {
		return (String) ATR_SIGNATURE_HTML.getMember(this);
	}

	public void setSignatureHTML(final String signature) {
		ATR_SIGNATURE_HTML.setMember(this, signature);
	}

	public String getSignaturePlain() {
		return (String) ATR_SIGNATURE_PLAIN.getMember(this);
	}

	public void setSignaturePlain(final String signature) {
		ATR_SIGNATURE_PLAIN.setMember(this, signature);
	}

	@Override
	public USRStatusCode getStatusCode() {
		return (USRStatusCode) ATR_STATUSCODE.getMember(this);
	}

	public void setStatusCode(final USRStatusCode statusCode) {
		ATR_STATUSCODE.setMember(this, statusCode);
	}

	// --------------------------------------------------------------
	// ---
	// --- Utility methods
	// ---
	// --------------------------------------------------------------
	public void setPassword(final char[] password, final PasswordMethodCode method) throws Exception {
		if (password == null || password.length == 0) {
			setPasswordhash(null);
			setPasswordsalt(null);
			setPasswordtype(method);
		} else {
			// generate salt (if needed)
			final byte[] salt;
			if (method.isSalted()) {
				salt = CryptUtil.getRandomBytes(81, 121);
			} else {
				salt = null;
			}
			// generate password hash
			final Hash hash = method.getPasswordHash(password, salt);
			setPasswordtype(method);
			setPasswordsalt(ByteEncoding.BASE64.encode(salt, true));
			setPasswordhash(ByteEncoding.BASE64.encode(hash.toByteArray(), true));
		}
	}

	// --------------------------------------------------------------
	// ---
	// --- UserFacade implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public long getRowId() {
		return ((Number) getPrimaryKey().getKeyValue()).longValue();
	}

	@Override
	public String getDisplayName() {
		final StringMaker b = StringMaker.obtain();
		b.append(getLastname());
		final String f = getFirstname();
		if (!Strings.isEmpty(f)) {
			b.append(", ");
			b.append(f);
		}
		if (!isLoginAllowed())
			b.append("*");
		return b.release();
	}

	@Override
	public String getFullname() {
		final String firstname = getFirstname();
		final StringMaker b = StringMaker.obtain(60);
		if (!Strings.isEmpty(firstname)) {
			b.append(firstname);
		}
		final String lastname = getLastname();
		if (!Strings.isEmpty(lastname)) {
			if (b.length() > 0)
				b.append(' ');
			b.append(lastname);
		}
		return b.release((String) null);
	}

	
}

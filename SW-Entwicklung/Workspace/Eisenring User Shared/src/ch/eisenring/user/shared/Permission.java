package ch.eisenring.user.shared;

/**
 * Common interface for right objects
 */
public interface Permission {

	/**
	 * Gets the id that identifies this permission.
	 * This in an int value that is unique among
	 * all permissions.
	 */
	public int getId();
	
	/**
	 * Checks if the given permission holder has this permission.
	 */
	public default boolean isPermitted(final PermissionHolder user) {
		return user == null ? false : user.isPermitted(this);		
	}

	/**
	 * Checks if the default permission holder has this permission.
	 */
	public boolean isPermitted();

	/**
	 * Gets display name
	 */
	public String getDisplayname();

}

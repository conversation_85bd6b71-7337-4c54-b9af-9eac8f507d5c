package ch.eisenring.user.shared.network;

import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.io.AutoStreamed;

public class PacketCheckLoginNameRequest extends AbstractUSRPacket {

	@AutoStreamed
	private String loginName;

	private PacketCheckLoginNameRequest() {
		super(CAPS_SEQUENCE_REQUEST, PRIORITY_REQUEST);
	}
	
	// --------------------------------------------------------------
	// ---
	// --- Factory methods
	// ---
	// --------------------------------------------------------------
	public static PacketCheckLoginNameRequest create(final CharSequence loginName) {
		final PacketCheckLoginNameRequest packet = new PacketCheckLoginNameRequest();
		packet.loginName = Strings.toString(loginName);
		return packet;
	}

	// --------------------------------------------------------------
	// ---
	// --- API
	// ---
	// --------------------------------------------------------------
	public String getLoginName() {
		return loginName;
	}
	
}

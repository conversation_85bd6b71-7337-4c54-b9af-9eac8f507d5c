package ch.eisenring.user.shared.model;

import static ch.eisenring.user.shared.metamodel.USRRoleMeta.ATR_DISPLAYNAME;
import static ch.eisenring.user.shared.metamodel.USRRoleMeta.REL_RIGHTS;
import static ch.eisenring.user.shared.metamodel.USRRoleMeta.REL_USERS;

import java.util.Iterator;

import ch.eisenring.core.collections.List;
import ch.eisenring.model.MetaClass;
import ch.eisenring.model.Model;
import ch.eisenring.model.PrimaryKey;
import ch.eisenring.model.TransactionContext;
import ch.eisenring.user.shared.Permission;
import ch.eisenring.user.shared.PermissionHolder;
import ch.eisenring.user.shared.metamodel.USRRoleMeta;
import ch.eisenring.user.shared.service.RoleFacade;
import ch.eisenring.user.shared.service.UserFacade;

public final class RoleModel extends Model implements RoleFacade, PermissionHolder {

	public final static MetaClass METACLASS = USRRoleMeta.METACLASS;
	
	public RoleModel(final TransactionContext context,
				final PrimaryKey pk) {
		super(context, pk);
	}

	// --------------------------------------------------------------
	// ---
	// --- Attributes
	// ---
	// --------------------------------------------------------------
	@Override
	public String getDisplayName() {
		return (String) ATR_DISPLAYNAME.getMember(this);
	}
	
	public void setDisplayname(final String displayname) {
		ATR_DISPLAYNAME.setMember(this, displayname);
	}

	// --------------------------------------------------------------
	// ---
	// --- Permission (derived relation)
	// ---
	// --------------------------------------------------------------
	@Override
	public boolean isPermitted(final Permission permission) {
		if (permission == null)
			return false;
		final int id = permission.getId();
		final Iterator<RightModel> i = getRightIterator();
		while (i.hasNext()) {
			final RightModel right = i.next();
			if (right.getId() == id)
				return true;
		}
		return false;
	}

	// --------------------------------------------------------------
	// ---
	// --- User relation access
	// ---
	// --------------------------------------------------------------
	public void addUser(final UserModel user) {
		REL_USERS.add(this, user);
	}
	
	public void removeUser(final UserModel user) {
		REL_USERS.remove(this, user);
	}

	@SuppressWarnings("unchecked")
	public Iterator<UserModel> getUserIterator() {
		return (Iterator) REL_USERS.iterator(this);
	}

	@SuppressWarnings("unchecked")
	public List<UserModel> getUsers() {
		return (List) REL_USERS.list(this);
	}

	// --------------------------------------------------------------
	// ---
	// --- Right relation access
	// ---
	// --------------------------------------------------------------
	public void addRight(final RightModel right) {
		REL_RIGHTS.add(this, right);
	}
	
	public void removeRight(final RightModel right) {
		REL_RIGHTS.remove(this, right);
	}
	
	@SuppressWarnings("unchecked")
	public Iterator<RightModel> getRightIterator() {
		return (Iterator) REL_RIGHTS.iterator(this);
	}

	@SuppressWarnings("unchecked")
	public List<RightModel> getRights() {
		return (List) REL_RIGHTS.list(this);
	}

	// --------------------------------------------------------------
	// ---
	// --- RoleFacade implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public long getRowId() {
		return ((Number) getPrimaryKey().getKeyValue()).longValue();
	}

	@Override
	public boolean isMember(final UserFacade user) {
		if (user == null)
			return false;
		final long rowId = user.getRowId();
		final Model[] users = REL_USERS.toArray(this);
		for (final Model model : users) {
			final long modelId = ((Number) model.getPrimaryKey().getKeyValue()).longValue();
			if (modelId == rowId)
				return true;
		}
		return false;
	}

}

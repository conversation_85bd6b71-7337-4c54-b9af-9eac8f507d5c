package ch.eisenring.user.shared.network;

import java.io.IOException;

import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.io.StreamReader;
import ch.eisenring.core.io.StreamWriter;
import ch.eisenring.user.shared.api.USRDeviceKey;
import ch.eisenring.user.shared.codetables.DeviceKeyAction;

public final class PacketDevKeyActionReply extends PacketDevKeyBase {

	private USRDeviceKey deviceKey;
	private DeviceKeyAction action;

	private PacketDevKeyActionReply() {
		super(CAPS_SEQUENCE_REPLY | CAPS_ERRORMESSAGE, PRIORITY_REPLY);
	}

	// --------------------------------------------------------------
	// ---
	// --- Factory methods
	// ---
	// --------------------------------------------------------------
	public static PacketDevKeyActionReply create(final PacketDevKeyActionRequest request, final ErrorMessage message) {
		final PacketDevKeyActionReply packet = new PacketDevKeyActionReply();
		packet.setReplyId(request, message);
		packet.deviceKey = request.getDeviceKey();
		packet.action = request.getAction();
		return packet;
	}

	// --------------------------------------------------------------
	// ---
	// --- Packet specific API
	// ---
	// --------------------------------------------------------------
	public DeviceKeyAction getAction() {
		return action;
	}
	
	public USRDeviceKey getDeviceKey() {
		return deviceKey;
	}

	// --------------------------------------------------------------
	// ---
	// --- Streaming
	// ---
	// --------------------------------------------------------------
	@Override
	protected void readImpl(final StreamReader reader) throws IOException {
		super.readImpl(reader);
		action = reader.readCode(DeviceKeyAction.class);
		deviceKey = (USRDeviceKey) reader.readObject();
	}

	@Override
	protected void writeImpl(final StreamWriter writer) throws IOException {
		super.writeImpl(writer);
		writer.writeCode(action);
		writer.writeObject(deviceKey);
	}
	
}

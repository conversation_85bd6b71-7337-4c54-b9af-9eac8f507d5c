package ch.eisenring.user.shared.metamodel;

import static ch.eisenring.user.shared.metamodel.UserMapping.TABLE_ROLERIGHT;
import static ch.eisenring.user.shared.metamodel.UserMapping.TABLE_USERROLE;
import ch.eisenring.model.MetaAttribute;
import ch.eisenring.model.MetaClass;
import ch.eisenring.model.MetaRelationMany;
import ch.eisenring.model.engine.jdbc.ColumnSpecifier;
import ch.eisenring.model.engine.jdbc.TableSpecifier;
import ch.eisenring.model.factory.AttributeFactory;
import ch.eisenring.model.primarykey.LongPrimaryKey;
import ch.eisenring.user.shared.model.RoleModel;

public interface USRRoleMeta {

	TableSpecifier TABLE_ROLE = TableSpecifier.get(UserMapping.DATABASE, "HEAGRole");

	MetaAttribute ATR_DISPLAYNAME = AttributeFactory.string(
			"Anzeigename", null, 64,
			ColumnSpecifier.get(USRRoleMeta.TABLE_ROLE, "Displayname"));
	
	MetaAttribute ATR_TACKONVALUES = AttributeFactory.string(
			"Zusatzeigenschaften", null, 0x00FFFFFF,
			ColumnSpecifier.get(USRRoleMeta.TABLE_ROLE, "Tackonvalues"));

	MetaRelationMany REL_USERS = new MetaRelationMany(
			"Users", USRUserMeta.REL_ROLES,
			ColumnSpecifier.get(TABLE_USERROLE, "User_Rowid"));

	MetaRelationMany REL_RIGHTS = new MetaRelationMany(
			"Rights", USRRightMeta.REL_ROLES, 
			ColumnSpecifier.get(TABLE_ROLERIGHT, "Right_Rowid"));
	
	MetaClass METACLASS = new MetaClass(
			"USRRole", RoleModel.class,
			LongPrimaryKey.class, null,
			ColumnSpecifier.get(USRRoleMeta.TABLE_ROLE, "Rowid"),
			USRRoleMeta.TABLE_ROLE,
			ATR_DISPLAYNAME,
			ATR_TACKONVALUES,
			REL_USERS,
			REL_RIGHTS);

}

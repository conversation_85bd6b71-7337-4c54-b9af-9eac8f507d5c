package heag.mbl.server.model.pojo;

import java.util.Locale;
import java.util.UUID;

import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.security.auth.DestroyFailedException;
import javax.security.auth.Destroyable;

import ch.eisenring.core.crypt.CryptUtil;
import ch.eisenring.core.crypt.SecretTransport;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.resource.ResourceFinalizer;
import ch.eisenring.model.primarykey.LongPrimaryKey;
import ch.eisenring.user.shared.codetables.DeviceStatusCode;
import heag.mbl.server.MBLConstants;
import heag.mbl.shared.MBLDeviceKey;

public final class MBLDeviceKeyPojo implements MBLDeviceKey, Destroyable {

	private long rowId;
	private String deviceId;
	private DeviceStatusCode deviceStatus;
	private long createdOn;
	private long lastLogin;
	private String deviceName;
	private byte[] presharedKey;
	private byte[] secretKey;

	// ResourceFinalizer for Java 21 compatibility (replaces finalize())
	private final ResourceFinalizer resourceFinalizer;

	private MBLDeviceKeyPojo() {
		// Create ResourceFinalizer to handle cleanup when this object is garbage collected
		this.resourceFinalizer = new ResourceFinalizer(this) {
			@Override
			protected void cleanup() {
				try {
					destroy();
				} catch (DestroyFailedException e) {
					// Ignore cleanup failures during finalization
				}
			}
		};
	}

	// --------------------------------------------------------------
	// ---
	// --- Factory methods
	// ---
	// --------------------------------------------------------------
	/**
	 * Creates a new result object with default values.
	 * Beware that this constructor takes A HELL OF A LOT OF TIME.
	 */
	public static MBLDeviceKeyPojo create(final UUID uuid) throws Exception {
		final MBLDeviceKeyPojo result = new MBLDeviceKeyPojo();
		// create a new primary key for the object
		result.rowId = LongPrimaryKey.create();
		result.setUUID(uuid);
		result.setDeviceStatus(DeviceStatusCode.REGISTERED);
		final long timestamp = System.currentTimeMillis();
		result.setCreatedOn(timestamp);
		result.setLastLogin(timestamp);
		result.setDeviceName(Strings.concat("MBL Device #", result.getRowId()));
		result.secretKey = new byte[MBLConstants.KEY_SIZE];
		CryptUtil.getRandomBytes(result.secretKey, 17344);
		MBLDeviceKeyPojo.generateSecret(result.secretKey);
		return result;
	}		

	// --------------------------------------------------------------
	// ---
	// --- API
	// ---
	// --------------------------------------------------------------
	@Override
	public String getDeviceId() {
		return deviceId;
	}

	@Override
	public long getRowId() {
		return rowId;
	}

	public UUID getUUID() {
		return deviceId == null ? null : UUID.fromString(deviceId);
	}

	public void setUUID(final UUID uuid) {
		deviceId = toString(uuid);
	}

	@Override
	public DeviceStatusCode getDeviceStatus() {
		return deviceStatus;
	}

	public void setDeviceStatus(final DeviceStatusCode deviceStatus) {
		this.deviceStatus = deviceStatus;
	}

	@Override
	public long getCreatedOn() {
		return createdOn;
	}

	public void setCreatedOn(final long createdOn) {
		this.createdOn = createdOn;
	}

	@Override
	public long getLastLogin() {
		return lastLogin;
	}

	public void setLastLogin(final long lastLogin) {
		this.lastLogin = lastLogin;
	}

	@Override
	public String getDeviceName() {
		return deviceName;
	}

	public void setDeviceName(final String deviceName) {
		this.deviceName = deviceName;
	}

	public byte[] getPresharedKey() {
		return presharedKey == null ? null : presharedKey.clone();
	}

	public void setPresharedKey(final byte[] presharedKey) {
		this.presharedKey = presharedKey == null ? null : presharedKey.clone();
	}

	public byte[] getSecretKey() {
		return secretKey == null ? null : secretKey.clone();
	}

//	public void setSecretKey(final byte[] secretKey) {
//		this.secretKey = secretKey == null ? null : secretKey.clone();
//	}

	// --------------------------------------------------------------
	// ---
	// --- Derived attributes
	// ---
	// --------------------------------------------------------------
	public SecretTransport getTransport() throws Exception {
		return generateTransport(getPresharedKey(), getSecretKey());
	}

	// --------------------------------------------------------------
	// ---
	// --- Helper methods
	// ---
	// --------------------------------------------------------------
	public static String toString(final UUID uuid) {
		return Strings.toUpper(Strings.toString(uuid), Locale.ENGLISH);		
	}

	private static SecretTransport generateTransport(final byte[] presharedKey, final byte[] rawSecret) throws Exception {
		final byte[] iv = new byte[32];
		byte[] encrypted = null;
		try {
			generateSecret(iv);
			encrypted = CryptUtil.encrypt(rawSecret, presharedKey, iv);
			return new SecretTransport(encrypted, iv);
		} finally {
			CryptUtil.destroy(iv);
			CryptUtil.destroy(encrypted);
		}
	}

	public static void generateSecret(final byte[] buffer) throws Exception {
		CryptUtil.getRandomBytes(buffer, 223);
		// now the real deal
		final KeyGenerator keyGen = KeyGenerator.getInstance("AES");
		keyGen.init(256);
		for (int i=0; i<buffer.length; ) {
			final SecretKey secretKey = keyGen.generateKey();
			final byte[] bytes = secretKey.getEncoded();
			for (int j=0; j<bytes.length && i<buffer.length; ++j) {
				buffer[i] ^= bytes[j];
				++i;
			}
			CryptUtil.destroy(secretKey);
			CryptUtil.destroy(bytes);
		}
	}

	// --------------------------------------------------------------
	// ---
	// --- Destroyable implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public void destroy() throws DestroyFailedException {
		CryptUtil.destroy(presharedKey);
		CryptUtil.destroy(secretKey);
	}

	// --------------------------------------------------------------
	// ---
	// --- Object overrides
	// ---
	// --------------------------------------------------------------
	// finalize() method removed - now using ResourceFinalizer for Java 21 compatibility

}

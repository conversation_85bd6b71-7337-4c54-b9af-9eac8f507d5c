package heag.mbl.server.sync;

import ch.eisenring.app.shared.CoreTickListenerAdapter;
import ch.eisenring.app.shared.timing.NightlyTimer;
import ch.eisenring.app.shared.timing.Timer;
import ch.eisenring.core.collections.Collection;
import ch.eisenring.core.datatypes.date.TimeOfDay;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.logiware.code.service.LogiwareService;
import ch.eisenring.logiware.code.soft.LWProduktStatusCode;
import ch.eisenring.lw.condition.Factory;
import ch.eisenring.lw.meta.LWProduktMeta;
import ch.eisenring.lw.model.navigable.LWObjectCache;
import ch.eisenring.lw.model.navigable.LWProdukt;
import heag.mbl.server.MBLServer;
import heag.mbl.shared.network.obj.MBLProduktKatalog;
import heag.mbl.shared.network.obj.MBLProduktKatalogBuilder;

public final class MBLProduktKatalogProvider extends CoreTickListenerAdapter {

	private final MBLServer server;

	private final Timer timer = new NightlyTimer(TimeOfDay.parse("05:45")); 

	public MBLProduktKatalogProvider(final MBLServer server) {
		super(server, null, ASYNCHRONOUS);
		this.server = server;
	}

	// --------------------------------------------------------------
	// ---
	// --- Activation control
	// ---
	// --------------------------------------------------------------
	@Override
	protected boolean isDue(final long now) {
		return timer.isDue();
	}

	// --------------------------------------------------------------
	// ---
	// --- Catalog update
	// ---
	// --------------------------------------------------------------
	@Override
	protected void tickImpl(final long now) {
		final MBLProduktKatalog oldCatalog = server.PRODUKT_KATALOG.get();
		// if old catalog hasn't been built, don't attempt to update it
		if (oldCatalog == null)
			return;
		final MBLProduktKatalog newCatalog;
		try {
			newCatalog = buildCatalog(server);
		} catch (final Exception e) {
			// error building new catalog
			Logger.error(e);
			return;
		}
		if (!newCatalog.equals(oldCatalog)) {
			// replace the existing catalog
			server.PRODUKT_KATALOG.set(newCatalog);
		}
	}

	public MBLProduktKatalog getProduktKatalog() throws Exception {
		MBLProduktKatalog result = server.PRODUKT_KATALOG.get();
		if (result == null) {
			result = buildCatalog(server);
			server.PRODUKT_KATALOG.set(result);
		}
		return result;
	}

	/**
	 * Builds new MBLProduktCatalog from database
	 */
	static MBLProduktKatalog buildCatalog(final MBLServer server) throws Exception {
		final LogiwareService service = server.locateService(LogiwareService.class);
		final LWObjectCache cache = service.createObjectCache();
		// load product catalog
		final Collection<LWProdukt> produkte = cache.load(LWProdukt.class,
				Factory.in(LWProduktMeta.ATR_STATUS, LWProduktStatusCode.AKTIV, LWProduktStatusCode.LIQUIDATION));
		final MBLProduktKatalogBuilder builder = new MBLProduktKatalogBuilder();
		for (final LWProdukt produkt : produkte) {
			final String produktNummer = produkt.getProduktnummer();
			final LWProduktStatusCode statusCode = produkt.getProduktStatus();
			builder.add(produktNummer, statusCode);
		}
		return builder.compile();
	}

}

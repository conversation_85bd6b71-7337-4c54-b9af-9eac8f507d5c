package heag.mbl.server.network.wan;

import heag.mbl.server.MBLServer;
import heag.mbl.server.network.MBLPacketHandler;
import heag.mbl.server.resources.EMailTemplates;
import heag.mbl.shared.network.wan.PacketMassaufnahmePutReply;
import heag.mbl.shared.network.wan.PacketMassaufnahmePutRequest;
import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.Map;
import ch.eisenring.core.collections.impl.HashMap;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.logging.Logger;
import ch.eisenring.core.template.VariableResolver;
import ch.eisenring.core.util.file.FileImage;
import ch.eisenring.dms.service.DMSService;
import ch.eisenring.dms.service.codetables.DMSDocumentType;
import ch.eisenring.dms.service.codetables.DMSFolderType;
import ch.eisenring.dms.service.proxy.DMSDocumentHandle;
import ch.eisenring.dms.service.proxy.DMSFolderHandle;
import ch.eisenring.dms.service.proxy.DMSObjectHandle;
import ch.eisenring.email.EMail;
import ch.eisenring.email.EMailTemplate;
import ch.eisenring.email.server.MailServiceServer;
import ch.eisenring.logiware.code.service.LogiwareService;
import ch.eisenring.logiware.code.soft.LWKundenberaterCode;
import ch.eisenring.lw.export.LWExportAuftrag;
import ch.eisenring.lw.export.LWExporter;
import ch.eisenring.lw.model.navigable.LWAuftrag;
import ch.eisenring.lw.model.navigable.LWObjectCache;
import ch.eisenring.network.PacketSink;
import ch.eisenring.network.packet.AbstractPacket;
import ch.eisenring.user.shared.codetables.UserTackOnCode;
import ch.eisenring.user.shared.service.UserFacade;
import ch.eisenring.user.shared.service.util.USRCodeCanonicalizer;

final class MassaufnahmePutRequestHandler extends MBLPacketHandler {

	MassaufnahmePutRequestHandler(final MBLServer server) {
		super(server, PacketMassaufnahmePutRequest.class);
	}

	@Override
	public PacketMassaufnahmePutReply handle(final AbstractPacket packet) {
		final PacketMassaufnahmePutRequest request = (PacketMassaufnahmePutRequest) packet;
		try {
			final PacketMassaufnahmePutReply reply = handleImpl(server, request);
			if (reply != null)
				return reply;
			return PacketMassaufnahmePutReply.create(request, ErrorMessage.ERROR);
		} catch (final Exception e) {
			return PacketMassaufnahmePutReply.create(request, new ErrorMessage(e));
		}
	}

	@Override
	public void handle(final AbstractPacket packet, final PacketSink sink) {
		final PacketMassaufnahmePutRequest request = (PacketMassaufnahmePutRequest) packet;
		final PacketMassaufnahmePutReply reply = isAuthenticated(sink)
				? handle(request) : PacketMassaufnahmePutReply.create(request, NOT_AUTHENTICATED);
		sink.sendPacket(reply);
	}
	

	private static PacketMassaufnahmePutReply handleImpl(final MBLServer server, final PacketMassaufnahmePutRequest request) throws Exception {
		final DMSService service = server.locateService(DMSService.class);
		final String projectNumber = request.getProjektKey().getProjektnummer();
		final String user = request.getUser();

		// store massaufnahme
		final DMSFolderHandle massaufnahmeFolder = findFolder(service, DMSFolderType.MASSAUFNAHMEN, projectNumber);
		final DMSDocumentHandle massaufnahmeDocument = service.putDocument(massaufnahmeFolder, user, request.getMassaufnahme());
		service.setDocumentType(massaufnahmeDocument, DMSDocumentType.MASSAUFNAHME, user);
		
		// store photos
		final DMSFolderHandle photoFolder = findFolder(service, DMSFolderType.FOTOS_MASSAUFNAHME, projectNumber);
		final List<FileImage> photos = request.getPhotos();
		for (final FileImage photo : photos) {
			service.putDocument(photoFolder, user, photo);
		}

		// write "Info Masse"
		writeBackLWAuftrag(server, request);
		
		// send mail
		sendInfomail(server, massaufnahmeDocument, request);
		return PacketMassaufnahmePutReply.create(request, ErrorMessage.OK);
	}

	private static void writeBackLWAuftrag(final MBLServer server, final PacketMassaufnahmePutRequest request) throws Exception {
		final String infoMasse = request.getInfoMasse();
		if (PacketMassaufnahmePutRequest.INFO_MASSE_UNCHANGED.equals(infoMasse))
			return;
		final LWExportAuftrag export = LWExportAuftrag.create(request.getAuftragKey(), "MBL");
		export.setInfoMasse(infoMasse);
		final LWExporter exporter = LWExporter.create(server);
		exporter.export(List.asList(export));		
	}

	static DMSFolderHandle findFolder(final DMSService service, final DMSFolderType folderType, final String projectNumber) throws Exception {
		final DMSFolderHandle folder = service.getProjectContentFolder(projectNumber, folderType);
		if (folder == null)
			throw new IllegalArgumentException(Strings.concat("Kein Ordner \"", folderType, "\" für ", projectNumber));
		return folder;
	}
	
	private static void sendInfomail(final MBLServer server, final DMSObjectHandle document, final PacketMassaufnahmePutRequest request) {
		// mail requested?
		if (request.getSendInfomail()) { 
			try {
				doSendInfomail(server, document, request);
			} catch (final Exception e) {
				// ignore
				Logger.warn(e);
			}
		}
	}

	private static void doSendInfomail(final MBLServer server, final DMSObjectHandle document, final PacketMassaufnahmePutRequest request) throws Exception {
		final Map<String, String> valueMap = new HashMap<>();
		
		// determine involved dudes
		final LogiwareService lwService = server.locateService(LogiwareService.class);
		final LWObjectCache cache = lwService.createObjectCache();
		final LWAuftrag auftrag = cache.getObject(request.getAuftragKey(), LWAuftrag.class);
		final LWKundenberaterCode kb1 = auftrag.getKundenberater();
		final USRCodeCanonicalizer<LWKundenberaterCode> kbCodeCan = new USRCodeCanonicalizer<>(LWKundenberaterCode.class, UserTackOnCode.KUNDENBERATER, server);
		final UserFacade user = kbCodeCan.getUser(kb1);
		if (user == null)
			return;
		valueMap.put("KundenberaterEMail", user.getEMailAddress());
		valueMap.put("ObjectId", Strings.toString(document.getPKValue()));
		valueMap.put("ObjectName", document.getObjectName());
		valueMap.put("Projektnummer", request.getProjektKey().getProjektnummer());
		valueMap.put("Auftragnummer", Strings.toString(request.getAuftragKey().getAuftragnummer()));

		final EMailTemplate template;
		try {
			template = new EMailTemplate(EMailTemplates.TEMPLATE_MASSAUFNAHME_ERSTELLT.getData());
		} catch (final Exception e) {
			// template not found
			throw new RuntimeException(e.getMessage(), e);
		}
		template.addModel(VariableResolver.create(valueMap, false, null));
		final EMail mail = template.toEMail();
		mail.setFromName("IT Helpdesk");
		mail.setFromAddress("<EMAIL>");
		mail.setReplyTo("<EMAIL>");
		
		// send that mail
		final MailServiceServer mailer = server.getCore().getComponent(MailServiceServer.class, true);
		mailer.enqueue(mail, null);
	}

}

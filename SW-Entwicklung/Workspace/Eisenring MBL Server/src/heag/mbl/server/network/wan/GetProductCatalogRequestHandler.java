package heag.mbl.server.network.wan;

import heag.mbl.server.MBLServer;
import heag.mbl.server.network.MBLPacketHandler;
import heag.mbl.server.sync.MBLProduktKatalogProvider;
import heag.mbl.shared.network.obj.MBLProduktKatalog;
import heag.mbl.shared.network.wan.PacketGetProductCatalogReply;
import heag.mbl.shared.network.wan.PacketGetProductCatalogRequest;
import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.network.PacketSink;
import ch.eisenring.network.packet.AbstractPacket;

public final class GetProductCatalogRequestHandler extends MBLPacketHandler {

	GetProductCatalogRequestHandler(final MBLServer server) {
		super(server, PacketGetProductCatalogRequest.class);
	}

	@Override
	public PacketGetProductCatalogReply handle(final AbstractPacket packet) {
		final PacketGetProductCatalogRequest request = (PacketGetProductCatalogRequest) packet;
		final PacketGetProductCatalogReply reply = handle(server, request);
		return reply;
	}

	@Override
	public void handle(final AbstractPacket packet, final PacketSink sink) {
		final PacketGetProductCatalogRequest request = (PacketGetProductCatalogRequest) packet;
		final PacketGetProductCatalogReply reply;
		if (isAuthenticated(sink)) {
			reply = handle(server, request);
		} else {
			reply = PacketGetProductCatalogReply.create(request, NOT_AUTHENTICATED, null);
		}
		sink.sendPacket(reply);
	}

	static PacketGetProductCatalogReply handle(final MBLServer server, final PacketGetProductCatalogRequest request) {
		try {
			final PacketGetProductCatalogReply reply = handleImpl(server, request);
			return reply == null ? PacketGetProductCatalogReply.create(request, ErrorMessage.ERROR, null) : reply;
		} catch (final Exception e) {
			return PacketGetProductCatalogReply.create(request, new ErrorMessage(e), null);
		}
	}

	static PacketGetProductCatalogReply handleImpl(final MBLServer server, final PacketGetProductCatalogRequest request) throws Exception {
		final MBLProduktKatalogProvider provider = server.getFeatureLookup().getFeature(MBLProduktKatalogProvider.class);
		final MBLProduktKatalog catalog = provider.getProduktKatalog();
		final MBLProduktKatalog update = request.getCatalogVersion() == catalog.getVersion()
				? null : catalog;
		return PacketGetProductCatalogReply.create(request, ErrorMessage.OK, update);
	}

}

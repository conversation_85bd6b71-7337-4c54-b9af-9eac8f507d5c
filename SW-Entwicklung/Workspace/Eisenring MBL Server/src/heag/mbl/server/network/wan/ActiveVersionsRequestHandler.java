package heag.mbl.server.network.wan;

import static ch.eisenring.dms.server.DMSMapping.TM_PROPERTY;

import java.sql.SQLException;
import java.util.concurrent.atomic.AtomicReference;

import ch.eisenring.app.server.model.ContextSource;
import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.collections.Map;
import ch.eisenring.core.collections.Set;
import ch.eisenring.core.collections.impl.HashMap;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.dms.server.functions.SQLFunctions;
import ch.eisenring.dms.service.codetables.DMSObjectStatus;
import ch.eisenring.dms.service.codetables.DMSObjectType;
import ch.eisenring.dms.service.codetables.DMSPropertyCode;
import ch.eisenring.dms.shared.DMSConstants;
import ch.eisenring.dms.shared.model.meta.DMSPropertyMeta;
import ch.eisenring.jdbc.JDBCResultSet;
import ch.eisenring.jdbc.RowHandler;
import ch.eisenring.jdbc.StatementParameters;
import ch.eisenring.model.TransactionContext;
import ch.eisenring.model.TransactionSource;
import ch.eisenring.network.PacketSink;
import ch.eisenring.network.packet.AbstractPacket;
import heag.mbl.server.MBLServer;
import heag.mbl.server.network.MBLPacketHandler;
import heag.mbl.shared.network.wan.PacketActiveVersionsReply;
import heag.mbl.shared.network.wan.PacketActiveVersionsRequest;

final class ActiveVersionsRequestHandler extends MBLPacketHandler {

	ActiveVersionsRequestHandler(final MBLServer server) {
		super(server, PacketActiveVersionsRequest.class);
	}

	@Override
	public PacketActiveVersionsReply handle(final AbstractPacket packet) {
		final PacketActiveVersionsRequest request = (PacketActiveVersionsRequest) packet;
		final PacketActiveVersionsReply reply = handle(server, request);
		return reply;
	}

	@Override
	public void handle(final AbstractPacket packet, final PacketSink sink) {
		final PacketActiveVersionsRequest request = (PacketActiveVersionsRequest) packet;
		final PacketActiveVersionsReply reply;
		if (isAuthenticated(sink)) {
			reply = handle(server, request);
		} else {
			reply = PacketActiveVersionsReply.create(request, NOT_AUTHENTICATED);
		}
		sink.sendPacket(reply);
	}

	private static PacketActiveVersionsReply handle(final MBLServer server, final PacketActiveVersionsRequest request) {
		try {
			final PacketActiveVersionsReply reply = handleImpl(server, request);
			if (reply != null)
				return reply;
		} catch (final Exception e) {
			return PacketActiveVersionsReply.create(request,  new ErrorMessage(e));
		}
		return PacketActiveVersionsReply.create(request, ErrorMessage.ERROR);
	}
	
	private final static Set<DMSObjectStatus> ACTIVE_STATUS_CODES = Set.asReadonlySet(
			DMSObjectStatus.ACTIVE, DMSObjectStatus.ARCHIVED);

	private static PacketActiveVersionsReply handleImpl(final MBLServer server, final PacketActiveVersionsRequest request) throws Exception {
		final Map<Long, Long> objectVersionMap = new HashMap<>();
		final TransactionSource source = new ContextSource(DMSConstants.DMS_DATABASE) {
			@Override
			protected void loadImpl(final TransactionContext context) throws SQLException {
				final long baseFolderRowId = getBaseFolderRowId();
				// Query all child files for current version
				final StatementParameters params = new StatementParameters();
				final StringMaker sql = StringMaker.obtain();
				sql.append("SELECT C.rowId, ");
				SQLFunctions.ACTIVE_OBJECT_VERSION.appendCall(sql, this, "C.rowId");
				sql.append(" AS versionId FROM ");
				SQLFunctions.ALL_CHILDREN.appendCall(sql, this, "?");
				params.add(baseFolderRowId);
				sql.append(" AS C WHERE C.Typecode = ? AND C.Statuscode IN ");
				params.add(DMSObjectType.FILE);
				prepareIn(sql, ACTIVE_STATUS_CODES);
				params.addAll(ACTIVE_STATUS_CODES);
				doQuery(sql.release(), params, new RowHandler() {
					@Override
					public void handleRow(final JDBCResultSet resultSet) throws SQLException {
						final long objectRowId = resultSet.getLong(1);
						final long versionRowId = resultSet.getLong(2);
						objectVersionMap.put(objectRowId, versionRowId);
					}
				});
			}

			private long getBaseFolderRowId() throws SQLException {
				final AtomicReference<Long> baseFolderRowId = new AtomicReference<>();
				// find the project folder
				final StringMaker sql = StringMaker.obtain();
				sql.append("SELECT ");
				sql.append(TM_PROPERTY.getColumn(DMSPropertyMeta.ATR_OBJECT_ROWID));
				sql.append(" FROM ");
				qualifyTableName(sql, TM_PROPERTY.getTableSpecifier());
				sql.append(" WHERE ");
				sql.append(TM_PROPERTY.getColumn(DMSPropertyMeta.ATR_PROPERTYCODE));
				sql.append(" = ? AND ");
				sql.append(TM_PROPERTY.getColumn(DMSPropertyMeta.ATR_PROPERTYVALUE));
				sql.append(" = ?");
				final StatementParameters params = new StatementParameters(2);
				params.add(DMSPropertyCode.BASEPROJECTNUMBER);
				params.add(request.getBaseProjectNumber());
				doQuery(sql.release(), params, new RowHandler() {
					@Override
					public void handleRow(final JDBCResultSet resultSet) throws SQLException {
						long objectRowId = resultSet.getLong(1);
						if (resultSet.wasNull())
							throw new SQLException("unexpected NULL value");
						if (baseFolderRowId.get() != null)
							throw new SQLException("multiple folders found for " + request.getBaseProjectNumber());
						baseFolderRowId.set(objectRowId);
					}
				});
				if (baseFolderRowId.get() == null)
					throw new SQLException("No folder found for " + request.getBaseProjectNumber());
				return baseFolderRowId.get();
			}
		};
		// load from the source
		final TransactionContext context = new TransactionContext();
		context.load(source);
		return PacketActiveVersionsReply.create(request, objectVersionMap);
	}

}

package heag.mbl.server.network.wan;

import heag.dsp.pub.model.DSPMontageBewertung;
import heag.mbl.server.MBLServer;
import heag.mbl.server.network.MBLPacketHandler;
import heag.mbl.shared.network.wan.PacketPutMontageBewertungReply;
import heag.mbl.shared.network.wan.PacketPutMontageBewertungRequest;
import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.util.file.FileImage;
import ch.eisenring.dispo.shared.network.packets.PacketMontageBewertungPutReply;
import ch.eisenring.dispo.shared.network.packets.PacketMontageBewertungPutRequest;
import ch.eisenring.dms.service.DMSService;
import ch.eisenring.dms.service.codetables.DMSFolderType;
import ch.eisenring.dms.service.proxy.DMSFolderHandle;
import ch.eisenring.network.MasterPacketDispatcher;
import ch.eisenring.network.PacketSink;
import ch.eisenring.network.packet.AbstractPacket;

final class PutMontageBewertungRequestHandler extends MBLPacketHandler {

	PutMontageBewertungRequestHandler(final MBLServer server) {
		super(server, PacketPutMontageBewertungRequest.class);
	}

	@Override
	public void handle(final AbstractPacket packet, final PacketSink sink) {
		final PacketPutMontageBewertungRequest request = (PacketPutMontageBewertungRequest) packet;
		final PacketPutMontageBewertungReply reply = isAuthenticated(sink)
				? handle(request) : PacketPutMontageBewertungReply.create(request, NOT_AUTHENTICATED);
		sink.sendPacket(reply);
	}
	
	@Override
	public PacketPutMontageBewertungReply handle(final AbstractPacket packet) {
		final PacketPutMontageBewertungRequest request = (PacketPutMontageBewertungRequest) packet;
		try {
			final PacketPutMontageBewertungReply reply = handleImpl(server, request);
			if (reply != null)
				return reply;
			return PacketPutMontageBewertungReply.create(request, ErrorMessage.ERROR);
		} catch (final Exception e) {
			return PacketPutMontageBewertungReply.create(request, new ErrorMessage(e));
		}
	}

	private static PacketPutMontageBewertungReply handleImpl(final MBLServer server, final PacketPutMontageBewertungRequest request) throws Exception {
		{ // delegate storing bewertung to DSP component
			final MasterPacketDispatcher dispatcher = server.getCore().getMasterPacketDispatcher();
			final PacketMontageBewertungPutRequest dspRequest = PacketMontageBewertungPutRequest.create(request.getBewertung());
			final PacketMontageBewertungPutReply dspReply = (PacketMontageBewertungPutReply) dispatcher.dispatch(dspRequest);
			// if DSP returns error, abort here 
			if (!dspReply.isValid())
				return PacketPutMontageBewertungReply.create(request, dspReply.getMessage()); 
		}
		final DMSService service = server.locateService(DMSService.class);
		final DSPMontageBewertung bewertung = request.getBewertung();
		// store the photos
		final List<FileImage> photos = request.getPhotos();
		if (!photos.isEmpty()) {
			final String user = bewertung.getUser();
			final String projektNummer = bewertung.getProjektnummer();
			final DMSFolderHandle photoFolder = MassaufnahmePutRequestHandler.findFolder(service, DMSFolderType.FOTOS_MONTAGE_MOEBEL, projektNummer);
			for (final FileImage photo : photos) {
				service.putDocument(photoFolder, user, photo);
			}
		}
		return PacketPutMontageBewertungReply.create(request, ErrorMessage.OK);
	}

}

package heag.mbl.server.network.wan;

import static heag.mbl.server.model.MBLMapping.OM_DEVICEKEY;
import static heag.mbl.server.model.MBLMapping.TM_DEVICEKEY;

import java.sql.SQLException;
import java.util.concurrent.atomic.AtomicReference;

import ch.eisenring.app.server.model.ContextSource;
import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.crypt.CryptUtil;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.jdbc.JDBCResultSet;
import ch.eisenring.jdbc.RowHandler;
import ch.eisenring.jdbc.StatementParameters;
import ch.eisenring.model.TransactionContext;
import ch.eisenring.model.TransactionSource;
import ch.eisenring.network.PacketSink;
import ch.eisenring.network.packet.AbstractPacket;
import ch.eisenring.user.shared.codetables.DeviceStatusCode;
import heag.mbl.server.MBLConstants;
import heag.mbl.server.MBLServer;
import heag.mbl.server.model.meta.MBLDeviceKeyMeta;
import heag.mbl.server.model.pojo.MBLDeviceKeyPojo;
import heag.mbl.server.network.MBLPacketHandler;
import heag.mbl.shared.network.wan.PacketDeviceStatusReply;
import heag.mbl.shared.network.wan.PacketDeviceStatusRequest;

final class DeviceStatusRequestHandler extends MBLPacketHandler {

	DeviceStatusRequestHandler(final MBLServer server) {
		super(server, PacketDeviceStatusRequest.class);
	}

	@Override
	public void handle(final AbstractPacket packet, final PacketSink sink) {
		final PacketDeviceStatusRequest request = (PacketDeviceStatusRequest) packet;
		final PacketDeviceStatusReply reply = handle(server, request);
		sink.sendPacket(reply);
	}

	public static PacketDeviceStatusReply handle(final MBLServer server, final PacketDeviceStatusRequest request) {
		try {
			final PacketDeviceStatusReply reply = handleImpl(server, request);
			if (reply != null)
				return reply;
		} catch (final Exception e) {
			return PacketDeviceStatusReply.create(request, DeviceStatusCode.NULL, new ErrorMessage(e));
		}
		return PacketDeviceStatusReply.create(request, DeviceStatusCode.NULL, ErrorMessage.ERROR);
	}
	
	private static PacketDeviceStatusReply handleImpl(final MBLServer server, final PacketDeviceStatusRequest request) throws Exception {
		final AtomicReference<DeviceStatusCode> result = new AtomicReference<>();
		final TransactionSource source = new ContextSource(MBLConstants.MBL_DATABASE) {
			@Override
			protected void loadImpl(final TransactionContext context) throws SQLException {
				StatementParameters params = new StatementParameters(2);
				final StringMaker sql = StringMaker.obtain();
				OM_DEVICEKEY.appendSelectSQL(sql, this);
				sql.append(" WHERE ");
				sql.append(TM_DEVICEKEY.getColumn(MBLDeviceKeyMeta.ATR_DEVICEID));
				sql.append(" = ?");
				params.add(MBLDeviceKeyPojo.toString(request.getDeviceID()));
				doQuery(sql.release(), params, new RowHandler() {
					@Override
					public void handleRow(final JDBCResultSet resultSet) throws SQLException {
						final MBLDeviceKeyPojo key = OM_DEVICEKEY.loadRow(context, resultSet);
						result.set(key.getDeviceStatus());
						CryptUtil.destroy(key);
					}
				});
				if (result.get() == null) {
					// if no result found, the device is not registered yet
					result.set(DeviceStatusCode.UNREGISTERED);
				}
			}
		};
		// load from the source
		final TransactionContext context = new TransactionContext();
		context.load(source);
		return PacketDeviceStatusReply.create(request, result.get());
	}

}

package heag.mbl.server.network;

import java.util.Arrays;
import java.util.Random;
import java.util.UUID;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import javax.security.auth.DestroyFailedException;

import ch.eisenring.core.crypt.CryptUtil;
import ch.eisenring.core.crypt.KeySizePolicyHack;
import ch.eisenring.core.resource.ResourceFinalizer;
import heag.saggitarius.fs.SaggitariusKey;

public final class SaggitariusKeyImpl implements SaggitariusKey {

	private final UUID keyIdentity;
	private final int ivSize;
	private final int keySize;
	private final String algo;
	private final String algoMode;
	private final byte[] secret;
	private final int secretMask;

	// ResourceFinalizer for Java 1.8 and Java 21 compatibility
	private final ResourceFinalizer resourceFinalizer;
	private volatile boolean destroyed = false;

	protected int state;

	protected SaggitariusKeyImpl(final UUID keyIdentity, final int secretSize) {
		this(keyIdentity, new byte[secretSize]);
	}

	private SaggitariusKeyImpl(final UUID keyIdentity, final byte[] secret) {
		KeySizePolicyHack.init();
		this.secretMask = secret.length - 1;
		this.secret = secret;
		this.keyIdentity = keyIdentity;
		this.ivSize = 16;
		this.keySize = 32;
		this.algo = "AES";
		this.algoMode = "AES/CBC/NoPadding";
		this.resourceFinalizer = new ResourceFinalizer(this) {
			@Override
			protected void cleanup() {
				try {
					CryptUtil.destroy(SaggitariusKeyImpl.this);
				} catch (Exception e) {
					// Ignore cleanup failures during finalization
				}
			}
		};
	}

	// --------------------------------------------------------------
	// ---
	// --- SaggitariusKey implementation
	// ---
	// --------------------------------------------------------------
	@Override
	public final UUID getKeyIdentity() {
		return keyIdentity;
	}

	@Override
	public boolean isValid() {
		return true;
	}

	@Override
	public void destroy() throws DestroyFailedException {
		if (!destroyed) {
			destroyed = true;
			// Clear the secret
			if (secret != null) {
				CryptUtil.destroy(secret);
			}
			state = 0;
		}
	}

	@Override
	public boolean isDestroyed() {
		return destroyed;
	}

	@Override
	public void decrypt(final long lbn, final byte[] buffer, final int offset, final int length) throws Exception {
		crypt(Cipher.DECRYPT_MODE, lbn, buffer, offset, length);
	}

	@Override
	public void encrypt(final long lbn, final byte[] buffer, final int offset, final int length) throws Exception {
		crypt(Cipher.ENCRYPT_MODE, lbn, buffer, offset, length);
	}

	// --------------------------------------------------------------
	// ---
	// --- Internal implementation
	// ---
	// --------------------------------------------------------------
	protected void crypt(final int mode, final long lbn, final byte[] buffer, final int offset, final int length) throws Exception {
		IvParameterSpec iv = null;
		SecretKeySpec key = null;
		byte[] temp = null;
		try {
			final Cipher c = Cipher.getInstance(algoMode);
			iv = deriveIV(lbn);
			key = deriveKey(lbn);
		    c.init(mode, key, iv);
		    temp = c.doFinal(buffer, offset, length);
		    System.arraycopy(temp, 0, buffer, offset, length);
		} finally {
			CryptUtil.destroy(key);
			CryptUtil.destroy(iv);
			CryptUtil.destroy(temp);
		}
	}

	protected IvParameterSpec deriveIV(final long lbn) throws Exception {
		final byte[] ivData = new byte[ivSize];
		try {
			final Random random = new Random(lbn * 0xBCD9_9E1F_7A53_8015L);
			random.nextBytes(ivData);
			obfuscate(ivData, (int) lbn, 8719, 4691);
			return new IvParameterSpec(ivData);
		} finally {
			CryptUtil.destroy(ivData);
		}
	}

	protected SecretKeySpec deriveKey(final long lbn) throws Exception {
		byte[] keyData = keySlice(lbn);
		try {
			// mess up the data according to LBN
			obfuscate(keyData, (int) lbn, 8069, 6211);
			return new SecretKeySpec(keyData, algo);
		} finally {
			CryptUtil.destroy(keyData);
		}
	}

	protected void obfuscate(final byte[] data, final int offset, final int p1, final int p2) {
		final byte[] secret = this.secret;
		final int mask = this.secretMask;
		int x = offset;
		for (int i=0; i<data.length; ++i) {
			x ^= p2;
			x *= p1;
			data[i] ^= secret[x & mask];
		}
	}

	protected byte[] keySlice(final long lbn) throws Exception {
		int start, end;
		start = ((int) (lbn * keySize));
		start = (start & 0x7FFF_FFFF) % secret.length;
		end = start + keySize;
		if (end >= secret.length) {
			start = 0;
			end = keySize;
		}
		return Arrays.copyOfRange(secret, start, end);
	}

	// --------------------------------------------------------------
	// ---
	// --- Object overrides
	// ---
	// --------------------------------------------------------------
	@Override
	public int hashCode() {
		return keyIdentity.hashCode();
	}

	@Override
	public boolean equals(final Object o) {
		return o instanceof SaggitariusKey && ((SaggitariusKey) o).getKeyIdentity().equals(getKeyIdentity());
	}

	// finalize() method removed - now using ResourceFinalizer for Java 1.8 and Java 21 compatibility

}

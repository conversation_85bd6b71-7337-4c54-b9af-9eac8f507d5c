package heag.mbl.server.network.wan;

import ch.eisenring.logiware.code.pseudo.AbwicklungsartCode;
import heag.mbl.server.MBLServer;
import heag.mbl.server.network.MBLPacketHandler;
import heag.mbl.shared.constants.MassaufnahmeConstants;
import heag.mbl.shared.network.wan.PacketSearchAuftragInfoReply;
import heag.mbl.shared.network.wan.PacketSearchAuftragInfoRequest;
import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.collections.List;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.logiware.code.service.LogiwareService;
import ch.eisenring.logiware.code.soft.LWAuftragStatusCode;
import ch.eisenring.lw.api.LWAuftragData;
import ch.eisenring.lw.condition.Factory;
import ch.eisenring.lw.condition.LWCondition;
import ch.eisenring.lw.meta.LWAuftragKopfMeta;
import ch.eisenring.lw.meta.LWAuftragMeta;
import ch.eisenring.lw.meta.LWProjektMeta;
import ch.eisenring.lw.model.navigable.LWAuftrag;
import ch.eisenring.lw.model.navigable.LWAuftragKopf;
import ch.eisenring.lw.model.navigable.LWObjectCache;
import ch.eisenring.lw.model.navigable.LWProjekt;
import ch.eisenring.network.PacketSink;
import ch.eisenring.network.packet.AbstractPacket;

import java.util.Comparator;
import java.util.stream.Collectors;

final class SearchAuftragInfoRequestHandler extends MBLPacketHandler {

	SearchAuftragInfoRequestHandler(final MBLServer server) {
		super(server, PacketSearchAuftragInfoRequest.class);
	}

	@Override
	public PacketSearchAuftragInfoReply handle(final AbstractPacket packet) {
		final PacketSearchAuftragInfoRequest request = (PacketSearchAuftragInfoRequest) packet;
		final PacketSearchAuftragInfoReply reply = handle(server, request);
		return reply;
	}

	@Override
	public void handle(final AbstractPacket packet, final PacketSink sink) {
		final PacketSearchAuftragInfoRequest request = (PacketSearchAuftragInfoRequest) packet;
		final PacketSearchAuftragInfoReply reply;
		if (isAuthenticated(sink)) {
			reply = handle(server, request);
		} else {
			reply = PacketSearchAuftragInfoReply.create(request, NOT_AUTHENTICATED);
		}
		sink.sendPacket(reply);
	}

	static PacketSearchAuftragInfoReply handle(final MBLServer server, final PacketSearchAuftragInfoRequest request) {
		try {
			final PacketSearchAuftragInfoReply reply = handleImpl(server, request);
			if (reply != null)
				return reply;
			return PacketSearchAuftragInfoReply.create(request, ErrorMessage.ERROR);
		} catch (final Exception e) {
			return PacketSearchAuftragInfoReply.create(request, new ErrorMessage(e));
		}
	}

	static PacketSearchAuftragInfoReply handleImpl(final MBLServer server, final PacketSearchAuftragInfoRequest request) throws Exception {
		final LogiwareService service = server.locateService(LogiwareService.class);
		final LWObjectCache cache = service.createObjectCache();

		final LWCondition cndPrj = Strings.isEmpty(request.getProjektnummer()) ? Factory.noOp() :
				(MassaufnahmeConstants.ACTIVITY_LABEL.equals(request.getLocation()) ?
						Factory.like(LWAuftragMeta.ATR_PROJEKTNUMMER, request.getProjektnummer() + "%") :
						Factory.like(LWAuftragMeta.ATR_PROJEKTNUMMER, request.getProjektnummer()));

		final List<LWAuftrag> auftraege = cache.load(LWAuftrag.class, LWAuftragKopf.class, LWProjekt.class,
				Factory.and(
						cndPrj,
						Factory.in(LWAuftragMeta.ATR_ABWICKLUNGSART, request.getAbwicklungsarten()),
						Factory.ne(LWAuftragMeta.ATR_STATUSCODE, LWAuftragStatusCode.STORNIERT),
						Factory.eq(LWAuftragMeta.ATR_AUFTRAGNUMMER, LWAuftragKopfMeta.ATR_AUFTRAGNUMMER),
						Factory.eq(LWAuftragMeta.ATR_GSE_AUFTRAG, LWAuftragKopfMeta.ATR_GSE_AUFTRAG),
						Factory.eq(LWAuftragMeta.ATR_PROJEKTNUMMER, LWProjektMeta.ATR_PROJEKTNUMMER),
						Factory.eq(LWAuftragMeta.ATR_GSE_PROJEKT, LWProjektMeta.ATR_GSE_PROJEKT)
				)
		);

		List<LWAuftragData> matches = request.getFilter().filter(auftraege);
		//Filtering out auftraege with AWA175 if there are other auftraege around and selecting only the latest auftrag of AWA175
		if (MassaufnahmeConstants.ACTIVITY_LABEL.equals(request.getLocation()) && !matches.isEmpty()) {
			if (matches.stream().anyMatch(a ->
					AbwicklungsartCode.AWA001.equals(a.getAbwicklungsart())
							|| AbwicklungsartCode.AWA002.equals(a.getAbwicklungsart())
							|| AbwicklungsartCode.AWA130.equals(a.getAbwicklungsart())
							|| AbwicklungsartCode.AWA132.equals(a.getAbwicklungsart())
							|| AbwicklungsartCode.AWA133.equals(a.getAbwicklungsart())
							|| AbwicklungsartCode.AWA134.equals(a.getAbwicklungsart())
							|| AbwicklungsartCode.AWA135.equals(a.getAbwicklungsart()))) {
				matches = List.asList(matches.stream().filter(
						a -> !AbwicklungsartCode.AWA175.equals(a.getAbwicklungsart())).collect(Collectors.toList()));
			} else {
				matches.sort(Comparator.comparing(LWAuftragData::getAddDate));
				matches = List.asList(matches.getLast());
			}
		}
		final PacketSearchAuftragInfoReply reply = PacketSearchAuftragInfoReply.create(request, ErrorMessage.OK);
		reply.addSearchResults(matches);
		return reply;
	}

}

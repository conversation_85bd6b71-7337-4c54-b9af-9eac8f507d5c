package heag.huo.client.gui.haus.etappen;

import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.jdbc.ChangeState;
import ch.eisenring.core.jdbc.ObjectState;
import ch.eisenring.gui.GridBagConstraints;
import ch.eisenring.gui.components.HEAGLabel;
import ch.eisenring.gui.components.HEAGPanel;
import ch.eisenring.gui.components.HEAGScrollPane;
import ch.eisenring.gui.components.HEAGTable;
import ch.eisenring.logiware.LWProjektKey;
import ch.eisenring.logiware.code.pseudo.AbwicklungsartCode;
import ch.eisenring.lw.api.EtappeResolver;
import ch.eisenring.lw.api.LWEtappeData;
import ch.eisenring.lw.pojo.LWEtappePojo;
import ch.eisenring.lw.pojo.LWXEtappeAssocPojo;
import heag.huo.shared.network.PacketEtappenStoreRequest;
import heag.huo.shared.pojo.HUOKleinauftrag;
import heag.huo.shared.pojo.HUOKueche;
import heag.huo.shared.pojo.HUOObjekt;
import javax.swing.JTextField;
import javax.swing.RowFilter;
import javax.swing.ScrollPaneConstants;
import javax.swing.event.DocumentEvent;
import javax.swing.event.DocumentListener;
import javax.swing.table.TableRowSorter;
import java.awt.GridBagLayout;
import java.util.List;

class AssocPanel extends HEAGPanel implements EtappeResolver {

	final EtappenDialog parent;
	final EtappeResolver resolver;

	AssocTableModel tableModel = new AssocTableModel(this);
	private HEAGTable table = new HEAGTable(tableModel) {
		// @Override
		// protected void rowDoubleClicked(final int rowIndex, final Object rowObject, final MouseEvent event) {
		// final EtappeHolder holder = tableModel.getRow(rowIndex);
		// if (holder != null)
		// doEdit(holder);
		// }
	};
	final HEAGScrollPane scrollPane = new HEAGScrollPane(table);

	private final JTextField filterTextField = new JTextField();

	public AssocPanel(final EtappenDialog parent, final EtappeResolver resolver) {
		super(new GridBagLayout());
		this.parent = parent;
		this.resolver = resolver;
		initComponents();
		initLayout();
	}

	private void initComponents() {
		final List<EtappeAssoc> assocList = new ArrayList<>();
		HUOObjekt objekt = parent.context.getObjekt();
		for (final HUOKueche kueche : objekt.getKuechen()) {
			assocList.add(new EtappeAssoc(new EtappenItem(kueche)));
			for (HUOKleinauftrag auftrag : kueche.getKleinAuftraege()) {
				AbwicklungsartCode abwicklungsArt = auftrag.getAbwicklungsArt();
				if (AbwicklungsartCode.AWA132.equals(abwicklungsArt) || AbwicklungsartCode.AWA002.equals(abwicklungsArt))
					assocList.add(new EtappeAssoc(new EtappenItem(auftrag)));
			}
		}
		for (HUOKleinauftrag waschturm : objekt.getWaschTurmAuftraegeBasis()) {
			assocList.add(new EtappeAssoc(new EtappenItem(waschturm)));
		}
		tableModel.setContent(assocList);
		tableModel.applyLayout(table);
		scrollPane.setVerticalScrollBarPolicy(ScrollPaneConstants.VERTICAL_SCROLLBAR_ALWAYS);

		table.setRowSorter(null); // don't use HEAGTableRowSorter
		table.setAutoCreateRowSorter(true); // use default TableRowSorter
		filterTextField.getDocument().addDocumentListener(new DocumentListener() {
			@Override
			public void insertUpdate(DocumentEvent e) {
				updateFilter(filterTextField.getText());
			}

			@Override
			public void removeUpdate(DocumentEvent e) {
				updateFilter(filterTextField.getText());
			}

			@Override
			public void changedUpdate(DocumentEvent e) {
				updateFilter(filterTextField.getText());
			}

			private void updateFilter(String text) {
				if (table.getRowSorter() instanceof TableRowSorter) {
					((TableRowSorter<?>) table.getRowSorter()).setRowFilter(text.length() > 0 ? RowFilter.regexFilter("(?i)" + text.replace(" ", ".*")) : null);
				}
			}
		});
	}

	private void initLayout() {
		add(new HEAGLabel("Filter"), GridBagConstraints.label(0, 0).insetBottom(5));
		add(filterTextField, GridBagConstraints.field(1, 0).insetBottom(5));
		add(scrollPane, GridBagConstraints.area(0, 1).gridWidth(2));
	}

	@Override
	public LWEtappeData getEtappe(final Long etappeId) {
		return resolver.getEtappe(etappeId);
	}

	@Override
	public List<LWEtappeData> getEtappen() {
		return resolver.getEtappen();
	}

	void editCallback(final ObjectState<LWEtappePojo> holder) {
		repaint();
	}

	void prepareStore(final PacketEtappenStoreRequest request) {
		final List<EtappeAssoc> assocs = tableModel.getContent();
		for (final EtappeAssoc assoc : assocs) {
			final ChangeState state = assoc.getState();
			final LWProjektKey projektKey = assoc.getEttappenItem().getProjekt().getProjektKey();
			final Long etappeRowId = assoc.getEtappeId();
			final LWXEtappeAssocPojo hassoc = LWXEtappeAssocPojo.create(projektKey, etappeRowId);
			request.addAssoc(hassoc, state);
		}
	}

}
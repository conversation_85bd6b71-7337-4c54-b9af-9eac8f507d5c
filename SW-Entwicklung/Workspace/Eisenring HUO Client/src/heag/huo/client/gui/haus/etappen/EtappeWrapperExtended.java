package heag.huo.client.gui.haus.etappen;

import ch.eisenring.core.datatypes.date.DateGranularityCode;
import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.core.datatypes.strings.StringMaker;
import ch.eisenring.lw.api.LWEtappeData;

class EtappeWrapperExtended extends EtappeWrapper {

	public EtappeWrapperExtended(final LWEtappeData etappe) {
		super(etappe);
	}

	@Override
	public String toString() {
		if (etappe == null)
			return "";
		final int n = etappe.getOrdinalNumber();
		final StringMaker b = StringMaker.obtain();
		b.append("#");
		b.append(n);
		b.append(": ");
		b.append(etappe.getBegin(), TimestampUtil.DATE8);
		b.append(" - ");
		b.append(DateGranularityCode.DAY.round(etappe.getEnd(), -1), TimestampUtil.DATE8);
		if (etappe.getBezeichnung() != null && etappe.getBezeichnung().length() > 0) {
			b.append(" / ");
			b.append(etappe.getBezeichnung());
		}
		return b.release();
	}
}

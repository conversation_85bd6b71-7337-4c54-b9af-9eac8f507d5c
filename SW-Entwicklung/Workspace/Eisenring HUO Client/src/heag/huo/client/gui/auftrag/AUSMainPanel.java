package heag.huo.client.gui.auftrag;

import java.awt.GridBagLayout;
import java.io.File;
import java.io.IOException;
import javax.swing.ScrollPaneConstants;
import ch.eisenring.core.csv.CSVFile;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.core.io.binary.BinaryHolder;
import ch.eisenring.core.platform.Platform;
import ch.eisenring.core.poi.POIUtil;
import ch.eisenring.gui.GridBagConstraints;
import ch.eisenring.gui.components.HEAGPanel;
import ch.eisenring.gui.components.HEAGScrollPane;
import heag.huo.client.HUOClient;
import heag.huo.client.gui.aobase.AOExportHelper;
import heag.huo.shared.pojo.AUSMainData;
import org.apache.poi.ss.usermodel.Workbook;

@SuppressWarnings("serial")
public final class AUSMainPanel extends HEAGPanel {

	public final HUOClient client;
	public final AUSMainData data;
	final HEAGScrollPane scrollPane = new HEAGScrollPane();	
	final AUSHeaderPanel headerPanel;
	final AUSPositionPanel positionPanel;

	AUSMainPanel(final HUOClient client, final AUSMainData data) {
		super(new GridBagLayout());
		this.client = client;
		this.data = data;
		this.headerPanel = new AUSHeaderPanel(client, this);
		this.positionPanel = new AUSPositionPanel(client);
		initComponents();
		initLayout();
	}

	private void initComponents() {
		scrollPane.setHorizontalScrollBarPolicy(ScrollPaneConstants.HORIZONTAL_SCROLLBAR_AS_NEEDED);
		scrollPane.setVerticalScrollBarPolicy(ScrollPaneConstants.VERTICAL_SCROLLBAR_ALWAYS);
		headerPanel.setData(data);
		positionPanel.setData(data);
		scrollPane.setViewportView(positionPanel);
		scrollPane.getViewport().setBackground(AUSLayout.TABLE_BG);

	}
	
	private void initLayout() {
		add(headerPanel, GridBagConstraints.panel(0, 0).fill(GridBagConstraints.HORIZONTAL).weight(1, 0));
		add(scrollPane, GridBagConstraints.panel(0, 1).fill(GridBagConstraints.BOTH).weight(1, 1));
	}

	void doExcelExport() throws IOException {
		final AOExportHelper exportHelper = positionPanel.getExportHelper();
		if (exportHelper == null)
			return;
		final CSVFile csvFile = new CSVFile();
		exportHelper.appendToCSV(csvFile);

		Workbook workbook = POIUtil.createWorkbookFromCSVFile(csvFile);

		final BinaryHolder fileData = POIUtil.toBinary(workbook);
		final Platform platform = Platform.getPlatform();
		final String fileName = Strings.concat("Auftragsübersicht ", data.getKundenAuftrag().getProjektnummer(), ".xlsx");
		final File file = platform.createTempFile(fileData, fileName, false);
		platform.openFile(file.getAbsolutePath());
	}

}

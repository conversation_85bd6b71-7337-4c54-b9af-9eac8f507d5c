package heag.huo.client.gui.konditionen;

import ch.eisenring.gui.components.HEAGCodeComboBox;
import ch.eisenring.gui.components.HEAGLabel;
import ch.eisenring.gui.components.HEAGPanel;
import ch.eisenring.print.client.gui.PrintServiceCombo;
import heag.huo.client.HUOClient;

import java.awt.*;

public class EPKPrintOptionPanel extends HEAGPanel {

    final HEAGCodeComboBox<EPKPrintFormatCode> cmbFormat = new HEAGCodeComboBox<>(EPKPrintFormatCode.class);
    final PrintServiceCombo cmbPrinter;

    public EPKPrintOptionPanel(final HUOClient client) {
        super(new GridBagLayout());
        this.cmbPrinter = new PrintServiceCombo(client.getUserSettings());
        initComponents();
        initLayout();
    }

    private void initComponents() {
        cmbFormat.setSelectedCode(EPKPrintFormatCode.AUTO);
    }

    private void initLayout() {
        add(new HEAGLabel("Papierformat"), ch.eisenring.gui.GridBagConstraints.label(0, 0));
        add(cmbFormat, ch.eisenring.gui.GridBagConstraints.fixed(1, 0));

        add(new HEAGLabel("Drucker"), ch.eisenring.gui.GridBagConstraints.label(0, 1));
        add(cmbPrinter, ch.eisenring.gui.GridBagConstraints.fixed(1, 1));
    }
}

package heag.huo.client.gui.haus.etappen;

import javax.swing.AbstractAction;
import javax.swing.Action;
import javax.swing.JButton;
import javax.swing.JLabel;
import javax.swing.JPanel;
import javax.swing.JScrollPane;
import javax.swing.JTextField;
import javax.swing.event.DocumentEvent;
import javax.swing.event.DocumentListener;
import java.awt.BorderLayout;
import java.awt.Component;
import java.awt.event.ActionEvent;
import java.util.Arrays;
import java.util.Collection;

public class ChooserPanel<E> extends JPanel {
	private static final String FILTER_CAPTION = "Filter";
	private static final String SELECT_CAPTION = ">>";
	private static final String DESELECT_CAPTION = "<<";
	private static final String AVAILABLE_CAPTION = "nicht zugewiesen";
	private static final String ACTIVE_CAPTION = "zugewiesen";

	private final ChooserModel<E> model = new ChooserModel<>();
	private final ChooserListComponent<E> availableList;
	private final ChooserListComponent<E> activeList;

	// --------------------------------------------------------------------------------------------------------------------------------------------
	// -- Constructors
	// --------------------------------------------------------------------------------------------------------------------------------------------

	public ChooserPanel() {
		super(new BorderLayout());
		availableList = new ChooserListComponent<E>(model.getAvailableListModel()) {
			@Override
			public void onDoubleClick(ListModelItem<E> item) {
				model.selectSelected(Arrays.asList(item));
			}
		};
		activeList = new ChooserListComponent<E>(model.getActiveListModel()) {
			@Override
			public void onDoubleClick(ListModelItem<E> item) {
				model.deselectSelected(Arrays.asList(item));
			}
		};
		add(createTextFilterPanel(), BorderLayout.NORTH);
		add(createCenterPanel(), BorderLayout.CENTER);
	}

	// --------------------------------------------------------------------------------------------------------------------------------------------
	// -- Actions
	// --------------------------------------------------------------------------------------------------------------------------------------------

	private final Action actionSelect = new AbstractAction(SELECT_CAPTION) {
		@Override
		public void actionPerformed(ActionEvent e) {
			model.selectSelected(availableList.getSelectedValuesList());
		}
	};

	private final Action actionDeselect = new AbstractAction(DESELECT_CAPTION) {
		@Override
		public void actionPerformed(ActionEvent e) {
			model.deselectSelected(activeList.getSelectedValuesList());
		}
	};

	// --------------------------------------------------------------------------------------------------------------------------------------------
	// -- Model Delegates
	// --------------------------------------------------------------------------------------------------------------------------------------------

	public void addValue(E value, boolean active, String caption) {
		model.addValue(value, active, caption);
	}

	public void addValue(E value, boolean active) {
		addValue(value, active, value.toString());
	}

	public Collection<E> getSelectedValues() {
		return model.getSelectedValues();
	}

	public Collection<E> getUnselectedValues() {
		return model.getUnselectedValues();
	}

	// --------------------------------------------------------------------------------------------------------------------------------------------
	// -- Components
	// --------------------------------------------------------------------------------------------------------------------------------------------

	private Component createCenterPanel() {
		JPanel p = new JPanel(new HorizontalGridLayout(2, 0.8, 2));
		p.add(createLeftComponent());
		p.add(createCenterButtonPanel());
		p.add(createRightComponent());
		return p;
	}

	private Component createCenterButtonPanel() {
		JPanel p = new JPanel(new ButtonLayout());
		p.add(createButton(actionSelect));
		p.add(createButton(actionDeselect));
		return p;
	}

	private Component createLeftComponent() {
		return createNamedComponent(AVAILABLE_CAPTION, createVerticalScroll(availableList));
	}

	private Component createRightComponent() {
		return createNamedComponent(ACTIVE_CAPTION, createVerticalScroll(activeList));
	}

	private Component createButton(Action action) {
		return new JButton(action);
	}

	private Component createTextFilterPanel() {
		JPanel p = new JPanel(new BorderLayout());
		p.add(createCaptionLabel(FILTER_CAPTION), BorderLayout.WEST);
		p.add(createSearchTextField(), BorderLayout.CENTER);
		return p;
	}

	private Component createSearchTextField() {
		JTextField txt = new JTextField();
		txt.getDocument().addDocumentListener(new DocumentListener() {
			@Override
			public void insertUpdate(DocumentEvent e) {
				model.setFilter(txt.getText());
			}

			@Override
			public void changedUpdate(DocumentEvent e) {
				model.setFilter(txt.getText());
			}

			@Override
			public void removeUpdate(DocumentEvent e) {
				model.setFilter(txt.getText());
			}
		});
		return txt;
	}

	private Component createCaptionLabel(String text) {
		JLabel lbl = new JLabel(text + ":");
		lbl.setHorizontalAlignment(JLabel.RIGHT);
		return lbl;
	}

	private Component createVerticalScroll(Component component) {
		return new JScrollPane(component, JScrollPane.VERTICAL_SCROLLBAR_ALWAYS, JScrollPane.HORIZONTAL_SCROLLBAR_NEVER);
	}

	private Component createNamedComponent(String text, Component component) {
		JPanel p = new JPanel(new BorderLayout());
		JLabel lbl = new JLabel(text);
		lbl.setHorizontalAlignment(JLabel.CENTER);
		p.add(lbl, BorderLayout.NORTH);
		p.add(component, BorderLayout.CENTER);
		return p;
	}

}
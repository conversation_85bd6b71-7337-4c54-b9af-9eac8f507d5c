package heag.huo.client.gui.haus;

import heag.huo.client.HUOClient;
import heag.huo.shared.pojo.HUOObjekt;
import heag.huo.shared.pojo.HUOProjekt;

import javax.swing.JLabel;

import ch.eisenring.core.codetable.ErrorMessage;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.lw.pojo.LWXPAuftragPojo;

final class EditInfoBemerkungen extends EditInfo {

	public EditInfoBemerkungen(final HUOClient client, final JLabel guiElement, final HUOObjekt objekt) {
		super(client, guiElement, objekt);
	}

	@Override
	protected HUOProjekt getProjekt() {
		return objekt.getBasisProjekt();
	}

	@Override
	public Object getValue() {
		final LWXPAuftragPojo lwxPAuftrag = getLWXPAuftrag();
		return lwxPAuftrag == null ? null : lwxPAuftrag.getHausBemerkungenUebersicht();
	}

	@Override
	public ErrorMessage setValue(final Object value) {
		final LWXPAuftragPojo lwxPAuftrag = getLWXPAuftrag();
		if (lwxPAuftrag == null)
			return ErrorMessage.ERROR;
		final String s = Strings.toString(value);
		lwxPAuftrag.setHausBemerkungenUebersicht(s);
		final ErrorMessage result = store(getLWXPAuftrag());
		updateGUIElement(s);
		return result;
	}

	@Override
	public boolean isMultiLine() {
		return true;
	}

	@Override
	public int getLength() {
		return 1000;
	}

}

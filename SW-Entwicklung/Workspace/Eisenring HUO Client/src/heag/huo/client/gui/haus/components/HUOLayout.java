package heag.huo.client.gui.haus.components;

import java.awt.Color;
import java.awt.Font;

import javax.swing.BorderFactory;
import javax.swing.border.Border;

/**
 * Controls the look of "Haus<PERSON><PERSON>icht".
 */
public class HUOLayout {

	public final static Color COLOR_TRANSPARENT = new Color(0x00FFFFFF, true);
	public final static Color COLOR_TEXT = Color.BLACK;
	public final static Color COLOR_BACKGROUND = Color.WHITE;
	public final static Color COLOR_BORDER = Color.BLACK;
	public final static Color COLOR_BORDER_LIGHT = new Color(0xFFDDDDDD);
	public final static Color COLOR_BASIS_BG = new Color(0xFFE0E0FF);

	public final static Color COLOR_GLAS = new Color(0xFFAAFFDD);
	
	private final static int[] ETAPPEN_RGB = {
		0xF0E781,		// yellow
		0xEFC1F3,		// pink
		0xC8E9C6,		// pale green
		0x9DD9ED,		// aqua
		0xD5ED95,		// bright green
		0xAFF3E4,		// cyan
		0xF3CAAF,		// orange
		0xD4C4F5,		// purple,
		0xEEB3CF,		// rose
		0xC0CCEF,		// blue-gray
		0xD4CDCD,		// gray
		0xEEEAC7		// eggshell
	};

	public final static Color[] COLORS_ETAPPEN; static {
		final Color[] c = new Color[ETAPPEN_RGB.length];
		for (int i=0; i<c.length; ++i)
			c[i] = new Color(ETAPPEN_RGB[i]);
		COLORS_ETAPPEN = c;
	}

	private int baseSize;

	public HUOLayout() {
		this(16);
	}

	public HUOLayout(final int baseSize) {
		setBaseSize(baseSize);
	}

	public int getBaseSize() {
		return baseSize;
	}

	public void setBaseSize(final int baseSize) {
		this.baseSize = baseSize;
		initSizes(baseSize);
		initFonts(baseSize);
		initBorders(baseSize);
	}

	// --------------------------------------------------------------
	// ---
	// --- Basic sizes
	// ---
	// --------------------------------------------------------------
	protected int sizeH;
	protected int sizeW;

	protected void initSizes(final int size) {
		this.sizeH = size;
		this.sizeW = size * 10;
	}

	public int sizeH() {
		return sizeH;
	}

	public int sizeW() {
		return sizeW;
	}

	// --------------------------------------------------------------
	// ---
	// --- Borders
	// ---
	// --------------------------------------------------------------
	protected Border borderWohnung;
	protected Border borderHaus;

	protected void initBorders(final int size) {
		borderWohnung = BorderFactory.createCompoundBorder(
				BorderFactory.createEmptyBorder(4, 4, 4, 4),
				BorderFactory.createLineBorder(COLOR_BORDER, 2)
			);
		borderHaus = BorderFactory.createCompoundBorder(
				BorderFactory.createEmptyBorder(7, 7, 7, 7),
				BorderFactory.createLineBorder(COLOR_BORDER_LIGHT, 1)
			);
	}

	public Border borderWohung() {
		return borderWohnung;
	}

	public Border borderHaus() {
		return borderHaus;
	}
	
	// --------------------------------------------------------------
	// ---
	// --- Font handling
	// ---
	// --------------------------------------------------------------
	protected Font fontText;
	protected Font fontTextBold;
	protected Font fontTitle;
	
	protected void initFonts(final int size) {
		final int s1 = (size * 3) >> 2;
		final int s2 = (s1 * 3) >> 1;
		fontText = new Font(Font.SANS_SERIF, Font.PLAIN, s1);
		fontTextBold = new Font(Font.SANS_SERIF, Font.BOLD, s1);
		fontTitle = new Font(Font.SANS_SERIF, Font.BOLD, s2);
	}

	public final Font textFont() {
		return fontText;
	}

	public final Font textFontBold() {
		return fontTextBold;
	}

	public final Font titleFont() {
		return fontTitle;
	}

}

package heag.huo.client.gui.objekt;

import ch.eisenring.core.collections.List;
import ch.eisenring.core.collections.impl.ArrayList;
import heag.huo.client.gui.haus.print.PageData;
import heag.huo.client.gui.haus.print.PanelPrintSlicer;
import javax.swing.JComponent;
import java.awt.Component;
import java.awt.Rectangle;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Set;
import java.util.TreeSet;
import java.util.stream.Collectors;

public class OUSSlicer implements PanelPrintSlicer {

    private final JComponent panel;

    public OUSSlicer(final JComponent panel) {
        this.panel = panel;
    }

    @Override
    public List<Object> slice() {
        final List<Object> pageList = new ArrayList<>();
        final OUSHeaderPanel headerPanel = (OUSHeaderPanel) panel.getComponent(0);
        final OUSDetailPanel detailsPanel = (OUSDetailPanel) panel.getComponent(1);
        final int panelWidth = detailsPanel.getWidth();
        final double maxHeightPerPage = detailsPanel.getWidth() / 1.45;

        // first page
        Set<Integer> detailComponentsYCoordinates = Arrays.stream(detailsPanel.getComponents()).map(Component::getY).collect(Collectors.toSet());
        java.util.List<Integer> sortedYCoordinates = new ArrayList<>(new TreeSet<>(detailComponentsYCoordinates));
        int firstPageY = sortedYCoordinates.stream().filter(y -> y + headerPanel.getHeight() < maxHeightPerPage).max(Comparator.comparing(Integer::valueOf)).get() + headerPanel.getHeight();
        Rectangle regionFirstPage = new Rectangle(0, 0, panelWidth, firstPageY);
        PageData firstPage = new PageData(panel, regionFirstPage);
        pageList.add(firstPage);

        // if more than one page is necessary to print the whole panel
        int maxY = sortedYCoordinates.stream().max(Comparator.comparing(Integer::valueOf)).get();
        int previousPageY = firstPageY - headerPanel.getHeight();
        List<Integer> consideredCoordinates = new ArrayList<>();
        while (previousPageY < maxY) {
            sortedYCoordinates = sortedYCoordinates.subList(sortedYCoordinates.indexOf(previousPageY), sortedYCoordinates.size());
            for (int y : sortedYCoordinates) {
                if (y - previousPageY < maxHeightPerPage) {
                    consideredCoordinates.add(y);
                    continue;
                }
                break;
            }
            int currentPageY = consideredCoordinates.stream().max(Comparator.comparing(Integer::valueOf)).get();
            Rectangle region = new Rectangle(0, previousPageY, panelWidth, currentPageY - previousPageY);
            PageData currentPage = new PageData(detailsPanel, region);
            pageList.add(currentPage);
            previousPageY = currentPageY;
            consideredCoordinates.clear();
        }
        return pageList;
    }

}

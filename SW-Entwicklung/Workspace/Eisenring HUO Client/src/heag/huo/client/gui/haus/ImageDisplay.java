package heag.huo.client.gui.haus;

import heag.huo.client.gui.haus.components.HUOLayout;

import java.awt.Dimension;
import java.awt.Graphics;
import java.awt.Graphics2D;
import java.awt.Image;
import java.awt.RenderingHints;
import java.util.Map;

import javax.swing.JPanel;

import ch.eisenring.core.collections.impl.HashMap;
import ch.eisenring.core.resource.image.ImageUtil;

@SuppressWarnings("serial")
class ImageDisplay extends JPanel {

	final static Map<Object, Object> RENDERING_HINTS;
	static {
		final Map<Object, Object> m = new HashMap<>();
		m.put(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BICUBIC);
		m.put(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
		RENDERING_HINTS = m;
	}
	
	Image image;
	
	public ImageDisplay(final Image image) {
		this.image = image;
		setBackground(HUOLayout.COLOR_BACKGROUND);
		setForeground(HUOLayout.COLOR_TEXT);
	}

	@Override
	protected void paintChildren(Graphics g) {
		// intentionally empty
	}

	@Override
	protected void paintComponent(final Graphics g) {
		final int w = getWidth();
		final int h = getHeight();
		g.setColor(getBackground());
		g.fillRect(0, 0, w, h);
		final Image image = this.image;
		if (image == null)
			return;
		// determine scaled image size
		final int iw = image.getWidth(null);
		final int ih = image.getHeight(null);
		final Dimension d = ImageUtil.getScaleToLimitSize(iw, ih, w, h, true);
		// draw the image
		final Graphics2D g2d = (Graphics2D) g;
		g2d.setRenderingHints(RENDERING_HINTS);
		g2d.drawImage(image, 0, 0, d.width, d.height, null);
	}

	public void setImage(final Image image) {
		this.image = image;
		repaint();
	}

}

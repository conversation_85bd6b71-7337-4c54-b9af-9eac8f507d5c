package heag.huo.client.gui.einzel;

import java.awt.Dimension;

import ch.eisenring.core.datatypes.date.Period;
import ch.eisenring.core.tag.TagSet;
import ch.eisenring.gui.BorderLayout;
import ch.eisenring.gui.model.StoreResults;
import ch.eisenring.gui.window.AbstractBaseDialog;
import ch.eisenring.gui.window.DialogTags;
import ch.eisenring.gui.window.SACButtonPanel;
import ch.eisenring.gui.window.WindowTags;
import ch.eisenring.lw.pojo.LWEtappePojo;
import ch.eisenring.lw.pojo.LWXPAuftragPojo;
import heag.huo.client.HUOClient;
import heag.huo.client.resources.images.Images;
import heag.huo.shared.network.PacketEtappeEKStoreReply;
import heag.huo.shared.network.PacketEtappeEKStoreRequest;

@SuppressWarnings("serial")
final class EKEtappeDialog extends AbstractBaseDialog {
	
	public final HUOClient client;
	final EKEtappeContext context;
	final SACButtonPanel buttonPanel = new SACButtonPanel();	
	final EKEtappePanel etappePanel;

	public EKEtappeDialog(final HUOClient client, final EKEtappeContext context) {
		super(new TagSet(
				WindowTags.ICON, Images.CALENDAR,
				WindowTags.TITLE, "Etappe Einzelküche",
				WindowTags.POSITION_SETTINGS_ID, "EKEtappeWindow",
				WindowTags.RESIZEABLE, WindowTags.RESIZEABLE_NO,
				WindowTags.MINIMUM_SIZE, new Dimension(320, 128),
				WindowTags.LAYOUTMANAGER, new BorderLayout(),
				DialogTags.MODALITY, DialogTags.MODALITY_MODAL,
				DialogTags.DIALOG_OWNER, context.ownerWindow)
			);
		this.client = client;
		this.context = context;
		this.etappePanel = new EKEtappePanel(context);
		initComponents();
		initLayout();
		pack();
	}

	private void initComponents() {
		buttonPanel.configureApplyButton(null, null);
	}

	private void initLayout() {
		add(etappePanel, BorderLayout.CENTER);
		add(buttonPanel, BorderLayout.SOUTH);
	}

	@Override
	protected boolean doApply() {
		return super.doApply();
	}

	@Override
	public void storeView(final StoreResults results, final Object model) {
		super.storeView(results, model);
		if (!results.isSuccess())
			return;
		// create etappe if needed
		final LWEtappePojo etappe = context.getOrCreateEtappe();
		final LWXPAuftragPojo projekt = context.projekt;
		projekt.setEtappeRowId(etappe.getRowId());
		final Period period = (Period) etappePanel.periodField.getValue();
		etappe.setBegin(period.getBegin());
		etappe.setEnd(period.getEnd());
		// store data
		final PacketEtappeEKStoreRequest request = PacketEtappeEKStoreRequest.create(projekt, etappe);
		final PacketEtappeEKStoreReply reply = (PacketEtappeEKStoreReply) client.sendAndWait(request);
		if (!reply.isValid()) {
			results.add(reply.getMessage(), etappePanel);
		}
	}

}

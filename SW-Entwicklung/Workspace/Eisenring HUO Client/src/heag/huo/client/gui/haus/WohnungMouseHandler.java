package heag.huo.client.gui.haus;

import heag.huo.shared.pojo.HUOAuftrag;

import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;

final class WohnungMouseHandler extends MouseAdapter {

	final HUOAuftrag auftrag;
	final ClickHandler handler;
	
	public WohnungMouseHandler(final HUOAuftrag auftrag, final ClickHandler handler) {
		this.auftrag = auftrag;
		this.handler = handler;
	}

	@Override
	public void mouseReleased(final MouseEvent event) {
		if (handler == null || event.isConsumed())
			return;
		event.consume();
		handler.clicked(auftrag);
	}

}

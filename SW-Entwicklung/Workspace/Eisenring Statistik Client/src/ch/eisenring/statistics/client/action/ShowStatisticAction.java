package ch.eisenring.statistics.client.action;

import ch.eisenring.app.client.AppCore;
import ch.eisenring.commons.resource.images.Images;
import ch.eisenring.statistics.client.StatisticClient;
import ch.eisenring.statistics.client.gui.StatisticDialog;
import ch.eisenring.statistics.shared.codetables.RightCode;

public final class ShowStatisticAction extends AbstractSTSAction {

	public ShowStatisticAction(final AppCore core) {
		super((StatisticClient) core.getComponent(StatisticClient.class, true),
				Images.PIECHART, "Statistiken...");
		client.CODES_ENUMERATED.addObserver(observer);
		addPermissionObserver();
	}

	@Override
	protected boolean isEnabledImpl() {
		if (!Boolean.TRUE.equals(client.CODES_ENUMERATED.get()))
			return false;
		return RightCode.USE_STATISTICS.isPermitted();
	}

	@Override
	protected void performAction() {
		StatisticDialog.show(client);
	}

}

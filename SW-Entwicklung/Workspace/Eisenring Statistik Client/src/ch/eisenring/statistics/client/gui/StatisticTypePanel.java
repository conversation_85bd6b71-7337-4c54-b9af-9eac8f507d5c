package ch.eisenring.statistics.client.gui;

import java.awt.GridBagLayout;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.ItemEvent;
import java.awt.event.ItemListener;
import java.util.Collection;

import javax.swing.BorderFactory;
import javax.swing.JButton;

import ch.eisenring.core.codetype.AbstractCode;
import ch.eisenring.core.usersettings.UserSettings;
import ch.eisenring.gui.components.CompoundLinePanel;
import ch.eisenring.gui.components.HEAGCodeComboBox;
import ch.eisenring.gui.components.HEAGPanel;
import ch.eisenring.gui.model.ValidationResults;
import ch.eisenring.gui.util.LayoutUtil;
import ch.eisenring.statistics.shared.codetables.StatisticGroupCode;
import ch.eisenring.statistics.shared.codetables.StatisticTypeCode;
import ch.eisenring.statistics.shared.network.PacketStatisticResponse;

@SuppressWarnings("serial")
public class StatisticTypePanel extends HEAGPanel {

	private final static String KEY_LAST_GROUP = "StatisticTypePanel:StatisticGroupCode";
	private final static String KEY_LAST_TYPE = "StatisticTypePanel:StatisticTypeCode";
	
	private final StatisticControlPanel controlPanel;
	
	private final JButton btnRefresh = new JButton("Auswerten");
	private final JButton btnClear = new JButton("Alle geöffneten Auswertungen schliessen");

	private final HEAGCodeComboBox<StatisticTypeCode> cmbType;
	private final HEAGCodeComboBox<StatisticGroupCode> cmbGroup;
	
	private final ActionListener refreshListener = new ActionListener() {
		@Override
		public void actionPerformed(final ActionEvent event) {
			controlPanel.refresh();
		}
	};

	private final ActionListener clearListener = new ActionListener() {
		@Override
		public void actionPerformed(final ActionEvent event) {
			controlPanel.clearResults();
		}
	};

	public StatisticTypePanel(StatisticControlPanel controlPanel) {
		this.controlPanel = controlPanel;
		cmbType = new HEAGCodeComboBox<StatisticTypeCode>(StatisticTypeCode.class);
		cmbType.addItemListener(new ItemListener() {
			@Override
			public void itemStateChanged(final ItemEvent event) {
				if (event.getStateChange() == ItemEvent.SELECTED) {
					StatisticTypeCode type = cmbType.getSelectedCode();
					if (type == null || !type.isPermitted()) {
						btnRefresh.setEnabled(false);
					} else {
						btnRefresh.setEnabled(true);
					}
				}
			}
		});
		cmbType.setSelectedCode(null);
		cmbGroup = new HEAGCodeComboBox<StatisticGroupCode>(StatisticGroupCode.class);
		cmbGroup.addItemListener(new ItemListener() {
			@Override
			public void itemStateChanged(final ItemEvent event) {
				if (event.getStateChange() == ItemEvent.SELECTED) {
					StatisticGroupCode group = cmbGroup.getSelectedCode();
					if (group == null) {
						cmbType.populate((Collection<StatisticTypeCode>) null, true);
					} else {
						cmbType.populate(group.getItemsInGroup(), true);
					}
				}
			}
		});
		initComponents();
		initLayout();
	}

	private void initComponents() {
		setBorder(BorderFactory.createEtchedBorder());
		btnRefresh.addActionListener(refreshListener);
		btnClear.addActionListener(clearListener);
		btnClear.setToolTipText("<html>Entfernt alle offenen Auswertungen aus dem Ergebnisfenster.<br>"
				                + "Die Wirkung ist die gleiche als ob Sie für jedes einzelne Ergebnis den"
				                + "Schliessen-Button angeklickt hätten.");
		btnRefresh.setToolTipText("<html>Mit diesem Button wird die oben augewählte Auswertung gestartet.<br>"
				                  + "Das Ergebnis wird unten dargestellt, sobald verfügbar.<br>"
				                  + "Wenn der Button gesperrt ist, fehlt Ihnen die Berechtigung für die<br>"
				                  + "gewählte Auswertung.");
		cmbGroup.setToolTipText("<html>Wählen Sie hier das Thema zu dem Sie Auswertungen machen wollen.<br>"
				                + "Die darunterliegene Combobox zeigt dann nur Auswertungen zu dem Thema.<br>"
				                + "Wenn Sie hier nichts auswählen werden unten alle existierenden Auswertungen angezeigt.<br>");
	}
	
	private void initLayout() {
		removeAll();
		setLayout(new GridBagLayout());
		final LayoutUtil l = new LayoutUtil(2);

		add(cmbGroup, l.field(1));

		add(new HEAGPanel(), l.button());

		add(cmbType, l.field(1));
		l.nextLine();

		CompoundLinePanel linePanel = new CompoundLinePanel();
		linePanel.addField(btnClear, 10);
		linePanel.addField(btnRefresh, 10);
		add(linePanel, l.area(10, 0, 2));
	}
	
	public StatisticTypeCode getSelectedType() {
		return cmbType.getSelectedCode();
	}

	public void packetReceived(final PacketStatisticResponse packet) {
		// ignore
	}

	public void restoreUserSettings(final UserSettings settings) {
		final int gid = settings.restore(KEY_LAST_GROUP, 100);
		final StatisticGroupCode group = (StatisticGroupCode) AbstractCode.getById(gid, StatisticGroupCode.class);
		cmbGroup.setSelectedCode(group);
		final int tid = settings.restore(KEY_LAST_TYPE, 6040);
		final StatisticTypeCode type = (StatisticTypeCode) AbstractCode.getById(tid, StatisticTypeCode.class);
		cmbType.setSelectedCode(type);
	}
	
	public void saveUserSettings(final UserSettings settings) {
		final StatisticGroupCode group = cmbGroup.getSelectedCode();
		settings.save(KEY_LAST_GROUP, AbstractCode.getId(group, 0));
		final StatisticTypeCode type = cmbType.getSelectedCode();
		settings.save(KEY_LAST_TYPE, AbstractCode.getId(type, 0));
	}
	

	@Override
	public void validateView(final ValidationResults results, final Object model) {
		super.validateView(results, model);
		if (AbstractCode.isNull(cmbType.getSelectedCode())) 
			results.add("Kein Auswertungstyp ausgewählt", cmbType);
	}	

}

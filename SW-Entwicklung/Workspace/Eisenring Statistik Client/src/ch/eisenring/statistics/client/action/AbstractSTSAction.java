package ch.eisenring.statistics.client.action;

import ch.eisenring.app.client.action.AbstractAPPAction;
import ch.eisenring.core.observable.Observable;
import ch.eisenring.core.observable.Observer;
import ch.eisenring.core.usersettings.UserSettings;
import ch.eisenring.statistics.client.StatisticClient;

/**
 * Base class for application actions
 */
public abstract class AbstractSTSAction extends AbstractAPPAction<StatisticClient> {

	protected final Observer<Object> observer = new Observer<Object>() {
		@Override
		public void observableChanged(final Observable<Object> observeable) {
			isEnabled();
		}
	};

	protected AbstractSTSAction(final StatisticClient client, final Object ... properties) {
		super(client, properties);
	}

	/**
	 * Helper method to access the client instance
	 */
	public StatisticClient getClient() {
		return client;
	}

	/**
	 * Helper method to access the user settings
	 */
	public UserSettings getUserSettings() {
		return client.getCore().getUserSettings();
	}

}

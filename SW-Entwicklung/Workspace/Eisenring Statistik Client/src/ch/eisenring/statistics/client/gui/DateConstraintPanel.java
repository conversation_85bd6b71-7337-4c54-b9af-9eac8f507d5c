package ch.eisenring.statistics.client.gui;

import java.awt.GridBagLayout;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.FocusAdapter;
import java.awt.event.FocusEvent;
import java.awt.event.FocusListener;
import java.util.Calendar;
import java.util.Date;

import javax.swing.BorderFactory;
import javax.swing.ButtonGroup;
import javax.swing.JLabel;
import javax.swing.JRadioButton;
import javax.swing.SwingConstants;
import javax.swing.border.EtchedBorder;

import ch.eisenring.core.datatypes.date.DateGranularityCode;
import ch.eisenring.core.datatypes.date.DateUtil;
import ch.eisenring.core.datatypes.date.HEAGCalendar;
import ch.eisenring.gui.balloontip.BalloonTipManager;
import ch.eisenring.gui.components.CompoundLinePanel;
import ch.eisenring.gui.components.HEAGDateField;
import ch.eisenring.gui.components.HEAGFloatingButton;
import ch.eisenring.gui.components.HEAGLabel;
import ch.eisenring.gui.components.HEAGPanel;
import ch.eisenring.gui.model.ValidationResults;
import ch.eisenring.gui.resource.images.Images;
import ch.eisenring.gui.util.GUIUtil;
import ch.eisenring.gui.util.LayoutUtil;
import ch.eisenring.statistics.shared.network.PacketStatisticResponse;

@SuppressWarnings("serial")
public class DateConstraintPanel extends HEAGPanel {

	private final ActionListener kwNextListener = new ActionListener() {
		@Override
		public void actionPerformed(final ActionEvent e) {
			final HEAGCalendar c = HEAGCalendar.obtain(dateKW);
			c.add(Calendar.WEEK_OF_YEAR, 1);
			dateKW = c.getTime();
			c.release();
			updateFields();
			rbtKW.setSelected(true);
		}
	};
	
	private final ActionListener kwPrevListener = new ActionListener() {
		@Override
		public void actionPerformed(final ActionEvent e) {
			final HEAGCalendar c = HEAGCalendar.obtain(dateKW);
			c.add(Calendar.WEEK_OF_YEAR, -1);
			dateKW = c.getTime();
			c.release();
			updateFields();
			rbtKW.setSelected(true);
		}
	};

	private final ActionListener monthNextListener = new ActionListener() {
		@Override
		public void actionPerformed(final ActionEvent e) {
			final HEAGCalendar c = HEAGCalendar.obtain(dateMonth);
			c.add(Calendar.MONTH, 1);
			dateMonth = c.getTime();
			c.release();
			updateFields();
			rbtMonth.setSelected(true);
		}
	};
	
	private final FocusListener focusListener = new FocusAdapter() {
		@Override
		public void focusGained(final FocusEvent e) {
			rbtManual.setSelected(true);
		}
	};
	
	private final ActionListener monthPrevListener = new ActionListener() {
		@Override
		public void actionPerformed(final ActionEvent e) {
			final HEAGCalendar c = HEAGCalendar.obtain(dateMonth);
			c.add(Calendar.MONTH, -1);
			dateMonth = c.getTime();
			c.release();
			updateFields();
			rbtMonth.setSelected(true);
		}
	};

	private final HEAGLabel lblConstraint = new HEAGLabel("Auswertungszeitraum");

	private final JRadioButton rbtKW = new JRadioButton("Kalenderwoche");
	private final JRadioButton rbtMonth = new JRadioButton("Kalendermonat");
	private final JRadioButton rbtManual = new JRadioButton("Manuell");

	private final HEAGFloatingButton btnKWPrev = new HEAGFloatingButton(Images.BACK, 16);
	private final JLabel txtKW = new JLabel();
	private final HEAGFloatingButton btnKWNext = new HEAGFloatingButton(Images.FORWARD, 16);

	private final HEAGFloatingButton btnMonthPrev = new HEAGFloatingButton(Images.BACK, 16);
	private final JLabel txtMonth = new JLabel();
	private final HEAGFloatingButton btnMonthNext = new HEAGFloatingButton(Images.FORWARD, 16);

	private final HEAGLabel lblFrom = new HEAGLabel("von");
	private final HEAGDateField dtfFrom = new HEAGDateField();
	private final HEAGLabel lblUpTo = new HEAGLabel("bis");
	private final HEAGDateField dtfUpTo = new HEAGDateField();

	private Date dateKW = new Date();
	private Date dateMonth = new Date();

	public DateConstraintPanel() {
		initComponents();
		updateFields();
		initLayout();
	}

	private void initComponents() {
		setBorder(BorderFactory.createEtchedBorder());
		// --- radio button group (for mutual exclusion)
		final ButtonGroup rbg = new ButtonGroup();
		rbg.add(rbtKW);
		rbg.add(rbtMonth);
		rbg.add(rbtManual);
		rbtKW.setSelected(true);

		// --- attach listeners
		btnKWNext.addActionListener(kwNextListener);
		btnKWPrev.addActionListener(kwPrevListener);
		btnMonthNext.addActionListener(monthNextListener);
		btnMonthPrev.addActionListener(monthPrevListener);
		dtfFrom.addFocusListener(focusListener);
		dtfUpTo.addFocusListener(focusListener);
		
		// --- layout hacks
		txtKW.setHorizontalAlignment(SwingConstants.CENTER);
		txtMonth.setHorizontalAlignment(SwingConstants.CENTER);
		GUIUtil.setMinSizeForText(txtKW, " 00 / 0000 ");
		GUIUtil.setMinSizeForText(txtMonth, " 00 / 0000 ");
		txtKW.setBorder(BorderFactory.createEtchedBorder(EtchedBorder.LOWERED));
		txtMonth.setBorder(BorderFactory.createEtchedBorder(EtchedBorder.LOWERED));
		GUIUtil.makeSameHeight(btnKWNext, btnKWPrev, txtKW);
		GUIUtil.makeSameHeight(btnMonthNext, btnMonthPrev, txtMonth);
	}

	private void initLayout() {
		final LayoutUtil l = new LayoutUtil(3);
		removeAll();
		setLayout(new GridBagLayout());

		add(lblConstraint, l.field(1));
		add(rbtKW, l.fixed(1));
		{ 	// --- kw panel
			final CompoundLinePanel pnlKW = new CompoundLinePanel();
			pnlKW.addButton(btnKWPrev);
			pnlKW.addFixed(txtKW);
			pnlKW.addButton(btnKWNext);
			pnlKW.addField(new HEAGPanel(), 1);
			add(pnlKW, l.panel());
		}
		
		l.label();
		add(rbtMonth, l.fixed(1));
		{	// --- month panel
			final CompoundLinePanel pnlMonth = new CompoundLinePanel();
			pnlMonth.addButton(btnMonthPrev);
			pnlMonth.addFixed(txtMonth);
			pnlMonth.addButton(btnMonthNext);
			pnlMonth.addField(new HEAGPanel(), 1);
			add(pnlMonth, l.panel());
		}
		
		l.label();
		add(rbtManual, l.fixed(1));
		{	// --- manual panel
			final CompoundLinePanel pnlManual = new CompoundLinePanel();
			pnlManual.addLabel(lblFrom);
			pnlManual.addFixed(dtfFrom);
			pnlManual.addLabel(lblUpTo);
			pnlManual.addFixed(dtfUpTo);
			add(pnlManual, l.panel());
		}
	}

	protected int getConstraintMode() {
		if (rbtKW.isSelected()) {
			return 0;
		} else if (rbtMonth.isSelected()) {
			return 1;
		} else {
			return 2;
		}
	}

	@Override
	public void validateView(final ValidationResults results, final Object model) {
		super.validateView(results, model);

		final Date from = getDateFrom();
		final Date upto = getDateUpTo();
		if (from == null) {
			results.add("Von-Datum ist ein Pflichtfeld", dtfFrom);
		}
		if (upto == null) {
			results.add("Bis-Datum ist ein Pflichtfeld", dtfUpTo);
		}
		if (upto.before(from)) {
			results.add("Das Bis-Datum darf nicht vor dem Von-Datum liegen", dtfUpTo);
		}
	}

	public boolean isContentValid() {
		BalloonTipManager.clearAll(this);
		final Date from = getDateFrom();
		final Date upto = getDateUpTo();
		if (from == null) {
			BalloonTipManager.showError(dtfFrom, "Von-Datum ist ein Pflichtfeld");
			return false;
		} else if (upto == null) {
			BalloonTipManager.showError(dtfUpTo, "Bis-Datum ist ein Pflichtfeld");
			return false;
		} else if (upto.before(from)) {
			BalloonTipManager.showError(dtfUpTo, "Das Bis-Datum darf nicht vor dem Von-Datum liegen");
			return false;
		}
		return true;
	}

	public Date getDateFrom() {
		Date result = null;
		switch (getConstraintMode()) {
			default:
				break;
			case 0: // KW
				result = DateUtil.findMonday(dateKW, 0);
				break;
			case 1: // Month
				final HEAGCalendar c = HEAGCalendar.obtain(dateMonth);
				c.set(Calendar.DAY_OF_MONTH, 1);
				result = c.getTime();
				c.release();
				break;
			case 2: // Manual
				result = dtfFrom.getDate();
				break;
		}
		return result;
	}
	
	public Date getDateUpTo() {
		final HEAGCalendar c = HEAGCalendar.obtain();
		Date result = null;
		switch (getConstraintMode()) {
			default:
				break;
			case 0: // KW
				c.setTime(dateKW);
				DateGranularityCode.KW.round(c, 0);
				c.add(Calendar.DAY_OF_MONTH, 6);
				result = c.getTime();
				break;
			case 1: // Month
				c.setTime(dateMonth);
				c.set(Calendar.DAY_OF_MONTH, 1);
				c.add(Calendar.MONTH, 1);
				c.add(Calendar.DAY_OF_MONTH, -1);
				result = c.getTime();
				break;
			case 2: // Manual
				result = dtfUpTo.getDate();
				break;
		}
		c.release();
		return result;
	}

	protected void updateFields() {
		final HEAGCalendar calendar = HEAGCalendar.obtain();
		calendar.setTime(dateKW);
		txtKW.setText(calendar.get(Calendar.WEEK_OF_YEAR) + " / " + calendar.get(Calendar.YEAR));
		calendar.setTime(dateMonth);
		txtMonth.setText((calendar.get(Calendar.MONTH)+1) + " / " + calendar.get(Calendar.YEAR));
		calendar.release();
	}

	public void packetReceived(final PacketStatisticResponse packet) {
		// ignore
	}

}

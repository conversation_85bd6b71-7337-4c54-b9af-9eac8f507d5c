package ch.eisenring.statistics.client.network;

import ch.eisenring.network.PacketSink;
import ch.eisenring.network.implementation.PacketDispatchMode;
import ch.eisenring.network.packet.AbstractPacket;
import ch.eisenring.statistics.client.StatisticClient;
import ch.eisenring.statistics.shared.network.PacketEnumerateCodesResponse;

public final class EnumerateCodesResponseHandler extends AbstractSTSPacketHandler {

	EnumerateCodesResponseHandler(final StatisticClient client) {
		super(client, PacketEnumerateCodesResponse.class, PacketDispatchMode.ASYNCHRONOUS);
	}
	
	@Override
	public void handle(final AbstractPacket abstractPacket, final PacketSink sink) {
		// ignore the packet, the packet already served its purpose
		// when it was received.
		client.CODES_ENUMERATED.set(Boolean.TRUE);
	}

}

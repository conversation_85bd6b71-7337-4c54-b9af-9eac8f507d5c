package ch.eisenring.vmd.shared.model.meta;

import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.core.util.VarArgs;
import ch.eisenring.model.MetaAttribute;
import ch.eisenring.model.MetaClass;
import ch.eisenring.model.MetaClassMember;
import ch.eisenring.model.MetaProperty;
import ch.eisenring.model.Model;
import ch.eisenring.model.PrimaryKeyOrdinal;
import ch.eisenring.model.engine.jdbc.ColumnSpecifier;
import ch.eisenring.model.engine.jdbc.TableSpecifier;
import ch.eisenring.model.factory.AttributeFactory;
import ch.eisenring.model.mapping.POJOField;
import ch.eisenring.model.primarykey.LongPrimaryKey;
import ch.eisenring.vmd.shared.VMDConstants;
import ch.eisenring.vmd.shared.model.pojo.VMDDayStatistic;

public interface VMDDayStatisticMeta {

	TableSpecifier TABLE = TableSpecifier.get(VMDConstants.VMD_DATABASE, "VMDDayStatistic");

	MetaAttribute ATR_ROWID = AttributeFactory.pLong(
			"rowId", 0, 1L, Long.MAX_VALUE,
			ColumnSpecifier.get(TABLE, "rowId"),
			POJOField.get(VMDDayStatistic.class, "rowId"),
			PrimaryKeyOrdinal.get(1));

	MetaAttribute ATR_DAY = AttributeFactory.date(
			"dayKey", TimestampUtil.NULL_TIMESTAMP,
			ColumnSpecifier.get(TABLE, "dayKey"),
			POJOField.get(VMDDayStatistic.class, "dayKey"));
					
	MetaAttribute ATR_PROBLEMOWNER = AttributeFactory.string(
			"problemOwner", null, 10, 
			ColumnSpecifier.get(TABLE, "problemOwner"),
			POJOField.get(VMDDayStatistic.class, "problemOwner"));

	MetaAttribute ATR_PROBLEMCOUNT = AttributeFactory.pLong(
			"problemCount", 0L, 0L, Long.MAX_VALUE,
			ColumnSpecifier.get(TABLE, "problemCount"),
			POJOField.get(VMDDayStatistic.class, "problemCount"));

	MetaClass METACLASS = new MetaClass(
			"VMDDayStatistic", (Class<Model>) null,
			LongPrimaryKey.class,
			null,
			// Attributes
			new VarArgs<MetaProperty>(MetaProperty.class).flat(
			ColumnSpecifier.get(TABLE, "rowId"),
			TABLE,
			MetaClassMember.discoverMetaClassMembers(VMDDayStatisticMeta.class)
			));

}

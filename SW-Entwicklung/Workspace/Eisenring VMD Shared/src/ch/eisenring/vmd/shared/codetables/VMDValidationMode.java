package ch.eisenring.vmd.shared.codetables;

import ch.eisenring.core.codetype.StaticCode;

/**
 * Controls when rule is validated
 */
public final class VMDValidationMode extends StaticCode {

	/**
	 * Rule is validated in all modes
	 */
	public final static VMDValidationMode ALWAYS = new VMDValidationMode(
			0, "BS", "Batch- und Sofortprüfung");

	/**
	 * Rule is validated only in batch mode
	 */
	public final static VMDValidationMode BATCH = new VMDValidationMode(
			1, "B", "Batchprüfung");

	/**
	 * Rule is validated only in immediate mode
	 */
	public final static VMDValidationMode IMMEDIATE = new VMDValidationMode(
			2, "S", "Sofortprüfung");
	
	private VMDValidationMode(final int id, final String shortText, final String longText) {
		super(id, Integer.valueOf(id), shortText, longText);
	}

	// --------------------------------------------------------------
	// ---
	// --- Code specific API
	// ---
	// --------------------------------------------------------------
	/**
	 * Checks if given mode matches this mode.
	 * 
	 * A match is given if either this oder the argument mode is
	 * "always", or the modes are identical.
	 */
	public boolean matches(final VMDValidationMode mode) {
		return equals(this, ALWAYS) || equals(mode, ALWAYS) || equals(this, mode);
	}

	/**
	 * Returns true if this mode requires persistent results
	 */
	public boolean isPersistent() {
		return !equals(IMMEDIATE);
	}

}

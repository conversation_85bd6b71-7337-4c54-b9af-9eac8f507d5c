package ch.eisenring.vmd.shared.model.meta;

import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.core.util.VarArgs;
import ch.eisenring.model.MetaAttribute;
import ch.eisenring.model.MetaClass;
import ch.eisenring.model.MetaClassMember;
import ch.eisenring.model.MetaProperty;
import ch.eisenring.model.Model;
import ch.eisenring.model.PrimaryKeyOrdinal;
import ch.eisenring.model.engine.jdbc.ColumnSpecifier;
import ch.eisenring.model.engine.jdbc.TableSpecifier;
import ch.eisenring.model.factory.AttributeFactory;
import ch.eisenring.model.mapping.POJOField;
import ch.eisenring.model.primarykey.LongPrimaryKey;
import ch.eisenring.vmd.shared.VMDConstants;
import ch.eisenring.vmd.shared.codetables.VMDResultStatusType;
import ch.eisenring.vmd.shared.codetables.VMDSeverityType;
import ch.eisenring.vmd.shared.model.pojo.VMDRuleResult;

public interface VMDRuleResultMeta {

	TableSpecifier TABLE = TableSpecifier.get(VMDConstants.VMD_DATABASE, "VMDRuleResult");

	ColumnSpecifier PKCOL = ColumnSpecifier.get(TABLE, "rowId");

	MetaAttribute ATR_ROWID = AttributeFactory.pLong(
			"rowId", 0, 1L, Long.MAX_VALUE,
			PKCOL, PrimaryKeyOrdinal.O1,
			POJOField.get(VMDRuleResult.class, "rowId"));

	MetaAttribute ATR_RUELID = AttributeFactory.pInt(
			"ruleId", 0, 1, Integer.MAX_VALUE,
			ColumnSpecifier.get(TABLE, "ruleId"),
			POJOField.get(VMDRuleResult.class, "ruleId"));
					
	MetaAttribute ATR_FKVALUE = AttributeFactory.string(
			"fkValue", null, 128,
			ColumnSpecifier.get(TABLE, "fkValue"),
			POJOField.get(VMDRuleResult.class, "fkValue"));

	MetaAttribute ATR_SEVERITY = AttributeFactory.code(
			"severity", VMDSeverityType.class, VMDSeverityType.FATAL,
			ColumnSpecifier.get(TABLE, "severityId"),
			POJOField.get(VMDRuleResult.class, "severity"));

	MetaAttribute ATR_STATUS = AttributeFactory.code(
			"status", VMDResultStatusType.class, VMDResultStatusType.OPEN,
			ColumnSpecifier.get(TABLE, "resultStatus"),
			POJOField.get(VMDRuleResult.class, "status"));

	MetaAttribute ATR_OPENSINCE = AttributeFactory.datetime(
			"openSince", TimestampUtil.NULL_TIMESTAMP,
			ColumnSpecifier.get(TABLE, "openSince"),
			POJOField.get(VMDRuleResult.class, "openSince"));

	MetaAttribute ATR_CLOSEDON = AttributeFactory.datetime(
			"closedOn", TimestampUtil.NULL_TIMESTAMP,
			ColumnSpecifier.get(TABLE, "closedOn"),
			POJOField.get(VMDRuleResult.class, "closedOn"));
	
	MetaAttribute ATR_CLOSEDBY = AttributeFactory.string(
			"closedBy", null, 10, 
			ColumnSpecifier.get(TABLE, "closedBy"),
			POJOField.get(VMDRuleResult.class, "closedBy"));

	MetaAttribute ATR_PROBLEMOWNER = AttributeFactory.string(
			"problemOwner", null, 10, 
			ColumnSpecifier.get(TABLE, "problemOwner"),
			POJOField.get(VMDRuleResult.class, "problemOwner"));

	MetaClass METACLASS = new MetaClass(
			"VMDRuleResult", (Class<Model>) null,
			LongPrimaryKey.class,
			null,
			// Attributes
			new VarArgs<MetaProperty>(MetaProperty.class).flat(
			TABLE, PKCOL,
			MetaClassMember.discoverMetaClassMembers(VMDRuleResultMeta.class)
			));

}

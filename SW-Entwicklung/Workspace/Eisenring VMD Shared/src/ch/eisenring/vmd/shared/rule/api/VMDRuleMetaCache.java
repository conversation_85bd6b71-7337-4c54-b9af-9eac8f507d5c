package ch.eisenring.vmd.shared.rule.api;

import java.io.IOException;
import java.util.concurrent.atomic.AtomicBoolean;

import ch.eisenring.core.collections.Collection;
import ch.eisenring.core.collections.Map;
import ch.eisenring.core.collections.Set;
import ch.eisenring.core.collections.impl.ArrayList;
import ch.eisenring.core.collections.impl.AtomicArrayLookup;
import ch.eisenring.core.collections.impl.HashSet;
import ch.eisenring.core.datatypes.strings.Strings;
import ch.eisenring.vmd.shared.VMDConstants;

public abstract class VMDRuleMetaCache {

	private final AtomicBoolean allLoaded = new AtomicBoolean();
	
	private final Map<Integer, VMDRuleMeta> cache = new AtomicArrayLookup<>();

	protected VMDRuleMetaCache() {
	}

	/**
	 * Gets all meta data.
	 */
	public final Collection<VMDRuleMeta> getAll() {
		// load all meta data
		final Collection<Integer> ruleIds = new ArrayList<>(1);
		ruleIds.add(VMDConstants.ALL_RULES_ID);
		try {
			return loadMeta(ruleIds);
		} catch (final IOException e) {
			throw new RuntimeException(e);
		}
	}
	
	/**
	 * Gets the meta data for the specified rule.
	 */
	public final VMDRuleMeta get(final Integer ruleId) {
		VMDRuleMeta result = cache.get(ruleId);
		if (result != null)
			return result;
		try {
			return loadMeta(ruleId);
		} catch (final IOException e) {
			throw new RuntimeException(e.getMessage(), e);
		}
	}

	/**
	 * Add/replace the specified meta
	 */
	protected void put(final VMDRuleMeta ruleMeta) {
		if (ruleMeta == null)
			return;
		final Integer ruleId = ruleMeta.getRuleId();
		cache.put(ruleId, ruleMeta);
	}

	/**
	 * Add/replace list of meta
	 */
	protected void put(final Collection<VMDRuleMeta> ruleMetas) {
		for (final VMDRuleMeta ruleMeta : ruleMetas)
			put(ruleMeta);
	}


	/**
	 * Loads a single rule meta
	 */
	protected final VMDRuleMeta loadMeta(final Integer ruleId) throws IOException {
		final Collection<Integer> ruleIds = new ArrayList<>(1);
		ruleIds.add(ruleId);
		for (final VMDRuleMeta meta : loadMeta(ruleIds)) {
			if (ruleId.equals(meta.getRuleId()))
				return meta;
		}
		throw new IOException(Strings.concat("no meta data for rule id: ", ruleId));
	}

	
	protected final Collection<VMDRuleMeta> loadMeta(final Collection<Integer> ruleIds) throws IOException {
		if (ruleIds.contains(VMDConstants.ALL_RULES_ID)) {
			if (!allLoaded.get()) {
				final Collection<VMDRuleMeta> metas = loadMetaImpl(ruleIds);
				put(metas);
				allLoaded.set(true);
			}
			return new ArrayList<>(cache.values());
		} else {
			// check what needs to be loaded
			final Set<VMDRuleMeta> result = new HashSet<>(ruleIds.size());
			final Set<Integer> loadIds = new HashSet<>(ruleIds.size());
			for (final Integer ruleId : ruleIds) {
				final VMDRuleMeta meta = cache.get(ruleId);
				if (meta == null) {
					loadIds.add(ruleId);
				} else {
					result.add(meta);
				}
			}
			if (!loadIds.isEmpty()) {
				final Collection<VMDRuleMeta> loaded = loadMetaImpl(loadIds);
				put(loaded);
				result.addAll(loaded);
			}
			return result;
		}
	}

	/**
	 * Must be implements by child class.
	 * Must handle request of VMDConstants.ALL_RULES_ID
	 */
	protected abstract Collection<VMDRuleMeta> loadMetaImpl(final Collection<Integer> ruleIds) throws IOException;

}

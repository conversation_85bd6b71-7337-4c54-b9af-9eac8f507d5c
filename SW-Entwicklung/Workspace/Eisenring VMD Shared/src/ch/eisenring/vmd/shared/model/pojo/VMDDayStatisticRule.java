package ch.eisenring.vmd.shared.model.pojo;

import java.io.IOException;

import ch.eisenring.core.io.StreamReader;
import ch.eisenring.core.io.StreamWriter;
import ch.eisenring.core.sort.Comparator;
import ch.eisenring.model.primarykey.LongPrimaryKey;

public final class VMDDayStatisticRule extends VMDDayStatisticBase<Integer> {

	private int ruleId;

	// --------------------------------------------------------------
	// ---
	// --- Factory methods
	// ---
	// --------------------------------------------------------------
	/**
	 * Creates a new result object with default values
	 */
	public static VMDDayStatisticRule create(final long day, final int ruleId, final long problemCount) {
		final VMDDayStatisticRule result = new VMDDayStatisticRule();
		// create a new primary key for the object
		result.rowId = LongPrimaryKey.create();
		result.dayKey = day;
		result.ruleId = ruleId;
		result.problemCount = problemCount;
		return result;
	}
	
	// --------------------------------------------------------------
	// ---
	// --- Getters / Setters
	// ---
	// --------------------------------------------------------------
	@Override
	public Integer getOwner() {
		return ruleId;
	}

	// --------------------------------------------------------------
	// ---
	// --- Streamable implementation
	// ---
	// --------------------------------------------------------------
	VMDDayStatisticRule() {
		// default constructor for use by streaming
	}
	
	@Override
	public void read(final StreamReader reader) throws IOException {
		super.read(reader);
		ruleId = reader.readInt();
	}

	@Override
	public void write(final StreamWriter writer) throws IOException {
		super.write(writer);
		writer.writeInt(ruleId);
	}

	// --------------------------------------------------------------
	// ---
	// --- Sorting
	// ---
	// --------------------------------------------------------------
	public final static class Order extends VMDDayStatisticBase.Order {
		public final static Comparator<VMDDayStatisticRule> RuleId = new Comparator<VMDDayStatisticRule>() {
			@Override
			public int compare(final VMDDayStatisticRule r0, final VMDDayStatisticRule r1) {
				return compareSigned(r0.ruleId, r1.ruleId);
			}
		};
	}

}

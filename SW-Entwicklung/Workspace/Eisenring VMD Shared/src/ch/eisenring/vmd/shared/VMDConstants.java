package ch.eisenring.vmd.shared;

import ch.eisenring.core.application.SoftwareDescriptor;
import ch.eisenring.core.codetable.SoftwareType;
import ch.eisenring.core.datatypes.date.TimestampUtil;
import ch.eisenring.model.engine.jdbc.DatabaseSpecifier;

public interface VMDConstants {

	// DONT MODFIY THE VERSION LINE, EXCEPT THE STRING CONTENTS!
	// BUILD SCRIPTS NEED THE LINE TO BE EXACTLY AS IT IS!!!
	// THE VERSION STRING MUST HAVE THREE DOT-SEPARATED PARTS AND
	// MUST CONTAIN ONLY DIGITS (it is used as .exe and installer
	// version by the build)
	String VERSION = "1.0.14";
	long VERSION_TIMESTAMP = TimestampUtil.toTimestamp("2018-08-21");

	String APPNAME = "HEAG Validierung";

	SoftwareDescriptor DESCRIPTOR = new SoftwareDescriptor(
			APPNAME, VERSION, VERSION_TIMESTAMP, SoftwareType.APPLICATION);

	String URL_PROTOCOL = "EisenringVMD://";

	DatabaseSpecifier VMD_DATABASE = DatabaseSpecifier.get("VMD");

	Integer ALL_RULES_ID = Integer.valueOf(-2);

	String VMD_ENGINE_NAME = "---VMRE---";
	String VMD_UNKNOWN_OWNER = "---NULL---";

}
